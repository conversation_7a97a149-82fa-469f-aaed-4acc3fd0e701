{"ast": null, "code": "import { a as d, b } from \"./chunk-4EPLOSKY.mjs\";\nimport { a as h, b as z } from \"./chunk-L22R3OIW.mjs\";\nimport { a as p, b as g } from \"./chunk-QVWBJJRF.mjs\";\nimport { a as o, c as y } from \"./chunk-A2Z7I2EY.mjs\";\nimport { a as c } from \"./chunk-A63SMUOU.mjs\";\nvar r = class extends c {\n    static deserialize(i) {\n      let e = i.deserializeUleb128AsU32();\n      switch (e) {\n        case 0:\n          return u.load(i);\n        case 1:\n          return l.load(i);\n        case 2:\n          return n.load(i);\n        case 3:\n          return a.load(i);\n        default:\n          throw new Error(`Unknown variant index for AccountAuthenticator: ${e}`);\n      }\n    }\n    isEd25519() {\n      return this instanceof u;\n    }\n    isMultiEd25519() {\n      return this instanceof l;\n    }\n    isSingleKey() {\n      return this instanceof n;\n    }\n    isMultiKey() {\n      return this instanceof a;\n    }\n  },\n  u = class t extends r {\n    constructor(i, e) {\n      super(), this.public_key = i, this.signature = e;\n    }\n    serialize(i) {\n      i.serializeU32AsUleb128(0), this.public_key.serialize(i), this.signature.serialize(i);\n    }\n    static load(i) {\n      let e = o.deserialize(i),\n        s = y.deserialize(i);\n      return new t(e, s);\n    }\n  },\n  l = class t extends r {\n    constructor(i, e) {\n      super(), this.public_key = i, this.signature = e;\n    }\n    serialize(i) {\n      i.serializeU32AsUleb128(1), this.public_key.serialize(i), this.signature.serialize(i);\n    }\n    static load(i) {\n      let e = d.deserialize(i),\n        s = b.deserialize(i);\n      return new t(e, s);\n    }\n  },\n  n = class t extends r {\n    constructor(i, e) {\n      super(), this.public_key = i, this.signature = e;\n    }\n    serialize(i) {\n      i.serializeU32AsUleb128(2), this.public_key.serialize(i), this.signature.serialize(i);\n    }\n    static load(i) {\n      let e = p.deserialize(i),\n        s = g.deserialize(i);\n      return new t(e, s);\n    }\n  },\n  a = class t extends r {\n    constructor(i, e) {\n      super(), this.public_keys = i, this.signatures = e;\n    }\n    serialize(i) {\n      i.serializeU32AsUleb128(3), this.public_keys.serialize(i), this.signatures.serialize(i);\n    }\n    static load(i) {\n      let e = h.deserialize(i),\n        s = z.deserialize(i);\n      return new t(e, s);\n    }\n  };\nexport { r as a, u as b, l as c, n as d, a as e };", "map": {"version": 3, "names": ["r", "c", "deserialize", "i", "e", "deserializeUleb128AsU32", "u", "load", "l", "n", "a", "Error", "isEd25519", "isMultiEd25519", "isSingleKey", "isMultiKey", "t", "constructor", "public_key", "signature", "serialize", "serializeU32AsUleb128", "o", "s", "y", "d", "b", "p", "g", "public_keys", "signatures", "h", "z"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\authenticator\\account.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\nimport { Serializer, Deserializer, Serializable } from \"../../bcs\";\nimport { AnyPublicKey, AnySignature } from \"../../core/crypto\";\nimport { Ed25519PublicKey, Ed25519Signature } from \"../../core/crypto/ed25519\";\nimport { MultiEd25519PublicKey, MultiEd25519Signature } from \"../../core/crypto/multiEd25519\";\nimport { MultiKey, MultiKeySignature } from \"../../core/crypto/multiKey\";\nimport { AccountAuthenticatorVariant } from \"../../types\";\n\nexport abstract class AccountAuthenticator extends Serializable {\n  abstract serialize(serializer: Serializer): void;\n\n  static deserialize(deserializer: Deserializer): AccountAuthenticator {\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case AccountAuthenticatorVariant.Ed25519:\n        return AccountAuthenticatorEd25519.load(deserializer);\n      case AccountAuthenticatorVariant.MultiEd25519:\n        return AccountAuthenticatorMultiEd25519.load(deserializer);\n      case AccountAuthenticatorVariant.SingleKey:\n        return AccountAuthenticatorSingleKey.load(deserializer);\n      case AccountAuthenticatorVariant.MultiKey:\n        return AccountAuthenticatorMultiKey.load(deserializer);\n      default:\n        throw new Error(`Unknown variant index for AccountAuthenticator: ${index}`);\n    }\n  }\n\n  isEd25519(): this is AccountAuthenticatorEd25519 {\n    return this instanceof AccountAuthenticatorEd25519;\n  }\n\n  isMultiEd25519(): this is AccountAuthenticatorMultiEd25519 {\n    return this instanceof AccountAuthenticatorMultiEd25519;\n  }\n\n  isSingleKey(): this is AccountAuthenticatorSingleKey {\n    return this instanceof AccountAuthenticatorSingleKey;\n  }\n\n  isMultiKey(): this is AccountAuthenticatorMultiKey {\n    return this instanceof AccountAuthenticatorMultiKey;\n  }\n}\n\n/**\n * Transaction authenticator Ed25519 for a multi signer transaction\n *\n * @param public_key Account's Ed25519 public key.\n * @param signature Account's Ed25519 signature\n *\n */\nexport class AccountAuthenticatorEd25519 extends AccountAuthenticator {\n  public readonly public_key: Ed25519PublicKey;\n\n  public readonly signature: Ed25519Signature;\n\n  constructor(public_key: Ed25519PublicKey, signature: Ed25519Signature) {\n    super();\n    this.public_key = public_key;\n    this.signature = signature;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(AccountAuthenticatorVariant.Ed25519);\n    this.public_key.serialize(serializer);\n    this.signature.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): AccountAuthenticatorEd25519 {\n    const public_key = Ed25519PublicKey.deserialize(deserializer);\n    const signature = Ed25519Signature.deserialize(deserializer);\n    return new AccountAuthenticatorEd25519(public_key, signature);\n  }\n}\n\n/**\n * Transaction authenticator Multi Ed25519 for a multi signers transaction\n *\n * @param public_key Account's MultiEd25519 public key.\n * @param signature Account's MultiEd25519 signature\n *\n */\nexport class AccountAuthenticatorMultiEd25519 extends AccountAuthenticator {\n  public readonly public_key: MultiEd25519PublicKey;\n\n  public readonly signature: MultiEd25519Signature;\n\n  constructor(public_key: MultiEd25519PublicKey, signature: MultiEd25519Signature) {\n    super();\n    this.public_key = public_key;\n    this.signature = signature;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(AccountAuthenticatorVariant.MultiEd25519);\n    this.public_key.serialize(serializer);\n    this.signature.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): AccountAuthenticatorMultiEd25519 {\n    const public_key = MultiEd25519PublicKey.deserialize(deserializer);\n    const signature = MultiEd25519Signature.deserialize(deserializer);\n    return new AccountAuthenticatorMultiEd25519(public_key, signature);\n  }\n}\n\n/**\n * AccountAuthenticatorSingleKey for a single signer\n *\n * @param public_key AnyPublicKey\n * @param signature AnySignature\n *\n */\nexport class AccountAuthenticatorSingleKey extends AccountAuthenticator {\n  public readonly public_key: AnyPublicKey;\n\n  public readonly signature: AnySignature;\n\n  constructor(public_key: AnyPublicKey, signature: AnySignature) {\n    super();\n    this.public_key = public_key;\n    this.signature = signature;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(AccountAuthenticatorVariant.SingleKey);\n    this.public_key.serialize(serializer);\n    this.signature.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): AccountAuthenticatorSingleKey {\n    const public_key = AnyPublicKey.deserialize(deserializer);\n    const signature = AnySignature.deserialize(deserializer);\n    return new AccountAuthenticatorSingleKey(public_key, signature);\n  }\n}\n\n/**\n * AccountAuthenticatorMultiKey for a multi signer\n *\n * @param public_keys MultiKey\n * @param signatures Signature\n *\n */\nexport class AccountAuthenticatorMultiKey extends AccountAuthenticator {\n  public readonly public_keys: MultiKey;\n\n  public readonly signatures: MultiKeySignature;\n\n  constructor(public_keys: MultiKey, signatures: MultiKeySignature) {\n    super();\n    this.public_keys = public_keys;\n    this.signatures = signatures;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(AccountAuthenticatorVariant.MultiKey);\n    this.public_keys.serialize(serializer);\n    this.signatures.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): AccountAuthenticatorMultiKey {\n    const public_keys = MultiKey.deserialize(deserializer);\n    const signatures = MultiKeySignature.deserialize(deserializer);\n    return new AccountAuthenticatorMultiKey(public_keys, signatures);\n  }\n}\n"], "mappings": ";;;;;AAYO,IAAeA,CAAA,GAAf,cAA4CC,CAAa;IAG9D,OAAOC,YAAYC,CAAA,EAAkD;MACnE,IAAMC,CAAA,GAAQD,CAAA,CAAaE,uBAAA,CAAwB;MACnD,QAAQD,CAAA;QACN;UACE,OAAOE,CAAA,CAA4BC,IAAA,CAAKJ,CAAY;QACtD;UACE,OAAOK,CAAA,CAAiCD,IAAA,CAAKJ,CAAY;QAC3D;UACE,OAAOM,CAAA,CAA8BF,IAAA,CAAKJ,CAAY;QACxD;UACE,OAAOO,CAAA,CAA6BH,IAAA,CAAKJ,CAAY;QACvD;UACE,MAAM,IAAIQ,KAAA,CAAM,mDAAmDP,CAAK,EAAE,CAC9E;MAAA;IACF;IAEAQ,UAAA,EAAiD;MAC/C,OAAO,gBAAgBN,CACzB;IAAA;IAEAO,eAAA,EAA2D;MACzD,OAAO,gBAAgBL,CACzB;IAAA;IAEAM,YAAA,EAAqD;MACnD,OAAO,gBAAgBL,CACzB;IAAA;IAEAM,WAAA,EAAmD;MACjD,OAAO,gBAAgBL,CACzB;IAAA;EACF;EASaJ,CAAA,GAAN,MAAMU,CAAA,SAAoChB,CAAqB;IAKpEiB,YAAYd,CAAA,EAA8BC,CAAA,EAA6B;MACrE,MAAM,GACN,KAAKc,UAAA,GAAaf,CAAA,EAClB,KAAKgB,SAAA,GAAYf,CACnB;IAAA;IAEAgB,UAAUjB,CAAA,EAA8B;MACtCA,CAAA,CAAWkB,qBAAA,EAAyD,GACpE,KAAKH,UAAA,CAAWE,SAAA,CAAUjB,CAAU,GACpC,KAAKgB,SAAA,CAAUC,SAAA,CAAUjB,CAAU,CACrC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAAyD;MACnE,IAAMC,CAAA,GAAakB,CAAA,CAAiBpB,WAAA,CAAYC,CAAY;QACtDoB,CAAA,GAAYC,CAAA,CAAiBtB,WAAA,CAAYC,CAAY;MAC3D,OAAO,IAAIa,CAAA,CAA4BZ,CAAA,EAAYmB,CAAS,CAC9D;IAAA;EACF;EASaf,CAAA,GAAN,MAAMQ,CAAA,SAAyChB,CAAqB;IAKzEiB,YAAYd,CAAA,EAAmCC,CAAA,EAAkC;MAC/E,MAAM,GACN,KAAKc,UAAA,GAAaf,CAAA,EAClB,KAAKgB,SAAA,GAAYf,CACnB;IAAA;IAEAgB,UAAUjB,CAAA,EAA8B;MACtCA,CAAA,CAAWkB,qBAAA,EAA8D,GACzE,KAAKH,UAAA,CAAWE,SAAA,CAAUjB,CAAU,GACpC,KAAKgB,SAAA,CAAUC,SAAA,CAAUjB,CAAU,CACrC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAA8D;MACxE,IAAMC,CAAA,GAAaqB,CAAA,CAAsBvB,WAAA,CAAYC,CAAY;QAC3DoB,CAAA,GAAYG,CAAA,CAAsBxB,WAAA,CAAYC,CAAY;MAChE,OAAO,IAAIa,CAAA,CAAiCZ,CAAA,EAAYmB,CAAS,CACnE;IAAA;EACF;EASad,CAAA,GAAN,MAAMO,CAAA,SAAsChB,CAAqB;IAKtEiB,YAAYd,CAAA,EAA0BC,CAAA,EAAyB;MAC7D,MAAM,GACN,KAAKc,UAAA,GAAaf,CAAA,EAClB,KAAKgB,SAAA,GAAYf,CACnB;IAAA;IAEAgB,UAAUjB,CAAA,EAA8B;MACtCA,CAAA,CAAWkB,qBAAA,EAA2D,GACtE,KAAKH,UAAA,CAAWE,SAAA,CAAUjB,CAAU,GACpC,KAAKgB,SAAA,CAAUC,SAAA,CAAUjB,CAAU,CACrC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAA2D;MACrE,IAAMC,CAAA,GAAauB,CAAA,CAAazB,WAAA,CAAYC,CAAY;QAClDoB,CAAA,GAAYK,CAAA,CAAa1B,WAAA,CAAYC,CAAY;MACvD,OAAO,IAAIa,CAAA,CAA8BZ,CAAA,EAAYmB,CAAS,CAChE;IAAA;EACF;EASab,CAAA,GAAN,MAAMM,CAAA,SAAqChB,CAAqB;IAKrEiB,YAAYd,CAAA,EAAuBC,CAAA,EAA+B;MAChE,MAAM,GACN,KAAKyB,WAAA,GAAc1B,CAAA,EACnB,KAAK2B,UAAA,GAAa1B,CACpB;IAAA;IAEAgB,UAAUjB,CAAA,EAA8B;MACtCA,CAAA,CAAWkB,qBAAA,EAA0D,GACrE,KAAKQ,WAAA,CAAYT,SAAA,CAAUjB,CAAU,GACrC,KAAK2B,UAAA,CAAWV,SAAA,CAAUjB,CAAU,CACtC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAA0D;MACpE,IAAMC,CAAA,GAAc2B,CAAA,CAAS7B,WAAA,CAAYC,CAAY;QAC/CoB,CAAA,GAAaS,CAAA,CAAkB9B,WAAA,CAAYC,CAAY;MAC7D,OAAO,IAAIa,CAAA,CAA6BZ,CAAA,EAAamB,CAAU,CACjE;IAAA;EACF;AAAA,SAAAvB,CAAA,IAAAU,CAAA,EAAAJ,CAAA,IAAAoB,CAAA,EAAAlB,CAAA,IAAAP,CAAA,EAAAQ,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAN,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}