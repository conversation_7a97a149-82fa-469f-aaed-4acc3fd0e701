{"ast": null, "code": "import { a as l, b as s, c as u, d as c, e as d, f as m, g as U } from \"./chunk-YPHH6CAO.mjs\";\nimport { a as o } from \"./chunk-A63SMUOU.mjs\";\nimport { b as w } from \"./chunk-BCUSI3N6.mjs\";\nvar t = class n extends o {\n    constructor(e) {\n      super(), this.values = e;\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      if (!(this.values[0] instanceof s) && this.values[0] !== void 0) throw new Error(\"Script function arguments only accept u8 vectors\");\n      e.serializeU32AsUleb128(4), e.serialize(this);\n    }\n    static U8(e) {\n      let r;\n      if (Array.isArray(e) && typeof e[0] == \"number\") r = e;else if (typeof e == \"string\") {\n        let i = w.fromHexInput(e);\n        r = Array.from(i.toUint8Array());\n      } else if (e instanceof Uint8Array) r = Array.from(e);else throw new Error(\"Invalid input type\");\n      return new n(r.map(i => new s(i)));\n    }\n    static U16(e) {\n      return new n(e.map(r => new u(r)));\n    }\n    static U32(e) {\n      return new n(e.map(r => new c(r)));\n    }\n    static U64(e) {\n      return new n(e.map(r => new d(r)));\n    }\n    static U128(e) {\n      return new n(e.map(r => new m(r)));\n    }\n    static U256(e) {\n      return new n(e.map(r => new U(r)));\n    }\n    static Bool(e) {\n      return new n(e.map(r => new l(r)));\n    }\n    static MoveString(e) {\n      return new n(e.map(r => new a(r)));\n    }\n    serialize(e) {\n      e.serializeVector(this.values);\n    }\n    static deserialize(e, r) {\n      let i = e.deserializeUleb128AsU32(),\n        p = new Array();\n      for (let y = 0; y < i; y += 1) p.push(r.deserialize(e));\n      return new n(p);\n    }\n  },\n  a = class n extends o {\n    constructor(e) {\n      super(), this.value = e;\n    }\n    serialize(e) {\n      e.serializeStr(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      let r = this.bcsToBytes().slice(1);\n      t.U8(r).serializeForScriptFunction(e);\n    }\n    static deserialize(e) {\n      return new n(e.deserializeStr());\n    }\n  },\n  z = class n extends o {\n    constructor(e) {\n      super(), typeof e < \"u\" && e !== null ? this.vec = new t([e]) : this.vec = new t([]), [this.value] = this.vec.values;\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    unwrap() {\n      if (this.isSome()) return this.vec.values[0];\n      throw new Error(\"Called unwrap on a MoveOption with no value\");\n    }\n    isSome() {\n      return this.vec.values.length === 1;\n    }\n    serialize(e) {\n      this.vec.serialize(e);\n    }\n    static U8(e) {\n      return new n(e != null ? new s(e) : void 0);\n    }\n    static U16(e) {\n      return new n(e != null ? new u(e) : void 0);\n    }\n    static U32(e) {\n      return new n(e != null ? new c(e) : void 0);\n    }\n    static U64(e) {\n      return new n(e != null ? new d(e) : void 0);\n    }\n    static U128(e) {\n      return new n(e != null ? new m(e) : void 0);\n    }\n    static U256(e) {\n      return new n(e != null ? new U(e) : void 0);\n    }\n    static Bool(e) {\n      return new n(e != null ? new l(e) : void 0);\n    }\n    static MoveString(e) {\n      return new n(e != null ? new a(e) : void 0);\n    }\n    static deserialize(e, r) {\n      let i = t.deserialize(e, r);\n      return new n(i.values[0]);\n    }\n  };\nexport { t as a, a as b, z as c };", "map": {"version": 3, "names": ["t", "n", "o", "constructor", "e", "values", "serializeForEntryFunction", "r", "bcsToBytes", "serializeBytes", "serializeForScriptFunction", "s", "Error", "serializeU32AsUleb128", "serialize", "U8", "Array", "isArray", "i", "w", "fromHexInput", "from", "toUint8Array", "Uint8Array", "map", "U16", "u", "U32", "c", "U64", "d", "U128", "m", "U256", "U", "Bool", "l", "MoveString", "a", "serializeVector", "deserialize", "deserializeUleb128AsU32", "p", "y", "push", "value", "serializeStr", "slice", "deserializeStr", "z", "vec", "unwrap", "isSome", "length", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\serializable\\moveStructs.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Bool, U128, U16, U256, U32, U64, U8 } from \"./movePrimitives\";\nimport { Serializable, Serializer } from \"../serializer\";\nimport { Deserializable, Deserializer } from \"../deserializer\";\nimport { AnyNumber, HexInput, ScriptTransactionArgumentVariants } from \"../../types\";\nimport { Hex } from \"../../core/hex\";\nimport { EntryFunctionArgument, TransactionArgument } from \"../../transactions/instances/transactionArgument\";\n\n/**\n * This class is the Aptos Typescript SDK representation of a Move `vector<T>`,\n * where `T` represents either a primitive type (`bool`, `u8`, `u64`, ...)\n * or a BCS-serializable struct itself.\n *\n * It is a BCS-serializable, array-like type that contains an array of values of type `T`,\n * where `T` is a class that implements `Serializable`.\n *\n * The purpose of this class is to facilitate easy construction of BCS-serializable\n * Move `vector<T>` types.\n *\n * @example\n * // in Move: `vector<u8> [1, 2, 3, 4];`\n * const vecOfU8s = new MoveVector<U8>([new U8(1), new U8(2), new U8(3), new U8(4)]);\n * // in Move: `std::bcs::to_bytes(vector<u8> [1, 2, 3, 4]);`\n * const bcsBytes = vecOfU8s.toUint8Array();\n *\n * // vector<vector<u8>> [ vector<u8> [1], vector<u8> [1, 2, 3, 4], vector<u8> [5, 6, 7, 8] ];\n * const vecOfVecs = new MoveVector<MoveVector<U8>>([\n *   new MoveVector<U8>([new U8(1)]),\n *   MoveVector.U8([1, 2, 3, 4]),\n *   MoveVector.U8([5, 6, 7, 8]),\n * ]);\n *\n * // vector<Option<u8>> [ std::option::some<u8>(1), std::option::some<u8>(2) ];\n * const vecOfOptionU8s = new MoveVector<MoveOption<U8>>([\n *    MoveOption.U8(1),\n *    MoveOption.U8(2),\n * ]);\n *\n * // vector<MoveString> [ std::string::utf8(b\"hello\"), std::string::utf8(b\"world\") ];\n * const vecOfStrings = new MoveVector([new MoveString(\"hello\"), new MoveString(\"world\")]);\n * const vecOfStrings2 = MoveVector.MoveString([\"hello\", \"world\"]);\n *\n * @params\n * values: an Array<T> of values where T is a class that implements Serializable\n * @returns a `MoveVector<T>` with the values `values`\n */\nexport class MoveVector<T extends Serializable & EntryFunctionArgument>\n  extends Serializable\n  implements TransactionArgument\n{\n  public values: Array<T>;\n\n  constructor(values: Array<T>) {\n    super();\n    this.values = values;\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  /**\n   * NOTE: This function will only work when the inner values in the `MoveVector` are `U8`s.\n   * @param serializer\n   */\n  serializeForScriptFunction(serializer: Serializer): void {\n    // runtime check to ensure that you can't serialize anything other than vector<u8>\n    const isU8 = this.values[0] instanceof U8;\n    // if the inner array is length 0, we can't check the type because it has no instance, so we assume it's a u8\n    // it may not be, but we don't care because regardless of a vector's type,\n    // a zero-length vector is serialized to a single byte value: 0\n    if (!isU8 && this.values[0] !== undefined) {\n      throw new Error(\"Script function arguments only accept u8 vectors\");\n    }\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U8Vector);\n    serializer.serialize(this);\n  }\n\n  /**\n   * Factory method to generate a MoveVector of U8s from an array of numbers.\n   *\n   * @example\n   * const v = MoveVector.U8([1, 2, 3, 4]);\n   * @params values: an array of `numbers` to convert to U8s\n   * @returns a `MoveVector<U8>`\n   */\n  static U8(values: Array<number> | HexInput): MoveVector<U8> {\n    let numbers: Array<number>;\n\n    if (Array.isArray(values) && typeof values[0] === \"number\") {\n      numbers = values;\n    } else if (typeof values === \"string\") {\n      const hex = Hex.fromHexInput(values);\n      numbers = Array.from(hex.toUint8Array());\n    } else if (values instanceof Uint8Array) {\n      numbers = Array.from(values);\n    } else {\n      throw new Error(\"Invalid input type\");\n    }\n\n    return new MoveVector<U8>(numbers.map((v) => new U8(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of U16s from an array of numbers.\n   *\n   * @example\n   * const v = MoveVector.U16([1, 2, 3, 4]);\n   * @params values: an array of `numbers` to convert to U16s\n   * @returns a `MoveVector<U16>`\n   */\n  static U16(values: Array<number>): MoveVector<U16> {\n    return new MoveVector<U16>(values.map((v) => new U16(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of U32s from an array of numbers.\n   *\n   * @example\n   * const v = MoveVector.U32([1, 2, 3, 4]);\n   * @params values: an array of `numbers` to convert to U32s\n   * @returns a `MoveVector<U32>`\n   */\n  static U32(values: Array<number>): MoveVector<U32> {\n    return new MoveVector<U32>(values.map((v) => new U32(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of U64s from an array of numbers or bigints.\n   *\n   * @example\n   * const v = MoveVector.U64([1, 2, 3, 4]);\n   * @params values: an array of numbers of type `number | bigint` to convert to U64s\n   * @returns a `MoveVector<U64>`\n   */\n  static U64(values: Array<AnyNumber>): MoveVector<U64> {\n    return new MoveVector<U64>(values.map((v) => new U64(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of U128s from an array of numbers or bigints.\n   *\n   * @example\n   * const v = MoveVector.U128([1, 2, 3, 4]);\n   * @params values: an array of numbers of type `number | bigint` to convert to U128s\n   * @returns a `MoveVector<U128>`\n   */\n  static U128(values: Array<AnyNumber>): MoveVector<U128> {\n    return new MoveVector<U128>(values.map((v) => new U128(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of U256s from an array of numbers or bigints.\n   *\n   * @example\n   * const v = MoveVector.U256([1, 2, 3, 4]);\n   * @params values: an array of numbers of type `number | bigint` to convert to U256s\n   * @returns a `MoveVector<U256>`\n   */\n  static U256(values: Array<AnyNumber>): MoveVector<U256> {\n    return new MoveVector<U256>(values.map((v) => new U256(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of Bools from an array of booleans.\n   *\n   * @example\n   * const v = MoveVector.Bool([true, false, true, false]);\n   * @params values: an array of `bools` to convert to Bools\n   * @returns a `MoveVector<Bool>`\n   */\n  static Bool(values: Array<boolean>): MoveVector<Bool> {\n    return new MoveVector<Bool>(values.map((v) => new Bool(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector of MoveStrings from an array of strings.\n   *\n   * @example\n   * const v = MoveVector.MoveString([\"hello\", \"world\"]);\n   * @params values: an array of `strings` to convert to MoveStrings\n   * @returns a `MoveVector<MoveString>`\n   */\n  static MoveString(values: Array<string>): MoveVector<MoveString> {\n    return new MoveVector<MoveString>(values.map((v) => new MoveString(v)));\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeVector(this.values);\n  }\n\n  /**\n   * Deserialize a MoveVector of type T, specifically where T is a Serializable and Deserializable type.\n   *\n   * NOTE: This only works with a depth of one. Generics will not work.\n   *\n   * NOTE: This will not work with types that aren't of the Serializable class.\n   *\n   * If you're looking for a more flexible deserialization function, you can use the deserializeVector function\n   * in the Deserializer class.\n   *\n   * @example\n   * const vec = MoveVector.deserialize(deserializer, U64);\n   * @params deserializer: the Deserializer instance to use, with bytes loaded into it already.\n   * cls: the class to typecast the input values to, must be a Serializable and Deserializable type.\n   * @returns a MoveVector of the corresponding class T\n   * *\n   */\n  static deserialize<T extends Serializable & EntryFunctionArgument>(\n    deserializer: Deserializer,\n    cls: Deserializable<T>,\n  ): MoveVector<T> {\n    const length = deserializer.deserializeUleb128AsU32();\n    const values = new Array<T>();\n    for (let i = 0; i < length; i += 1) {\n      values.push(cls.deserialize(deserializer));\n    }\n    return new MoveVector(values);\n  }\n}\n\nexport class MoveString extends Serializable implements TransactionArgument {\n  public value: string;\n\n  constructor(value: string) {\n    super();\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeStr(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    // Serialize the string as a fixed byte string, i.e., without the length prefix\n    const fixedStringBytes = this.bcsToBytes().slice(1);\n    // Put those bytes into a vector<u8> and serialize it as a script function argument\n    const vectorU8 = MoveVector.U8(fixedStringBytes);\n    vectorU8.serializeForScriptFunction(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): MoveString {\n    return new MoveString(deserializer.deserializeStr());\n  }\n}\n\nexport class MoveOption<T extends Serializable & EntryFunctionArgument>\n  extends Serializable\n  implements EntryFunctionArgument\n{\n  private vec: MoveVector<T>;\n\n  public readonly value?: T;\n\n  constructor(value?: T | null) {\n    super();\n    if (typeof value !== \"undefined\" && value !== null) {\n      this.vec = new MoveVector([value]);\n    } else {\n      this.vec = new MoveVector([]);\n    }\n\n    [this.value] = this.vec.values;\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  /**\n   * Retrieves the inner value of the MoveOption.\n   *\n   * This method is inspired by Rust's `Option<T>.unwrap()`.\n   * In Rust, attempting to unwrap a `None` value results in a panic.\n   *\n   * Similarly, this method will throw an error if the value is not present.\n   *\n   * @example\n   * const option = new MoveOption<Bool>(new Bool(true));\n   * const value = option.unwrap();  // Returns the Bool instance\n   *\n   * @throws {Error} Throws an error if the MoveOption does not contain a value.\n   *\n   * @returns {T} The contained value if present.\n   */\n  unwrap(): T {\n    if (!this.isSome()) {\n      throw new Error(\"Called unwrap on a MoveOption with no value\");\n    } else {\n      return this.vec.values[0];\n    }\n  }\n\n  // Check if the MoveOption has a value.\n  isSome(): boolean {\n    return this.vec.values.length === 1;\n  }\n\n  serialize(serializer: Serializer): void {\n    // serialize 0 or 1\n    // if 1, serialize the value\n    this.vec.serialize(serializer);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U8> from a `number` or `undefined`.\n   *\n   * @example\n   * MoveOption.U8(1).isSome() === true;\n   * MoveOption.U8().isSome() === false;\n   * MoveOption.U8(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U8> with an inner value `value`\n   */\n  static U8(value?: number | null): MoveOption<U8> {\n    return new MoveOption<U8>(value !== null && value !== undefined ? new U8(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U16> from a `number` or `undefined`.\n   *\n   * @example\n   * MoveOption.U16(1).isSome() === true;\n   * MoveOption.U16().isSome() === false;\n   * MoveOption.U16(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U16> with an inner value `value`\n   */\n  static U16(value?: number | null): MoveOption<U16> {\n    return new MoveOption<U16>(value !== null && value !== undefined ? new U16(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U32> from a `number` or `undefined`.\n   *\n   * @example\n   * MoveOption.U32(1).isSome() === true;\n   * MoveOption.U32().isSome() === false;\n   * MoveOption.U32(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U32> with an inner value `value`\n   */\n  static U32(value?: number | null): MoveOption<U32> {\n    return new MoveOption<U32>(value !== null && value !== undefined ? new U32(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U64> from a `number` or a `bigint` or `undefined`.\n   *\n   * @example\n   * MoveOption.U64(1).isSome() === true;\n   * MoveOption.U64().isSome() === false;\n   * MoveOption.U64(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U64> with an inner value `value`\n   */\n  static U64(value?: AnyNumber | null): MoveOption<U64> {\n    return new MoveOption<U64>(value !== null && value !== undefined ? new U64(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U128> from a `number` or a `bigint` or `undefined`.\n   *\n   * @example\n   * MoveOption.U128(1).isSome() === true;\n   * MoveOption.U128().isSome() === false;\n   * MoveOption.U128(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U128> with an inner value `value`\n   */\n  static U128(value?: AnyNumber | null): MoveOption<U128> {\n    return new MoveOption<U128>(value !== null && value !== undefined ? new U128(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U256> from a `number` or a `bigint` or `undefined`.\n   *\n   * @example\n   * MoveOption.U256(1).isSome() === true;\n   * MoveOption.U256().isSome() === false;\n   * MoveOption.U256(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U256> with an inner value `value`\n   */\n  static U256(value?: AnyNumber | null): MoveOption<U256> {\n    return new MoveOption<U256>(value !== null && value !== undefined ? new U256(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<Bool> from a `boolean` or `undefined`.\n   *\n   * @example\n   * MoveOption.Bool(true).isSome() === true;\n   * MoveOption.Bool().isSome() === false;\n   * MoveOption.Bool(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<Bool> with an inner value `value`\n   */\n  static Bool(value?: boolean | null): MoveOption<Bool> {\n    return new MoveOption<Bool>(value !== null && value !== undefined ? new Bool(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<MoveString> from a `string` or `undefined`.\n   *\n   * @example\n   * MoveOption.MoveString(\"hello\").isSome() === true;\n   * MoveOption.MoveString(\"\").isSome() === true;\n   * MoveOption.MoveString().isSome() === false;\n   * MoveOption.MoveString(undefined).isSome() === false;\n   * @params value: the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<MoveString> with an inner value `value`\n   */\n  static MoveString(value?: string | null): MoveOption<MoveString> {\n    return new MoveOption<MoveString>(value !== null && value !== undefined ? new MoveString(value) : undefined);\n  }\n\n  static deserialize<U extends Serializable & EntryFunctionArgument>(\n    deserializer: Deserializer,\n    cls: Deserializable<U>,\n  ): MoveOption<U> {\n    const vector = MoveVector.deserialize(deserializer, cls);\n    return new MoveOption(vector.values[0]);\n  }\n}\n"], "mappings": ";;;AAgDO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SACHC,CAEV;IAGEC,YAAYC,CAAA,EAAkB;MAC5B,MAAM,GACN,KAAKC,MAAA,GAASD,CAChB;IAAA;IAEAE,0BAA0BF,CAAA,EAA8B;MACtD,IAAMG,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCJ,CAAA,CAAWK,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAMAG,2BAA2BN,CAAA,EAA8B;MAMvD,IAAI,EAJS,KAAKC,MAAA,CAAO,CAAC,aAAaM,CAAA,KAI1B,KAAKN,MAAA,CAAO,CAAC,MAAM,QAC9B,MAAM,IAAIO,KAAA,CAAM,kDAAkD;MAEpER,CAAA,CAAWS,qBAAA,EAAgE,GAC3ET,CAAA,CAAWU,SAAA,CAAU,IAAI,CAC3B;IAAA;IAUA,OAAOC,GAAGX,CAAA,EAAkD;MAC1D,IAAIG,CAAA;MAEJ,IAAIS,KAAA,CAAMC,OAAA,CAAQb,CAAM,KAAK,OAAOA,CAAA,CAAO,CAAC,KAAM,UAChDG,CAAA,GAAUH,CAAA,UACD,OAAOA,CAAA,IAAW,UAAU;QACrC,IAAMc,CAAA,GAAMC,CAAA,CAAIC,YAAA,CAAahB,CAAM;QACnCG,CAAA,GAAUS,KAAA,CAAMK,IAAA,CAAKH,CAAA,CAAII,YAAA,CAAa,CAAC,CACzC;MAAA,WAAWlB,CAAA,YAAkBmB,UAAA,EAC3BhB,CAAA,GAAUS,KAAA,CAAMK,IAAA,CAAKjB,CAAM,OAE3B,MAAM,IAAIQ,KAAA,CAAM,oBAAoB;MAGtC,OAAO,IAAIX,CAAA,CAAeM,CAAA,CAAQiB,GAAA,CAAKN,CAAA,IAAM,IAAIP,CAAA,CAAGO,CAAC,CAAC,CAAC,CACzD;IAAA;IAUA,OAAOO,IAAIrB,CAAA,EAAwC;MACjD,OAAO,IAAIH,CAAA,CAAgBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAImB,CAAA,CAAInB,CAAC,CAAC,CAAC,CAC1D;IAAA;IAUA,OAAOoB,IAAIvB,CAAA,EAAwC;MACjD,OAAO,IAAIH,CAAA,CAAgBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAIqB,CAAA,CAAIrB,CAAC,CAAC,CAAC,CAC1D;IAAA;IAUA,OAAOsB,IAAIzB,CAAA,EAA2C;MACpD,OAAO,IAAIH,CAAA,CAAgBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAIuB,CAAA,CAAIvB,CAAC,CAAC,CAAC,CAC1D;IAAA;IAUA,OAAOwB,KAAK3B,CAAA,EAA4C;MACtD,OAAO,IAAIH,CAAA,CAAiBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAIyB,CAAA,CAAKzB,CAAC,CAAC,CAAC,CAC5D;IAAA;IAUA,OAAO0B,KAAK7B,CAAA,EAA4C;MACtD,OAAO,IAAIH,CAAA,CAAiBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAI2B,CAAA,CAAK3B,CAAC,CAAC,CAAC,CAC5D;IAAA;IAUA,OAAO4B,KAAK/B,CAAA,EAA0C;MACpD,OAAO,IAAIH,CAAA,CAAiBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAI6B,CAAA,CAAK7B,CAAC,CAAC,CAAC,CAC5D;IAAA;IAUA,OAAO8B,WAAWjC,CAAA,EAA+C;MAC/D,OAAO,IAAIH,CAAA,CAAuBG,CAAA,CAAOoB,GAAA,CAAKjB,CAAA,IAAM,IAAI+B,CAAA,CAAW/B,CAAC,CAAC,CAAC,CACxE;IAAA;IAEAO,UAAUV,CAAA,EAA8B;MACtCA,CAAA,CAAWmC,eAAA,CAAgB,KAAKlC,MAAM,CACxC;IAAA;IAmBA,OAAOmC,YACLpC,CAAA,EACAG,CAAA,EACe;MACf,IAAMW,CAAA,GAASd,CAAA,CAAaqC,uBAAA,CAAwB;QAC9CC,CAAA,GAAS,IAAI1B,KAAA;MACnB,SAAS2B,CAAA,GAAI,GAAGA,CAAA,GAAIzB,CAAA,EAAQyB,CAAA,IAAK,GAC/BD,CAAA,CAAOE,IAAA,CAAKrC,CAAA,CAAIiC,WAAA,CAAYpC,CAAY,CAAC;MAE3C,OAAO,IAAIH,CAAA,CAAWyC,CAAM,CAC9B;IAAA;EACF;EAEaJ,CAAA,GAAN,MAAMrC,CAAA,SAAmBC,CAA4C;IAG1EC,YAAYC,CAAA,EAAe;MACzB,MAAM,GACN,KAAKyC,KAAA,GAAQzC,CACf;IAAA;IAEAU,UAAUV,CAAA,EAA8B;MACtCA,CAAA,CAAW0C,YAAA,CAAa,KAAKD,KAAK,CACpC;IAAA;IAEAvC,0BAA0BF,CAAA,EAA8B;MACtD,IAAMG,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCJ,CAAA,CAAWK,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BN,CAAA,EAA8B;MAEvD,IAAMG,CAAA,GAAmB,KAAKC,UAAA,CAAW,EAAEuC,KAAA,CAAM,CAAC;MAEjC/C,CAAA,CAAWe,EAAA,CAAGR,CAAgB,EACtCG,0BAAA,CAA2BN,CAAU,CAChD;IAAA;IAEA,OAAOoC,YAAYpC,CAAA,EAAwC;MACzD,OAAO,IAAIH,CAAA,CAAWG,CAAA,CAAa4C,cAAA,CAAe,CAAC,CACrD;IAAA;EACF;EAEaC,CAAA,GAAN,MAAMhD,CAAA,SACHC,CAEV;IAKEC,YAAYC,CAAA,EAAkB;MAC5B,MAAM,GACF,OAAOA,CAAA,GAAU,OAAeA,CAAA,KAAU,OAC5C,KAAK8C,GAAA,GAAM,IAAIlD,CAAA,CAAW,CAACI,CAAK,CAAC,IAEjC,KAAK8C,GAAA,GAAM,IAAIlD,CAAA,CAAW,EAAE,GAG9B,CAAC,KAAK6C,KAAK,IAAI,KAAKK,GAAA,CAAI7C,MAC1B;IAAA;IAEAC,0BAA0BF,CAAA,EAA8B;MACtD,IAAMG,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCJ,CAAA,CAAWK,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAkBA4C,OAAA,EAAY;MACV,IAAK,KAAKC,MAAA,CAAO,GAGf,OAAO,KAAKF,GAAA,CAAI7C,MAAA,CAAO,CAAC;MAFxB,MAAM,IAAIO,KAAA,CAAM,6CAA6C,CAIjE;IAAA;IAGAwC,OAAA,EAAkB;MAChB,OAAO,KAAKF,GAAA,CAAI7C,MAAA,CAAOgD,MAAA,KAAW,CACpC;IAAA;IAEAvC,UAAUV,CAAA,EAA8B;MAGtC,KAAK8C,GAAA,CAAIpC,SAAA,CAAUV,CAAU,CAC/B;IAAA;IAaA,OAAOW,GAAGX,CAAA,EAAuC;MAC/C,OAAO,IAAIH,CAAA,CAAeG,CAAA,IAAU,OAA8B,IAAIO,CAAA,CAAGP,CAAK,IAAI,MAAS,CAC7F;IAAA;IAaA,OAAOqB,IAAIrB,CAAA,EAAwC;MACjD,OAAO,IAAIH,CAAA,CAAgBG,CAAA,IAAU,OAA8B,IAAIsB,CAAA,CAAItB,CAAK,IAAI,MAAS,CAC/F;IAAA;IAaA,OAAOuB,IAAIvB,CAAA,EAAwC;MACjD,OAAO,IAAIH,CAAA,CAAgBG,CAAA,IAAU,OAA8B,IAAIwB,CAAA,CAAIxB,CAAK,IAAI,MAAS,CAC/F;IAAA;IAaA,OAAOyB,IAAIzB,CAAA,EAA2C;MACpD,OAAO,IAAIH,CAAA,CAAgBG,CAAA,IAAU,OAA8B,IAAI0B,CAAA,CAAI1B,CAAK,IAAI,MAAS,CAC/F;IAAA;IAaA,OAAO2B,KAAK3B,CAAA,EAA4C;MACtD,OAAO,IAAIH,CAAA,CAAiBG,CAAA,IAAU,OAA8B,IAAI4B,CAAA,CAAK5B,CAAK,IAAI,MAAS,CACjG;IAAA;IAaA,OAAO6B,KAAK7B,CAAA,EAA4C;MACtD,OAAO,IAAIH,CAAA,CAAiBG,CAAA,IAAU,OAA8B,IAAI8B,CAAA,CAAK9B,CAAK,IAAI,MAAS,CACjG;IAAA;IAaA,OAAO+B,KAAK/B,CAAA,EAA0C;MACpD,OAAO,IAAIH,CAAA,CAAiBG,CAAA,IAAU,OAA8B,IAAIgC,CAAA,CAAKhC,CAAK,IAAI,MAAS,CACjG;IAAA;IAcA,OAAOiC,WAAWjC,CAAA,EAA+C;MAC/D,OAAO,IAAIH,CAAA,CAAuBG,CAAA,IAAU,OAA8B,IAAIkC,CAAA,CAAWlC,CAAK,IAAI,MAAS,CAC7G;IAAA;IAEA,OAAOoC,YACLpC,CAAA,EACAG,CAAA,EACe;MACf,IAAMW,CAAA,GAASlB,CAAA,CAAWwC,WAAA,CAAYpC,CAAA,EAAcG,CAAG;MACvD,OAAO,IAAIN,CAAA,CAAWiB,CAAA,CAAOb,MAAA,CAAO,CAAC,CAAC,CACxC;IAAA;EACF;AAAA,SAAAL,CAAA,IAAAsC,CAAA,EAAAA,CAAA,IAAAgB,CAAA,EAAAL,CAAA,IAAArB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}