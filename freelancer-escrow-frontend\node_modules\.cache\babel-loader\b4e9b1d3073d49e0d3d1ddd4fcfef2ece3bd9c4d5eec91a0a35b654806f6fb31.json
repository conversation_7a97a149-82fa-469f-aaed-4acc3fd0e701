{"ast": null, "code": "const U32_MASK64 = /* @__PURE__ */BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n  if (le) return {\n    h: Number(n & U32_MASK64),\n    l: Number(n >> _32n & U32_MASK64)\n  };\n  return {\n    h: Number(n >> _32n & U32_MASK64) | 0,\n    l: Number(n & U32_MASK64) | 0\n  };\n}\nfunction split(lst, le = false) {\n  let Ah = new Uint32Array(lst.length);\n  let Al = new Uint32Array(lst.length);\n  for (let i = 0; i < lst.length; i++) {\n    const {\n      h,\n      l\n    } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\nconst toBig = (h, l) => BigInt(h >>> 0) << _32n | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => h << 32 - s | l >>> s;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => h >>> s | l << 32 - s;\nconst rotrSL = (h, l, s) => h << 32 - s | l >>> s;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => h << 64 - s | l >>> s - 32;\nconst rotrBL = (h, l, s) => h >>> s - 32 | l << 64 - s;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => h << s | l >>> 32 - s;\nconst rotlSL = (h, l, s) => l << s | h >>> 32 - s;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => l << s - 32 | h >>> 64 - s;\nconst rotlBL = (h, l, s) => h << s - 32 | l >>> 64 - s;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return {\n    h: Ah + Bh + (l / 2 ** 32 | 0) | 0,\n    l: l | 0\n  };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => Ah + Bh + Ch + (low / 2 ** 32 | 0) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => Ah + Bh + Ch + Dh + (low / 2 ** 32 | 0) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => Ah + Bh + Ch + Dh + Eh + (low / 2 ** 32 | 0) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L };\n// prettier-ignore\nconst u64 = {\n  fromBig,\n  split,\n  toBig,\n  shrSH,\n  shrSL,\n  rotrSH,\n  rotrSL,\n  rotrBH,\n  rotrBL,\n  rotr32H,\n  rotr32L,\n  rotlSH,\n  rotlSL,\n  rotlBH,\n  rotlBL,\n  add,\n  add3L,\n  add3H,\n  add4L,\n  add4H,\n  add5H,\n  add5L\n};\nexport default u64;", "map": {"version": 3, "names": ["U32_MASK64", "BigInt", "_32n", "fromBig", "n", "le", "h", "Number", "l", "split", "lst", "Ah", "Uint32Array", "length", "Al", "i", "toBig", "shrSH", "_l", "s", "shrSL", "rotrSH", "rotrSL", "rotrBH", "rotrBL", "rotr32H", "_h", "rotr32L", "rotlSH", "rotlSL", "rotlBH", "rotlBL", "add", "Bh", "Bl", "add3L", "Cl", "add3H", "low", "Ch", "add4L", "Dl", "add4H", "Dh", "add5L", "El", "add5H", "Eh", "u64"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\aptos\\node_modules\\@noble\\hashes\\src\\_u64.ts"], "sourcesContent": ["const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n: bigint, le = false) {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false) {\n  let Ah = new Uint32Array(lst.length);\n  let Al = new Uint32Array(lst.length);\n  for (let i = 0; i < lst.length; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number) => h >>> s;\nconst shrSL = (h: number, l: number, s: number) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number) => l;\nconst rotr32L = (h: number, _l: number) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number) => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah: number, Al: number, Bh: number, Bl: number) {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number) =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number) =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number) =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number) =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n// prettier-ignore\nconst u64 = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,eAAgBC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACtD,MAAMC,IAAI,GAAG,eAAgBD,MAAM,CAAC,EAAE,CAAC;AAEvC;AACA,SAASE,OAAOA,CAACC,CAAS,EAAEC,EAAE,GAAG,KAAK;EACpC,IAAIA,EAAE,EAAE,OAAO;IAAEC,CAAC,EAAEC,MAAM,CAACH,CAAC,GAAGJ,UAAU,CAAC;IAAEQ,CAAC,EAAED,MAAM,CAAEH,CAAC,IAAIF,IAAI,GAAIF,UAAU;EAAC,CAAE;EACjF,OAAO;IAAEM,CAAC,EAAEC,MAAM,CAAEH,CAAC,IAAIF,IAAI,GAAIF,UAAU,CAAC,GAAG,CAAC;IAAEQ,CAAC,EAAED,MAAM,CAACH,CAAC,GAAGJ,UAAU,CAAC,GAAG;EAAC,CAAE;AACnF;AAEA,SAASS,KAAKA,CAACC,GAAa,EAAEL,EAAE,GAAG,KAAK;EACtC,IAAIM,EAAE,GAAG,IAAIC,WAAW,CAACF,GAAG,CAACG,MAAM,CAAC;EACpC,IAAIC,EAAE,GAAG,IAAIF,WAAW,CAACF,GAAG,CAACG,MAAM,CAAC;EACpC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;IACnC,MAAM;MAAET,CAAC;MAAEE;IAAC,CAAE,GAAGL,OAAO,CAACO,GAAG,CAACK,CAAC,CAAC,EAAEV,EAAE,CAAC;IACpC,CAACM,EAAE,CAACI,CAAC,CAAC,EAAED,EAAE,CAACC,CAAC,CAAC,CAAC,GAAG,CAACT,CAAC,EAAEE,CAAC,CAAC;EACzB;EACA,OAAO,CAACG,EAAE,EAAEG,EAAE,CAAC;AACjB;AAEA,MAAME,KAAK,GAAGA,CAACV,CAAS,EAAEE,CAAS,KAAMP,MAAM,CAACK,CAAC,KAAK,CAAC,CAAC,IAAIJ,IAAI,GAAID,MAAM,CAACO,CAAC,KAAK,CAAC,CAAC;AACnF;AACA,MAAMS,KAAK,GAAGA,CAACX,CAAS,EAAEY,EAAU,EAAEC,CAAS,KAAKb,CAAC,KAAKa,CAAC;AAC3D,MAAMC,KAAK,GAAGA,CAACd,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,IAAK,EAAE,GAAGa,CAAE,GAAKX,CAAC,KAAKW,CAAE;AAC9E;AACA,MAAME,MAAM,GAAGA,CAACf,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,KAAKa,CAAC,GAAKX,CAAC,IAAK,EAAE,GAAGW,CAAG;AAC/E,MAAMG,MAAM,GAAGA,CAAChB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,IAAK,EAAE,GAAGa,CAAE,GAAKX,CAAC,KAAKW,CAAE;AAC/E;AACA,MAAMI,MAAM,GAAGA,CAACjB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,IAAK,EAAE,GAAGa,CAAE,GAAKX,CAAC,KAAMW,CAAC,GAAG,EAAI;AACtF,MAAMK,MAAM,GAAGA,CAAClB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,KAAMa,CAAC,GAAG,EAAG,GAAKX,CAAC,IAAK,EAAE,GAAGW,CAAG;AACtF;AACA,MAAMM,OAAO,GAAGA,CAACC,EAAU,EAAElB,CAAS,KAAKA,CAAC;AAC5C,MAAMmB,OAAO,GAAGA,CAACrB,CAAS,EAAEY,EAAU,KAAKZ,CAAC;AAC5C;AACA,MAAMsB,MAAM,GAAGA,CAACtB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,IAAIa,CAAC,GAAKX,CAAC,KAAM,EAAE,GAAGW,CAAG;AAC/E,MAAMU,MAAM,GAAGA,CAACvB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMX,CAAC,IAAIW,CAAC,GAAKb,CAAC,KAAM,EAAE,GAAGa,CAAG;AAC/E;AACA,MAAMW,MAAM,GAAGA,CAACxB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMX,CAAC,IAAKW,CAAC,GAAG,EAAG,GAAKb,CAAC,KAAM,EAAE,GAAGa,CAAG;AACtF,MAAMY,MAAM,GAAGA,CAACzB,CAAS,EAAEE,CAAS,EAAEW,CAAS,KAAMb,CAAC,IAAKa,CAAC,GAAG,EAAG,GAAKX,CAAC,KAAM,EAAE,GAAGW,CAAG;AAEtF;AACA;AACA,SAASa,GAAGA,CAACrB,EAAU,EAAEG,EAAU,EAAEmB,EAAU,EAAEC,EAAU;EACzD,MAAM1B,CAAC,GAAG,CAACM,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC;EACjC,OAAO;IAAE5B,CAAC,EAAGK,EAAE,GAAGsB,EAAE,IAAKzB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;IAAEA,CAAC,EAAEA,CAAC,GAAG;EAAC,CAAE;AAC7D;AACA;AACA,MAAM2B,KAAK,GAAGA,CAACrB,EAAU,EAAEoB,EAAU,EAAEE,EAAU,KAAK,CAACtB,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC;AAC1F,MAAMC,KAAK,GAAGA,CAACC,GAAW,EAAE3B,EAAU,EAAEsB,EAAU,EAAEM,EAAU,KAC3D5B,EAAE,GAAGsB,EAAE,GAAGM,EAAE,IAAKD,GAAG,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;AAC5C,MAAME,KAAK,GAAGA,CAAC1B,EAAU,EAAEoB,EAAU,EAAEE,EAAU,EAAEK,EAAU,KAC3D,CAAC3B,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC,IAAIK,EAAE,KAAK,CAAC,CAAC;AACnD,MAAMC,KAAK,GAAGA,CAACJ,GAAW,EAAE3B,EAAU,EAAEsB,EAAU,EAAEM,EAAU,EAAEI,EAAU,KACvEhC,EAAE,GAAGsB,EAAE,GAAGM,EAAE,GAAGI,EAAE,IAAKL,GAAG,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;AACjD,MAAMM,KAAK,GAAGA,CAAC9B,EAAU,EAAEoB,EAAU,EAAEE,EAAU,EAAEK,EAAU,EAAEI,EAAU,KACvE,CAAC/B,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC,IAAIK,EAAE,KAAK,CAAC,CAAC,IAAII,EAAE,KAAK,CAAC,CAAC;AAChE,MAAMC,KAAK,GAAGA,CAACR,GAAW,EAAE3B,EAAU,EAAEsB,EAAU,EAAEM,EAAU,EAAEI,EAAU,EAAEI,EAAU,KACnFpC,EAAE,GAAGsB,EAAE,GAAGM,EAAE,GAAGI,EAAE,GAAGI,EAAE,IAAKT,GAAG,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;AAEtD;AACA,SACEnC,OAAO,EAAEM,KAAK,EAAEO,KAAK,EACrBC,KAAK,EAAEG,KAAK,EACZC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAC9BC,OAAO,EAAEE,OAAO,EAChBC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAC9BC,GAAG,EAAEG,KAAK,EAAEE,KAAK,EAAEG,KAAK,EAAEE,KAAK,EAAEI,KAAK,EAAEF,KAAK;AAE/C;AACA,MAAMI,GAAG,GAAG;EACV7C,OAAO;EAAEM,KAAK;EAAEO,KAAK;EACrBC,KAAK;EAAEG,KAAK;EACZC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAC9BC,OAAO;EAAEE,OAAO;EAChBC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAC9BC,GAAG;EAAEG,KAAK;EAAEE,KAAK;EAAEG,KAAK;EAAEE,KAAK;EAAEI,KAAK;EAAEF;CACzC;AACD,eAAeI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}