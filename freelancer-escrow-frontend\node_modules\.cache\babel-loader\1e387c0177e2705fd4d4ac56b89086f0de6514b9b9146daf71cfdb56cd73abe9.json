{"ast": null, "code": "var t = \"aptos:network\";\nexport { t as a };", "map": {"version": 3, "names": ["t", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosGetNetwork.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { NetworkInfo } from '../misc'\n\n/** Version of the feature. */\nexport type AptosGetNetworkVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosGetNetworkNamespace = 'aptos:network'\n/** TODO: docs */\nexport type AptosGetNetworkFeature = {\n  /** Namespace for the feature. */\n  [AptosGetNetworkNamespace]: {\n    /** Version of the feature API. */\n    version: AptosGetNetworkVersion\n    network: AptosGetNetworkMethod\n  }\n}\n/** TODO: docs */\nexport type AptosGetNetworkMethod = () => Promise<AptosGetNetworkOutput>\n/** TODO: docs */\nexport type AptosGetNetworkOutput = NetworkInfo\n"], "mappings": "AAQO,IAAMA,CAAA,GAA2B;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}