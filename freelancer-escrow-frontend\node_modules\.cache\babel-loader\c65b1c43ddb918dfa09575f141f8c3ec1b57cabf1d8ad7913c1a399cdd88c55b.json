{"ast": null, "code": "var e = \"aptos:changeNetwork\";\nexport { e as a };", "map": {"version": 3, "names": ["e", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosChangeNetwork.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { NetworkInfo, UserResponse } from '../misc'\n\n/** Version of the feature. */\nexport type AptosChangeNetworkVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosChangeNetworkNamespace = 'aptos:changeNetwork'\n/** TODO: docs */\nexport type AptosChangeNetworkFeature = {\n  /** Namespace for the feature. */\n  [AptosChangeNetworkNamespace]: {\n    /** Version of the feature API. */\n    version: AptosChangeNetworkVersion\n    changeNetwork: AptosChangeNetworkMethod\n  }\n}\n\n/** TODO: docs */\nexport type AptosChangeNetworkMethod = (\n  input: AptosChangeNetworkInput\n) => Promise<UserResponse<AptosChangeNetworkOutput>>\n/** TODO: docs */\nexport type AptosChangeNetworkInput = NetworkInfo\n\n/** TODO: docs */\nexport interface AptosChangeNetworkOutput {\n  success: boolean\n  reason?: string\n}\n"], "mappings": "AAQO,IAAMA,CAAA,GAA8B;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}