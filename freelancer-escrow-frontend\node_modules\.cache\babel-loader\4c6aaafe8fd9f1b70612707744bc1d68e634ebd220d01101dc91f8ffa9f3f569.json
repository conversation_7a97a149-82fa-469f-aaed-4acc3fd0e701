{"ast": null, "code": "import { m as c, v as n } from \"./chunk-HIHKTLLM.mjs\";\nimport { b as i } from \"./chunk-HHE63GFW.mjs\";\nimport { c as p } from \"./chunk-FTIW5GGG.mjs\";\nasync function d(o) {\n  let {\n      aptosConfig: s\n    } = o,\n    {\n      data: e\n    } = await i({\n      aptosConfig: s,\n      originMethod: \"getLedgerInfo\",\n      path: \"\"\n    });\n  return e;\n}\nasync function C(o) {\n  let {\n    aptosConfig: s,\n    limit: e\n  } = o;\n  return (await a({\n    aptosConfig: s,\n    query: {\n      query: c,\n      variables: {\n        limit: e\n      }\n    },\n    originMethod: \"getChainTopUserTransactions\"\n  })).user_transactions;\n}\nasync function a(o) {\n  let {\n      aptosConfig: s,\n      query: e,\n      originMethod: t\n    } = o,\n    {\n      data: r\n    } = await p({\n      aptosConfig: s,\n      originMethod: t ?? \"queryIndexer\",\n      path: \"\",\n      body: e,\n      overrides: {\n        WITH_CREDENTIALS: !1\n      }\n    });\n  return r;\n}\nasync function u(o) {\n  let {\n    aptosConfig: s\n  } = o;\n  return (await a({\n    aptosConfig: s,\n    query: {\n      query: n\n    },\n    originMethod: \"getProcessorStatuses\"\n  })).processor_status;\n}\nasync function h(o) {\n  let s = await u({\n    aptosConfig: o.aptosConfig\n  });\n  return BigInt(s[0].last_success_version);\n}\nasync function T(o) {\n  let {\n    aptosConfig: s,\n    processorType: e\n  } = o;\n  return (await a({\n    aptosConfig: s,\n    query: {\n      query: n,\n      variables: {\n        where_condition: {\n          processor: {\n            _eq: e\n          }\n        }\n      }\n    },\n    originMethod: \"getProcessorStatus\"\n  })).processor_status[0];\n}\nexport { d as a, C as b, a as c, u as d, h as e, T as f };", "map": {"version": 3, "names": ["d", "o", "aptosConfig", "s", "data", "e", "i", "originMethod", "path", "C", "limit", "a", "query", "c", "variables", "user_transactions", "t", "r", "p", "body", "overrides", "WITH_CREDENTIALS", "u", "n", "processor_status", "h", "BigInt", "last_success_version", "T", "processorType", "where_condition", "processor", "_eq", "b", "f"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\internal\\general.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This file contains the underlying implementations for exposed API surface in\n * the {@link api/general}. By moving the methods out into a separate file,\n * other namespaces and processes can access these methods without depending on the entire\n * general namespace and without having a dependency cycle error.\n */\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { getAptosFullNode, postAptosIndexer } from \"../client\";\nimport { GetChainTopUserTransactionsResponse, GetProcessorStatusResponse, GraphqlQuery, LedgerInfo } from \"../types\";\nimport { GetChainTopUserTransactionsQuery, GetProcessorStatusQuery } from \"../types/generated/operations\";\nimport { GetChainTopUserTransactions, GetProcessorStatus } from \"../types/generated/queries\";\nimport { ProcessorType } from \"../utils/const\";\n\nexport async function getLedgerInfo(args: { aptosConfig: AptosConfig }): Promise<LedgerInfo> {\n  const { aptosConfig } = args;\n  const { data } = await getAptosFullNode<{}, LedgerInfo>({\n    aptosConfig,\n    originMethod: \"getLedgerInfo\",\n    path: \"\",\n  });\n  return data;\n}\n\nexport async function getChainTopUserTransactions(args: {\n  aptosConfig: AptosConfig;\n  limit: number;\n}): Promise<GetChainTopUserTransactionsResponse> {\n  const { aptosConfig, limit } = args;\n  const graphqlQuery = {\n    query: GetChainTopUserTransactions,\n    variables: { limit },\n  };\n\n  const data = await queryIndexer<GetChainTopUserTransactionsQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getChainTopUserTransactions\",\n  });\n\n  return data.user_transactions;\n}\n\nexport async function queryIndexer<T extends {}>(args: {\n  aptosConfig: AptosConfig;\n  query: GraphqlQuery;\n  originMethod?: string;\n}): Promise<T> {\n  const { aptosConfig, query, originMethod } = args;\n  const { data } = await postAptosIndexer<GraphqlQuery, T>({\n    aptosConfig,\n    originMethod: originMethod ?? \"queryIndexer\",\n    path: \"\",\n    body: query,\n    overrides: { WITH_CREDENTIALS: false },\n  });\n  return data;\n}\n\nexport async function getProcessorStatuses(args: { aptosConfig: AptosConfig }): Promise<GetProcessorStatusResponse> {\n  const { aptosConfig } = args;\n\n  const graphqlQuery = {\n    query: GetProcessorStatus,\n  };\n\n  const data = await queryIndexer<GetProcessorStatusQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getProcessorStatuses\",\n  });\n\n  return data.processor_status;\n}\n\nexport async function getIndexerLastSuccessVersion(args: { aptosConfig: AptosConfig }): Promise<bigint> {\n  const response = await getProcessorStatuses({ aptosConfig: args.aptosConfig });\n  return BigInt(response[0].last_success_version);\n}\n\nexport async function getProcessorStatus(args: {\n  aptosConfig: AptosConfig;\n  processorType: ProcessorType;\n}): Promise<GetProcessorStatusResponse[0]> {\n  const { aptosConfig, processorType } = args;\n\n  const whereCondition: { processor: { _eq: string } } = {\n    processor: { _eq: processorType },\n  };\n\n  const graphqlQuery = {\n    query: GetProcessorStatus,\n    variables: {\n      where_condition: whereCondition,\n    },\n  };\n\n  const data = await queryIndexer<GetProcessorStatusQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getProcessorStatus\",\n  });\n\n  return data.processor_status[0];\n}\n"], "mappings": ";;;AAiBA,eAAsBA,EAAcC,CAAA,EAAyD;EAC3F,IAAM;MAAEC,WAAA,EAAAC;IAAY,IAAIF,CAAA;IAClB;MAAEG,IAAA,EAAAC;IAAK,IAAI,MAAMC,CAAA,CAAiC;MACtDJ,WAAA,EAAAC,CAAA;MACAI,YAAA,EAAc;MACdC,IAAA,EAAM;IACR,CAAC;EACD,OAAOH,CACT;AAAA;AAEA,eAAsBI,EAA4BR,CAAA,EAGD;EAC/C,IAAM;IAAEC,WAAA,EAAAC,CAAA;IAAaO,KAAA,EAAAL;EAAM,IAAIJ,CAAA;EAY/B,QANa,MAAMU,CAAA,CAA+C;IAChET,WAAA,EAAAC,CAAA;IACAS,KAAA,EAPmB;MACnBA,KAAA,EAAOC,CAAA;MACPC,SAAA,EAAW;QAAEJ,KAAA,EAAAL;MAAM;IACrB;IAKEE,YAAA,EAAc;EAChB,CAAC,GAEWQ,iBACd;AAAA;AAEA,eAAsBJ,EAA2BV,CAAA,EAIlC;EACb,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaS,KAAA,EAAAP,CAAA;MAAOE,YAAA,EAAAS;IAAa,IAAIf,CAAA;IACvC;MAAEG,IAAA,EAAAa;IAAK,IAAI,MAAMC,CAAA,CAAkC;MACvDhB,WAAA,EAAAC,CAAA;MACAI,YAAA,EAAcS,CAAA,IAAgB;MAC9BR,IAAA,EAAM;MACNW,IAAA,EAAMd,CAAA;MACNe,SAAA,EAAW;QAAEC,gBAAA,EAAkB;MAAM;IACvC,CAAC;EACD,OAAOJ,CACT;AAAA;AAEA,eAAsBK,EAAqBrB,CAAA,EAAyE;EAClH,IAAM;IAAEC,WAAA,EAAAC;EAAY,IAAIF,CAAA;EAYxB,QANa,MAAMU,CAAA,CAAsC;IACvDT,WAAA,EAAAC,CAAA;IACAS,KAAA,EANmB;MACnBA,KAAA,EAAOW;IACT;IAKEhB,YAAA,EAAc;EAChB,CAAC,GAEWiB,gBACd;AAAA;AAEA,eAAsBC,EAA6BxB,CAAA,EAAqD;EACtG,IAAME,CAAA,GAAW,MAAMmB,CAAA,CAAqB;IAAEpB,WAAA,EAAaD,CAAA,CAAKC;EAAY,CAAC;EAC7E,OAAOwB,MAAA,CAAOvB,CAAA,CAAS,CAAC,EAAEwB,oBAAoB,CAChD;AAAA;AAEA,eAAsBC,EAAmB3B,CAAA,EAGE;EACzC,IAAM;IAAEC,WAAA,EAAAC,CAAA;IAAa0B,aAAA,EAAAxB;EAAc,IAAIJ,CAAA;EAmBvC,QANa,MAAMU,CAAA,CAAsC;IACvDT,WAAA,EAAAC,CAAA;IACAS,KAAA,EATmB;MACnBA,KAAA,EAAOW,CAAA;MACPT,SAAA,EAAW;QACTgB,eAAA,EAPmD;UACrDC,SAAA,EAAW;YAAEC,GAAA,EAAK3B;UAAc;QAClC;MAME;IACF;IAKEE,YAAA,EAAc;EAChB,CAAC,GAEWiB,gBAAA,CAAiB,CAAC,CAChC;AAAA;AAAA,SAAAxB,CAAA,IAAAW,CAAA,EAAAF,CAAA,IAAAwB,CAAA,EAAAtB,CAAA,IAAAE,CAAA,EAAAS,CAAA,IAAAtB,CAAA,EAAAyB,CAAA,IAAApB,CAAA,EAAAuB,CAAA,IAAAM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}