import React, { createContext, useContext, useState, useEffect } from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import aptosService from '../services/aptosService';

const EscrowContext = createContext();

export const useEscrow = () => {
  const context = useContext(EscrowContext);
  if (!context) {
    throw new Error('useEscrow must be used within an EscrowProvider');
  }
  return context;
};

export const EscrowProvider = ({ children }) => {
  const { account, signAndSubmitTransaction } = useWallet();
  const [escrows, setEscrows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [balance, setBalance] = useState('0');

  // Mock data for demonstration
  const mockEscrows = [
    {
      id: 1,
      client: '0x1234...5678',
      freelancer: '0x8765...4321',
      title: 'Website Development',
      description: 'Build a modern React website with responsive design',
      category: 'Web Development',
      amount: 5.0,
      platform_fee: 0.125,
      deadline: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now
      status: aptosService.ESCROW_STATUS.FUNDED,
      created_at: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2 days ago
      funded_at: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
      started_at: 0,
      submitted_at: 0,
      completed_at: 0
    },
    {
      id: 2,
      client: '0x2345...6789',
      freelancer: '0x9876...5432',
      title: 'Mobile App UI Design',
      description: 'Design modern UI/UX for a mobile application',
      category: 'Design',
      amount: 3.5,
      platform_fee: 0.0875,
      deadline: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5 days from now
      status: aptosService.ESCROW_STATUS.IN_PROGRESS,
      created_at: Date.now() - 3 * 24 * 60 * 60 * 1000,
      funded_at: Date.now() - 2 * 24 * 60 * 60 * 1000,
      started_at: Date.now() - 1 * 24 * 60 * 60 * 1000,
      submitted_at: 0,
      completed_at: 0
    },
    {
      id: 3,
      client: '0x3456...7890',
      freelancer: '0x0987...6543',
      title: 'Smart Contract Audit',
      description: 'Security audit for DeFi smart contracts',
      category: 'Blockchain',
      amount: 10.0,
      platform_fee: 0.25,
      deadline: Date.now() + 14 * 24 * 60 * 60 * 1000, // 14 days from now
      status: aptosService.ESCROW_STATUS.SUBMITTED,
      created_at: Date.now() - 5 * 24 * 60 * 60 * 1000,
      funded_at: Date.now() - 4 * 24 * 60 * 60 * 1000,
      started_at: Date.now() - 3 * 24 * 60 * 60 * 1000,
      submitted_at: Date.now() - 1 * 24 * 60 * 60 * 1000,
      completed_at: 0
    }
  ];

  // Load user's escrows
  const loadEscrows = async () => {
    if (!account?.address) return;
    
    setLoading(true);
    try {
      // For now, use mock data
      // In production, this would call aptosService.getUserEscrows(account.address)
      setEscrows(mockEscrows);
    } catch (error) {
      console.error('Error loading escrows:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load account balance
  const loadBalance = async () => {
    if (!account?.address) return;
    
    try {
      const accountBalance = await aptosService.getAccountBalance(account.address);
      setBalance(accountBalance);
    } catch (error) {
      console.error('Error loading balance:', error);
    }
  };

  // Create new escrow
  const createEscrow = async (escrowData) => {
    if (!signAndSubmitTransaction) throw new Error('Wallet not connected');
    
    setLoading(true);
    try {
      const response = await aptosService.createEscrow(signAndSubmitTransaction, escrowData);
      await loadEscrows(); // Refresh escrows
      return response;
    } catch (error) {
      console.error('Error creating escrow:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Fund escrow
  const fundEscrow = async (escrowId) => {
    if (!signAndSubmitTransaction) throw new Error('Wallet not connected');
    
    setLoading(true);
    try {
      const response = await aptosService.fundEscrow(signAndSubmitTransaction, escrowId);
      await loadEscrows(); // Refresh escrows
      await loadBalance(); // Refresh balance
      return response;
    } catch (error) {
      console.error('Error funding escrow:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Start work
  const startWork = async (escrowId) => {
    if (!signAndSubmitTransaction) throw new Error('Wallet not connected');
    
    setLoading(true);
    try {
      const response = await aptosService.startWork(signAndSubmitTransaction, escrowId);
      await loadEscrows(); // Refresh escrows
      return response;
    } catch (error) {
      console.error('Error starting work:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Submit work
  const submitWork = async (escrowId) => {
    if (!signAndSubmitTransaction) throw new Error('Wallet not connected');
    
    setLoading(true);
    try {
      const response = await aptosService.submitWork(signAndSubmitTransaction, escrowId);
      await loadEscrows(); // Refresh escrows
      return response;
    } catch (error) {
      console.error('Error submitting work:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Approve work
  const approveWork = async (escrowId) => {
    if (!signAndSubmitTransaction) throw new Error('Wallet not connected');
    
    setLoading(true);
    try {
      const response = await aptosService.approveWork(signAndSubmitTransaction, escrowId);
      await loadEscrows(); // Refresh escrows
      await loadBalance(); // Refresh balance
      return response;
    } catch (error) {
      console.error('Error approving work:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Load data when account changes
  useEffect(() => {
    if (account?.address) {
      loadEscrows();
      loadBalance();
    } else {
      setEscrows([]);
      setBalance('0');
    }
  }, [account?.address]);

  const value = {
    escrows,
    loading,
    balance,
    createEscrow,
    fundEscrow,
    startWork,
    submitWork,
    approveWork,
    loadEscrows,
    loadBalance
  };

  return (
    <EscrowContext.Provider value={value}>
      {children}
    </EscrowContext.Provider>
  );
};

export default EscrowContext;
