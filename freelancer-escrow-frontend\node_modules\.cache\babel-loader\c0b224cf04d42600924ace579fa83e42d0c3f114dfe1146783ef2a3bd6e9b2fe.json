{"ast": null, "code": "var E = (S => (S.FULLNODE = \"Fullnode\", S.INDEXER = \"Indexer\", S.FAUCET = \"Faucet\", S.PEPPER = \"Pepper\", S.PROVER = \"Prover\", S))(E || {}),\n  t = 2e5,\n  R = 20,\n  n = 20,\n  o = \"0x1::aptos_coin::AptosCoin\",\n  T = \"APTOS::RawTransaction\",\n  A = \"APTOS::RawTransactionWithData\",\n  O = (_ => (_.ACCOUNT_TRANSACTION_PROCESSOR = \"account_transactions_processor\", _.DEFAULT = \"default_processor\", _.EVENTS_PROCESSOR = \"events_processor\", _.FUNGIBLE_ASSET_PROCESSOR = \"fungible_asset_processor\", _.STAKE_PROCESSOR = \"stake_processor\", _.TOKEN_V2_PROCESSOR = \"token_v2_processor\", _.USER_TRANSACTION_PROCESSOR = \"user_transaction_processor\", _))(O || {});\nexport { E as a, t as b, R as c, n as d, o as e, T as f, A as g, O as h };", "map": {"version": 3, "names": ["E", "S", "FULLNODE", "INDEXER", "FAUCET", "PEPPER", "PROVER", "t", "R", "n", "o", "T", "A", "O", "_", "ACCOUNT_TRANSACTION_PROCESSOR", "DEFAULT", "EVENTS_PROCESSOR", "FUNGIBLE_ASSET_PROCESSOR", "STAKE_PROCESSOR", "TOKEN_V2_PROCESSOR", "USER_TRANSACTION_PROCESSOR", "a", "b", "c", "d", "e", "f", "g", "h"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\utils\\const.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * Type of API endpoint for request routing\n */\nexport enum AptosApiType {\n  FULLNODE = \"Fullnode\",\n  INDEXER = \"Indexer\",\n  FAUCET = \"Faucet\",\n  PEPPER = \"Pepper\",\n  PROVER = \"Prover\",\n}\n\n/**\n * The default max gas amount when none is given.\n *\n * This is the maximum number of gas units that will be used by a transaction before being rejected.\n *\n * Note that max gas amount varies based on the transaction.  A larger transaction will go over this\n * default gas amount, and the value will need to be changed for the specific transaction.\n */\nexport const DEFAULT_MAX_GAS_AMOUNT = 200000;\n\n/**\n * The default transaction expiration seconds from now.\n *\n * This time is how long until the blockchain nodes will reject the transaction.\n *\n * Note that the transaction expiration time varies based on network connection and network load.  It may need to be\n * increased for the transaction to be processed.\n */\nexport const DEFAULT_TXN_EXP_SEC_FROM_NOW = 20;\n\n/**\n * The default number of seconds to wait for a transaction to be processed.\n *\n * This time is the amount of time that the SDK will wait for a transaction to be processed when waiting for\n * the results of the transaction.  It may take longer based on network connection and network load.\n */\nexport const DEFAULT_TXN_TIMEOUT_SEC = 20;\n\n/**\n * The default gas currency for the network.\n */\nexport const APTOS_COIN = \"0x1::aptos_coin::AptosCoin\";\n\nexport const RAW_TRANSACTION_SALT = \"APTOS::RawTransaction\";\nexport const RAW_TRANSACTION_WITH_DATA_SALT = \"APTOS::RawTransactionWithData\";\n\n/**\n * The list of supported Processor types for our indexer api.\n *\n * These can be found from the processor_status table in the indexer database.\n * {@link https://cloud.hasura.io/public/graphiql?endpoint=https://api.mainnet.aptoslabs.com/v1/graphql}\n */\nexport enum ProcessorType {\n  ACCOUNT_TRANSACTION_PROCESSOR = \"account_transactions_processor\",\n  DEFAULT = \"default_processor\",\n  EVENTS_PROCESSOR = \"events_processor\",\n  // Fungible asset processor also handles coins\n  FUNGIBLE_ASSET_PROCESSOR = \"fungible_asset_processor\",\n  STAKE_PROCESSOR = \"stake_processor\",\n  // Token V2 processor replaces Token processor (not only for digital assets)\n  TOKEN_V2_PROCESSOR = \"token_v2_processor\",\n  USER_TRANSACTION_PROCESSOR = \"user_transaction_processor\",\n}\n"], "mappings": "AAMO,IAAKA,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,QAAA,GAAW,YACXD,CAAA,CAAAE,OAAA,GAAU,WACVF,CAAA,CAAAG,MAAA,GAAS,UACTH,CAAA,CAAAI,MAAA,GAAS,UACTJ,CAAA,CAAAK,MAAA,GAAS,UALCL,CAAA,GAAAD,CAAA;EAgBCO,CAAA,GAAyB;EAUzBC,CAAA,GAA+B;EAQ/BC,CAAA,GAA0B;EAK1BC,CAAA,GAAa;EAEbC,CAAA,GAAuB;EACvBC,CAAA,GAAiC;EAQlCC,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,6BAAA,GAAgC,kCAChCD,CAAA,CAAAE,OAAA,GAAU,qBACVF,CAAA,CAAAG,gBAAA,GAAmB,oBAEnBH,CAAA,CAAAI,wBAAA,GAA2B,4BAC3BJ,CAAA,CAAAK,eAAA,GAAkB,mBAElBL,CAAA,CAAAM,kBAAA,GAAqB,sBACrBN,CAAA,CAAAO,0BAAA,GAA6B,8BATnBP,CAAA,GAAAD,CAAA;AAAA,SAAAb,CAAA,IAAAsB,CAAA,EAAAf,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAgB,CAAA,EAAAf,CAAA,IAAAgB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}