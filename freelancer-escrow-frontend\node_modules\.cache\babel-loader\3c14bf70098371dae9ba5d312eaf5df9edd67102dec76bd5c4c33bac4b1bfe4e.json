{"ast": null, "code": "import { a } from \"./chunk-A63SMUOU.mjs\";\nimport { a as i } from \"./chunk-LG7RJQ57.mjs\";\nimport { bytesToHex as o, hexToBytes as l } from \"@noble/hashes/utils\";\nvar h = (s => (s.INCORRECT_NUMBER_OF_BYTES = \"incorrect_number_of_bytes\", s.INVALID_HEX_CHARS = \"invalid_hex_chars\", s.TOO_SHORT = \"too_short\", s.TOO_LONG = \"too_long\", s.LEADING_ZERO_X_REQUIRED = \"leading_zero_x_required\", s.LONG_FORM_REQUIRED_UNLESS_SPECIAL = \"long_form_required_unless_special\", s.INVALID_PADDING_ZEROES = \"INVALID_PADDING_ZEROES\", s))(h || {}),\n  r = class r extends a {\n    constructor(t) {\n      if (super(), t.length !== r.LENGTH) throw new i(\"AccountAddress data should be exactly 32 bytes long\", \"incorrect_number_of_bytes\");\n      this.data = t;\n    }\n    isSpecial() {\n      return this.data.slice(0, this.data.length - 1).every(t => t === 0) && this.data[this.data.length - 1] < 16;\n    }\n    toString() {\n      return `0x${this.toStringWithoutPrefix()}`;\n    }\n    toStringWithoutPrefix() {\n      let t = o(this.data);\n      return this.isSpecial() && (t = t[t.length - 1]), t;\n    }\n    toStringLong() {\n      return `0x${this.toStringLongWithoutPrefix()}`;\n    }\n    toStringLongWithoutPrefix() {\n      return o(this.data);\n    }\n    toUint8Array() {\n      return this.data;\n    }\n    serialize(t) {\n      t.serializeFixedBytes(this.data);\n    }\n    serializeForEntryFunction(t) {\n      let e = this.bcsToBytes();\n      t.serializeBytes(e);\n    }\n    serializeForScriptFunction(t) {\n      t.serializeU32AsUleb128(3), t.serialize(this);\n    }\n    static deserialize(t) {\n      let e = t.deserializeFixedBytes(r.LENGTH);\n      return new r(e);\n    }\n    static fromStringStrict(t) {\n      if (!t.startsWith(\"0x\")) throw new i(\"Hex string must start with a leading 0x.\", \"leading_zero_x_required\");\n      let e = r.fromString(t);\n      if (t.length !== r.LONG_STRING_LENGTH + 2) if (e.isSpecial()) {\n        if (t.length !== 3) throw new i(`The given hex string ${t} is a special address not in LONG form, it must be 0x0 to 0xf without padding zeroes.`, \"INVALID_PADDING_ZEROES\");\n      } else throw new i(`The given hex string ${t} is not a special address, it must be represented as 0x + 64 chars.`, \"long_form_required_unless_special\");\n      return e;\n    }\n    static fromString(t) {\n      let e = t;\n      if (t.startsWith(\"0x\") && (e = t.slice(2)), e.length === 0) throw new i(\"Hex string is too short, must be 1 to 64 chars long, excluding the leading 0x.\", \"too_short\");\n      if (e.length > 64) throw new i(\"Hex string is too long, must be 1 to 64 chars long, excluding the leading 0x.\", \"too_long\");\n      let n;\n      try {\n        n = l(e.padStart(64, \"0\"));\n      } catch (d) {\n        throw new i(`Hex characters are invalid: ${d?.message}`, \"invalid_hex_chars\");\n      }\n      return new r(n);\n    }\n    static from(t) {\n      return t instanceof r ? t : t instanceof Uint8Array ? new r(t) : r.fromString(t);\n    }\n    static fromStrict(t) {\n      return t instanceof r ? t : t instanceof Uint8Array ? new r(t) : r.fromStringStrict(t);\n    }\n    static isValid(t) {\n      try {\n        return t.strict ? r.fromStrict(t.input) : r.from(t.input), {\n          valid: !0\n        };\n      } catch (e) {\n        return {\n          valid: !1,\n          invalidReason: e?.invalidReason,\n          invalidReasonMessage: e?.message\n        };\n      }\n    }\n    equals(t) {\n      return this.data.length !== t.data.length ? !1 : this.data.every((e, n) => e === t.data[n]);\n    }\n  };\nr.LENGTH = 32, r.LONG_STRING_LENGTH = 64, r.ZERO = r.from(\"0x0\"), r.ONE = r.from(\"0x1\"), r.TWO = r.from(\"0x2\"), r.THREE = r.from(\"0x3\"), r.FOUR = r.from(\"0x4\");\nvar c = r;\nexport { h as a, c as b };", "map": {"version": 3, "names": ["bytesToHex", "o", "hexToBytes", "l", "h", "s", "INCORRECT_NUMBER_OF_BYTES", "INVALID_HEX_CHARS", "TOO_SHORT", "TOO_LONG", "LEADING_ZERO_X_REQUIRED", "LONG_FORM_REQUIRED_UNLESS_SPECIAL", "INVALID_PADDING_ZEROES", "r", "a", "constructor", "t", "length", "LENGTH", "i", "data", "isSpecial", "slice", "every", "toString", "toStringWithoutPrefix", "toStringLong", "toStringLongWithoutPrefix", "toUint8Array", "serialize", "serializeFixedBytes", "serializeForEntryFunction", "e", "bcsToBytes", "serializeBytes", "serializeForScriptFunction", "serializeU32AsUleb128", "deserialize", "deserializeFixedBytes", "fromStringStrict", "startsWith", "fromString", "LONG_STRING_LENGTH", "n", "padStart", "d", "message", "from", "Uint8Array", "fromStrict", "<PERSON><PERSON><PERSON><PERSON>", "strict", "input", "valid", "invalidReason", "invalidReasonMessage", "equals", "ZERO", "ONE", "TWO", "THREE", "FOUR", "c", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\accountAddress.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bytesToHex, hexToBytes } from \"@noble/hashes/utils\";\nimport { Serializable, Serializer } from \"../bcs/serializer\";\nimport { Deserializer } from \"../bcs/deserializer\";\nimport { Parsing<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./common\";\nimport { TransactionArgument } from \"../transactions/instances/transactionArgument\";\nimport { HexInput, ScriptTransactionArgumentVariants } from \"../types\";\n\n/**\n * This enum is used to explain why an address was invalid.\n */\nexport enum AddressInvalidReason {\n  INCORRECT_NUMBER_OF_BYTES = \"incorrect_number_of_bytes\",\n  INVALID_HEX_CHARS = \"invalid_hex_chars\",\n  TOO_SHORT = \"too_short\",\n  TOO_LONG = \"too_long\",\n  LEADING_ZERO_X_REQUIRED = \"leading_zero_x_required\",\n  LONG_FORM_REQUIRED_UNLESS_SPECIAL = \"long_form_required_unless_special\",\n  INVALID_PADDING_ZEROES = \"INVALID_PADDING_ZEROES\",\n}\n\nexport type AccountAddressInput = HexInput | AccountAddress;\n\n/**\n * NOTE: Only use this class for account addresses. For other hex data, e.g. transaction\n * hashes, use the Hex class.\n *\n * AccountAddress is used for working with account addresses. Account addresses, when\n * represented as a string, generally look like these examples:\n * - 0x1\n * - 0xaa86fe99004361f747f91342ca13c426ca0cccb0c1217677180c9493bad6ef0c\n *\n * Proper formatting and parsing of account addresses is defined by AIP-40.\n * To learn more about the standard, read the AIP here:\n * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n *\n * The comments in this class make frequent reference to the LONG and SHORT formats,\n * as well as \"special\" addresses. To learn what these refer to see AIP-40.\n */\nexport class AccountAddress extends Serializable implements TransactionArgument {\n  /**\n   * This is the internal representation of an account address.\n   */\n  readonly data: Uint8Array;\n\n  /**\n   * The number of bytes that make up an account address.\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The length of an address string in LONG form without a leading 0x.\n   */\n  static readonly LONG_STRING_LENGTH: number = 64;\n\n  static ZERO: AccountAddress = AccountAddress.from(\"0x0\");\n\n  static ONE: AccountAddress = AccountAddress.from(\"0x1\");\n\n  static TWO: AccountAddress = AccountAddress.from(\"0x2\");\n\n  static THREE: AccountAddress = AccountAddress.from(\"0x3\");\n\n  static FOUR: AccountAddress = AccountAddress.from(\"0x4\");\n\n  /**\n   * Creates an instance of AccountAddress from a Uint8Array.\n   *\n   * @param args.data A Uint8Array representing an account address.\n   */\n  constructor(input: Uint8Array) {\n    super();\n    if (input.length !== AccountAddress.LENGTH) {\n      throw new ParsingError(\n        \"AccountAddress data should be exactly 32 bytes long\",\n        AddressInvalidReason.INCORRECT_NUMBER_OF_BYTES,\n      );\n    }\n    this.data = input;\n  }\n\n  /**\n   * Returns whether an address is special, where special is defined as 0x0 to 0xf\n   * inclusive. In other words, the last byte of the address must be < 0b10000 (16)\n   * and every other byte must be zero.\n   *\n   * For more information on how special addresses are defined see AIP-40:\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * @returns true if the address is special, false if not.\n   */\n  isSpecial(): boolean {\n    return (\n      this.data.slice(0, this.data.length - 1).every((byte) => byte === 0) && this.data[this.data.length - 1] < 0b10000\n    );\n  }\n\n  // ===\n  // Methods for representing an instance of AccountAddress as other types.\n  // ===\n\n  /**\n   * Return the AccountAddress as a string as per AIP-40.\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * In short, it means that special addresses are represented in SHORT form, meaning\n   * 0x0 through to 0xf inclusive, and every other address is represented in LONG form,\n   * meaning 0x + 64 hex characters.\n   *\n   * @returns AccountAddress as a string conforming to AIP-40.\n   */\n  toString(): `0x${string}` {\n    return `0x${this.toStringWithoutPrefix()}`;\n  }\n\n  /**\n   * NOTE: Prefer to use `toString` where possible.\n   *\n   * Return the AccountAddress as a string as per AIP-40 but without the leading 0x.\n   *\n   * Learn more by reading the docstring of `toString`.\n   *\n   * @returns AccountAddress as a string conforming to AIP-40 but without the leading 0x.\n   */\n  toStringWithoutPrefix(): string {\n    let hex = bytesToHex(this.data);\n    if (this.isSpecial()) {\n      hex = hex[hex.length - 1];\n    }\n    return hex;\n  }\n\n  /**\n   * NOTE: Prefer to use `toString` where possible.\n   *\n   * Whereas toString will format special addresses (as defined by isSpecial) using the\n   * SHORT form (no leading 0s), this format the address in the LONG format\n   * unconditionally.\n   *\n   * This means it will be 0x + 64 hex characters.\n   *\n   * @returns AccountAddress as a string in LONG form.\n   */\n  toStringLong(): `0x${string}` {\n    return `0x${this.toStringLongWithoutPrefix()}`;\n  }\n\n  /**\n   * NOTE: Prefer to use `toString` where possible.\n   *\n   * Whereas toString will format special addresses (as defined by isSpecial) using the\n   * SHORT form (no leading 0s), this function will include leading zeroes. The string\n   * will not have a leading zero.\n   *\n   * This means it will be 64 hex characters without a leading 0x.\n   *\n   * @returns AccountAddress as a string in LONG form without a leading 0x.\n   */\n  toStringLongWithoutPrefix(): string {\n    return bytesToHex(this.data);\n  }\n\n  /**\n   * Get the inner hex data. The inner data is already a Uint8Array so no conversion\n   * is taking place here, it just returns the inner data.\n   *\n   * @returns Hex data as Uint8Array\n   */\n  toUint8Array(): Uint8Array {\n    return this.data;\n  }\n\n  /**\n   * Serialize the AccountAddress to a Serializer instance's data buffer.\n   * @param serializer The serializer to serialize the AccountAddress to.\n   * @returns void\n   * @example\n   * const serializer = new Serializer();\n   * const address = AccountAddress.fromString(\"0x1\");\n   * address.serialize(serializer);\n   * const bytes = serializer.toUint8Array();\n   * // `bytes` is now the BCS-serialized address.\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.data);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.Address);\n    serializer.serialize(this);\n  }\n\n  /**\n   * Deserialize an AccountAddress from the byte buffer in a Deserializer instance.\n   * @param deserializer The deserializer to deserialize the AccountAddress from.\n   * @returns An instance of AccountAddress.\n   * @example\n   * const bytes = hexToBytes(\"0x0102030405060708091011121314151617181920212223242526272829303132\");\n   * const deserializer = new Deserializer(bytes);\n   * const address = AccountAddress.deserialize(deserializer);\n   * // `address` is now an instance of AccountAddress.\n   */\n  static deserialize(deserializer: Deserializer): AccountAddress {\n    const bytes = deserializer.deserializeFixedBytes(AccountAddress.LENGTH);\n    return new AccountAddress(bytes);\n  }\n\n  // ===\n  // Methods for creating an instance of AccountAddress from other types.\n  // ===\n\n  /**\n   * NOTE: This function has strict parsing behavior. For relaxed behavior, please use\n   * the `fromString` function.\n   *\n   * Creates an instance of AccountAddress from a hex string.\n   *\n   * This function allows only the strictest formats defined by AIP-40. In short this\n   * means only the following formats are accepted:\n   *\n   * - LONG\n   * - SHORT for special addresses\n   *\n   * Where:\n   * - LONG is defined as 0x + 64 hex characters.\n   * - SHORT for special addresses is 0x0 to 0xf inclusive without padding zeroes.\n   *\n   * This means the following are not accepted:\n   * - SHORT for non-special addresses.\n   * - Any address without a leading 0x.\n   *\n   * Learn more about the different address formats by reading AIP-40:\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * @param input A hex string representing an account address.\n   *\n   * @returns An instance of AccountAddress.\n   */\n  static fromStringStrict(input: string): AccountAddress {\n    // Assert the string starts with 0x.\n    if (!input.startsWith(\"0x\")) {\n      throw new ParsingError(\"Hex string must start with a leading 0x.\", AddressInvalidReason.LEADING_ZERO_X_REQUIRED);\n    }\n\n    const address = AccountAddress.fromString(input);\n\n    // Check if the address is in LONG form. If it is not, this is only allowed for\n    // special addresses, in which case we check it is in proper SHORT form.\n    if (input.length !== AccountAddress.LONG_STRING_LENGTH + 2) {\n      if (!address.isSpecial()) {\n        throw new ParsingError(\n          `The given hex string ${input} is not a special address, it must be represented as 0x + 64 chars.`,\n          AddressInvalidReason.LONG_FORM_REQUIRED_UNLESS_SPECIAL,\n        );\n      } else if (input.length !== 3) {\n        // 0x + one hex char is the only valid SHORT form for special addresses.\n        throw new ParsingError(\n          // eslint-disable-next-line max-len\n          `The given hex string ${input} is a special address not in LONG form, it must be 0x0 to 0xf without padding zeroes.`,\n          AddressInvalidReason.INVALID_PADDING_ZEROES,\n        );\n      }\n    }\n\n    return address;\n  }\n\n  /**\n   * NOTE: This function has relaxed parsing behavior. For strict behavior, please use\n   * the `fromStringStrict` function. Where possible use `fromStringStrict` rather than this\n   * function, `fromString` is only provided for backwards compatibility.\n   *\n   * Creates an instance of AccountAddress from a hex string.\n   *\n   * This function allows all formats defined by AIP-40. In short this means the\n   * following formats are accepted:\n   *\n   * - LONG, with or without leading 0x\n   * - SHORT, with or without leading 0x\n   *\n   * Where:\n   * - LONG is 64 hex characters.\n   * - SHORT is 1 to 63 hex characters inclusive.\n   * - Padding zeroes are allowed, e.g. 0x0123 is valid.\n   *\n   * Learn more about the different address formats by reading AIP-40:\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * @param input A hex string representing an account address.\n   *\n   * @returns An instance of AccountAddress.\n   */\n  static fromString(input: string): AccountAddress {\n    let parsedInput = input;\n    // Remove leading 0x for parsing.\n    if (input.startsWith(\"0x\")) {\n      parsedInput = input.slice(2);\n    }\n\n    // Ensure the address string is at least 1 character long.\n    if (parsedInput.length === 0) {\n      throw new ParsingError(\n        \"Hex string is too short, must be 1 to 64 chars long, excluding the leading 0x.\",\n        AddressInvalidReason.TOO_SHORT,\n      );\n    }\n\n    // Ensure the address string is not longer than 64 characters.\n    if (parsedInput.length > 64) {\n      throw new ParsingError(\n        \"Hex string is too long, must be 1 to 64 chars long, excluding the leading 0x.\",\n        AddressInvalidReason.TOO_LONG,\n      );\n    }\n\n    let addressBytes: Uint8Array;\n    try {\n      // Pad the address with leading zeroes, so it is 64 chars long and then convert\n      // the hex string to bytes. Every two characters in a hex string constitutes a\n      // single byte. So a 64 length hex string becomes a 32 byte array.\n      addressBytes = hexToBytes(parsedInput.padStart(64, \"0\"));\n    } catch (error: any) {\n      // At this point the only way this can fail is if the hex string contains\n      // invalid characters.\n      throw new ParsingError(`Hex characters are invalid: ${error?.message}`, AddressInvalidReason.INVALID_HEX_CHARS);\n    }\n\n    return new AccountAddress(addressBytes);\n  }\n\n  /**\n   * Convenience method for creating an AccountAddress from all known inputs.\n   *\n   * This handles, Uint8array, string, and AccountAddress itself\n   * @param input\n   */\n  static from(input: AccountAddressInput): AccountAddress {\n    if (input instanceof AccountAddress) {\n      return input;\n    }\n    if (input instanceof Uint8Array) {\n      return new AccountAddress(input);\n    }\n    return AccountAddress.fromString(input);\n  }\n\n  /**\n   * Convenience method for creating an AccountAddress from all known inputs.\n   *\n   * This handles, Uint8array, string, and AccountAddress itself\n   * @param input\n   */\n  static fromStrict(input: AccountAddressInput): AccountAddress {\n    if (input instanceof AccountAddress) {\n      return input;\n    }\n    if (input instanceof Uint8Array) {\n      return new AccountAddress(input);\n    }\n    return AccountAddress.fromStringStrict(input);\n  }\n\n  // ===\n  // Methods for checking validity.\n  // ===\n\n  /**\n   * Check if the string is a valid AccountAddress.\n   *\n   * @param args.input A hex string representing an account address.\n   * @param args.strict If true, use strict parsing behavior. If false, use relaxed parsing behavior.\n   *\n   * @returns valid = true if the string is valid, valid = false if not. If the string\n   * is not valid, invalidReason will be set explaining why it is invalid.\n   */\n  static isValid(args: { input: AccountAddressInput; strict?: boolean }): ParsingResult<AddressInvalidReason> {\n    try {\n      if (args.strict) {\n        AccountAddress.fromStrict(args.input);\n      } else {\n        AccountAddress.from(args.input);\n      }\n      return { valid: true };\n    } catch (error: any) {\n      return {\n        valid: false,\n        invalidReason: error?.invalidReason,\n        invalidReasonMessage: error?.message,\n      };\n    }\n  }\n\n  /**\n   * Return whether AccountAddresses are equal. AccountAddresses are considered equal\n   * if their underlying byte data is identical.\n   *\n   * @param other The AccountAddress to compare to.\n   * @returns true if the AccountAddresses are equal, false if not.\n   */\n  equals(other: AccountAddress): boolean {\n    if (this.data.length !== other.data.length) return false;\n    return this.data.every((value, index) => value === other.data[index]);\n  }\n}\n"], "mappings": ";;AAGA,SAASA,UAAA,IAAAC,CAAA,EAAYC,UAAA,IAAAC,CAAA,QAAkB;AAUhC,IAAKC,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,yBAAA,GAA4B,6BAC5BD,CAAA,CAAAE,iBAAA,GAAoB,qBACpBF,CAAA,CAAAG,SAAA,GAAY,aACZH,CAAA,CAAAI,QAAA,GAAW,YACXJ,CAAA,CAAAK,uBAAA,GAA0B,2BAC1BL,CAAA,CAAAM,iCAAA,GAAoC,qCACpCN,CAAA,CAAAO,sBAAA,GAAyB,0BAPfP,CAAA,GAAAD,CAAA;EA4BCS,CAAA,GAAN,MAAMA,CAAA,SAAuBC,CAA4C;IA+B9EC,YAAYC,CAAA,EAAmB;MAE7B,IADA,MAAM,GACFA,CAAA,CAAMC,MAAA,KAAWJ,CAAA,CAAeK,MAAA,EAClC,MAAM,IAAIC,CAAA,CACR,uDACA,2BACF;MAEF,KAAKC,IAAA,GAAOJ,CACd;IAAA;IAYAK,UAAA,EAAqB;MACnB,OACE,KAAKD,IAAA,CAAKE,KAAA,CAAM,GAAG,KAAKF,IAAA,CAAKH,MAAA,GAAS,CAAC,EAAEM,KAAA,CAAOP,CAAA,IAASA,CAAA,KAAS,CAAC,KAAK,KAAKI,IAAA,CAAK,KAAKA,IAAA,CAAKH,MAAA,GAAS,CAAC,IAAI,EAE9G;IAAA;IAgBAO,SAAA,EAA0B;MACxB,OAAO,KAAK,KAAKC,qBAAA,CAAsB,CAAC,EAC1C;IAAA;IAWAA,sBAAA,EAAgC;MAC9B,IAAIT,CAAA,GAAMf,CAAA,CAAW,KAAKmB,IAAI;MAC9B,OAAI,KAAKC,SAAA,CAAU,MACjBL,CAAA,GAAMA,CAAA,CAAIA,CAAA,CAAIC,MAAA,GAAS,CAAC,IAEnBD,CACT;IAAA;IAaAU,aAAA,EAA8B;MAC5B,OAAO,KAAK,KAAKC,yBAAA,CAA0B,CAAC,EAC9C;IAAA;IAaAA,0BAAA,EAAoC;MAClC,OAAO1B,CAAA,CAAW,KAAKmB,IAAI,CAC7B;IAAA;IAQAQ,aAAA,EAA2B;MACzB,OAAO,KAAKR,IACd;IAAA;IAaAS,UAAUb,CAAA,EAA8B;MACtCA,CAAA,CAAWc,mBAAA,CAAoB,KAAKV,IAAI,CAC1C;IAAA;IAEAW,0BAA0Bf,CAAA,EAA8B;MACtD,IAAMgB,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCjB,CAAA,CAAWkB,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BnB,CAAA,EAA8B;MACvDA,CAAA,CAAWoB,qBAAA,EAA+D,GAC1EpB,CAAA,CAAWa,SAAA,CAAU,IAAI,CAC3B;IAAA;IAYA,OAAOQ,YAAYrB,CAAA,EAA4C;MAC7D,IAAMgB,CAAA,GAAQhB,CAAA,CAAasB,qBAAA,CAAsBzB,CAAA,CAAeK,MAAM;MACtE,OAAO,IAAIL,CAAA,CAAemB,CAAK,CACjC;IAAA;IAiCA,OAAOO,iBAAiBvB,CAAA,EAA+B;MAErD,IAAI,CAACA,CAAA,CAAMwB,UAAA,CAAW,IAAI,GACxB,MAAM,IAAIrB,CAAA,CAAa,4CAA4C,yBAA4C;MAGjH,IAAMa,CAAA,GAAUnB,CAAA,CAAe4B,UAAA,CAAWzB,CAAK;MAI/C,IAAIA,CAAA,CAAMC,MAAA,KAAWJ,CAAA,CAAe6B,kBAAA,GAAqB,GACvD,IAAKV,CAAA,CAAQX,SAAA,CAAU;QAKhB,IAAIL,CAAA,CAAMC,MAAA,KAAW,GAE1B,MAAM,IAAIE,CAAA,CAER,wBAAwBH,CAAK,yFAC7B,wBACF;MAAA,OAVA,MAAM,IAAIG,CAAA,CACR,wBAAwBH,CAAK,uEAC7B,mCACF;MAWJ,OAAOgB,CACT;IAAA;IA2BA,OAAOS,WAAWzB,CAAA,EAA+B;MAC/C,IAAIgB,CAAA,GAAchB,CAAA;MAOlB,IALIA,CAAA,CAAMwB,UAAA,CAAW,IAAI,MACvBR,CAAA,GAAchB,CAAA,CAAMM,KAAA,CAAM,CAAC,IAIzBU,CAAA,CAAYf,MAAA,KAAW,GACzB,MAAM,IAAIE,CAAA,CACR,kFACA,WACF;MAIF,IAAIa,CAAA,CAAYf,MAAA,GAAS,IACvB,MAAM,IAAIE,CAAA,CACR,iFACA,UACF;MAGF,IAAIwB,CAAA;MACJ,IAAI;QAIFA,CAAA,GAAexC,CAAA,CAAW6B,CAAA,CAAYY,QAAA,CAAS,IAAI,GAAG,CAAC,CACzD;MAAA,SAASC,CAAA,EAAY;QAGnB,MAAM,IAAI1B,CAAA,CAAa,+BAA+B0B,CAAA,EAAOC,OAAO,IAAI,mBAAsC,CAChH;MAAA;MAEA,OAAO,IAAIjC,CAAA,CAAe8B,CAAY,CACxC;IAAA;IAQA,OAAOI,KAAK/B,CAAA,EAA4C;MACtD,OAAIA,CAAA,YAAiBH,CAAA,GACZG,CAAA,GAELA,CAAA,YAAiBgC,UAAA,GACZ,IAAInC,CAAA,CAAeG,CAAK,IAE1BH,CAAA,CAAe4B,UAAA,CAAWzB,CAAK,CACxC;IAAA;IAQA,OAAOiC,WAAWjC,CAAA,EAA4C;MAC5D,OAAIA,CAAA,YAAiBH,CAAA,GACZG,CAAA,GAELA,CAAA,YAAiBgC,UAAA,GACZ,IAAInC,CAAA,CAAeG,CAAK,IAE1BH,CAAA,CAAe0B,gBAAA,CAAiBvB,CAAK,CAC9C;IAAA;IAeA,OAAOkC,QAAQlC,CAAA,EAA6F;MAC1G,IAAI;QACF,OAAIA,CAAA,CAAKmC,MAAA,GACPtC,CAAA,CAAeoC,UAAA,CAAWjC,CAAA,CAAKoC,KAAK,IAEpCvC,CAAA,CAAekC,IAAA,CAAK/B,CAAA,CAAKoC,KAAK,GAEzB;UAAEC,KAAA,EAAO;QAAK,CACvB;MAAA,SAASrB,CAAA,EAAY;QACnB,OAAO;UACLqB,KAAA,EAAO;UACPC,aAAA,EAAetB,CAAA,EAAOsB,aAAA;UACtBC,oBAAA,EAAsBvB,CAAA,EAAOc;QAC/B,CACF;MAAA;IACF;IASAU,OAAOxC,CAAA,EAAgC;MACrC,OAAI,KAAKI,IAAA,CAAKH,MAAA,KAAWD,CAAA,CAAMI,IAAA,CAAKH,MAAA,GAAe,KAC5C,KAAKG,IAAA,CAAKG,KAAA,CAAM,CAACS,CAAA,EAAOW,CAAA,KAAUX,CAAA,KAAUhB,CAAA,CAAMI,IAAA,CAAKuB,CAAK,CAAC,CACtE;IAAA;EACF;AAjXa9B,CAAA,CASKK,MAAA,GAAiB,IATtBL,CAAA,CAcK6B,kBAAA,GAA6B,IAdlC7B,CAAA,CAgBJ4C,IAAA,GAAuB5C,CAAA,CAAekC,IAAA,CAAK,KAAK,GAhB5ClC,CAAA,CAkBJ6C,GAAA,GAAsB7C,CAAA,CAAekC,IAAA,CAAK,KAAK,GAlB3ClC,CAAA,CAoBJ8C,GAAA,GAAsB9C,CAAA,CAAekC,IAAA,CAAK,KAAK,GApB3ClC,CAAA,CAsBJ+C,KAAA,GAAwB/C,CAAA,CAAekC,IAAA,CAAK,KAAK,GAtB7ClC,CAAA,CAwBJgD,IAAA,GAAuBhD,CAAA,CAAekC,IAAA,CAAK,KAAK;AAxBlD,IAAMe,CAAA,GAANjD,CAAA;AAAA,SAAAT,CAAA,IAAAU,CAAA,EAAAgD,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}