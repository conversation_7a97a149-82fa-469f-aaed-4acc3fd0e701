{"ast": null, "code": "import { a as T } from \"./chunk-WQHW4WQN.mjs\";\nimport { d as v } from \"./chunk-YTQVMLFD.mjs\";\nimport { a as b, b as x } from \"./chunk-VTKPSYKA.mjs\";\nimport { d as g } from \"./chunk-AYKZA676.mjs\";\nimport { a as y, b as m } from \"./chunk-QVWBJJRF.mjs\";\nimport { i as A, j as F, k as E, n as f } from \"./chunk-GFRNBBTY.mjs\";\nimport { b as S } from \"./chunk-BF46IXHH.mjs\";\nimport { a as w } from \"./chunk-DZXM2MQY.mjs\";\nimport { a as u } from \"./chunk-A63SMUOU.mjs\";\nimport { b as K } from \"./chunk-BCUSI3N6.mjs\";\nimport { jwtDecode as z } from \"jwt-decode\";\nimport Z from \"eventemitter3\";\nvar o = class o extends u {\n  constructor(e) {\n    super();\n    let {\n      address: r,\n      ephemeralKeyPair: t,\n      uidKey: i,\n      uidVal: a,\n      aud: c,\n      pepper: p,\n      proof: n,\n      proofFetchCallback: s,\n      jwt: l\n    } = e;\n    if (this.ephemeralKeyPair = t, this.publicKey = A.create(e), this.accountAddress = r ? S.from(r) : this.publicKey.authKey().derivedAddress(), this.uidKey = i, this.uidVal = a, this.aud = c, this.jwt = l, this.emitter = new Z(), this.proofOrPromise = n, n instanceof f) this.proof = n;else {\n      if (s === void 0) throw new Error(\"Must provide callback for async proof fetch\");\n      this.emitter.on(\"proofFetchFinish\", async d => {\n        await s(d), this.emitter.removeAllListeners();\n      }), this.init(n);\n    }\n    this.signingScheme = 2;\n    let h = K.fromHexInput(p).toUint8Array();\n    if (h.length !== o.PEPPER_LENGTH) throw new Error(`Pepper length in bytes should be ${o.PEPPER_LENGTH}`);\n    this.pepper = h;\n  }\n  async init(e) {\n    try {\n      this.proof = await e, this.emitter.emit(\"proofFetchFinish\", {\n        status: \"Success\"\n      });\n    } catch (r) {\n      r instanceof Error ? this.emitter.emit(\"proofFetchFinish\", {\n        status: \"Failed\",\n        error: r.toString()\n      }) : this.emitter.emit(\"proofFetchFinish\", {\n        status: \"Failed\",\n        error: \"Unknown\"\n      });\n    }\n  }\n  serialize(e) {\n    if (e.serializeStr(this.jwt), e.serializeStr(this.uidKey), e.serializeFixedBytes(this.pepper), this.ephemeralKeyPair.serialize(e), this.proof === void 0) throw new Error(\"Connot serialize - proof undefined\");\n    this.proof.serialize(e);\n  }\n  static deserialize(e) {\n    let r = e.deserializeStr(),\n      t = e.deserializeStr(),\n      i = e.deserializeFixedBytes(31),\n      a = T.deserialize(e),\n      c = f.deserialize(e);\n    return o.create({\n      proof: c,\n      pepper: i,\n      uidKey: t,\n      jwt: r,\n      ephemeralKeyPair: a\n    });\n  }\n  isExpired() {\n    return this.ephemeralKeyPair.isExpired();\n  }\n  signWithAuthenticator(e) {\n    let r = new m(this.sign(e)),\n      t = new y(this.publicKey);\n    return new g(t, r);\n  }\n  signTransactionWithAuthenticator(e) {\n    let r = new m(this.signTransaction(e)),\n      t = new y(this.publicKey);\n    return new g(t, r);\n  }\n  async waitForProofFetch() {\n    this.proofOrPromise instanceof Promise && (await this.proofOrPromise);\n  }\n  sign(e) {\n    let {\n      expiryDateSecs: r\n    } = this.ephemeralKeyPair;\n    if (this.isExpired()) throw new Error(\"EphemeralKeyPair is expired\");\n    if (this.proof === void 0) throw new Error(\"Proof not defined\");\n    let t = this.ephemeralKeyPair.getPublicKey(),\n      i = this.ephemeralKeyPair.sign(e);\n    return new F({\n      jwtHeader: v(this.jwt.split(\".\")[0]),\n      ephemeralCertificate: new E(this.proof, 0),\n      expiryDateSecs: r,\n      ephemeralPublicKey: t,\n      ephemeralSignature: i\n    });\n  }\n  signTransaction(e) {\n    if (this.proof === void 0) throw new Error(\"Proof not found\");\n    let r = b(e),\n      i = new P(r, this.proof.proof).hash();\n    return this.sign(i);\n  }\n  verifySignature(e) {\n    throw new Error(\"Not implemented\");\n  }\n  static fromBytes(e) {\n    return o.deserialize(new w(e));\n  }\n  static create(e) {\n    let {\n        address: r,\n        proof: t,\n        jwt: i,\n        ephemeralKeyPair: a,\n        pepper: c,\n        uidKey: p = \"sub\",\n        proofFetchCallback: n\n      } = e,\n      s = z(i),\n      l = s.iss;\n    if (typeof s.aud != \"string\") throw new Error(\"aud was not found or an array of values\");\n    let h = s.aud,\n      d = s[p];\n    return new o({\n      address: r,\n      proof: t,\n      ephemeralKeyPair: a,\n      iss: l,\n      uidKey: p,\n      uidVal: d,\n      aud: h,\n      pepper: c,\n      jwt: i,\n      proofFetchCallback: n\n    });\n  }\n};\no.PEPPER_LENGTH = 31;\nvar k = o,\n  P = class extends u {\n    constructor(r, t) {\n      super();\n      this.domainSeparator = \"APTOS::TransactionAndProof\";\n      this.transaction = r, this.proof = t;\n    }\n    serialize(r) {\n      r.serializeFixedBytes(this.transaction.bcsToBytes()), r.serializeOption(this.proof);\n    }\n    hash() {\n      return x(this.bcsToBytes(), this.domainSeparator);\n    }\n  };\nexport { k as a };", "map": {"version": 3, "names": ["jwtDecode", "z", "Z", "o", "u", "constructor", "e", "address", "r", "ephemeralKeyPair", "t", "uid<PERSON><PERSON>", "i", "uidVal", "a", "aud", "c", "pepper", "p", "proof", "n", "proofFetchCallback", "s", "jwt", "l", "public<PERSON>ey", "A", "create", "accountAddress", "S", "from", "auth<PERSON><PERSON>", "derivedAddress", "emitter", "proofOrPromise", "f", "Error", "on", "d", "removeAllListeners", "init", "signingScheme", "h", "K", "fromHexInput", "toUint8Array", "length", "PEPPER_LENGTH", "emit", "status", "error", "toString", "serialize", "serializeStr", "serializeFixedBytes", "deserialize", "deserializeStr", "deserializeFixedBytes", "T", "isExpired", "signWithAuthenticator", "m", "sign", "y", "g", "signTransactionWithAuthenticator", "signTransaction", "waitForProofFetch", "Promise", "expiryDateSecs", "getPublicKey", "F", "jwtHeader", "v", "split", "ephemeralCertificate", "E", "ephemeralPublicKey", "ephemeralSignature", "b", "P", "hash", "verifySignature", "fromBytes", "w", "iss", "k", "domainSeparator", "transaction", "bcsToBytes", "serializeOption", "x"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\account\\KeylessAccount.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { JwtPayload, jwtDecode } from \"jwt-decode\";\nimport EventEmitter from \"eventemitter3\";\nimport { EphemeralCertificateVariant, HexInput, SigningScheme } from \"../types\";\nimport { AccountAddress } from \"../core/accountAddress\";\nimport {\n  AnyPublicKey,\n  AnySignature,\n  KeylessPublicKey,\n  KeylessSignature,\n  EphemeralCertificate,\n  Signature,\n  ZeroKnowledgeSig,\n  ZkProof,\n} from \"../core/crypto\";\n\nimport { Account } from \"./Account\";\nimport { EphemeralKeyPair } from \"./EphemeralKeyPair\";\nimport { Hex } from \"../core/hex\";\nimport { AccountAuthenticatorSingleKey } from \"../transactions/authenticator/account\";\nimport { Deserializer, Serializable, Serializer } from \"../bcs\";\nimport { deriveTransactionType, generateSigningMessage } from \"../transactions/transactionBuilder/signingMessage\";\nimport { AnyRawTransaction, AnyRawTransactionInstance } from \"../transactions/types\";\nimport { base64UrlDecode } from \"../utils/helpers\";\n\n/**\n * Account implementation for the Keyless authentication scheme.\n *\n * Used to represent a Keyless based account and sign transactions with it.\n *\n * Use KeylessAccount.fromJWTAndProof to instantiate a KeylessAccount with a JWT, proof and EphemeralKeyPair.\n *\n * When the proof expires or the JWT becomes invalid, the KeylessAccount must be instantiated again with a new JWT,\n * EphemeralKeyPair, and corresponding proof.\n */\nexport class KeylessAccount extends Serializable implements Account {\n  static readonly PEPPER_LENGTH: number = 31;\n\n  /**\n   * The KeylessPublicKey associated with the account\n   */\n  readonly publicKey: KeylessPublicKey;\n\n  /**\n   * The EphemeralKeyPair used to generate sign.\n   */\n  readonly ephemeralKeyPair: EphemeralKeyPair;\n\n  /**\n   * The claim on the JWT to identify a user.  This is typically 'sub' or 'email'.\n   */\n  readonly uidKey: string;\n\n  /**\n   * The value of the uidKey claim on the JWT.  This intended to be a stable user identifier.\n   */\n  readonly uidVal: string;\n\n  /**\n   * The value of the 'aud' claim on the JWT, also known as client ID.  This is the identifier for the dApp's\n   * OIDC registration with the identity provider.\n   */\n  readonly aud: string;\n\n  /**\n   * A value contains 31 bytes of entropy that preserves privacy of the account. Typically fetched from a pepper provider.\n   */\n  readonly pepper: Uint8Array;\n\n  /**\n   * Account address associated with the account\n   */\n  readonly accountAddress: AccountAddress;\n\n  /**\n   * The zero knowledge signature (if ready) which contains the proof used to validate the EphemeralKeyPair.\n   */\n  proof: ZeroKnowledgeSig | undefined;\n\n  /**\n   * The proof of the EphemeralKeyPair or a promise that provides the proof.  This is used to allow for awaiting on\n   * fetching the proof.\n   */\n  readonly proofOrPromise: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;\n\n  /**\n   * Signing scheme used to sign transactions\n   */\n  readonly signingScheme: SigningScheme;\n\n  /**\n   * The JWT token used to derive the account\n   */\n  readonly jwt: string;\n\n  /**\n   * An event emitter used to assist in handling asycronous proof fetching.\n   */\n  private readonly emitter: EventEmitter<ProofFetchEvents>;\n\n  // Use the static constructor 'create' instead.\n  private constructor(args: {\n    address?: AccountAddress;\n    ephemeralKeyPair: EphemeralKeyPair;\n    iss: string;\n    uidKey: string;\n    uidVal: string;\n    aud: string;\n    pepper: HexInput;\n    proof: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;\n    proofFetchCallback?: ProofFetchCallback;\n    jwt: string;\n  }) {\n    super();\n    const { address, ephemeralKeyPair, uidKey, uidVal, aud, pepper, proof, proofFetchCallback, jwt } = args;\n    this.ephemeralKeyPair = ephemeralKeyPair;\n    this.publicKey = KeylessPublicKey.create(args);\n    this.accountAddress = address ? AccountAddress.from(address) : this.publicKey.authKey().derivedAddress();\n    this.uidKey = uidKey;\n    this.uidVal = uidVal;\n    this.aud = aud;\n    this.jwt = jwt;\n    this.emitter = new EventEmitter<ProofFetchEvents>();\n    this.proofOrPromise = proof;\n    if (proof instanceof ZeroKnowledgeSig) {\n      this.proof = proof;\n    } else {\n      if (proofFetchCallback === undefined) {\n        throw new Error(\"Must provide callback for async proof fetch\");\n      }\n      this.emitter.on(\"proofFetchFinish\", async (status) => {\n        await proofFetchCallback(status);\n        this.emitter.removeAllListeners();\n      });\n      this.init(proof);\n    }\n    this.signingScheme = SigningScheme.SingleKey;\n    const pepperBytes = Hex.fromHexInput(pepper).toUint8Array();\n    if (pepperBytes.length !== KeylessAccount.PEPPER_LENGTH) {\n      throw new Error(`Pepper length in bytes should be ${KeylessAccount.PEPPER_LENGTH}`);\n    }\n    this.pepper = pepperBytes;\n  }\n\n  /**\n   * This initializes the asyncronous proof fetch\n   * @return\n   */\n  async init(promise: Promise<ZeroKnowledgeSig>) {\n    try {\n      this.proof = await promise;\n      this.emitter.emit(\"proofFetchFinish\", { status: \"Success\" });\n    } catch (error) {\n      if (error instanceof Error) {\n        this.emitter.emit(\"proofFetchFinish\", { status: \"Failed\", error: error.toString() });\n      } else {\n        this.emitter.emit(\"proofFetchFinish\", { status: \"Failed\", error: \"Unknown\" });\n      }\n    }\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeStr(this.jwt);\n    serializer.serializeStr(this.uidKey);\n    serializer.serializeFixedBytes(this.pepper);\n    this.ephemeralKeyPair.serialize(serializer);\n    if (this.proof === undefined) {\n      throw new Error(\"Connot serialize - proof undefined\");\n    }\n    this.proof.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): KeylessAccount {\n    const jwt = deserializer.deserializeStr();\n    const uidKey = deserializer.deserializeStr();\n    const pepper = deserializer.deserializeFixedBytes(31);\n    const ephemeralKeyPair = EphemeralKeyPair.deserialize(deserializer);\n    const proof = ZeroKnowledgeSig.deserialize(deserializer);\n    return KeylessAccount.create({\n      proof,\n      pepper,\n      uidKey,\n      jwt,\n      ephemeralKeyPair,\n    });\n  }\n\n  /**\n   * Checks if the proof is expired.  If so the account must be rederived with a new EphemeralKeyPair\n   * and JWT token.\n   * @return boolean\n   */\n  isExpired(): boolean {\n    return this.ephemeralKeyPair.isExpired();\n  }\n\n  /**\n   * Sign a message using Keyless.\n   * @param message the message to sign, as binary input\n   * @return the AccountAuthenticator containing the signature, together with the account's public key\n   */\n  signWithAuthenticator(message: HexInput): AccountAuthenticatorSingleKey {\n    const signature = new AnySignature(this.sign(message));\n    const publicKey = new AnyPublicKey(this.publicKey);\n    return new AccountAuthenticatorSingleKey(publicKey, signature);\n  }\n\n  /**\n   * Sign a transaction using Keyless.\n   * @param transaction the raw transaction\n   * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key\n   */\n  signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorSingleKey {\n    const signature = new AnySignature(this.signTransaction(transaction));\n    const publicKey = new AnyPublicKey(this.publicKey);\n    return new AccountAuthenticatorSingleKey(publicKey, signature);\n  }\n\n  /**\n   * Waits for asyncronous proof fetching to finish.\n   * @return\n   */\n  async waitForProofFetch() {\n    if (this.proofOrPromise instanceof Promise) {\n      await this.proofOrPromise;\n    }\n  }\n\n  /**\n   * Sign the given message using Keyless.\n   * @param message in HexInput format\n   * @returns Signature\n   */\n  sign(data: HexInput): KeylessSignature {\n    const { expiryDateSecs } = this.ephemeralKeyPair;\n    if (this.isExpired()) {\n      throw new Error(\"EphemeralKeyPair is expired\");\n    }\n    if (this.proof === undefined) {\n      throw new Error(\"Proof not defined\");\n    }\n    const ephemeralPublicKey = this.ephemeralKeyPair.getPublicKey();\n    const ephemeralSignature = this.ephemeralKeyPair.sign(data);\n\n    return new KeylessSignature({\n      jwtHeader: base64UrlDecode(this.jwt.split(\".\")[0]),\n      ephemeralCertificate: new EphemeralCertificate(this.proof, EphemeralCertificateVariant.ZkProof),\n      expiryDateSecs,\n      ephemeralPublicKey,\n      ephemeralSignature,\n    });\n  }\n\n  /**\n   * Sign the given transaction with Keyless.\n   * Signs the transaction and proof to guard against proof malleability.\n   * @param transaction the transaction to be signed\n   * @returns KeylessSignature\n   */\n  signTransaction(transaction: AnyRawTransaction): KeylessSignature {\n    if (this.proof === undefined) {\n      throw new Error(\"Proof not found\");\n    }\n    const raw = deriveTransactionType(transaction);\n    const txnAndProof = new TransactionAndProof(raw, this.proof.proof);\n    const signMess = txnAndProof.hash();\n    return this.sign(signMess);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this\n  verifySignature(args: { message: HexInput; signature: Signature }): boolean {\n    throw new Error(\"Not implemented\");\n  }\n\n  static fromBytes(bytes: Uint8Array): KeylessAccount {\n    return KeylessAccount.deserialize(new Deserializer(bytes));\n  }\n\n  static create(args: {\n    address?: AccountAddress;\n    proof: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;\n    jwt: string;\n    ephemeralKeyPair: EphemeralKeyPair;\n    pepper: HexInput;\n    uidKey?: string;\n    proofFetchCallback?: ProofFetchCallback;\n  }): KeylessAccount {\n    const { address, proof, jwt, ephemeralKeyPair, pepper, uidKey = \"sub\", proofFetchCallback } = args;\n\n    const jwtPayload = jwtDecode<JwtPayload & { [key: string]: string }>(jwt);\n    const iss = jwtPayload.iss!;\n    if (typeof jwtPayload.aud !== \"string\") {\n      throw new Error(\"aud was not found or an array of values\");\n    }\n    const aud = jwtPayload.aud!;\n    const uidVal = jwtPayload[uidKey];\n    return new KeylessAccount({\n      address,\n      proof,\n      ephemeralKeyPair,\n      iss,\n      uidKey,\n      uidVal,\n      aud,\n      pepper,\n      jwt,\n      proofFetchCallback,\n    });\n  }\n}\n\n/**\n * A container class to hold a transaction and a proof.  It implements CryptoHashable which is used to create\n * the signing message for Keyless transactions.  We sign over the proof to ensure non-malleability.\n */\nclass TransactionAndProof extends Serializable {\n  /**\n   * The transaction to sign.\n   */\n  transaction: AnyRawTransactionInstance;\n\n  /**\n   * The zero knowledge proof used in signing the transaction.\n   */\n  proof?: ZkProof;\n\n  /**\n   * The domain separator prefix used when hashing.\n   */\n  readonly domainSeparator = \"APTOS::TransactionAndProof\";\n\n  constructor(transaction: AnyRawTransactionInstance, proof?: ZkProof) {\n    super();\n    this.transaction = transaction;\n    this.proof = proof;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.transaction.bcsToBytes());\n    serializer.serializeOption(this.proof);\n  }\n\n  /**\n   * Hashes the bcs serialized from of the class. This is the typescript corollary to the BCSCryptoHash macro in aptos-core.\n   *\n   * @returns Uint8Array\n   */\n  hash(): Uint8Array {\n    return generateSigningMessage(this.bcsToBytes(), this.domainSeparator);\n  }\n}\n\nexport type ProofFetchSuccess = {\n  status: \"Success\";\n};\n\nexport type ProofFetchFailure = {\n  status: \"Failed\";\n  error: string;\n};\n\nexport type ProofFetchStatus = ProofFetchSuccess | ProofFetchFailure;\n\nexport type ProofFetchCallback = (status: ProofFetchStatus) => Promise<void>;\n\nexport interface ProofFetchEvents {\n  proofFetchFinish: (status: ProofFetchStatus) => void;\n}\n"], "mappings": ";;;;;;;;;;AAGA,SAAqBA,SAAA,IAAAC,CAAA,QAAiB;AACtC,OAAOC,CAAA,MAAkB;AAiClB,IAAMC,CAAA,GAAN,MAAMA,CAAA,SAAuBC,CAAgC;EAkE1DC,YAAYC,CAAA,EAWjB;IACD,MAAM;IACN,IAAM;MAAEC,OAAA,EAAAC,CAAA;MAASC,gBAAA,EAAAC,CAAA;MAAkBC,MAAA,EAAAC,CAAA;MAAQC,MAAA,EAAAC,CAAA;MAAQC,GAAA,EAAAC,CAAA;MAAKC,MAAA,EAAAC,CAAA;MAAQC,KAAA,EAAAC,CAAA;MAAOC,kBAAA,EAAAC,CAAA;MAAoBC,GAAA,EAAAC;IAAI,IAAIlB,CAAA;IAUnG,IATA,KAAKG,gBAAA,GAAmBC,CAAA,EACxB,KAAKe,SAAA,GAAYC,CAAA,CAAiBC,MAAA,CAAOrB,CAAI,GAC7C,KAAKsB,cAAA,GAAiBpB,CAAA,GAAUqB,CAAA,CAAeC,IAAA,CAAKtB,CAAO,IAAI,KAAKiB,SAAA,CAAUM,OAAA,CAAQ,EAAEC,cAAA,CAAe,GACvG,KAAKrB,MAAA,GAASC,CAAA,EACd,KAAKC,MAAA,GAASC,CAAA,EACd,KAAKC,GAAA,GAAMC,CAAA,EACX,KAAKO,GAAA,GAAMC,CAAA,EACX,KAAKS,OAAA,GAAU,IAAI/B,CAAA,IACnB,KAAKgC,cAAA,GAAiBd,CAAA,EAClBA,CAAA,YAAiBe,CAAA,EACnB,KAAKhB,KAAA,GAAQC,CAAA,MACR;MACL,IAAIE,CAAA,KAAuB,QACzB,MAAM,IAAIc,KAAA,CAAM,6CAA6C;MAE/D,KAAKH,OAAA,CAAQI,EAAA,CAAG,oBAAoB,MAAOC,CAAA,IAAW;QACpD,MAAMhB,CAAA,CAAmBgB,CAAM,GAC/B,KAAKL,OAAA,CAAQM,kBAAA,CAAmB,CAClC;MAAA,CAAC,GACD,KAAKC,IAAA,CAAKpB,CAAK,CACjB;IAAA;IACA,KAAKqB,aAAA,GAAgB;IACrB,IAAMC,CAAA,GAAcC,CAAA,CAAIC,YAAA,CAAa1B,CAAM,EAAE2B,YAAA,CAAa;IAC1D,IAAIH,CAAA,CAAYI,MAAA,KAAW3C,CAAA,CAAe4C,aAAA,EACxC,MAAM,IAAIX,KAAA,CAAM,oCAAoCjC,CAAA,CAAe4C,aAAa,EAAE;IAEpF,KAAK9B,MAAA,GAASyB,CAChB;EAAA;EAMA,MAAMF,KAAKlC,CAAA,EAAoC;IAC7C,IAAI;MACF,KAAKa,KAAA,GAAQ,MAAMb,CAAA,EACnB,KAAK2B,OAAA,CAAQe,IAAA,CAAK,oBAAoB;QAAEC,MAAA,EAAQ;MAAU,CAAC,CAC7D;IAAA,SAASzC,CAAA,EAAO;MACVA,CAAA,YAAiB4B,KAAA,GACnB,KAAKH,OAAA,CAAQe,IAAA,CAAK,oBAAoB;QAAEC,MAAA,EAAQ;QAAUC,KAAA,EAAO1C,CAAA,CAAM2C,QAAA,CAAS;MAAE,CAAC,IAEnF,KAAKlB,OAAA,CAAQe,IAAA,CAAK,oBAAoB;QAAEC,MAAA,EAAQ;QAAUC,KAAA,EAAO;MAAU,CAAC,CAEhF;IAAA;EACF;EAEAE,UAAU9C,CAAA,EAA8B;IAKtC,IAJAA,CAAA,CAAW+C,YAAA,CAAa,KAAK9B,GAAG,GAChCjB,CAAA,CAAW+C,YAAA,CAAa,KAAK1C,MAAM,GACnCL,CAAA,CAAWgD,mBAAA,CAAoB,KAAKrC,MAAM,GAC1C,KAAKR,gBAAA,CAAiB2C,SAAA,CAAU9C,CAAU,GACtC,KAAKa,KAAA,KAAU,QACjB,MAAM,IAAIiB,KAAA,CAAM,oCAAoC;IAEtD,KAAKjB,KAAA,CAAMiC,SAAA,CAAU9C,CAAU,CACjC;EAAA;EAEA,OAAOiD,YAAYjD,CAAA,EAA4C;IAC7D,IAAME,CAAA,GAAMF,CAAA,CAAakD,cAAA,CAAe;MAClC9C,CAAA,GAASJ,CAAA,CAAakD,cAAA,CAAe;MACrC5C,CAAA,GAASN,CAAA,CAAamD,qBAAA,CAAsB,EAAE;MAC9C3C,CAAA,GAAmB4C,CAAA,CAAiBH,WAAA,CAAYjD,CAAY;MAC5DU,CAAA,GAAQmB,CAAA,CAAiBoB,WAAA,CAAYjD,CAAY;IACvD,OAAOH,CAAA,CAAewB,MAAA,CAAO;MAC3BR,KAAA,EAAAH,CAAA;MACAC,MAAA,EAAAL,CAAA;MACAD,MAAA,EAAAD,CAAA;MACAa,GAAA,EAAAf,CAAA;MACAC,gBAAA,EAAAK;IACF,CAAC,CACH;EAAA;EAOA6C,UAAA,EAAqB;IACnB,OAAO,KAAKlD,gBAAA,CAAiBkD,SAAA,CAAU,CACzC;EAAA;EAOAC,sBAAsBtD,CAAA,EAAkD;IACtE,IAAME,CAAA,GAAY,IAAIqD,CAAA,CAAa,KAAKC,IAAA,CAAKxD,CAAO,CAAC;MAC/CI,CAAA,GAAY,IAAIqD,CAAA,CAAa,KAAKtC,SAAS;IACjD,OAAO,IAAIuC,CAAA,CAA8BtD,CAAA,EAAWF,CAAS,CAC/D;EAAA;EAOAyD,iCAAiC3D,CAAA,EAA+D;IAC9F,IAAME,CAAA,GAAY,IAAIqD,CAAA,CAAa,KAAKK,eAAA,CAAgB5D,CAAW,CAAC;MAC9DI,CAAA,GAAY,IAAIqD,CAAA,CAAa,KAAKtC,SAAS;IACjD,OAAO,IAAIuC,CAAA,CAA8BtD,CAAA,EAAWF,CAAS,CAC/D;EAAA;EAMA,MAAM2D,kBAAA,EAAoB;IACpB,KAAKjC,cAAA,YAA0BkC,OAAA,KACjC,MAAM,KAAKlC,cAEf;EAAA;EAOA4B,KAAKxD,CAAA,EAAkC;IACrC,IAAM;MAAE+D,cAAA,EAAA7D;IAAe,IAAI,KAAKC,gBAAA;IAChC,IAAI,KAAKkD,SAAA,CAAU,GACjB,MAAM,IAAIvB,KAAA,CAAM,6BAA6B;IAE/C,IAAI,KAAKjB,KAAA,KAAU,QACjB,MAAM,IAAIiB,KAAA,CAAM,mBAAmB;IAErC,IAAM1B,CAAA,GAAqB,KAAKD,gBAAA,CAAiB6D,YAAA,CAAa;MACxD1D,CAAA,GAAqB,KAAKH,gBAAA,CAAiBqD,IAAA,CAAKxD,CAAI;IAE1D,OAAO,IAAIiE,CAAA,CAAiB;MAC1BC,SAAA,EAAWC,CAAA,CAAgB,KAAKlD,GAAA,CAAImD,KAAA,CAAM,GAAG,EAAE,CAAC,CAAC;MACjDC,oBAAA,EAAsB,IAAIC,CAAA,CAAqB,KAAKzD,KAAA,GAA0C;MAC9FkD,cAAA,EAAA7D,CAAA;MACAqE,kBAAA,EAAAnE,CAAA;MACAoE,kBAAA,EAAAlE;IACF,CAAC,CACH;EAAA;EAQAsD,gBAAgB5D,CAAA,EAAkD;IAChE,IAAI,KAAKa,KAAA,KAAU,QACjB,MAAM,IAAIiB,KAAA,CAAM,iBAAiB;IAEnC,IAAM5B,CAAA,GAAMuE,CAAA,CAAsBzE,CAAW;MAEvCM,CAAA,GADc,IAAIoE,CAAA,CAAoBxE,CAAA,EAAK,KAAKW,KAAA,CAAMA,KAAK,EACpC8D,IAAA,CAAK;IAClC,OAAO,KAAKnB,IAAA,CAAKlD,CAAQ,CAC3B;EAAA;EAGAsE,gBAAgB5E,CAAA,EAA4D;IAC1E,MAAM,IAAI8B,KAAA,CAAM,iBAAiB,CACnC;EAAA;EAEA,OAAO+C,UAAU7E,CAAA,EAAmC;IAClD,OAAOH,CAAA,CAAeoD,WAAA,CAAY,IAAI6B,CAAA,CAAa9E,CAAK,CAAC,CAC3D;EAAA;EAEA,OAAOqB,OAAOrB,CAAA,EAQK;IACjB,IAAM;QAAEC,OAAA,EAAAC,CAAA;QAASW,KAAA,EAAAT,CAAA;QAAOa,GAAA,EAAAX,CAAA;QAAKH,gBAAA,EAAAK,CAAA;QAAkBG,MAAA,EAAAD,CAAA;QAAQL,MAAA,EAAAO,CAAA,GAAS;QAAOG,kBAAA,EAAAD;MAAmB,IAAId,CAAA;MAExFgB,CAAA,GAAarB,CAAA,CAAkDW,CAAG;MAClEY,CAAA,GAAMF,CAAA,CAAW+D,GAAA;IACvB,IAAI,OAAO/D,CAAA,CAAWP,GAAA,IAAQ,UAC5B,MAAM,IAAIqB,KAAA,CAAM,yCAAyC;IAE3D,IAAMM,CAAA,GAAMpB,CAAA,CAAWP,GAAA;MACjBuB,CAAA,GAAShB,CAAA,CAAWJ,CAAM;IAChC,OAAO,IAAIf,CAAA,CAAe;MACxBI,OAAA,EAAAC,CAAA;MACAW,KAAA,EAAAT,CAAA;MACAD,gBAAA,EAAAK,CAAA;MACAuE,GAAA,EAAA7D,CAAA;MACAb,MAAA,EAAAO,CAAA;MACAL,MAAA,EAAAyB,CAAA;MACAvB,GAAA,EAAA2B,CAAA;MACAzB,MAAA,EAAAD,CAAA;MACAO,GAAA,EAAAX,CAAA;MACAS,kBAAA,EAAAD;IACF,CAAC,CACH;EAAA;AACF;AAlRajB,CAAA,CACK4C,aAAA,GAAwB;AADnC,IAAMuC,CAAA,GAANnF,CAAA;EAwRD6E,CAAA,GAAN,cAAkC5E,CAAa;IAgB7CC,YAAYG,CAAA,EAAwCE,CAAA,EAAiB;MACnE,MAAM;MAHR,KAAS6E,eAAA,GAAkB;MAIzB,KAAKC,WAAA,GAAchF,CAAA,EACnB,KAAKW,KAAA,GAAQT,CACf;IAAA;IAEA0C,UAAU5C,CAAA,EAA8B;MACtCA,CAAA,CAAW8C,mBAAA,CAAoB,KAAKkC,WAAA,CAAYC,UAAA,CAAW,CAAC,GAC5DjF,CAAA,CAAWkF,eAAA,CAAgB,KAAKvE,KAAK,CACvC;IAAA;IAOA8D,KAAA,EAAmB;MACjB,OAAOU,CAAA,CAAuB,KAAKF,UAAA,CAAW,GAAG,KAAKF,eAAe,CACvE;IAAA;EACF;AAAA,SAAAD,CAAA,IAAAxE,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}