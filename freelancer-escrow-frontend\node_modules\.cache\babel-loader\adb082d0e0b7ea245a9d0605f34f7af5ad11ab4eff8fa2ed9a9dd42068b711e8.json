{"ast": null, "code": "import { a as n } from \"./chunk-JPDT6E3B.mjs\";\nimport { b as i } from \"./chunk-BF46IXHH.mjs\";\nimport { a as d } from \"./chunk-A63SMUOU.mjs\";\nvar o = class t extends d {\n  constructor(e, s, r) {\n    super(), this.rawTransaction = e, this.feePayerAddress = r, this.secondarySignerAddresses = s;\n  }\n  serialize(e) {\n    this.rawTransaction.serialize(e), e.serializeVector(this.secondarySignerAddresses), this.feePayerAddress === void 0 ? e.serializeBool(!1) : (e.serializeBool(!0), this.feePayerAddress.serialize(e));\n  }\n  static deserialize(e) {\n    let s = n.deserialize(e),\n      r = e.deserializeVector(i),\n      c = e.deserializeBool(),\n      a;\n    return c && (a = i.deserialize(e)), new t(s, r, a);\n  }\n};\nexport { o as a };", "map": {"version": 3, "names": ["o", "t", "d", "constructor", "e", "s", "r", "rawTransaction", "feePayer<PERSON>dd<PERSON>", "secondarySignerAddresses", "serialize", "serializeVector", "serializeBool", "deserialize", "n", "deserializeVector", "i", "c", "deserializeBool", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\multiAgentTransaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { AccountAddress } from \"../../core\";\nimport { RawTransaction } from \"./rawTransaction\";\n\n/**\n * Representation of a Raw Transaction that can serialized and deserialized\n */\nexport class MultiAgentTransaction extends Serializable {\n  public rawTransaction: RawTransaction;\n\n  public feePayerAddress?: AccountAddress | undefined;\n\n  public secondarySignerAddresses: AccountAddress[];\n\n  /**\n   * SimpleTransaction represents a simple transaction type of a single signer that\n   * can be submitted to Aptos chain for execution.\n   *\n   * SimpleTransaction metadata contains the Raw Transaction and an optional\n   * sponsor Account Address to pay the gas fees.\n   *\n   * @param rawTransaction The Raw Tranasaction\n   * @param feePayerAddress The sponsor Account Address\n   */\n  constructor(\n    rawTransaction: RawTransaction,\n    secondarySignerAddresses: AccountAddress[],\n    feePayerAddress?: AccountAddress,\n  ) {\n    super();\n    this.rawTransaction = rawTransaction;\n    this.feePayerAddress = feePayerAddress;\n    this.secondarySignerAddresses = secondarySignerAddresses;\n  }\n\n  serialize(serializer: Serializer): void {\n    this.rawTransaction.serialize(serializer);\n\n    serializer.serializeVector<AccountAddress>(this.secondarySignerAddresses);\n\n    if (this.feePayerAddress === undefined) {\n      serializer.serializeBool(false);\n    } else {\n      serializer.serializeBool(true);\n      this.feePayerAddress.serialize(serializer);\n    }\n  }\n\n  static deserialize(deserializer: Deserializer): MultiAgentTransaction {\n    const rawTransaction = RawTransaction.deserialize(deserializer);\n\n    const secondarySignerAddresses = deserializer.deserializeVector(AccountAddress);\n\n    const feepayerPresent = deserializer.deserializeBool();\n    let feePayerAddress;\n    if (feepayerPresent) {\n      feePayerAddress = AccountAddress.deserialize(deserializer);\n    }\n\n    return new MultiAgentTransaction(rawTransaction, secondarySignerAddresses, feePayerAddress);\n  }\n}\n"], "mappings": ";;;AAaO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAA8BC,CAAa;EAiBtDC,YACEC,CAAA,EACAC,CAAA,EACAC,CAAA,EACA;IACA,MAAM,GACN,KAAKC,cAAA,GAAiBH,CAAA,EACtB,KAAKI,eAAA,GAAkBF,CAAA,EACvB,KAAKG,wBAAA,GAA2BJ,CAClC;EAAA;EAEAK,UAAUN,CAAA,EAA8B;IACtC,KAAKG,cAAA,CAAeG,SAAA,CAAUN,CAAU,GAExCA,CAAA,CAAWO,eAAA,CAAgC,KAAKF,wBAAwB,GAEpE,KAAKD,eAAA,KAAoB,SAC3BJ,CAAA,CAAWQ,aAAA,CAAc,EAAK,KAE9BR,CAAA,CAAWQ,aAAA,CAAc,EAAI,GAC7B,KAAKJ,eAAA,CAAgBE,SAAA,CAAUN,CAAU,EAE7C;EAAA;EAEA,OAAOS,YAAYT,CAAA,EAAmD;IACpE,IAAMC,CAAA,GAAiBS,CAAA,CAAeD,WAAA,CAAYT,CAAY;MAExDE,CAAA,GAA2BF,CAAA,CAAaW,iBAAA,CAAkBC,CAAc;MAExEC,CAAA,GAAkBb,CAAA,CAAac,eAAA,CAAgB;MACjDC,CAAA;IACJ,OAAIF,CAAA,KACFE,CAAA,GAAkBH,CAAA,CAAeH,WAAA,CAAYT,CAAY,IAGpD,IAAIH,CAAA,CAAsBI,CAAA,EAAgBC,CAAA,EAA0Ba,CAAe,CAC5F;EAAA;AACF;AAAA,SAAAnB,CAAA,IAAAmB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}