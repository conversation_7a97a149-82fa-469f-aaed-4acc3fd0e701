{"ast": null, "code": "import { poseidon1 as g, poseidon2 as h, poseidon3 as u, poseidon4 as a, poseidon5 as c, poseidon6 as d, poseidon7 as p, poseidon8 as l, poseidon9 as f, poseidon10 as A, poseidon11 as U, poseidon12 as b, poseidon13 as w, poseidon14 as E, poseidon15 as I, poseidon16 as y } from \"poseidon-lite\";\nvar e = [g, h, u, a, c, d, p, l, f, A, U, b, w, E, I, y],\n  s = 31,\n  B = 16,\n  i = (B - 1) * s;\nfunction x(n, t) {\n  let o = new TextEncoder().encode(n);\n  return $(o, t);\n}\nfunction $(n, t) {\n  if (n.length > t) throw new Error(`Inputted bytes of length ${n} is longer than ${t}`);\n  let r = T(n, t);\n  return k(r);\n}\nfunction m(n, t) {\n  if (n.length > t) throw new Error(`Input bytes of length ${n} is longer than ${t}`);\n  let r = N(n, t);\n  return _(r);\n}\nfunction T(n, t) {\n  if (n.length > t) throw new Error(`Input bytes of length ${n} is longer than ${t}`);\n  return m(n, t).concat([BigInt(n.length)]);\n}\nfunction _(n) {\n  if (n.length > i) throw new Error(`Can't pack more than ${i}.  Was given ${n.length} bytes`);\n  return P(n, s).map(t => L(t));\n}\nfunction P(n, t) {\n  let r = [];\n  for (let o = 0; o < n.length; o += t) r.push(n.subarray(o, o + t));\n  return r;\n}\nfunction L(n) {\n  let t = BigInt(0);\n  for (let r = n.length - 1; r >= 0; r -= 1) t = t << BigInt(8) | BigInt(n[r]);\n  return t;\n}\nfunction C(n, t) {\n  let r = new Uint8Array(t);\n  for (let o = 0; o < t; o += 1) r[o] = Number(n & BigInt(255)), n >>= BigInt(8);\n  return r;\n}\nfunction N(n, t) {\n  if (t < n.length) throw new Error(\"Padded size must be greater than or equal to the input array size.\");\n  let r = new Uint8Array(t);\n  r.set(n);\n  for (let o = n.length; o < t; o += 1) r[o] = 0;\n  return r;\n}\nfunction k(n) {\n  if (n.length > e.length) throw new Error(`Unable to hash input of length ${n.length}.  Max input length is ${e.length}`);\n  return e[n.length - 1](n);\n}\nexport { x as a, T as b, L as c, C as d, k as e };", "map": {"version": 3, "names": ["poseidon1", "g", "poseidon2", "h", "poseidon3", "u", "poseidon4", "a", "poseidon5", "c", "poseidon6", "d", "poseidon7", "p", "poseidon8", "l", "poseidon9", "f", "poseidon10", "A", "poseidon11", "U", "poseidon12", "b", "poseidon13", "w", "poseidon14", "E", "poseidon15", "I", "poseidon16", "y", "e", "s", "B", "i", "x", "n", "t", "o", "TextEncoder", "encode", "$", "length", "Error", "r", "T", "k", "m", "N", "_", "concat", "BigInt", "P", "map", "L", "push", "subarray", "C", "Uint8Array", "Number", "set"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\poseidon.ts"], "sourcesContent": ["/* eslint-disable no-bitwise */\nimport {\n  poseidon1,\n  poseidon2,\n  poseidon3,\n  poseidon4,\n  poseidon5,\n  poseidon6,\n  poseidon7,\n  poseidon8,\n  poseidon9,\n  poseidon10,\n  poseidon11,\n  poseidon12,\n  poseidon13,\n  poseidon14,\n  poseidon15,\n  poseidon16,\n} from \"poseidon-lite\";\n\nconst numInputsToPoseidonFunc = [\n  poseidon1,\n  poseidon2,\n  poseidon3,\n  poseidon4,\n  poseidon5,\n  poseidon6,\n  poseidon7,\n  poseidon8,\n  poseidon9,\n  poseidon10,\n  poseidon11,\n  poseidon12,\n  poseidon13,\n  poseidon14,\n  poseidon15,\n  poseidon16,\n];\n\nconst BYTES_PACKED_PER_SCALAR = 31;\nconst MAX_NUM_INPUT_SCALARS = 16;\nconst MAX_NUM_INPUT_BYTES = (MAX_NUM_INPUT_SCALARS - 1) * BYTES_PACKED_PER_SCALAR;\n\n/**\n * Hashes a string to a field element via poseidon\n *\n * @returns bigint result of the hash\n */\nexport function hashStrToField(str: string, maxSizeBytes: number): bigint {\n  const textEncoder = new TextEncoder();\n  const strBytes = textEncoder.encode(str);\n  return hashBytesWithLen(strBytes, maxSizeBytes);\n}\n\nfunction hashBytesWithLen(bytes: Uint8Array, maxSizeBytes: number): bigint {\n  if (bytes.length > maxSizeBytes) {\n    throw new Error(`Inputted bytes of length ${bytes} is longer than ${maxSizeBytes}`);\n  }\n  const packed = padAndPackBytesWithLen(bytes, maxSizeBytes);\n  return poseidonHash(packed);\n}\n\nfunction padAndPackBytesNoLen(bytes: Uint8Array, maxSizeBytes: number): bigint[] {\n  if (bytes.length > maxSizeBytes) {\n    throw new Error(`Input bytes of length ${bytes} is longer than ${maxSizeBytes}`);\n  }\n  const paddedStrBytes = padUint8ArrayWithZeros(bytes, maxSizeBytes);\n  return packBytes(paddedStrBytes);\n}\n\nexport function padAndPackBytesWithLen(bytes: Uint8Array, maxSizeBytes: number): bigint[] {\n  if (bytes.length > maxSizeBytes) {\n    throw new Error(`Input bytes of length ${bytes} is longer than ${maxSizeBytes}`);\n  }\n  return padAndPackBytesNoLen(bytes, maxSizeBytes).concat([BigInt(bytes.length)]);\n}\n\nfunction packBytes(bytes: Uint8Array): bigint[] {\n  if (bytes.length > MAX_NUM_INPUT_BYTES) {\n    throw new Error(`Can't pack more than ${MAX_NUM_INPUT_BYTES}.  Was given ${bytes.length} bytes`);\n  }\n  return chunkUint8Array(bytes, BYTES_PACKED_PER_SCALAR).map((chunk) => bytesToBigIntLE(chunk));\n}\n\nfunction chunkUint8Array(array: Uint8Array, chunkSize: number): Uint8Array[] {\n  const result: Uint8Array[] = [];\n  for (let i = 0; i < array.length; i += chunkSize) {\n    result.push(array.subarray(i, i + chunkSize));\n  }\n  return result;\n}\n\nexport function bytesToBigIntLE(bytes: Uint8Array): bigint {\n  let result = BigInt(0);\n  for (let i = bytes.length - 1; i >= 0; i -= 1) {\n    result = (result << BigInt(8)) | BigInt(bytes[i]);\n  }\n  return result;\n}\n\nexport function bigIntToBytesLE(value: bigint, length: number): Uint8Array {\n  const bytes = new Uint8Array(length);\n  for (let i = 0; i < length; i += 1) {\n    bytes[i] = Number(value & BigInt(0xff));\n    // eslint-disable-next-line no-param-reassign\n    value >>= BigInt(8);\n  }\n  return bytes;\n}\n\nfunction padUint8ArrayWithZeros(inputArray: Uint8Array, paddedSize: number): Uint8Array {\n  if (paddedSize < inputArray.length) {\n    throw new Error(\"Padded size must be greater than or equal to the input array size.\");\n  }\n\n  // Create a new Uint8Array with the padded size\n  const paddedArray = new Uint8Array(paddedSize);\n\n  // Copy the content of the input array to the new array\n  paddedArray.set(inputArray);\n\n  // Fill the remaining space with zeros\n  for (let i = inputArray.length; i < paddedSize; i += 1) {\n    paddedArray[i] = 0;\n  }\n\n  return paddedArray;\n}\n\n/**\n * Hashes up to 16 scalar elements via the poseidon hashing algorithm.\n *\n * Each element must be scalar fields of the BN254 elliptic curve group.\n *\n * @returns bigint result of the hash\n */\nexport function poseidonHash(inputs: (number | bigint | string)[]): bigint {\n  if (inputs.length > numInputsToPoseidonFunc.length) {\n    throw new Error(\n      `Unable to hash input of length ${inputs.length}.  Max input length is ${numInputsToPoseidonFunc.length}`,\n    );\n  }\n  return numInputsToPoseidonFunc[inputs.length - 1](inputs);\n}\n"], "mappings": "AACA,SACEA,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,SAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,EACAC,UAAA,IAAAC,CAAA,QACK;AAEP,IAAMC,CAAA,GAA0B,CAC9B/B,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CACF;EAEME,CAAA,GAA0B;EAC1BC,CAAA,GAAwB;EACxBC,CAAA,IAAuBD,CAAA,GAAwB,KAAKD,CAAA;AAOnD,SAASG,EAAeC,CAAA,EAAaC,CAAA,EAA8B;EAExE,IAAMC,CAAA,GADc,IAAIC,WAAA,CAAY,EACPC,MAAA,CAAOJ,CAAG;EACvC,OAAOK,CAAA,CAAiBH,CAAA,EAAUD,CAAY,CAChD;AAAA;AAEA,SAASI,EAAiBL,CAAA,EAAmBC,CAAA,EAA8B;EACzE,IAAID,CAAA,CAAMM,MAAA,GAASL,CAAA,EACjB,MAAM,IAAIM,KAAA,CAAM,4BAA4BP,CAAK,mBAAmBC,CAAY,EAAE;EAEpF,IAAMO,CAAA,GAASC,CAAA,CAAuBT,CAAA,EAAOC,CAAY;EACzD,OAAOS,CAAA,CAAaF,CAAM,CAC5B;AAAA;AAEA,SAASG,EAAqBX,CAAA,EAAmBC,CAAA,EAAgC;EAC/E,IAAID,CAAA,CAAMM,MAAA,GAASL,CAAA,EACjB,MAAM,IAAIM,KAAA,CAAM,yBAAyBP,CAAK,mBAAmBC,CAAY,EAAE;EAEjF,IAAMO,CAAA,GAAiBI,CAAA,CAAuBZ,CAAA,EAAOC,CAAY;EACjE,OAAOY,CAAA,CAAUL,CAAc,CACjC;AAAA;AAEO,SAASC,EAAuBT,CAAA,EAAmBC,CAAA,EAAgC;EACxF,IAAID,CAAA,CAAMM,MAAA,GAASL,CAAA,EACjB,MAAM,IAAIM,KAAA,CAAM,yBAAyBP,CAAK,mBAAmBC,CAAY,EAAE;EAEjF,OAAOU,CAAA,CAAqBX,CAAA,EAAOC,CAAY,EAAEa,MAAA,CAAO,CAACC,MAAA,CAAOf,CAAA,CAAMM,MAAM,CAAC,CAAC,CAChF;AAAA;AAEA,SAASO,EAAUb,CAAA,EAA6B;EAC9C,IAAIA,CAAA,CAAMM,MAAA,GAASR,CAAA,EACjB,MAAM,IAAIS,KAAA,CAAM,wBAAwBT,CAAmB,gBAAgBE,CAAA,CAAMM,MAAM,QAAQ;EAEjG,OAAOU,CAAA,CAAgBhB,CAAA,EAAOJ,CAAuB,EAAEqB,GAAA,CAAKhB,CAAA,IAAUiB,CAAA,CAAgBjB,CAAK,CAAC,CAC9F;AAAA;AAEA,SAASe,EAAgBhB,CAAA,EAAmBC,CAAA,EAAiC;EAC3E,IAAMO,CAAA,GAAuB,EAAC;EAC9B,SAASN,CAAA,GAAI,GAAGA,CAAA,GAAIF,CAAA,CAAMM,MAAA,EAAQJ,CAAA,IAAKD,CAAA,EACrCO,CAAA,CAAOW,IAAA,CAAKnB,CAAA,CAAMoB,QAAA,CAASlB,CAAA,EAAGA,CAAA,GAAID,CAAS,CAAC;EAE9C,OAAOO,CACT;AAAA;AAEO,SAASU,EAAgBlB,CAAA,EAA2B;EACzD,IAAIC,CAAA,GAASc,MAAA,CAAO,CAAC;EACrB,SAASP,CAAA,GAAIR,CAAA,CAAMM,MAAA,GAAS,GAAGE,CAAA,IAAK,GAAGA,CAAA,IAAK,GAC1CP,CAAA,GAAUA,CAAA,IAAUc,MAAA,CAAO,CAAC,IAAKA,MAAA,CAAOf,CAAA,CAAMQ,CAAC,CAAC;EAElD,OAAOP,CACT;AAAA;AAEO,SAASoB,EAAgBrB,CAAA,EAAeC,CAAA,EAA4B;EACzE,IAAMO,CAAA,GAAQ,IAAIc,UAAA,CAAWrB,CAAM;EACnC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,EAAQC,CAAA,IAAK,GAC/BM,CAAA,CAAMN,CAAC,IAAIqB,MAAA,CAAOvB,CAAA,GAAQe,MAAA,CAAO,GAAI,CAAC,GAEtCf,CAAA,KAAUe,MAAA,CAAO,CAAC;EAEpB,OAAOP,CACT;AAAA;AAEA,SAASI,EAAuBZ,CAAA,EAAwBC,CAAA,EAAgC;EACtF,IAAIA,CAAA,GAAaD,CAAA,CAAWM,MAAA,EAC1B,MAAM,IAAIC,KAAA,CAAM,oEAAoE;EAItF,IAAMC,CAAA,GAAc,IAAIc,UAAA,CAAWrB,CAAU;EAG7CO,CAAA,CAAYgB,GAAA,CAAIxB,CAAU;EAG1B,SAASE,CAAA,GAAIF,CAAA,CAAWM,MAAA,EAAQJ,CAAA,GAAID,CAAA,EAAYC,CAAA,IAAK,GACnDM,CAAA,CAAYN,CAAC,IAAI;EAGnB,OAAOM,CACT;AAAA;AASO,SAASE,EAAaV,CAAA,EAA8C;EACzE,IAAIA,CAAA,CAAOM,MAAA,GAASX,CAAA,CAAwBW,MAAA,EAC1C,MAAM,IAAIC,KAAA,CACR,kCAAkCP,CAAA,CAAOM,MAAM,0BAA0BX,CAAA,CAAwBW,MAAM,EACzG;EAEF,OAAOX,CAAA,CAAwBK,CAAA,CAAOM,MAAA,GAAS,CAAC,EAAEN,CAAM,CAC1D;AAAA;AAAA,SAAAD,CAAA,IAAA7B,CAAA,EAAAuC,CAAA,IAAAvB,CAAA,EAAAgC,CAAA,IAAA9C,CAAA,EAAAiD,CAAA,IAAA/C,CAAA,EAAAoC,CAAA,IAAAf,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}