{"ast": null, "code": "import { c as r } from \"./chunk-QAMV4L3K.mjs\";\nimport { b as i } from \"./chunk-FTIW5GGG.mjs\";\nimport { b as t } from \"./chunk-A63SMUOU.mjs\";\nasync function v(a) {\n  let {\n      aptosConfig: o,\n      payload: n,\n      options: s\n    } = a,\n    p = await r({\n      ...n,\n      aptosConfig: o\n    }),\n    e = new t();\n  p.serialize(e);\n  let l = e.toUint8Array(),\n    {\n      data: d\n    } = await i({\n      aptosConfig: o,\n      path: \"view\",\n      originMethod: \"view\",\n      contentType: \"application/x.aptos.view_function+bcs\",\n      params: {\n        ledger_version: s?.ledgerVersion\n      },\n      body: l\n    });\n  return d;\n}\nexport { v as a };", "map": {"version": 3, "names": ["v", "a", "aptosConfig", "o", "payload", "n", "options", "s", "p", "r", "e", "t", "serialize", "l", "toUint8Array", "data", "d", "i", "path", "originMethod", "contentType", "params", "ledger_version", "ledgerVersion", "body"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\internal\\view.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { LedgerVersionArg, MimeType, MoveValue } from \"../types\";\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { generateViewFunctionPayload, InputViewFunctionData } from \"../transactions\";\nimport { Serializer } from \"../bcs\";\nimport { postAptosFullNode } from \"../client\";\n\nexport async function view<T extends Array<MoveValue> = Array<MoveValue>>(args: {\n  aptosConfig: AptosConfig;\n  payload: InputViewFunctionData;\n  options?: LedgerVersionArg;\n}): Promise<T> {\n  const { aptosConfig, payload, options } = args;\n  const viewFunctionPayload = await generateViewFunctionPayload({\n    ...payload,\n    aptosConfig,\n  });\n\n  const serializer = new Serializer();\n  viewFunctionPayload.serialize(serializer);\n  const bytes = serializer.toUint8Array();\n\n  const { data } = await postAptosFullNode<Uint8Array, MoveValue[]>({\n    aptosConfig,\n    path: \"view\",\n    originMethod: \"view\",\n    contentType: MimeType.BCS_VIEW_FUNCTION,\n    params: { ledger_version: options?.ledgerVersion },\n    body: bytes,\n  });\n\n  return data as T;\n}\n"], "mappings": ";;;AASA,eAAsBA,EAAoDC,CAAA,EAI3D;EACb,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,OAAA,EAAAC,CAAA;MAASC,OAAA,EAAAC;IAAQ,IAAIN,CAAA;IACpCO,CAAA,GAAsB,MAAMC,CAAA,CAA4B;MAC5D,GAAGJ,CAAA;MACHH,WAAA,EAAAC;IACF,CAAC;IAEKO,CAAA,GAAa,IAAIC,CAAA;EACvBH,CAAA,CAAoBI,SAAA,CAAUF,CAAU;EACxC,IAAMG,CAAA,GAAQH,CAAA,CAAWI,YAAA,CAAa;IAEhC;MAAEC,IAAA,EAAAC;IAAK,IAAI,MAAMC,CAAA,CAA2C;MAChEf,WAAA,EAAAC,CAAA;MACAe,IAAA,EAAM;MACNC,YAAA,EAAc;MACdC,WAAA;MACAC,MAAA,EAAQ;QAAEC,cAAA,EAAgBf,CAAA,EAASgB;MAAc;MACjDC,IAAA,EAAMX;IACR,CAAC;EAED,OAAOG,CACT;AAAA;AAAA,SAAAhB,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}