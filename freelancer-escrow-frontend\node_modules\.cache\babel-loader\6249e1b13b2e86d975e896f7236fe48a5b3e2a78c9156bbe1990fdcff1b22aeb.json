{"ast": null, "code": "import { b as o } from \"./chunk-BF46IXHH.mjs\";\nimport { a as u, b as r } from \"./chunk-RJ4RKVVQ.mjs\";\nimport { e as t } from \"./chunk-YPHH6CAO.mjs\";\nimport { a as i } from \"./chunk-A63SMUOU.mjs\";\nvar c = class extends i {\n  constructor(e) {\n    super();\n    this.accountAddress = o.ONE;\n    this.moduleName = new r(\"account\");\n    this.structName = new r(\"RotationProofChallenge\");\n    this.sequenceNumber = new t(e.sequenceNumber), this.originator = e.originator, this.currentAuthKey = e.currentAuthKey, this.newPublicKey = u.U8(e.newPublicKey.toUint8Array());\n  }\n  serialize(e) {\n    e.serialize(this.accountAddress), e.serialize(this.moduleName), e.serialize(this.structName), e.serialize(this.sequenceNumber), e.serialize(this.originator), e.serialize(this.currentAuth<PERSON><PERSON>), e.serialize(this.newPublicKey);\n  }\n};\nexport { c as a };", "map": {"version": 3, "names": ["c", "i", "constructor", "e", "accountAddress", "o", "ONE", "moduleName", "r", "structName", "sequenceNumber", "t", "originator", "current<PERSON><PERSON><PERSON><PERSON>", "newPublicKey", "u", "U8", "toUint8Array", "serialize", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\rotationProofChallenge.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../../bcs/serializer\";\nimport { AccountAddress } from \"../../core/accountAddress\";\nimport { AnyNumber } from \"../../types\";\nimport { PublicKey } from \"../../core/crypto\";\nimport { MoveString, MoveVector, U64, U8 } from \"../../bcs\";\n\n/**\n * Representation of the challenge which is needed to sign by owner of the account\n * to rotate the authentication key.\n */\nexport class RotationProofChallenge extends Serializable {\n  // Resource account address\n  public readonly accountAddress: AccountAddress = AccountAddress.ONE;\n\n  // Module name, i.e: 0x1::account\n  public readonly moduleName: MoveString = new MoveString(\"account\");\n\n  // The rotation proof challenge struct name that live under the module\n  public readonly structName: MoveString = new MoveString(\"RotationProofChallenge\");\n\n  // Signer's address\n  public readonly originator: AccountAddress;\n\n  // Signer's current authentication key\n  public readonly currentAuthKey: AccountAddress;\n\n  // New public key to rotate to\n  public readonly newPublicKey: MoveVector<U8>;\n\n  // Sequence number of the account\n  public readonly sequenceNumber: U64;\n\n  constructor(args: {\n    sequenceNumber: AnyNumber;\n    originator: AccountAddress;\n    currentAuthKey: AccountAddress;\n    newPublicKey: PublicKey;\n  }) {\n    super();\n    this.sequenceNumber = new U64(args.sequenceNumber);\n    this.originator = args.originator;\n    this.currentAuthKey = args.currentAuthKey;\n    this.newPublicKey = MoveVector.U8(args.newPublicKey.toUint8Array());\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serialize(this.accountAddress);\n    serializer.serialize(this.moduleName);\n    serializer.serialize(this.structName);\n    serializer.serialize(this.sequenceNumber);\n    serializer.serialize(this.originator);\n    serializer.serialize(this.currentAuthKey);\n    serializer.serialize(this.newPublicKey);\n  }\n}\n"], "mappings": ";;;;AAaO,IAAMA,CAAA,GAAN,cAAqCC,CAAa;EAsBvDC,YAAYC,CAAA,EAKT;IACD,MAAM;IA1BR,KAAgBC,cAAA,GAAiCC,CAAA,CAAeC,GAAA;IAGhE,KAAgBC,UAAA,GAAyB,IAAIC,CAAA,CAAW,SAAS;IAGjE,KAAgBC,UAAA,GAAyB,IAAID,CAAA,CAAW,wBAAwB;IAqB9E,KAAKE,cAAA,GAAiB,IAAIC,CAAA,CAAIR,CAAA,CAAKO,cAAc,GACjD,KAAKE,UAAA,GAAaT,CAAA,CAAKS,UAAA,EACvB,KAAKC,cAAA,GAAiBV,CAAA,CAAKU,cAAA,EAC3B,KAAKC,YAAA,GAAeC,CAAA,CAAWC,EAAA,CAAGb,CAAA,CAAKW,YAAA,CAAaG,YAAA,CAAa,CAAC,CACpE;EAAA;EAEAC,UAAUf,CAAA,EAA8B;IACtCA,CAAA,CAAWe,SAAA,CAAU,KAAKd,cAAc,GACxCD,CAAA,CAAWe,SAAA,CAAU,KAAKX,UAAU,GACpCJ,CAAA,CAAWe,SAAA,CAAU,KAAKT,UAAU,GACpCN,CAAA,CAAWe,SAAA,CAAU,KAAKR,cAAc,GACxCP,CAAA,CAAWe,SAAA,CAAU,KAAKN,UAAU,GACpCT,CAAA,CAAWe,SAAA,CAAU,KAAKL,cAAc,GACxCV,CAAA,CAAWe,SAAA,CAAU,KAAKJ,YAAY,CACxC;EAAA;AACF;AAAA,SAAAd,CAAA,IAAAmB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}