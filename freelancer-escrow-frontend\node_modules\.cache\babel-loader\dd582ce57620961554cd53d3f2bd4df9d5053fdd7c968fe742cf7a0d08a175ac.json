{"ast": null, "code": "var t = \"aptos:account\";\nexport { t as a };", "map": {"version": 3, "names": ["t", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosGetAccount.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AccountInfo } from '../AccountInfo'\n\n/** Version of the feature. */\nexport type AptosGetAccountVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosGetAccountNamespace = 'aptos:account'\n\n/** TODO: docs */\nexport type AptosGetAccountFeature = {\n  /** Namespace for the feature. */\n  [AptosGetAccountNamespace]: {\n    /** Version of the feature API. */\n    version: AptosGetAccountVersion\n    account: AptosGetAccountMethod\n  }\n}\n/** TODO: docs */\nexport type AptosGetAccountMethod = () => Promise<AptoGetsAccountOutput>\n/** TODO: docs */\nexport type AptoGetsAccountOutput = AccountInfo\n"], "mappings": "AAQO,IAAMA,CAAA,GAA2B;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}