{"ast": null, "code": "var o = \"aptos:disconnect\";\nexport { o as a };", "map": {"version": 3, "names": ["o", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosDisconnect.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/** Version of the feature. */\nexport type AptosDisconnectVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosDisconnectNamespace = 'aptos:disconnect'\n/** TODO: docs */\nexport type AptosDisconnectFeature = {\n  /** Namespace for the feature. */\n  [AptosDisconnectNamespace]: {\n    /** Version of the feature API. */\n    version: AptosDisconnectVersion\n    disconnect: AptosDisconnectMethod\n  }\n}\n/** TODO: docs */\nexport type AptosDisconnectMethod = () => Promise<void>\n"], "mappings": "AAMO,IAAMA,CAAA,GAA2B;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}