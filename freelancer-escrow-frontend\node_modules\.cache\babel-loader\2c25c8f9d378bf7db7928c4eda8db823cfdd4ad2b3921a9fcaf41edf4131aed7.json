{"ast": null, "code": "import { a as m, c as V, d as v, e as B } from \"./chunk-IVVWQKCF.mjs\";\nimport { a as P } from \"./chunk-MQGW234H.mjs\";\nimport { a as b, b as y } from \"./chunk-U6X2FYNI.mjs\";\nimport { a as C, c as I } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b as U } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as d } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as H } from \"./chunk-77NXCSLY.mjs\";\nimport { b as x } from \"./chunk-BF46IXHH.mjs\";\nimport { a as T } from \"./chunk-AH44UPM4.mjs\";\nimport { b as w } from \"./chunk-HHE63GFW.mjs\";\nimport { a as G } from \"./chunk-DZXM2MQY.mjs\";\nimport { a as u, b as E } from \"./chunk-A63SMUOU.mjs\";\nimport { b as p } from \"./chunk-BCUSI3N6.mjs\";\nimport { jwtDecode as k } from \"jwt-decode\";\nvar he = 1e7,\n  F = 120,\n  L = 30,\n  N = 330,\n  ge = 120,\n  fe = 350,\n  Se = 300,\n  xe = 93,\n  o = class o extends U {\n    constructor(e, t) {\n      super();\n      let i = p.fromHexInput(t).toUint8Array();\n      if (i.length !== o.ID_COMMITMENT_LENGTH) throw new Error(`Id Commitment length in bytes should be ${o.ID_COMMITMENT_LENGTH}`);\n      this.iss = e, this.idCommitment = i;\n    }\n    authKey() {\n      let e = new E();\n      return e.serializeU32AsUleb128(3), e.serializeFixedBytes(this.bcsToBytes()), H.fromSchemeAndBytes({\n        scheme: 2,\n        input: e.toUint8Array()\n      });\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    toString() {\n      return p.fromHexInput(this.toUint8Array()).toString();\n    }\n    verifySignature(e) {\n      throw new Error(\"Not yet implemented\");\n    }\n    serialize(e) {\n      e.serializeStr(this.iss), e.serializeBytes(this.idCommitment);\n    }\n    static deserialize(e) {\n      let t = e.deserializeStr(),\n        i = e.deserializeBytes();\n      return new o(t, i);\n    }\n    static load(e) {\n      let t = e.deserializeStr(),\n        i = e.deserializeBytes();\n      return new o(t, i);\n    }\n    static isPublicKey(e) {\n      return e instanceof o;\n    }\n    static create(e) {\n      return _(e), new o(e.iss, _(e));\n    }\n    static fromJwtAndPepper(e) {\n      let {\n          jwt: t,\n          pepper: i,\n          uidKey: a = \"sub\"\n        } = e,\n        n = k(t),\n        s = n.iss;\n      if (typeof n.aud != \"string\") throw new Error(\"aud was not found or an array of values\");\n      let M = n.aud,\n        R = n[a];\n      return o.create({\n        iss: s,\n        uidKey: a,\n        uidVal: R,\n        aud: M,\n        pepper: i\n      });\n    }\n  };\no.ID_COMMITMENT_LENGTH = 32;\nvar A = o;\nfunction _(r) {\n  let {\n      uidKey: e,\n      uidVal: t,\n      aud: i,\n      pepper: a\n    } = r,\n    n = [V(p.fromHexInput(a).toUint8Array()), m(i, F), m(t, N), m(e, L)];\n  return v(B(n), A.ID_COMMITMENT_LENGTH);\n}\nvar D = class r extends d {\n    constructor(e) {\n      super();\n      let {\n        jwtHeader: t,\n        ephemeralCertificate: i,\n        expiryDateSecs: a,\n        ephemeralPublicKey: n,\n        ephemeralSignature: s\n      } = e;\n      this.jwtHeader = t, this.ephemeralCertificate = i, this.expiryDateSecs = a, this.ephemeralPublicKey = n, this.ephemeralSignature = s;\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    serialize(e) {\n      this.ephemeralCertificate.serialize(e), e.serializeStr(this.jwtHeader), e.serializeU64(this.expiryDateSecs), this.ephemeralPublicKey.serialize(e), this.ephemeralSignature.serialize(e);\n    }\n    static deserialize(e) {\n      let t = h.deserialize(e),\n        i = e.deserializeStr(),\n        a = e.deserializeU64(),\n        n = b.deserialize(e),\n        s = y.deserialize(e);\n      return new r({\n        jwtHeader: i,\n        expiryDateSecs: Number(a),\n        ephemeralCertificate: t,\n        ephemeralPublicKey: n,\n        ephemeralSignature: s\n      });\n    }\n    static getSimulationSignature() {\n      return new r({\n        jwtHeader: \"{}\",\n        ephemeralCertificate: new h(new S({\n          proof: new f(new g({\n            a: new Uint8Array(32),\n            b: new Uint8Array(64),\n            c: new Uint8Array(32)\n          }), 0),\n          expHorizonSecs: 0\n        }), 0),\n        expiryDateSecs: 0,\n        ephemeralPublicKey: new b(new C(new Uint8Array(32))),\n        ephemeralSignature: new y(new I(new Uint8Array(64)))\n      });\n    }\n    static isSignature(e) {\n      return e instanceof r;\n    }\n  },\n  h = class r extends d {\n    constructor(e, t) {\n      super(), this.signature = e, this.variant = t;\n    }\n    toUint8Array() {\n      return this.signature.toUint8Array();\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(this.variant), this.signature.serialize(e);\n    }\n    static deserialize(e) {\n      let t = e.deserializeUleb128AsU32();\n      switch (t) {\n        case 0:\n          return new r(S.deserialize(e), t);\n        default:\n          throw new Error(`Unknown variant index for EphemeralCertificate: ${t}`);\n      }\n    }\n  },\n  c = class r extends u {\n    constructor(e) {\n      if (super(), this.data = p.fromHexInput(e).toUint8Array(), this.data.length !== 32) throw new Error(\"Input needs to be 32 bytes\");\n    }\n    serialize(e) {\n      e.serializeFixedBytes(this.data);\n    }\n    static deserialize(e) {\n      let t = e.deserializeFixedBytes(32);\n      return new r(t);\n    }\n  },\n  l = class r extends u {\n    constructor(e) {\n      if (super(), this.data = p.fromHexInput(e).toUint8Array(), this.data.length !== 64) throw new Error(\"Input needs to be 64 bytes\");\n    }\n    serialize(e) {\n      e.serializeFixedBytes(this.data);\n    }\n    static deserialize(e) {\n      let t = e.deserializeFixedBytes(64);\n      return new r(t);\n    }\n  },\n  g = class r extends P {\n    constructor(e) {\n      super();\n      let {\n        a: t,\n        b: i,\n        c: a\n      } = e;\n      this.a = new c(t), this.b = new l(i), this.c = new c(a);\n    }\n    serialize(e) {\n      this.a.serialize(e), this.b.serialize(e), this.c.serialize(e);\n    }\n    static deserialize(e) {\n      let t = c.deserialize(e).bcsToBytes(),\n        i = l.deserialize(e).bcsToBytes(),\n        a = c.deserialize(e).bcsToBytes();\n      return new r({\n        a: t,\n        b: i,\n        c: a\n      });\n    }\n  },\n  f = class r extends u {\n    constructor(e, t) {\n      super(), this.proof = e, this.variant = t;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(this.variant), this.proof.serialize(e);\n    }\n    static deserialize(e) {\n      let t = e.deserializeUleb128AsU32();\n      switch (t) {\n        case 0:\n          return new r(g.deserialize(e), t);\n        default:\n          throw new Error(`Unknown variant index for ZkProof: ${t}`);\n      }\n    }\n  },\n  S = class r extends d {\n    constructor(e) {\n      super();\n      let {\n        proof: t,\n        expHorizonSecs: i,\n        trainingWheelsSignature: a,\n        extraField: n,\n        overrideAudVal: s\n      } = e;\n      this.proof = t, this.expHorizonSecs = i, this.trainingWheelsSignature = a, this.extraField = n, this.overrideAudVal = s;\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    static fromBytes(e) {\n      return r.deserialize(new G(e));\n    }\n    serialize(e) {\n      this.proof.serialize(e), e.serializeU64(this.expHorizonSecs), e.serializeOptionStr(this.extraField), e.serializeOptionStr(this.overrideAudVal), e.serializeOption(this.trainingWheelsSignature);\n    }\n    static deserialize(e) {\n      let t = f.deserialize(e),\n        i = Number(e.deserializeU64()),\n        a = e.deserializeOptionStr(),\n        n = e.deserializeOptionStr(),\n        s = e.deserializeOption(y);\n      return new r({\n        proof: t,\n        expHorizonSecs: i,\n        trainingWheelsSignature: s,\n        extraField: a,\n        overrideAudVal: n\n      });\n    }\n  },\n  z = class r {\n    constructor(e, t) {\n      this.verficationKey = e, this.maxExpHorizonSecs = t;\n    }\n    static create(e, t) {\n      return new r(new K({\n        alphaG1: e.alpha_g1,\n        betaG2: e.beta_g2,\n        deltaG2: e.delta_g2,\n        gammaAbcG1: e.gamma_abc_g1,\n        gammaG2: e.gamma_g2\n      }), t);\n    }\n  },\n  K = class r {\n    constructor(e) {\n      let {\n        alphaG1: t,\n        betaG2: i,\n        deltaG2: a,\n        gammaAbcG1: n,\n        gammaG2: s\n      } = e;\n      this.alphaG1 = new c(t), this.betaG2 = new l(i), this.deltaG2 = new l(a), this.gammaAbcG1 = [new c(n[0]), new c(n[1])], this.gammaG2 = new l(s);\n    }\n    static fromGroth16VerificationKeyResponse(e) {\n      return new r({\n        alphaG1: e.alpha_g1,\n        betaG2: e.beta_g2,\n        deltaG2: e.delta_g2,\n        gammaAbcG1: e.gamma_abc_g1,\n        gammaG2: e.gamma_g2\n      });\n    }\n  };\nasync function be(r) {\n  let {\n    aptosConfig: e\n  } = r;\n  return T(async () => {\n    let t = await O(r),\n      i = await j(r);\n    return z.create(i, Number(t.max_exp_horizon_secs));\n  }, `keyless-configuration-${e.network}`, 1e3 * 60 * 5)();\n}\nasync function O(r) {\n  let {\n      aptosConfig: e,\n      options: t\n    } = r,\n    i = \"0x1::keyless_account::Configuration\",\n    {\n      data: a\n    } = await w({\n      aptosConfig: e,\n      originMethod: \"getKeylessConfigurationResource\",\n      path: `accounts/${x.from(\"0x1\").toString()}/resource/${i}`,\n      params: {\n        ledger_version: t?.ledgerVersion\n      }\n    });\n  return a.data;\n}\nasync function j(r) {\n  let {\n      aptosConfig: e,\n      options: t\n    } = r,\n    i = \"0x1::keyless_account::Groth16VerificationKey\",\n    {\n      data: a\n    } = await w({\n      aptosConfig: e,\n      originMethod: \"getGroth16VerificationKeyResource\",\n      path: `accounts/${x.from(\"0x1\").toString()}/resource/${i}`,\n      params: {\n        ledger_version: t?.ledgerVersion\n      }\n    });\n  return a.data;\n}\nexport { he as a, F as b, L as c, N as d, ge as e, fe as f, Se as g, xe as h, A as i, D as j, h as k, g as l, f as m, S as n, z as o, be as p };", "map": {"version": 3, "names": ["jwtDecode", "k", "he", "F", "L", "N", "ge", "fe", "Se", "xe", "o", "U", "constructor", "e", "t", "i", "p", "fromHexInput", "toUint8Array", "length", "ID_COMMITMENT_LENGTH", "Error", "iss", "idCommitment", "auth<PERSON><PERSON>", "E", "serializeU32AsUleb128", "serializeFixedBytes", "bcsToBytes", "H", "fromSchemeAndBytes", "scheme", "input", "toString", "verifySignature", "serialize", "serializeStr", "serializeBytes", "deserialize", "deserializeStr", "deserializeBytes", "load", "isPublicKey", "create", "_", "fromJwtAndPepper", "jwt", "pepper", "uid<PERSON><PERSON>", "a", "n", "s", "aud", "M", "R", "uidVal", "A", "r", "V", "m", "v", "B", "D", "d", "jwtHeader", "ephemeralCertificate", "expiryDateSecs", "ephemeralPublicKey", "ephemeralSignature", "serializeU64", "h", "deserializeU64", "b", "y", "Number", "getSimulationSignature", "S", "proof", "f", "g", "Uint8Array", "c", "expHorizonSecs", "C", "I", "isSignature", "signature", "variant", "deserializeUleb128AsU32", "u", "data", "deserializeFixedBytes", "l", "P", "trainingWheelsSignature", "extraField", "overrideAudVal", "fromBytes", "G", "serializeOptionStr", "serializeOption", "deserializeOptionStr", "deserializeOption", "z", "verficationKey", "maxExpHorizonSecs", "K", "alphaG1", "alpha_g1", "betaG2", "beta_g2", "deltaG2", "delta_g2", "gammaAbcG1", "gamma_abc_g1", "gammaG2", "gamma_g2", "fromGroth16VerificationKeyResponse", "be", "aptosConfig", "T", "O", "j", "max_exp_horizon_secs", "network", "options", "w", "originMethod", "path", "x", "from", "params", "ledger_version", "ledgerVersion"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\keyless.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { JwtPayload, jwtDecode } from \"jwt-decode\";\nimport { AccountPublicKey, PublicKey } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { Deserializer, Serializable, Serializer } from \"../../bcs\";\nimport { Hex } from \"../hex\";\nimport {\n  HexInput,\n  EphemeralCertificateVariant,\n  AnyPublicKeyVariant,\n  SigningScheme,\n  ZkpVariant,\n  LedgerVersionArg,\n  MoveResource,\n} from \"../../types\";\nimport { EphemeralPublicKey, EphemeralSignature } from \"./ephemeral\";\nimport { bigIntToBytesLE, bytesToBigIntLE, hashStrToField, poseidonHash } from \"./poseidon\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Proof } from \"./proof\";\nimport { Ed25519Pub<PERSON><PERSON><PERSON>, Ed25519Signature } from \"./ed25519\";\nimport { Groth16VerificationKeyResponse, KeylessConfigurationResponse } from \"../../types/keyless\";\nimport { AptosConfig } from \"../../api/aptosConfig\";\nimport { getAptosFullNode } from \"../../client\";\nimport { memoizeAsync } from \"../../utils/memoize\";\nimport { AccountAddress } from \"../accountAddress\";\n\nexport const EPK_HORIZON_SECS = ********;\nexport const MAX_AUD_VAL_BYTES = 120;\nexport const MAX_UID_KEY_BYTES = 30;\nexport const MAX_UID_VAL_BYTES = 330;\nexport const MAX_ISS_VAL_BYTES = 120;\nexport const MAX_EXTRA_FIELD_BYTES = 350;\nexport const MAX_JWT_HEADER_B64_BYTES = 300;\nexport const MAX_COMMITED_EPK_BYTES = 93;\n\n/**\n * Represents the KeylessPublicKey public key\n *\n * KeylessPublicKey authentication key is represented in the SDK as `AnyPublicKey`.\n */\nexport class KeylessPublicKey extends AccountPublicKey {\n  /**\n   * The number of bytes that `idCommitment` should be\n   */\n  static readonly ID_COMMITMENT_LENGTH: number = 32;\n\n  /**\n   * The value of the 'iss' claim on the JWT which identifies the OIDC provider.\n   */\n  readonly iss: string;\n\n  /**\n   * A value representing a cryptographic commitment to a user identity.\n   *\n   * It is calculated from the aud, uidKey, uidVal, pepper.\n   */\n  readonly idCommitment: Uint8Array;\n\n  constructor(iss: string, idCommitment: HexInput) {\n    super();\n    const idcBytes = Hex.fromHexInput(idCommitment).toUint8Array();\n    if (idcBytes.length !== KeylessPublicKey.ID_COMMITMENT_LENGTH) {\n      throw new Error(`Id Commitment length in bytes should be ${KeylessPublicKey.ID_COMMITMENT_LENGTH}`);\n    }\n    this.iss = iss;\n    this.idCommitment = idcBytes;\n  }\n\n  /**\n   * Get the authentication key for the keyless public key\n   *\n   * @returns AuthenticationKey\n   */\n  authKey(): AuthenticationKey {\n    const serializer = new Serializer();\n    serializer.serializeU32AsUleb128(AnyPublicKeyVariant.Keyless);\n    serializer.serializeFixedBytes(this.bcsToBytes());\n    return AuthenticationKey.fromSchemeAndBytes({\n      scheme: SigningScheme.SingleKey,\n      input: serializer.toUint8Array(),\n    });\n  }\n\n  /**\n   * Get the public key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the public key\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  /**\n   * Get the public key as a hex string with the 0x prefix.\n   *\n   * @returns string representation of the public key\n   */\n  toString(): string {\n    return Hex.fromHexInput(this.toUint8Array()).toString();\n  }\n\n  /**\n   * Verifies a signed data with a public key\n   *\n   * @param args.message message\n   * @param args.signature The signature\n   * @returns true if the signature is valid\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this\n  verifySignature(args: { message: HexInput; signature: KeylessSignature }): boolean {\n    throw new Error(\"Not yet implemented\");\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeStr(this.iss);\n    serializer.serializeBytes(this.idCommitment);\n  }\n\n  static deserialize(deserializer: Deserializer): KeylessPublicKey {\n    const iss = deserializer.deserializeStr();\n    const addressSeed = deserializer.deserializeBytes();\n    return new KeylessPublicKey(iss, addressSeed);\n  }\n\n  static load(deserializer: Deserializer): KeylessPublicKey {\n    const iss = deserializer.deserializeStr();\n    const addressSeed = deserializer.deserializeBytes();\n    return new KeylessPublicKey(iss, addressSeed);\n  }\n\n  static isPublicKey(publicKey: PublicKey): publicKey is KeylessPublicKey {\n    return publicKey instanceof KeylessPublicKey;\n  }\n\n  /**\n   * Creates a KeylessPublicKey from the JWT components plus pepper\n   *\n   * @param args.iss the iss of the identity\n   * @param args.uidKey the key to use to get the uidVal in the JWT token\n   * @param args.uidVal the value of the uidKey in the JWT token\n   * @param args.aud the client ID of the application\n   * @param args.pepper The pepper used to maintain privacy of the account\n   * @returns KeylessPublicKey\n   */\n  static create(args: {\n    iss: string;\n    uidKey: string;\n    uidVal: string;\n    aud: string;\n    pepper: HexInput;\n  }): KeylessPublicKey {\n    computeIdCommitment(args);\n    return new KeylessPublicKey(args.iss, computeIdCommitment(args));\n  }\n\n  static fromJwtAndPepper(args: { jwt: string; pepper: HexInput; uidKey?: string }): KeylessPublicKey {\n    const { jwt, pepper, uidKey = \"sub\" } = args;\n    const jwtPayload = jwtDecode<JwtPayload & { [key: string]: string }>(jwt);\n    const iss = jwtPayload.iss!;\n    if (typeof jwtPayload.aud !== \"string\") {\n      throw new Error(\"aud was not found or an array of values\");\n    }\n    const aud = jwtPayload.aud!;\n    const uidVal = jwtPayload[uidKey];\n    return KeylessPublicKey.create({ iss, uidKey, uidVal, aud, pepper });\n  }\n}\n\nfunction computeIdCommitment(args: { uidKey: string; uidVal: string; aud: string; pepper: HexInput }): Uint8Array {\n  const { uidKey, uidVal, aud, pepper } = args;\n\n  const fields = [\n    bytesToBigIntLE(Hex.fromHexInput(pepper).toUint8Array()),\n    hashStrToField(aud, MAX_AUD_VAL_BYTES),\n    hashStrToField(uidVal, MAX_UID_VAL_BYTES),\n    hashStrToField(uidKey, MAX_UID_KEY_BYTES),\n  ];\n\n  return bigIntToBytesLE(poseidonHash(fields), KeylessPublicKey.ID_COMMITMENT_LENGTH);\n}\n\n/**\n * A signature of a message signed via Keyless Accounnt that uses proofs or the jwt token to authenticate.\n */\nexport class KeylessSignature extends Signature {\n  /**\n   * The inner signature ZeroKnowledgeSigniature or OpenIdSignature\n   */\n  readonly ephemeralCertificate: EphemeralCertificate;\n\n  /**\n   * The jwt header in the token used to create the proof/signature.  In json string representation.\n   */\n  readonly jwtHeader: string;\n\n  /**\n   * The expiry timestamp in seconds of the EphemeralKeyPair used to sign\n   */\n  readonly expiryDateSecs: number;\n\n  /**\n   * The ephemeral public key used to verify the signature\n   */\n  readonly ephemeralPublicKey: EphemeralPublicKey;\n\n  /**\n   * The signature resulting from signing with the private key of the EphemeralKeyPair\n   */\n  readonly ephemeralSignature: EphemeralSignature;\n\n  constructor(args: {\n    jwtHeader: string;\n    ephemeralCertificate: EphemeralCertificate;\n    expiryDateSecs: number;\n    ephemeralPublicKey: EphemeralPublicKey;\n    ephemeralSignature: EphemeralSignature;\n  }) {\n    super();\n    const { jwtHeader, ephemeralCertificate, expiryDateSecs, ephemeralPublicKey, ephemeralSignature } = args;\n    this.jwtHeader = jwtHeader;\n    this.ephemeralCertificate = ephemeralCertificate;\n    this.expiryDateSecs = expiryDateSecs;\n    this.ephemeralPublicKey = ephemeralPublicKey;\n    this.ephemeralSignature = ephemeralSignature;\n  }\n\n  /**\n   * Get the signature in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the signature\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  serialize(serializer: Serializer): void {\n    this.ephemeralCertificate.serialize(serializer);\n    serializer.serializeStr(this.jwtHeader);\n    serializer.serializeU64(this.expiryDateSecs);\n    this.ephemeralPublicKey.serialize(serializer);\n    this.ephemeralSignature.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): KeylessSignature {\n    const ephemeralCertificate = EphemeralCertificate.deserialize(deserializer);\n    const jwtHeader = deserializer.deserializeStr();\n    const expiryDateSecs = deserializer.deserializeU64();\n    const ephemeralPublicKey = EphemeralPublicKey.deserialize(deserializer);\n    const ephemeralSignature = EphemeralSignature.deserialize(deserializer);\n    return new KeylessSignature({\n      jwtHeader,\n      expiryDateSecs: Number(expiryDateSecs),\n      ephemeralCertificate,\n      ephemeralPublicKey,\n      ephemeralSignature,\n    });\n  }\n\n  static getSimulationSignature(): KeylessSignature {\n    return new KeylessSignature({\n      jwtHeader: \"{}\",\n      ephemeralCertificate: new EphemeralCertificate(\n        new ZeroKnowledgeSig({\n          proof: new ZkProof(\n            new Groth16Zkp({ a: new Uint8Array(32), b: new Uint8Array(64), c: new Uint8Array(32) }),\n            ZkpVariant.Groth16,\n          ),\n          expHorizonSecs: 0,\n        }),\n        EphemeralCertificateVariant.ZkProof,\n      ),\n      expiryDateSecs: 0,\n      ephemeralPublicKey: new EphemeralPublicKey(new Ed25519PublicKey(new Uint8Array(32))),\n      ephemeralSignature: new EphemeralSignature(new Ed25519Signature(new Uint8Array(64))),\n    });\n  }\n\n  static isSignature(signature: Signature): signature is KeylessSignature {\n    return signature instanceof KeylessSignature;\n  }\n}\n\n/**\n * A container for a signature that is a ZeroKnowledgeSig.  Can be expanded to support OpenIdSignature.\n */\nexport class EphemeralCertificate extends Signature {\n  public readonly signature: Signature;\n\n  /**\n   * Index of the underlying enum variant\n   */\n  private readonly variant: EphemeralCertificateVariant;\n\n  constructor(signature: Signature, variant: EphemeralCertificateVariant) {\n    super();\n    this.signature = signature;\n    this.variant = variant;\n  }\n\n  /**\n   * Get the public key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the public key\n   */\n  toUint8Array(): Uint8Array {\n    return this.signature.toUint8Array();\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.variant);\n    this.signature.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): EphemeralCertificate {\n    const variant = deserializer.deserializeUleb128AsU32();\n    switch (variant) {\n      case EphemeralCertificateVariant.ZkProof:\n        return new EphemeralCertificate(ZeroKnowledgeSig.deserialize(deserializer), variant);\n      default:\n        throw new Error(`Unknown variant index for EphemeralCertificate: ${variant}`);\n    }\n  }\n}\n\nclass G1Bytes extends Serializable {\n  data: Uint8Array;\n\n  constructor(data: HexInput) {\n    super();\n    this.data = Hex.fromHexInput(data).toUint8Array();\n    if (this.data.length !== 32) {\n      throw new Error(\"Input needs to be 32 bytes\");\n    }\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.data);\n  }\n\n  static deserialize(deserializer: Deserializer): G1Bytes {\n    const bytes = deserializer.deserializeFixedBytes(32);\n    return new G1Bytes(bytes);\n  }\n}\n\nclass G2Bytes extends Serializable {\n  data: Uint8Array;\n\n  constructor(data: HexInput) {\n    super();\n    this.data = Hex.fromHexInput(data).toUint8Array();\n    if (this.data.length !== 64) {\n      throw new Error(\"Input needs to be 64 bytes\");\n    }\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.data);\n  }\n\n  static deserialize(deserializer: Deserializer): G2Bytes {\n    const bytes = deserializer.deserializeFixedBytes(64);\n    return new G2Bytes(bytes);\n  }\n}\n\n/**\n * A representation of a Groth16 proof.  The points are the compressed serialization of affine reprentation of the proof.\n */\nexport class Groth16Zkp extends Proof {\n  /**\n   * The bytes of G1 proof point a\n   */\n  a: G1Bytes;\n\n  /**\n   * The bytes of G2 proof point b\n   */\n  b: G2Bytes;\n\n  /**\n   * The bytes of G1 proof point c\n   */\n  c: G1Bytes;\n\n  constructor(args: { a: HexInput; b: HexInput; c: HexInput }) {\n    super();\n    const { a, b, c } = args;\n    this.a = new G1Bytes(a);\n    this.b = new G2Bytes(b);\n    this.c = new G1Bytes(c);\n  }\n\n  serialize(serializer: Serializer): void {\n    this.a.serialize(serializer);\n    this.b.serialize(serializer);\n    this.c.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): Groth16Zkp {\n    const a = G1Bytes.deserialize(deserializer).bcsToBytes();\n    const b = G2Bytes.deserialize(deserializer).bcsToBytes();\n    const c = G1Bytes.deserialize(deserializer).bcsToBytes();\n    return new Groth16Zkp({ a, b, c });\n  }\n}\n\n/**\n * A container for a different zero knowledge proof types\n */\nexport class ZkProof extends Serializable {\n  public readonly proof: Proof;\n\n  /**\n   * Index of the underlying enum variant\n   */\n  private readonly variant: ZkpVariant;\n\n  constructor(proof: Proof, variant: ZkpVariant) {\n    super();\n    this.proof = proof;\n    this.variant = variant;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.variant);\n    this.proof.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): ZkProof {\n    const variant = deserializer.deserializeUleb128AsU32();\n    switch (variant) {\n      case ZkpVariant.Groth16:\n        return new ZkProof(Groth16Zkp.deserialize(deserializer), variant);\n      default:\n        throw new Error(`Unknown variant index for ZkProof: ${variant}`);\n    }\n  }\n}\n\n/**\n * The signature representation of a proof\n */\nexport class ZeroKnowledgeSig extends Signature {\n  /**\n   * The proof\n   */\n  readonly proof: ZkProof;\n\n  /**\n   * The max lifespan of the proof\n   */\n  readonly expHorizonSecs: number;\n\n  /**\n   * A key value pair on the JWT token that can be specified on the signature which would reveal the value on chain.\n   * Can be used to assert identity or other attributes.\n   */\n  readonly extraField?: string;\n\n  /**\n   * The 'aud' value of the recovery service which is set when recovering an account.\n   */\n  readonly overrideAudVal?: string;\n\n  /**\n   * The training wheels signature\n   */\n  readonly trainingWheelsSignature?: EphemeralSignature;\n\n  constructor(args: {\n    proof: ZkProof;\n    expHorizonSecs: number;\n    extraField?: string;\n    overrideAudVal?: string;\n    trainingWheelsSignature?: EphemeralSignature;\n  }) {\n    super();\n    const { proof, expHorizonSecs, trainingWheelsSignature, extraField, overrideAudVal } = args;\n    this.proof = proof;\n    this.expHorizonSecs = expHorizonSecs;\n    this.trainingWheelsSignature = trainingWheelsSignature;\n    this.extraField = extraField;\n    this.overrideAudVal = overrideAudVal;\n  }\n\n  /**\n   * Get the signature in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the signature\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  /**\n   * Return a ZeroKnowledgeSig object from its bcs serialization in bytes.\n   *\n   * @returns ZeroKnowledgeSig\n   */\n  static fromBytes(bytes: Uint8Array): ZeroKnowledgeSig {\n    return ZeroKnowledgeSig.deserialize(new Deserializer(bytes));\n  }\n\n  serialize(serializer: Serializer): void {\n    this.proof.serialize(serializer);\n    serializer.serializeU64(this.expHorizonSecs);\n    serializer.serializeOptionStr(this.extraField);\n    serializer.serializeOptionStr(this.overrideAudVal);\n    serializer.serializeOption(this.trainingWheelsSignature);\n  }\n\n  static deserialize(deserializer: Deserializer): ZeroKnowledgeSig {\n    const proof = ZkProof.deserialize(deserializer);\n    const expHorizonSecs = Number(deserializer.deserializeU64());\n    const extraField = deserializer.deserializeOptionStr();\n    const overrideAudVal = deserializer.deserializeOptionStr();\n    const trainingWheelsSignature = deserializer.deserializeOption(EphemeralSignature);\n    return new ZeroKnowledgeSig({ proof, expHorizonSecs, trainingWheelsSignature, extraField, overrideAudVal });\n  }\n}\n\n/**\n * A class which represents the on-chain configuration for how Keyless accounts work\n */\nexport class KeylessConfiguration {\n  /**\n   * The verification key used to verify Groth16 proofs on chain\n   */\n  readonly verficationKey: Groth16VerificationKey;\n\n  /**\n   * The maximum lifespan of an ephemeral key pair.  This is configured on chain.\n   */\n  readonly maxExpHorizonSecs: number;\n\n  constructor(verficationKey: Groth16VerificationKey, maxExpHorizonSecs: number) {\n    this.verficationKey = verficationKey;\n    this.maxExpHorizonSecs = maxExpHorizonSecs;\n  }\n\n  static create(res: Groth16VerificationKeyResponse, maxExpHorizonSecs: number): KeylessConfiguration {\n    return new KeylessConfiguration(\n      new Groth16VerificationKey({\n        alphaG1: res.alpha_g1,\n        betaG2: res.beta_g2,\n        deltaG2: res.delta_g2,\n        gammaAbcG1: res.gamma_abc_g1,\n        gammaG2: res.gamma_g2,\n      }),\n      maxExpHorizonSecs,\n    );\n  }\n}\n\n/**\n * A representation of the verification key stored on chain used to verify Groth16 proofs\n */\nclass Groth16VerificationKey {\n  // The docstrings below are borrowed from ark-groth16\n\n  /**\n   * The `alpha * G`, where `G` is the generator of G1\n   */\n  readonly alphaG1: G1Bytes;\n\n  /**\n   * The `alpha * H`, where `H` is the generator of G2\n   */\n  readonly betaG2: G2Bytes;\n\n  /**\n   * The `delta * H`, where `H` is the generator of G2\n   */\n  readonly deltaG2: G2Bytes;\n\n  /**\n   * The `gamma^{-1} * (beta * a_i + alpha * b_i + c_i) * H`, where H is the generator of G1\n   */\n  readonly gammaAbcG1: G1Bytes[];\n\n  /**\n   * The `gamma * H`, where `H` is the generator of G2\n   */\n  readonly gammaG2: G2Bytes;\n\n  constructor(args: {\n    alphaG1: HexInput;\n    betaG2: HexInput;\n    deltaG2: HexInput;\n    gammaAbcG1: [HexInput, HexInput];\n    gammaG2: HexInput;\n  }) {\n    const { alphaG1, betaG2, deltaG2, gammaAbcG1, gammaG2 } = args;\n    this.alphaG1 = new G1Bytes(alphaG1);\n    this.betaG2 = new G2Bytes(betaG2);\n    this.deltaG2 = new G2Bytes(deltaG2);\n    this.gammaAbcG1 = [new G1Bytes(gammaAbcG1[0]), new G1Bytes(gammaAbcG1[1])];\n    this.gammaG2 = new G2Bytes(gammaG2);\n  }\n\n  static fromGroth16VerificationKeyResponse(res: Groth16VerificationKeyResponse): Groth16VerificationKey {\n    return new Groth16VerificationKey({\n      alphaG1: res.alpha_g1,\n      betaG2: res.beta_g2,\n      deltaG2: res.delta_g2,\n      gammaAbcG1: res.gamma_abc_g1,\n      gammaG2: res.gamma_g2,\n    });\n  }\n}\n\n/**\n * Gets the parameters of how Keyless Accounts are configured on chain including the verifying key and the max expiry horizon\n *\n * @param args.options.ledgerVersion The ledger version to query, if not provided it will get the latest version\n * @returns KeylessConfiguration\n */\nexport async function getKeylessConfig(args: {\n  aptosConfig: AptosConfig;\n  options?: LedgerVersionArg;\n}): Promise<KeylessConfiguration> {\n  const { aptosConfig } = args;\n  return memoizeAsync(\n    async () => {\n      const config = await getKeylessConfigurationResource(args);\n      const vk = await getGroth16VerificationKeyResource(args);\n      return KeylessConfiguration.create(vk, Number(config.max_exp_horizon_secs));\n    },\n    `keyless-configuration-${aptosConfig.network}`,\n    1000 * 60 * 5, // 5 minutes\n  )();\n}\n\n/**\n * Gets the KeylessConfiguration set on chain\n *\n * @param args.options.ledgerVersion The ledger version to query, if not provided it will get the latest version\n * @returns KeylessConfigurationResponse\n */\nasync function getKeylessConfigurationResource(args: {\n  aptosConfig: AptosConfig;\n  options?: LedgerVersionArg;\n}): Promise<KeylessConfigurationResponse> {\n  const { aptosConfig, options } = args;\n  const resourceType = \"0x1::keyless_account::Configuration\";\n  const { data } = await getAptosFullNode<{}, MoveResource<KeylessConfigurationResponse>>({\n    aptosConfig,\n    originMethod: \"getKeylessConfigurationResource\",\n    path: `accounts/${AccountAddress.from(\"0x1\").toString()}/resource/${resourceType}`,\n    params: { ledger_version: options?.ledgerVersion },\n  });\n\n  return data.data;\n}\n\n/**\n * Gets the Groth16VerificationKey set on chain\n *\n * @param args.options.ledgerVersion The ledger version to query, if not provided it will get the latest version\n * @returns Groth16VerificationKeyResponse\n */\nasync function getGroth16VerificationKeyResource(args: {\n  aptosConfig: AptosConfig;\n  options?: LedgerVersionArg;\n}): Promise<Groth16VerificationKeyResponse> {\n  const { aptosConfig, options } = args;\n  const resourceType = \"0x1::keyless_account::Groth16VerificationKey\";\n  const { data } = await getAptosFullNode<{}, MoveResource<Groth16VerificationKeyResponse>>({\n    aptosConfig,\n    originMethod: \"getGroth16VerificationKeyResource\",\n    path: `accounts/${AccountAddress.from(\"0x1\").toString()}/resource/${resourceType}`,\n    params: { ledger_version: options?.ledgerVersion },\n  });\n\n  return data.data;\n}\n"], "mappings": ";;;;;;;;;;;;;AAGA,SAAqBA,SAAA,IAAAC,CAAA,QAAiB;AAyB/B,IAAMC,EAAA,GAAmB;EACnBC,CAAA,GAAoB;EACpBC,CAAA,GAAoB;EACpBC,CAAA,GAAoB;EACpBC,EAAA,GAAoB;EACpBC,EAAA,GAAwB;EACxBC,EAAA,GAA2B;EAC3BC,EAAA,GAAyB;EAOzBC,CAAA,GAAN,MAAMA,CAAA,SAAyBC,CAAiB;IAkBrDC,YAAYC,CAAA,EAAaC,CAAA,EAAwB;MAC/C,MAAM;MACN,IAAMC,CAAA,GAAWC,CAAA,CAAIC,YAAA,CAAaH,CAAY,EAAEI,YAAA,CAAa;MAC7D,IAAIH,CAAA,CAASI,MAAA,KAAWT,CAAA,CAAiBU,oBAAA,EACvC,MAAM,IAAIC,KAAA,CAAM,2CAA2CX,CAAA,CAAiBU,oBAAoB,EAAE;MAEpG,KAAKE,GAAA,GAAMT,CAAA,EACX,KAAKU,YAAA,GAAeR,CACtB;IAAA;IAOAS,QAAA,EAA6B;MAC3B,IAAMX,CAAA,GAAa,IAAIY,CAAA;MACvB,OAAAZ,CAAA,CAAWa,qBAAA,EAAiD,GAC5Db,CAAA,CAAWc,mBAAA,CAAoB,KAAKC,UAAA,CAAW,CAAC,GACzCC,CAAA,CAAkBC,kBAAA,CAAmB;QAC1CC,MAAA;QACAC,KAAA,EAAOnB,CAAA,CAAWK,YAAA,CAAa;MACjC,CAAC,CACH;IAAA;IAOAA,aAAA,EAA2B;MACzB,OAAO,KAAKU,UAAA,CAAW,CACzB;IAAA;IAOAK,SAAA,EAAmB;MACjB,OAAOjB,CAAA,CAAIC,YAAA,CAAa,KAAKC,YAAA,CAAa,CAAC,EAAEe,QAAA,CAAS,CACxD;IAAA;IAUAC,gBAAgBrB,CAAA,EAAmE;MACjF,MAAM,IAAIQ,KAAA,CAAM,qBAAqB,CACvC;IAAA;IAEAc,UAAUtB,CAAA,EAA8B;MACtCA,CAAA,CAAWuB,YAAA,CAAa,KAAKd,GAAG,GAChCT,CAAA,CAAWwB,cAAA,CAAe,KAAKd,YAAY,CAC7C;IAAA;IAEA,OAAOe,YAAYzB,CAAA,EAA8C;MAC/D,IAAMC,CAAA,GAAMD,CAAA,CAAa0B,cAAA,CAAe;QAClCxB,CAAA,GAAcF,CAAA,CAAa2B,gBAAA,CAAiB;MAClD,OAAO,IAAI9B,CAAA,CAAiBI,CAAA,EAAKC,CAAW,CAC9C;IAAA;IAEA,OAAO0B,KAAK5B,CAAA,EAA8C;MACxD,IAAMC,CAAA,GAAMD,CAAA,CAAa0B,cAAA,CAAe;QAClCxB,CAAA,GAAcF,CAAA,CAAa2B,gBAAA,CAAiB;MAClD,OAAO,IAAI9B,CAAA,CAAiBI,CAAA,EAAKC,CAAW,CAC9C;IAAA;IAEA,OAAO2B,YAAY7B,CAAA,EAAqD;MACtE,OAAOA,CAAA,YAAqBH,CAC9B;IAAA;IAYA,OAAOiC,OAAO9B,CAAA,EAMO;MACnB,OAAA+B,CAAA,CAAoB/B,CAAI,GACjB,IAAIH,CAAA,CAAiBG,CAAA,CAAKS,GAAA,EAAKsB,CAAA,CAAoB/B,CAAI,CAAC,CACjE;IAAA;IAEA,OAAOgC,iBAAiBhC,CAAA,EAA4E;MAClG,IAAM;UAAEiC,GAAA,EAAAhC,CAAA;UAAKiC,MAAA,EAAAhC,CAAA;UAAQiC,MAAA,EAAAC,CAAA,GAAS;QAAM,IAAIpC,CAAA;QAClCqC,CAAA,GAAajD,CAAA,CAAkDa,CAAG;QAClEqC,CAAA,GAAMD,CAAA,CAAW5B,GAAA;MACvB,IAAI,OAAO4B,CAAA,CAAWE,GAAA,IAAQ,UAC5B,MAAM,IAAI/B,KAAA,CAAM,yCAAyC;MAE3D,IAAMgC,CAAA,GAAMH,CAAA,CAAWE,GAAA;QACjBE,CAAA,GAASJ,CAAA,CAAWD,CAAM;MAChC,OAAOvC,CAAA,CAAiBiC,MAAA,CAAO;QAAErB,GAAA,EAAA6B,CAAA;QAAKH,MAAA,EAAAC,CAAA;QAAQM,MAAA,EAAAD,CAAA;QAAQF,GAAA,EAAAC,CAAA;QAAKN,MAAA,EAAAhC;MAAO,CAAC,CACrE;IAAA;EACF;AA9HaL,CAAA,CAIKU,oBAAA,GAA+B;AAJ1C,IAAMoC,CAAA,GAAN9C,CAAA;AAgIP,SAASkC,EAAoBa,CAAA,EAAqF;EAChH,IAAM;MAAET,MAAA,EAAAnC,CAAA;MAAQ0C,MAAA,EAAAzC,CAAA;MAAQsC,GAAA,EAAArC,CAAA;MAAKgC,MAAA,EAAAE;IAAO,IAAIQ,CAAA;IAElCP,CAAA,GAAS,CACbQ,CAAA,CAAgB1C,CAAA,CAAIC,YAAA,CAAagC,CAAM,EAAE/B,YAAA,CAAa,CAAC,GACvDyC,CAAA,CAAe5C,CAAA,EAAKZ,CAAiB,GACrCwD,CAAA,CAAe7C,CAAA,EAAQT,CAAiB,GACxCsD,CAAA,CAAe9C,CAAA,EAAQT,CAAiB,CAC1C;EAEA,OAAOwD,CAAA,CAAgBC,CAAA,CAAaX,CAAM,GAAGM,CAAA,CAAiBpC,oBAAoB,CACpF;AAAA;AAKO,IAAM0C,CAAA,GAAN,MAAML,CAAA,SAAyBM,CAAU;IA0B9CnD,YAAYC,CAAA,EAMT;MACD,MAAM;MACN,IAAM;QAAEmD,SAAA,EAAAlD,CAAA;QAAWmD,oBAAA,EAAAlD,CAAA;QAAsBmD,cAAA,EAAAjB,CAAA;QAAgBkB,kBAAA,EAAAjB,CAAA;QAAoBkB,kBAAA,EAAAjB;MAAmB,IAAItC,CAAA;MACpG,KAAKmD,SAAA,GAAYlD,CAAA,EACjB,KAAKmD,oBAAA,GAAuBlD,CAAA,EAC5B,KAAKmD,cAAA,GAAiBjB,CAAA,EACtB,KAAKkB,kBAAA,GAAqBjB,CAAA,EAC1B,KAAKkB,kBAAA,GAAqBjB,CAC5B;IAAA;IAOAjC,aAAA,EAA2B;MACzB,OAAO,KAAKU,UAAA,CAAW,CACzB;IAAA;IAEAO,UAAUtB,CAAA,EAA8B;MACtC,KAAKoD,oBAAA,CAAqB9B,SAAA,CAAUtB,CAAU,GAC9CA,CAAA,CAAWuB,YAAA,CAAa,KAAK4B,SAAS,GACtCnD,CAAA,CAAWwD,YAAA,CAAa,KAAKH,cAAc,GAC3C,KAAKC,kBAAA,CAAmBhC,SAAA,CAAUtB,CAAU,GAC5C,KAAKuD,kBAAA,CAAmBjC,SAAA,CAAUtB,CAAU,CAC9C;IAAA;IAEA,OAAOyB,YAAYzB,CAAA,EAA8C;MAC/D,IAAMC,CAAA,GAAuBwD,CAAA,CAAqBhC,WAAA,CAAYzB,CAAY;QACpEE,CAAA,GAAYF,CAAA,CAAa0B,cAAA,CAAe;QACxCU,CAAA,GAAiBpC,CAAA,CAAa0D,cAAA,CAAe;QAC7CrB,CAAA,GAAqBsB,CAAA,CAAmBlC,WAAA,CAAYzB,CAAY;QAChEsC,CAAA,GAAqBsB,CAAA,CAAmBnC,WAAA,CAAYzB,CAAY;MACtE,OAAO,IAAI4C,CAAA,CAAiB;QAC1BO,SAAA,EAAAjD,CAAA;QACAmD,cAAA,EAAgBQ,MAAA,CAAOzB,CAAc;QACrCgB,oBAAA,EAAAnD,CAAA;QACAqD,kBAAA,EAAAjB,CAAA;QACAkB,kBAAA,EAAAjB;MACF,CAAC,CACH;IAAA;IAEA,OAAOwB,uBAAA,EAA2C;MAChD,OAAO,IAAIlB,CAAA,CAAiB;QAC1BO,SAAA,EAAW;QACXC,oBAAA,EAAsB,IAAIK,CAAA,CACxB,IAAIM,CAAA,CAAiB;UACnBC,KAAA,EAAO,IAAIC,CAAA,CACT,IAAIC,CAAA,CAAW;YAAE9B,CAAA,EAAG,IAAI+B,UAAA,CAAW,EAAE;YAAGR,CAAA,EAAG,IAAIQ,UAAA,CAAW,EAAE;YAAGC,CAAA,EAAG,IAAID,UAAA,CAAW,EAAE;UAAE,CAAC,IAExF;UACAE,cAAA,EAAgB;QAClB,CAAC,IAEH;QACAhB,cAAA,EAAgB;QAChBC,kBAAA,EAAoB,IAAIK,CAAA,CAAmB,IAAIW,CAAA,CAAiB,IAAIH,UAAA,CAAW,EAAE,CAAC,CAAC;QACnFZ,kBAAA,EAAoB,IAAIK,CAAA,CAAmB,IAAIW,CAAA,CAAiB,IAAIJ,UAAA,CAAW,EAAE,CAAC,CAAC;MACrF,CAAC,CACH;IAAA;IAEA,OAAOK,YAAYxE,CAAA,EAAqD;MACtE,OAAOA,CAAA,YAAqB4C,CAC9B;IAAA;EACF;EAKaa,CAAA,GAAN,MAAMb,CAAA,SAA6BM,CAAU;IAQlDnD,YAAYC,CAAA,EAAsBC,CAAA,EAAsC;MACtE,MAAM,GACN,KAAKwE,SAAA,GAAYzE,CAAA,EACjB,KAAK0E,OAAA,GAAUzE,CACjB;IAAA;IAOAI,aAAA,EAA2B;MACzB,OAAO,KAAKoE,SAAA,CAAUpE,YAAA,CAAa,CACrC;IAAA;IAEAiB,UAAUtB,CAAA,EAA8B;MACtCA,CAAA,CAAWa,qBAAA,CAAsB,KAAK6D,OAAO,GAC7C,KAAKD,SAAA,CAAUnD,SAAA,CAAUtB,CAAU,CACrC;IAAA;IAEA,OAAOyB,YAAYzB,CAAA,EAAkD;MACnE,IAAMC,CAAA,GAAUD,CAAA,CAAa2E,uBAAA,CAAwB;MACrD,QAAQ1E,CAAA;QACN;UACE,OAAO,IAAI2C,CAAA,CAAqBmB,CAAA,CAAiBtC,WAAA,CAAYzB,CAAY,GAAGC,CAAO;QACrF;UACE,MAAM,IAAIO,KAAA,CAAM,mDAAmDP,CAAO,EAAE,CAChF;MAAA;IACF;EACF;EAEMmE,CAAA,GAAN,MAAMxB,CAAA,SAAgBgC,CAAa;IAGjC7E,YAAYC,CAAA,EAAgB;MAG1B,IAFA,MAAM,GACN,KAAK6E,IAAA,GAAO1E,CAAA,CAAIC,YAAA,CAAaJ,CAAI,EAAEK,YAAA,CAAa,GAC5C,KAAKwE,IAAA,CAAKvE,MAAA,KAAW,IACvB,MAAM,IAAIE,KAAA,CAAM,4BAA4B,CAEhD;IAAA;IAEAc,UAAUtB,CAAA,EAA8B;MACtCA,CAAA,CAAWc,mBAAA,CAAoB,KAAK+D,IAAI,CAC1C;IAAA;IAEA,OAAOpD,YAAYzB,CAAA,EAAqC;MACtD,IAAMC,CAAA,GAAQD,CAAA,CAAa8E,qBAAA,CAAsB,EAAE;MACnD,OAAO,IAAIlC,CAAA,CAAQ3C,CAAK,CAC1B;IAAA;EACF;EAEM8E,CAAA,GAAN,MAAMnC,CAAA,SAAgBgC,CAAa;IAGjC7E,YAAYC,CAAA,EAAgB;MAG1B,IAFA,MAAM,GACN,KAAK6E,IAAA,GAAO1E,CAAA,CAAIC,YAAA,CAAaJ,CAAI,EAAEK,YAAA,CAAa,GAC5C,KAAKwE,IAAA,CAAKvE,MAAA,KAAW,IACvB,MAAM,IAAIE,KAAA,CAAM,4BAA4B,CAEhD;IAAA;IAEAc,UAAUtB,CAAA,EAA8B;MACtCA,CAAA,CAAWc,mBAAA,CAAoB,KAAK+D,IAAI,CAC1C;IAAA;IAEA,OAAOpD,YAAYzB,CAAA,EAAqC;MACtD,IAAMC,CAAA,GAAQD,CAAA,CAAa8E,qBAAA,CAAsB,EAAE;MACnD,OAAO,IAAIlC,CAAA,CAAQ3C,CAAK,CAC1B;IAAA;EACF;EAKaiE,CAAA,GAAN,MAAMtB,CAAA,SAAmBoC,CAAM;IAgBpCjF,YAAYC,CAAA,EAAiD;MAC3D,MAAM;MACN,IAAM;QAAEoC,CAAA,EAAAnC,CAAA;QAAG0D,CAAA,EAAAzD,CAAA;QAAGkE,CAAA,EAAAhC;MAAE,IAAIpC,CAAA;MACpB,KAAKoC,CAAA,GAAI,IAAIgC,CAAA,CAAQnE,CAAC,GACtB,KAAK0D,CAAA,GAAI,IAAIoB,CAAA,CAAQ7E,CAAC,GACtB,KAAKkE,CAAA,GAAI,IAAIA,CAAA,CAAQhC,CAAC,CACxB;IAAA;IAEAd,UAAUtB,CAAA,EAA8B;MACtC,KAAKoC,CAAA,CAAEd,SAAA,CAAUtB,CAAU,GAC3B,KAAK2D,CAAA,CAAErC,SAAA,CAAUtB,CAAU,GAC3B,KAAKoE,CAAA,CAAE9C,SAAA,CAAUtB,CAAU,CAC7B;IAAA;IAEA,OAAOyB,YAAYzB,CAAA,EAAwC;MACzD,IAAMC,CAAA,GAAImE,CAAA,CAAQ3C,WAAA,CAAYzB,CAAY,EAAEe,UAAA,CAAW;QACjDb,CAAA,GAAI6E,CAAA,CAAQtD,WAAA,CAAYzB,CAAY,EAAEe,UAAA,CAAW;QACjDqB,CAAA,GAAIgC,CAAA,CAAQ3C,WAAA,CAAYzB,CAAY,EAAEe,UAAA,CAAW;MACvD,OAAO,IAAI6B,CAAA,CAAW;QAAER,CAAA,EAAAnC,CAAA;QAAG0D,CAAA,EAAAzD,CAAA;QAAGkE,CAAA,EAAAhC;MAAE,CAAC,CACnC;IAAA;EACF;EAKa6B,CAAA,GAAN,MAAMrB,CAAA,SAAgBgC,CAAa;IAQxC7E,YAAYC,CAAA,EAAcC,CAAA,EAAqB;MAC7C,MAAM,GACN,KAAK+D,KAAA,GAAQhE,CAAA,EACb,KAAK0E,OAAA,GAAUzE,CACjB;IAAA;IAEAqB,UAAUtB,CAAA,EAA8B;MACtCA,CAAA,CAAWa,qBAAA,CAAsB,KAAK6D,OAAO,GAC7C,KAAKV,KAAA,CAAM1C,SAAA,CAAUtB,CAAU,CACjC;IAAA;IAEA,OAAOyB,YAAYzB,CAAA,EAAqC;MACtD,IAAMC,CAAA,GAAUD,CAAA,CAAa2E,uBAAA,CAAwB;MACrD,QAAQ1E,CAAA;QACN;UACE,OAAO,IAAI2C,CAAA,CAAQsB,CAAA,CAAWzC,WAAA,CAAYzB,CAAY,GAAGC,CAAO;QAClE;UACE,MAAM,IAAIO,KAAA,CAAM,sCAAsCP,CAAO,EAAE,CACnE;MAAA;IACF;EACF;EAKa8D,CAAA,GAAN,MAAMnB,CAAA,SAAyBM,CAAU;IA2B9CnD,YAAYC,CAAA,EAMT;MACD,MAAM;MACN,IAAM;QAAEgE,KAAA,EAAA/D,CAAA;QAAOoE,cAAA,EAAAnE,CAAA;QAAgB+E,uBAAA,EAAA7C,CAAA;QAAyB8C,UAAA,EAAA7C,CAAA;QAAY8C,cAAA,EAAA7C;MAAe,IAAItC,CAAA;MACvF,KAAKgE,KAAA,GAAQ/D,CAAA,EACb,KAAKoE,cAAA,GAAiBnE,CAAA,EACtB,KAAK+E,uBAAA,GAA0B7C,CAAA,EAC/B,KAAK8C,UAAA,GAAa7C,CAAA,EAClB,KAAK8C,cAAA,GAAiB7C,CACxB;IAAA;IAOAjC,aAAA,EAA2B;MACzB,OAAO,KAAKU,UAAA,CAAW,CACzB;IAAA;IAOA,OAAOqE,UAAUpF,CAAA,EAAqC;MACpD,OAAO4C,CAAA,CAAiBnB,WAAA,CAAY,IAAI4D,CAAA,CAAarF,CAAK,CAAC,CAC7D;IAAA;IAEAsB,UAAUtB,CAAA,EAA8B;MACtC,KAAKgE,KAAA,CAAM1C,SAAA,CAAUtB,CAAU,GAC/BA,CAAA,CAAWwD,YAAA,CAAa,KAAKa,cAAc,GAC3CrE,CAAA,CAAWsF,kBAAA,CAAmB,KAAKJ,UAAU,GAC7ClF,CAAA,CAAWsF,kBAAA,CAAmB,KAAKH,cAAc,GACjDnF,CAAA,CAAWuF,eAAA,CAAgB,KAAKN,uBAAuB,CACzD;IAAA;IAEA,OAAOxD,YAAYzB,CAAA,EAA8C;MAC/D,IAAMC,CAAA,GAAQgE,CAAA,CAAQxC,WAAA,CAAYzB,CAAY;QACxCE,CAAA,GAAiB2D,MAAA,CAAO7D,CAAA,CAAa0D,cAAA,CAAe,CAAC;QACrDtB,CAAA,GAAapC,CAAA,CAAawF,oBAAA,CAAqB;QAC/CnD,CAAA,GAAiBrC,CAAA,CAAawF,oBAAA,CAAqB;QACnDlD,CAAA,GAA0BtC,CAAA,CAAayF,iBAAA,CAAkB7B,CAAkB;MACjF,OAAO,IAAIhB,CAAA,CAAiB;QAAEoB,KAAA,EAAA/D,CAAA;QAAOoE,cAAA,EAAAnE,CAAA;QAAgB+E,uBAAA,EAAA3C,CAAA;QAAyB4C,UAAA,EAAA9C,CAAA;QAAY+C,cAAA,EAAA9C;MAAe,CAAC,CAC5G;IAAA;EACF;EAKaqD,CAAA,GAAN,MAAM9C,CAAqB;IAWhC7C,YAAYC,CAAA,EAAwCC,CAAA,EAA2B;MAC7E,KAAK0F,cAAA,GAAiB3F,CAAA,EACtB,KAAK4F,iBAAA,GAAoB3F,CAC3B;IAAA;IAEA,OAAO6B,OAAO9B,CAAA,EAAqCC,CAAA,EAAiD;MAClG,OAAO,IAAI2C,CAAA,CACT,IAAIiD,CAAA,CAAuB;QACzBC,OAAA,EAAS9F,CAAA,CAAI+F,QAAA;QACbC,MAAA,EAAQhG,CAAA,CAAIiG,OAAA;QACZC,OAAA,EAASlG,CAAA,CAAImG,QAAA;QACbC,UAAA,EAAYpG,CAAA,CAAIqG,YAAA;QAChBC,OAAA,EAAStG,CAAA,CAAIuG;MACf,CAAC,GACDtG,CACF,CACF;IAAA;EACF;EAKM4F,CAAA,GAAN,MAAMjD,CAAuB;IA4B3B7C,YAAYC,CAAA,EAMT;MACD,IAAM;QAAE8F,OAAA,EAAA7F,CAAA;QAAS+F,MAAA,EAAA9F,CAAA;QAAQgG,OAAA,EAAA9D,CAAA;QAASgE,UAAA,EAAA/D,CAAA;QAAYiE,OAAA,EAAAhE;MAAQ,IAAItC,CAAA;MAC1D,KAAK8F,OAAA,GAAU,IAAI1B,CAAA,CAAQnE,CAAO,GAClC,KAAK+F,MAAA,GAAS,IAAIjB,CAAA,CAAQ7E,CAAM,GAChC,KAAKgG,OAAA,GAAU,IAAInB,CAAA,CAAQ3C,CAAO,GAClC,KAAKgE,UAAA,GAAa,CAAC,IAAIhC,CAAA,CAAQ/B,CAAA,CAAW,CAAC,CAAC,GAAG,IAAI+B,CAAA,CAAQ/B,CAAA,CAAW,CAAC,CAAC,CAAC,GACzE,KAAKiE,OAAA,GAAU,IAAIvB,CAAA,CAAQzC,CAAO,CACpC;IAAA;IAEA,OAAOkE,mCAAmCxG,CAAA,EAA6D;MACrG,OAAO,IAAI4C,CAAA,CAAuB;QAChCkD,OAAA,EAAS9F,CAAA,CAAI+F,QAAA;QACbC,MAAA,EAAQhG,CAAA,CAAIiG,OAAA;QACZC,OAAA,EAASlG,CAAA,CAAImG,QAAA;QACbC,UAAA,EAAYpG,CAAA,CAAIqG,YAAA;QAChBC,OAAA,EAAStG,CAAA,CAAIuG;MACf,CAAC,CACH;IAAA;EACF;AAQA,eAAsBE,GAAiB7D,CAAA,EAGL;EAChC,IAAM;IAAE8D,WAAA,EAAA1G;EAAY,IAAI4C,CAAA;EACxB,OAAO+D,CAAA,CACL,YAAY;IACV,IAAM1G,CAAA,GAAS,MAAM2G,CAAA,CAAgChE,CAAI;MACnD1C,CAAA,GAAK,MAAM2G,CAAA,CAAkCjE,CAAI;IACvD,OAAO8C,CAAA,CAAqB5D,MAAA,CAAO5B,CAAA,EAAI2D,MAAA,CAAO5D,CAAA,CAAO6G,oBAAoB,CAAC,CAC5E;EAAA,GACA,yBAAyB9G,CAAA,CAAY+G,OAAO,IAC5C,MAAO,KAAK,CACd,EAAE,CACJ;AAAA;AAQA,eAAeH,EAAgChE,CAAA,EAGL;EACxC,IAAM;MAAE8D,WAAA,EAAA1G,CAAA;MAAagH,OAAA,EAAA/G;IAAQ,IAAI2C,CAAA;IAC3B1C,CAAA,GAAe;IACf;MAAE2E,IAAA,EAAAzC;IAAK,IAAI,MAAM6E,CAAA,CAAiE;MACtFP,WAAA,EAAA1G,CAAA;MACAkH,YAAA,EAAc;MACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAK,KAAK,EAAEjG,QAAA,CAAS,CAAC,aAAalB,CAAY;MAChFoH,MAAA,EAAQ;QAAEC,cAAA,EAAgBtH,CAAA,EAASuH;MAAc;IACnD,CAAC;EAED,OAAOpF,CAAA,CAAKyC,IACd;AAAA;AAQA,eAAegC,EAAkCjE,CAAA,EAGL;EAC1C,IAAM;MAAE8D,WAAA,EAAA1G,CAAA;MAAagH,OAAA,EAAA/G;IAAQ,IAAI2C,CAAA;IAC3B1C,CAAA,GAAe;IACf;MAAE2E,IAAA,EAAAzC;IAAK,IAAI,MAAM6E,CAAA,CAAmE;MACxFP,WAAA,EAAA1G,CAAA;MACAkH,YAAA,EAAc;MACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAK,KAAK,EAAEjG,QAAA,CAAS,CAAC,aAAalB,CAAY;MAChFoH,MAAA,EAAQ;QAAEC,cAAA,EAAgBtH,CAAA,EAASuH;MAAc;IACnD,CAAC;EAED,OAAOpF,CAAA,CAAKyC,IACd;AAAA;AAAA,SAAAxF,EAAA,IAAA+C,CAAA,EAAA9C,CAAA,IAAAqE,CAAA,EAAApE,CAAA,IAAA6E,CAAA,EAAA5E,CAAA,IAAA0D,CAAA,EAAAzD,EAAA,IAAAO,CAAA,EAAAN,EAAA,IAAAuE,CAAA,EAAAtE,EAAA,IAAAuE,CAAA,EAAAtE,EAAA,IAAA6D,CAAA,EAAAd,CAAA,IAAAzC,CAAA,EAAA+C,CAAA,IAAA4D,CAAA,EAAApD,CAAA,IAAArE,CAAA,EAAA8E,CAAA,IAAAa,CAAA,EAAAd,CAAA,IAAAnB,CAAA,EAAAiB,CAAA,IAAA1B,CAAA,EAAAqD,CAAA,IAAA7F,CAAA,EAAA4G,EAAA,IAAAtG,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}