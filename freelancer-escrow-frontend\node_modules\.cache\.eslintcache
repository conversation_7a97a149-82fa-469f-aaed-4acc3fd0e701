[{"C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\App.js": "4", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\WalletConnection.js": "5", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\FreelancerSection.js": "6", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\BlockchainSection.js": "7", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\EscrowSection.js": "8"}, {"size": 278, "mtime": 1755956184654, "results": "9", "hashOfConfig": "10"}, {"size": 9885, "mtime": 1755965211921, "results": "11", "hashOfConfig": "10"}, {"size": 254, "mtime": 1755977828436, "results": "12", "hashOfConfig": "13"}, {"size": 6183, "mtime": 1755977864908, "results": "14", "hashOfConfig": "13"}, {"size": 4596, "mtime": 1755978318722, "results": "15", "hashOfConfig": "13"}, {"size": 6988, "mtime": 1755977909857, "results": "16", "hashOfConfig": "13"}, {"size": 8104, "mtime": 1755978295850, "results": "17", "hashOfConfig": "13"}, {"size": 10823, "mtime": 1755977949814, "results": "18", "hashOfConfig": "13"}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cwm9m4", {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g<PERSON><PERSON><PERSON>", {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\src\\App.tsx", ["43"], [], "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\WalletConnection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\FreelancerSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\BlockchainSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\src\\components\\EscrowSection.js", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 40, "column": 19, "nodeType": "46", "messageId": "47", "endLine": 40, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'setLoading' is assigned a value but never used.", "Identifier", "unusedVar"]