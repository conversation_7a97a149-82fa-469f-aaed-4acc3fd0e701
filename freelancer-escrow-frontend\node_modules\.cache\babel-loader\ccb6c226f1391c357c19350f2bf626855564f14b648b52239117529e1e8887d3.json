{"ast": null, "code": "import { bytes, exists, number, output } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport { Hash, u32, toBytes, wrapConstructor, wrapXOFConstructorWithOpts } from './utils.js';\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n// Various per round constants calculations\nconst [SHA3_PI, SHA3_ROTL, _SHA3_IOTA] = [[], [], []];\nconst _0n = /* @__PURE__ */BigInt(0);\nconst _1n = /* @__PURE__ */BigInt(1);\nconst _2n = /* @__PURE__ */BigInt(2);\nconst _7n = /* @__PURE__ */BigInt(7);\nconst _256n = /* @__PURE__ */BigInt(256);\nconst _0x71n = /* @__PURE__ */BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((round + 1) * (round + 2) / 2 % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = (R << _1n ^ (R >> _7n) * _0x71n) % _256n;\n    if (R & _2n) t ^= _1n << (_1n << /* @__PURE__ */BigInt(j)) - _1n;\n  }\n  _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */split(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s);\nconst rotlL = (h, l, s) => s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s);\n// Same as keccakf1600, but allows to skip some rounds\nexport function keccakP(s, rounds = 24) {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  B.fill(0);\n}\nexport class Keccak extends Hash {\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n    super();\n    this.blockLen = blockLen;\n    this.suffix = suffix;\n    this.outputLen = outputLen;\n    this.enableXOF = enableXOF;\n    this.rounds = rounds;\n    this.pos = 0;\n    this.posOut = 0;\n    this.finished = false;\n    this.destroyed = false;\n    // Can be passed from user as dkLen\n    number(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    if (0 >= this.blockLen || this.blockLen >= 200) throw new Error('Sha3 supports only keccak-f1600 function');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  keccak() {\n    keccakP(this.state32, this.rounds);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data) {\n    exists(this);\n    const {\n      blockLen,\n      state\n    } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len;) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  finish() {\n    if (this.finished) return;\n    this.finished = true;\n    const {\n      state,\n      suffix,\n      pos,\n      blockLen\n    } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  writeInto(out) {\n    exists(this, false);\n    bytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const {\n      blockLen\n    } = this;\n    for (let pos = 0, len = out.length; pos < len;) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out) {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes) {\n    number(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out) {\n    output(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest() {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy() {\n    this.destroyed = true;\n    this.state.fill(0);\n  }\n  _cloneInto(to) {\n    const {\n      blockLen,\n      suffix,\n      outputLen,\n      rounds,\n      enableXOF\n    } = this;\n    to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\nconst gen = (suffix, blockLen, outputLen) => wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\nexport const sha3_224 = /* @__PURE__ */gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */\nexport const sha3_256 = /* @__PURE__ */gen(0x06, 136, 256 / 8);\nexport const sha3_384 = /* @__PURE__ */gen(0x06, 104, 384 / 8);\nexport const sha3_512 = /* @__PURE__ */gen(0x06, 72, 512 / 8);\nexport const keccak_224 = /* @__PURE__ */gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */\nexport const keccak_256 = /* @__PURE__ */gen(0x01, 136, 256 / 8);\nexport const keccak_384 = /* @__PURE__ */gen(0x01, 104, 384 / 8);\nexport const keccak_512 = /* @__PURE__ */gen(0x01, 72, 512 / 8);\nconst genShake = (suffix, blockLen, outputLen) => wrapXOFConstructorWithOpts((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\nexport const shake128 = /* @__PURE__ */genShake(0x1f, 168, 128 / 8);\nexport const shake256 = /* @__PURE__ */genShake(0x1f, 136, 256 / 8);", "map": {"version": 3, "names": ["bytes", "exists", "number", "output", "rotlBH", "rotlBL", "rotlSH", "rotlSL", "split", "Hash", "u32", "toBytes", "wrapConstructor", "wrapXOFConstructorWithOpts", "SHA3_PI", "SHA3_ROTL", "_SHA3_IOTA", "_0n", "BigInt", "_1n", "_2n", "_7n", "_256n", "_0x71n", "round", "R", "x", "y", "push", "t", "j", "SHA3_IOTA_H", "SHA3_IOTA_L", "rotlH", "h", "l", "s", "rotlL", "keccakP", "rounds", "B", "Uint32Array", "idx1", "idx0", "B0", "B1", "Th", "Tl", "curH", "curL", "shift", "PI", "fill", "Keccak", "constructor", "blockLen", "suffix", "outputLen", "enableXOF", "pos", "posOut", "finished", "destroyed", "Error", "state", "Uint8Array", "state32", "keccak", "update", "data", "len", "length", "take", "Math", "min", "i", "finish", "writeInto", "out", "bufferOut", "set", "subarray", "xofInto", "xof", "digestInto", "destroy", "digest", "_cloneInto", "to", "gen", "sha3_224", "sha3_256", "sha3_384", "sha3_512", "keccak_224", "keccak_256", "keccak_384", "keccak_512", "gen<PERSON>hake", "opts", "dkLen", "undefined", "shake128", "shake256"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\aptos\\node_modules\\@noble\\hashes\\src\\sha3.ts"], "sourcesContent": ["import { bytes, exists, number, output } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport {\n  Hash,\n  u32,\n  Input,\n  toBytes,\n  wrapConstructor,\n  wrapXOFConstructorWithOpts,\n  HashXOF,\n} from './utils.js';\n\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n\n// Various per round constants calculations\nconst [SHA3_PI, SHA3_ROTL, _SHA3_IOTA]: [number[], number[], bigint[]] = [[], [], []];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n// Same as keccakf1600, but allows to skip some rounds\nexport function keccakP(s: Uint32Array, rounds: number = 24) {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  B.fill(0);\n}\n\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    public blockLen: number,\n    public suffix: number,\n    public outputLen: number,\n    protected enableXOF = false,\n    protected rounds: number = 24\n  ) {\n    super();\n    // Can be passed from user as dkLen\n    number(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    if (0 >= this.blockLen || this.blockLen >= 200)\n      throw new Error('Sha3 supports only keccak-f1600 function');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  protected keccak() {\n    keccakP(this.state32, this.rounds);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input) {\n    exists(this);\n    const { blockLen, state } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish() {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    exists(this, false);\n    bytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    number(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array) {\n    output(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest() {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy() {\n    this.destroyed = true;\n    this.state.fill(0);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\n\nexport const sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */\nexport const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\nexport const sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\nexport const sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\nexport const keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */\nexport const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\nexport const keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\nexport const keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapXOFConstructorWithOpts<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\nexport const shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\nexport const shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,cAAc;AAC5D,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,WAAW;AACjE,SACEC,IAAI,EACJC,GAAG,EAEHC,OAAO,EACPC,eAAe,EACfC,0BAA0B,QAErB,YAAY;AAEnB;AACA;AAEA;AACA,MAAM,CAACC,OAAO,EAAEC,SAAS,EAAEC,UAAU,CAAC,GAAmC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrF,MAAMC,GAAG,GAAG,eAAgBC,MAAM,CAAC,CAAC,CAAC;AACrC,MAAMC,GAAG,GAAG,eAAgBD,MAAM,CAAC,CAAC,CAAC;AACrC,MAAME,GAAG,GAAG,eAAgBF,MAAM,CAAC,CAAC,CAAC;AACrC,MAAMG,GAAG,GAAG,eAAgBH,MAAM,CAAC,CAAC,CAAC;AACrC,MAAMI,KAAK,GAAG,eAAgBJ,MAAM,CAAC,GAAG,CAAC;AACzC,MAAMK,MAAM,GAAG,eAAgBL,MAAM,CAAC,IAAI,CAAC;AAC3C,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEC,CAAC,GAAGN,GAAG,EAAEO,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEH,KAAK,GAAG,EAAE,EAAEA,KAAK,EAAE,EAAE;EAC9D;EACA,CAACE,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACA,CAAC,EAAE,CAAC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAGC,CAAC,IAAI,CAAC,CAAC;EACjCb,OAAO,CAACc,IAAI,CAAC,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAGD,CAAC,CAAC,CAAC;EAC7B;EACAX,SAAS,CAACa,IAAI,CAAG,CAACJ,KAAK,GAAG,CAAC,KAAKA,KAAK,GAAG,CAAC,CAAC,GAAI,CAAC,GAAI,EAAE,CAAC;EACtD;EACA,IAAIK,CAAC,GAAGZ,GAAG;EACX,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BL,CAAC,GAAG,CAAEA,CAAC,IAAIN,GAAG,GAAK,CAACM,CAAC,IAAIJ,GAAG,IAAIE,MAAO,IAAID,KAAK;IAChD,IAAIG,CAAC,GAAGL,GAAG,EAAES,CAAC,IAAIV,GAAG,IAAK,CAACA,GAAG,IAAI,eAAgBD,MAAM,CAACY,CAAC,CAAC,IAAIX,GAAI;EACrE;EACAH,UAAU,CAACY,IAAI,CAACC,CAAC,CAAC;AACpB;AACA,MAAM,CAACE,WAAW,EAAEC,WAAW,CAAC,GAAG,eAAgBxB,KAAK,CAACQ,UAAU,EAAE,IAAI,CAAC;AAE1E;AACA,MAAMiB,KAAK,GAAGA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS,KAAMA,CAAC,GAAG,EAAE,GAAGhC,MAAM,CAAC8B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG9B,MAAM,CAAC4B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAE;AAC/F,MAAMC,KAAK,GAAGA,CAACH,CAAS,EAAEC,CAAS,EAAEC,CAAS,KAAMA,CAAC,GAAG,EAAE,GAAG/B,MAAM,CAAC6B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG7B,MAAM,CAAC2B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAE;AAE/F;AACA,OAAM,SAAUE,OAAOA,CAACF,CAAc,EAAEG,MAAA,GAAiB,EAAE;EACzD,MAAMC,CAAC,GAAG,IAAIC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;EAChC;EACA,KAAK,IAAIjB,KAAK,GAAG,EAAE,GAAGe,MAAM,EAAEf,KAAK,GAAG,EAAE,EAAEA,KAAK,EAAE,EAAE;IACjD;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEc,CAAC,CAACd,CAAC,CAAC,GAAGU,CAAC,CAACV,CAAC,CAAC,GAAGU,CAAC,CAACV,CAAC,GAAG,EAAE,CAAC,GAAGU,CAAC,CAACV,CAAC,GAAG,EAAE,CAAC,GAAGU,CAAC,CAACV,CAAC,GAAG,EAAE,CAAC,GAAGU,CAAC,CAACV,CAAC,GAAG,EAAE,CAAC;IACxF,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC9B,MAAMgB,IAAI,GAAG,CAAChB,CAAC,GAAG,CAAC,IAAI,EAAE;MACzB,MAAMiB,IAAI,GAAG,CAACjB,CAAC,GAAG,CAAC,IAAI,EAAE;MACzB,MAAMkB,EAAE,GAAGJ,CAAC,CAACG,IAAI,CAAC;MAClB,MAAME,EAAE,GAAGL,CAAC,CAACG,IAAI,GAAG,CAAC,CAAC;MACtB,MAAMG,EAAE,GAAGb,KAAK,CAACW,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC,GAAGL,CAAC,CAACE,IAAI,CAAC;MACrC,MAAMK,EAAE,GAAGV,KAAK,CAACO,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC,GAAGL,CAAC,CAACE,IAAI,GAAG,CAAC,CAAC;MACzC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE;QAC/BS,CAAC,CAACV,CAAC,GAAGC,CAAC,CAAC,IAAImB,EAAE;QACdV,CAAC,CAACV,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,IAAIoB,EAAE;MACpB;IACF;IACA;IACA,IAAIC,IAAI,GAAGZ,CAAC,CAAC,CAAC,CAAC;IACf,IAAIa,IAAI,GAAGb,CAAC,CAAC,CAAC,CAAC;IACf,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMqB,KAAK,GAAGnC,SAAS,CAACc,CAAC,CAAC;MAC1B,MAAMiB,EAAE,GAAGb,KAAK,CAACe,IAAI,EAAEC,IAAI,EAAEC,KAAK,CAAC;MACnC,MAAMH,EAAE,GAAGV,KAAK,CAACW,IAAI,EAAEC,IAAI,EAAEC,KAAK,CAAC;MACnC,MAAMC,EAAE,GAAGrC,OAAO,CAACe,CAAC,CAAC;MACrBmB,IAAI,GAAGZ,CAAC,CAACe,EAAE,CAAC;MACZF,IAAI,GAAGb,CAAC,CAACe,EAAE,GAAG,CAAC,CAAC;MAChBf,CAAC,CAACe,EAAE,CAAC,GAAGL,EAAE;MACVV,CAAC,CAACe,EAAE,GAAG,CAAC,CAAC,GAAGJ,EAAE;IAChB;IACA;IACA,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE;MAC/B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEc,CAAC,CAACd,CAAC,CAAC,GAAGU,CAAC,CAACT,CAAC,GAAGD,CAAC,CAAC;MAC5C,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEU,CAAC,CAACT,CAAC,GAAGD,CAAC,CAAC,IAAI,CAACc,CAAC,CAAC,CAACd,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAGc,CAAC,CAAC,CAACd,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;IAC7E;IACA;IACAU,CAAC,CAAC,CAAC,CAAC,IAAIL,WAAW,CAACP,KAAK,CAAC;IAC1BY,CAAC,CAAC,CAAC,CAAC,IAAIJ,WAAW,CAACR,KAAK,CAAC;EAC5B;EACAgB,CAAC,CAACY,IAAI,CAAC,CAAC,CAAC;AACX;AAEA,OAAM,MAAOC,MAAO,SAAQ5C,IAAY;EAOtC;EACA6C,YACSC,QAAgB,EAChBC,MAAc,EACdC,SAAiB,EACdC,SAAA,GAAY,KAAK,EACjBnB,MAAA,GAAiB,EAAE;IAE7B,KAAK,EAAE;IANA,KAAAgB,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IACN,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAnB,MAAM,GAANA,MAAM;IAXR,KAAAoB,GAAG,GAAG,CAAC;IACP,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,SAAS,GAAG,KAAK;IAUzB;IACA5D,MAAM,CAACuD,SAAS,CAAC;IACjB;IACA,IAAI,CAAC,IAAI,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,GAAG,EAC5C,MAAM,IAAIQ,KAAK,CAAC,0CAA0C,CAAC;IAC7D,IAAI,CAACC,KAAK,GAAG,IAAIC,UAAU,CAAC,GAAG,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGxD,GAAG,CAAC,IAAI,CAACsD,KAAK,CAAC;EAChC;EACUG,MAAMA,CAAA;IACd7B,OAAO,CAAC,IAAI,CAAC4B,OAAO,EAAE,IAAI,CAAC3B,MAAM,CAAC;IAClC,IAAI,CAACqB,MAAM,GAAG,CAAC;IACf,IAAI,CAACD,GAAG,GAAG,CAAC;EACd;EACAS,MAAMA,CAACC,IAAW;IAChBpE,MAAM,CAAC,IAAI,CAAC;IACZ,MAAM;MAAEsD,QAAQ;MAAES;IAAK,CAAE,GAAG,IAAI;IAChCK,IAAI,GAAG1D,OAAO,CAAC0D,IAAI,CAAC;IACpB,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM;IACvB,KAAK,IAAIZ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGW,GAAG,GAAI;MAC7B,MAAME,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACnB,QAAQ,GAAG,IAAI,CAACI,GAAG,EAAEW,GAAG,GAAGX,GAAG,CAAC;MACrD,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAEX,KAAK,CAAC,IAAI,CAACL,GAAG,EAAE,CAAC,IAAIU,IAAI,CAACV,GAAG,EAAE,CAAC;MAC/D,IAAI,IAAI,CAACA,GAAG,KAAKJ,QAAQ,EAAE,IAAI,CAACY,MAAM,EAAE;IAC1C;IACA,OAAO,IAAI;EACb;EACUS,MAAMA,CAAA;IACd,IAAI,IAAI,CAACf,QAAQ,EAAE;IACnB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,MAAM;MAAEG,KAAK;MAAER,MAAM;MAAEG,GAAG;MAAEJ;IAAQ,CAAE,GAAG,IAAI;IAC7C;IACAS,KAAK,CAACL,GAAG,CAAC,IAAIH,MAAM;IACpB,IAAI,CAACA,MAAM,GAAG,IAAI,MAAM,CAAC,IAAIG,GAAG,KAAKJ,QAAQ,GAAG,CAAC,EAAE,IAAI,CAACY,MAAM,EAAE;IAChEH,KAAK,CAACT,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI;IAC3B,IAAI,CAACY,MAAM,EAAE;EACf;EACUU,SAASA,CAACC,GAAe;IACjC7E,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;IACnBD,KAAK,CAAC8E,GAAG,CAAC;IACV,IAAI,CAACF,MAAM,EAAE;IACb,MAAMG,SAAS,GAAG,IAAI,CAACf,KAAK;IAC5B,MAAM;MAAET;IAAQ,CAAE,GAAG,IAAI;IACzB,KAAK,IAAII,GAAG,GAAG,CAAC,EAAEW,GAAG,GAAGQ,GAAG,CAACP,MAAM,EAAEZ,GAAG,GAAGW,GAAG,GAAI;MAC/C,IAAI,IAAI,CAACV,MAAM,IAAIL,QAAQ,EAAE,IAAI,CAACY,MAAM,EAAE;MAC1C,MAAMK,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACnB,QAAQ,GAAG,IAAI,CAACK,MAAM,EAAEU,GAAG,GAAGX,GAAG,CAAC;MACxDmB,GAAG,CAACE,GAAG,CAACD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACrB,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGY,IAAI,CAAC,EAAEb,GAAG,CAAC;MACjE,IAAI,CAACC,MAAM,IAAIY,IAAI;MACnBb,GAAG,IAAIa,IAAI;IACb;IACA,OAAOM,GAAG;EACZ;EACAI,OAAOA,CAACJ,GAAe;IACrB;IACA,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE,MAAM,IAAIK,KAAK,CAAC,uCAAuC,CAAC;IAC7E,OAAO,IAAI,CAACc,SAAS,CAACC,GAAG,CAAC;EAC5B;EACAK,GAAGA,CAACnF,KAAa;IACfE,MAAM,CAACF,KAAK,CAAC;IACb,OAAO,IAAI,CAACkF,OAAO,CAAC,IAAIjB,UAAU,CAACjE,KAAK,CAAC,CAAC;EAC5C;EACAoF,UAAUA,CAACN,GAAe;IACxB3E,MAAM,CAAC2E,GAAG,EAAE,IAAI,CAAC;IACjB,IAAI,IAAI,CAACjB,QAAQ,EAAE,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;IACjE,IAAI,CAACc,SAAS,CAACC,GAAG,CAAC;IACnB,IAAI,CAACO,OAAO,EAAE;IACd,OAAOP,GAAG;EACZ;EACAQ,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACF,UAAU,CAAC,IAAInB,UAAU,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC;EACxD;EACA4B,OAAOA,CAAA;IACL,IAAI,CAACvB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACE,KAAK,CAACZ,IAAI,CAAC,CAAC,CAAC;EACpB;EACAmC,UAAUA,CAACC,EAAW;IACpB,MAAM;MAAEjC,QAAQ;MAAEC,MAAM;MAAEC,SAAS;MAAElB,MAAM;MAAEmB;IAAS,CAAE,GAAG,IAAI;IAC/D8B,EAAE,KAAFA,EAAE,GAAK,IAAInC,MAAM,CAACE,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEnB,MAAM,CAAC;IACjEiD,EAAE,CAACtB,OAAO,CAACc,GAAG,CAAC,IAAI,CAACd,OAAO,CAAC;IAC5BsB,EAAE,CAAC7B,GAAG,GAAG,IAAI,CAACA,GAAG;IACjB6B,EAAE,CAAC5B,MAAM,GAAG,IAAI,CAACA,MAAM;IACvB4B,EAAE,CAAC3B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC3B2B,EAAE,CAACjD,MAAM,GAAGA,MAAM;IAClB;IACAiD,EAAE,CAAChC,MAAM,GAAGA,MAAM;IAClBgC,EAAE,CAAC/B,SAAS,GAAGA,SAAS;IACxB+B,EAAE,CAAC9B,SAAS,GAAGA,SAAS;IACxB8B,EAAE,CAAC1B,SAAS,GAAG,IAAI,CAACA,SAAS;IAC7B,OAAO0B,EAAE;EACX;;AAGF,MAAMC,GAAG,GAAGA,CAACjC,MAAc,EAAED,QAAgB,EAAEE,SAAiB,KAC9D7C,eAAe,CAAC,MAAM,IAAIyC,MAAM,CAACE,QAAQ,EAAEC,MAAM,EAAEC,SAAS,CAAC,CAAC;AAEhE,OAAO,MAAMiC,QAAQ,GAAG,eAAgBD,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AAC/D;;;;AAIA,OAAO,MAAME,QAAQ,GAAG,eAAgBF,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AAC/D,OAAO,MAAMG,QAAQ,GAAG,eAAgBH,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AAC/D,OAAO,MAAMI,QAAQ,GAAG,eAAgBJ,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC;AAC9D,OAAO,MAAMK,UAAU,GAAG,eAAgBL,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AACjE;;;;AAIA,OAAO,MAAMM,UAAU,GAAG,eAAgBN,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AACjE,OAAO,MAAMO,UAAU,GAAG,eAAgBP,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AACjE,OAAO,MAAMQ,UAAU,GAAG,eAAgBR,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC;AAIhE,MAAMS,QAAQ,GAAGA,CAAC1C,MAAc,EAAED,QAAgB,EAAEE,SAAiB,KACnE5C,0BAA0B,CACxB,CAACsF,IAAA,GAAkB,EAAE,KACnB,IAAI9C,MAAM,CAACE,QAAQ,EAAEC,MAAM,EAAE2C,IAAI,CAACC,KAAK,KAAKC,SAAS,GAAG5C,SAAS,GAAG0C,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC,CACxF;AAEH,OAAO,MAAME,QAAQ,GAAG,eAAgBJ,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AACpE,OAAO,MAAMK,QAAQ,GAAG,eAAgBL,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}