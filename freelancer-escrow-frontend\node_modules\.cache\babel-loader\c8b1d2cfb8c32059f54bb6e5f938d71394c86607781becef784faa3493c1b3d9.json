{"ast": null, "code": "/**\n * Montgomery curve methods. It's not really whole montgomery curve,\n * just bunch of very specific methods for X25519 / X448 from\n * [RFC 7748](https://www.rfc-editor.org/rfc/rfc7748)\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { _validateObject, abytes, aInRange, bytesToNumberLE, ensureBytes, numberToBytesLE, randomBytes } from \"../utils.js\";\nimport { mod } from \"./modular.js\";\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nfunction validateOpts(curve) {\n  _validateObject(curve, {\n    adjustScalarBytes: 'function',\n    powPminus2: 'function'\n  });\n  return Object.freeze({\n    ...curve\n  });\n}\nexport function montgomery(curveDef) {\n  const CURVE = validateOpts(curveDef);\n  const {\n    P,\n    type,\n    adjustScalarBytes,\n    powPminus2,\n    randomBytes: rand\n  } = CURVE;\n  const is25519 = type === 'x25519';\n  if (!is25519 && type !== 'x448') throw new Error('invalid type');\n  const randomBytes_ = rand || randomBytes;\n  const montgomeryBits = is25519 ? 255 : 448;\n  const fieldLen = is25519 ? 32 : 56;\n  const Gu = is25519 ? BigInt(9) : BigInt(5);\n  // RFC 7748 #5:\n  // The constant a24 is (486662 - 2) / 4 = 121665 for curve25519/X25519 and\n  // (156326 - 2) / 4 = 39081 for curve448/X448\n  // const a = is25519 ? 156326n : 486662n;\n  const a24 = is25519 ? BigInt(121665) : BigInt(39081);\n  // RFC: x25519 \"the resulting integer is of the form 2^254 plus\n  // eight times a value between 0 and 2^251 - 1 (inclusive)\"\n  // x448: \"2^447 plus four times a value between 0 and 2^445 - 1 (inclusive)\"\n  const minScalar = is25519 ? _2n ** BigInt(254) : _2n ** BigInt(447);\n  const maxAdded = is25519 ? BigInt(8) * _2n ** BigInt(251) - _1n : BigInt(4) * _2n ** BigInt(445) - _1n;\n  const maxScalar = minScalar + maxAdded + _1n; // (inclusive)\n  const modP = n => mod(n, P);\n  const GuBytes = encodeU(Gu);\n  function encodeU(u) {\n    return numberToBytesLE(modP(u), fieldLen);\n  }\n  function decodeU(u) {\n    const _u = ensureBytes('u coordinate', u, fieldLen);\n    // RFC: When receiving such an array, implementations of X25519\n    // (but not X448) MUST mask the most significant bit in the final byte.\n    if (is25519) _u[31] &= 127; // 0b0111_1111\n    // RFC: Implementations MUST accept non-canonical values and process them as\n    // if they had been reduced modulo the field prime.  The non-canonical\n    // values are 2^255 - 19 through 2^255 - 1 for X25519 and 2^448 - 2^224\n    // - 1 through 2^448 - 1 for X448.\n    return modP(bytesToNumberLE(_u));\n  }\n  function decodeScalar(scalar) {\n    return bytesToNumberLE(adjustScalarBytes(ensureBytes('scalar', scalar, fieldLen)));\n  }\n  function scalarMult(scalar, u) {\n    const pu = montgomeryLadder(decodeU(u), decodeScalar(scalar));\n    // Some public keys are useless, of low-order. Curve author doesn't think\n    // it needs to be validated, but we do it nonetheless.\n    // https://cr.yp.to/ecdh.html#validate\n    if (pu === _0n) throw new Error('invalid private or public key received');\n    return encodeU(pu);\n  }\n  // Computes public key from private. By doing scalar multiplication of base point.\n  function scalarMultBase(scalar) {\n    return scalarMult(scalar, GuBytes);\n  }\n  // cswap from RFC7748 \"example code\"\n  function cswap(swap, x_2, x_3) {\n    // dummy = mask(swap) AND (x_2 XOR x_3)\n    // Where mask(swap) is the all-1 or all-0 word of the same length as x_2\n    // and x_3, computed, e.g., as mask(swap) = 0 - swap.\n    const dummy = modP(swap * (x_2 - x_3));\n    x_2 = modP(x_2 - dummy); // x_2 = x_2 XOR dummy\n    x_3 = modP(x_3 + dummy); // x_3 = x_3 XOR dummy\n    return {\n      x_2,\n      x_3\n    };\n  }\n  /**\n   * Montgomery x-only multiplication ladder.\n   * @param pointU u coordinate (x) on Montgomery Curve 25519\n   * @param scalar by which the point would be multiplied\n   * @returns new Point on Montgomery curve\n   */\n  function montgomeryLadder(u, scalar) {\n    aInRange('u', u, _0n, P);\n    aInRange('scalar', scalar, minScalar, maxScalar);\n    const k = scalar;\n    const x_1 = u;\n    let x_2 = _1n;\n    let z_2 = _0n;\n    let x_3 = u;\n    let z_3 = _1n;\n    let swap = _0n;\n    for (let t = BigInt(montgomeryBits - 1); t >= _0n; t--) {\n      const k_t = k >> t & _1n;\n      swap ^= k_t;\n      ({\n        x_2,\n        x_3\n      } = cswap(swap, x_2, x_3));\n      ({\n        x_2: z_2,\n        x_3: z_3\n      } = cswap(swap, z_2, z_3));\n      swap = k_t;\n      const A = x_2 + z_2;\n      const AA = modP(A * A);\n      const B = x_2 - z_2;\n      const BB = modP(B * B);\n      const E = AA - BB;\n      const C = x_3 + z_3;\n      const D = x_3 - z_3;\n      const DA = modP(D * A);\n      const CB = modP(C * B);\n      const dacb = DA + CB;\n      const da_cb = DA - CB;\n      x_3 = modP(dacb * dacb);\n      z_3 = modP(x_1 * modP(da_cb * da_cb));\n      x_2 = modP(AA * BB);\n      z_2 = modP(E * (AA + modP(a24 * E)));\n    }\n    ({\n      x_2,\n      x_3\n    } = cswap(swap, x_2, x_3));\n    ({\n      x_2: z_2,\n      x_3: z_3\n    } = cswap(swap, z_2, z_3));\n    const z2 = powPminus2(z_2); // `Fp.pow(x, P - _2n)` is much slower equivalent\n    return modP(x_2 * z2); // Return x_2 * (z_2^(p - 2))\n  }\n  const lengths = {\n    secretKey: fieldLen,\n    publicKey: fieldLen,\n    seed: fieldLen\n  };\n  const randomSecretKey = (seed = randomBytes_(fieldLen)) => {\n    abytes(seed, lengths.seed);\n    return seed;\n  };\n  function keygen(seed) {\n    const secretKey = randomSecretKey(seed);\n    return {\n      secretKey,\n      publicKey: scalarMultBase(secretKey)\n    };\n  }\n  const utils = {\n    randomSecretKey,\n    randomPrivateKey: randomSecretKey\n  };\n  return {\n    keygen,\n    getSharedSecret: (secretKey, publicKey) => scalarMult(secretKey, publicKey),\n    getPublicKey: secretKey => scalarMultBase(secretKey),\n    scalarMult,\n    scalarMultBase,\n    utils,\n    GuBytes: GuBytes.slice(),\n    lengths\n  };\n}", "map": {"version": 3, "names": ["_validateObject", "abytes", "aInRange", "bytesToNumberLE", "ensureBytes", "numberToBytesLE", "randomBytes", "mod", "_0n", "BigInt", "_1n", "_2n", "validateOpts", "curve", "adjustScalarBytes", "powPminus2", "Object", "freeze", "montgomery", "curveDef", "CURVE", "P", "type", "rand", "is25519", "Error", "randomBytes_", "montgomeryBits", "fieldLen", "<PERSON>u", "a24", "minScalar", "maxAdded", "maxScalar", "modP", "n", "GuBytes", "encodeU", "u", "decodeU", "_u", "decodeScalar", "scalar", "scalarMult", "pu", "montgomeryLadder", "scalarMultBase", "cswap", "swap", "x_2", "x_3", "dummy", "k", "x_1", "z_2", "z_3", "t", "k_t", "A", "AA", "B", "BB", "E", "C", "D", "DA", "CB", "dacb", "da_cb", "z2", "lengths", "secret<PERSON>ey", "public<PERSON>ey", "seed", "randomSec<PERSON><PERSON>ey", "keygen", "utils", "randomPrivateKey", "getSharedSecret", "getPublicKey", "slice"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\abstract\\montgomery.ts"], "sourcesContent": ["/**\n * Montgomery curve methods. It's not really whole montgomery curve,\n * just bunch of very specific methods for X25519 / X448 from\n * [RFC 7748](https://www.rfc-editor.org/rfc/rfc7748)\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  _validateObject,\n  abytes,\n  aInRange,\n  bytesToNumberLE,\n  ensureBytes,\n  numberToBytesLE,\n  randomBytes,\n} from '../utils.ts';\nimport type { CurveLengths } from './curve.ts';\nimport { mod } from './modular.ts';\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\ntype Hex = string | Uint8Array;\n\nexport type CurveType = {\n  P: bigint; // finite field prime\n  type: 'x25519' | 'x448';\n  adjustScalarBytes: (bytes: Uint8Array) => Uint8Array;\n  powPminus2: (x: bigint) => bigint;\n  randomBytes?: (bytesLength?: number) => Uint8Array;\n};\n\nexport type MontgomeryECDH = {\n  scalarMult: (scalar: Hex, u: Hex) => Uint8Array;\n  scalarMultBase: (scalar: Hex) => Uint8Array;\n  getSharedSecret: (secretKeyA: Hex, publicKeyB: Hex) => Uint8Array;\n  getPublicKey: (secretKey: Hex) => Uint8Array;\n  utils: {\n    randomSecretKey: () => Uint8Array;\n    /** @deprecated use `randomSecretKey` */\n    randomPrivateKey: () => Uint8Array;\n  };\n  GuBytes: Uint8Array;\n  lengths: CurveLengths;\n  keygen: (seed?: Uint8Array) => { secretKey: Uint8Array; publicKey: Uint8Array };\n};\nexport type CurveFn = MontgomeryECDH;\n\nfunction validateOpts(curve: CurveType) {\n  _validateObject(curve, {\n    adjustScalarBytes: 'function',\n    powPminus2: 'function',\n  });\n  return Object.freeze({ ...curve } as const);\n}\n\nexport function montgomery(curveDef: CurveType): MontgomeryECDH {\n  const CURVE = validateOpts(curveDef);\n  const { P, type, adjustScalarBytes, powPminus2, randomBytes: rand } = CURVE;\n  const is25519 = type === 'x25519';\n  if (!is25519 && type !== 'x448') throw new Error('invalid type');\n  const randomBytes_ = rand || randomBytes;\n\n  const montgomeryBits = is25519 ? 255 : 448;\n  const fieldLen = is25519 ? 32 : 56;\n  const Gu = is25519 ? BigInt(9) : BigInt(5);\n  // RFC 7748 #5:\n  // The constant a24 is (486662 - 2) / 4 = 121665 for curve25519/X25519 and\n  // (156326 - 2) / 4 = 39081 for curve448/X448\n  // const a = is25519 ? 156326n : 486662n;\n  const a24 = is25519 ? BigInt(121665) : BigInt(39081);\n  // RFC: x25519 \"the resulting integer is of the form 2^254 plus\n  // eight times a value between 0 and 2^251 - 1 (inclusive)\"\n  // x448: \"2^447 plus four times a value between 0 and 2^445 - 1 (inclusive)\"\n  const minScalar = is25519 ? _2n ** BigInt(254) : _2n ** BigInt(447);\n  const maxAdded = is25519\n    ? BigInt(8) * _2n ** BigInt(251) - _1n\n    : BigInt(4) * _2n ** BigInt(445) - _1n;\n  const maxScalar = minScalar + maxAdded + _1n; // (inclusive)\n  const modP = (n: bigint) => mod(n, P);\n  const GuBytes = encodeU(Gu);\n  function encodeU(u: bigint): Uint8Array {\n    return numberToBytesLE(modP(u), fieldLen);\n  }\n  function decodeU(u: Hex): bigint {\n    const _u = ensureBytes('u coordinate', u, fieldLen);\n    // RFC: When receiving such an array, implementations of X25519\n    // (but not X448) MUST mask the most significant bit in the final byte.\n    if (is25519) _u[31] &= 127; // 0b0111_1111\n    // RFC: Implementations MUST accept non-canonical values and process them as\n    // if they had been reduced modulo the field prime.  The non-canonical\n    // values are 2^255 - 19 through 2^255 - 1 for X25519 and 2^448 - 2^224\n    // - 1 through 2^448 - 1 for X448.\n    return modP(bytesToNumberLE(_u));\n  }\n  function decodeScalar(scalar: Hex): bigint {\n    return bytesToNumberLE(adjustScalarBytes(ensureBytes('scalar', scalar, fieldLen)));\n  }\n  function scalarMult(scalar: Hex, u: Hex): Uint8Array {\n    const pu = montgomeryLadder(decodeU(u), decodeScalar(scalar));\n    // Some public keys are useless, of low-order. Curve author doesn't think\n    // it needs to be validated, but we do it nonetheless.\n    // https://cr.yp.to/ecdh.html#validate\n    if (pu === _0n) throw new Error('invalid private or public key received');\n    return encodeU(pu);\n  }\n  // Computes public key from private. By doing scalar multiplication of base point.\n  function scalarMultBase(scalar: Hex): Uint8Array {\n    return scalarMult(scalar, GuBytes);\n  }\n\n  // cswap from RFC7748 \"example code\"\n  function cswap(swap: bigint, x_2: bigint, x_3: bigint): { x_2: bigint; x_3: bigint } {\n    // dummy = mask(swap) AND (x_2 XOR x_3)\n    // Where mask(swap) is the all-1 or all-0 word of the same length as x_2\n    // and x_3, computed, e.g., as mask(swap) = 0 - swap.\n    const dummy = modP(swap * (x_2 - x_3));\n    x_2 = modP(x_2 - dummy); // x_2 = x_2 XOR dummy\n    x_3 = modP(x_3 + dummy); // x_3 = x_3 XOR dummy\n    return { x_2, x_3 };\n  }\n\n  /**\n   * Montgomery x-only multiplication ladder.\n   * @param pointU u coordinate (x) on Montgomery Curve 25519\n   * @param scalar by which the point would be multiplied\n   * @returns new Point on Montgomery curve\n   */\n  function montgomeryLadder(u: bigint, scalar: bigint): bigint {\n    aInRange('u', u, _0n, P);\n    aInRange('scalar', scalar, minScalar, maxScalar);\n    const k = scalar;\n    const x_1 = u;\n    let x_2 = _1n;\n    let z_2 = _0n;\n    let x_3 = u;\n    let z_3 = _1n;\n    let swap = _0n;\n    for (let t = BigInt(montgomeryBits - 1); t >= _0n; t--) {\n      const k_t = (k >> t) & _1n;\n      swap ^= k_t;\n      ({ x_2, x_3 } = cswap(swap, x_2, x_3));\n      ({ x_2: z_2, x_3: z_3 } = cswap(swap, z_2, z_3));\n      swap = k_t;\n\n      const A = x_2 + z_2;\n      const AA = modP(A * A);\n      const B = x_2 - z_2;\n      const BB = modP(B * B);\n      const E = AA - BB;\n      const C = x_3 + z_3;\n      const D = x_3 - z_3;\n      const DA = modP(D * A);\n      const CB = modP(C * B);\n      const dacb = DA + CB;\n      const da_cb = DA - CB;\n      x_3 = modP(dacb * dacb);\n      z_3 = modP(x_1 * modP(da_cb * da_cb));\n      x_2 = modP(AA * BB);\n      z_2 = modP(E * (AA + modP(a24 * E)));\n    }\n    ({ x_2, x_3 } = cswap(swap, x_2, x_3));\n    ({ x_2: z_2, x_3: z_3 } = cswap(swap, z_2, z_3));\n    const z2 = powPminus2(z_2); // `Fp.pow(x, P - _2n)` is much slower equivalent\n    return modP(x_2 * z2); // Return x_2 * (z_2^(p - 2))\n  }\n  const lengths = {\n    secretKey: fieldLen,\n    publicKey: fieldLen,\n    seed: fieldLen,\n  };\n  const randomSecretKey = (seed = randomBytes_(fieldLen)) => {\n    abytes(seed, lengths.seed);\n    return seed;\n  };\n  function keygen(seed?: Uint8Array) {\n    const secretKey = randomSecretKey(seed);\n    return { secretKey, publicKey: scalarMultBase(secretKey) };\n  }\n  const utils = {\n    randomSecretKey,\n    randomPrivateKey: randomSecretKey,\n  };\n  return {\n    keygen,\n    getSharedSecret: (secretKey: Hex, publicKey: Hex) => scalarMult(secretKey, publicKey),\n    getPublicKey: (secretKey: Hex): Uint8Array => scalarMultBase(secretKey),\n    scalarMult,\n    scalarMultBase,\n    utils,\n    GuBytes: GuBytes.slice(),\n    lengths,\n  };\n}\n"], "mappings": "AAAA;;;;;;AAMA;AACA,SACEA,eAAe,EACfC,MAAM,EACNC,QAAQ,EACRC,eAAe,EACfC,WAAW,EACXC,eAAe,EACfC,WAAW,QACN,aAAa;AAEpB,SAASC,GAAG,QAAQ,cAAc;AAElC,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;AACrB,MAAME,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;AA2BrB,SAASG,YAAYA,CAACC,KAAgB;EACpCb,eAAe,CAACa,KAAK,EAAE;IACrBC,iBAAiB,EAAE,UAAU;IAC7BC,UAAU,EAAE;GACb,CAAC;EACF,OAAOC,MAAM,CAACC,MAAM,CAAC;IAAE,GAAGJ;EAAK,CAAW,CAAC;AAC7C;AAEA,OAAM,SAAUK,UAAUA,CAACC,QAAmB;EAC5C,MAAMC,KAAK,GAAGR,YAAY,CAACO,QAAQ,CAAC;EACpC,MAAM;IAAEE,CAAC;IAAEC,IAAI;IAAER,iBAAiB;IAAEC,UAAU;IAAET,WAAW,EAAEiB;EAAI,CAAE,GAAGH,KAAK;EAC3E,MAAMI,OAAO,GAAGF,IAAI,KAAK,QAAQ;EACjC,IAAI,CAACE,OAAO,IAAIF,IAAI,KAAK,MAAM,EAAE,MAAM,IAAIG,KAAK,CAAC,cAAc,CAAC;EAChE,MAAMC,YAAY,GAAGH,IAAI,IAAIjB,WAAW;EAExC,MAAMqB,cAAc,GAAGH,OAAO,GAAG,GAAG,GAAG,GAAG;EAC1C,MAAMI,QAAQ,GAAGJ,OAAO,GAAG,EAAE,GAAG,EAAE;EAClC,MAAMK,EAAE,GAAGL,OAAO,GAAGf,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EAC1C;EACA;EACA;EACA;EACA,MAAMqB,GAAG,GAAGN,OAAO,GAAGf,MAAM,CAAC,MAAM,CAAC,GAAGA,MAAM,CAAC,KAAK,CAAC;EACpD;EACA;EACA;EACA,MAAMsB,SAAS,GAAGP,OAAO,GAAGb,GAAG,IAAIF,MAAM,CAAC,GAAG,CAAC,GAAGE,GAAG,IAAIF,MAAM,CAAC,GAAG,CAAC;EACnE,MAAMuB,QAAQ,GAAGR,OAAO,GACpBf,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG,IAAIF,MAAM,CAAC,GAAG,CAAC,GAAGC,GAAG,GACpCD,MAAM,CAAC,CAAC,CAAC,GAAGE,GAAG,IAAIF,MAAM,CAAC,GAAG,CAAC,GAAGC,GAAG;EACxC,MAAMuB,SAAS,GAAGF,SAAS,GAAGC,QAAQ,GAAGtB,GAAG,CAAC,CAAC;EAC9C,MAAMwB,IAAI,GAAIC,CAAS,IAAK5B,GAAG,CAAC4B,CAAC,EAAEd,CAAC,CAAC;EACrC,MAAMe,OAAO,GAAGC,OAAO,CAACR,EAAE,CAAC;EAC3B,SAASQ,OAAOA,CAACC,CAAS;IACxB,OAAOjC,eAAe,CAAC6B,IAAI,CAACI,CAAC,CAAC,EAAEV,QAAQ,CAAC;EAC3C;EACA,SAASW,OAAOA,CAACD,CAAM;IACrB,MAAME,EAAE,GAAGpC,WAAW,CAAC,cAAc,EAAEkC,CAAC,EAAEV,QAAQ,CAAC;IACnD;IACA;IACA,IAAIJ,OAAO,EAAEgB,EAAE,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;IAC5B;IACA;IACA;IACA;IACA,OAAON,IAAI,CAAC/B,eAAe,CAACqC,EAAE,CAAC,CAAC;EAClC;EACA,SAASC,YAAYA,CAACC,MAAW;IAC/B,OAAOvC,eAAe,CAACW,iBAAiB,CAACV,WAAW,CAAC,QAAQ,EAAEsC,MAAM,EAAEd,QAAQ,CAAC,CAAC,CAAC;EACpF;EACA,SAASe,UAAUA,CAACD,MAAW,EAAEJ,CAAM;IACrC,MAAMM,EAAE,GAAGC,gBAAgB,CAACN,OAAO,CAACD,CAAC,CAAC,EAAEG,YAAY,CAACC,MAAM,CAAC,CAAC;IAC7D;IACA;IACA;IACA,IAAIE,EAAE,KAAKpC,GAAG,EAAE,MAAM,IAAIiB,KAAK,CAAC,wCAAwC,CAAC;IACzE,OAAOY,OAAO,CAACO,EAAE,CAAC;EACpB;EACA;EACA,SAASE,cAAcA,CAACJ,MAAW;IACjC,OAAOC,UAAU,CAACD,MAAM,EAAEN,OAAO,CAAC;EACpC;EAEA;EACA,SAASW,KAAKA,CAACC,IAAY,EAAEC,GAAW,EAAEC,GAAW;IACnD;IACA;IACA;IACA,MAAMC,KAAK,GAAGjB,IAAI,CAACc,IAAI,IAAIC,GAAG,GAAGC,GAAG,CAAC,CAAC;IACtCD,GAAG,GAAGf,IAAI,CAACe,GAAG,GAAGE,KAAK,CAAC,CAAC,CAAC;IACzBD,GAAG,GAAGhB,IAAI,CAACgB,GAAG,GAAGC,KAAK,CAAC,CAAC,CAAC;IACzB,OAAO;MAAEF,GAAG;MAAEC;IAAG,CAAE;EACrB;EAEA;;;;;;EAMA,SAASL,gBAAgBA,CAACP,CAAS,EAAEI,MAAc;IACjDxC,QAAQ,CAAC,GAAG,EAAEoC,CAAC,EAAE9B,GAAG,EAAEa,CAAC,CAAC;IACxBnB,QAAQ,CAAC,QAAQ,EAAEwC,MAAM,EAAEX,SAAS,EAAEE,SAAS,CAAC;IAChD,MAAMmB,CAAC,GAAGV,MAAM;IAChB,MAAMW,GAAG,GAAGf,CAAC;IACb,IAAIW,GAAG,GAAGvC,GAAG;IACb,IAAI4C,GAAG,GAAG9C,GAAG;IACb,IAAI0C,GAAG,GAAGZ,CAAC;IACX,IAAIiB,GAAG,GAAG7C,GAAG;IACb,IAAIsC,IAAI,GAAGxC,GAAG;IACd,KAAK,IAAIgD,CAAC,GAAG/C,MAAM,CAACkB,cAAc,GAAG,CAAC,CAAC,EAAE6B,CAAC,IAAIhD,GAAG,EAAEgD,CAAC,EAAE,EAAE;MACtD,MAAMC,GAAG,GAAIL,CAAC,IAAII,CAAC,GAAI9C,GAAG;MAC1BsC,IAAI,IAAIS,GAAG;MACX,CAAC;QAAER,GAAG;QAAEC;MAAG,CAAE,GAAGH,KAAK,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;MACrC,CAAC;QAAED,GAAG,EAAEK,GAAG;QAAEJ,GAAG,EAAEK;MAAG,CAAE,GAAGR,KAAK,CAACC,IAAI,EAAEM,GAAG,EAAEC,GAAG,CAAC;MAC/CP,IAAI,GAAGS,GAAG;MAEV,MAAMC,CAAC,GAAGT,GAAG,GAAGK,GAAG;MACnB,MAAMK,EAAE,GAAGzB,IAAI,CAACwB,CAAC,GAAGA,CAAC,CAAC;MACtB,MAAME,CAAC,GAAGX,GAAG,GAAGK,GAAG;MACnB,MAAMO,EAAE,GAAG3B,IAAI,CAAC0B,CAAC,GAAGA,CAAC,CAAC;MACtB,MAAME,CAAC,GAAGH,EAAE,GAAGE,EAAE;MACjB,MAAME,CAAC,GAAGb,GAAG,GAAGK,GAAG;MACnB,MAAMS,CAAC,GAAGd,GAAG,GAAGK,GAAG;MACnB,MAAMU,EAAE,GAAG/B,IAAI,CAAC8B,CAAC,GAAGN,CAAC,CAAC;MACtB,MAAMQ,EAAE,GAAGhC,IAAI,CAAC6B,CAAC,GAAGH,CAAC,CAAC;MACtB,MAAMO,IAAI,GAAGF,EAAE,GAAGC,EAAE;MACpB,MAAME,KAAK,GAAGH,EAAE,GAAGC,EAAE;MACrBhB,GAAG,GAAGhB,IAAI,CAACiC,IAAI,GAAGA,IAAI,CAAC;MACvBZ,GAAG,GAAGrB,IAAI,CAACmB,GAAG,GAAGnB,IAAI,CAACkC,KAAK,GAAGA,KAAK,CAAC,CAAC;MACrCnB,GAAG,GAAGf,IAAI,CAACyB,EAAE,GAAGE,EAAE,CAAC;MACnBP,GAAG,GAAGpB,IAAI,CAAC4B,CAAC,IAAIH,EAAE,GAAGzB,IAAI,CAACJ,GAAG,GAAGgC,CAAC,CAAC,CAAC,CAAC;IACtC;IACA,CAAC;MAAEb,GAAG;MAAEC;IAAG,CAAE,GAAGH,KAAK,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;IACrC,CAAC;MAAED,GAAG,EAAEK,GAAG;MAAEJ,GAAG,EAAEK;IAAG,CAAE,GAAGR,KAAK,CAACC,IAAI,EAAEM,GAAG,EAAEC,GAAG,CAAC;IAC/C,MAAMc,EAAE,GAAGtD,UAAU,CAACuC,GAAG,CAAC,CAAC,CAAC;IAC5B,OAAOpB,IAAI,CAACe,GAAG,GAAGoB,EAAE,CAAC,CAAC,CAAC;EACzB;EACA,MAAMC,OAAO,GAAG;IACdC,SAAS,EAAE3C,QAAQ;IACnB4C,SAAS,EAAE5C,QAAQ;IACnB6C,IAAI,EAAE7C;GACP;EACD,MAAM8C,eAAe,GAAGA,CAACD,IAAI,GAAG/C,YAAY,CAACE,QAAQ,CAAC,KAAI;IACxD3B,MAAM,CAACwE,IAAI,EAAEH,OAAO,CAACG,IAAI,CAAC;IAC1B,OAAOA,IAAI;EACb,CAAC;EACD,SAASE,MAAMA,CAACF,IAAiB;IAC/B,MAAMF,SAAS,GAAGG,eAAe,CAACD,IAAI,CAAC;IACvC,OAAO;MAAEF,SAAS;MAAEC,SAAS,EAAE1B,cAAc,CAACyB,SAAS;IAAC,CAAE;EAC5D;EACA,MAAMK,KAAK,GAAG;IACZF,eAAe;IACfG,gBAAgB,EAAEH;GACnB;EACD,OAAO;IACLC,MAAM;IACNG,eAAe,EAAEA,CAACP,SAAc,EAAEC,SAAc,KAAK7B,UAAU,CAAC4B,SAAS,EAAEC,SAAS,CAAC;IACrFO,YAAY,EAAGR,SAAc,IAAiBzB,cAAc,CAACyB,SAAS,CAAC;IACvE5B,UAAU;IACVG,cAAc;IACd8B,KAAK;IACLxC,OAAO,EAAEA,OAAO,CAAC4C,KAAK,EAAE;IACxBV;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}