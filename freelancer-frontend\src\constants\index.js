// Smart Contract Constants
export const CONTRACT_ADDRESS = '0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22';

// Network Configuration
export const NETWORK_CONFIG = {
  DEVNET: 'devnet',
  TESTNET: 'testnet',
  MAINNET: 'mainnet'
};

// Escrow Status Constants
export const ESCROW_STATUS = {
  CREATED: 0,
  FUNDED: 1,
  IN_PROGRESS: 2,
  SUBMITTED: 3,
  COMPLETED: 4,
  DISPUTED: 5,
  CANCELLED: 6,
  REFUNDED: 7
};

export const STATUS_LABELS = {
  [ESCROW_STATUS.CREATED]: 'Created',
  [ESCROW_STATUS.FUNDED]: 'Funded',
  [ESCROW_STATUS.IN_PROGRESS]: 'In Progress',
  [ESCROW_STATUS.SUBMITTED]: 'Submitted',
  [ESCROW_STATUS.COMPLETED]: 'Completed',
  [ESCROW_STATUS.DISPUTED]: 'Disputed',
  [ESCROW_STATUS.CANCELLED]: 'Cancelled',
  [ESCROW_STATUS.REFUNDED]: 'Refunded'
};

export const STATUS_COLORS = {
  [ESCROW_STATUS.CREATED]: 'secondary',
  [ESCROW_STATUS.FUNDED]: 'info',
  [ESCROW_STATUS.IN_PROGRESS]: 'warning',
  [ESCROW_STATUS.SUBMITTED]: 'primary',
  [ESCROW_STATUS.COMPLETED]: 'success',
  [ESCROW_STATUS.DISPUTED]: 'danger',
  [ESCROW_STATUS.CANCELLED]: 'secondary',
  [ESCROW_STATUS.REFUNDED]: 'warning'
};

// Platform Constants
export const PLATFORM_CONFIG = {
  FEE_BPS: 250, // 2.5%
  BASIS_POINTS: 10000,
  MIN_AMOUNT: 0.01, // APT
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 1000,
  DISPUTE_PERIOD: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
  REVIEW_PERIOD: 3 * 24 * 60 * 60 * 1000 // 3 days in milliseconds
};

// Categories
export const PROJECT_CATEGORIES = [
  'Web Development',
  'Mobile Development',
  'UI/UX Design',
  'Blockchain Development',
  'Smart Contract Development',
  'Content Writing',
  'Digital Marketing',
  'Data Science',
  'Machine Learning',
  'DevOps',
  'Quality Assurance',
  'Project Management',
  'Consulting',
  'Other'
];

// API Endpoints
export const API_ENDPOINTS = {
  APTOS_DEVNET: 'https://fullnode.devnet.aptoslabs.com',
  APTOS_TESTNET: 'https://fullnode.testnet.aptoslabs.com',
  APTOS_MAINNET: 'https://fullnode.mainnet.aptoslabs.com',
  FAUCET_DEVNET: 'https://faucet.devnet.aptoslabs.com',
  EXPLORER_DEVNET: 'https://explorer.aptoslabs.com',
  EXPLORER_TESTNET: 'https://explorer.aptoslabs.com',
  EXPLORER_MAINNET: 'https://explorer.aptoslabs.com'
};

// Error Messages
export const ERROR_MESSAGES = {
  WALLET_NOT_CONNECTED: 'Please connect your wallet to continue',
  INSUFFICIENT_BALANCE: 'Insufficient balance for this transaction',
  TRANSACTION_FAILED: 'Transaction failed. Please try again.',
  INVALID_ADDRESS: 'Invalid wallet address format',
  INVALID_AMOUNT: 'Please enter a valid amount',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  CONTRACT_ERROR: 'Smart contract error. Please try again later.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  ESCROW_NOT_FOUND: 'Escrow contract not found',
  INVALID_STATUS: 'Invalid escrow status for this operation'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  ESCROW_CREATED: 'Project created successfully!',
  ESCROW_FUNDED: 'Project funded successfully!',
  WORK_STARTED: 'Work started successfully!',
  WORK_SUBMITTED: 'Work submitted for review!',
  WORK_APPROVED: 'Work approved and payment released!',
  WALLET_CONNECTED: 'Wallet connected successfully!',
  WALLET_DISCONNECTED: 'Wallet disconnected successfully!'
};

// Local Storage Keys
export const STORAGE_KEYS = {
  WALLET_PREFERENCE: 'freelancechain_wallet_preference',
  USER_SETTINGS: 'freelancechain_user_settings',
  THEME_PREFERENCE: 'freelancechain_theme',
  RECENT_TRANSACTIONS: 'freelancechain_recent_transactions'
};

// Animation Durations
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000
};

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
};

// Theme Colors
export const THEME_COLORS = {
  PRIMARY: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'
  },
  SECONDARY: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a'
  }
};
