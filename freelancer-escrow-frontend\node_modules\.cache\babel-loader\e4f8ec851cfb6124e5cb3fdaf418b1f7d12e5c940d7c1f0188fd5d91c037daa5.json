{"ast": null, "code": "import { a as n } from \"./chunk-JPDT6E3B.mjs\";\nimport { b as a } from \"./chunk-BF46IXHH.mjs\";\nimport { a as i } from \"./chunk-A63SMUOU.mjs\";\nvar o = class t extends i {\n  constructor(e, r) {\n    super(), this.rawTransaction = e, this.feePayerAddress = r;\n  }\n  serialize(e) {\n    this.rawTransaction.serialize(e), this.feePayerAddress === void 0 ? e.serializeBool(!1) : (e.serializeBool(!0), this.feePayerAddress.serialize(e));\n  }\n  static deserialize(e) {\n    let r = n.deserialize(e),\n      d = e.deserializeBool(),\n      s;\n    return d && (s = a.deserialize(e)), new t(r, s);\n  }\n};\nexport { o as a };", "map": {"version": 3, "names": ["o", "t", "i", "constructor", "e", "r", "rawTransaction", "feePayer<PERSON>dd<PERSON>", "serialize", "serializeBool", "deserialize", "n", "d", "deserializeBool", "s", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\simpleTransaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { AccountAddress } from \"../../core\";\nimport { RawTransaction } from \"./rawTransaction\";\n\n/**\n * Representation of a SimpleTransaction that can serialized and deserialized\n */\nexport class SimpleTransaction extends Serializable {\n  public rawTransaction: RawTransaction;\n\n  public feePayerAddress?: AccountAddress | undefined;\n\n  // We dont really need it, we add it for type checkings we do\n  // throughout the SDK\n  public readonly secondarySignerAddresses: undefined;\n\n  /**\n   * SimpleTransaction represents a simple transaction type of a single signer that\n   * can be submitted to Aptos chain for execution.\n   *\n   * SimpleTransaction metadata contains the Raw Transaction and an optional\n   * sponsor Account Address to pay the gas fees.\n   *\n   * @param rawTransaction The Raw Tranasaction\n   * @param feePayerAddress The sponsor Account Address\n   */\n  constructor(rawTransaction: RawTransaction, feePayerAddress?: AccountAddress) {\n    super();\n    this.rawTransaction = rawTransaction;\n    this.feePayerAddress = feePayerAddress;\n  }\n\n  serialize(serializer: Serializer): void {\n    this.rawTransaction.serialize(serializer);\n\n    if (this.feePayerAddress === undefined) {\n      serializer.serializeBool(false);\n    } else {\n      serializer.serializeBool(true);\n      this.feePayerAddress.serialize(serializer);\n    }\n  }\n\n  static deserialize(deserializer: Deserializer): SimpleTransaction {\n    const rawTransaction = RawTransaction.deserialize(deserializer);\n    const feepayerPresent = deserializer.deserializeBool();\n    let feePayerAddress;\n    if (feepayerPresent) {\n      feePayerAddress = AccountAddress.deserialize(deserializer);\n    }\n\n    return new SimpleTransaction(rawTransaction, feePayerAddress);\n  }\n}\n"], "mappings": ";;;AAaO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAA0BC,CAAa;EAmBlDC,YAAYC,CAAA,EAAgCC,CAAA,EAAkC;IAC5E,MAAM,GACN,KAAKC,cAAA,GAAiBF,CAAA,EACtB,KAAKG,eAAA,GAAkBF,CACzB;EAAA;EAEAG,UAAUJ,CAAA,EAA8B;IACtC,KAAKE,cAAA,CAAeE,SAAA,CAAUJ,CAAU,GAEpC,KAAKG,eAAA,KAAoB,SAC3BH,CAAA,CAAWK,aAAA,CAAc,EAAK,KAE9BL,CAAA,CAAWK,aAAA,CAAc,EAAI,GAC7B,KAAKF,eAAA,CAAgBC,SAAA,CAAUJ,CAAU,EAE7C;EAAA;EAEA,OAAOM,YAAYN,CAAA,EAA+C;IAChE,IAAMC,CAAA,GAAiBM,CAAA,CAAeD,WAAA,CAAYN,CAAY;MACxDQ,CAAA,GAAkBR,CAAA,CAAaS,eAAA,CAAgB;MACjDC,CAAA;IACJ,OAAIF,CAAA,KACFE,CAAA,GAAkBC,CAAA,CAAeL,WAAA,CAAYN,CAAY,IAGpD,IAAIH,CAAA,CAAkBI,CAAA,EAAgBS,CAAe,CAC9D;EAAA;AACF;AAAA,SAAAd,CAAA,IAAAe,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}