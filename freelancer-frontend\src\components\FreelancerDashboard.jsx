import React, { useState } from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import { useEscrow } from '../contexts/EscrowContext';
import { Briefcase, Clock, DollarSign, CheckCircle, AlertCircle, TrendingUp } from 'lucide-react';
import aptosService from '../services/aptosService';
import EscrowCard from './EscrowCard';

const FreelancerDashboard = () => {
  const { account } = useWallet();
  const { escrows, loading, balance, startWork, submitWork } = useEscrow();
  const [selectedEscrow, setSelectedEscrow] = useState(null);

  // Filter escrows where user is the freelancer
  const freelancerEscrows = escrows.filter(escrow => 
    escrow.freelancer === account?.address || escrow.freelancer.includes('4321') // Mock data filter
  );

  const stats = {
    total: freelancerEscrows.length,
    active: freelancerEscrows.filter(e => 
      [aptosService.ESCROW_STATUS.FUNDED, aptosService.ESCROW_STATUS.IN_PROGRESS].includes(e.status)
    ).length,
    completed: freelancerEscrows.filter(e => e.status === aptosService.ESCROW_STATUS.COMPLETED).length,
    totalEarned: freelancerEscrows
      .filter(e => e.status === aptosService.ESCROW_STATUS.COMPLETED)
      .reduce((sum, e) => sum + e.amount, 0),
    pendingReview: freelancerEscrows.filter(e => e.status === aptosService.ESCROW_STATUS.SUBMITTED).length
  };

  const handleStartWork = async (escrowId) => {
    try {
      await startWork(escrowId);
      alert('Work started successfully!');
    } catch (error) {
      alert('Failed to start work: ' + error.message);
    }
  };

  const handleSubmitWork = async (escrowId) => {
    try {
      await submitWork(escrowId);
      alert('Work submitted for review!');
    } catch (error) {
      alert('Failed to submit work: ' + error.message);
    }
  };

  if (!account) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-secondary-900 mb-2">
            Connect Your Wallet
          </h3>
          <p className="text-secondary-600">
            Please connect your wallet to access the freelancer dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Freelancer Dashboard</h1>
          <p className="text-secondary-600">Manage your projects and earnings</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-secondary-600">Balance</p>
          <p className="text-lg font-semibold text-secondary-900">{balance} APT</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Projects</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.total}</p>
            </div>
            <Briefcase className="w-8 h-8 text-primary-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Active Projects</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.active}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Completed</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.completed}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Earned</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.totalEarned.toFixed(2)} APT</p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Pending Review</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.pendingReview}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card p-6">
        <h2 className="text-lg font-semibold text-secondary-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-primary-50 rounded-lg">
            <Clock className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <h3 className="font-medium text-secondary-900">Projects to Start</h3>
            <p className="text-2xl font-bold text-primary-600">
              {freelancerEscrows.filter(e => e.status === aptosService.ESCROW_STATUS.FUNDED).length}
            </p>
          </div>
          
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <TrendingUp className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
            <h3 className="font-medium text-secondary-900">In Progress</h3>
            <p className="text-2xl font-bold text-yellow-600">
              {freelancerEscrows.filter(e => e.status === aptosService.ESCROW_STATUS.IN_PROGRESS).length}
            </p>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <CheckCircle className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <h3 className="font-medium text-secondary-900">Awaiting Approval</h3>
            <p className="text-2xl font-bold text-purple-600">
              {stats.pendingReview}
            </p>
          </div>
        </div>
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-secondary-900">Your Projects</h2>
        
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : freelancerEscrows.length === 0 ? (
          <div className="text-center py-8">
            <Briefcase className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
            <p className="text-secondary-600">No projects assigned yet.</p>
            <p className="text-secondary-500 text-sm">Projects will appear here when clients create them for you.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Active Projects */}
            {freelancerEscrows.filter(e => 
              [aptosService.ESCROW_STATUS.FUNDED, aptosService.ESCROW_STATUS.IN_PROGRESS, aptosService.ESCROW_STATUS.SUBMITTED].includes(e.status)
            ).length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-secondary-900 mb-3">Active Projects</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {freelancerEscrows
                    .filter(e => [aptosService.ESCROW_STATUS.FUNDED, aptosService.ESCROW_STATUS.IN_PROGRESS, aptosService.ESCROW_STATUS.SUBMITTED].includes(e.status))
                    .map((escrow) => (
                      <EscrowCard
                        key={escrow.id}
                        escrow={escrow}
                        userRole="freelancer"
                        onStart={() => handleStartWork(escrow.id)}
                        onSubmit={() => handleSubmitWork(escrow.id)}
                        onViewDetails={() => setSelectedEscrow(escrow)}
                      />
                    ))}
                </div>
              </div>
            )}

            {/* Completed Projects */}
            {freelancerEscrows.filter(e => e.status === aptosService.ESCROW_STATUS.COMPLETED).length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-secondary-900 mb-3">Completed Projects</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {freelancerEscrows
                    .filter(e => e.status === aptosService.ESCROW_STATUS.COMPLETED)
                    .map((escrow) => (
                      <EscrowCard
                        key={escrow.id}
                        escrow={escrow}
                        userRole="freelancer"
                        onViewDetails={() => setSelectedEscrow(escrow)}
                      />
                    ))}
                </div>
              </div>
            )}

            {/* Other Projects */}
            {freelancerEscrows.filter(e => 
              ![aptosService.ESCROW_STATUS.FUNDED, aptosService.ESCROW_STATUS.IN_PROGRESS, aptosService.ESCROW_STATUS.SUBMITTED, aptosService.ESCROW_STATUS.COMPLETED].includes(e.status)
            ).length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-secondary-900 mb-3">Other Projects</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {freelancerEscrows
                    .filter(e => ![aptosService.ESCROW_STATUS.FUNDED, aptosService.ESCROW_STATUS.IN_PROGRESS, aptosService.ESCROW_STATUS.SUBMITTED, aptosService.ESCROW_STATUS.COMPLETED].includes(e.status))
                    .map((escrow) => (
                      <EscrowCard
                        key={escrow.id}
                        escrow={escrow}
                        userRole="freelancer"
                        onViewDetails={() => setSelectedEscrow(escrow)}
                      />
                    ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FreelancerDashboard;
