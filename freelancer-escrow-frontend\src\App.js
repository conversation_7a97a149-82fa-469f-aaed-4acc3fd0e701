import React, { useState } from 'react';
import { AptosWalletAdapterProvider } from '@aptos-labs/wallet-adapter-react';
import { Network } from '@aptos-labs/ts-sdk';
import FreelancerSection from './components/FreelancerSection';
import EscrowSection from './components/EscrowSection';
import BlockchainSection from './components/BlockchainSection';
import WalletConnection from './components/WalletConnection';
import './App.css';

// Wallet configuration
const wallets = [];

function App() {
  const [activeSection, setActiveSection] = useState('freelancer');

  return (
    <AptosWalletAdapterProvider
      plugins={wallets}
      autoConnect={true}
      dappConfig={{
        network: Network.DEVNET,
        aptosConnectDappId: 'freelancer-platform'
      }}
      onError={(error) => {
        console.error('Wallet connection error:', error);
      }}
    >
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header */}
        <header className="bg-white shadow-lg border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <h1 className="text-3xl font-bold text-gray-900">
                  FreelanceChain
                </h1>
                <span className="ml-3 px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                  Live on Aptos
                </span>
              </div>
              <WalletConnection />
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-5xl font-bold mb-6">
              The Future of Decentralized Freelancing
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Three powerful components working together: <strong>Freelancers</strong> provide expertise, 
              <strong> Smart Escrow</strong> ensures security, and <strong>Blockchain</strong> guarantees transparency
            </p>
            <div className="flex justify-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span>Secure</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                <span>Fast</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Transparent</span>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav className="flex space-x-8">
              {[
                { id: 'freelancer', label: 'Freelancer', icon: '👨‍💻' },
                { id: 'escrow', label: 'Smart Escrow (Middleman)', icon: '🛡️' },
                { id: 'blockchain', label: 'Blockchain', icon: '⛓️' }
              ].map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`flex items-center space-x-2 py-4 px-6 border-b-2 font-medium text-sm transition-colors ${
                    activeSection === section.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="text-lg">{section.icon}</span>
                  <span>{section.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {activeSection === 'freelancer' && <FreelancerSection />}
          {activeSection === 'escrow' && <EscrowSection />}
          {activeSection === 'blockchain' && <BlockchainSection />}
        </main>

        {/* Footer */}
        <footer className="bg-gray-800 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">FreelanceChain</h3>
                <p className="text-gray-300">
                  The first decentralized freelancing platform built on Aptos blockchain,
                  connecting talent with opportunities through secure smart contracts.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Platform Stats</h3>
                <div className="space-y-2 text-gray-300">
                  <p>🎯 2,500+ Active Freelancers</p>
                  <p>💼 15,000+ Projects Completed</p>
                  <p>💰 $2.5M+ Secured in Escrow</p>
                  <p>⚡ 99.9% Uptime</p>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Blockchain Info</h3>
                <div className="space-y-2 text-gray-300">
                  <p>🌐 Network: Aptos Devnet</p>
                  <p>⚡ TPS: 160,000+</p>
                  <p>⏱️ Finality: &lt;1 second</p>
                  <p>💸 Gas Cost: $0.0001</p>
                </div>
              </div>
            </div>
            <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; 2024 FreelanceChain. Built on Aptos Blockchain.</p>
            </div>
          </div>
        </footer>
      </div>
    </AptosWalletAdapterProvider>
  );
}

export default App;
