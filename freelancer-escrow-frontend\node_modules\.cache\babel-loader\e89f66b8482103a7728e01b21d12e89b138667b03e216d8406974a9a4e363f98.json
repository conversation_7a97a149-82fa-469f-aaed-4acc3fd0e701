{"ast": null, "code": "var n = \"aptos:signAndSubmitTransaction\";\nexport { n as a };", "map": {"version": 3, "names": ["n", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosSignAndSubmitTransaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AnyRawTransaction, PendingTransactionResponse } from '@aptos-labs/ts-sdk'\nimport { UserResponse } from '../misc'\n/** Version of the feature. */\nexport type AptosSignAndSubmitTransactionVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosSignAndSubmitTransactionNamespace = 'aptos:signAndSubmitTransaction'\n/**\n * A Wallet Standard feature for signing a transaction, and returning the\n * hash of the transaction.\n */\nexport type AptosSignAndSubmitTransactionFeature = {\n  /** Namespace for the feature. */\n  [AptosSignAndSubmitTransactionNamespace]: {\n    /** Version of the feature API. */\n    version: AptosSignAndSubmitTransactionVersion\n    signAndSubmitTransaction: AptosSignAndSubmitTransactionMethod\n  }\n}\n/** TODO: docs */\nexport type AptosSignAndSubmitTransactionMethod = (\n  transaction: AptosSignAndSubmitTransactionInput\n) => Promise<UserResponse<AptosSignAndSubmitTransactionOutput>>\n\n/** TODO: docs */\nexport type AptosSignAndSubmitTransactionInput = AnyRawTransaction\n/** Output of signing transactions. */\nexport type AptosSignAndSubmitTransactionOutput = PendingTransactionResponse\n"], "mappings": "AAQO,IAAMA,CAAA,GAAyC;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}