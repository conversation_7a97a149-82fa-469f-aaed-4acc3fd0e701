{"ast": null, "code": "/**\n * Short <PERSON> curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac as nobleHmac } from '@noble/hashes/hmac.js';\nimport { ahash } from '@noble/hashes/utils';\nimport { _validateObject, _abool2 as abool, _abytes2 as abytes, aInRange, bitLen, bitMask, bytesToHex, bytesToNumberBE, concatBytes, createHmacDrbg, ensureBytes, hexToBytes, inRange, isBytes, memoized, numberToHexUnpadded, randomBytes as randomBytesWeb } from \"../utils.js\";\nimport { _createCurveFields, mulEndoUnsafe, negateCt, normalizeZ, pippenger, wNAF } from \"./curve.js\";\nimport { Field, FpInvertBatch, getMinHashLength, mapHashToField, nLength, validateField } from \"./modular.js\";\n// We construct basis in such way that den is always positive and equals n, but num sign depends on basis (not on secret value)\nconst divNearest = (num, den) => (num + (num >= 0 ? den : -den) / _2n) / den;\n/**\n * Splits scalar for GLV endomorphism.\n */\nexport function _splitEndoScalar(k, basis, n) {\n  // Split scalar into two such that part is ~half bits: `abs(part) < sqrt(N)`\n  // Since part can be negative, we need to do this on point.\n  // TODO: verifyScalar function which consumes lambda\n  const [[a1, b1], [a2, b2]] = basis;\n  const c1 = divNearest(b2 * k, n);\n  const c2 = divNearest(-b1 * k, n);\n  // |k1|/|k2| is < sqrt(N), but can be negative.\n  // If we do `k1 mod N`, we'll get big scalar (`> sqrt(N)`): so, we do cheaper negation instead.\n  let k1 = k - c1 * a1 - c2 * a2;\n  let k2 = -c1 * b1 - c2 * b2;\n  const k1neg = k1 < _0n;\n  const k2neg = k2 < _0n;\n  if (k1neg) k1 = -k1;\n  if (k2neg) k2 = -k2;\n  // Double check that resulting scalar less than half bits of N: otherwise wNAF will fail.\n  // This should only happen on wrong basises. Also, math inside is too complex and I don't trust it.\n  const MAX_NUM = bitMask(Math.ceil(bitLen(n) / 2)) + _1n; // Half bits of N\n  if (k1 < _0n || k1 >= MAX_NUM || k2 < _0n || k2 >= MAX_NUM) {\n    throw new Error('splitScalar (endomorphism): failed, k=' + k);\n  }\n  return {\n    k1neg,\n    k1,\n    k2neg,\n    k2\n  };\n}\nfunction validateSigFormat(format) {\n  if (!['compact', 'recovered', 'der'].includes(format)) throw new Error('Signature format must be \"compact\", \"recovered\", or \"der\"');\n  return format;\n}\nfunction validateSigOpts(opts, def) {\n  const optsn = {};\n  for (let optName of Object.keys(def)) {\n    // @ts-ignore\n    optsn[optName] = opts[optName] === undefined ? def[optName] : opts[optName];\n  }\n  abool(optsn.lowS, 'lowS');\n  abool(optsn.prehash, 'prehash');\n  if (optsn.format !== undefined) validateSigFormat(optsn.format);\n  return optsn;\n}\nexport class DERErr extends Error {\n  constructor(m = '') {\n    super(m);\n  }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nexport const DER = {\n  // asn.1 DER encoding utils\n  Err: DERErr,\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag, data) => {\n      const {\n        Err: E\n      } = DER;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length & 1) throw new E('tlv.encode: unpadded data');\n      const dataLen = data.length / 2;\n      const len = numberToHexUnpadded(dataLen);\n      if (len.length / 2 & 128) throw new E('tlv.encode: long form length too big');\n      // length of length with long form flag\n      const lenLen = dataLen > 127 ? numberToHexUnpadded(len.length / 2 | 128) : '';\n      const t = numberToHexUnpadded(tag);\n      return t + lenLen + len + data;\n    },\n    // v - value, l - left bytes (unparsed)\n    decode(tag, data) {\n      const {\n        Err: E\n      } = DER;\n      let pos = 0;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length < 2 || data[pos++] !== tag) throw new E('tlv.decode: wrong tlv');\n      const first = data[pos++];\n      const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n      let length = 0;\n      if (!isLong) length = first;else {\n        // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n        const lenLen = first & 127;\n        if (!lenLen) throw new E('tlv.decode(long): indefinite length not supported');\n        if (lenLen > 4) throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n        const lengthBytes = data.subarray(pos, pos + lenLen);\n        if (lengthBytes.length !== lenLen) throw new E('tlv.decode: length bytes not complete');\n        if (lengthBytes[0] === 0) throw new E('tlv.decode(long): zero leftmost byte');\n        for (const b of lengthBytes) length = length << 8 | b;\n        pos += lenLen;\n        if (length < 128) throw new E('tlv.decode(long): not minimal encoding');\n      }\n      const v = data.subarray(pos, pos + length);\n      if (v.length !== length) throw new E('tlv.decode: wrong value length');\n      return {\n        v,\n        l: data.subarray(pos + length)\n      };\n    }\n  },\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num) {\n      const {\n        Err: E\n      } = DER;\n      if (num < _0n) throw new E('integer: negative integers are not allowed');\n      let hex = numberToHexUnpadded(num);\n      // Pad with zero byte if negative flag is present\n      if (Number.parseInt(hex[0], 16) & 0b1000) hex = '00' + hex;\n      if (hex.length & 1) throw new E('unexpected DER parsing assertion: unpadded hex');\n      return hex;\n    },\n    decode(data) {\n      const {\n        Err: E\n      } = DER;\n      if (data[0] & 128) throw new E('invalid signature integer: negative');\n      if (data[0] === 0x00 && !(data[1] & 128)) throw new E('invalid signature integer: unnecessary leading zero');\n      return bytesToNumberBE(data);\n    }\n  },\n  toSig(hex) {\n    // parse DER signature\n    const {\n      Err: E,\n      _int: int,\n      _tlv: tlv\n    } = DER;\n    const data = ensureBytes('signature', hex);\n    const {\n      v: seqBytes,\n      l: seqLeftBytes\n    } = tlv.decode(0x30, data);\n    if (seqLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    const {\n      v: rBytes,\n      l: rLeftBytes\n    } = tlv.decode(0x02, seqBytes);\n    const {\n      v: sBytes,\n      l: sLeftBytes\n    } = tlv.decode(0x02, rLeftBytes);\n    if (sLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    return {\n      r: int.decode(rBytes),\n      s: int.decode(sBytes)\n    };\n  },\n  hexFromSig(sig) {\n    const {\n      _tlv: tlv,\n      _int: int\n    } = DER;\n    const rs = tlv.encode(0x02, int.encode(sig.r));\n    const ss = tlv.encode(0x02, int.encode(sig.s));\n    const seq = rs + ss;\n    return tlv.encode(0x30, seq);\n  }\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0),\n  _1n = BigInt(1),\n  _2n = BigInt(2),\n  _3n = BigInt(3),\n  _4n = BigInt(4);\nexport function _normFnElement(Fn, key) {\n  const {\n    BYTES: expected\n  } = Fn;\n  let num;\n  if (typeof key === 'bigint') {\n    num = key;\n  } else {\n    let bytes = ensureBytes('private key', key);\n    try {\n      num = Fn.fromBytes(bytes);\n    } catch (error) {\n      throw new Error(`invalid private key: expected ui8a of size ${expected}, got ${typeof key}`);\n    }\n  }\n  if (!Fn.isValidNot0(num)) throw new Error('invalid private key: out of range [1..N-1]');\n  return num;\n}\n/**\n * Creates weierstrass Point constructor, based on specified curve options.\n *\n * @example\n```js\nconst opts = {\n  p: BigInt('0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff'),\n  n: BigInt('0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551'),\n  h: BigInt(1),\n  a: BigInt('0xffffffff00000001000000000000000000000000fffffffffffffffffffffffc'),\n  b: BigInt('0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b'),\n  Gx: BigInt('0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296'),\n  Gy: BigInt('0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5'),\n};\nconst p256_Point = weierstrass(opts);\n```\n */\nexport function weierstrassN(params, extraOpts = {}) {\n  const validated = _createCurveFields('weierstrass', params, extraOpts);\n  const {\n    Fp,\n    Fn\n  } = validated;\n  let CURVE = validated.CURVE;\n  const {\n    h: cofactor,\n    n: CURVE_ORDER\n  } = CURVE;\n  _validateObject(extraOpts, {}, {\n    allowInfinityPoint: 'boolean',\n    clearCofactor: 'function',\n    isTorsionFree: 'function',\n    fromBytes: 'function',\n    toBytes: 'function',\n    endo: 'object',\n    wrapPrivateKey: 'boolean'\n  });\n  const {\n    endo\n  } = extraOpts;\n  if (endo) {\n    // validateObject(endo, { beta: 'bigint', splitScalar: 'function' });\n    if (!Fp.is0(CURVE.a) || typeof endo.beta !== 'bigint' || !Array.isArray(endo.basises)) {\n      throw new Error('invalid endo: expected \"beta\": bigint and \"basises\": array');\n    }\n  }\n  const lengths = getWLengths(Fp, Fn);\n  function assertCompressionIsSupported() {\n    if (!Fp.isOdd) throw new Error('compression is not supported: Field does not have .isOdd()');\n  }\n  // Implements IEEE P1363 point encoding\n  function pointToBytes(_c, point, isCompressed) {\n    const {\n      x,\n      y\n    } = point.toAffine();\n    const bx = Fp.toBytes(x);\n    abool(isCompressed, 'isCompressed');\n    if (isCompressed) {\n      assertCompressionIsSupported();\n      const hasEvenY = !Fp.isOdd(y);\n      return concatBytes(pprefix(hasEvenY), bx);\n    } else {\n      return concatBytes(Uint8Array.of(0x04), bx, Fp.toBytes(y));\n    }\n  }\n  function pointFromBytes(bytes) {\n    abytes(bytes, undefined, 'Point');\n    const {\n      publicKey: comp,\n      publicKeyUncompressed: uncomp\n    } = lengths; // e.g. for 32-byte: 33, 65\n    const length = bytes.length;\n    const head = bytes[0];\n    const tail = bytes.subarray(1);\n    // No actual validation is done here: use .assertValidity()\n    if (length === comp && (head === 0x02 || head === 0x03)) {\n      const x = Fp.fromBytes(tail);\n      if (!Fp.isValid(x)) throw new Error('bad point: is not on curve, wrong x');\n      const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n      let y;\n      try {\n        y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n      } catch (sqrtError) {\n        const err = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n        throw new Error('bad point: is not on curve, sqrt error' + err);\n      }\n      assertCompressionIsSupported();\n      const isYOdd = Fp.isOdd(y); // (y & _1n) === _1n;\n      const isHeadOdd = (head & 1) === 1; // ECDSA-specific\n      if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n      return {\n        x,\n        y\n      };\n    } else if (length === uncomp && head === 0x04) {\n      // TODO: more checks\n      const L = Fp.BYTES;\n      const x = Fp.fromBytes(tail.subarray(0, L));\n      const y = Fp.fromBytes(tail.subarray(L, L * 2));\n      if (!isValidXY(x, y)) throw new Error('bad point: is not on curve');\n      return {\n        x,\n        y\n      };\n    } else {\n      throw new Error(`bad point: got length ${length}, expected compressed=${comp} or uncompressed=${uncomp}`);\n    }\n  }\n  const encodePoint = extraOpts.toBytes || pointToBytes;\n  const decodePoint = extraOpts.fromBytes || pointFromBytes;\n  function weierstrassEquation(x) {\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x² * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, CURVE.a)), CURVE.b); // x³ + a * x + b\n  }\n  // TODO: move top-level\n  /** Checks whether equation holds for given x, y: y² == x³ + ax + b */\n  function isValidXY(x, y) {\n    const left = Fp.sqr(y); // y²\n    const right = weierstrassEquation(x); // x³ + ax + b\n    return Fp.eql(left, right);\n  }\n  // Validate whether the passed curve params are valid.\n  // Test 1: equation y² = x³ + ax + b should work for generator point.\n  if (!isValidXY(CURVE.Gx, CURVE.Gy)) throw new Error('bad curve params: generator point');\n  // Test 2: discriminant Δ part should be non-zero: 4a³ + 27b² != 0.\n  // Guarantees curve is genus-1, smooth (non-singular).\n  const _4a3 = Fp.mul(Fp.pow(CURVE.a, _3n), _4n);\n  const _27b2 = Fp.mul(Fp.sqr(CURVE.b), BigInt(27));\n  if (Fp.is0(Fp.add(_4a3, _27b2))) throw new Error('bad curve params: a or b');\n  /** Asserts coordinate is valid: 0 <= n < Fp.ORDER. */\n  function acoord(title, n, banZero = false) {\n    if (!Fp.isValid(n) || banZero && Fp.is0(n)) throw new Error(`bad point coordinate ${title}`);\n    return n;\n  }\n  function aprjpoint(other) {\n    if (!(other instanceof Point)) throw new Error('ProjectivePoint expected');\n  }\n  function splitEndoScalarN(k) {\n    if (!endo || !endo.basises) throw new Error('no endo');\n    return _splitEndoScalar(k, endo.basises, Fn.ORDER);\n  }\n  // Memoized toAffine / validity check. They are heavy. Points are immutable.\n  // Converts Projective point to affine (x, y) coordinates.\n  // Can accept precomputed Z^-1 - for example, from invertBatch.\n  // (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n  const toAffineMemo = memoized((p, iz) => {\n    const {\n      X,\n      Y,\n      Z\n    } = p;\n    // Fast-path for normalized points\n    if (Fp.eql(Z, Fp.ONE)) return {\n      x: X,\n      y: Y\n    };\n    const is0 = p.is0();\n    // If invZ was 0, we return zero point. However we still want to execute\n    // all operations, so we replace invZ with a random number, 1.\n    if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(Z);\n    const x = Fp.mul(X, iz);\n    const y = Fp.mul(Y, iz);\n    const zz = Fp.mul(Z, iz);\n    if (is0) return {\n      x: Fp.ZERO,\n      y: Fp.ZERO\n    };\n    if (!Fp.eql(zz, Fp.ONE)) throw new Error('invZ was invalid');\n    return {\n      x,\n      y\n    };\n  });\n  // NOTE: on exception this will crash 'cached' and no value will be set.\n  // Otherwise true will be return\n  const assertValidMemo = memoized(p => {\n    if (p.is0()) {\n      // (0, 1, 0) aka ZERO is invalid in most contexts.\n      // In BLS, ZERO can be serialized, so we allow it.\n      // (0, 0, 0) is invalid representation of ZERO.\n      if (extraOpts.allowInfinityPoint && !Fp.is0(p.Y)) return;\n      throw new Error('bad point: ZERO');\n    }\n    // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n    const {\n      x,\n      y\n    } = p.toAffine();\n    if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error('bad point: x or y not field elements');\n    if (!isValidXY(x, y)) throw new Error('bad point: equation left != right');\n    if (!p.isTorsionFree()) throw new Error('bad point: not in prime-order subgroup');\n    return true;\n  });\n  function finishEndo(endoBeta, k1p, k2p, k1neg, k2neg) {\n    k2p = new Point(Fp.mul(k2p.X, endoBeta), k2p.Y, k2p.Z);\n    k1p = negateCt(k1neg, k1p);\n    k2p = negateCt(k2neg, k2p);\n    return k1p.add(k2p);\n  }\n  /**\n   * Projective Point works in 3d / projective (homogeneous) coordinates:(X, Y, Z) ∋ (x=X/Z, y=Y/Z).\n   * Default Point works in 2d / affine coordinates: (x, y).\n   * We're doing calculations in projective, because its operations don't require costly inversion.\n   */\n  class Point {\n    /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n    constructor(X, Y, Z) {\n      this.X = acoord('x', X);\n      this.Y = acoord('y', Y, true);\n      this.Z = acoord('z', Z);\n      Object.freeze(this);\n    }\n    static CURVE() {\n      return CURVE;\n    }\n    /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n    static fromAffine(p) {\n      const {\n        x,\n        y\n      } = p || {};\n      if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error('invalid affine point');\n      if (p instanceof Point) throw new Error('projective point not allowed');\n      // (0, 0) would've produced (0, 0, 1) - instead, we need (0, 1, 0)\n      if (Fp.is0(x) && Fp.is0(y)) return Point.ZERO;\n      return new Point(x, y, Fp.ONE);\n    }\n    static fromBytes(bytes) {\n      const P = Point.fromAffine(decodePoint(abytes(bytes, undefined, 'point')));\n      P.assertValidity();\n      return P;\n    }\n    static fromHex(hex) {\n      return Point.fromBytes(ensureBytes('pointHex', hex));\n    }\n    get x() {\n      return this.toAffine().x;\n    }\n    get y() {\n      return this.toAffine().y;\n    }\n    /**\n     *\n     * @param windowSize\n     * @param isLazy true will defer table computation until the first multiplication\n     * @returns\n     */\n    precompute(windowSize = 8, isLazy = true) {\n      wnaf.createCache(this, windowSize);\n      if (!isLazy) this.multiply(_3n); // random number\n      return this;\n    }\n    // TODO: return `this`\n    /** A point on curve is valid if it conforms to equation. */\n    assertValidity() {\n      assertValidMemo(this);\n    }\n    hasEvenY() {\n      const {\n        y\n      } = this.toAffine();\n      if (!Fp.isOdd) throw new Error(\"Field doesn't support isOdd\");\n      return !Fp.isOdd(y);\n    }\n    /** Compare one point to another. */\n    equals(other) {\n      aprjpoint(other);\n      const {\n        X: X1,\n        Y: Y1,\n        Z: Z1\n      } = this;\n      const {\n        X: X2,\n        Y: Y2,\n        Z: Z2\n      } = other;\n      const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n      const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n      return U1 && U2;\n    }\n    /** Flips point to one corresponding to (x, -y) in Affine coordinates. */\n    negate() {\n      return new Point(this.X, Fp.neg(this.Y), this.Z);\n    }\n    // Renes-Costello-Batina exception-free doubling formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 3\n    // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n    double() {\n      const {\n        a,\n        b\n      } = CURVE;\n      const b3 = Fp.mul(b, _3n);\n      const {\n        X: X1,\n        Y: Y1,\n        Z: Z1\n      } = this;\n      let X3 = Fp.ZERO,\n        Y3 = Fp.ZERO,\n        Z3 = Fp.ZERO; // prettier-ignore\n      let t0 = Fp.mul(X1, X1); // step 1\n      let t1 = Fp.mul(Y1, Y1);\n      let t2 = Fp.mul(Z1, Z1);\n      let t3 = Fp.mul(X1, Y1);\n      t3 = Fp.add(t3, t3); // step 5\n      Z3 = Fp.mul(X1, Z1);\n      Z3 = Fp.add(Z3, Z3);\n      X3 = Fp.mul(a, Z3);\n      Y3 = Fp.mul(b3, t2);\n      Y3 = Fp.add(X3, Y3); // step 10\n      X3 = Fp.sub(t1, Y3);\n      Y3 = Fp.add(t1, Y3);\n      Y3 = Fp.mul(X3, Y3);\n      X3 = Fp.mul(t3, X3);\n      Z3 = Fp.mul(b3, Z3); // step 15\n      t2 = Fp.mul(a, t2);\n      t3 = Fp.sub(t0, t2);\n      t3 = Fp.mul(a, t3);\n      t3 = Fp.add(t3, Z3);\n      Z3 = Fp.add(t0, t0); // step 20\n      t0 = Fp.add(Z3, t0);\n      t0 = Fp.add(t0, t2);\n      t0 = Fp.mul(t0, t3);\n      Y3 = Fp.add(Y3, t0);\n      t2 = Fp.mul(Y1, Z1); // step 25\n      t2 = Fp.add(t2, t2);\n      t0 = Fp.mul(t2, t3);\n      X3 = Fp.sub(X3, t0);\n      Z3 = Fp.mul(t2, t1);\n      Z3 = Fp.add(Z3, Z3); // step 30\n      Z3 = Fp.add(Z3, Z3);\n      return new Point(X3, Y3, Z3);\n    }\n    // Renes-Costello-Batina exception-free addition formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 1\n    // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n    add(other) {\n      aprjpoint(other);\n      const {\n        X: X1,\n        Y: Y1,\n        Z: Z1\n      } = this;\n      const {\n        X: X2,\n        Y: Y2,\n        Z: Z2\n      } = other;\n      let X3 = Fp.ZERO,\n        Y3 = Fp.ZERO,\n        Z3 = Fp.ZERO; // prettier-ignore\n      const a = CURVE.a;\n      const b3 = Fp.mul(CURVE.b, _3n);\n      let t0 = Fp.mul(X1, X2); // step 1\n      let t1 = Fp.mul(Y1, Y2);\n      let t2 = Fp.mul(Z1, Z2);\n      let t3 = Fp.add(X1, Y1);\n      let t4 = Fp.add(X2, Y2); // step 5\n      t3 = Fp.mul(t3, t4);\n      t4 = Fp.add(t0, t1);\n      t3 = Fp.sub(t3, t4);\n      t4 = Fp.add(X1, Z1);\n      let t5 = Fp.add(X2, Z2); // step 10\n      t4 = Fp.mul(t4, t5);\n      t5 = Fp.add(t0, t2);\n      t4 = Fp.sub(t4, t5);\n      t5 = Fp.add(Y1, Z1);\n      X3 = Fp.add(Y2, Z2); // step 15\n      t5 = Fp.mul(t5, X3);\n      X3 = Fp.add(t1, t2);\n      t5 = Fp.sub(t5, X3);\n      Z3 = Fp.mul(a, t4);\n      X3 = Fp.mul(b3, t2); // step 20\n      Z3 = Fp.add(X3, Z3);\n      X3 = Fp.sub(t1, Z3);\n      Z3 = Fp.add(t1, Z3);\n      Y3 = Fp.mul(X3, Z3);\n      t1 = Fp.add(t0, t0); // step 25\n      t1 = Fp.add(t1, t0);\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.mul(b3, t4);\n      t1 = Fp.add(t1, t2);\n      t2 = Fp.sub(t0, t2); // step 30\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.add(t4, t2);\n      t0 = Fp.mul(t1, t4);\n      Y3 = Fp.add(Y3, t0);\n      t0 = Fp.mul(t5, t4); // step 35\n      X3 = Fp.mul(t3, X3);\n      X3 = Fp.sub(X3, t0);\n      t0 = Fp.mul(t3, t1);\n      Z3 = Fp.mul(t5, Z3);\n      Z3 = Fp.add(Z3, t0); // step 40\n      return new Point(X3, Y3, Z3);\n    }\n    subtract(other) {\n      return this.add(other.negate());\n    }\n    is0() {\n      return this.equals(Point.ZERO);\n    }\n    /**\n     * Constant time multiplication.\n     * Uses wNAF method. Windowed method may be 10% faster,\n     * but takes 2x longer to generate and consumes 2x memory.\n     * Uses precomputes when available.\n     * Uses endomorphism for Koblitz curves.\n     * @param scalar by which the point would be multiplied\n     * @returns New point\n     */\n    multiply(scalar) {\n      const {\n        endo\n      } = extraOpts;\n      if (!Fn.isValidNot0(scalar)) throw new Error('invalid scalar: out of range'); // 0 is invalid\n      let point, fake; // Fake point is used to const-time mult\n      const mul = n => wnaf.cached(this, n, p => normalizeZ(Point, p));\n      /** See docs for {@link EndomorphismOpts} */\n      if (endo) {\n        const {\n          k1neg,\n          k1,\n          k2neg,\n          k2\n        } = splitEndoScalarN(scalar);\n        const {\n          p: k1p,\n          f: k1f\n        } = mul(k1);\n        const {\n          p: k2p,\n          f: k2f\n        } = mul(k2);\n        fake = k1f.add(k2f);\n        point = finishEndo(endo.beta, k1p, k2p, k1neg, k2neg);\n      } else {\n        const {\n          p,\n          f\n        } = mul(scalar);\n        point = p;\n        fake = f;\n      }\n      // Normalize `z` for both points, but return only real one\n      return normalizeZ(Point, [point, fake])[0];\n    }\n    /**\n     * Non-constant-time multiplication. Uses double-and-add algorithm.\n     * It's faster, but should only be used when you don't care about\n     * an exposed secret key e.g. sig verification, which works over *public* keys.\n     */\n    multiplyUnsafe(sc) {\n      const {\n        endo\n      } = extraOpts;\n      const p = this;\n      if (!Fn.isValid(sc)) throw new Error('invalid scalar: out of range'); // 0 is valid\n      if (sc === _0n || p.is0()) return Point.ZERO;\n      if (sc === _1n) return p; // fast-path\n      if (wnaf.hasCache(this)) return this.multiply(sc);\n      if (endo) {\n        const {\n          k1neg,\n          k1,\n          k2neg,\n          k2\n        } = splitEndoScalarN(sc);\n        const {\n          p1,\n          p2\n        } = mulEndoUnsafe(Point, p, k1, k2); // 30% faster vs wnaf.unsafe\n        return finishEndo(endo.beta, p1, p2, k1neg, k2neg);\n      } else {\n        return wnaf.unsafe(p, sc);\n      }\n    }\n    multiplyAndAddUnsafe(Q, a, b) {\n      const sum = this.multiplyUnsafe(a).add(Q.multiplyUnsafe(b));\n      return sum.is0() ? undefined : sum;\n    }\n    /**\n     * Converts Projective point to affine (x, y) coordinates.\n     * @param invertedZ Z^-1 (inverted zero) - optional, precomputation is useful for invertBatch\n     */\n    toAffine(invertedZ) {\n      return toAffineMemo(this, invertedZ);\n    }\n    /**\n     * Checks whether Point is free of torsion elements (is in prime subgroup).\n     * Always torsion-free for cofactor=1 curves.\n     */\n    isTorsionFree() {\n      const {\n        isTorsionFree\n      } = extraOpts;\n      if (cofactor === _1n) return true;\n      if (isTorsionFree) return isTorsionFree(Point, this);\n      return wnaf.unsafe(this, CURVE_ORDER).is0();\n    }\n    clearCofactor() {\n      const {\n        clearCofactor\n      } = extraOpts;\n      if (cofactor === _1n) return this; // Fast-path\n      if (clearCofactor) return clearCofactor(Point, this);\n      return this.multiplyUnsafe(cofactor);\n    }\n    isSmallOrder() {\n      // can we use this.clearCofactor()?\n      return this.multiplyUnsafe(cofactor).is0();\n    }\n    toBytes(isCompressed = true) {\n      abool(isCompressed, 'isCompressed');\n      this.assertValidity();\n      return encodePoint(Point, this, isCompressed);\n    }\n    toHex(isCompressed = true) {\n      return bytesToHex(this.toBytes(isCompressed));\n    }\n    toString() {\n      return `<Point ${this.is0() ? 'ZERO' : this.toHex()}>`;\n    }\n    // TODO: remove\n    get px() {\n      return this.X;\n    }\n    get py() {\n      return this.X;\n    }\n    get pz() {\n      return this.Z;\n    }\n    toRawBytes(isCompressed = true) {\n      return this.toBytes(isCompressed);\n    }\n    _setWindowSize(windowSize) {\n      this.precompute(windowSize);\n    }\n    static normalizeZ(points) {\n      return normalizeZ(Point, points);\n    }\n    static msm(points, scalars) {\n      return pippenger(Point, Fn, points, scalars);\n    }\n    static fromPrivateKey(privateKey) {\n      return Point.BASE.multiply(_normFnElement(Fn, privateKey));\n    }\n  }\n  // base / generator point\n  Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n  // zero / infinity / identity point\n  Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO); // 0, 1, 0\n  // math field\n  Point.Fp = Fp;\n  // scalar field\n  Point.Fn = Fn;\n  const bits = Fn.BITS;\n  const wnaf = new wNAF(Point, extraOpts.endo ? Math.ceil(bits / 2) : bits);\n  Point.BASE.precompute(8); // Enable precomputes. Slows down first publicKey computation by 20ms.\n  return Point;\n}\n// Points start with byte 0x02 when y is even; otherwise 0x03\nfunction pprefix(hasEvenY) {\n  return Uint8Array.of(hasEvenY ? 0x02 : 0x03);\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio(Fp, Z) {\n  // Generic implementation\n  const q = Fp.ORDER;\n  let l = _0n;\n  for (let o = q - _1n; o % _2n === _0n; o /= _2n) l += _1n;\n  const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n  // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n  // 2n ** c1 == 2n << (c1-1)\n  const _2n_pow_c1_1 = _2n << c1 - _1n - _1n;\n  const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n  const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n  const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n  const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n  const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n  const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n  const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n  let sqrtRatio = (u, v) => {\n    let tv1 = c6; // 1. tv1 = c6\n    let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n    let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n    tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n    let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n    tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n    tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n    tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n    tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n    let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n    tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n    let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n    tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n    tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n    tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n    tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n    // 17. for i in (c1, c1 - 1, ..., 2):\n    for (let i = c1; i > _1n; i--) {\n      let tv5 = i - _2n; // 18.    tv5 = i - 2\n      tv5 = _2n << tv5 - _1n; // 19.    tv5 = 2^tv5\n      let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n      const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n      tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n      tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n      tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n      tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n      tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n    }\n    return {\n      isValid: isQR,\n      value: tv3\n    };\n  };\n  if (Fp.ORDER % _4n === _3n) {\n    // sqrt_ratio_3mod4(u, v)\n    const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n    const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n    sqrtRatio = (u, v) => {\n      let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n      const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n      tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n      let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n      y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n      const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n      const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n      const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n      let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n      return {\n        isValid: isQR,\n        value: y\n      }; // 11. return (isQR, y) isQR ? y : y*c2\n    };\n  }\n  // No curves uses that\n  // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n  return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU(Fp, opts) {\n  validateField(Fp);\n  const {\n    A,\n    B,\n    Z\n  } = opts;\n  if (!Fp.isValid(A) || !Fp.isValid(B) || !Fp.isValid(Z)) throw new Error('mapToCurveSimpleSWU: invalid opts');\n  const sqrtRatio = SWUFpSqrtRatio(Fp, Z);\n  if (!Fp.isOdd) throw new Error('Field does not have .isOdd()');\n  // Input: u, an element of F.\n  // Output: (x, y), a point on E.\n  return u => {\n    // prettier-ignore\n    let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n    tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, Z); // 2.  tv1 = Z * tv1\n    tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n    tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n    tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n    tv3 = Fp.mul(tv3, B); // 6.  tv3 = B * tv3\n    tv4 = Fp.cmov(Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n    tv4 = Fp.mul(tv4, A); // 8.  tv4 = A * tv4\n    tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n    tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n    tv5 = Fp.mul(tv6, A); // 11. tv5 = A * tv6\n    tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n    tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n    tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n    tv5 = Fp.mul(tv6, B); // 15. tv5 = B * tv6\n    tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n    x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n    const {\n      isValid,\n      value\n    } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n    y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n    y = Fp.mul(y, value); // 20.   y = y * y1\n    x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n    y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n    const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n    y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n    const tv4_inv = FpInvertBatch(Fp, [tv4], true)[0];\n    x = Fp.mul(x, tv4_inv); // 25.   x = x / tv4\n    return {\n      x,\n      y\n    };\n  };\n}\nfunction getWLengths(Fp, Fn) {\n  return {\n    secretKey: Fn.BYTES,\n    publicKey: 1 + Fp.BYTES,\n    publicKeyUncompressed: 1 + 2 * Fp.BYTES,\n    publicKeyHasPrefix: true,\n    signature: 2 * Fn.BYTES\n  };\n}\n/**\n * Sometimes users only need getPublicKey, getSharedSecret, and secret key handling.\n * This helper ensures no signature functionality is present. Less code, smaller bundle size.\n */\nexport function ecdh(Point, ecdhOpts = {}) {\n  const {\n    Fn\n  } = Point;\n  const randomBytes_ = ecdhOpts.randomBytes || randomBytesWeb;\n  const lengths = Object.assign(getWLengths(Point.Fp, Fn), {\n    seed: getMinHashLength(Fn.ORDER)\n  });\n  function isValidSecretKey(secretKey) {\n    try {\n      return !!_normFnElement(Fn, secretKey);\n    } catch (error) {\n      return false;\n    }\n  }\n  function isValidPublicKey(publicKey, isCompressed) {\n    const {\n      publicKey: comp,\n      publicKeyUncompressed\n    } = lengths;\n    try {\n      const l = publicKey.length;\n      if (isCompressed === true && l !== comp) return false;\n      if (isCompressed === false && l !== publicKeyUncompressed) return false;\n      return !!Point.fromBytes(publicKey);\n    } catch (error) {\n      return false;\n    }\n  }\n  /**\n   * Produces cryptographically secure secret key from random of size\n   * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n   */\n  function randomSecretKey(seed = randomBytes_(lengths.seed)) {\n    return mapHashToField(abytes(seed, lengths.seed, 'seed'), Fn.ORDER);\n  }\n  /**\n   * Computes public key for a secret key. Checks for validity of the secret key.\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns Public key, full when isCompressed=false; short when isCompressed=true\n   */\n  function getPublicKey(secretKey, isCompressed = true) {\n    return Point.BASE.multiply(_normFnElement(Fn, secretKey)).toBytes(isCompressed);\n  }\n  function keygen(seed) {\n    const secretKey = randomSecretKey(seed);\n    return {\n      secretKey,\n      publicKey: getPublicKey(secretKey)\n    };\n  }\n  /**\n   * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n   */\n  function isProbPub(item) {\n    if (typeof item === 'bigint') return false;\n    if (item instanceof Point) return true;\n    const {\n      secretKey,\n      publicKey,\n      publicKeyUncompressed\n    } = lengths;\n    if (Fn.allowedLengths || secretKey === publicKey) return undefined;\n    const l = ensureBytes('key', item).length;\n    return l === publicKey || l === publicKeyUncompressed;\n  }\n  /**\n   * ECDH (Elliptic Curve Diffie Hellman).\n   * Computes shared public key from secret key A and public key B.\n   * Checks: 1) secret key validity 2) shared key is on-curve.\n   * Does NOT hash the result.\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns shared public key\n   */\n  function getSharedSecret(secretKeyA, publicKeyB, isCompressed = true) {\n    if (isProbPub(secretKeyA) === true) throw new Error('first arg must be private key');\n    if (isProbPub(publicKeyB) === false) throw new Error('second arg must be public key');\n    const s = _normFnElement(Fn, secretKeyA);\n    const b = Point.fromHex(publicKeyB); // checks for being on-curve\n    return b.multiply(s).toBytes(isCompressed);\n  }\n  const utils = {\n    isValidSecretKey,\n    isValidPublicKey,\n    randomSecretKey,\n    // TODO: remove\n    isValidPrivateKey: isValidSecretKey,\n    randomPrivateKey: randomSecretKey,\n    normPrivateKeyToScalar: key => _normFnElement(Fn, key),\n    precompute(windowSize = 8, point = Point.BASE) {\n      return point.precompute(windowSize, false);\n    }\n  };\n  return Object.freeze({\n    getPublicKey,\n    getSharedSecret,\n    keygen,\n    Point,\n    utils,\n    lengths\n  });\n}\n/**\n * Creates ECDSA signing interface for given elliptic curve `Point` and `hash` function.\n * We need `hash` for 2 features:\n * 1. Message prehash-ing. NOT used if `sign` / `verify` are called with `prehash: false`\n * 2. k generation in `sign`, using HMAC-drbg(hash)\n *\n * ECDSAOpts are only rarely needed.\n *\n * @example\n * ```js\n * const p256_Point = weierstrass(...);\n * const p256_sha256 = ecdsa(p256_Point, sha256);\n * const p256_sha224 = ecdsa(p256_Point, sha224);\n * const p256_sha224_r = ecdsa(p256_Point, sha224, { randomBytes: (length) => { ... } });\n * ```\n */\nexport function ecdsa(Point, hash, ecdsaOpts = {}) {\n  ahash(hash);\n  _validateObject(ecdsaOpts, {}, {\n    hmac: 'function',\n    lowS: 'boolean',\n    randomBytes: 'function',\n    bits2int: 'function',\n    bits2int_modN: 'function'\n  });\n  const randomBytes = ecdsaOpts.randomBytes || randomBytesWeb;\n  const hmac = ecdsaOpts.hmac || ((key, ...msgs) => nobleHmac(hash, key, concatBytes(...msgs)));\n  const {\n    Fp,\n    Fn\n  } = Point;\n  const {\n    ORDER: CURVE_ORDER,\n    BITS: fnBits\n  } = Fn;\n  const {\n    keygen,\n    getPublicKey,\n    getSharedSecret,\n    utils,\n    lengths\n  } = ecdh(Point, ecdsaOpts);\n  const defaultSigOpts = {\n    prehash: false,\n    lowS: typeof ecdsaOpts.lowS === 'boolean' ? ecdsaOpts.lowS : false,\n    format: undefined,\n    //'compact' as ECDSASigFormat,\n    extraEntropy: false\n  };\n  const defaultSigOpts_format = 'compact';\n  function isBiggerThanHalfOrder(number) {\n    const HALF = CURVE_ORDER >> _1n;\n    return number > HALF;\n  }\n  function validateRS(title, num) {\n    if (!Fn.isValidNot0(num)) throw new Error(`invalid signature ${title}: out of range 1..Point.Fn.ORDER`);\n    return num;\n  }\n  function validateSigLength(bytes, format) {\n    validateSigFormat(format);\n    const size = lengths.signature;\n    const sizer = format === 'compact' ? size : format === 'recovered' ? size + 1 : undefined;\n    return abytes(bytes, sizer, `${format} signature`);\n  }\n  /**\n   * ECDSA signature with its (r, s) properties. Supports compact, recovered & DER representations.\n   */\n  class Signature {\n    constructor(r, s, recovery) {\n      this.r = validateRS('r', r); // r in [1..N-1];\n      this.s = validateRS('s', s); // s in [1..N-1];\n      if (recovery != null) this.recovery = recovery;\n      Object.freeze(this);\n    }\n    static fromBytes(bytes, format = defaultSigOpts_format) {\n      validateSigLength(bytes, format);\n      let recid;\n      if (format === 'der') {\n        const {\n          r,\n          s\n        } = DER.toSig(abytes(bytes));\n        return new Signature(r, s);\n      }\n      if (format === 'recovered') {\n        recid = bytes[0];\n        format = 'compact';\n        bytes = bytes.subarray(1);\n      }\n      const L = Fn.BYTES;\n      const r = bytes.subarray(0, L);\n      const s = bytes.subarray(L, L * 2);\n      return new Signature(Fn.fromBytes(r), Fn.fromBytes(s), recid);\n    }\n    static fromHex(hex, format) {\n      return this.fromBytes(hexToBytes(hex), format);\n    }\n    addRecoveryBit(recovery) {\n      return new Signature(this.r, this.s, recovery);\n    }\n    recoverPublicKey(messageHash) {\n      const FIELD_ORDER = Fp.ORDER;\n      const {\n        r,\n        s,\n        recovery: rec\n      } = this;\n      if (rec == null || ![0, 1, 2, 3].includes(rec)) throw new Error('recovery id invalid');\n      // ECDSA recovery is hard for cofactor > 1 curves.\n      // In sign, `r = q.x mod n`, and here we recover q.x from r.\n      // While recovering q.x >= n, we need to add r+n for cofactor=1 curves.\n      // However, for cofactor>1, r+n may not get q.x:\n      // r+n*i would need to be done instead where i is unknown.\n      // To easily get i, we either need to:\n      // a. increase amount of valid recid values (4, 5...); OR\n      // b. prohibit non-prime-order signatures (recid > 1).\n      const hasCofactor = CURVE_ORDER * _2n < FIELD_ORDER;\n      if (hasCofactor && rec > 1) throw new Error('recovery id is ambiguous for h>1 curve');\n      const radj = rec === 2 || rec === 3 ? r + CURVE_ORDER : r;\n      if (!Fp.isValid(radj)) throw new Error('recovery id 2 or 3 invalid');\n      const x = Fp.toBytes(radj);\n      const R = Point.fromBytes(concatBytes(pprefix((rec & 1) === 0), x));\n      const ir = Fn.inv(radj); // r^-1\n      const h = bits2int_modN(ensureBytes('msgHash', messageHash)); // Truncate hash\n      const u1 = Fn.create(-h * ir); // -hr^-1\n      const u2 = Fn.create(s * ir); // sr^-1\n      // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1). unsafe is fine: there is no private data.\n      const Q = Point.BASE.multiplyUnsafe(u1).add(R.multiplyUnsafe(u2));\n      if (Q.is0()) throw new Error('point at infinify');\n      Q.assertValidity();\n      return Q;\n    }\n    // Signatures should be low-s, to prevent malleability.\n    hasHighS() {\n      return isBiggerThanHalfOrder(this.s);\n    }\n    toBytes(format = defaultSigOpts_format) {\n      validateSigFormat(format);\n      if (format === 'der') return hexToBytes(DER.hexFromSig(this));\n      const r = Fn.toBytes(this.r);\n      const s = Fn.toBytes(this.s);\n      if (format === 'recovered') {\n        if (this.recovery == null) throw new Error('recovery bit must be present');\n        return concatBytes(Uint8Array.of(this.recovery), r, s);\n      }\n      return concatBytes(r, s);\n    }\n    toHex(format) {\n      return bytesToHex(this.toBytes(format));\n    }\n    // TODO: remove\n    assertValidity() {}\n    static fromCompact(hex) {\n      return Signature.fromBytes(ensureBytes('sig', hex), 'compact');\n    }\n    static fromDER(hex) {\n      return Signature.fromBytes(ensureBytes('sig', hex), 'der');\n    }\n    normalizeS() {\n      return this.hasHighS() ? new Signature(this.r, Fn.neg(this.s), this.recovery) : this;\n    }\n    toDERRawBytes() {\n      return this.toBytes('der');\n    }\n    toDERHex() {\n      return bytesToHex(this.toBytes('der'));\n    }\n    toCompactRawBytes() {\n      return this.toBytes('compact');\n    }\n    toCompactHex() {\n      return bytesToHex(this.toBytes('compact'));\n    }\n  }\n  // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n  // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n  // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n  // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n  const bits2int = ecdsaOpts.bits2int || function bits2int_def(bytes) {\n    // Our custom check \"just in case\", for protection against DoS\n    if (bytes.length > 8192) throw new Error('input is too large');\n    // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n    // for some cases, since bytes.length * 8 is not actual bitLength.\n    const num = bytesToNumberBE(bytes); // check for == u8 done here\n    const delta = bytes.length * 8 - fnBits; // truncate to nBitLength leftmost bits\n    return delta > 0 ? num >> BigInt(delta) : num;\n  };\n  const bits2int_modN = ecdsaOpts.bits2int_modN || function bits2int_modN_def(bytes) {\n    return Fn.create(bits2int(bytes)); // can't use bytesToNumberBE here\n  };\n  // Pads output with zero as per spec\n  const ORDER_MASK = bitMask(fnBits);\n  /** Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`. */\n  function int2octets(num) {\n    // IMPORTANT: the check ensures working for case `Fn.BYTES != Fn.BITS * 8`\n    aInRange('num < 2^' + fnBits, num, _0n, ORDER_MASK);\n    return Fn.toBytes(num);\n  }\n  function validateMsgAndHash(message, prehash) {\n    abytes(message, undefined, 'message');\n    return prehash ? abytes(hash(message), undefined, 'prehashed message') : message;\n  }\n  /**\n   * Steps A, D of RFC6979 3.2.\n   * Creates RFC6979 seed; converts msg/privKey to numbers.\n   * Used only in sign, not in verify.\n   *\n   * Warning: we cannot assume here that message has same amount of bytes as curve order,\n   * this will be invalid at least for P521. Also it can be bigger for P224 + SHA256.\n   */\n  function prepSig(message, privateKey, opts) {\n    if (['recovered', 'canonical'].some(k => k in opts)) throw new Error('sign() legacy options not supported');\n    const {\n      lowS,\n      prehash,\n      extraEntropy\n    } = validateSigOpts(opts, defaultSigOpts);\n    message = validateMsgAndHash(message, prehash); // RFC6979 3.2 A: h1 = H(m)\n    // We can't later call bits2octets, since nested bits2int is broken for curves\n    // with fnBits % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n    // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n    const h1int = bits2int_modN(message);\n    const d = _normFnElement(Fn, privateKey); // validate secret key, convert to bigint\n    const seedArgs = [int2octets(d), int2octets(h1int)];\n    // extraEntropy. RFC6979 3.6: additional k' (optional).\n    if (extraEntropy != null && extraEntropy !== false) {\n      // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n      // gen random bytes OR pass as-is\n      const e = extraEntropy === true ? randomBytes(lengths.secretKey) : extraEntropy;\n      seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n    }\n    const seed = concatBytes(...seedArgs); // Step D of RFC6979 3.2\n    const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n    // Converts signature params into point w r/s, checks result for validity.\n    // To transform k => Signature:\n    // q = k⋅G\n    // r = q.x mod n\n    // s = k^-1(m + rd) mod n\n    // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n    // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n    // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n    function k2sig(kBytes) {\n      // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n      // Important: all mod() calls here must be done over N\n      const k = bits2int(kBytes); // mod n, not mod p\n      if (!Fn.isValidNot0(k)) return; // Valid scalars (including k) must be in 1..N-1\n      const ik = Fn.inv(k); // k^-1 mod n\n      const q = Point.BASE.multiply(k).toAffine(); // q = k⋅G\n      const r = Fn.create(q.x); // r = q.x mod n\n      if (r === _0n) return;\n      const s = Fn.create(ik * Fn.create(m + r * d)); // Not using blinding here, see comment above\n      if (s === _0n) return;\n      let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n      let normS = s;\n      if (lowS && isBiggerThanHalfOrder(s)) {\n        normS = Fn.neg(s); // if lowS was passed, ensure s is always\n        recovery ^= 1; // // in the bottom half of N\n      }\n      return new Signature(r, normS, recovery); // use normS, not s\n    }\n    return {\n      seed,\n      k2sig\n    };\n  }\n  /**\n   * Signs message hash with a secret key.\n   *\n   * ```\n   * sign(m, d) where\n   *   k = rfc6979_hmac_drbg(m, d)\n   *   (x, y) = G × k\n   *   r = x mod n\n   *   s = (m + dr) / k mod n\n   * ```\n   */\n  function sign(message, secretKey, opts = {}) {\n    message = ensureBytes('message', message);\n    const {\n      seed,\n      k2sig\n    } = prepSig(message, secretKey, opts); // Steps A, D of RFC6979 3.2.\n    const drbg = createHmacDrbg(hash.outputLen, Fn.BYTES, hmac);\n    const sig = drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    return sig;\n  }\n  function tryParsingSig(sg) {\n    // Try to deduce format\n    let sig = undefined;\n    const isHex = typeof sg === 'string' || isBytes(sg);\n    const isObj = !isHex && sg !== null && typeof sg === 'object' && typeof sg.r === 'bigint' && typeof sg.s === 'bigint';\n    if (!isHex && !isObj) throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n    if (isObj) {\n      sig = new Signature(sg.r, sg.s);\n    } else if (isHex) {\n      try {\n        sig = Signature.fromBytes(ensureBytes('sig', sg), 'der');\n      } catch (derError) {\n        if (!(derError instanceof DER.Err)) throw derError;\n      }\n      if (!sig) {\n        try {\n          sig = Signature.fromBytes(ensureBytes('sig', sg), 'compact');\n        } catch (error) {\n          return false;\n        }\n      }\n    }\n    if (!sig) return false;\n    return sig;\n  }\n  /**\n   * Verifies a signature against message and public key.\n   * Rejects lowS signatures by default: see {@link ECDSAVerifyOpts}.\n   * Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n   *\n   * ```\n   * verify(r, s, h, P) where\n   *   u1 = hs^-1 mod n\n   *   u2 = rs^-1 mod n\n   *   R = u1⋅G + u2⋅P\n   *   mod(R.x, n) == r\n   * ```\n   */\n  function verify(signature, message, publicKey, opts = {}) {\n    const {\n      lowS,\n      prehash,\n      format\n    } = validateSigOpts(opts, defaultSigOpts);\n    publicKey = ensureBytes('publicKey', publicKey);\n    message = validateMsgAndHash(ensureBytes('message', message), prehash);\n    if ('strict' in opts) throw new Error('options.strict was renamed to lowS');\n    const sig = format === undefined ? tryParsingSig(signature) : Signature.fromBytes(ensureBytes('sig', signature), format);\n    if (sig === false) return false;\n    try {\n      const P = Point.fromBytes(publicKey);\n      if (lowS && sig.hasHighS()) return false;\n      const {\n        r,\n        s\n      } = sig;\n      const h = bits2int_modN(message); // mod n, not mod p\n      const is = Fn.inv(s); // s^-1 mod n\n      const u1 = Fn.create(h * is); // u1 = hs^-1 mod n\n      const u2 = Fn.create(r * is); // u2 = rs^-1 mod n\n      const R = Point.BASE.multiplyUnsafe(u1).add(P.multiplyUnsafe(u2)); // u1⋅G + u2⋅P\n      if (R.is0()) return false;\n      const v = Fn.create(R.x); // v = r.x mod n\n      return v === r;\n    } catch (e) {\n      return false;\n    }\n  }\n  function recoverPublicKey(signature, message, opts = {}) {\n    const {\n      prehash\n    } = validateSigOpts(opts, defaultSigOpts);\n    message = validateMsgAndHash(message, prehash);\n    return Signature.fromBytes(signature, 'recovered').recoverPublicKey(message).toBytes();\n  }\n  return Object.freeze({\n    keygen,\n    getPublicKey,\n    getSharedSecret,\n    utils,\n    lengths,\n    Point,\n    sign,\n    verify,\n    recoverPublicKey,\n    Signature,\n    hash\n  });\n}\n/** @deprecated use `weierstrass` in newer releases */\nexport function weierstrassPoints(c) {\n  const {\n    CURVE,\n    curveOpts\n  } = _weierstrass_legacy_opts_to_new(c);\n  const Point = weierstrassN(CURVE, curveOpts);\n  return _weierstrass_new_output_to_legacy(c, Point);\n}\nfunction _weierstrass_legacy_opts_to_new(c) {\n  const CURVE = {\n    a: c.a,\n    b: c.b,\n    p: c.Fp.ORDER,\n    n: c.n,\n    h: c.h,\n    Gx: c.Gx,\n    Gy: c.Gy\n  };\n  const Fp = c.Fp;\n  let allowedLengths = c.allowedPrivateKeyLengths ? Array.from(new Set(c.allowedPrivateKeyLengths.map(l => Math.ceil(l / 2)))) : undefined;\n  const Fn = Field(CURVE.n, {\n    BITS: c.nBitLength,\n    allowedLengths: allowedLengths,\n    modFromBytes: c.wrapPrivateKey\n  });\n  const curveOpts = {\n    Fp,\n    Fn,\n    allowInfinityPoint: c.allowInfinityPoint,\n    endo: c.endo,\n    isTorsionFree: c.isTorsionFree,\n    clearCofactor: c.clearCofactor,\n    fromBytes: c.fromBytes,\n    toBytes: c.toBytes\n  };\n  return {\n    CURVE,\n    curveOpts\n  };\n}\nfunction _ecdsa_legacy_opts_to_new(c) {\n  const {\n    CURVE,\n    curveOpts\n  } = _weierstrass_legacy_opts_to_new(c);\n  const ecdsaOpts = {\n    hmac: c.hmac,\n    randomBytes: c.randomBytes,\n    lowS: c.lowS,\n    bits2int: c.bits2int,\n    bits2int_modN: c.bits2int_modN\n  };\n  return {\n    CURVE,\n    curveOpts,\n    hash: c.hash,\n    ecdsaOpts\n  };\n}\nexport function _legacyHelperEquat(Fp, a, b) {\n  /**\n   * y² = x³ + ax + b: Short weierstrass curve formula. Takes x, returns y².\n   * @returns y²\n   */\n  function weierstrassEquation(x) {\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x² * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x³ + a * x + b\n  }\n  return weierstrassEquation;\n}\nfunction _weierstrass_new_output_to_legacy(c, Point) {\n  const {\n    Fp,\n    Fn\n  } = Point;\n  function isWithinCurveOrder(num) {\n    return inRange(num, _1n, Fn.ORDER);\n  }\n  const weierstrassEquation = _legacyHelperEquat(Fp, c.a, c.b);\n  return Object.assign({}, {\n    CURVE: c,\n    Point: Point,\n    ProjectivePoint: Point,\n    normPrivateKeyToScalar: key => _normFnElement(Fn, key),\n    weierstrassEquation,\n    isWithinCurveOrder\n  });\n}\nfunction _ecdsa_new_output_to_legacy(c, _ecdsa) {\n  const Point = _ecdsa.Point;\n  return Object.assign({}, _ecdsa, {\n    ProjectivePoint: Point,\n    CURVE: Object.assign({}, c, nLength(Point.Fn.ORDER, Point.Fn.BITS))\n  });\n}\n// _ecdsa_legacy\nexport function weierstrass(c) {\n  const {\n    CURVE,\n    curveOpts,\n    hash,\n    ecdsaOpts\n  } = _ecdsa_legacy_opts_to_new(c);\n  const Point = weierstrassN(CURVE, curveOpts);\n  const signs = ecdsa(Point, hash, ecdsaOpts);\n  return _ecdsa_new_output_to_legacy(c, signs);\n}", "map": {"version": 3, "names": ["hmac", "nobleHmac", "ahash", "_validateObject", "_abool2", "abool", "_abytes2", "abytes", "aInRange", "bitLen", "bitMask", "bytesToHex", "bytesToNumberBE", "concatBytes", "createHmacDrbg", "ensureBytes", "hexToBytes", "inRange", "isBytes", "memoized", "numberToHexUnpadded", "randomBytes", "randomBytesWeb", "_createCurveFields", "mulEndoUnsafe", "negateCt", "normalizeZ", "pippenger", "wNAF", "Field", "FpInvertBatch", "get<PERSON>in<PERSON>ash<PERSON><PERSON><PERSON>", "mapHashToField", "nLength", "validateField", "divNearest", "num", "den", "_2n", "_splitEndoScalar", "k", "basis", "n", "a1", "b1", "a2", "b2", "c1", "c2", "k1", "k2", "k1neg", "_0n", "k2neg", "MAX_NUM", "Math", "ceil", "_1n", "Error", "validateSigFormat", "format", "includes", "validateSigOpts", "opts", "def", "optsn", "optName", "Object", "keys", "undefined", "lowS", "prehash", "DERErr", "constructor", "m", "DER", "Err", "_tlv", "encode", "tag", "data", "E", "length", "dataLen", "len", "lenLen", "t", "decode", "pos", "first", "isLong", "lengthBytes", "subarray", "b", "v", "l", "_int", "hex", "Number", "parseInt", "to<PERSON><PERSON>", "int", "tlv", "seqBytes", "seqLeftBytes", "rBytes", "rLeftBytes", "sBytes", "sLeftBytes", "r", "s", "hexFromSig", "sig", "rs", "ss", "seq", "BigInt", "_3n", "_4n", "_normFnElement", "Fn", "key", "BYTES", "expected", "bytes", "fromBytes", "error", "isValidNot0", "weierstrassN", "params", "extraOpts", "validated", "Fp", "CURVE", "h", "cofactor", "CURVE_ORDER", "allowInfinityPoint", "clearCofactor", "isTorsionFree", "toBytes", "endo", "wrapPrivateKey", "is0", "a", "beta", "Array", "isArray", "basises", "lengths", "getWLengths", "assertCompressionIsSupported", "isOdd", "pointToBytes", "_c", "point", "isCompressed", "x", "y", "toAffine", "bx", "hasEvenY", "pprefix", "Uint8Array", "of", "pointFromBytes", "public<PERSON>ey", "comp", "publicKeyUncompressed", "uncomp", "head", "tail", "<PERSON><PERSON><PERSON><PERSON>", "y2", "weierstrassEquation", "sqrt", "sqrtError", "err", "message", "isYOdd", "isHeadOdd", "neg", "L", "isValidXY", "encodePoint", "decodePoint", "x2", "sqr", "x3", "mul", "add", "left", "right", "eql", "Gx", "Gy", "_4a3", "pow", "_27b2", "acoord", "title", "banZero", "aprjpoint", "other", "Point", "splitEndoScalarN", "ORDER", "toAffineMemo", "p", "iz", "X", "Y", "Z", "ONE", "inv", "zz", "ZERO", "assertValidMemo", "finishEndo", "endoBeta", "k1p", "k2p", "freeze", "fromAffine", "P", "assertValidity", "fromHex", "precompute", "windowSize", "isLazy", "wnaf", "createCache", "multiply", "equals", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "U1", "U2", "negate", "double", "b3", "X3", "Y3", "Z3", "t0", "t1", "t2", "t3", "sub", "t4", "t5", "subtract", "scalar", "fake", "cached", "f", "k1f", "k2f", "multiplyUnsafe", "sc", "<PERSON><PERSON><PERSON>", "p1", "p2", "unsafe", "multiplyAndAddUnsafe", "Q", "sum", "invertedZ", "isSmallOrder", "toHex", "toString", "px", "py", "pz", "toRawBytes", "_setWindowSize", "points", "msm", "scalars", "fromPrivateKey", "privateKey", "BASE", "bits", "BITS", "SWUFpSqrtRatio", "q", "o", "_2n_pow_c1_1", "_2n_pow_c1", "c3", "c4", "c5", "c6", "c7", "sqrtRatio", "u", "tv1", "tv2", "tv3", "tv5", "tv4", "isQR", "cmov", "i", "tvv5", "e1", "value", "y1", "mapToCurveSimpleSWU", "A", "B", "tv6", "tv4_inv", "secret<PERSON>ey", "publicKeyHasPrefix", "signature", "ecdh", "ecdhOpts", "randomBytes_", "assign", "seed", "isValidSecret<PERSON>", "isValidPublicKey", "randomSec<PERSON><PERSON>ey", "getPublicKey", "keygen", "isProbPub", "item", "allowedLengths", "getSharedSecret", "secretKeyA", "publicKeyB", "utils", "isValidPrivateKey", "randomPrivateKey", "normPrivateKeyToScalar", "ecdsa", "hash", "ecdsaOpts", "bits2int", "bits2int_modN", "msgs", "fnBits", "defaultSigOpts", "extraEntropy", "defaultSigOpts_format", "isBiggerThanHalfOrder", "number", "HALF", "validateRS", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "sizer", "Signature", "recovery", "recid", "addRecoveryBit", "recoverPublicKey", "messageHash", "FIELD_ORDER", "rec", "hasCofactor", "radj", "R", "ir", "u1", "create", "u2", "hasHighS", "fromCompact", "fromDER", "normalizeS", "toDERRawBytes", "toDERHex", "toCompactRawBytes", "toCompactHex", "bits2int_def", "delta", "bits2int_modN_def", "ORDER_MASK", "int2octets", "validateMsgAndHash", "prepSig", "some", "h1int", "d", "seedArgs", "e", "push", "k2sig", "kBytes", "ik", "normS", "sign", "drbg", "outputLen", "tryParsingSig", "sg", "isHex", "isObj", "<PERSON><PERSON><PERSON><PERSON>", "verify", "is", "weierstrassPoints", "c", "curveOpts", "_we<PERSON><PERSON>s_legacy_opts_to_new", "_we<PERSON>trass_new_output_to_legacy", "allowedPrivateKeyLengths", "from", "Set", "map", "nBitLength", "modFromBytes", "_ecdsa_legacy_opts_to_new", "_legacyHelperEquat", "isWithinCurveOrder", "ProjectivePoint", "_ecdsa_new_output_to_legacy", "_ecdsa", "<PERSON><PERSON><PERSON><PERSON>", "signs"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\abstract\\weierstrass.ts"], "sourcesContent": ["/**\n * Short <PERSON> curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac as nobleHmac } from '@noble/hashes/hmac.js';\nimport { ahash } from '@noble/hashes/utils';\nimport {\n  _validateObject,\n  _abool2 as abool,\n  _abytes2 as abytes,\n  aInRange,\n  bitLen,\n  bitMask,\n  bytesToHex,\n  bytesToNumberBE,\n  concatBytes,\n  createHmacDrbg,\n  ensureBytes,\n  hexToBytes,\n  inRange,\n  isBytes,\n  memoized,\n  numberToHexUnpadded,\n  randomBytes as randomBytesWeb,\n  type CHash,\n  type Hex,\n  type PrivKey,\n} from '../utils.ts';\nimport {\n  _createCurveFields,\n  mulEndoUnsafe,\n  negateCt,\n  normalizeZ,\n  pippenger,\n  wNAF,\n  type AffinePoint,\n  type BasicCurve,\n  type CurveLengths,\n  type CurvePoint,\n  type CurvePointCons,\n} from './curve.ts';\nimport {\n  Field,\n  FpInvertBatch,\n  getMinHashLength,\n  mapHashToField,\n  nLength,\n  validateField,\n  type IField,\n  type NLength,\n} from './modular.ts';\n\nexport type { AffinePoint };\nexport type HmacFnSync = (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array;\n\ntype EndoBasis = [[bigint, bigint], [bigint, bigint]];\n/**\n * When Weierstrass curve has `a=0`, it becomes Koblitz curve.\n * Koblitz curves allow using **efficiently-computable GLV endomorphism ψ**.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n *\n * Endomorphism consists of beta, lambda and splitScalar:\n *\n * 1. GLV endomorphism ψ transforms a point: `P = (x, y) ↦ ψ(P) = (β·x mod p, y)`\n * 2. GLV scalar decomposition transforms a scalar: `k ≡ k₁ + k₂·λ (mod n)`\n * 3. Then these are combined: `k·P = k₁·P + k₂·ψ(P)`\n * 4. Two 128-bit point-by-scalar multiplications + one point addition is faster than\n *    one 256-bit multiplication.\n *\n * where\n * * beta: β ∈ Fₚ with β³ = 1, β ≠ 1\n * * lambda: λ ∈ Fₙ with λ³ = 1, λ ≠ 1\n * * splitScalar decomposes k ↦ k₁, k₂, by using reduced basis vectors.\n *   Gauss lattice reduction calculates them from initial basis vectors `(n, 0), (-λ, 0)`\n *\n * Check out `test/misc/endomorphism.js` and\n * [gist](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n */\nexport type EndomorphismOpts = {\n  beta: bigint;\n  basises?: EndoBasis;\n  splitScalar?: (k: bigint) => { k1neg: boolean; k1: bigint; k2neg: boolean; k2: bigint };\n};\n\n// We construct basis in such way that den is always positive and equals n, but num sign depends on basis (not on secret value)\nconst divNearest = (num: bigint, den: bigint) => (num + (num >= 0 ? den : -den) / _2n) / den;\n\nexport type ScalarEndoParts = { k1neg: boolean; k1: bigint; k2neg: boolean; k2: bigint };\n\n/**\n * Splits scalar for GLV endomorphism.\n */\nexport function _splitEndoScalar(k: bigint, basis: EndoBasis, n: bigint): ScalarEndoParts {\n  // Split scalar into two such that part is ~half bits: `abs(part) < sqrt(N)`\n  // Since part can be negative, we need to do this on point.\n  // TODO: verifyScalar function which consumes lambda\n  const [[a1, b1], [a2, b2]] = basis;\n  const c1 = divNearest(b2 * k, n);\n  const c2 = divNearest(-b1 * k, n);\n  // |k1|/|k2| is < sqrt(N), but can be negative.\n  // If we do `k1 mod N`, we'll get big scalar (`> sqrt(N)`): so, we do cheaper negation instead.\n  let k1 = k - c1 * a1 - c2 * a2;\n  let k2 = -c1 * b1 - c2 * b2;\n  const k1neg = k1 < _0n;\n  const k2neg = k2 < _0n;\n  if (k1neg) k1 = -k1;\n  if (k2neg) k2 = -k2;\n  // Double check that resulting scalar less than half bits of N: otherwise wNAF will fail.\n  // This should only happen on wrong basises. Also, math inside is too complex and I don't trust it.\n  const MAX_NUM = bitMask(Math.ceil(bitLen(n) / 2)) + _1n; // Half bits of N\n  if (k1 < _0n || k1 >= MAX_NUM || k2 < _0n || k2 >= MAX_NUM) {\n    throw new Error('splitScalar (endomorphism): failed, k=' + k);\n  }\n  return { k1neg, k1, k2neg, k2 };\n}\n\nexport type ECDSASigFormat = 'compact' | 'recovered' | 'der';\nexport type ECDSARecoverOpts = {\n  prehash?: boolean;\n};\nexport type ECDSAVerifyOpts = {\n  prehash?: boolean;\n  lowS?: boolean;\n  format?: ECDSASigFormat;\n};\nexport type ECDSASignOpts = {\n  prehash?: boolean;\n  lowS?: boolean;\n  format?: ECDSASigFormat;\n  extraEntropy?: Uint8Array | boolean;\n};\n\nfunction validateSigFormat(format: string): ECDSASigFormat {\n  if (!['compact', 'recovered', 'der'].includes(format))\n    throw new Error('Signature format must be \"compact\", \"recovered\", or \"der\"');\n  return format as ECDSASigFormat;\n}\n\nfunction validateSigOpts<T extends ECDSASignOpts, D extends Required<ECDSASignOpts>>(\n  opts: T,\n  def: D\n): Required<ECDSASignOpts> {\n  const optsn: ECDSASignOpts = {};\n  for (let optName of Object.keys(def)) {\n    // @ts-ignore\n    optsn[optName] = opts[optName] === undefined ? def[optName] : opts[optName];\n  }\n  abool(optsn.lowS!, 'lowS');\n  abool(optsn.prehash!, 'prehash');\n  if (optsn.format !== undefined) validateSigFormat(optsn.format);\n  return optsn as Required<ECDSASignOpts>;\n}\n\n/** Instance methods for 3D XYZ projective points. */\nexport interface WeierstrassPoint<T> extends CurvePoint<T, WeierstrassPoint<T>> {\n  /** projective X coordinate. Different from affine x. */\n  readonly X: T;\n  /** projective Y coordinate. Different from affine y. */\n  readonly Y: T;\n  /** projective z coordinate */\n  readonly Z: T;\n  /** affine x coordinate. Different from projective X. */\n  get x(): T;\n  /** affine y coordinate. Different from projective Y. */\n  get y(): T;\n  /** Encodes point using IEEE P1363 (DER) encoding. First byte is 2/3/4. Default = isCompressed. */\n  toBytes(isCompressed?: boolean): Uint8Array;\n  toHex(isCompressed?: boolean): string;\n\n  /** @deprecated use `.X` */\n  readonly px: T;\n  /** @deprecated use `.Y` */\n  readonly py: T;\n  /** @deprecated use `.Z` */\n  readonly pz: T;\n  /** @deprecated use `toBytes` */\n  toRawBytes(isCompressed?: boolean): Uint8Array;\n  /** @deprecated use `multiplyUnsafe` */\n  multiplyAndAddUnsafe(\n    Q: WeierstrassPoint<T>,\n    a: bigint,\n    b: bigint\n  ): WeierstrassPoint<T> | undefined;\n  /** @deprecated use `p.y % 2n === 0n` */\n  hasEvenY(): boolean;\n  /** @deprecated use `p.precompute(windowSize)` */\n  _setWindowSize(windowSize: number): void;\n}\n\n/** Static methods for 3D XYZ projective points. */\nexport interface WeierstrassPointCons<T> extends CurvePointCons<WeierstrassPoint<T>> {\n  /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n  new (X: T, Y: T, Z: T): WeierstrassPoint<T>;\n  CURVE(): WeierstrassOpts<T>;\n  /** @deprecated use `Point.BASE.multiply(Point.Fn.fromBytes(privateKey))` */\n  fromPrivateKey(privateKey: PrivKey): WeierstrassPoint<T>;\n  /** @deprecated use `import { normalizeZ } from '@noble/curves/abstract/curve.js';` */\n  normalizeZ(points: WeierstrassPoint<T>[]): WeierstrassPoint<T>[];\n  /** @deprecated use `import { pippenger } from '@noble/curves/abstract/curve.js';` */\n  msm(points: WeierstrassPoint<T>[], scalars: bigint[]): WeierstrassPoint<T>;\n}\n\n/**\n * Weierstrass curve options.\n *\n * * p: prime characteristic (order) of finite field, in which arithmetics is done\n * * n: order of prime subgroup a.k.a total amount of valid curve points\n * * h: cofactor, usually 1. h*n is group order; n is subgroup order\n * * a: formula param, must be in field of p\n * * b: formula param, must be in field of p\n * * Gx: x coordinate of generator point a.k.a. base point\n * * Gy: y coordinate of generator point\n */\nexport type WeierstrassOpts<T> = Readonly<{\n  p: bigint;\n  n: bigint;\n  h: bigint;\n  a: T;\n  b: T;\n  Gx: T;\n  Gy: T;\n}>;\n\n// When a cofactor != 1, there can be an effective methods to:\n// 1. Determine whether a point is torsion-free\n// 2. Clear torsion component\n// wrapPrivateKey: bls12-381 requires mod(n) instead of rejecting keys >= n\nexport type WeierstrassExtraOpts<T> = Partial<{\n  Fp: IField<T>;\n  Fn: IField<bigint>;\n  allowInfinityPoint: boolean;\n  endo: EndomorphismOpts;\n  isTorsionFree: (c: WeierstrassPointCons<T>, point: WeierstrassPoint<T>) => boolean;\n  clearCofactor: (c: WeierstrassPointCons<T>, point: WeierstrassPoint<T>) => WeierstrassPoint<T>;\n  fromBytes: (bytes: Uint8Array) => AffinePoint<T>;\n  toBytes: (\n    c: WeierstrassPointCons<T>,\n    point: WeierstrassPoint<T>,\n    isCompressed: boolean\n  ) => Uint8Array;\n}>;\n\n/**\n * Options for ECDSA signatures over a Weierstrass curve.\n *\n * * lowS: (default: true) whether produced / verified signatures occupy low half of ecdsaOpts.p. Prevents malleability.\n * * hmac: (default: noble-hashes hmac) function, would be used to init hmac-drbg for k generation.\n * * randomBytes: (default: webcrypto os-level CSPRNG) custom method for fetching secure randomness.\n * * bits2int, bits2int_modN: used in sigs, sometimes overridden by curves\n */\nexport type ECDSAOpts = Partial<{\n  lowS: boolean;\n  hmac: HmacFnSync;\n  randomBytes: (bytesLength?: number) => Uint8Array;\n  bits2int: (bytes: Uint8Array) => bigint;\n  bits2int_modN: (bytes: Uint8Array) => bigint;\n}>;\n\n/**\n * Elliptic Curve Diffie-Hellman interface.\n * Provides keygen, secret-to-public conversion, calculating shared secrets.\n */\nexport interface ECDH {\n  keygen: (seed?: Uint8Array) => { secretKey: Uint8Array; publicKey: Uint8Array };\n  getPublicKey: (secretKey: PrivKey, isCompressed?: boolean) => Uint8Array;\n  getSharedSecret: (secretKeyA: PrivKey, publicKeyB: Hex, isCompressed?: boolean) => Uint8Array;\n  Point: WeierstrassPointCons<bigint>;\n  utils: {\n    isValidSecretKey: (secretKey: PrivKey) => boolean;\n    isValidPublicKey: (publicKey: Uint8Array, isCompressed?: boolean) => boolean;\n    randomSecretKey: (seed?: Uint8Array) => Uint8Array;\n    /** @deprecated use `randomSecretKey` */\n    randomPrivateKey: (seed?: Uint8Array) => Uint8Array;\n    /** @deprecated use `isValidSecretKey` */\n    isValidPrivateKey: (secretKey: PrivKey) => boolean;\n    /** @deprecated use `Point.Fn.fromBytes()` */\n    normPrivateKeyToScalar: (key: PrivKey) => bigint;\n    /** @deprecated use `point.precompute()` */\n    precompute: (windowSize?: number, point?: WeierstrassPoint<bigint>) => WeierstrassPoint<bigint>;\n  };\n  lengths: CurveLengths;\n}\n\n/**\n * ECDSA interface.\n * Only supported for prime fields, not Fp2 (extension fields).\n */\nexport interface ECDSA extends ECDH {\n  sign: (message: Hex, secretKey: PrivKey, opts?: ECDSASignOpts) => ECDSASigRecovered;\n  verify: (\n    signature: Uint8Array,\n    message: Uint8Array,\n    publicKey: Uint8Array,\n    opts?: ECDSAVerifyOpts\n  ) => boolean;\n  recoverPublicKey(signature: Uint8Array, message: Uint8Array, opts?: ECDSARecoverOpts): Uint8Array;\n  Signature: ECDSASignatureCons;\n}\nexport class DERErr extends Error {\n  constructor(m = '') {\n    super(m);\n  }\n}\nexport type IDER = {\n  // asn.1 DER encoding utils\n  Err: typeof DERErr;\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag: number, data: string) => string;\n    // v - value, l - left bytes (unparsed)\n    decode(tag: number, data: Uint8Array): { v: Uint8Array; l: Uint8Array };\n  };\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num: bigint): string;\n    decode(data: Uint8Array): bigint;\n  };\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint };\n  hexFromSig(sig: { r: bigint; s: bigint }): string;\n};\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nexport const DER: IDER = {\n  // asn.1 DER encoding utils\n  Err: DERErr,\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag: number, data: string): string => {\n      const { Err: E } = DER;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length & 1) throw new E('tlv.encode: unpadded data');\n      const dataLen = data.length / 2;\n      const len = numberToHexUnpadded(dataLen);\n      if ((len.length / 2) & 0b1000_0000) throw new E('tlv.encode: long form length too big');\n      // length of length with long form flag\n      const lenLen = dataLen > 127 ? numberToHexUnpadded((len.length / 2) | 0b1000_0000) : '';\n      const t = numberToHexUnpadded(tag);\n      return t + lenLen + len + data;\n    },\n    // v - value, l - left bytes (unparsed)\n    decode(tag: number, data: Uint8Array): { v: Uint8Array; l: Uint8Array } {\n      const { Err: E } = DER;\n      let pos = 0;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length < 2 || data[pos++] !== tag) throw new E('tlv.decode: wrong tlv');\n      const first = data[pos++];\n      const isLong = !!(first & 0b1000_0000); // First bit of first length byte is flag for short/long form\n      let length = 0;\n      if (!isLong) length = first;\n      else {\n        // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n        const lenLen = first & 0b0111_1111;\n        if (!lenLen) throw new E('tlv.decode(long): indefinite length not supported');\n        if (lenLen > 4) throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n        const lengthBytes = data.subarray(pos, pos + lenLen);\n        if (lengthBytes.length !== lenLen) throw new E('tlv.decode: length bytes not complete');\n        if (lengthBytes[0] === 0) throw new E('tlv.decode(long): zero leftmost byte');\n        for (const b of lengthBytes) length = (length << 8) | b;\n        pos += lenLen;\n        if (length < 128) throw new E('tlv.decode(long): not minimal encoding');\n      }\n      const v = data.subarray(pos, pos + length);\n      if (v.length !== length) throw new E('tlv.decode: wrong value length');\n      return { v, l: data.subarray(pos + length) };\n    },\n  },\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num: bigint): string {\n      const { Err: E } = DER;\n      if (num < _0n) throw new E('integer: negative integers are not allowed');\n      let hex = numberToHexUnpadded(num);\n      // Pad with zero byte if negative flag is present\n      if (Number.parseInt(hex[0], 16) & 0b1000) hex = '00' + hex;\n      if (hex.length & 1) throw new E('unexpected DER parsing assertion: unpadded hex');\n      return hex;\n    },\n    decode(data: Uint8Array): bigint {\n      const { Err: E } = DER;\n      if (data[0] & 0b1000_0000) throw new E('invalid signature integer: negative');\n      if (data[0] === 0x00 && !(data[1] & 0b1000_0000))\n        throw new E('invalid signature integer: unnecessary leading zero');\n      return bytesToNumberBE(data);\n    },\n  },\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint } {\n    // parse DER signature\n    const { Err: E, _int: int, _tlv: tlv } = DER;\n    const data = ensureBytes('signature', hex);\n    const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n    if (seqLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n    const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n    if (sLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    return { r: int.decode(rBytes), s: int.decode(sBytes) };\n  },\n  hexFromSig(sig: { r: bigint; s: bigint }): string {\n    const { _tlv: tlv, _int: int } = DER;\n    const rs = tlv.encode(0x02, int.encode(sig.r));\n    const ss = tlv.encode(0x02, int.encode(sig.s));\n    const seq = rs + ss;\n    return tlv.encode(0x30, seq);\n  },\n};\n\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\n\nexport function _normFnElement(Fn: IField<bigint>, key: PrivKey): bigint {\n  const { BYTES: expected } = Fn;\n  let num: bigint;\n  if (typeof key === 'bigint') {\n    num = key;\n  } else {\n    let bytes = ensureBytes('private key', key);\n    try {\n      num = Fn.fromBytes(bytes);\n    } catch (error) {\n      throw new Error(`invalid private key: expected ui8a of size ${expected}, got ${typeof key}`);\n    }\n  }\n  if (!Fn.isValidNot0(num)) throw new Error('invalid private key: out of range [1..N-1]');\n  return num;\n}\n\n/**\n * Creates weierstrass Point constructor, based on specified curve options.\n *\n * @example\n```js\nconst opts = {\n  p: BigInt('0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff'),\n  n: BigInt('0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551'),\n  h: BigInt(1),\n  a: BigInt('0xffffffff00000001000000000000000000000000fffffffffffffffffffffffc'),\n  b: BigInt('0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b'),\n  Gx: BigInt('0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296'),\n  Gy: BigInt('0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5'),\n};\nconst p256_Point = weierstrass(opts);\n```\n */\nexport function weierstrassN<T>(\n  params: WeierstrassOpts<T>,\n  extraOpts: WeierstrassExtraOpts<T> = {}\n): WeierstrassPointCons<T> {\n  const validated = _createCurveFields('weierstrass', params, extraOpts);\n  const { Fp, Fn } = validated;\n  let CURVE = validated.CURVE as WeierstrassOpts<T>;\n  const { h: cofactor, n: CURVE_ORDER } = CURVE;\n  _validateObject(\n    extraOpts,\n    {},\n    {\n      allowInfinityPoint: 'boolean',\n      clearCofactor: 'function',\n      isTorsionFree: 'function',\n      fromBytes: 'function',\n      toBytes: 'function',\n      endo: 'object',\n      wrapPrivateKey: 'boolean',\n    }\n  );\n\n  const { endo } = extraOpts;\n  if (endo) {\n    // validateObject(endo, { beta: 'bigint', splitScalar: 'function' });\n    if (!Fp.is0(CURVE.a) || typeof endo.beta !== 'bigint' || !Array.isArray(endo.basises)) {\n      throw new Error('invalid endo: expected \"beta\": bigint and \"basises\": array');\n    }\n  }\n\n  const lengths = getWLengths(Fp, Fn);\n\n  function assertCompressionIsSupported() {\n    if (!Fp.isOdd) throw new Error('compression is not supported: Field does not have .isOdd()');\n  }\n\n  // Implements IEEE P1363 point encoding\n  function pointToBytes(\n    _c: WeierstrassPointCons<T>,\n    point: WeierstrassPoint<T>,\n    isCompressed: boolean\n  ): Uint8Array {\n    const { x, y } = point.toAffine();\n    const bx = Fp.toBytes(x);\n    abool(isCompressed, 'isCompressed');\n    if (isCompressed) {\n      assertCompressionIsSupported();\n      const hasEvenY = !Fp.isOdd!(y);\n      return concatBytes(pprefix(hasEvenY), bx);\n    } else {\n      return concatBytes(Uint8Array.of(0x04), bx, Fp.toBytes(y));\n    }\n  }\n  function pointFromBytes(bytes: Uint8Array) {\n    abytes(bytes, undefined, 'Point');\n    const { publicKey: comp, publicKeyUncompressed: uncomp } = lengths; // e.g. for 32-byte: 33, 65\n    const length = bytes.length;\n    const head = bytes[0];\n    const tail = bytes.subarray(1);\n    // No actual validation is done here: use .assertValidity()\n    if (length === comp && (head === 0x02 || head === 0x03)) {\n      const x = Fp.fromBytes(tail);\n      if (!Fp.isValid(x)) throw new Error('bad point: is not on curve, wrong x');\n      const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n      let y: T;\n      try {\n        y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n      } catch (sqrtError) {\n        const err = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n        throw new Error('bad point: is not on curve, sqrt error' + err);\n      }\n      assertCompressionIsSupported();\n      const isYOdd = Fp.isOdd!(y); // (y & _1n) === _1n;\n      const isHeadOdd = (head & 1) === 1; // ECDSA-specific\n      if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n      return { x, y };\n    } else if (length === uncomp && head === 0x04) {\n      // TODO: more checks\n      const L = Fp.BYTES;\n      const x = Fp.fromBytes(tail.subarray(0, L));\n      const y = Fp.fromBytes(tail.subarray(L, L * 2));\n      if (!isValidXY(x, y)) throw new Error('bad point: is not on curve');\n      return { x, y };\n    } else {\n      throw new Error(\n        `bad point: got length ${length}, expected compressed=${comp} or uncompressed=${uncomp}`\n      );\n    }\n  }\n\n  const encodePoint = extraOpts.toBytes || pointToBytes;\n  const decodePoint = extraOpts.fromBytes || pointFromBytes;\n  function weierstrassEquation(x: T): T {\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x² * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, CURVE.a)), CURVE.b); // x³ + a * x + b\n  }\n\n  // TODO: move top-level\n  /** Checks whether equation holds for given x, y: y² == x³ + ax + b */\n  function isValidXY(x: T, y: T): boolean {\n    const left = Fp.sqr(y); // y²\n    const right = weierstrassEquation(x); // x³ + ax + b\n    return Fp.eql(left, right);\n  }\n\n  // Validate whether the passed curve params are valid.\n  // Test 1: equation y² = x³ + ax + b should work for generator point.\n  if (!isValidXY(CURVE.Gx, CURVE.Gy)) throw new Error('bad curve params: generator point');\n\n  // Test 2: discriminant Δ part should be non-zero: 4a³ + 27b² != 0.\n  // Guarantees curve is genus-1, smooth (non-singular).\n  const _4a3 = Fp.mul(Fp.pow(CURVE.a, _3n), _4n);\n  const _27b2 = Fp.mul(Fp.sqr(CURVE.b), BigInt(27));\n  if (Fp.is0(Fp.add(_4a3, _27b2))) throw new Error('bad curve params: a or b');\n\n  /** Asserts coordinate is valid: 0 <= n < Fp.ORDER. */\n  function acoord(title: string, n: T, banZero = false) {\n    if (!Fp.isValid(n) || (banZero && Fp.is0(n))) throw new Error(`bad point coordinate ${title}`);\n    return n;\n  }\n\n  function aprjpoint(other: unknown) {\n    if (!(other instanceof Point)) throw new Error('ProjectivePoint expected');\n  }\n\n  function splitEndoScalarN(k: bigint) {\n    if (!endo || !endo.basises) throw new Error('no endo');\n    return _splitEndoScalar(k, endo.basises, Fn.ORDER);\n  }\n\n  // Memoized toAffine / validity check. They are heavy. Points are immutable.\n\n  // Converts Projective point to affine (x, y) coordinates.\n  // Can accept precomputed Z^-1 - for example, from invertBatch.\n  // (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n  const toAffineMemo = memoized((p: Point, iz?: T): AffinePoint<T> => {\n    const { X, Y, Z } = p;\n    // Fast-path for normalized points\n    if (Fp.eql(Z, Fp.ONE)) return { x: X, y: Y };\n    const is0 = p.is0();\n    // If invZ was 0, we return zero point. However we still want to execute\n    // all operations, so we replace invZ with a random number, 1.\n    if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(Z);\n    const x = Fp.mul(X, iz);\n    const y = Fp.mul(Y, iz);\n    const zz = Fp.mul(Z, iz);\n    if (is0) return { x: Fp.ZERO, y: Fp.ZERO };\n    if (!Fp.eql(zz, Fp.ONE)) throw new Error('invZ was invalid');\n    return { x, y };\n  });\n  // NOTE: on exception this will crash 'cached' and no value will be set.\n  // Otherwise true will be return\n  const assertValidMemo = memoized((p: Point) => {\n    if (p.is0()) {\n      // (0, 1, 0) aka ZERO is invalid in most contexts.\n      // In BLS, ZERO can be serialized, so we allow it.\n      // (0, 0, 0) is invalid representation of ZERO.\n      if (extraOpts.allowInfinityPoint && !Fp.is0(p.Y)) return;\n      throw new Error('bad point: ZERO');\n    }\n    // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n    const { x, y } = p.toAffine();\n    if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error('bad point: x or y not field elements');\n    if (!isValidXY(x, y)) throw new Error('bad point: equation left != right');\n    if (!p.isTorsionFree()) throw new Error('bad point: not in prime-order subgroup');\n    return true;\n  });\n\n  function finishEndo(\n    endoBeta: EndomorphismOpts['beta'],\n    k1p: Point,\n    k2p: Point,\n    k1neg: boolean,\n    k2neg: boolean\n  ) {\n    k2p = new Point(Fp.mul(k2p.X, endoBeta), k2p.Y, k2p.Z);\n    k1p = negateCt(k1neg, k1p);\n    k2p = negateCt(k2neg, k2p);\n    return k1p.add(k2p);\n  }\n\n  /**\n   * Projective Point works in 3d / projective (homogeneous) coordinates:(X, Y, Z) ∋ (x=X/Z, y=Y/Z).\n   * Default Point works in 2d / affine coordinates: (x, y).\n   * We're doing calculations in projective, because its operations don't require costly inversion.\n   */\n  class Point implements WeierstrassPoint<T> {\n    // base / generator point\n    static readonly BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    // zero / infinity / identity point\n    static readonly ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO); // 0, 1, 0\n    // math field\n    static readonly Fp = Fp;\n    // scalar field\n    static readonly Fn = Fn;\n\n    readonly X: T;\n    readonly Y: T;\n    readonly Z: T;\n\n    /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n    constructor(X: T, Y: T, Z: T) {\n      this.X = acoord('x', X);\n      this.Y = acoord('y', Y, true);\n      this.Z = acoord('z', Z);\n      Object.freeze(this);\n    }\n\n    static CURVE(): WeierstrassOpts<T> {\n      return CURVE;\n    }\n\n    /** Does NOT validate if the point is valid. Use `.assertValidity()`. */\n    static fromAffine(p: AffinePoint<T>): Point {\n      const { x, y } = p || {};\n      if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error('invalid affine point');\n      if (p instanceof Point) throw new Error('projective point not allowed');\n      // (0, 0) would've produced (0, 0, 1) - instead, we need (0, 1, 0)\n      if (Fp.is0(x) && Fp.is0(y)) return Point.ZERO;\n      return new Point(x, y, Fp.ONE);\n    }\n\n    static fromBytes(bytes: Uint8Array): Point {\n      const P = Point.fromAffine(decodePoint(abytes(bytes, undefined, 'point')));\n      P.assertValidity();\n      return P;\n    }\n    static fromHex(hex: Hex): Point {\n      return Point.fromBytes(ensureBytes('pointHex', hex));\n    }\n\n    get x(): T {\n      return this.toAffine().x;\n    }\n    get y(): T {\n      return this.toAffine().y;\n    }\n\n    /**\n     *\n     * @param windowSize\n     * @param isLazy true will defer table computation until the first multiplication\n     * @returns\n     */\n    precompute(windowSize: number = 8, isLazy = true): Point {\n      wnaf.createCache(this, windowSize);\n      if (!isLazy) this.multiply(_3n); // random number\n      return this;\n    }\n\n    // TODO: return `this`\n    /** A point on curve is valid if it conforms to equation. */\n    assertValidity(): void {\n      assertValidMemo(this);\n    }\n\n    hasEvenY(): boolean {\n      const { y } = this.toAffine();\n      if (!Fp.isOdd) throw new Error(\"Field doesn't support isOdd\");\n      return !Fp.isOdd(y);\n    }\n\n    /** Compare one point to another. */\n    equals(other: Point): boolean {\n      aprjpoint(other);\n      const { X: X1, Y: Y1, Z: Z1 } = this;\n      const { X: X2, Y: Y2, Z: Z2 } = other;\n      const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n      const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n      return U1 && U2;\n    }\n\n    /** Flips point to one corresponding to (x, -y) in Affine coordinates. */\n    negate(): Point {\n      return new Point(this.X, Fp.neg(this.Y), this.Z);\n    }\n\n    // Renes-Costello-Batina exception-free doubling formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 3\n    // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n    double() {\n      const { a, b } = CURVE;\n      const b3 = Fp.mul(b, _3n);\n      const { X: X1, Y: Y1, Z: Z1 } = this;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      let t0 = Fp.mul(X1, X1); // step 1\n      let t1 = Fp.mul(Y1, Y1);\n      let t2 = Fp.mul(Z1, Z1);\n      let t3 = Fp.mul(X1, Y1);\n      t3 = Fp.add(t3, t3); // step 5\n      Z3 = Fp.mul(X1, Z1);\n      Z3 = Fp.add(Z3, Z3);\n      X3 = Fp.mul(a, Z3);\n      Y3 = Fp.mul(b3, t2);\n      Y3 = Fp.add(X3, Y3); // step 10\n      X3 = Fp.sub(t1, Y3);\n      Y3 = Fp.add(t1, Y3);\n      Y3 = Fp.mul(X3, Y3);\n      X3 = Fp.mul(t3, X3);\n      Z3 = Fp.mul(b3, Z3); // step 15\n      t2 = Fp.mul(a, t2);\n      t3 = Fp.sub(t0, t2);\n      t3 = Fp.mul(a, t3);\n      t3 = Fp.add(t3, Z3);\n      Z3 = Fp.add(t0, t0); // step 20\n      t0 = Fp.add(Z3, t0);\n      t0 = Fp.add(t0, t2);\n      t0 = Fp.mul(t0, t3);\n      Y3 = Fp.add(Y3, t0);\n      t2 = Fp.mul(Y1, Z1); // step 25\n      t2 = Fp.add(t2, t2);\n      t0 = Fp.mul(t2, t3);\n      X3 = Fp.sub(X3, t0);\n      Z3 = Fp.mul(t2, t1);\n      Z3 = Fp.add(Z3, Z3); // step 30\n      Z3 = Fp.add(Z3, Z3);\n      return new Point(X3, Y3, Z3);\n    }\n\n    // Renes-Costello-Batina exception-free addition formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 1\n    // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n    add(other: Point): Point {\n      aprjpoint(other);\n      const { X: X1, Y: Y1, Z: Z1 } = this;\n      const { X: X2, Y: Y2, Z: Z2 } = other;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      const a = CURVE.a;\n      const b3 = Fp.mul(CURVE.b, _3n);\n      let t0 = Fp.mul(X1, X2); // step 1\n      let t1 = Fp.mul(Y1, Y2);\n      let t2 = Fp.mul(Z1, Z2);\n      let t3 = Fp.add(X1, Y1);\n      let t4 = Fp.add(X2, Y2); // step 5\n      t3 = Fp.mul(t3, t4);\n      t4 = Fp.add(t0, t1);\n      t3 = Fp.sub(t3, t4);\n      t4 = Fp.add(X1, Z1);\n      let t5 = Fp.add(X2, Z2); // step 10\n      t4 = Fp.mul(t4, t5);\n      t5 = Fp.add(t0, t2);\n      t4 = Fp.sub(t4, t5);\n      t5 = Fp.add(Y1, Z1);\n      X3 = Fp.add(Y2, Z2); // step 15\n      t5 = Fp.mul(t5, X3);\n      X3 = Fp.add(t1, t2);\n      t5 = Fp.sub(t5, X3);\n      Z3 = Fp.mul(a, t4);\n      X3 = Fp.mul(b3, t2); // step 20\n      Z3 = Fp.add(X3, Z3);\n      X3 = Fp.sub(t1, Z3);\n      Z3 = Fp.add(t1, Z3);\n      Y3 = Fp.mul(X3, Z3);\n      t1 = Fp.add(t0, t0); // step 25\n      t1 = Fp.add(t1, t0);\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.mul(b3, t4);\n      t1 = Fp.add(t1, t2);\n      t2 = Fp.sub(t0, t2); // step 30\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.add(t4, t2);\n      t0 = Fp.mul(t1, t4);\n      Y3 = Fp.add(Y3, t0);\n      t0 = Fp.mul(t5, t4); // step 35\n      X3 = Fp.mul(t3, X3);\n      X3 = Fp.sub(X3, t0);\n      t0 = Fp.mul(t3, t1);\n      Z3 = Fp.mul(t5, Z3);\n      Z3 = Fp.add(Z3, t0); // step 40\n      return new Point(X3, Y3, Z3);\n    }\n\n    subtract(other: Point) {\n      return this.add(other.negate());\n    }\n\n    is0(): boolean {\n      return this.equals(Point.ZERO);\n    }\n\n    /**\n     * Constant time multiplication.\n     * Uses wNAF method. Windowed method may be 10% faster,\n     * but takes 2x longer to generate and consumes 2x memory.\n     * Uses precomputes when available.\n     * Uses endomorphism for Koblitz curves.\n     * @param scalar by which the point would be multiplied\n     * @returns New point\n     */\n    multiply(scalar: bigint): Point {\n      const { endo } = extraOpts;\n      if (!Fn.isValidNot0(scalar)) throw new Error('invalid scalar: out of range'); // 0 is invalid\n      let point: Point, fake: Point; // Fake point is used to const-time mult\n      const mul = (n: bigint) => wnaf.cached(this, n, (p) => normalizeZ(Point, p));\n      /** See docs for {@link EndomorphismOpts} */\n      if (endo) {\n        const { k1neg, k1, k2neg, k2 } = splitEndoScalarN(scalar);\n        const { p: k1p, f: k1f } = mul(k1);\n        const { p: k2p, f: k2f } = mul(k2);\n        fake = k1f.add(k2f);\n        point = finishEndo(endo.beta, k1p, k2p, k1neg, k2neg);\n      } else {\n        const { p, f } = mul(scalar);\n        point = p;\n        fake = f;\n      }\n      // Normalize `z` for both points, but return only real one\n      return normalizeZ(Point, [point, fake])[0];\n    }\n\n    /**\n     * Non-constant-time multiplication. Uses double-and-add algorithm.\n     * It's faster, but should only be used when you don't care about\n     * an exposed secret key e.g. sig verification, which works over *public* keys.\n     */\n    multiplyUnsafe(sc: bigint): Point {\n      const { endo } = extraOpts;\n      const p = this as Point;\n      if (!Fn.isValid(sc)) throw new Error('invalid scalar: out of range'); // 0 is valid\n      if (sc === _0n || p.is0()) return Point.ZERO;\n      if (sc === _1n) return p; // fast-path\n      if (wnaf.hasCache(this)) return this.multiply(sc);\n      if (endo) {\n        const { k1neg, k1, k2neg, k2 } = splitEndoScalarN(sc);\n        const { p1, p2 } = mulEndoUnsafe(Point, p, k1, k2); // 30% faster vs wnaf.unsafe\n        return finishEndo(endo.beta, p1, p2, k1neg, k2neg);\n      } else {\n        return wnaf.unsafe(p, sc);\n      }\n    }\n\n    multiplyAndAddUnsafe(Q: Point, a: bigint, b: bigint): Point | undefined {\n      const sum = this.multiplyUnsafe(a).add(Q.multiplyUnsafe(b));\n      return sum.is0() ? undefined : sum;\n    }\n\n    /**\n     * Converts Projective point to affine (x, y) coordinates.\n     * @param invertedZ Z^-1 (inverted zero) - optional, precomputation is useful for invertBatch\n     */\n    toAffine(invertedZ?: T): AffinePoint<T> {\n      return toAffineMemo(this, invertedZ);\n    }\n\n    /**\n     * Checks whether Point is free of torsion elements (is in prime subgroup).\n     * Always torsion-free for cofactor=1 curves.\n     */\n    isTorsionFree(): boolean {\n      const { isTorsionFree } = extraOpts;\n      if (cofactor === _1n) return true;\n      if (isTorsionFree) return isTorsionFree(Point, this);\n      return wnaf.unsafe(this, CURVE_ORDER).is0();\n    }\n\n    clearCofactor(): Point {\n      const { clearCofactor } = extraOpts;\n      if (cofactor === _1n) return this; // Fast-path\n      if (clearCofactor) return clearCofactor(Point, this) as Point;\n      return this.multiplyUnsafe(cofactor);\n    }\n\n    isSmallOrder(): boolean {\n      // can we use this.clearCofactor()?\n      return this.multiplyUnsafe(cofactor).is0();\n    }\n\n    toBytes(isCompressed = true): Uint8Array {\n      abool(isCompressed, 'isCompressed');\n      this.assertValidity();\n      return encodePoint(Point, this, isCompressed);\n    }\n\n    toHex(isCompressed = true): string {\n      return bytesToHex(this.toBytes(isCompressed));\n    }\n\n    toString() {\n      return `<Point ${this.is0() ? 'ZERO' : this.toHex()}>`;\n    }\n\n    // TODO: remove\n    get px(): T {\n      return this.X;\n    }\n    get py(): T {\n      return this.X;\n    }\n    get pz(): T {\n      return this.Z;\n    }\n    toRawBytes(isCompressed = true): Uint8Array {\n      return this.toBytes(isCompressed);\n    }\n    _setWindowSize(windowSize: number) {\n      this.precompute(windowSize);\n    }\n    static normalizeZ(points: Point[]): Point[] {\n      return normalizeZ(Point, points);\n    }\n    static msm(points: Point[], scalars: bigint[]): Point {\n      return pippenger(Point, Fn, points, scalars);\n    }\n    static fromPrivateKey(privateKey: PrivKey) {\n      return Point.BASE.multiply(_normFnElement(Fn, privateKey));\n    }\n  }\n  const bits = Fn.BITS;\n  const wnaf = new wNAF(Point, extraOpts.endo ? Math.ceil(bits / 2) : bits);\n  Point.BASE.precompute(8); // Enable precomputes. Slows down first publicKey computation by 20ms.\n  return Point;\n}\n\n/** Methods of ECDSA signature instance. */\nexport interface ECDSASignature {\n  readonly r: bigint;\n  readonly s: bigint;\n  readonly recovery?: number;\n  addRecoveryBit(recovery: number): ECDSASigRecovered;\n  hasHighS(): boolean;\n  toBytes(format?: string): Uint8Array;\n  toHex(format?: string): string;\n\n  /** @deprecated */\n  assertValidity(): void;\n  /** @deprecated */\n  normalizeS(): ECDSASignature;\n  /** @deprecated use standalone method `curve.recoverPublicKey(sig.toBytes('recovered'), msg)` */\n  recoverPublicKey(msgHash: Hex): WeierstrassPoint<bigint>;\n  /** @deprecated use `.toBytes('compact')` */\n  toCompactRawBytes(): Uint8Array;\n  /** @deprecated use `.toBytes('compact')` */\n  toCompactHex(): string;\n  /** @deprecated use `.toBytes('der')` */\n  toDERRawBytes(): Uint8Array;\n  /** @deprecated use `.toBytes('der')` */\n  toDERHex(): string;\n}\nexport type ECDSASigRecovered = ECDSASignature & {\n  readonly recovery: number;\n};\n/** Methods of ECDSA signature constructor. */\nexport type ECDSASignatureCons = {\n  new (r: bigint, s: bigint, recovery?: number): ECDSASignature;\n  fromBytes(bytes: Uint8Array, format?: ECDSASigFormat): ECDSASignature;\n  fromHex(hex: string, format?: ECDSASigFormat): ECDSASignature;\n\n  /** @deprecated use `.fromBytes(bytes, 'compact')` */\n  fromCompact(hex: Hex): ECDSASignature;\n  /** @deprecated use `.fromBytes(bytes, 'der')` */\n  fromDER(hex: Hex): ECDSASignature;\n};\n\n// Points start with byte 0x02 when y is even; otherwise 0x03\nfunction pprefix(hasEvenY: boolean): Uint8Array {\n  return Uint8Array.of(hasEvenY ? 0x02 : 0x03);\n}\n\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio<T>(\n  Fp: IField<T>,\n  Z: T\n): (u: T, v: T) => { isValid: boolean; value: T } {\n  // Generic implementation\n  const q = Fp.ORDER;\n  let l = _0n;\n  for (let o = q - _1n; o % _2n === _0n; o /= _2n) l += _1n;\n  const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n  // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n  // 2n ** c1 == 2n << (c1-1)\n  const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n  const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n  const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n  const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n  const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n  const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n  const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n  const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n  let sqrtRatio = (u: T, v: T): { isValid: boolean; value: T } => {\n    let tv1 = c6; // 1. tv1 = c6\n    let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n    let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n    tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n    let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n    tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n    tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n    tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n    tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n    let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n    tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n    let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n    tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n    tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n    tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n    tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n    // 17. for i in (c1, c1 - 1, ..., 2):\n    for (let i = c1; i > _1n; i--) {\n      let tv5 = i - _2n; // 18.    tv5 = i - 2\n      tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n      let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n      const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n      tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n      tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n      tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n      tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n      tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n    }\n    return { isValid: isQR, value: tv3 };\n  };\n  if (Fp.ORDER % _4n === _3n) {\n    // sqrt_ratio_3mod4(u, v)\n    const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n    const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n    sqrtRatio = (u: T, v: T) => {\n      let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n      const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n      tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n      let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n      y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n      const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n      const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n      const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n      let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n      return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n    };\n  }\n  // No curves uses that\n  // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n  return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU<T>(\n  Fp: IField<T>,\n  opts: {\n    A: T;\n    B: T;\n    Z: T;\n  }\n): (u: T) => { x: T; y: T } {\n  validateField(Fp);\n  const { A, B, Z } = opts;\n  if (!Fp.isValid(A) || !Fp.isValid(B) || !Fp.isValid(Z))\n    throw new Error('mapToCurveSimpleSWU: invalid opts');\n  const sqrtRatio = SWUFpSqrtRatio(Fp, Z);\n  if (!Fp.isOdd) throw new Error('Field does not have .isOdd()');\n  // Input: u, an element of F.\n  // Output: (x, y), a point on E.\n  return (u: T): { x: T; y: T } => {\n    // prettier-ignore\n    let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n    tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, Z); // 2.  tv1 = Z * tv1\n    tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n    tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n    tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n    tv3 = Fp.mul(tv3, B); // 6.  tv3 = B * tv3\n    tv4 = Fp.cmov(Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n    tv4 = Fp.mul(tv4, A); // 8.  tv4 = A * tv4\n    tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n    tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n    tv5 = Fp.mul(tv6, A); // 11. tv5 = A * tv6\n    tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n    tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n    tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n    tv5 = Fp.mul(tv6, B); // 15. tv5 = B * tv6\n    tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n    x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n    const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n    y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n    y = Fp.mul(y, value); // 20.   y = y * y1\n    x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n    y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n    const e1 = Fp.isOdd!(u) === Fp.isOdd!(y); // 23.  e1 = sgn0(u) == sgn0(y)\n    y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n    const tv4_inv = FpInvertBatch(Fp, [tv4], true)[0];\n    x = Fp.mul(x, tv4_inv); // 25.   x = x / tv4\n    return { x, y };\n  };\n}\n\nfunction getWLengths<T>(Fp: IField<T>, Fn: IField<bigint>) {\n  return {\n    secretKey: Fn.BYTES,\n    publicKey: 1 + Fp.BYTES,\n    publicKeyUncompressed: 1 + 2 * Fp.BYTES,\n    publicKeyHasPrefix: true,\n    signature: 2 * Fn.BYTES,\n  };\n}\n\n/**\n * Sometimes users only need getPublicKey, getSharedSecret, and secret key handling.\n * This helper ensures no signature functionality is present. Less code, smaller bundle size.\n */\nexport function ecdh(\n  Point: WeierstrassPointCons<bigint>,\n  ecdhOpts: { randomBytes?: (bytesLength?: number) => Uint8Array } = {}\n): ECDH {\n  const { Fn } = Point;\n  const randomBytes_ = ecdhOpts.randomBytes || randomBytesWeb;\n  const lengths = Object.assign(getWLengths(Point.Fp, Fn), { seed: getMinHashLength(Fn.ORDER) });\n\n  function isValidSecretKey(secretKey: PrivKey) {\n    try {\n      return !!_normFnElement(Fn, secretKey);\n    } catch (error) {\n      return false;\n    }\n  }\n\n  function isValidPublicKey(publicKey: Uint8Array, isCompressed?: boolean): boolean {\n    const { publicKey: comp, publicKeyUncompressed } = lengths;\n    try {\n      const l = publicKey.length;\n      if (isCompressed === true && l !== comp) return false;\n      if (isCompressed === false && l !== publicKeyUncompressed) return false;\n      return !!Point.fromBytes(publicKey);\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Produces cryptographically secure secret key from random of size\n   * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n   */\n  function randomSecretKey(seed = randomBytes_(lengths.seed)): Uint8Array {\n    return mapHashToField(abytes(seed, lengths.seed, 'seed'), Fn.ORDER);\n  }\n\n  /**\n   * Computes public key for a secret key. Checks for validity of the secret key.\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns Public key, full when isCompressed=false; short when isCompressed=true\n   */\n  function getPublicKey(secretKey: PrivKey, isCompressed = true): Uint8Array {\n    return Point.BASE.multiply(_normFnElement(Fn, secretKey)).toBytes(isCompressed);\n  }\n\n  function keygen(seed?: Uint8Array) {\n    const secretKey = randomSecretKey(seed);\n    return { secretKey, publicKey: getPublicKey(secretKey) };\n  }\n\n  /**\n   * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n   */\n  function isProbPub(item: PrivKey | PubKey): boolean | undefined {\n    if (typeof item === 'bigint') return false;\n    if (item instanceof Point) return true;\n    const { secretKey, publicKey, publicKeyUncompressed } = lengths;\n    if (Fn.allowedLengths || secretKey === publicKey) return undefined;\n    const l = ensureBytes('key', item).length;\n    return l === publicKey || l === publicKeyUncompressed;\n  }\n\n  /**\n   * ECDH (Elliptic Curve Diffie Hellman).\n   * Computes shared public key from secret key A and public key B.\n   * Checks: 1) secret key validity 2) shared key is on-curve.\n   * Does NOT hash the result.\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns shared public key\n   */\n  function getSharedSecret(secretKeyA: PrivKey, publicKeyB: Hex, isCompressed = true): Uint8Array {\n    if (isProbPub(secretKeyA) === true) throw new Error('first arg must be private key');\n    if (isProbPub(publicKeyB) === false) throw new Error('second arg must be public key');\n    const s = _normFnElement(Fn, secretKeyA);\n    const b = Point.fromHex(publicKeyB); // checks for being on-curve\n    return b.multiply(s).toBytes(isCompressed);\n  }\n\n  const utils = {\n    isValidSecretKey,\n    isValidPublicKey,\n    randomSecretKey,\n\n    // TODO: remove\n    isValidPrivateKey: isValidSecretKey,\n    randomPrivateKey: randomSecretKey,\n    normPrivateKeyToScalar: (key: PrivKey) => _normFnElement(Fn, key),\n    precompute(windowSize = 8, point = Point.BASE): WeierstrassPoint<bigint> {\n      return point.precompute(windowSize, false);\n    },\n  };\n\n  return Object.freeze({ getPublicKey, getSharedSecret, keygen, Point, utils, lengths });\n}\n\n/**\n * Creates ECDSA signing interface for given elliptic curve `Point` and `hash` function.\n * We need `hash` for 2 features:\n * 1. Message prehash-ing. NOT used if `sign` / `verify` are called with `prehash: false`\n * 2. k generation in `sign`, using HMAC-drbg(hash)\n *\n * ECDSAOpts are only rarely needed.\n *\n * @example\n * ```js\n * const p256_Point = weierstrass(...);\n * const p256_sha256 = ecdsa(p256_Point, sha256);\n * const p256_sha224 = ecdsa(p256_Point, sha224);\n * const p256_sha224_r = ecdsa(p256_Point, sha224, { randomBytes: (length) => { ... } });\n * ```\n */\nexport function ecdsa(\n  Point: WeierstrassPointCons<bigint>,\n  hash: CHash,\n  ecdsaOpts: ECDSAOpts = {}\n): ECDSA {\n  ahash(hash);\n  _validateObject(\n    ecdsaOpts,\n    {},\n    {\n      hmac: 'function',\n      lowS: 'boolean',\n      randomBytes: 'function',\n      bits2int: 'function',\n      bits2int_modN: 'function',\n    }\n  );\n\n  const randomBytes = ecdsaOpts.randomBytes || randomBytesWeb;\n  const hmac: HmacFnSync =\n    ecdsaOpts.hmac ||\n    (((key, ...msgs) => nobleHmac(hash, key, concatBytes(...msgs))) satisfies HmacFnSync);\n\n  const { Fp, Fn } = Point;\n  const { ORDER: CURVE_ORDER, BITS: fnBits } = Fn;\n  const { keygen, getPublicKey, getSharedSecret, utils, lengths } = ecdh(Point, ecdsaOpts);\n  const defaultSigOpts: Required<ECDSASignOpts> = {\n    prehash: false,\n    lowS: typeof ecdsaOpts.lowS === 'boolean' ? ecdsaOpts.lowS : false,\n    format: undefined as any, //'compact' as ECDSASigFormat,\n    extraEntropy: false,\n  };\n  const defaultSigOpts_format = 'compact';\n\n  function isBiggerThanHalfOrder(number: bigint) {\n    const HALF = CURVE_ORDER >> _1n;\n    return number > HALF;\n  }\n  function validateRS(title: string, num: bigint): bigint {\n    if (!Fn.isValidNot0(num))\n      throw new Error(`invalid signature ${title}: out of range 1..Point.Fn.ORDER`);\n    return num;\n  }\n  function validateSigLength(bytes: Uint8Array, format: ECDSASigFormat) {\n    validateSigFormat(format);\n    const size = lengths.signature!;\n    const sizer = format === 'compact' ? size : format === 'recovered' ? size + 1 : undefined;\n    return abytes(bytes, sizer, `${format} signature`);\n  }\n\n  /**\n   * ECDSA signature with its (r, s) properties. Supports compact, recovered & DER representations.\n   */\n  class Signature implements ECDSASignature {\n    readonly r: bigint;\n    readonly s: bigint;\n    readonly recovery?: number;\n    constructor(r: bigint, s: bigint, recovery?: number) {\n      this.r = validateRS('r', r); // r in [1..N-1];\n      this.s = validateRS('s', s); // s in [1..N-1];\n      if (recovery != null) this.recovery = recovery;\n      Object.freeze(this);\n    }\n\n    static fromBytes(bytes: Uint8Array, format: ECDSASigFormat = defaultSigOpts_format): Signature {\n      validateSigLength(bytes, format);\n      let recid: number | undefined;\n      if (format === 'der') {\n        const { r, s } = DER.toSig(abytes(bytes));\n        return new Signature(r, s);\n      }\n      if (format === 'recovered') {\n        recid = bytes[0];\n        format = 'compact';\n        bytes = bytes.subarray(1);\n      }\n      const L = Fn.BYTES;\n      const r = bytes.subarray(0, L);\n      const s = bytes.subarray(L, L * 2);\n      return new Signature(Fn.fromBytes(r), Fn.fromBytes(s), recid);\n    }\n\n    static fromHex(hex: string, format?: ECDSASigFormat) {\n      return this.fromBytes(hexToBytes(hex), format);\n    }\n\n    addRecoveryBit(recovery: number): RecoveredSignature {\n      return new Signature(this.r, this.s, recovery) as RecoveredSignature;\n    }\n\n    recoverPublicKey(messageHash: Hex): WeierstrassPoint<bigint> {\n      const FIELD_ORDER = Fp.ORDER;\n      const { r, s, recovery: rec } = this;\n      if (rec == null || ![0, 1, 2, 3].includes(rec)) throw new Error('recovery id invalid');\n\n      // ECDSA recovery is hard for cofactor > 1 curves.\n      // In sign, `r = q.x mod n`, and here we recover q.x from r.\n      // While recovering q.x >= n, we need to add r+n for cofactor=1 curves.\n      // However, for cofactor>1, r+n may not get q.x:\n      // r+n*i would need to be done instead where i is unknown.\n      // To easily get i, we either need to:\n      // a. increase amount of valid recid values (4, 5...); OR\n      // b. prohibit non-prime-order signatures (recid > 1).\n      const hasCofactor = CURVE_ORDER * _2n < FIELD_ORDER;\n      if (hasCofactor && rec > 1) throw new Error('recovery id is ambiguous for h>1 curve');\n\n      const radj = rec === 2 || rec === 3 ? r + CURVE_ORDER : r;\n      if (!Fp.isValid(radj)) throw new Error('recovery id 2 or 3 invalid');\n      const x = Fp.toBytes(radj);\n      const R = Point.fromBytes(concatBytes(pprefix((rec & 1) === 0), x));\n      const ir = Fn.inv(radj); // r^-1\n      const h = bits2int_modN(ensureBytes('msgHash', messageHash)); // Truncate hash\n      const u1 = Fn.create(-h * ir); // -hr^-1\n      const u2 = Fn.create(s * ir); // sr^-1\n      // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1). unsafe is fine: there is no private data.\n      const Q = Point.BASE.multiplyUnsafe(u1).add(R.multiplyUnsafe(u2));\n      if (Q.is0()) throw new Error('point at infinify');\n      Q.assertValidity();\n      return Q;\n    }\n\n    // Signatures should be low-s, to prevent malleability.\n    hasHighS(): boolean {\n      return isBiggerThanHalfOrder(this.s);\n    }\n\n    toBytes(format: ECDSASigFormat = defaultSigOpts_format) {\n      validateSigFormat(format);\n      if (format === 'der') return hexToBytes(DER.hexFromSig(this));\n      const r = Fn.toBytes(this.r);\n      const s = Fn.toBytes(this.s);\n      if (format === 'recovered') {\n        if (this.recovery == null) throw new Error('recovery bit must be present');\n        return concatBytes(Uint8Array.of(this.recovery), r, s);\n      }\n      return concatBytes(r, s);\n    }\n\n    toHex(format?: ECDSASigFormat) {\n      return bytesToHex(this.toBytes(format));\n    }\n\n    // TODO: remove\n    assertValidity(): void {}\n    static fromCompact(hex: Hex) {\n      return Signature.fromBytes(ensureBytes('sig', hex), 'compact');\n    }\n    static fromDER(hex: Hex) {\n      return Signature.fromBytes(ensureBytes('sig', hex), 'der');\n    }\n    normalizeS() {\n      return this.hasHighS() ? new Signature(this.r, Fn.neg(this.s), this.recovery) : this;\n    }\n    toDERRawBytes() {\n      return this.toBytes('der');\n    }\n    toDERHex() {\n      return bytesToHex(this.toBytes('der'));\n    }\n    toCompactRawBytes() {\n      return this.toBytes('compact');\n    }\n    toCompactHex() {\n      return bytesToHex(this.toBytes('compact'));\n    }\n  }\n  type RecoveredSignature = Signature & { recovery: number };\n\n  // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n  // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n  // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n  // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n  const bits2int =\n    ecdsaOpts.bits2int ||\n    function bits2int_def(bytes: Uint8Array): bigint {\n      // Our custom check \"just in case\", for protection against DoS\n      if (bytes.length > 8192) throw new Error('input is too large');\n      // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n      // for some cases, since bytes.length * 8 is not actual bitLength.\n      const num = bytesToNumberBE(bytes); // check for == u8 done here\n      const delta = bytes.length * 8 - fnBits; // truncate to nBitLength leftmost bits\n      return delta > 0 ? num >> BigInt(delta) : num;\n    };\n  const bits2int_modN =\n    ecdsaOpts.bits2int_modN ||\n    function bits2int_modN_def(bytes: Uint8Array): bigint {\n      return Fn.create(bits2int(bytes)); // can't use bytesToNumberBE here\n    };\n  // Pads output with zero as per spec\n  const ORDER_MASK = bitMask(fnBits);\n  /** Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`. */\n  function int2octets(num: bigint): Uint8Array {\n    // IMPORTANT: the check ensures working for case `Fn.BYTES != Fn.BITS * 8`\n    aInRange('num < 2^' + fnBits, num, _0n, ORDER_MASK);\n    return Fn.toBytes(num);\n  }\n\n  function validateMsgAndHash(message: Uint8Array, prehash: boolean) {\n    abytes(message, undefined, 'message');\n    return prehash ? abytes(hash(message), undefined, 'prehashed message') : message;\n  }\n\n  /**\n   * Steps A, D of RFC6979 3.2.\n   * Creates RFC6979 seed; converts msg/privKey to numbers.\n   * Used only in sign, not in verify.\n   *\n   * Warning: we cannot assume here that message has same amount of bytes as curve order,\n   * this will be invalid at least for P521. Also it can be bigger for P224 + SHA256.\n   */\n  function prepSig(message: Uint8Array, privateKey: PrivKey, opts: ECDSASignOpts) {\n    if (['recovered', 'canonical'].some((k) => k in opts))\n      throw new Error('sign() legacy options not supported');\n    const { lowS, prehash, extraEntropy } = validateSigOpts(opts, defaultSigOpts);\n    message = validateMsgAndHash(message, prehash); // RFC6979 3.2 A: h1 = H(m)\n    // We can't later call bits2octets, since nested bits2int is broken for curves\n    // with fnBits % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n    // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n    const h1int = bits2int_modN(message);\n    const d = _normFnElement(Fn, privateKey); // validate secret key, convert to bigint\n    const seedArgs = [int2octets(d), int2octets(h1int)];\n    // extraEntropy. RFC6979 3.6: additional k' (optional).\n    if (extraEntropy != null && extraEntropy !== false) {\n      // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n      // gen random bytes OR pass as-is\n      const e = extraEntropy === true ? randomBytes(lengths.secretKey) : extraEntropy;\n      seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n    }\n    const seed = concatBytes(...seedArgs); // Step D of RFC6979 3.2\n    const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n    // Converts signature params into point w r/s, checks result for validity.\n    // To transform k => Signature:\n    // q = k⋅G\n    // r = q.x mod n\n    // s = k^-1(m + rd) mod n\n    // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n    // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n    // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n    function k2sig(kBytes: Uint8Array): RecoveredSignature | undefined {\n      // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n      // Important: all mod() calls here must be done over N\n      const k = bits2int(kBytes); // mod n, not mod p\n      if (!Fn.isValidNot0(k)) return; // Valid scalars (including k) must be in 1..N-1\n      const ik = Fn.inv(k); // k^-1 mod n\n      const q = Point.BASE.multiply(k).toAffine(); // q = k⋅G\n      const r = Fn.create(q.x); // r = q.x mod n\n      if (r === _0n) return;\n      const s = Fn.create(ik * Fn.create(m + r * d)); // Not using blinding here, see comment above\n      if (s === _0n) return;\n      let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n      let normS = s;\n      if (lowS && isBiggerThanHalfOrder(s)) {\n        normS = Fn.neg(s); // if lowS was passed, ensure s is always\n        recovery ^= 1; // // in the bottom half of N\n      }\n      return new Signature(r, normS, recovery) as RecoveredSignature; // use normS, not s\n    }\n    return { seed, k2sig };\n  }\n\n  /**\n   * Signs message hash with a secret key.\n   *\n   * ```\n   * sign(m, d) where\n   *   k = rfc6979_hmac_drbg(m, d)\n   *   (x, y) = G × k\n   *   r = x mod n\n   *   s = (m + dr) / k mod n\n   * ```\n   */\n  function sign(message: Hex, secretKey: PrivKey, opts: ECDSASignOpts = {}): RecoveredSignature {\n    message = ensureBytes('message', message);\n    const { seed, k2sig } = prepSig(message, secretKey, opts); // Steps A, D of RFC6979 3.2.\n    const drbg = createHmacDrbg<RecoveredSignature>(hash.outputLen, Fn.BYTES, hmac);\n    const sig = drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    return sig;\n  }\n\n  function tryParsingSig(sg: Hex | SignatureLike) {\n    // Try to deduce format\n    let sig: Signature | undefined = undefined;\n    const isHex = typeof sg === 'string' || isBytes(sg);\n    const isObj =\n      !isHex &&\n      sg !== null &&\n      typeof sg === 'object' &&\n      typeof sg.r === 'bigint' &&\n      typeof sg.s === 'bigint';\n    if (!isHex && !isObj)\n      throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n    if (isObj) {\n      sig = new Signature(sg.r, sg.s);\n    } else if (isHex) {\n      try {\n        sig = Signature.fromBytes(ensureBytes('sig', sg), 'der');\n      } catch (derError) {\n        if (!(derError instanceof DER.Err)) throw derError;\n      }\n      if (!sig) {\n        try {\n          sig = Signature.fromBytes(ensureBytes('sig', sg), 'compact');\n        } catch (error) {\n          return false;\n        }\n      }\n    }\n    if (!sig) return false;\n    return sig;\n  }\n\n  /**\n   * Verifies a signature against message and public key.\n   * Rejects lowS signatures by default: see {@link ECDSAVerifyOpts}.\n   * Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n   *\n   * ```\n   * verify(r, s, h, P) where\n   *   u1 = hs^-1 mod n\n   *   u2 = rs^-1 mod n\n   *   R = u1⋅G + u2⋅P\n   *   mod(R.x, n) == r\n   * ```\n   */\n  function verify(\n    signature: Hex | SignatureLike,\n    message: Hex,\n    publicKey: Hex,\n    opts: ECDSAVerifyOpts = {}\n  ): boolean {\n    const { lowS, prehash, format } = validateSigOpts(opts, defaultSigOpts);\n    publicKey = ensureBytes('publicKey', publicKey);\n    message = validateMsgAndHash(ensureBytes('message', message), prehash);\n    if ('strict' in opts) throw new Error('options.strict was renamed to lowS');\n    const sig =\n      format === undefined\n        ? tryParsingSig(signature)\n        : Signature.fromBytes(ensureBytes('sig', signature as Hex), format);\n    if (sig === false) return false;\n    try {\n      const P = Point.fromBytes(publicKey);\n      if (lowS && sig.hasHighS()) return false;\n      const { r, s } = sig;\n      const h = bits2int_modN(message); // mod n, not mod p\n      const is = Fn.inv(s); // s^-1 mod n\n      const u1 = Fn.create(h * is); // u1 = hs^-1 mod n\n      const u2 = Fn.create(r * is); // u2 = rs^-1 mod n\n      const R = Point.BASE.multiplyUnsafe(u1).add(P.multiplyUnsafe(u2)); // u1⋅G + u2⋅P\n      if (R.is0()) return false;\n      const v = Fn.create(R.x); // v = r.x mod n\n      return v === r;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  function recoverPublicKey(\n    signature: Uint8Array,\n    message: Uint8Array,\n    opts: ECDSARecoverOpts = {}\n  ): Uint8Array {\n    const { prehash } = validateSigOpts(opts, defaultSigOpts);\n    message = validateMsgAndHash(message, prehash);\n    return Signature.fromBytes(signature, 'recovered').recoverPublicKey(message).toBytes();\n  }\n\n  return Object.freeze({\n    keygen,\n    getPublicKey,\n    getSharedSecret,\n    utils,\n    lengths,\n    Point,\n    sign,\n    verify,\n    recoverPublicKey,\n    Signature,\n    hash,\n  });\n}\n\n// TODO: remove everything below\n/** @deprecated use ECDSASignature */\nexport type SignatureType = ECDSASignature;\n/** @deprecated use ECDSASigRecovered */\nexport type RecoveredSignatureType = ECDSASigRecovered;\n/** @deprecated switch to Uint8Array signatures in format 'compact' */\nexport type SignatureLike = { r: bigint; s: bigint };\nexport type ECDSAExtraEntropy = Hex | boolean;\n/** @deprecated use `ECDSAExtraEntropy` */\nexport type Entropy = Hex | boolean;\nexport type BasicWCurve<T> = BasicCurve<T> & {\n  // Params: a, b\n  a: T;\n  b: T;\n\n  // Optional params\n  allowedPrivateKeyLengths?: readonly number[]; // for P521\n  wrapPrivateKey?: boolean; // bls12-381 requires mod(n) instead of rejecting keys >= n\n  endo?: EndomorphismOpts;\n  // When a cofactor != 1, there can be an effective methods to:\n  // 1. Determine whether a point is torsion-free\n  isTorsionFree?: (c: WeierstrassPointCons<T>, point: WeierstrassPoint<T>) => boolean;\n  // 2. Clear torsion component\n  clearCofactor?: (c: WeierstrassPointCons<T>, point: WeierstrassPoint<T>) => WeierstrassPoint<T>;\n};\n/** @deprecated use ECDSASignOpts */\nexport type SignOpts = ECDSASignOpts;\n/** @deprecated use ECDSASignOpts */\nexport type VerOpts = ECDSAVerifyOpts;\n\n/** @deprecated use WeierstrassPoint */\nexport type ProjPointType<T> = WeierstrassPoint<T>;\n/** @deprecated use WeierstrassPointCons */\nexport type ProjConstructor<T> = WeierstrassPointCons<T>;\n/** @deprecated use ECDSASignatureCons */\nexport type SignatureConstructor = ECDSASignatureCons;\n\n// TODO: remove\nexport type CurvePointsType<T> = BasicWCurve<T> & {\n  fromBytes?: (bytes: Uint8Array) => AffinePoint<T>;\n  toBytes?: (\n    c: WeierstrassPointCons<T>,\n    point: WeierstrassPoint<T>,\n    isCompressed: boolean\n  ) => Uint8Array;\n};\n\n// LegacyWeierstrassOpts\nexport type CurvePointsTypeWithLength<T> = Readonly<CurvePointsType<T> & Partial<NLength>>;\n\n// LegacyWeierstrass\nexport type CurvePointsRes<T> = {\n  Point: WeierstrassPointCons<T>;\n\n  /** @deprecated use `Point.CURVE()` */\n  CURVE: CurvePointsType<T>;\n  /** @deprecated use `Point` */\n  ProjectivePoint: WeierstrassPointCons<T>;\n  /** @deprecated use `Point.Fn.fromBytes(privateKey)` */\n  normPrivateKeyToScalar: (key: PrivKey) => bigint;\n  /** @deprecated */\n  weierstrassEquation: (x: T) => T;\n  /** @deprecated use `Point.Fn.isValidNot0(num)` */\n  isWithinCurveOrder: (num: bigint) => boolean;\n};\n\n// Aliases to legacy types\n// export type CurveType = LegacyECDSAOpts;\n// export type CurveFn = LegacyECDSA;\n// export type CurvePointsRes<T> = LegacyWeierstrass<T>;\n// export type CurvePointsType<T> = LegacyWeierstrassOpts<T>;\n// export type CurvePointsTypeWithLength<T> = LegacyWeierstrassOpts<T>;\n// export type BasicWCurve<T> = LegacyWeierstrassOpts<T>;\n\n/** @deprecated use `Uint8Array` */\nexport type PubKey = Hex | WeierstrassPoint<bigint>;\nexport type CurveType = BasicWCurve<bigint> & {\n  hash: CHash; // CHash not FHash because we need outputLen for DRBG\n  hmac?: HmacFnSync;\n  randomBytes?: (bytesLength?: number) => Uint8Array;\n  lowS?: boolean;\n  bits2int?: (bytes: Uint8Array) => bigint;\n  bits2int_modN?: (bytes: Uint8Array) => bigint;\n};\nexport type CurveFn = {\n  /** @deprecated use `Point.CURVE()` */\n  CURVE: CurvePointsType<bigint>;\n  keygen: ECDSA['keygen'];\n  getPublicKey: ECDSA['getPublicKey'];\n  getSharedSecret: ECDSA['getSharedSecret'];\n  sign: ECDSA['sign'];\n  verify: ECDSA['verify'];\n  Point: WeierstrassPointCons<bigint>;\n  /** @deprecated use `Point` */\n  ProjectivePoint: WeierstrassPointCons<bigint>;\n  Signature: ECDSASignatureCons;\n  utils: ECDSA['utils'];\n  lengths: ECDSA['lengths'];\n};\n/** @deprecated use `weierstrass` in newer releases */\nexport function weierstrassPoints<T>(c: CurvePointsTypeWithLength<T>): CurvePointsRes<T> {\n  const { CURVE, curveOpts } = _weierstrass_legacy_opts_to_new(c);\n  const Point = weierstrassN(CURVE, curveOpts);\n  return _weierstrass_new_output_to_legacy(c, Point);\n}\nexport type WsPointComposed<T> = {\n  CURVE: WeierstrassOpts<T>;\n  curveOpts: WeierstrassExtraOpts<T>;\n};\nexport type WsComposed = {\n  /** @deprecated use `Point.CURVE()` */\n  CURVE: WeierstrassOpts<bigint>;\n  hash: CHash;\n  curveOpts: WeierstrassExtraOpts<bigint>;\n  ecdsaOpts: ECDSAOpts;\n};\nfunction _weierstrass_legacy_opts_to_new<T>(c: CurvePointsType<T>): WsPointComposed<T> {\n  const CURVE: WeierstrassOpts<T> = {\n    a: c.a,\n    b: c.b,\n    p: c.Fp.ORDER,\n    n: c.n,\n    h: c.h,\n    Gx: c.Gx,\n    Gy: c.Gy,\n  };\n  const Fp = c.Fp;\n  let allowedLengths = c.allowedPrivateKeyLengths\n    ? Array.from(new Set(c.allowedPrivateKeyLengths.map((l) => Math.ceil(l / 2))))\n    : undefined;\n  const Fn = Field(CURVE.n, {\n    BITS: c.nBitLength,\n    allowedLengths: allowedLengths,\n    modFromBytes: c.wrapPrivateKey,\n  });\n  const curveOpts: WeierstrassExtraOpts<T> = {\n    Fp,\n    Fn,\n    allowInfinityPoint: c.allowInfinityPoint,\n    endo: c.endo,\n    isTorsionFree: c.isTorsionFree,\n    clearCofactor: c.clearCofactor,\n    fromBytes: c.fromBytes,\n    toBytes: c.toBytes,\n  };\n  return { CURVE, curveOpts };\n}\nfunction _ecdsa_legacy_opts_to_new(c: CurveType): WsComposed {\n  const { CURVE, curveOpts } = _weierstrass_legacy_opts_to_new(c);\n  const ecdsaOpts: ECDSAOpts = {\n    hmac: c.hmac,\n    randomBytes: c.randomBytes,\n    lowS: c.lowS,\n    bits2int: c.bits2int,\n    bits2int_modN: c.bits2int_modN,\n  };\n  return { CURVE, curveOpts, hash: c.hash, ecdsaOpts };\n}\nexport function _legacyHelperEquat<T>(Fp: IField<T>, a: T, b: T): (x: T) => T {\n  /**\n   * y² = x³ + ax + b: Short weierstrass curve formula. Takes x, returns y².\n   * @returns y²\n   */\n  function weierstrassEquation(x: T): T {\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x² * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x³ + a * x + b\n  }\n  return weierstrassEquation;\n}\nfunction _weierstrass_new_output_to_legacy<T>(\n  c: CurvePointsType<T>,\n  Point: WeierstrassPointCons<T>\n): CurvePointsRes<T> {\n  const { Fp, Fn } = Point;\n  function isWithinCurveOrder(num: bigint): boolean {\n    return inRange(num, _1n, Fn.ORDER);\n  }\n  const weierstrassEquation = _legacyHelperEquat(Fp, c.a, c.b);\n  return Object.assign(\n    {},\n    {\n      CURVE: c,\n      Point: Point,\n      ProjectivePoint: Point,\n      normPrivateKeyToScalar: (key: PrivKey) => _normFnElement(Fn, key),\n      weierstrassEquation,\n      isWithinCurveOrder,\n    }\n  );\n}\nfunction _ecdsa_new_output_to_legacy(c: CurveType, _ecdsa: ECDSA): CurveFn {\n  const Point = _ecdsa.Point;\n  return Object.assign({}, _ecdsa, {\n    ProjectivePoint: Point,\n    CURVE: Object.assign({}, c, nLength(Point.Fn.ORDER, Point.Fn.BITS)),\n  });\n}\n\n// _ecdsa_legacy\nexport function weierstrass(c: CurveType): CurveFn {\n  const { CURVE, curveOpts, hash, ecdsaOpts } = _ecdsa_legacy_opts_to_new(c);\n  const Point = weierstrassN(CURVE, curveOpts);\n  const signs = ecdsa(Point, hash, ecdsaOpts);\n  return _ecdsa_new_output_to_legacy(c, signs);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;AACA,SAASA,IAAI,IAAIC,SAAS,QAAQ,uBAAuB;AACzD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SACEC,eAAe,EACfC,OAAO,IAAIC,KAAK,EAChBC,QAAQ,IAAIC,MAAM,EAClBC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,eAAe,EACfC,WAAW,EACXC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,mBAAmB,EACnBC,WAAW,IAAIC,cAAc,QAIxB,aAAa;AACpB,SACEC,kBAAkB,EAClBC,aAAa,EACbC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,IAAI,QAMC,YAAY;AACnB,SACEC,KAAK,EACLC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,OAAO,EACPC,aAAa,QAGR,cAAc;AAmCrB;AACA,MAAMC,UAAU,GAAGA,CAACC,GAAW,EAAEC,GAAW,KAAK,CAACD,GAAG,GAAG,CAACA,GAAG,IAAI,CAAC,GAAGC,GAAG,GAAG,CAACA,GAAG,IAAIC,GAAG,IAAID,GAAG;AAI5F;;;AAGA,OAAM,SAAUE,gBAAgBA,CAACC,CAAS,EAAEC,KAAgB,EAAEC,CAAS;EACrE;EACA;EACA;EACA,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC,GAAGL,KAAK;EAClC,MAAMM,EAAE,GAAGZ,UAAU,CAACW,EAAE,GAAGN,CAAC,EAAEE,CAAC,CAAC;EAChC,MAAMM,EAAE,GAAGb,UAAU,CAAC,CAACS,EAAE,GAAGJ,CAAC,EAAEE,CAAC,CAAC;EACjC;EACA;EACA,IAAIO,EAAE,GAAGT,CAAC,GAAGO,EAAE,GAAGJ,EAAE,GAAGK,EAAE,GAAGH,EAAE;EAC9B,IAAIK,EAAE,GAAG,CAACH,EAAE,GAAGH,EAAE,GAAGI,EAAE,GAAGF,EAAE;EAC3B,MAAMK,KAAK,GAAGF,EAAE,GAAGG,GAAG;EACtB,MAAMC,KAAK,GAAGH,EAAE,GAAGE,GAAG;EACtB,IAAID,KAAK,EAAEF,EAAE,GAAG,CAACA,EAAE;EACnB,IAAII,KAAK,EAAEH,EAAE,GAAG,CAACA,EAAE;EACnB;EACA;EACA,MAAMI,OAAO,GAAG5C,OAAO,CAAC6C,IAAI,CAACC,IAAI,CAAC/C,MAAM,CAACiC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGe,GAAG,CAAC,CAAC;EACzD,IAAIR,EAAE,GAAGG,GAAG,IAAIH,EAAE,IAAIK,OAAO,IAAIJ,EAAE,GAAGE,GAAG,IAAIF,EAAE,IAAII,OAAO,EAAE;IAC1D,MAAM,IAAII,KAAK,CAAC,wCAAwC,GAAGlB,CAAC,CAAC;EAC/D;EACA,OAAO;IAAEW,KAAK;IAAEF,EAAE;IAAEI,KAAK;IAAEH;EAAE,CAAE;AACjC;AAkBA,SAASS,iBAAiBA,CAACC,MAAc;EACvC,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,MAAM,CAAC,EACnD,MAAM,IAAIF,KAAK,CAAC,2DAA2D,CAAC;EAC9E,OAAOE,MAAwB;AACjC;AAEA,SAASE,eAAeA,CACtBC,IAAO,EACPC,GAAM;EAEN,MAAMC,KAAK,GAAkB,EAAE;EAC/B,KAAK,IAAIC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC,EAAE;IACpC;IACAC,KAAK,CAACC,OAAO,CAAC,GAAGH,IAAI,CAACG,OAAO,CAAC,KAAKG,SAAS,GAAGL,GAAG,CAACE,OAAO,CAAC,GAAGH,IAAI,CAACG,OAAO,CAAC;EAC7E;EACA7D,KAAK,CAAC4D,KAAK,CAACK,IAAK,EAAE,MAAM,CAAC;EAC1BjE,KAAK,CAAC4D,KAAK,CAACM,OAAQ,EAAE,SAAS,CAAC;EAChC,IAAIN,KAAK,CAACL,MAAM,KAAKS,SAAS,EAAEV,iBAAiB,CAACM,KAAK,CAACL,MAAM,CAAC;EAC/D,OAAOK,KAAgC;AACzC;AAmJA,OAAM,MAAOO,MAAO,SAAQd,KAAK;EAC/Be,YAAYC,CAAC,GAAG,EAAE;IAChB,KAAK,CAACA,CAAC,CAAC;EACV;;AAsBF;;;;;;;AAOA,OAAO,MAAMC,GAAG,GAAS;EACvB;EACAC,GAAG,EAAEJ,MAAM;EACX;EACAK,IAAI,EAAE;IACJC,MAAM,EAAEA,CAACC,GAAW,EAAEC,IAAY,KAAY;MAC5C,MAAM;QAAEJ,GAAG,EAAEK;MAAC,CAAE,GAAGN,GAAG;MACtB,IAAII,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,GAAG,EAAE,MAAM,IAAIE,CAAC,CAAC,uBAAuB,CAAC;MAC9D,IAAID,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE,MAAM,IAAID,CAAC,CAAC,2BAA2B,CAAC;MAC7D,MAAME,OAAO,GAAGH,IAAI,CAACE,MAAM,GAAG,CAAC;MAC/B,MAAME,GAAG,GAAGhE,mBAAmB,CAAC+D,OAAO,CAAC;MACxC,IAAKC,GAAG,CAACF,MAAM,GAAG,CAAC,GAAI,GAAW,EAAE,MAAM,IAAID,CAAC,CAAC,sCAAsC,CAAC;MACvF;MACA,MAAMI,MAAM,GAAGF,OAAO,GAAG,GAAG,GAAG/D,mBAAmB,CAAEgE,GAAG,CAACF,MAAM,GAAG,CAAC,GAAI,GAAW,CAAC,GAAG,EAAE;MACvF,MAAMI,CAAC,GAAGlE,mBAAmB,CAAC2D,GAAG,CAAC;MAClC,OAAOO,CAAC,GAAGD,MAAM,GAAGD,GAAG,GAAGJ,IAAI;IAChC,CAAC;IACD;IACAO,MAAMA,CAACR,GAAW,EAAEC,IAAgB;MAClC,MAAM;QAAEJ,GAAG,EAAEK;MAAC,CAAE,GAAGN,GAAG;MACtB,IAAIa,GAAG,GAAG,CAAC;MACX,IAAIT,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,GAAG,EAAE,MAAM,IAAIE,CAAC,CAAC,uBAAuB,CAAC;MAC9D,IAAID,IAAI,CAACE,MAAM,GAAG,CAAC,IAAIF,IAAI,CAACQ,GAAG,EAAE,CAAC,KAAKT,GAAG,EAAE,MAAM,IAAIE,CAAC,CAAC,uBAAuB,CAAC;MAChF,MAAMQ,KAAK,GAAGT,IAAI,CAACQ,GAAG,EAAE,CAAC;MACzB,MAAME,MAAM,GAAG,CAAC,EAAED,KAAK,GAAG,GAAW,CAAC,CAAC,CAAC;MACxC,IAAIP,MAAM,GAAG,CAAC;MACd,IAAI,CAACQ,MAAM,EAAER,MAAM,GAAGO,KAAK,CAAC,KACvB;QACH;QACA,MAAMJ,MAAM,GAAGI,KAAK,GAAG,GAAW;QAClC,IAAI,CAACJ,MAAM,EAAE,MAAM,IAAIJ,CAAC,CAAC,mDAAmD,CAAC;QAC7E,IAAII,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIJ,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC;QACzE,MAAMU,WAAW,GAAGX,IAAI,CAACY,QAAQ,CAACJ,GAAG,EAAEA,GAAG,GAAGH,MAAM,CAAC;QACpD,IAAIM,WAAW,CAACT,MAAM,KAAKG,MAAM,EAAE,MAAM,IAAIJ,CAAC,CAAC,uCAAuC,CAAC;QACvF,IAAIU,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM,IAAIV,CAAC,CAAC,sCAAsC,CAAC;QAC7E,KAAK,MAAMY,CAAC,IAAIF,WAAW,EAAET,MAAM,GAAIA,MAAM,IAAI,CAAC,GAAIW,CAAC;QACvDL,GAAG,IAAIH,MAAM;QACb,IAAIH,MAAM,GAAG,GAAG,EAAE,MAAM,IAAID,CAAC,CAAC,wCAAwC,CAAC;MACzE;MACA,MAAMa,CAAC,GAAGd,IAAI,CAACY,QAAQ,CAACJ,GAAG,EAAEA,GAAG,GAAGN,MAAM,CAAC;MAC1C,IAAIY,CAAC,CAACZ,MAAM,KAAKA,MAAM,EAAE,MAAM,IAAID,CAAC,CAAC,gCAAgC,CAAC;MACtE,OAAO;QAAEa,CAAC;QAAEC,CAAC,EAAEf,IAAI,CAACY,QAAQ,CAACJ,GAAG,GAAGN,MAAM;MAAC,CAAE;IAC9C;GACD;EACD;EACA;EACA;EACA;EACAc,IAAI,EAAE;IACJlB,MAAMA,CAAC1C,GAAW;MAChB,MAAM;QAAEwC,GAAG,EAAEK;MAAC,CAAE,GAAGN,GAAG;MACtB,IAAIvC,GAAG,GAAGgB,GAAG,EAAE,MAAM,IAAI6B,CAAC,CAAC,4CAA4C,CAAC;MACxE,IAAIgB,GAAG,GAAG7E,mBAAmB,CAACgB,GAAG,CAAC;MAClC;MACA,IAAI8D,MAAM,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,EAAEA,GAAG,GAAG,IAAI,GAAGA,GAAG;MAC1D,IAAIA,GAAG,CAACf,MAAM,GAAG,CAAC,EAAE,MAAM,IAAID,CAAC,CAAC,gDAAgD,CAAC;MACjF,OAAOgB,GAAG;IACZ,CAAC;IACDV,MAAMA,CAACP,IAAgB;MACrB,MAAM;QAAEJ,GAAG,EAAEK;MAAC,CAAE,GAAGN,GAAG;MACtB,IAAIK,IAAI,CAAC,CAAC,CAAC,GAAG,GAAW,EAAE,MAAM,IAAIC,CAAC,CAAC,qCAAqC,CAAC;MAC7E,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAW,CAAC,EAC9C,MAAM,IAAIC,CAAC,CAAC,qDAAqD,CAAC;MACpE,OAAOrE,eAAe,CAACoE,IAAI,CAAC;IAC9B;GACD;EACDoB,KAAKA,CAACH,GAAwB;IAC5B;IACA,MAAM;MAAErB,GAAG,EAAEK,CAAC;MAAEe,IAAI,EAAEK,GAAG;MAAExB,IAAI,EAAEyB;IAAG,CAAE,GAAG3B,GAAG;IAC5C,MAAMK,IAAI,GAAGjE,WAAW,CAAC,WAAW,EAAEkF,GAAG,CAAC;IAC1C,MAAM;MAAEH,CAAC,EAAES,QAAQ;MAAER,CAAC,EAAES;IAAY,CAAE,GAAGF,GAAG,CAACf,MAAM,CAAC,IAAI,EAAEP,IAAI,CAAC;IAC/D,IAAIwB,YAAY,CAACtB,MAAM,EAAE,MAAM,IAAID,CAAC,CAAC,6CAA6C,CAAC;IACnF,MAAM;MAAEa,CAAC,EAAEW,MAAM;MAAEV,CAAC,EAAEW;IAAU,CAAE,GAAGJ,GAAG,CAACf,MAAM,CAAC,IAAI,EAAEgB,QAAQ,CAAC;IAC/D,MAAM;MAAET,CAAC,EAAEa,MAAM;MAAEZ,CAAC,EAAEa;IAAU,CAAE,GAAGN,GAAG,CAACf,MAAM,CAAC,IAAI,EAAEmB,UAAU,CAAC;IACjE,IAAIE,UAAU,CAAC1B,MAAM,EAAE,MAAM,IAAID,CAAC,CAAC,6CAA6C,CAAC;IACjF,OAAO;MAAE4B,CAAC,EAAER,GAAG,CAACd,MAAM,CAACkB,MAAM,CAAC;MAAEK,CAAC,EAAET,GAAG,CAACd,MAAM,CAACoB,MAAM;IAAC,CAAE;EACzD,CAAC;EACDI,UAAUA,CAACC,GAA6B;IACtC,MAAM;MAAEnC,IAAI,EAAEyB,GAAG;MAAEN,IAAI,EAAEK;IAAG,CAAE,GAAG1B,GAAG;IACpC,MAAMsC,EAAE,GAAGX,GAAG,CAACxB,MAAM,CAAC,IAAI,EAAEuB,GAAG,CAACvB,MAAM,CAACkC,GAAG,CAACH,CAAC,CAAC,CAAC;IAC9C,MAAMK,EAAE,GAAGZ,GAAG,CAACxB,MAAM,CAAC,IAAI,EAAEuB,GAAG,CAACvB,MAAM,CAACkC,GAAG,CAACF,CAAC,CAAC,CAAC;IAC9C,MAAMK,GAAG,GAAGF,EAAE,GAAGC,EAAE;IACnB,OAAOZ,GAAG,CAACxB,MAAM,CAAC,IAAI,EAAEqC,GAAG,CAAC;EAC9B;CACD;AAED;AACA;AACA,MAAM/D,GAAG,GAAGgE,MAAM,CAAC,CAAC,CAAC;EAAE3D,GAAG,GAAG2D,MAAM,CAAC,CAAC,CAAC;EAAE9E,GAAG,GAAG8E,MAAM,CAAC,CAAC,CAAC;EAAEC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;EAAEE,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;AAEzF,OAAM,SAAUG,cAAcA,CAACC,EAAkB,EAAEC,GAAY;EAC7D,MAAM;IAAEC,KAAK,EAAEC;EAAQ,CAAE,GAAGH,EAAE;EAC9B,IAAIpF,GAAW;EACf,IAAI,OAAOqF,GAAG,KAAK,QAAQ,EAAE;IAC3BrF,GAAG,GAAGqF,GAAG;EACX,CAAC,MAAM;IACL,IAAIG,KAAK,GAAG7G,WAAW,CAAC,aAAa,EAAE0G,GAAG,CAAC;IAC3C,IAAI;MACFrF,GAAG,GAAGoF,EAAE,CAACK,SAAS,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,MAAM,IAAIpE,KAAK,CAAC,8CAA8CiE,QAAQ,SAAS,OAAOF,GAAG,EAAE,CAAC;IAC9F;EACF;EACA,IAAI,CAACD,EAAE,CAACO,WAAW,CAAC3F,GAAG,CAAC,EAAE,MAAM,IAAIsB,KAAK,CAAC,4CAA4C,CAAC;EACvF,OAAOtB,GAAG;AACZ;AAEA;;;;;;;;;;;;;;;;;AAiBA,OAAM,SAAU4F,YAAYA,CAC1BC,MAA0B,EAC1BC,SAAA,GAAqC,EAAE;EAEvC,MAAMC,SAAS,GAAG5G,kBAAkB,CAAC,aAAa,EAAE0G,MAAM,EAAEC,SAAS,CAAC;EACtE,MAAM;IAAEE,EAAE;IAAEZ;EAAE,CAAE,GAAGW,SAAS;EAC5B,IAAIE,KAAK,GAAGF,SAAS,CAACE,KAA2B;EACjD,MAAM;IAAEC,CAAC,EAAEC,QAAQ;IAAE7F,CAAC,EAAE8F;EAAW,CAAE,GAAGH,KAAK;EAC7ClI,eAAe,CACb+H,SAAS,EACT,EAAE,EACF;IACEO,kBAAkB,EAAE,SAAS;IAC7BC,aAAa,EAAE,UAAU;IACzBC,aAAa,EAAE,UAAU;IACzBd,SAAS,EAAE,UAAU;IACrBe,OAAO,EAAE,UAAU;IACnBC,IAAI,EAAE,QAAQ;IACdC,cAAc,EAAE;GACjB,CACF;EAED,MAAM;IAAED;EAAI,CAAE,GAAGX,SAAS;EAC1B,IAAIW,IAAI,EAAE;IACR;IACA,IAAI,CAACT,EAAE,CAACW,GAAG,CAACV,KAAK,CAACW,CAAC,CAAC,IAAI,OAAOH,IAAI,CAACI,IAAI,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACN,IAAI,CAACO,OAAO,CAAC,EAAE;MACrF,MAAM,IAAI1F,KAAK,CAAC,4DAA4D,CAAC;IAC/E;EACF;EAEA,MAAM2F,OAAO,GAAGC,WAAW,CAAClB,EAAE,EAAEZ,EAAE,CAAC;EAEnC,SAAS+B,4BAA4BA,CAAA;IACnC,IAAI,CAACnB,EAAE,CAACoB,KAAK,EAAE,MAAM,IAAI9F,KAAK,CAAC,4DAA4D,CAAC;EAC9F;EAEA;EACA,SAAS+F,YAAYA,CACnBC,EAA2B,EAC3BC,KAA0B,EAC1BC,YAAqB;IAErB,MAAM;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAGH,KAAK,CAACI,QAAQ,EAAE;IACjC,MAAMC,EAAE,GAAG5B,EAAE,CAACQ,OAAO,CAACiB,CAAC,CAAC;IACxBxJ,KAAK,CAACuJ,YAAY,EAAE,cAAc,CAAC;IACnC,IAAIA,YAAY,EAAE;MAChBL,4BAA4B,EAAE;MAC9B,MAAMU,QAAQ,GAAG,CAAC7B,EAAE,CAACoB,KAAM,CAACM,CAAC,CAAC;MAC9B,OAAOjJ,WAAW,CAACqJ,OAAO,CAACD,QAAQ,CAAC,EAAED,EAAE,CAAC;IAC3C,CAAC,MAAM;MACL,OAAOnJ,WAAW,CAACsJ,UAAU,CAACC,EAAE,CAAC,IAAI,CAAC,EAAEJ,EAAE,EAAE5B,EAAE,CAACQ,OAAO,CAACkB,CAAC,CAAC,CAAC;IAC5D;EACF;EACA,SAASO,cAAcA,CAACzC,KAAiB;IACvCrH,MAAM,CAACqH,KAAK,EAAEvD,SAAS,EAAE,OAAO,CAAC;IACjC,MAAM;MAAEiG,SAAS,EAAEC,IAAI;MAAEC,qBAAqB,EAAEC;IAAM,CAAE,GAAGpB,OAAO,CAAC,CAAC;IACpE,MAAMnE,MAAM,GAAG0C,KAAK,CAAC1C,MAAM;IAC3B,MAAMwF,IAAI,GAAG9C,KAAK,CAAC,CAAC,CAAC;IACrB,MAAM+C,IAAI,GAAG/C,KAAK,CAAChC,QAAQ,CAAC,CAAC,CAAC;IAC9B;IACA,IAAIV,MAAM,KAAKqF,IAAI,KAAKG,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE;MACvD,MAAMb,CAAC,GAAGzB,EAAE,CAACP,SAAS,CAAC8C,IAAI,CAAC;MAC5B,IAAI,CAACvC,EAAE,CAACwC,OAAO,CAACf,CAAC,CAAC,EAAE,MAAM,IAAInG,KAAK,CAAC,qCAAqC,CAAC;MAC1E,MAAMmH,EAAE,GAAGC,mBAAmB,CAACjB,CAAC,CAAC,CAAC,CAAC;MACnC,IAAIC,CAAI;MACR,IAAI;QACFA,CAAC,GAAG1B,EAAE,CAAC2C,IAAI,CAACF,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClB,MAAMC,GAAG,GAAGD,SAAS,YAAYtH,KAAK,GAAG,IAAI,GAAGsH,SAAS,CAACE,OAAO,GAAG,EAAE;QACtE,MAAM,IAAIxH,KAAK,CAAC,wCAAwC,GAAGuH,GAAG,CAAC;MACjE;MACA1B,4BAA4B,EAAE;MAC9B,MAAM4B,MAAM,GAAG/C,EAAE,CAACoB,KAAM,CAACM,CAAC,CAAC,CAAC,CAAC;MAC7B,MAAMsB,SAAS,GAAG,CAACV,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;MACpC,IAAIU,SAAS,KAAKD,MAAM,EAAErB,CAAC,GAAG1B,EAAE,CAACiD,GAAG,CAACvB,CAAC,CAAC;MACvC,OAAO;QAAED,CAAC;QAAEC;MAAC,CAAE;IACjB,CAAC,MAAM,IAAI5E,MAAM,KAAKuF,MAAM,IAAIC,IAAI,KAAK,IAAI,EAAE;MAC7C;MACA,MAAMY,CAAC,GAAGlD,EAAE,CAACV,KAAK;MAClB,MAAMmC,CAAC,GAAGzB,EAAE,CAACP,SAAS,CAAC8C,IAAI,CAAC/E,QAAQ,CAAC,CAAC,EAAE0F,CAAC,CAAC,CAAC;MAC3C,MAAMxB,CAAC,GAAG1B,EAAE,CAACP,SAAS,CAAC8C,IAAI,CAAC/E,QAAQ,CAAC0F,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACC,SAAS,CAAC1B,CAAC,EAAEC,CAAC,CAAC,EAAE,MAAM,IAAIpG,KAAK,CAAC,4BAA4B,CAAC;MACnE,OAAO;QAAEmG,CAAC;QAAEC;MAAC,CAAE;IACjB,CAAC,MAAM;MACL,MAAM,IAAIpG,KAAK,CACb,yBAAyBwB,MAAM,yBAAyBqF,IAAI,oBAAoBE,MAAM,EAAE,CACzF;IACH;EACF;EAEA,MAAMe,WAAW,GAAGtD,SAAS,CAACU,OAAO,IAAIa,YAAY;EACrD,MAAMgC,WAAW,GAAGvD,SAAS,CAACL,SAAS,IAAIwC,cAAc;EACzD,SAASS,mBAAmBA,CAACjB,CAAI;IAC/B,MAAM6B,EAAE,GAAGtD,EAAE,CAACuD,GAAG,CAAC9B,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM+B,EAAE,GAAGxD,EAAE,CAACyD,GAAG,CAACH,EAAE,EAAE7B,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAOzB,EAAE,CAAC0D,GAAG,CAAC1D,EAAE,CAAC0D,GAAG,CAACF,EAAE,EAAExD,EAAE,CAACyD,GAAG,CAAChC,CAAC,EAAExB,KAAK,CAACW,CAAC,CAAC,CAAC,EAAEX,KAAK,CAACxC,CAAC,CAAC,CAAC,CAAC;EAC1D;EAEA;EACA;EACA,SAAS0F,SAASA,CAAC1B,CAAI,EAAEC,CAAI;IAC3B,MAAMiC,IAAI,GAAG3D,EAAE,CAACuD,GAAG,CAAC7B,CAAC,CAAC,CAAC,CAAC;IACxB,MAAMkC,KAAK,GAAGlB,mBAAmB,CAACjB,CAAC,CAAC,CAAC,CAAC;IACtC,OAAOzB,EAAE,CAAC6D,GAAG,CAACF,IAAI,EAAEC,KAAK,CAAC;EAC5B;EAEA;EACA;EACA,IAAI,CAACT,SAAS,CAAClD,KAAK,CAAC6D,EAAE,EAAE7D,KAAK,CAAC8D,EAAE,CAAC,EAAE,MAAM,IAAIzI,KAAK,CAAC,mCAAmC,CAAC;EAExF;EACA;EACA,MAAM0I,IAAI,GAAGhE,EAAE,CAACyD,GAAG,CAACzD,EAAE,CAACiE,GAAG,CAAChE,KAAK,CAACW,CAAC,EAAE3B,GAAG,CAAC,EAAEC,GAAG,CAAC;EAC9C,MAAMgF,KAAK,GAAGlE,EAAE,CAACyD,GAAG,CAACzD,EAAE,CAACuD,GAAG,CAACtD,KAAK,CAACxC,CAAC,CAAC,EAAEuB,MAAM,CAAC,EAAE,CAAC,CAAC;EACjD,IAAIgB,EAAE,CAACW,GAAG,CAACX,EAAE,CAAC0D,GAAG,CAACM,IAAI,EAAEE,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI5I,KAAK,CAAC,0BAA0B,CAAC;EAE5E;EACA,SAAS6I,MAAMA,CAACC,KAAa,EAAE9J,CAAI,EAAE+J,OAAO,GAAG,KAAK;IAClD,IAAI,CAACrE,EAAE,CAACwC,OAAO,CAAClI,CAAC,CAAC,IAAK+J,OAAO,IAAIrE,EAAE,CAACW,GAAG,CAACrG,CAAC,CAAE,EAAE,MAAM,IAAIgB,KAAK,CAAC,wBAAwB8I,KAAK,EAAE,CAAC;IAC9F,OAAO9J,CAAC;EACV;EAEA,SAASgK,SAASA,CAACC,KAAc;IAC/B,IAAI,EAAEA,KAAK,YAAYC,KAAK,CAAC,EAAE,MAAM,IAAIlJ,KAAK,CAAC,0BAA0B,CAAC;EAC5E;EAEA,SAASmJ,gBAAgBA,CAACrK,CAAS;IACjC,IAAI,CAACqG,IAAI,IAAI,CAACA,IAAI,CAACO,OAAO,EAAE,MAAM,IAAI1F,KAAK,CAAC,SAAS,CAAC;IACtD,OAAOnB,gBAAgB,CAACC,CAAC,EAAEqG,IAAI,CAACO,OAAO,EAAE5B,EAAE,CAACsF,KAAK,CAAC;EACpD;EAEA;EAEA;EACA;EACA;EACA,MAAMC,YAAY,GAAG5L,QAAQ,CAAC,CAAC6L,CAAQ,EAAEC,EAAM,KAAoB;IACjE,MAAM;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAGJ,CAAC;IACrB;IACA,IAAI5E,EAAE,CAAC6D,GAAG,CAACmB,CAAC,EAAEhF,EAAE,CAACiF,GAAG,CAAC,EAAE,OAAO;MAAExD,CAAC,EAAEqD,CAAC;MAAEpD,CAAC,EAAEqD;IAAC,CAAE;IAC5C,MAAMpE,GAAG,GAAGiE,CAAC,CAACjE,GAAG,EAAE;IACnB;IACA;IACA,IAAIkE,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGlE,GAAG,GAAGX,EAAE,CAACiF,GAAG,GAAGjF,EAAE,CAACkF,GAAG,CAACF,CAAC,CAAC;IAC7C,MAAMvD,CAAC,GAAGzB,EAAE,CAACyD,GAAG,CAACqB,CAAC,EAAED,EAAE,CAAC;IACvB,MAAMnD,CAAC,GAAG1B,EAAE,CAACyD,GAAG,CAACsB,CAAC,EAAEF,EAAE,CAAC;IACvB,MAAMM,EAAE,GAAGnF,EAAE,CAACyD,GAAG,CAACuB,CAAC,EAAEH,EAAE,CAAC;IACxB,IAAIlE,GAAG,EAAE,OAAO;MAAEc,CAAC,EAAEzB,EAAE,CAACoF,IAAI;MAAE1D,CAAC,EAAE1B,EAAE,CAACoF;IAAI,CAAE;IAC1C,IAAI,CAACpF,EAAE,CAAC6D,GAAG,CAACsB,EAAE,EAAEnF,EAAE,CAACiF,GAAG,CAAC,EAAE,MAAM,IAAI3J,KAAK,CAAC,kBAAkB,CAAC;IAC5D,OAAO;MAAEmG,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC,CAAC;EACF;EACA;EACA,MAAM2D,eAAe,GAAGtM,QAAQ,CAAE6L,CAAQ,IAAI;IAC5C,IAAIA,CAAC,CAACjE,GAAG,EAAE,EAAE;MACX;MACA;MACA;MACA,IAAIb,SAAS,CAACO,kBAAkB,IAAI,CAACL,EAAE,CAACW,GAAG,CAACiE,CAAC,CAACG,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIzJ,KAAK,CAAC,iBAAiB,CAAC;IACpC;IACA;IACA,MAAM;MAAEmG,CAAC;MAAEC;IAAC,CAAE,GAAGkD,CAAC,CAACjD,QAAQ,EAAE;IAC7B,IAAI,CAAC3B,EAAE,CAACwC,OAAO,CAACf,CAAC,CAAC,IAAI,CAACzB,EAAE,CAACwC,OAAO,CAACd,CAAC,CAAC,EAAE,MAAM,IAAIpG,KAAK,CAAC,sCAAsC,CAAC;IAC7F,IAAI,CAAC6H,SAAS,CAAC1B,CAAC,EAAEC,CAAC,CAAC,EAAE,MAAM,IAAIpG,KAAK,CAAC,mCAAmC,CAAC;IAC1E,IAAI,CAACsJ,CAAC,CAACrE,aAAa,EAAE,EAAE,MAAM,IAAIjF,KAAK,CAAC,wCAAwC,CAAC;IACjF,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,SAASgK,UAAUA,CACjBC,QAAkC,EAClCC,GAAU,EACVC,GAAU,EACV1K,KAAc,EACdE,KAAc;IAEdwK,GAAG,GAAG,IAAIjB,KAAK,CAACxE,EAAE,CAACyD,GAAG,CAACgC,GAAG,CAACX,CAAC,EAAES,QAAQ,CAAC,EAAEE,GAAG,CAACV,CAAC,EAAEU,GAAG,CAACT,CAAC,CAAC;IACtDQ,GAAG,GAAGnM,QAAQ,CAAC0B,KAAK,EAAEyK,GAAG,CAAC;IAC1BC,GAAG,GAAGpM,QAAQ,CAAC4B,KAAK,EAAEwK,GAAG,CAAC;IAC1B,OAAOD,GAAG,CAAC9B,GAAG,CAAC+B,GAAG,CAAC;EACrB;EAEA;;;;;EAKA,MAAMjB,KAAK;IAcT;IACAnI,YAAYyI,CAAI,EAAEC,CAAI,EAAEC,CAAI;MAC1B,IAAI,CAACF,CAAC,GAAGX,MAAM,CAAC,GAAG,EAAEW,CAAC,CAAC;MACvB,IAAI,CAACC,CAAC,GAAGZ,MAAM,CAAC,GAAG,EAAEY,CAAC,EAAE,IAAI,CAAC;MAC7B,IAAI,CAACC,CAAC,GAAGb,MAAM,CAAC,GAAG,EAAEa,CAAC,CAAC;MACvBjJ,MAAM,CAAC2J,MAAM,CAAC,IAAI,CAAC;IACrB;IAEA,OAAOzF,KAAKA,CAAA;MACV,OAAOA,KAAK;IACd;IAEA;IACA,OAAO0F,UAAUA,CAACf,CAAiB;MACjC,MAAM;QAAEnD,CAAC;QAAEC;MAAC,CAAE,GAAGkD,CAAC,IAAI,EAAE;MACxB,IAAI,CAACA,CAAC,IAAI,CAAC5E,EAAE,CAACwC,OAAO,CAACf,CAAC,CAAC,IAAI,CAACzB,EAAE,CAACwC,OAAO,CAACd,CAAC,CAAC,EAAE,MAAM,IAAIpG,KAAK,CAAC,sBAAsB,CAAC;MACnF,IAAIsJ,CAAC,YAAYJ,KAAK,EAAE,MAAM,IAAIlJ,KAAK,CAAC,8BAA8B,CAAC;MACvE;MACA,IAAI0E,EAAE,CAACW,GAAG,CAACc,CAAC,CAAC,IAAIzB,EAAE,CAACW,GAAG,CAACe,CAAC,CAAC,EAAE,OAAO8C,KAAK,CAACY,IAAI;MAC7C,OAAO,IAAIZ,KAAK,CAAC/C,CAAC,EAAEC,CAAC,EAAE1B,EAAE,CAACiF,GAAG,CAAC;IAChC;IAEA,OAAOxF,SAASA,CAACD,KAAiB;MAChC,MAAMoG,CAAC,GAAGpB,KAAK,CAACmB,UAAU,CAACtC,WAAW,CAAClL,MAAM,CAACqH,KAAK,EAAEvD,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;MAC1E2J,CAAC,CAACC,cAAc,EAAE;MAClB,OAAOD,CAAC;IACV;IACA,OAAOE,OAAOA,CAACjI,GAAQ;MACrB,OAAO2G,KAAK,CAAC/E,SAAS,CAAC9G,WAAW,CAAC,UAAU,EAAEkF,GAAG,CAAC,CAAC;IACtD;IAEA,IAAI4D,CAACA,CAAA;MACH,OAAO,IAAI,CAACE,QAAQ,EAAE,CAACF,CAAC;IAC1B;IACA,IAAIC,CAACA,CAAA;MACH,OAAO,IAAI,CAACC,QAAQ,EAAE,CAACD,CAAC;IAC1B;IAEA;;;;;;IAMAqE,UAAUA,CAACC,UAAA,GAAqB,CAAC,EAAEC,MAAM,GAAG,IAAI;MAC9CC,IAAI,CAACC,WAAW,CAAC,IAAI,EAAEH,UAAU,CAAC;MAClC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACG,QAAQ,CAACnH,GAAG,CAAC,CAAC,CAAC;MACjC,OAAO,IAAI;IACb;IAEA;IACA;IACA4G,cAAcA,CAAA;MACZR,eAAe,CAAC,IAAI,CAAC;IACvB;IAEAxD,QAAQA,CAAA;MACN,MAAM;QAAEH;MAAC,CAAE,GAAG,IAAI,CAACC,QAAQ,EAAE;MAC7B,IAAI,CAAC3B,EAAE,CAACoB,KAAK,EAAE,MAAM,IAAI9F,KAAK,CAAC,6BAA6B,CAAC;MAC7D,OAAO,CAAC0E,EAAE,CAACoB,KAAK,CAACM,CAAC,CAAC;IACrB;IAEA;IACA2E,MAAMA,CAAC9B,KAAY;MACjBD,SAAS,CAACC,KAAK,CAAC;MAChB,MAAM;QAAEO,CAAC,EAAEwB,EAAE;QAAEvB,CAAC,EAAEwB,EAAE;QAAEvB,CAAC,EAAEwB;MAAE,CAAE,GAAG,IAAI;MACpC,MAAM;QAAE1B,CAAC,EAAE2B,EAAE;QAAE1B,CAAC,EAAE2B,EAAE;QAAE1B,CAAC,EAAE2B;MAAE,CAAE,GAAGpC,KAAK;MACrC,MAAMqC,EAAE,GAAG5G,EAAE,CAAC6D,GAAG,CAAC7D,EAAE,CAACyD,GAAG,CAAC6C,EAAE,EAAEK,EAAE,CAAC,EAAE3G,EAAE,CAACyD,GAAG,CAACgD,EAAE,EAAED,EAAE,CAAC,CAAC;MACjD,MAAMK,EAAE,GAAG7G,EAAE,CAAC6D,GAAG,CAAC7D,EAAE,CAACyD,GAAG,CAAC8C,EAAE,EAAEI,EAAE,CAAC,EAAE3G,EAAE,CAACyD,GAAG,CAACiD,EAAE,EAAEF,EAAE,CAAC,CAAC;MACjD,OAAOI,EAAE,IAAIC,EAAE;IACjB;IAEA;IACAC,MAAMA,CAAA;MACJ,OAAO,IAAItC,KAAK,CAAC,IAAI,CAACM,CAAC,EAAE9E,EAAE,CAACiD,GAAG,CAAC,IAAI,CAAC8B,CAAC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAClD;IAEA;IACA;IACA;IACA;IACA+B,MAAMA,CAAA;MACJ,MAAM;QAAEnG,CAAC;QAAEnD;MAAC,CAAE,GAAGwC,KAAK;MACtB,MAAM+G,EAAE,GAAGhH,EAAE,CAACyD,GAAG,CAAChG,CAAC,EAAEwB,GAAG,CAAC;MACzB,MAAM;QAAE6F,CAAC,EAAEwB,EAAE;QAAEvB,CAAC,EAAEwB,EAAE;QAAEvB,CAAC,EAAEwB;MAAE,CAAE,GAAG,IAAI;MACpC,IAAIS,EAAE,GAAGjH,EAAE,CAACoF,IAAI;QAAE8B,EAAE,GAAGlH,EAAE,CAACoF,IAAI;QAAE+B,EAAE,GAAGnH,EAAE,CAACoF,IAAI,CAAC,CAAC;MAC9C,IAAIgC,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAAC6C,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACzB,IAAIe,EAAE,GAAGrH,EAAE,CAACyD,GAAG,CAAC8C,EAAE,EAAEA,EAAE,CAAC;MACvB,IAAIe,EAAE,GAAGtH,EAAE,CAACyD,GAAG,CAAC+C,EAAE,EAAEA,EAAE,CAAC;MACvB,IAAIe,EAAE,GAAGvH,EAAE,CAACyD,GAAG,CAAC6C,EAAE,EAAEC,EAAE,CAAC;MACvBgB,EAAE,GAAGvH,EAAE,CAAC0D,GAAG,CAAC6D,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBJ,EAAE,GAAGnH,EAAE,CAACyD,GAAG,CAAC6C,EAAE,EAAEE,EAAE,CAAC;MACnBW,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAACyD,EAAE,EAAEA,EAAE,CAAC;MACnBF,EAAE,GAAGjH,EAAE,CAACyD,GAAG,CAAC7C,CAAC,EAAEuG,EAAE,CAAC;MAClBD,EAAE,GAAGlH,EAAE,CAACyD,GAAG,CAACuD,EAAE,EAAEM,EAAE,CAAC;MACnBJ,EAAE,GAAGlH,EAAE,CAAC0D,GAAG,CAACuD,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrBD,EAAE,GAAGjH,EAAE,CAACwH,GAAG,CAACH,EAAE,EAAEH,EAAE,CAAC;MACnBA,EAAE,GAAGlH,EAAE,CAAC0D,GAAG,CAAC2D,EAAE,EAAEH,EAAE,CAAC;MACnBA,EAAE,GAAGlH,EAAE,CAACyD,GAAG,CAACwD,EAAE,EAAEC,EAAE,CAAC;MACnBD,EAAE,GAAGjH,EAAE,CAACyD,GAAG,CAAC8D,EAAE,EAAEN,EAAE,CAAC;MACnBE,EAAE,GAAGnH,EAAE,CAACyD,GAAG,CAACuD,EAAE,EAAEG,EAAE,CAAC,CAAC,CAAC;MACrBG,EAAE,GAAGtH,EAAE,CAACyD,GAAG,CAAC7C,CAAC,EAAE0G,EAAE,CAAC;MAClBC,EAAE,GAAGvH,EAAE,CAACwH,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC;MACnBC,EAAE,GAAGvH,EAAE,CAACyD,GAAG,CAAC7C,CAAC,EAAE2G,EAAE,CAAC;MAClBA,EAAE,GAAGvH,EAAE,CAAC0D,GAAG,CAAC6D,EAAE,EAAEJ,EAAE,CAAC;MACnBA,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAAC0D,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBA,EAAE,GAAGpH,EAAE,CAAC0D,GAAG,CAACyD,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAGpH,EAAE,CAAC0D,GAAG,CAAC0D,EAAE,EAAEE,EAAE,CAAC;MACnBF,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAAC2D,EAAE,EAAEG,EAAE,CAAC;MACnBL,EAAE,GAAGlH,EAAE,CAAC0D,GAAG,CAACwD,EAAE,EAAEE,EAAE,CAAC;MACnBE,EAAE,GAAGtH,EAAE,CAACyD,GAAG,CAAC8C,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrBc,EAAE,GAAGtH,EAAE,CAAC0D,GAAG,CAAC4D,EAAE,EAAEA,EAAE,CAAC;MACnBF,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAAC6D,EAAE,EAAEC,EAAE,CAAC;MACnBN,EAAE,GAAGjH,EAAE,CAACwH,GAAG,CAACP,EAAE,EAAEG,EAAE,CAAC;MACnBD,EAAE,GAAGnH,EAAE,CAACyD,GAAG,CAAC6D,EAAE,EAAED,EAAE,CAAC;MACnBF,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAACyD,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBA,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAACyD,EAAE,EAAEA,EAAE,CAAC;MACnB,OAAO,IAAI3C,KAAK,CAACyC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9B;IAEA;IACA;IACA;IACA;IACAzD,GAAGA,CAACa,KAAY;MACdD,SAAS,CAACC,KAAK,CAAC;MAChB,MAAM;QAAEO,CAAC,EAAEwB,EAAE;QAAEvB,CAAC,EAAEwB,EAAE;QAAEvB,CAAC,EAAEwB;MAAE,CAAE,GAAG,IAAI;MACpC,MAAM;QAAE1B,CAAC,EAAE2B,EAAE;QAAE1B,CAAC,EAAE2B,EAAE;QAAE1B,CAAC,EAAE2B;MAAE,CAAE,GAAGpC,KAAK;MACrC,IAAI0C,EAAE,GAAGjH,EAAE,CAACoF,IAAI;QAAE8B,EAAE,GAAGlH,EAAE,CAACoF,IAAI;QAAE+B,EAAE,GAAGnH,EAAE,CAACoF,IAAI,CAAC,CAAC;MAC9C,MAAMxE,CAAC,GAAGX,KAAK,CAACW,CAAC;MACjB,MAAMoG,EAAE,GAAGhH,EAAE,CAACyD,GAAG,CAACxD,KAAK,CAACxC,CAAC,EAAEwB,GAAG,CAAC;MAC/B,IAAImI,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAAC6C,EAAE,EAAEG,EAAE,CAAC,CAAC,CAAC;MACzB,IAAIY,EAAE,GAAGrH,EAAE,CAACyD,GAAG,CAAC8C,EAAE,EAAEG,EAAE,CAAC;MACvB,IAAIY,EAAE,GAAGtH,EAAE,CAACyD,GAAG,CAAC+C,EAAE,EAAEG,EAAE,CAAC;MACvB,IAAIY,EAAE,GAAGvH,EAAE,CAAC0D,GAAG,CAAC4C,EAAE,EAAEC,EAAE,CAAC;MACvB,IAAIkB,EAAE,GAAGzH,EAAE,CAAC0D,GAAG,CAAC+C,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACzBa,EAAE,GAAGvH,EAAE,CAACyD,GAAG,CAAC8D,EAAE,EAAEE,EAAE,CAAC;MACnBA,EAAE,GAAGzH,EAAE,CAAC0D,GAAG,CAAC0D,EAAE,EAAEC,EAAE,CAAC;MACnBE,EAAE,GAAGvH,EAAE,CAACwH,GAAG,CAACD,EAAE,EAAEE,EAAE,CAAC;MACnBA,EAAE,GAAGzH,EAAE,CAAC0D,GAAG,CAAC4C,EAAE,EAAEE,EAAE,CAAC;MACnB,IAAIkB,EAAE,GAAG1H,EAAE,CAAC0D,GAAG,CAAC+C,EAAE,EAAEE,EAAE,CAAC,CAAC,CAAC;MACzBc,EAAE,GAAGzH,EAAE,CAACyD,GAAG,CAACgE,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAG1H,EAAE,CAAC0D,GAAG,CAAC0D,EAAE,EAAEE,EAAE,CAAC;MACnBG,EAAE,GAAGzH,EAAE,CAACwH,GAAG,CAACC,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAG1H,EAAE,CAAC0D,GAAG,CAAC6C,EAAE,EAAEC,EAAE,CAAC;MACnBS,EAAE,GAAGjH,EAAE,CAAC0D,GAAG,CAACgD,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrBe,EAAE,GAAG1H,EAAE,CAACyD,GAAG,CAACiE,EAAE,EAAET,EAAE,CAAC;MACnBA,EAAE,GAAGjH,EAAE,CAAC0D,GAAG,CAAC2D,EAAE,EAAEC,EAAE,CAAC;MACnBI,EAAE,GAAG1H,EAAE,CAACwH,GAAG,CAACE,EAAE,EAAET,EAAE,CAAC;MACnBE,EAAE,GAAGnH,EAAE,CAACyD,GAAG,CAAC7C,CAAC,EAAE6G,EAAE,CAAC;MAClBR,EAAE,GAAGjH,EAAE,CAACyD,GAAG,CAACuD,EAAE,EAAEM,EAAE,CAAC,CAAC,CAAC;MACrBH,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAACuD,EAAE,EAAEE,EAAE,CAAC;MACnBF,EAAE,GAAGjH,EAAE,CAACwH,GAAG,CAACH,EAAE,EAAEF,EAAE,CAAC;MACnBA,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAAC2D,EAAE,EAAEF,EAAE,CAAC;MACnBD,EAAE,GAAGlH,EAAE,CAACyD,GAAG,CAACwD,EAAE,EAAEE,EAAE,CAAC;MACnBE,EAAE,GAAGrH,EAAE,CAAC0D,GAAG,CAAC0D,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBC,EAAE,GAAGrH,EAAE,CAAC0D,GAAG,CAAC2D,EAAE,EAAED,EAAE,CAAC;MACnBE,EAAE,GAAGtH,EAAE,CAACyD,GAAG,CAAC7C,CAAC,EAAE0G,EAAE,CAAC;MAClBG,EAAE,GAAGzH,EAAE,CAACyD,GAAG,CAACuD,EAAE,EAAES,EAAE,CAAC;MACnBJ,EAAE,GAAGrH,EAAE,CAAC0D,GAAG,CAAC2D,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAGtH,EAAE,CAACwH,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC,CAAC,CAAC;MACrBA,EAAE,GAAGtH,EAAE,CAACyD,GAAG,CAAC7C,CAAC,EAAE0G,EAAE,CAAC;MAClBG,EAAE,GAAGzH,EAAE,CAAC0D,GAAG,CAAC+D,EAAE,EAAEH,EAAE,CAAC;MACnBF,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAAC4D,EAAE,EAAEI,EAAE,CAAC;MACnBP,EAAE,GAAGlH,EAAE,CAAC0D,GAAG,CAACwD,EAAE,EAAEE,EAAE,CAAC;MACnBA,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAACiE,EAAE,EAAED,EAAE,CAAC,CAAC,CAAC;MACrBR,EAAE,GAAGjH,EAAE,CAACyD,GAAG,CAAC8D,EAAE,EAAEN,EAAE,CAAC;MACnBA,EAAE,GAAGjH,EAAE,CAACwH,GAAG,CAACP,EAAE,EAAEG,EAAE,CAAC;MACnBA,EAAE,GAAGpH,EAAE,CAACyD,GAAG,CAAC8D,EAAE,EAAEF,EAAE,CAAC;MACnBF,EAAE,GAAGnH,EAAE,CAACyD,GAAG,CAACiE,EAAE,EAAEP,EAAE,CAAC;MACnBA,EAAE,GAAGnH,EAAE,CAAC0D,GAAG,CAACyD,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrB,OAAO,IAAI5C,KAAK,CAACyC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9B;IAEAQ,QAAQA,CAACpD,KAAY;MACnB,OAAO,IAAI,CAACb,GAAG,CAACa,KAAK,CAACuC,MAAM,EAAE,CAAC;IACjC;IAEAnG,GAAGA,CAAA;MACD,OAAO,IAAI,CAAC0F,MAAM,CAAC7B,KAAK,CAACY,IAAI,CAAC;IAChC;IAEA;;;;;;;;;IASAgB,QAAQA,CAACwB,MAAc;MACrB,MAAM;QAAEnH;MAAI,CAAE,GAAGX,SAAS;MAC1B,IAAI,CAACV,EAAE,CAACO,WAAW,CAACiI,MAAM,CAAC,EAAE,MAAM,IAAItM,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;MAC9E,IAAIiG,KAAY,EAAEsG,IAAW,CAAC,CAAC;MAC/B,MAAMpE,GAAG,GAAInJ,CAAS,IAAK4L,IAAI,CAAC4B,MAAM,CAAC,IAAI,EAAExN,CAAC,EAAGsK,CAAC,IAAKtL,UAAU,CAACkL,KAAK,EAAEI,CAAC,CAAC,CAAC;MAC5E;MACA,IAAInE,IAAI,EAAE;QACR,MAAM;UAAE1F,KAAK;UAAEF,EAAE;UAAEI,KAAK;UAAEH;QAAE,CAAE,GAAG2J,gBAAgB,CAACmD,MAAM,CAAC;QACzD,MAAM;UAAEhD,CAAC,EAAEY,GAAG;UAAEuC,CAAC,EAAEC;QAAG,CAAE,GAAGvE,GAAG,CAAC5I,EAAE,CAAC;QAClC,MAAM;UAAE+J,CAAC,EAAEa,GAAG;UAAEsC,CAAC,EAAEE;QAAG,CAAE,GAAGxE,GAAG,CAAC3I,EAAE,CAAC;QAClC+M,IAAI,GAAGG,GAAG,CAACtE,GAAG,CAACuE,GAAG,CAAC;QACnB1G,KAAK,GAAG+D,UAAU,CAAC7E,IAAI,CAACI,IAAI,EAAE2E,GAAG,EAAEC,GAAG,EAAE1K,KAAK,EAAEE,KAAK,CAAC;MACvD,CAAC,MAAM;QACL,MAAM;UAAE2J,CAAC;UAAEmD;QAAC,CAAE,GAAGtE,GAAG,CAACmE,MAAM,CAAC;QAC5BrG,KAAK,GAAGqD,CAAC;QACTiD,IAAI,GAAGE,CAAC;MACV;MACA;MACA,OAAOzO,UAAU,CAACkL,KAAK,EAAE,CAACjD,KAAK,EAAEsG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C;IAEA;;;;;IAKAK,cAAcA,CAACC,EAAU;MACvB,MAAM;QAAE1H;MAAI,CAAE,GAAGX,SAAS;MAC1B,MAAM8E,CAAC,GAAG,IAAa;MACvB,IAAI,CAACxF,EAAE,CAACoD,OAAO,CAAC2F,EAAE,CAAC,EAAE,MAAM,IAAI7M,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC;MACtE,IAAI6M,EAAE,KAAKnN,GAAG,IAAI4J,CAAC,CAACjE,GAAG,EAAE,EAAE,OAAO6D,KAAK,CAACY,IAAI;MAC5C,IAAI+C,EAAE,KAAK9M,GAAG,EAAE,OAAOuJ,CAAC,CAAC,CAAC;MAC1B,IAAIsB,IAAI,CAACkC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAChC,QAAQ,CAAC+B,EAAE,CAAC;MACjD,IAAI1H,IAAI,EAAE;QACR,MAAM;UAAE1F,KAAK;UAAEF,EAAE;UAAEI,KAAK;UAAEH;QAAE,CAAE,GAAG2J,gBAAgB,CAAC0D,EAAE,CAAC;QACrD,MAAM;UAAEE,EAAE;UAAEC;QAAE,CAAE,GAAGlP,aAAa,CAACoL,KAAK,EAAEI,CAAC,EAAE/J,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;QACpD,OAAOwK,UAAU,CAAC7E,IAAI,CAACI,IAAI,EAAEwH,EAAE,EAAEC,EAAE,EAAEvN,KAAK,EAAEE,KAAK,CAAC;MACpD,CAAC,MAAM;QACL,OAAOiL,IAAI,CAACqC,MAAM,CAAC3D,CAAC,EAAEuD,EAAE,CAAC;MAC3B;IACF;IAEAK,oBAAoBA,CAACC,CAAQ,EAAE7H,CAAS,EAAEnD,CAAS;MACjD,MAAMiL,GAAG,GAAG,IAAI,CAACR,cAAc,CAACtH,CAAC,CAAC,CAAC8C,GAAG,CAAC+E,CAAC,CAACP,cAAc,CAACzK,CAAC,CAAC,CAAC;MAC3D,OAAOiL,GAAG,CAAC/H,GAAG,EAAE,GAAG1E,SAAS,GAAGyM,GAAG;IACpC;IAEA;;;;IAIA/G,QAAQA,CAACgH,SAAa;MACpB,OAAOhE,YAAY,CAAC,IAAI,EAAEgE,SAAS,CAAC;IACtC;IAEA;;;;IAIApI,aAAaA,CAAA;MACX,MAAM;QAAEA;MAAa,CAAE,GAAGT,SAAS;MACnC,IAAIK,QAAQ,KAAK9E,GAAG,EAAE,OAAO,IAAI;MACjC,IAAIkF,aAAa,EAAE,OAAOA,aAAa,CAACiE,KAAK,EAAE,IAAI,CAAC;MACpD,OAAO0B,IAAI,CAACqC,MAAM,CAAC,IAAI,EAAEnI,WAAW,CAAC,CAACO,GAAG,EAAE;IAC7C;IAEAL,aAAaA,CAAA;MACX,MAAM;QAAEA;MAAa,CAAE,GAAGR,SAAS;MACnC,IAAIK,QAAQ,KAAK9E,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;MACnC,IAAIiF,aAAa,EAAE,OAAOA,aAAa,CAACkE,KAAK,EAAE,IAAI,CAAU;MAC7D,OAAO,IAAI,CAAC0D,cAAc,CAAC/H,QAAQ,CAAC;IACtC;IAEAyI,YAAYA,CAAA;MACV;MACA,OAAO,IAAI,CAACV,cAAc,CAAC/H,QAAQ,CAAC,CAACQ,GAAG,EAAE;IAC5C;IAEAH,OAAOA,CAACgB,YAAY,GAAG,IAAI;MACzBvJ,KAAK,CAACuJ,YAAY,EAAE,cAAc,CAAC;MACnC,IAAI,CAACqE,cAAc,EAAE;MACrB,OAAOzC,WAAW,CAACoB,KAAK,EAAE,IAAI,EAAEhD,YAAY,CAAC;IAC/C;IAEAqH,KAAKA,CAACrH,YAAY,GAAG,IAAI;MACvB,OAAOjJ,UAAU,CAAC,IAAI,CAACiI,OAAO,CAACgB,YAAY,CAAC,CAAC;IAC/C;IAEAsH,QAAQA,CAAA;MACN,OAAO,UAAU,IAAI,CAACnI,GAAG,EAAE,GAAG,MAAM,GAAG,IAAI,CAACkI,KAAK,EAAE,GAAG;IACxD;IAEA;IACA,IAAIE,EAAEA,CAAA;MACJ,OAAO,IAAI,CAACjE,CAAC;IACf;IACA,IAAIkE,EAAEA,CAAA;MACJ,OAAO,IAAI,CAAClE,CAAC;IACf;IACA,IAAImE,EAAEA,CAAA;MACJ,OAAO,IAAI,CAACjE,CAAC;IACf;IACAkE,UAAUA,CAAC1H,YAAY,GAAG,IAAI;MAC5B,OAAO,IAAI,CAAChB,OAAO,CAACgB,YAAY,CAAC;IACnC;IACA2H,cAAcA,CAACnD,UAAkB;MAC/B,IAAI,CAACD,UAAU,CAACC,UAAU,CAAC;IAC7B;IACA,OAAO1M,UAAUA,CAAC8P,MAAe;MAC/B,OAAO9P,UAAU,CAACkL,KAAK,EAAE4E,MAAM,CAAC;IAClC;IACA,OAAOC,GAAGA,CAACD,MAAe,EAAEE,OAAiB;MAC3C,OAAO/P,SAAS,CAACiL,KAAK,EAAEpF,EAAE,EAAEgK,MAAM,EAAEE,OAAO,CAAC;IAC9C;IACA,OAAOC,cAAcA,CAACC,UAAmB;MACvC,OAAOhF,KAAK,CAACiF,IAAI,CAACrD,QAAQ,CAACjH,cAAc,CAACC,EAAE,EAAEoK,UAAU,CAAC,CAAC;IAC5D;;EAhUA;EACgBhF,KAAA,CAAAiF,IAAI,GAAG,IAAIjF,KAAK,CAACvE,KAAK,CAAC6D,EAAE,EAAE7D,KAAK,CAAC8D,EAAE,EAAE/D,EAAE,CAACiF,GAAG,CAAC;EAC5D;EACgBT,KAAA,CAAAY,IAAI,GAAG,IAAIZ,KAAK,CAACxE,EAAE,CAACoF,IAAI,EAAEpF,EAAE,CAACiF,GAAG,EAAEjF,EAAE,CAACoF,IAAI,CAAC,CAAC,CAAC;EAC5D;EACgBZ,KAAA,CAAAxE,EAAE,GAAGA,EAAE;EACvB;EACgBwE,KAAA,CAAApF,EAAE,GAAGA,EAAE;EA2TzB,MAAMsK,IAAI,GAAGtK,EAAE,CAACuK,IAAI;EACpB,MAAMzD,IAAI,GAAG,IAAI1M,IAAI,CAACgL,KAAK,EAAE1E,SAAS,CAACW,IAAI,GAAGtF,IAAI,CAACC,IAAI,CAACsO,IAAI,GAAG,CAAC,CAAC,GAAGA,IAAI,CAAC;EACzElF,KAAK,CAACiF,IAAI,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,OAAOvB,KAAK;AACd;AA0CA;AACA,SAAS1C,OAAOA,CAACD,QAAiB;EAChC,OAAOE,UAAU,CAACC,EAAE,CAACH,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;AAC9C;AAEA;;;;;;;;;AASA,OAAM,SAAU+H,cAAcA,CAC5B5J,EAAa,EACbgF,CAAI;EAEJ;EACA,MAAM6E,CAAC,GAAG7J,EAAE,CAAC0E,KAAK;EAClB,IAAI/G,CAAC,GAAG3C,GAAG;EACX,KAAK,IAAI8O,CAAC,GAAGD,CAAC,GAAGxO,GAAG,EAAEyO,CAAC,GAAG5P,GAAG,KAAKc,GAAG,EAAE8O,CAAC,IAAI5P,GAAG,EAAEyD,CAAC,IAAItC,GAAG;EACzD,MAAMV,EAAE,GAAGgD,CAAC,CAAC,CAAC;EACd;EACA;EACA,MAAMoM,YAAY,GAAG7P,GAAG,IAAKS,EAAE,GAAGU,GAAG,GAAGA,GAAI;EAC5C,MAAM2O,UAAU,GAAGD,YAAY,GAAG7P,GAAG;EACrC,MAAMU,EAAE,GAAG,CAACiP,CAAC,GAAGxO,GAAG,IAAI2O,UAAU,CAAC,CAAC;EACnC,MAAMC,EAAE,GAAG,CAACrP,EAAE,GAAGS,GAAG,IAAInB,GAAG,CAAC,CAAC;EAC7B,MAAMgQ,EAAE,GAAGF,UAAU,GAAG3O,GAAG,CAAC,CAAC;EAC7B,MAAM8O,EAAE,GAAGJ,YAAY,CAAC,CAAC;EACzB,MAAMK,EAAE,GAAGpK,EAAE,CAACiE,GAAG,CAACe,CAAC,EAAEpK,EAAE,CAAC,CAAC,CAAC;EAC1B,MAAMyP,EAAE,GAAGrK,EAAE,CAACiE,GAAG,CAACe,CAAC,EAAE,CAACpK,EAAE,GAAGS,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC;EACxC,IAAIoQ,SAAS,GAAGA,CAACC,CAAI,EAAE7M,CAAI,KAAoC;IAC7D,IAAI8M,GAAG,GAAGJ,EAAE,CAAC,CAAC;IACd,IAAIK,GAAG,GAAGzK,EAAE,CAACiE,GAAG,CAACvG,CAAC,EAAEwM,EAAE,CAAC,CAAC,CAAC;IACzB,IAAIQ,GAAG,GAAG1K,EAAE,CAACuD,GAAG,CAACkH,GAAG,CAAC,CAAC,CAAC;IACvBC,GAAG,GAAG1K,EAAE,CAACyD,GAAG,CAACiH,GAAG,EAAEhN,CAAC,CAAC,CAAC,CAAC;IACtB,IAAIiN,GAAG,GAAG3K,EAAE,CAACyD,GAAG,CAAC8G,CAAC,EAAEG,GAAG,CAAC,CAAC,CAAC;IAC1BC,GAAG,GAAG3K,EAAE,CAACiE,GAAG,CAAC0G,GAAG,EAAEV,EAAE,CAAC,CAAC,CAAC;IACvBU,GAAG,GAAG3K,EAAE,CAACyD,GAAG,CAACkH,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;IACxBA,GAAG,GAAGzK,EAAE,CAACyD,GAAG,CAACkH,GAAG,EAAEjN,CAAC,CAAC,CAAC,CAAC;IACtBgN,GAAG,GAAG1K,EAAE,CAACyD,GAAG,CAACkH,GAAG,EAAEJ,CAAC,CAAC,CAAC,CAAC;IACtB,IAAIK,GAAG,GAAG5K,EAAE,CAACyD,GAAG,CAACiH,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;IAC5BE,GAAG,GAAG3K,EAAE,CAACiE,GAAG,CAAC2G,GAAG,EAAET,EAAE,CAAC,CAAC,CAAC;IACvB,IAAIU,IAAI,GAAG7K,EAAE,CAAC6D,GAAG,CAAC8G,GAAG,EAAE3K,EAAE,CAACiF,GAAG,CAAC,CAAC,CAAC;IAChCwF,GAAG,GAAGzK,EAAE,CAACyD,GAAG,CAACiH,GAAG,EAAEL,EAAE,CAAC,CAAC,CAAC;IACvBM,GAAG,GAAG3K,EAAE,CAACyD,GAAG,CAACmH,GAAG,EAAEJ,GAAG,CAAC,CAAC,CAAC;IACxBE,GAAG,GAAG1K,EAAE,CAAC8K,IAAI,CAACL,GAAG,EAAEC,GAAG,EAAEG,IAAI,CAAC,CAAC,CAAC;IAC/BD,GAAG,GAAG5K,EAAE,CAAC8K,IAAI,CAACH,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,CAAC;IAC/B;IACA,KAAK,IAAIE,CAAC,GAAGpQ,EAAE,EAAEoQ,CAAC,GAAG1P,GAAG,EAAE0P,CAAC,EAAE,EAAE;MAC7B,IAAIJ,GAAG,GAAGI,CAAC,GAAG7Q,GAAG,CAAC,CAAC;MACnByQ,GAAG,GAAGzQ,GAAG,IAAKyQ,GAAG,GAAGtP,GAAI,CAAC,CAAC;MAC1B,IAAI2P,IAAI,GAAGhL,EAAE,CAACiE,GAAG,CAAC2G,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;MAC7B,MAAMM,EAAE,GAAGjL,EAAE,CAAC6D,GAAG,CAACmH,IAAI,EAAEhL,EAAE,CAACiF,GAAG,CAAC,CAAC,CAAC;MACjCwF,GAAG,GAAGzK,EAAE,CAACyD,GAAG,CAACiH,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;MACxBA,GAAG,GAAGxK,EAAE,CAACyD,GAAG,CAAC+G,GAAG,EAAEA,GAAG,CAAC,CAAC,CAAC;MACxBQ,IAAI,GAAGhL,EAAE,CAACyD,GAAG,CAACmH,GAAG,EAAEJ,GAAG,CAAC,CAAC,CAAC;MACzBE,GAAG,GAAG1K,EAAE,CAAC8K,IAAI,CAACL,GAAG,EAAEC,GAAG,EAAEO,EAAE,CAAC,CAAC,CAAC;MAC7BL,GAAG,GAAG5K,EAAE,CAAC8K,IAAI,CAACE,IAAI,EAAEJ,GAAG,EAAEK,EAAE,CAAC,CAAC,CAAC;IAChC;IACA,OAAO;MAAEzI,OAAO,EAAEqI,IAAI;MAAEK,KAAK,EAAER;IAAG,CAAE;EACtC,CAAC;EACD,IAAI1K,EAAE,CAAC0E,KAAK,GAAGxF,GAAG,KAAKD,GAAG,EAAE;IAC1B;IACA,MAAMtE,EAAE,GAAG,CAACqF,EAAE,CAAC0E,KAAK,GAAGzF,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnC,MAAMtE,EAAE,GAAGoF,EAAE,CAAC2C,IAAI,CAAC3C,EAAE,CAACiD,GAAG,CAAC+B,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/BsF,SAAS,GAAGA,CAACC,CAAI,EAAE7M,CAAI,KAAI;MACzB,IAAI8M,GAAG,GAAGxK,EAAE,CAACuD,GAAG,CAAC7F,CAAC,CAAC,CAAC,CAAC;MACrB,MAAM+M,GAAG,GAAGzK,EAAE,CAACyD,GAAG,CAAC8G,CAAC,EAAE7M,CAAC,CAAC,CAAC,CAAC;MAC1B8M,GAAG,GAAGxK,EAAE,CAACyD,GAAG,CAAC+G,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;MACxB,IAAIU,EAAE,GAAGnL,EAAE,CAACiE,GAAG,CAACuG,GAAG,EAAE7P,EAAE,CAAC,CAAC,CAAC;MAC1BwQ,EAAE,GAAGnL,EAAE,CAACyD,GAAG,CAAC0H,EAAE,EAAEV,GAAG,CAAC,CAAC,CAAC;MACtB,MAAMhI,EAAE,GAAGzC,EAAE,CAACyD,GAAG,CAAC0H,EAAE,EAAEvQ,EAAE,CAAC,CAAC,CAAC;MAC3B,MAAM8P,GAAG,GAAG1K,EAAE,CAACyD,GAAG,CAACzD,EAAE,CAACuD,GAAG,CAAC4H,EAAE,CAAC,EAAEzN,CAAC,CAAC,CAAC,CAAC;MACnC,MAAMmN,IAAI,GAAG7K,EAAE,CAAC6D,GAAG,CAAC6G,GAAG,EAAEH,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAI7I,CAAC,GAAG1B,EAAE,CAAC8K,IAAI,CAACrI,EAAE,EAAE0I,EAAE,EAAEN,IAAI,CAAC,CAAC,CAAC;MAC/B,OAAO;QAAErI,OAAO,EAAEqI,IAAI;QAAEK,KAAK,EAAExJ;MAAC,CAAE,CAAC,CAAC;IACtC,CAAC;EACH;EACA;EACA;EACA,OAAO4I,SAAS;AAClB;AACA;;;;AAIA,OAAM,SAAUc,mBAAmBA,CACjCpL,EAAa,EACbrE,IAIC;EAED7B,aAAa,CAACkG,EAAE,CAAC;EACjB,MAAM;IAAEqL,CAAC;IAAEC,CAAC;IAAEtG;EAAC,CAAE,GAAGrJ,IAAI;EACxB,IAAI,CAACqE,EAAE,CAACwC,OAAO,CAAC6I,CAAC,CAAC,IAAI,CAACrL,EAAE,CAACwC,OAAO,CAAC8I,CAAC,CAAC,IAAI,CAACtL,EAAE,CAACwC,OAAO,CAACwC,CAAC,CAAC,EACpD,MAAM,IAAI1J,KAAK,CAAC,mCAAmC,CAAC;EACtD,MAAMgP,SAAS,GAAGV,cAAc,CAAC5J,EAAE,EAAEgF,CAAC,CAAC;EACvC,IAAI,CAAChF,EAAE,CAACoB,KAAK,EAAE,MAAM,IAAI9F,KAAK,CAAC,8BAA8B,CAAC;EAC9D;EACA;EACA,OAAQiP,CAAI,IAAoB;IAC9B;IACA,IAAIC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEE,GAAG,EAAED,GAAG,EAAEY,GAAG,EAAE9J,CAAC,EAAEC,CAAC;IACtC8I,GAAG,GAAGxK,EAAE,CAACuD,GAAG,CAACgH,CAAC,CAAC,CAAC,CAAC;IACjBC,GAAG,GAAGxK,EAAE,CAACyD,GAAG,CAAC+G,GAAG,EAAExF,CAAC,CAAC,CAAC,CAAC;IACtByF,GAAG,GAAGzK,EAAE,CAACuD,GAAG,CAACiH,GAAG,CAAC,CAAC,CAAC;IACnBC,GAAG,GAAGzK,EAAE,CAAC0D,GAAG,CAAC+G,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;IACxBE,GAAG,GAAG1K,EAAE,CAAC0D,GAAG,CAAC+G,GAAG,EAAEzK,EAAE,CAACiF,GAAG,CAAC,CAAC,CAAC;IAC3ByF,GAAG,GAAG1K,EAAE,CAACyD,GAAG,CAACiH,GAAG,EAAEY,CAAC,CAAC,CAAC,CAAC;IACtBV,GAAG,GAAG5K,EAAE,CAAC8K,IAAI,CAAC9F,CAAC,EAAEhF,EAAE,CAACiD,GAAG,CAACwH,GAAG,CAAC,EAAE,CAACzK,EAAE,CAAC6D,GAAG,CAAC4G,GAAG,EAAEzK,EAAE,CAACoF,IAAI,CAAC,CAAC,CAAC,CAAC;IACtDwF,GAAG,GAAG5K,EAAE,CAACyD,GAAG,CAACmH,GAAG,EAAES,CAAC,CAAC,CAAC,CAAC;IACtBZ,GAAG,GAAGzK,EAAE,CAACuD,GAAG,CAACmH,GAAG,CAAC,CAAC,CAAC;IACnBa,GAAG,GAAGvL,EAAE,CAACuD,GAAG,CAACqH,GAAG,CAAC,CAAC,CAAC;IACnBD,GAAG,GAAG3K,EAAE,CAACyD,GAAG,CAAC8H,GAAG,EAAEF,CAAC,CAAC,CAAC,CAAC;IACtBZ,GAAG,GAAGzK,EAAE,CAAC0D,GAAG,CAAC+G,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAC;IACxBF,GAAG,GAAGzK,EAAE,CAACyD,GAAG,CAACgH,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;IACxBa,GAAG,GAAGvL,EAAE,CAACyD,GAAG,CAAC8H,GAAG,EAAEX,GAAG,CAAC,CAAC,CAAC;IACxBD,GAAG,GAAG3K,EAAE,CAACyD,GAAG,CAAC8H,GAAG,EAAED,CAAC,CAAC,CAAC,CAAC;IACtBb,GAAG,GAAGzK,EAAE,CAAC0D,GAAG,CAAC+G,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAC;IACxBlJ,CAAC,GAAGzB,EAAE,CAACyD,GAAG,CAAC+G,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAC;IACtB,MAAM;MAAElI,OAAO;MAAE0I;IAAK,CAAE,GAAGZ,SAAS,CAACG,GAAG,EAAEc,GAAG,CAAC,CAAC,CAAC;IAChD7J,CAAC,GAAG1B,EAAE,CAACyD,GAAG,CAAC+G,GAAG,EAAED,CAAC,CAAC,CAAC,CAAC;IACpB7I,CAAC,GAAG1B,EAAE,CAACyD,GAAG,CAAC/B,CAAC,EAAEwJ,KAAK,CAAC,CAAC,CAAC;IACtBzJ,CAAC,GAAGzB,EAAE,CAAC8K,IAAI,CAACrJ,CAAC,EAAEiJ,GAAG,EAAElI,OAAO,CAAC,CAAC,CAAC;IAC9Bd,CAAC,GAAG1B,EAAE,CAAC8K,IAAI,CAACpJ,CAAC,EAAEwJ,KAAK,EAAE1I,OAAO,CAAC,CAAC,CAAC;IAChC,MAAMyI,EAAE,GAAGjL,EAAE,CAACoB,KAAM,CAACmJ,CAAC,CAAC,KAAKvK,EAAE,CAACoB,KAAM,CAACM,CAAC,CAAC,CAAC,CAAC;IAC1CA,CAAC,GAAG1B,EAAE,CAAC8K,IAAI,CAAC9K,EAAE,CAACiD,GAAG,CAACvB,CAAC,CAAC,EAAEA,CAAC,EAAEuJ,EAAE,CAAC,CAAC,CAAC;IAC/B,MAAMO,OAAO,GAAG9R,aAAa,CAACsG,EAAE,EAAE,CAAC4K,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACjDnJ,CAAC,GAAGzB,EAAE,CAACyD,GAAG,CAAChC,CAAC,EAAE+J,OAAO,CAAC,CAAC,CAAC;IACxB,OAAO;MAAE/J,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC;AACH;AAEA,SAASR,WAAWA,CAAIlB,EAAa,EAAEZ,EAAkB;EACvD,OAAO;IACLqM,SAAS,EAAErM,EAAE,CAACE,KAAK;IACnB4C,SAAS,EAAE,CAAC,GAAGlC,EAAE,CAACV,KAAK;IACvB8C,qBAAqB,EAAE,CAAC,GAAG,CAAC,GAAGpC,EAAE,CAACV,KAAK;IACvCoM,kBAAkB,EAAE,IAAI;IACxBC,SAAS,EAAE,CAAC,GAAGvM,EAAE,CAACE;GACnB;AACH;AAEA;;;;AAIA,OAAM,SAAUsM,IAAIA,CAClBpH,KAAmC,EACnCqH,QAAA,GAAmE,EAAE;EAErE,MAAM;IAAEzM;EAAE,CAAE,GAAGoF,KAAK;EACpB,MAAMsH,YAAY,GAAGD,QAAQ,CAAC5S,WAAW,IAAIC,cAAc;EAC3D,MAAM+H,OAAO,GAAGlF,MAAM,CAACgQ,MAAM,CAAC7K,WAAW,CAACsD,KAAK,CAACxE,EAAE,EAAEZ,EAAE,CAAC,EAAE;IAAE4M,IAAI,EAAErS,gBAAgB,CAACyF,EAAE,CAACsF,KAAK;EAAC,CAAE,CAAC;EAE9F,SAASuH,gBAAgBA,CAACR,SAAkB;IAC1C,IAAI;MACF,OAAO,CAAC,CAACtM,cAAc,CAACC,EAAE,EAAEqM,SAAS,CAAC;IACxC,CAAC,CAAC,OAAO/L,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;EAEA,SAASwM,gBAAgBA,CAAChK,SAAqB,EAAEV,YAAsB;IACrE,MAAM;MAAEU,SAAS,EAAEC,IAAI;MAAEC;IAAqB,CAAE,GAAGnB,OAAO;IAC1D,IAAI;MACF,MAAMtD,CAAC,GAAGuE,SAAS,CAACpF,MAAM;MAC1B,IAAI0E,YAAY,KAAK,IAAI,IAAI7D,CAAC,KAAKwE,IAAI,EAAE,OAAO,KAAK;MACrD,IAAIX,YAAY,KAAK,KAAK,IAAI7D,CAAC,KAAKyE,qBAAqB,EAAE,OAAO,KAAK;MACvE,OAAO,CAAC,CAACoC,KAAK,CAAC/E,SAAS,CAACyC,SAAS,CAAC;IACrC,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;EAEA;;;;EAIA,SAASyM,eAAeA,CAACH,IAAI,GAAGF,YAAY,CAAC7K,OAAO,CAAC+K,IAAI,CAAC;IACxD,OAAOpS,cAAc,CAACzB,MAAM,CAAC6T,IAAI,EAAE/K,OAAO,CAAC+K,IAAI,EAAE,MAAM,CAAC,EAAE5M,EAAE,CAACsF,KAAK,CAAC;EACrE;EAEA;;;;;EAKA,SAAS0H,YAAYA,CAACX,SAAkB,EAAEjK,YAAY,GAAG,IAAI;IAC3D,OAAOgD,KAAK,CAACiF,IAAI,CAACrD,QAAQ,CAACjH,cAAc,CAACC,EAAE,EAAEqM,SAAS,CAAC,CAAC,CAACjL,OAAO,CAACgB,YAAY,CAAC;EACjF;EAEA,SAAS6K,MAAMA,CAACL,IAAiB;IAC/B,MAAMP,SAAS,GAAGU,eAAe,CAACH,IAAI,CAAC;IACvC,OAAO;MAAEP,SAAS;MAAEvJ,SAAS,EAAEkK,YAAY,CAACX,SAAS;IAAC,CAAE;EAC1D;EAEA;;;EAGA,SAASa,SAASA,CAACC,IAAsB;IACvC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK;IAC1C,IAAIA,IAAI,YAAY/H,KAAK,EAAE,OAAO,IAAI;IACtC,MAAM;MAAEiH,SAAS;MAAEvJ,SAAS;MAAEE;IAAqB,CAAE,GAAGnB,OAAO;IAC/D,IAAI7B,EAAE,CAACoN,cAAc,IAAIf,SAAS,KAAKvJ,SAAS,EAAE,OAAOjG,SAAS;IAClE,MAAM0B,CAAC,GAAGhF,WAAW,CAAC,KAAK,EAAE4T,IAAI,CAAC,CAACzP,MAAM;IACzC,OAAOa,CAAC,KAAKuE,SAAS,IAAIvE,CAAC,KAAKyE,qBAAqB;EACvD;EAEA;;;;;;;;EAQA,SAASqK,eAAeA,CAACC,UAAmB,EAAEC,UAAe,EAAEnL,YAAY,GAAG,IAAI;IAChF,IAAI8K,SAAS,CAACI,UAAU,CAAC,KAAK,IAAI,EAAE,MAAM,IAAIpR,KAAK,CAAC,+BAA+B,CAAC;IACpF,IAAIgR,SAAS,CAACK,UAAU,CAAC,KAAK,KAAK,EAAE,MAAM,IAAIrR,KAAK,CAAC,+BAA+B,CAAC;IACrF,MAAMoD,CAAC,GAAGS,cAAc,CAACC,EAAE,EAAEsN,UAAU,CAAC;IACxC,MAAMjP,CAAC,GAAG+G,KAAK,CAACsB,OAAO,CAAC6G,UAAU,CAAC,CAAC,CAAC;IACrC,OAAOlP,CAAC,CAAC2I,QAAQ,CAAC1H,CAAC,CAAC,CAAC8B,OAAO,CAACgB,YAAY,CAAC;EAC5C;EAEA,MAAMoL,KAAK,GAAG;IACZX,gBAAgB;IAChBC,gBAAgB;IAChBC,eAAe;IAEf;IACAU,iBAAiB,EAAEZ,gBAAgB;IACnCa,gBAAgB,EAAEX,eAAe;IACjCY,sBAAsB,EAAG1N,GAAY,IAAKF,cAAc,CAACC,EAAE,EAAEC,GAAG,CAAC;IACjE0G,UAAUA,CAACC,UAAU,GAAG,CAAC,EAAEzE,KAAK,GAAGiD,KAAK,CAACiF,IAAI;MAC3C,OAAOlI,KAAK,CAACwE,UAAU,CAACC,UAAU,EAAE,KAAK,CAAC;IAC5C;GACD;EAED,OAAOjK,MAAM,CAAC2J,MAAM,CAAC;IAAE0G,YAAY;IAAEK,eAAe;IAAEJ,MAAM;IAAE7H,KAAK;IAAEoI,KAAK;IAAE3L;EAAO,CAAE,CAAC;AACxF;AAEA;;;;;;;;;;;;;;;;AAgBA,OAAM,SAAU+L,KAAKA,CACnBxI,KAAmC,EACnCyI,IAAW,EACXC,SAAA,GAAuB,EAAE;EAEzBpV,KAAK,CAACmV,IAAI,CAAC;EACXlV,eAAe,CACbmV,SAAS,EACT,EAAE,EACF;IACEtV,IAAI,EAAE,UAAU;IAChBsE,IAAI,EAAE,SAAS;IACfjD,WAAW,EAAE,UAAU;IACvBkU,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE;GAChB,CACF;EAED,MAAMnU,WAAW,GAAGiU,SAAS,CAACjU,WAAW,IAAIC,cAAc;EAC3D,MAAMtB,IAAI,GACRsV,SAAS,CAACtV,IAAI,KACZ,CAACyH,GAAG,EAAE,GAAGgO,IAAI,KAAKxV,SAAS,CAACoV,IAAI,EAAE5N,GAAG,EAAE5G,WAAW,CAAC,GAAG4U,IAAI,CAAC,CAAC,CAAuB;EAEvF,MAAM;IAAErN,EAAE;IAAEZ;EAAE,CAAE,GAAGoF,KAAK;EACxB,MAAM;IAAEE,KAAK,EAAEtE,WAAW;IAAEuJ,IAAI,EAAE2D;EAAM,CAAE,GAAGlO,EAAE;EAC/C,MAAM;IAAEiN,MAAM;IAAED,YAAY;IAAEK,eAAe;IAAEG,KAAK;IAAE3L;EAAO,CAAE,GAAG2K,IAAI,CAACpH,KAAK,EAAE0I,SAAS,CAAC;EACxF,MAAMK,cAAc,GAA4B;IAC9CpR,OAAO,EAAE,KAAK;IACdD,IAAI,EAAE,OAAOgR,SAAS,CAAChR,IAAI,KAAK,SAAS,GAAGgR,SAAS,CAAChR,IAAI,GAAG,KAAK;IAClEV,MAAM,EAAES,SAAgB;IAAE;IAC1BuR,YAAY,EAAE;GACf;EACD,MAAMC,qBAAqB,GAAG,SAAS;EAEvC,SAASC,qBAAqBA,CAACC,MAAc;IAC3C,MAAMC,IAAI,GAAGxN,WAAW,IAAI/E,GAAG;IAC/B,OAAOsS,MAAM,GAAGC,IAAI;EACtB;EACA,SAASC,UAAUA,CAACzJ,KAAa,EAAEpK,GAAW;IAC5C,IAAI,CAACoF,EAAE,CAACO,WAAW,CAAC3F,GAAG,CAAC,EACtB,MAAM,IAAIsB,KAAK,CAAC,qBAAqB8I,KAAK,kCAAkC,CAAC;IAC/E,OAAOpK,GAAG;EACZ;EACA,SAAS8T,iBAAiBA,CAACtO,KAAiB,EAAEhE,MAAsB;IAClED,iBAAiB,CAACC,MAAM,CAAC;IACzB,MAAMuS,IAAI,GAAG9M,OAAO,CAAC0K,SAAU;IAC/B,MAAMqC,KAAK,GAAGxS,MAAM,KAAK,SAAS,GAAGuS,IAAI,GAAGvS,MAAM,KAAK,WAAW,GAAGuS,IAAI,GAAG,CAAC,GAAG9R,SAAS;IACzF,OAAO9D,MAAM,CAACqH,KAAK,EAAEwO,KAAK,EAAE,GAAGxS,MAAM,YAAY,CAAC;EACpD;EAEA;;;EAGA,MAAMyS,SAAS;IAIb5R,YAAYoC,CAAS,EAAEC,CAAS,EAAEwP,QAAiB;MACjD,IAAI,CAACzP,CAAC,GAAGoP,UAAU,CAAC,GAAG,EAAEpP,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACC,CAAC,GAAGmP,UAAU,CAAC,GAAG,EAAEnP,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAIwP,QAAQ,IAAI,IAAI,EAAE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MAC9CnS,MAAM,CAAC2J,MAAM,CAAC,IAAI,CAAC;IACrB;IAEA,OAAOjG,SAASA,CAACD,KAAiB,EAAEhE,MAAA,GAAyBiS,qBAAqB;MAChFK,iBAAiB,CAACtO,KAAK,EAAEhE,MAAM,CAAC;MAChC,IAAI2S,KAAyB;MAC7B,IAAI3S,MAAM,KAAK,KAAK,EAAE;QACpB,MAAM;UAAEiD,CAAC;UAAEC;QAAC,CAAE,GAAGnC,GAAG,CAACyB,KAAK,CAAC7F,MAAM,CAACqH,KAAK,CAAC,CAAC;QACzC,OAAO,IAAIyO,SAAS,CAACxP,CAAC,EAAEC,CAAC,CAAC;MAC5B;MACA,IAAIlD,MAAM,KAAK,WAAW,EAAE;QAC1B2S,KAAK,GAAG3O,KAAK,CAAC,CAAC,CAAC;QAChBhE,MAAM,GAAG,SAAS;QAClBgE,KAAK,GAAGA,KAAK,CAAChC,QAAQ,CAAC,CAAC,CAAC;MAC3B;MACA,MAAM0F,CAAC,GAAG9D,EAAE,CAACE,KAAK;MAClB,MAAMb,CAAC,GAAGe,KAAK,CAAChC,QAAQ,CAAC,CAAC,EAAE0F,CAAC,CAAC;MAC9B,MAAMxE,CAAC,GAAGc,KAAK,CAAChC,QAAQ,CAAC0F,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;MAClC,OAAO,IAAI+K,SAAS,CAAC7O,EAAE,CAACK,SAAS,CAAChB,CAAC,CAAC,EAAEW,EAAE,CAACK,SAAS,CAACf,CAAC,CAAC,EAAEyP,KAAK,CAAC;IAC/D;IAEA,OAAOrI,OAAOA,CAACjI,GAAW,EAAErC,MAAuB;MACjD,OAAO,IAAI,CAACiE,SAAS,CAAC7G,UAAU,CAACiF,GAAG,CAAC,EAAErC,MAAM,CAAC;IAChD;IAEA4S,cAAcA,CAACF,QAAgB;MAC7B,OAAO,IAAID,SAAS,CAAC,IAAI,CAACxP,CAAC,EAAE,IAAI,CAACC,CAAC,EAAEwP,QAAQ,CAAuB;IACtE;IAEAG,gBAAgBA,CAACC,WAAgB;MAC/B,MAAMC,WAAW,GAAGvO,EAAE,CAAC0E,KAAK;MAC5B,MAAM;QAAEjG,CAAC;QAAEC,CAAC;QAAEwP,QAAQ,EAAEM;MAAG,CAAE,GAAG,IAAI;MACpC,IAAIA,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC/S,QAAQ,CAAC+S,GAAG,CAAC,EAAE,MAAM,IAAIlT,KAAK,CAAC,qBAAqB,CAAC;MAEtF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMmT,WAAW,GAAGrO,WAAW,GAAGlG,GAAG,GAAGqU,WAAW;MACnD,IAAIE,WAAW,IAAID,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIlT,KAAK,CAAC,wCAAwC,CAAC;MAErF,MAAMoT,IAAI,GAAGF,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,GAAG/P,CAAC,GAAG2B,WAAW,GAAG3B,CAAC;MACzD,IAAI,CAACuB,EAAE,CAACwC,OAAO,CAACkM,IAAI,CAAC,EAAE,MAAM,IAAIpT,KAAK,CAAC,4BAA4B,CAAC;MACpE,MAAMmG,CAAC,GAAGzB,EAAE,CAACQ,OAAO,CAACkO,IAAI,CAAC;MAC1B,MAAMC,CAAC,GAAGnK,KAAK,CAAC/E,SAAS,CAAChH,WAAW,CAACqJ,OAAO,CAAC,CAAC0M,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE/M,CAAC,CAAC,CAAC;MACnE,MAAMmN,EAAE,GAAGxP,EAAE,CAAC8F,GAAG,CAACwJ,IAAI,CAAC,CAAC,CAAC;MACzB,MAAMxO,CAAC,GAAGkN,aAAa,CAACzU,WAAW,CAAC,SAAS,EAAE2V,WAAW,CAAC,CAAC,CAAC,CAAC;MAC9D,MAAMO,EAAE,GAAGzP,EAAE,CAAC0P,MAAM,CAAC,CAAC5O,CAAC,GAAG0O,EAAE,CAAC,CAAC,CAAC;MAC/B,MAAMG,EAAE,GAAG3P,EAAE,CAAC0P,MAAM,CAACpQ,CAAC,GAAGkQ,EAAE,CAAC,CAAC,CAAC;MAC9B;MACA,MAAMnG,CAAC,GAAGjE,KAAK,CAACiF,IAAI,CAACvB,cAAc,CAAC2G,EAAE,CAAC,CAACnL,GAAG,CAACiL,CAAC,CAACzG,cAAc,CAAC6G,EAAE,CAAC,CAAC;MACjE,IAAItG,CAAC,CAAC9H,GAAG,EAAE,EAAE,MAAM,IAAIrF,KAAK,CAAC,mBAAmB,CAAC;MACjDmN,CAAC,CAAC5C,cAAc,EAAE;MAClB,OAAO4C,CAAC;IACV;IAEA;IACAuG,QAAQA,CAAA;MACN,OAAOtB,qBAAqB,CAAC,IAAI,CAAChP,CAAC,CAAC;IACtC;IAEA8B,OAAOA,CAAChF,MAAA,GAAyBiS,qBAAqB;MACpDlS,iBAAiB,CAACC,MAAM,CAAC;MACzB,IAAIA,MAAM,KAAK,KAAK,EAAE,OAAO5C,UAAU,CAAC2D,GAAG,CAACoC,UAAU,CAAC,IAAI,CAAC,CAAC;MAC7D,MAAMF,CAAC,GAAGW,EAAE,CAACoB,OAAO,CAAC,IAAI,CAAC/B,CAAC,CAAC;MAC5B,MAAMC,CAAC,GAAGU,EAAE,CAACoB,OAAO,CAAC,IAAI,CAAC9B,CAAC,CAAC;MAC5B,IAAIlD,MAAM,KAAK,WAAW,EAAE;QAC1B,IAAI,IAAI,CAAC0S,QAAQ,IAAI,IAAI,EAAE,MAAM,IAAI5S,KAAK,CAAC,8BAA8B,CAAC;QAC1E,OAAO7C,WAAW,CAACsJ,UAAU,CAACC,EAAE,CAAC,IAAI,CAACkM,QAAQ,CAAC,EAAEzP,CAAC,EAAEC,CAAC,CAAC;MACxD;MACA,OAAOjG,WAAW,CAACgG,CAAC,EAAEC,CAAC,CAAC;IAC1B;IAEAmK,KAAKA,CAACrN,MAAuB;MAC3B,OAAOjD,UAAU,CAAC,IAAI,CAACiI,OAAO,CAAChF,MAAM,CAAC,CAAC;IACzC;IAEA;IACAqK,cAAcA,CAAA,GAAU;IACxB,OAAOoJ,WAAWA,CAACpR,GAAQ;MACzB,OAAOoQ,SAAS,CAACxO,SAAS,CAAC9G,WAAW,CAAC,KAAK,EAAEkF,GAAG,CAAC,EAAE,SAAS,CAAC;IAChE;IACA,OAAOqR,OAAOA,CAACrR,GAAQ;MACrB,OAAOoQ,SAAS,CAACxO,SAAS,CAAC9G,WAAW,CAAC,KAAK,EAAEkF,GAAG,CAAC,EAAE,KAAK,CAAC;IAC5D;IACAsR,UAAUA,CAAA;MACR,OAAO,IAAI,CAACH,QAAQ,EAAE,GAAG,IAAIf,SAAS,CAAC,IAAI,CAACxP,CAAC,EAAEW,EAAE,CAAC6D,GAAG,CAAC,IAAI,CAACvE,CAAC,CAAC,EAAE,IAAI,CAACwP,QAAQ,CAAC,GAAG,IAAI;IACtF;IACAkB,aAAaA,CAAA;MACX,OAAO,IAAI,CAAC5O,OAAO,CAAC,KAAK,CAAC;IAC5B;IACA6O,QAAQA,CAAA;MACN,OAAO9W,UAAU,CAAC,IAAI,CAACiI,OAAO,CAAC,KAAK,CAAC,CAAC;IACxC;IACA8O,iBAAiBA,CAAA;MACf,OAAO,IAAI,CAAC9O,OAAO,CAAC,SAAS,CAAC;IAChC;IACA+O,YAAYA,CAAA;MACV,OAAOhX,UAAU,CAAC,IAAI,CAACiI,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5C;;EAIF;EACA;EACA;EACA;EACA,MAAM2M,QAAQ,GACZD,SAAS,CAACC,QAAQ,IAClB,SAASqC,YAAYA,CAAChQ,KAAiB;IACrC;IACA,IAAIA,KAAK,CAAC1C,MAAM,GAAG,IAAI,EAAE,MAAM,IAAIxB,KAAK,CAAC,oBAAoB,CAAC;IAC9D;IACA;IACA,MAAMtB,GAAG,GAAGxB,eAAe,CAACgH,KAAK,CAAC,CAAC,CAAC;IACpC,MAAMiQ,KAAK,GAAGjQ,KAAK,CAAC1C,MAAM,GAAG,CAAC,GAAGwQ,MAAM,CAAC,CAAC;IACzC,OAAOmC,KAAK,GAAG,CAAC,GAAGzV,GAAG,IAAIgF,MAAM,CAACyQ,KAAK,CAAC,GAAGzV,GAAG;EAC/C,CAAC;EACH,MAAMoT,aAAa,GACjBF,SAAS,CAACE,aAAa,IACvB,SAASsC,iBAAiBA,CAAClQ,KAAiB;IAC1C,OAAOJ,EAAE,CAAC0P,MAAM,CAAC3B,QAAQ,CAAC3N,KAAK,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC;EACH;EACA,MAAMmQ,UAAU,GAAGrX,OAAO,CAACgV,MAAM,CAAC;EAClC;EACA,SAASsC,UAAUA,CAAC5V,GAAW;IAC7B;IACA5B,QAAQ,CAAC,UAAU,GAAGkV,MAAM,EAAEtT,GAAG,EAAEgB,GAAG,EAAE2U,UAAU,CAAC;IACnD,OAAOvQ,EAAE,CAACoB,OAAO,CAACxG,GAAG,CAAC;EACxB;EAEA,SAAS6V,kBAAkBA,CAAC/M,OAAmB,EAAE3G,OAAgB;IAC/DhE,MAAM,CAAC2K,OAAO,EAAE7G,SAAS,EAAE,SAAS,CAAC;IACrC,OAAOE,OAAO,GAAGhE,MAAM,CAAC8U,IAAI,CAACnK,OAAO,CAAC,EAAE7G,SAAS,EAAE,mBAAmB,CAAC,GAAG6G,OAAO;EAClF;EAEA;;;;;;;;EAQA,SAASgN,OAAOA,CAAChN,OAAmB,EAAE0G,UAAmB,EAAE7N,IAAmB;IAC5E,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACoU,IAAI,CAAE3V,CAAC,IAAKA,CAAC,IAAIuB,IAAI,CAAC,EACnD,MAAM,IAAIL,KAAK,CAAC,qCAAqC,CAAC;IACxD,MAAM;MAAEY,IAAI;MAAEC,OAAO;MAAEqR;IAAY,CAAE,GAAG9R,eAAe,CAACC,IAAI,EAAE4R,cAAc,CAAC;IAC7EzK,OAAO,GAAG+M,kBAAkB,CAAC/M,OAAO,EAAE3G,OAAO,CAAC,CAAC,CAAC;IAChD;IACA;IACA;IACA,MAAM6T,KAAK,GAAG5C,aAAa,CAACtK,OAAO,CAAC;IACpC,MAAMmN,CAAC,GAAG9Q,cAAc,CAACC,EAAE,EAAEoK,UAAU,CAAC,CAAC,CAAC;IAC1C,MAAM0G,QAAQ,GAAG,CAACN,UAAU,CAACK,CAAC,CAAC,EAAEL,UAAU,CAACI,KAAK,CAAC,CAAC;IACnD;IACA,IAAIxC,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,KAAK,EAAE;MAClD;MACA;MACA,MAAM2C,CAAC,GAAG3C,YAAY,KAAK,IAAI,GAAGvU,WAAW,CAACgI,OAAO,CAACwK,SAAS,CAAC,GAAG+B,YAAY;MAC/E0C,QAAQ,CAACE,IAAI,CAACzX,WAAW,CAAC,cAAc,EAAEwX,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;IACA,MAAMnE,IAAI,GAAGvT,WAAW,CAAC,GAAGyX,QAAQ,CAAC,CAAC,CAAC;IACvC,MAAM5T,CAAC,GAAG0T,KAAK,CAAC,CAAC;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASK,KAAKA,CAACC,MAAkB;MAC/B;MACA;MACA,MAAMlW,CAAC,GAAG+S,QAAQ,CAACmD,MAAM,CAAC,CAAC,CAAC;MAC5B,IAAI,CAAClR,EAAE,CAACO,WAAW,CAACvF,CAAC,CAAC,EAAE,OAAO,CAAC;MAChC,MAAMmW,EAAE,GAAGnR,EAAE,CAAC8F,GAAG,CAAC9K,CAAC,CAAC,CAAC,CAAC;MACtB,MAAMyP,CAAC,GAAGrF,KAAK,CAACiF,IAAI,CAACrD,QAAQ,CAAChM,CAAC,CAAC,CAACuH,QAAQ,EAAE,CAAC,CAAC;MAC7C,MAAMlD,CAAC,GAAGW,EAAE,CAAC0P,MAAM,CAACjF,CAAC,CAACpI,CAAC,CAAC,CAAC,CAAC;MAC1B,IAAIhD,CAAC,KAAKzD,GAAG,EAAE;MACf,MAAM0D,CAAC,GAAGU,EAAE,CAAC0P,MAAM,CAACyB,EAAE,GAAGnR,EAAE,CAAC0P,MAAM,CAACxS,CAAC,GAAGmC,CAAC,GAAGwR,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,IAAIvR,CAAC,KAAK1D,GAAG,EAAE;MACf,IAAIkT,QAAQ,GAAG,CAACrE,CAAC,CAACpI,CAAC,KAAKhD,CAAC,GAAG,CAAC,GAAG,CAAC,IAAIX,MAAM,CAAC+L,CAAC,CAACnI,CAAC,GAAGrG,GAAG,CAAC,CAAC,CAAC;MACxD,IAAImV,KAAK,GAAG9R,CAAC;MACb,IAAIxC,IAAI,IAAIwR,qBAAqB,CAAChP,CAAC,CAAC,EAAE;QACpC8R,KAAK,GAAGpR,EAAE,CAAC6D,GAAG,CAACvE,CAAC,CAAC,CAAC,CAAC;QACnBwP,QAAQ,IAAI,CAAC,CAAC,CAAC;MACjB;MACA,OAAO,IAAID,SAAS,CAACxP,CAAC,EAAE+R,KAAK,EAAEtC,QAAQ,CAAuB,CAAC,CAAC;IAClE;IACA,OAAO;MAAElC,IAAI;MAAEqE;IAAK,CAAE;EACxB;EAEA;;;;;;;;;;;EAWA,SAASI,IAAIA,CAAC3N,OAAY,EAAE2I,SAAkB,EAAE9P,IAAA,GAAsB,EAAE;IACtEmH,OAAO,GAAGnK,WAAW,CAAC,SAAS,EAAEmK,OAAO,CAAC;IACzC,MAAM;MAAEkJ,IAAI;MAAEqE;IAAK,CAAE,GAAGP,OAAO,CAAChN,OAAO,EAAE2I,SAAS,EAAE9P,IAAI,CAAC,CAAC,CAAC;IAC3D,MAAM+U,IAAI,GAAGhY,cAAc,CAAqBuU,IAAI,CAAC0D,SAAS,EAAEvR,EAAE,CAACE,KAAK,EAAE1H,IAAI,CAAC;IAC/E,MAAMgH,GAAG,GAAG8R,IAAI,CAAC1E,IAAI,EAAEqE,KAAK,CAAC,CAAC,CAAC;IAC/B,OAAOzR,GAAG;EACZ;EAEA,SAASgS,aAAaA,CAACC,EAAuB;IAC5C;IACA,IAAIjS,GAAG,GAA0B3C,SAAS;IAC1C,MAAM6U,KAAK,GAAG,OAAOD,EAAE,KAAK,QAAQ,IAAI/X,OAAO,CAAC+X,EAAE,CAAC;IACnD,MAAME,KAAK,GACT,CAACD,KAAK,IACND,EAAE,KAAK,IAAI,IACX,OAAOA,EAAE,KAAK,QAAQ,IACtB,OAAOA,EAAE,CAACpS,CAAC,KAAK,QAAQ,IACxB,OAAOoS,EAAE,CAACnS,CAAC,KAAK,QAAQ;IAC1B,IAAI,CAACoS,KAAK,IAAI,CAACC,KAAK,EAClB,MAAM,IAAIzV,KAAK,CAAC,0EAA0E,CAAC;IAC7F,IAAIyV,KAAK,EAAE;MACTnS,GAAG,GAAG,IAAIqP,SAAS,CAAC4C,EAAE,CAACpS,CAAC,EAAEoS,EAAE,CAACnS,CAAC,CAAC;IACjC,CAAC,MAAM,IAAIoS,KAAK,EAAE;MAChB,IAAI;QACFlS,GAAG,GAAGqP,SAAS,CAACxO,SAAS,CAAC9G,WAAW,CAAC,KAAK,EAAEkY,EAAE,CAAC,EAAE,KAAK,CAAC;MAC1D,CAAC,CAAC,OAAOG,QAAQ,EAAE;QACjB,IAAI,EAAEA,QAAQ,YAAYzU,GAAG,CAACC,GAAG,CAAC,EAAE,MAAMwU,QAAQ;MACpD;MACA,IAAI,CAACpS,GAAG,EAAE;QACR,IAAI;UACFA,GAAG,GAAGqP,SAAS,CAACxO,SAAS,CAAC9G,WAAW,CAAC,KAAK,EAAEkY,EAAE,CAAC,EAAE,SAAS,CAAC;QAC9D,CAAC,CAAC,OAAOnR,KAAK,EAAE;UACd,OAAO,KAAK;QACd;MACF;IACF;IACA,IAAI,CAACd,GAAG,EAAE,OAAO,KAAK;IACtB,OAAOA,GAAG;EACZ;EAEA;;;;;;;;;;;;;EAaA,SAASqS,MAAMA,CACbtF,SAA8B,EAC9B7I,OAAY,EACZZ,SAAc,EACdvG,IAAA,GAAwB,EAAE;IAE1B,MAAM;MAAEO,IAAI;MAAEC,OAAO;MAAEX;IAAM,CAAE,GAAGE,eAAe,CAACC,IAAI,EAAE4R,cAAc,CAAC;IACvErL,SAAS,GAAGvJ,WAAW,CAAC,WAAW,EAAEuJ,SAAS,CAAC;IAC/CY,OAAO,GAAG+M,kBAAkB,CAAClX,WAAW,CAAC,SAAS,EAAEmK,OAAO,CAAC,EAAE3G,OAAO,CAAC;IACtE,IAAI,QAAQ,IAAIR,IAAI,EAAE,MAAM,IAAIL,KAAK,CAAC,oCAAoC,CAAC;IAC3E,MAAMsD,GAAG,GACPpD,MAAM,KAAKS,SAAS,GAChB2U,aAAa,CAACjF,SAAS,CAAC,GACxBsC,SAAS,CAACxO,SAAS,CAAC9G,WAAW,CAAC,KAAK,EAAEgT,SAAgB,CAAC,EAAEnQ,MAAM,CAAC;IACvE,IAAIoD,GAAG,KAAK,KAAK,EAAE,OAAO,KAAK;IAC/B,IAAI;MACF,MAAMgH,CAAC,GAAGpB,KAAK,CAAC/E,SAAS,CAACyC,SAAS,CAAC;MACpC,IAAIhG,IAAI,IAAI0C,GAAG,CAACoQ,QAAQ,EAAE,EAAE,OAAO,KAAK;MACxC,MAAM;QAAEvQ,CAAC;QAAEC;MAAC,CAAE,GAAGE,GAAG;MACpB,MAAMsB,CAAC,GAAGkN,aAAa,CAACtK,OAAO,CAAC,CAAC,CAAC;MAClC,MAAMoO,EAAE,GAAG9R,EAAE,CAAC8F,GAAG,CAACxG,CAAC,CAAC,CAAC,CAAC;MACtB,MAAMmQ,EAAE,GAAGzP,EAAE,CAAC0P,MAAM,CAAC5O,CAAC,GAAGgR,EAAE,CAAC,CAAC,CAAC;MAC9B,MAAMnC,EAAE,GAAG3P,EAAE,CAAC0P,MAAM,CAACrQ,CAAC,GAAGyS,EAAE,CAAC,CAAC,CAAC;MAC9B,MAAMvC,CAAC,GAAGnK,KAAK,CAACiF,IAAI,CAACvB,cAAc,CAAC2G,EAAE,CAAC,CAACnL,GAAG,CAACkC,CAAC,CAACsC,cAAc,CAAC6G,EAAE,CAAC,CAAC,CAAC,CAAC;MACnE,IAAIJ,CAAC,CAAChO,GAAG,EAAE,EAAE,OAAO,KAAK;MACzB,MAAMjD,CAAC,GAAG0B,EAAE,CAAC0P,MAAM,CAACH,CAAC,CAAClN,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAO/D,CAAC,KAAKe,CAAC;IAChB,CAAC,CAAC,OAAO0R,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF;EAEA,SAAS9B,gBAAgBA,CACvB1C,SAAqB,EACrB7I,OAAmB,EACnBnH,IAAA,GAAyB,EAAE;IAE3B,MAAM;MAAEQ;IAAO,CAAE,GAAGT,eAAe,CAACC,IAAI,EAAE4R,cAAc,CAAC;IACzDzK,OAAO,GAAG+M,kBAAkB,CAAC/M,OAAO,EAAE3G,OAAO,CAAC;IAC9C,OAAO8R,SAAS,CAACxO,SAAS,CAACkM,SAAS,EAAE,WAAW,CAAC,CAAC0C,gBAAgB,CAACvL,OAAO,CAAC,CAACtC,OAAO,EAAE;EACxF;EAEA,OAAOzE,MAAM,CAAC2J,MAAM,CAAC;IACnB2G,MAAM;IACND,YAAY;IACZK,eAAe;IACfG,KAAK;IACL3L,OAAO;IACPuD,KAAK;IACLiM,IAAI;IACJQ,MAAM;IACN5C,gBAAgB;IAChBJ,SAAS;IACThB;GACD,CAAC;AACJ;AAqGA;AACA,OAAM,SAAUkE,iBAAiBA,CAAIC,CAA+B;EAClE,MAAM;IAAEnR,KAAK;IAAEoR;EAAS,CAAE,GAAGC,+BAA+B,CAACF,CAAC,CAAC;EAC/D,MAAM5M,KAAK,GAAG5E,YAAY,CAACK,KAAK,EAAEoR,SAAS,CAAC;EAC5C,OAAOE,iCAAiC,CAACH,CAAC,EAAE5M,KAAK,CAAC;AACpD;AAYA,SAAS8M,+BAA+BA,CAAIF,CAAqB;EAC/D,MAAMnR,KAAK,GAAuB;IAChCW,CAAC,EAAEwQ,CAAC,CAACxQ,CAAC;IACNnD,CAAC,EAAE2T,CAAC,CAAC3T,CAAC;IACNmH,CAAC,EAAEwM,CAAC,CAACpR,EAAE,CAAC0E,KAAK;IACbpK,CAAC,EAAE8W,CAAC,CAAC9W,CAAC;IACN4F,CAAC,EAAEkR,CAAC,CAAClR,CAAC;IACN4D,EAAE,EAAEsN,CAAC,CAACtN,EAAE;IACRC,EAAE,EAAEqN,CAAC,CAACrN;GACP;EACD,MAAM/D,EAAE,GAAGoR,CAAC,CAACpR,EAAE;EACf,IAAIwM,cAAc,GAAG4E,CAAC,CAACI,wBAAwB,GAC3C1Q,KAAK,CAAC2Q,IAAI,CAAC,IAAIC,GAAG,CAACN,CAAC,CAACI,wBAAwB,CAACG,GAAG,CAAEhU,CAAC,IAAKxC,IAAI,CAACC,IAAI,CAACuC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAC5E1B,SAAS;EACb,MAAMmD,EAAE,GAAG3F,KAAK,CAACwG,KAAK,CAAC3F,CAAC,EAAE;IACxBqP,IAAI,EAAEyH,CAAC,CAACQ,UAAU;IAClBpF,cAAc,EAAEA,cAAc;IAC9BqF,YAAY,EAAET,CAAC,CAAC1Q;GACjB,CAAC;EACF,MAAM2Q,SAAS,GAA4B;IACzCrR,EAAE;IACFZ,EAAE;IACFiB,kBAAkB,EAAE+Q,CAAC,CAAC/Q,kBAAkB;IACxCI,IAAI,EAAE2Q,CAAC,CAAC3Q,IAAI;IACZF,aAAa,EAAE6Q,CAAC,CAAC7Q,aAAa;IAC9BD,aAAa,EAAE8Q,CAAC,CAAC9Q,aAAa;IAC9Bb,SAAS,EAAE2R,CAAC,CAAC3R,SAAS;IACtBe,OAAO,EAAE4Q,CAAC,CAAC5Q;GACZ;EACD,OAAO;IAAEP,KAAK;IAAEoR;EAAS,CAAE;AAC7B;AACA,SAASS,yBAAyBA,CAACV,CAAY;EAC7C,MAAM;IAAEnR,KAAK;IAAEoR;EAAS,CAAE,GAAGC,+BAA+B,CAACF,CAAC,CAAC;EAC/D,MAAMlE,SAAS,GAAc;IAC3BtV,IAAI,EAAEwZ,CAAC,CAACxZ,IAAI;IACZqB,WAAW,EAAEmY,CAAC,CAACnY,WAAW;IAC1BiD,IAAI,EAAEkV,CAAC,CAAClV,IAAI;IACZiR,QAAQ,EAAEiE,CAAC,CAACjE,QAAQ;IACpBC,aAAa,EAAEgE,CAAC,CAAChE;GAClB;EACD,OAAO;IAAEnN,KAAK;IAAEoR,SAAS;IAAEpE,IAAI,EAAEmE,CAAC,CAACnE,IAAI;IAAEC;EAAS,CAAE;AACtD;AACA,OAAM,SAAU6E,kBAAkBA,CAAI/R,EAAa,EAAEY,CAAI,EAAEnD,CAAI;EAC7D;;;;EAIA,SAASiF,mBAAmBA,CAACjB,CAAI;IAC/B,MAAM6B,EAAE,GAAGtD,EAAE,CAACuD,GAAG,CAAC9B,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM+B,EAAE,GAAGxD,EAAE,CAACyD,GAAG,CAACH,EAAE,EAAE7B,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAOzB,EAAE,CAAC0D,GAAG,CAAC1D,EAAE,CAAC0D,GAAG,CAACF,EAAE,EAAExD,EAAE,CAACyD,GAAG,CAAChC,CAAC,EAAEb,CAAC,CAAC,CAAC,EAAEnD,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA,OAAOiF,mBAAmB;AAC5B;AACA,SAAS6O,iCAAiCA,CACxCH,CAAqB,EACrB5M,KAA8B;EAE9B,MAAM;IAAExE,EAAE;IAAEZ;EAAE,CAAE,GAAGoF,KAAK;EACxB,SAASwN,kBAAkBA,CAAChY,GAAW;IACrC,OAAOnB,OAAO,CAACmB,GAAG,EAAEqB,GAAG,EAAE+D,EAAE,CAACsF,KAAK,CAAC;EACpC;EACA,MAAMhC,mBAAmB,GAAGqP,kBAAkB,CAAC/R,EAAE,EAAEoR,CAAC,CAACxQ,CAAC,EAAEwQ,CAAC,CAAC3T,CAAC,CAAC;EAC5D,OAAO1B,MAAM,CAACgQ,MAAM,CAClB,EAAE,EACF;IACE9L,KAAK,EAAEmR,CAAC;IACR5M,KAAK,EAAEA,KAAK;IACZyN,eAAe,EAAEzN,KAAK;IACtBuI,sBAAsB,EAAG1N,GAAY,IAAKF,cAAc,CAACC,EAAE,EAAEC,GAAG,CAAC;IACjEqD,mBAAmB;IACnBsP;GACD,CACF;AACH;AACA,SAASE,2BAA2BA,CAACd,CAAY,EAAEe,MAAa;EAC9D,MAAM3N,KAAK,GAAG2N,MAAM,CAAC3N,KAAK;EAC1B,OAAOzI,MAAM,CAACgQ,MAAM,CAAC,EAAE,EAAEoG,MAAM,EAAE;IAC/BF,eAAe,EAAEzN,KAAK;IACtBvE,KAAK,EAAElE,MAAM,CAACgQ,MAAM,CAAC,EAAE,EAAEqF,CAAC,EAAEvX,OAAO,CAAC2K,KAAK,CAACpF,EAAE,CAACsF,KAAK,EAAEF,KAAK,CAACpF,EAAE,CAACuK,IAAI,CAAC;GACnE,CAAC;AACJ;AAEA;AACA,OAAM,SAAUyI,WAAWA,CAAChB,CAAY;EACtC,MAAM;IAAEnR,KAAK;IAAEoR,SAAS;IAAEpE,IAAI;IAAEC;EAAS,CAAE,GAAG4E,yBAAyB,CAACV,CAAC,CAAC;EAC1E,MAAM5M,KAAK,GAAG5E,YAAY,CAACK,KAAK,EAAEoR,SAAS,CAAC;EAC5C,MAAMgB,KAAK,GAAGrF,KAAK,CAACxI,KAAK,EAAEyI,IAAI,EAAEC,SAAS,CAAC;EAC3C,OAAOgF,2BAA2B,CAACd,CAAC,EAAEiB,KAAK,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}