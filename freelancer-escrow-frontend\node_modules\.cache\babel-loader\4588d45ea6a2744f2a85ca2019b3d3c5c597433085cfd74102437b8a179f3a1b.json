{"ast": null, "code": "/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { weierstrass } from \"./abstract/weierstrass.js\";\n/** connects noble-curves to noble-hashes */\nexport function getHash(hash) {\n  return {\n    hash\n  };\n}\n/** @deprecated use new `weierstrass()` and `ecdsa()` methods */\nexport function createCurve(curveDef, defHash) {\n  const create = hash => weierstrass({\n    ...curveDef,\n    hash: hash\n  });\n  return {\n    ...create(defHash),\n    create\n  };\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "getHash", "hash", "createCurve", "curveDef", "defHash", "create"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\_shortw_utils.ts"], "sourcesContent": ["/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { type CurveFn, type CurveType, weierstrass } from './abstract/weierstrass.ts';\nimport type { CHash } from './utils.ts';\n\n/** connects noble-curves to noble-hashes */\nexport function getHash(hash: CHash): { hash: CHash } {\n  return { hash };\n}\n/** Same API as @noble/hashes, with ability to create curve with custom hash */\nexport type CurveDef = Readonly<Omit<CurveType, 'hash'>>;\nexport type CurveFnWithCreate = CurveFn & { create: (hash: CHash) => CurveFn };\n\n/** @deprecated use new `weierstrass()` and `ecdsa()` methods */\nexport function createCurve(curveDef: CurveDef, defHash: CHash): CurveFnWithCreate {\n  const create = (hash: CHash): CurveFn => weierstrass({ ...curveDef, hash: hash });\n  return { ...create(defHash), create };\n}\n"], "mappings": "AAAA;;;;AAIA;AACA,SAAuCA,WAAW,QAAQ,2BAA2B;AAGrF;AACA,OAAM,SAAUC,OAAOA,CAACC,IAAW;EACjC,OAAO;IAAEA;EAAI,CAAE;AACjB;AAKA;AACA,OAAM,SAAUC,WAAWA,CAACC,QAAkB,EAAEC,OAAc;EAC5D,MAAMC,MAAM,GAAIJ,IAAW,IAAcF,WAAW,CAAC;IAAE,GAAGI,QAAQ;IAAEF,IAAI,EAAEA;EAAI,CAAE,CAAC;EACjF,OAAO;IAAE,GAAGI,MAAM,CAACD,OAAO,CAAC;IAAEC;EAAM,CAAE;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}