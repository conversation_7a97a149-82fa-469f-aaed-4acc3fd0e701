{"ast": null, "code": "/**\n * PBKDF (RFC 2898). Can be used to create a key from password and salt.\n * @module\n */\nimport { hmac } from \"./hmac.js\";\n// prettier-ignore\nimport { ahash, anumber, asyncLoop, checkOpts, clean, createView, Hash, kdfInputToBytes } from \"./utils.js\";\n// Common prologue and epilogue for sync/async functions\nfunction pbkdf2Init(hash, _password, _salt, _opts) {\n  ahash(hash);\n  const opts = checkOpts({\n    dkLen: 32,\n    asyncTick: 10\n  }, _opts);\n  const {\n    c,\n    dkLen,\n    asyncTick\n  } = opts;\n  anumber(c);\n  anumber(dkLen);\n  anumber(asyncTick);\n  if (c < 1) throw new Error('iterations (c) should be >= 1');\n  const password = kdfInputToBytes(_password);\n  const salt = kdfInputToBytes(_salt);\n  // DK = PBKDF2(PRF, Password, Salt, c, dkLen);\n  const DK = new Uint8Array(dkLen);\n  // U1 = PRF(Password, Salt + INT_32_BE(i))\n  const PRF = hmac.create(hash, password);\n  const PRFSalt = PRF._cloneInto().update(salt);\n  return {\n    c,\n    dkLen,\n    asyncTick,\n    DK,\n    PRF,\n    PRFSalt\n  };\n}\nfunction pbkdf2Output(PRF, PRFSalt, DK, prfW, u) {\n  PRF.destroy();\n  PRFSalt.destroy();\n  if (prfW) prfW.destroy();\n  clean(u);\n  return DK;\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function\n * @param hash - hash function that would be used e.g. sha256\n * @param password - password from which a derived key is generated\n * @param salt - cryptographic salt\n * @param opts - {c, dkLen} where c is work factor and dkLen is output message size\n * @example\n * const key = pbkdf2(sha256, 'password', 'salt', { dkLen: 32, c: Math.pow(2, 18) });\n */\nexport function pbkdf2(hash, password, salt, opts) {\n  const {\n    c,\n    dkLen,\n    DK,\n    PRF,\n    PRFSalt\n  } = pbkdf2Init(hash, password, salt, opts);\n  let prfW; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    for (let ui = 1; ui < c; ui++) {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    }\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function. Async version.\n * @example\n * await pbkdf2Async(sha256, 'password', 'salt', { dkLen: 32, c: 500_000 });\n */\nexport async function pbkdf2Async(hash, password, salt, opts) {\n  const {\n    c,\n    dkLen,\n    asyncTick,\n    DK,\n    PRF,\n    PRFSalt\n  } = pbkdf2Init(hash, password, salt, opts);\n  let prfW; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    await asyncLoop(c - 1, asyncTick, () => {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    });\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}", "map": {"version": 3, "names": ["hmac", "ahash", "anumber", "asyncLoop", "checkOpts", "clean", "createView", "Hash", "kdfInputToBytes", "pbkdf2Init", "hash", "_password", "_salt", "_opts", "opts", "dkLen", "asyncTick", "c", "Error", "password", "salt", "DK", "Uint8Array", "PRF", "create", "PRFSalt", "_cloneInto", "update", "pbkdf2Output", "prfW", "u", "destroy", "pbkdf2", "arr", "view", "outputLen", "ti", "pos", "Ti", "subarray", "setInt32", "digestInto", "set", "length", "ui", "i", "pbkdf2Async"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\pbkdf2.ts"], "sourcesContent": ["/**\n * PBKDF (RFC 2898). Can be used to create a key from password and salt.\n * @module\n */\nimport { hmac } from './hmac.ts';\n// prettier-ignore\nimport {\n  ahash, anumber,\n  asyncLoop, checkOpts, clean, createView, Hash, kdfInputToBytes,\n  type CHash,\n  type KDFInput\n} from './utils.ts';\n\nexport type Pbkdf2Opt = {\n  c: number; // Iterations\n  dkLen?: number; // Desired key length in bytes (Intended output length in octets of the derived key\n  asyncTick?: number; // Maximum time in ms for which async function can block execution\n};\n// Common prologue and epilogue for sync/async functions\nfunction pbkdf2Init(hash: CHash, _password: KDFInput, _salt: KDFInput, _opts: Pbkdf2Opt) {\n  ahash(hash);\n  const opts = checkOpts({ dkLen: 32, asyncTick: 10 }, _opts);\n  const { c, dkLen, asyncTick } = opts;\n  anumber(c);\n  anumber(dkLen);\n  anumber(asyncTick);\n  if (c < 1) throw new Error('iterations (c) should be >= 1');\n  const password = kdfInputToBytes(_password);\n  const salt = kdfInputToBytes(_salt);\n  // DK = PBKDF2(PRF, Password, Salt, c, dkLen);\n  const DK = new Uint8Array(dkLen);\n  // U1 = PRF(Password, Salt + INT_32_BE(i))\n  const PRF = hmac.create(hash, password);\n  const PRFSalt = PRF._cloneInto().update(salt);\n  return { c, dkLen, asyncTick, DK, PRF, PRFSalt };\n}\n\nfunction pbkdf2Output<T extends Hash<T>>(\n  PRF: Hash<T>,\n  PRFSalt: Hash<T>,\n  DK: Uint8Array,\n  prfW: Hash<T>,\n  u: Uint8Array\n) {\n  PRF.destroy();\n  PRFSalt.destroy();\n  if (prfW) prfW.destroy();\n  clean(u);\n  return DK;\n}\n\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function\n * @param hash - hash function that would be used e.g. sha256\n * @param password - password from which a derived key is generated\n * @param salt - cryptographic salt\n * @param opts - {c, dkLen} where c is work factor and dkLen is output message size\n * @example\n * const key = pbkdf2(sha256, 'password', 'salt', { dkLen: 32, c: Math.pow(2, 18) });\n */\nexport function pbkdf2(\n  hash: CHash,\n  password: KDFInput,\n  salt: KDFInput,\n  opts: Pbkdf2Opt\n): Uint8Array {\n  const { c, dkLen, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n  let prfW: any; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    for (let ui = 1; ui < c; ui++) {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    }\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function. Async version.\n * @example\n * await pbkdf2Async(sha256, 'password', 'salt', { dkLen: 32, c: 500_000 });\n */\nexport async function pbkdf2Async(\n  hash: CHash,\n  password: KDFInput,\n  salt: KDFInput,\n  opts: Pbkdf2Opt\n): Promise<Uint8Array> {\n  const { c, dkLen, asyncTick, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n  let prfW: any; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    await asyncLoop(c - 1, asyncTick, () => {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    });\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n"], "mappings": "AAAA;;;;AAIA,SAASA,IAAI,QAAQ,WAAW;AAChC;AACA,SACEC,KAAK,EAAEC,OAAO,EACdC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAEC,eAAe,QAGzD,YAAY;AAOnB;AACA,SAASC,UAAUA,CAACC,IAAW,EAAEC,SAAmB,EAAEC,KAAe,EAAEC,KAAgB;EACrFZ,KAAK,CAACS,IAAI,CAAC;EACX,MAAMI,IAAI,GAAGV,SAAS,CAAC;IAAEW,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAE,CAAE,EAAEH,KAAK,CAAC;EAC3D,MAAM;IAAEI,CAAC;IAAEF,KAAK;IAAEC;EAAS,CAAE,GAAGF,IAAI;EACpCZ,OAAO,CAACe,CAAC,CAAC;EACVf,OAAO,CAACa,KAAK,CAAC;EACdb,OAAO,CAACc,SAAS,CAAC;EAClB,IAAIC,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;EAC3D,MAAMC,QAAQ,GAAGX,eAAe,CAACG,SAAS,CAAC;EAC3C,MAAMS,IAAI,GAAGZ,eAAe,CAACI,KAAK,CAAC;EACnC;EACA,MAAMS,EAAE,GAAG,IAAIC,UAAU,CAACP,KAAK,CAAC;EAChC;EACA,MAAMQ,GAAG,GAAGvB,IAAI,CAACwB,MAAM,CAACd,IAAI,EAAES,QAAQ,CAAC;EACvC,MAAMM,OAAO,GAAGF,GAAG,CAACG,UAAU,EAAE,CAACC,MAAM,CAACP,IAAI,CAAC;EAC7C,OAAO;IAAEH,CAAC;IAAEF,KAAK;IAAEC,SAAS;IAAEK,EAAE;IAAEE,GAAG;IAAEE;EAAO,CAAE;AAClD;AAEA,SAASG,YAAYA,CACnBL,GAAY,EACZE,OAAgB,EAChBJ,EAAc,EACdQ,IAAa,EACbC,CAAa;EAEbP,GAAG,CAACQ,OAAO,EAAE;EACbN,OAAO,CAACM,OAAO,EAAE;EACjB,IAAIF,IAAI,EAAEA,IAAI,CAACE,OAAO,EAAE;EACxB1B,KAAK,CAACyB,CAAC,CAAC;EACR,OAAOT,EAAE;AACX;AAEA;;;;;;;;;AASA,OAAM,SAAUW,MAAMA,CACpBtB,IAAW,EACXS,QAAkB,EAClBC,IAAc,EACdN,IAAe;EAEf,MAAM;IAAEG,CAAC;IAAEF,KAAK;IAAEM,EAAE;IAAEE,GAAG;IAAEE;EAAO,CAAE,GAAGhB,UAAU,CAACC,IAAI,EAAES,QAAQ,EAAEC,IAAI,EAAEN,IAAI,CAAC;EAC7E,IAAIe,IAAS,CAAC,CAAC;EACf,MAAMI,GAAG,GAAG,IAAIX,UAAU,CAAC,CAAC,CAAC;EAC7B,MAAMY,IAAI,GAAG5B,UAAU,CAAC2B,GAAG,CAAC;EAC5B,MAAMH,CAAC,GAAG,IAAIR,UAAU,CAACC,GAAG,CAACY,SAAS,CAAC;EACvC;EACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtB,KAAK,EAAEqB,EAAE,EAAE,EAAEC,GAAG,IAAId,GAAG,CAACY,SAAS,EAAE;IACjE;IACA,MAAMG,EAAE,GAAGjB,EAAE,CAACkB,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGd,GAAG,CAACY,SAAS,CAAC;IAChDD,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAEJ,EAAE,EAAE,KAAK,CAAC;IAC3B;IACA;IACA,CAACP,IAAI,GAAGJ,OAAO,CAACC,UAAU,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACM,GAAG,CAAC,CAACQ,UAAU,CAACX,CAAC,CAAC;IAC3DQ,EAAE,CAACI,GAAG,CAACZ,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAED,EAAE,CAACK,MAAM,CAAC,CAAC;IAChC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG3B,CAAC,EAAE2B,EAAE,EAAE,EAAE;MAC7B;MACArB,GAAG,CAACG,UAAU,CAACG,IAAI,CAAC,CAACF,MAAM,CAACG,CAAC,CAAC,CAACW,UAAU,CAACX,CAAC,CAAC;MAC5C,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACK,MAAM,EAAEE,CAAC,EAAE,EAAEP,EAAE,CAACO,CAAC,CAAC,IAAIf,CAAC,CAACe,CAAC,CAAC;IACnD;EACF;EACA,OAAOjB,YAAY,CAACL,GAAG,EAAEE,OAAO,EAAEJ,EAAE,EAAEQ,IAAI,EAAEC,CAAC,CAAC;AAChD;AAEA;;;;;AAKA,OAAO,eAAegB,WAAWA,CAC/BpC,IAAW,EACXS,QAAkB,EAClBC,IAAc,EACdN,IAAe;EAEf,MAAM;IAAEG,CAAC;IAAEF,KAAK;IAAEC,SAAS;IAAEK,EAAE;IAAEE,GAAG;IAAEE;EAAO,CAAE,GAAGhB,UAAU,CAACC,IAAI,EAAES,QAAQ,EAAEC,IAAI,EAAEN,IAAI,CAAC;EACxF,IAAIe,IAAS,CAAC,CAAC;EACf,MAAMI,GAAG,GAAG,IAAIX,UAAU,CAAC,CAAC,CAAC;EAC7B,MAAMY,IAAI,GAAG5B,UAAU,CAAC2B,GAAG,CAAC;EAC5B,MAAMH,CAAC,GAAG,IAAIR,UAAU,CAACC,GAAG,CAACY,SAAS,CAAC;EACvC;EACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGtB,KAAK,EAAEqB,EAAE,EAAE,EAAEC,GAAG,IAAId,GAAG,CAACY,SAAS,EAAE;IACjE;IACA,MAAMG,EAAE,GAAGjB,EAAE,CAACkB,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGd,GAAG,CAACY,SAAS,CAAC;IAChDD,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAEJ,EAAE,EAAE,KAAK,CAAC;IAC3B;IACA;IACA,CAACP,IAAI,GAAGJ,OAAO,CAACC,UAAU,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACM,GAAG,CAAC,CAACQ,UAAU,CAACX,CAAC,CAAC;IAC3DQ,EAAE,CAACI,GAAG,CAACZ,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAED,EAAE,CAACK,MAAM,CAAC,CAAC;IAChC,MAAMxC,SAAS,CAACc,CAAC,GAAG,CAAC,EAAED,SAAS,EAAE,MAAK;MACrC;MACAO,GAAG,CAACG,UAAU,CAACG,IAAI,CAAC,CAACF,MAAM,CAACG,CAAC,CAAC,CAACW,UAAU,CAACX,CAAC,CAAC;MAC5C,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACK,MAAM,EAAEE,CAAC,EAAE,EAAEP,EAAE,CAACO,CAAC,CAAC,IAAIf,CAAC,CAACe,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;EACA,OAAOjB,YAAY,CAACL,GAAG,EAAEE,OAAO,EAAEJ,EAAE,EAAEQ,IAAI,EAAEC,CAAC,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}