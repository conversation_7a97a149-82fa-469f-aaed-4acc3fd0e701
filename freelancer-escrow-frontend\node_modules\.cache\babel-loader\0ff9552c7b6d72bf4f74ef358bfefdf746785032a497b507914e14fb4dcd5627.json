{"ast": null, "code": "/**\n * ed25519 Twisted <PERSON> curve with following addons:\n * - X25519 ECDH\n * - Ristretto cofactor elimination\n * - Elligator hash-to-group / point indistinguishability\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha512 } from '@noble/hashes/sha2.js';\nimport { abytes, concatBytes, utf8ToBytes } from '@noble/hashes/utils.js';\nimport { pippenger } from \"./abstract/curve.js\";\nimport { PrimeEdwardsPoint, twistedEdwards } from \"./abstract/edwards.js\";\nimport { _DST_scalar, createHasher, expand_message_xmd } from \"./abstract/hash-to-curve.js\";\nimport { Field, FpInvertBatch, FpSqrtEven, isNegativeLE, mod, pow2 } from \"./abstract/modular.js\";\nimport { montgomery } from \"./abstract/montgomery.js\";\nimport { bytesToNumberLE, ensureBytes, equalBytes } from \"./utils.js\";\n// prettier-ignore\nconst _0n = /* @__PURE__ */BigInt(0),\n  _1n = BigInt(1),\n  _2n = BigInt(2),\n  _3n = BigInt(3);\n// prettier-ignore\nconst _5n = BigInt(5),\n  _8n = BigInt(8);\n// P = 2n**255n-19n\nconst ed25519_CURVE_p = BigInt('0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed');\n// N = 2n**252n + 27742317777372353535851937790883648493n\n// a = Fp.create(BigInt(-1))\n// d = -121665/121666 a.k.a. Fp.neg(121665 * Fp.inv(121666))\nconst ed25519_CURVE = /* @__PURE__ */(() => ({\n  p: ed25519_CURVE_p,\n  n: BigInt('0x1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed'),\n  h: _8n,\n  a: BigInt('0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec'),\n  d: BigInt('0x52036cee2b6ffe738cc740797779e89800700a4d4141d8ab75eb4dca135978a3'),\n  Gx: BigInt('0x216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a'),\n  Gy: BigInt('0x6666666666666666666666666666666666666666666666666666666666666658')\n}))();\nfunction ed25519_pow_2_252_3(x) {\n  // prettier-ignore\n  const _10n = BigInt(10),\n    _20n = BigInt(20),\n    _40n = BigInt(40),\n    _80n = BigInt(80);\n  const P = ed25519_CURVE_p;\n  const x2 = x * x % P;\n  const b2 = x2 * x % P; // x^3, 11\n  const b4 = pow2(b2, _2n, P) * b2 % P; // x^15, 1111\n  const b5 = pow2(b4, _1n, P) * x % P; // x^31\n  const b10 = pow2(b5, _5n, P) * b5 % P;\n  const b20 = pow2(b10, _10n, P) * b10 % P;\n  const b40 = pow2(b20, _20n, P) * b20 % P;\n  const b80 = pow2(b40, _40n, P) * b40 % P;\n  const b160 = pow2(b80, _80n, P) * b80 % P;\n  const b240 = pow2(b160, _80n, P) * b80 % P;\n  const b250 = pow2(b240, _10n, P) * b10 % P;\n  const pow_p_5_8 = pow2(b250, _2n, P) * x % P;\n  // ^ To pow to (p+3)/8, multiply it by x.\n  return {\n    pow_p_5_8,\n    b2\n  };\n}\nfunction adjustScalarBytes(bytes) {\n  // Section 5: For X25519, in order to decode 32 random bytes as an integer scalar,\n  // set the three least significant bits of the first byte\n  bytes[0] &= 248; // 0b1111_1000\n  // and the most significant bit of the last to zero,\n  bytes[31] &= 127; // 0b0111_1111\n  // set the second most significant bit of the last byte to 1\n  bytes[31] |= 64; // 0b0100_0000\n  return bytes;\n}\n// √(-1) aka √(a) aka 2^((p-1)/4)\n// Fp.sqrt(Fp.neg(1))\nconst ED25519_SQRT_M1 = /* @__PURE__ */BigInt('19681161376707505956807079304988542015446066515923890162744021073123829784752');\n// sqrt(u/v)\nfunction uvRatio(u, v) {\n  const P = ed25519_CURVE_p;\n  const v3 = mod(v * v * v, P); // v³\n  const v7 = mod(v3 * v3 * v, P); // v⁷\n  // (p+3)/8 and (p-5)/8\n  const pow = ed25519_pow_2_252_3(u * v7).pow_p_5_8;\n  let x = mod(u * v3 * pow, P); // (uv³)(uv⁷)^(p-5)/8\n  const vx2 = mod(v * x * x, P); // vx²\n  const root1 = x; // First root candidate\n  const root2 = mod(x * ED25519_SQRT_M1, P); // Second root candidate\n  const useRoot1 = vx2 === u; // If vx² = u (mod p), x is a square root\n  const useRoot2 = vx2 === mod(-u, P); // If vx² = -u, set x <-- x * 2^((p-1)/4)\n  const noRoot = vx2 === mod(-u * ED25519_SQRT_M1, P); // There is no valid root, vx² = -u√(-1)\n  if (useRoot1) x = root1;\n  if (useRoot2 || noRoot) x = root2; // We return root2 anyway, for const-time\n  if (isNegativeLE(x, P)) x = mod(-x, P);\n  return {\n    isValid: useRoot1 || useRoot2,\n    value: x\n  };\n}\nconst Fp = /* @__PURE__ */(() => Field(ed25519_CURVE.p, {\n  isLE: true\n}))();\nconst Fn = /* @__PURE__ */(() => Field(ed25519_CURVE.n, {\n  isLE: true\n}))();\nconst ed25519Defaults = /* @__PURE__ */(() => ({\n  ...ed25519_CURVE,\n  Fp,\n  hash: sha512,\n  adjustScalarBytes,\n  // dom2\n  // Ratio of u to v. Allows us to combine inversion and square root. Uses algo from RFC8032 5.1.3.\n  // Constant-time, u/√v\n  uvRatio\n}))();\n/**\n * ed25519 curve with EdDSA signatures.\n * @example\n * import { ed25519 } from '@noble/curves/ed25519';\n * const { secretKey, publicKey } = ed25519.keygen();\n * const msg = new TextEncoder().encode('hello');\n * const sig = ed25519.sign(msg, priv);\n * ed25519.verify(sig, msg, pub); // Default mode: follows ZIP215\n * ed25519.verify(sig, msg, pub, { zip215: false }); // RFC8032 / FIPS 186-5\n */\nexport const ed25519 = /* @__PURE__ */(() => twistedEdwards(ed25519Defaults))();\nfunction ed25519_domain(data, ctx, phflag) {\n  if (ctx.length > 255) throw new Error('Context is too big');\n  return concatBytes(utf8ToBytes('SigEd25519 no Ed25519 collisions'), new Uint8Array([phflag ? 1 : 0, ctx.length]), ctx, data);\n}\n/** Context of ed25519. Uses context for domain separation. */\nexport const ed25519ctx = /* @__PURE__ */(() => twistedEdwards({\n  ...ed25519Defaults,\n  domain: ed25519_domain\n}))();\n/** Prehashed version of ed25519. Accepts already-hashed messages in sign() and verify(). */\nexport const ed25519ph = /* @__PURE__ */(() => twistedEdwards(Object.assign({}, ed25519Defaults, {\n  domain: ed25519_domain,\n  prehash: sha512\n})))();\n/**\n * ECDH using curve25519 aka x25519.\n * @example\n * import { x25519 } from '@noble/curves/ed25519';\n * const priv = 'a546e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449ac4';\n * const pub = 'e6db6867583030db3594c1a424b15f7c726624ec26b3353b10a903a6d0ab1c4c';\n * x25519.getSharedSecret(priv, pub) === x25519.scalarMult(priv, pub); // aliases\n * x25519.getPublicKey(priv) === x25519.scalarMultBase(priv);\n * x25519.getPublicKey(x25519.utils.randomSecretKey());\n */\nexport const x25519 = /* @__PURE__ */(() => {\n  const P = Fp.ORDER;\n  return montgomery({\n    P,\n    type: 'x25519',\n    powPminus2: x => {\n      // x^(p-2) aka x^(2^255-21)\n      const {\n        pow_p_5_8,\n        b2\n      } = ed25519_pow_2_252_3(x);\n      return mod(pow2(pow_p_5_8, _3n, P) * b2, P);\n    },\n    adjustScalarBytes\n  });\n})();\n// Hash To Curve Elligator2 Map (NOTE: different from ristretto255 elligator)\n// NOTE: very important part is usage of FpSqrtEven for ELL2_C1_EDWARDS, since\n// SageMath returns different root first and everything falls apart\nconst ELL2_C1 = /* @__PURE__ */(() => (ed25519_CURVE_p + _3n) / _8n)(); // 1. c1 = (q + 3) / 8       # Integer arithmetic\nconst ELL2_C2 = /* @__PURE__ */(() => Fp.pow(_2n, ELL2_C1))(); // 2. c2 = 2^c1\nconst ELL2_C3 = /* @__PURE__ */(() => Fp.sqrt(Fp.neg(Fp.ONE)))(); // 3. c3 = sqrt(-1)\n// prettier-ignore\nfunction map_to_curve_elligator2_curve25519(u) {\n  const ELL2_C4 = (ed25519_CURVE_p - _5n) / _8n; // 4. c4 = (q - 5) / 8       # Integer arithmetic\n  const ELL2_J = BigInt(486662);\n  let tv1 = Fp.sqr(u); //  1.  tv1 = u^2\n  tv1 = Fp.mul(tv1, _2n); //  2.  tv1 = 2 * tv1\n  let xd = Fp.add(tv1, Fp.ONE); //  3.   xd = tv1 + 1         # Nonzero: -1 is square (mod p), tv1 is not\n  let x1n = Fp.neg(ELL2_J); //  4.  x1n = -J              # x1 = x1n / xd = -J / (1 + 2 * u^2)\n  let tv2 = Fp.sqr(xd); //  5.  tv2 = xd^2\n  let gxd = Fp.mul(tv2, xd); //  6.  gxd = tv2 * xd        # gxd = xd^3\n  let gx1 = Fp.mul(tv1, ELL2_J); //  7.  gx1 = J * tv1         # x1n + J * xd\n  gx1 = Fp.mul(gx1, x1n); //  8.  gx1 = gx1 * x1n       # x1n^2 + J * x1n * xd\n  gx1 = Fp.add(gx1, tv2); //  9.  gx1 = gx1 + tv2       # x1n^2 + J * x1n * xd + xd^2\n  gx1 = Fp.mul(gx1, x1n); //  10. gx1 = gx1 * x1n       # x1n^3 + J * x1n^2 * xd + x1n * xd^2\n  let tv3 = Fp.sqr(gxd); //  11. tv3 = gxd^2\n  tv2 = Fp.sqr(tv3); //  12. tv2 = tv3^2           # gxd^4\n  tv3 = Fp.mul(tv3, gxd); //  13. tv3 = tv3 * gxd       # gxd^3\n  tv3 = Fp.mul(tv3, gx1); //  14. tv3 = tv3 * gx1       # gx1 * gxd^3\n  tv2 = Fp.mul(tv2, tv3); //  15. tv2 = tv2 * tv3       # gx1 * gxd^7\n  let y11 = Fp.pow(tv2, ELL2_C4); //  16. y11 = tv2^c4        # (gx1 * gxd^7)^((p - 5) / 8)\n  y11 = Fp.mul(y11, tv3); //  17. y11 = y11 * tv3       # gx1*gxd^3*(gx1*gxd^7)^((p-5)/8)\n  let y12 = Fp.mul(y11, ELL2_C3); //  18. y12 = y11 * c3\n  tv2 = Fp.sqr(y11); //  19. tv2 = y11^2\n  tv2 = Fp.mul(tv2, gxd); //  20. tv2 = tv2 * gxd\n  let e1 = Fp.eql(tv2, gx1); //  21.  e1 = tv2 == gx1\n  let y1 = Fp.cmov(y12, y11, e1); //  22.  y1 = CMOV(y12, y11, e1)  # If g(x1) is square, this is its sqrt\n  let x2n = Fp.mul(x1n, tv1); //  23. x2n = x1n * tv1       # x2 = x2n / xd = 2 * u^2 * x1n / xd\n  let y21 = Fp.mul(y11, u); //  24. y21 = y11 * u\n  y21 = Fp.mul(y21, ELL2_C2); //  25. y21 = y21 * c2\n  let y22 = Fp.mul(y21, ELL2_C3); //  26. y22 = y21 * c3\n  let gx2 = Fp.mul(gx1, tv1); //  27. gx2 = gx1 * tv1       # g(x2) = gx2 / gxd = 2 * u^2 * g(x1)\n  tv2 = Fp.sqr(y21); //  28. tv2 = y21^2\n  tv2 = Fp.mul(tv2, gxd); //  29. tv2 = tv2 * gxd\n  let e2 = Fp.eql(tv2, gx2); //  30.  e2 = tv2 == gx2\n  let y2 = Fp.cmov(y22, y21, e2); //  31.  y2 = CMOV(y22, y21, e2)  # If g(x2) is square, this is its sqrt\n  tv2 = Fp.sqr(y1); //  32. tv2 = y1^2\n  tv2 = Fp.mul(tv2, gxd); //  33. tv2 = tv2 * gxd\n  let e3 = Fp.eql(tv2, gx1); //  34.  e3 = tv2 == gx1\n  let xn = Fp.cmov(x2n, x1n, e3); //  35.  xn = CMOV(x2n, x1n, e3)  # If e3, x = x1, else x = x2\n  let y = Fp.cmov(y2, y1, e3); //  36.   y = CMOV(y2, y1, e3)    # If e3, y = y1, else y = y2\n  let e4 = Fp.isOdd(y); //  37.  e4 = sgn0(y) == 1        # Fix sign of y\n  y = Fp.cmov(y, Fp.neg(y), e3 !== e4); //  38.   y = CMOV(y, -y, e3 XOR e4)\n  return {\n    xMn: xn,\n    xMd: xd,\n    yMn: y,\n    yMd: _1n\n  }; //  39. return (xn, xd, y, 1)\n}\nconst ELL2_C1_EDWARDS = /* @__PURE__ */(() => FpSqrtEven(Fp, Fp.neg(BigInt(486664))))(); // sgn0(c1) MUST equal 0\nfunction map_to_curve_elligator2_edwards25519(u) {\n  const {\n    xMn,\n    xMd,\n    yMn,\n    yMd\n  } = map_to_curve_elligator2_curve25519(u); //  1.  (xMn, xMd, yMn, yMd) =\n  // map_to_curve_elligator2_curve25519(u)\n  let xn = Fp.mul(xMn, yMd); //  2.  xn = xMn * yMd\n  xn = Fp.mul(xn, ELL2_C1_EDWARDS); //  3.  xn = xn * c1\n  let xd = Fp.mul(xMd, yMn); //  4.  xd = xMd * yMn    # xn / xd = c1 * xM / yM\n  let yn = Fp.sub(xMn, xMd); //  5.  yn = xMn - xMd\n  let yd = Fp.add(xMn, xMd); //  6.  yd = xMn + xMd    # (n / d - 1) / (n / d + 1) = (n - d) / (n + d)\n  let tv1 = Fp.mul(xd, yd); //  7. tv1 = xd * yd\n  let e = Fp.eql(tv1, Fp.ZERO); //  8.   e = tv1 == 0\n  xn = Fp.cmov(xn, Fp.ZERO, e); //  9.  xn = CMOV(xn, 0, e)\n  xd = Fp.cmov(xd, Fp.ONE, e); //  10. xd = CMOV(xd, 1, e)\n  yn = Fp.cmov(yn, Fp.ONE, e); //  11. yn = CMOV(yn, 1, e)\n  yd = Fp.cmov(yd, Fp.ONE, e); //  12. yd = CMOV(yd, 1, e)\n  const [xd_inv, yd_inv] = FpInvertBatch(Fp, [xd, yd], true); // batch division\n  return {\n    x: Fp.mul(xn, xd_inv),\n    y: Fp.mul(yn, yd_inv)\n  }; //  13. return (xn, xd, yn, yd)\n}\n/** Hashing to ed25519 points / field. RFC 9380 methods. */\nexport const ed25519_hasher = /* @__PURE__ */(() => createHasher(ed25519.Point, scalars => map_to_curve_elligator2_edwards25519(scalars[0]), {\n  DST: 'edwards25519_XMD:SHA-512_ELL2_RO_',\n  encodeDST: 'edwards25519_XMD:SHA-512_ELL2_NU_',\n  p: ed25519_CURVE_p,\n  m: 1,\n  k: 128,\n  expand: 'xmd',\n  hash: sha512\n}))();\n// √(-1) aka √(a) aka 2^((p-1)/4)\nconst SQRT_M1 = ED25519_SQRT_M1;\n// √(ad - 1)\nconst SQRT_AD_MINUS_ONE = /* @__PURE__ */BigInt('25063068953384623474111414158702152701244531502492656460079210482610430750235');\n// 1 / √(a-d)\nconst INVSQRT_A_MINUS_D = /* @__PURE__ */BigInt('54469307008909316920995813868745141605393597292927456921205312896311721017578');\n// 1-d²\nconst ONE_MINUS_D_SQ = /* @__PURE__ */BigInt('1159843021668779879193775521855586647937357759715417654439879720876111806838');\n// (d-1)²\nconst D_MINUS_ONE_SQ = /* @__PURE__ */BigInt('40440834346308536858101042469323190826248399146238708352240133220865137265952');\n// Calculates 1/√(number)\nconst invertSqrt = number => uvRatio(_1n, number);\nconst MAX_255B = /* @__PURE__ */BigInt('0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff');\nconst bytes255ToNumberLE = bytes => ed25519.Point.Fp.create(bytesToNumberLE(bytes) & MAX_255B);\n/**\n * Computes Elligator map for Ristretto255.\n * Described in [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#appendix-B) and on\n * the [website](https://ristretto.group/formulas/elligator.html).\n */\nfunction calcElligatorRistrettoMap(r0) {\n  const {\n    d\n  } = ed25519_CURVE;\n  const P = ed25519_CURVE_p;\n  const mod = n => Fp.create(n);\n  const r = mod(SQRT_M1 * r0 * r0); // 1\n  const Ns = mod((r + _1n) * ONE_MINUS_D_SQ); // 2\n  let c = BigInt(-1); // 3\n  const D = mod((c - d * r) * mod(r + d)); // 4\n  let {\n    isValid: Ns_D_is_sq,\n    value: s\n  } = uvRatio(Ns, D); // 5\n  let s_ = mod(s * r0); // 6\n  if (!isNegativeLE(s_, P)) s_ = mod(-s_);\n  if (!Ns_D_is_sq) s = s_; // 7\n  if (!Ns_D_is_sq) c = r; // 8\n  const Nt = mod(c * (r - _1n) * D_MINUS_ONE_SQ - D); // 9\n  const s2 = s * s;\n  const W0 = mod((s + s) * D); // 10\n  const W1 = mod(Nt * SQRT_AD_MINUS_ONE); // 11\n  const W2 = mod(_1n - s2); // 12\n  const W3 = mod(_1n + s2); // 13\n  return new ed25519.Point(mod(W0 * W3), mod(W2 * W1), mod(W1 * W3), mod(W0 * W2));\n}\nfunction ristretto255_map(bytes) {\n  abytes(bytes, 64);\n  const r1 = bytes255ToNumberLE(bytes.subarray(0, 32));\n  const R1 = calcElligatorRistrettoMap(r1);\n  const r2 = bytes255ToNumberLE(bytes.subarray(32, 64));\n  const R2 = calcElligatorRistrettoMap(r2);\n  return new _RistrettoPoint(R1.add(R2));\n}\n/**\n * Wrapper over Edwards Point for ristretto255.\n *\n * Each ed25519/ExtendedPoint has 8 different equivalent points. This can be\n * a source of bugs for protocols like ring signatures. Ristretto was created to solve this.\n * Ristretto point operates in X:Y:Z:T extended coordinates like ExtendedPoint,\n * but it should work in its own namespace: do not combine those two.\n * See [RFC9496](https://www.rfc-editor.org/rfc/rfc9496).\n */\nclass _RistrettoPoint extends PrimeEdwardsPoint {\n  constructor(ep) {\n    super(ep);\n  }\n  static fromAffine(ap) {\n    return new _RistrettoPoint(ed25519.Point.fromAffine(ap));\n  }\n  assertSame(other) {\n    if (!(other instanceof _RistrettoPoint)) throw new Error('RistrettoPoint expected');\n  }\n  init(ep) {\n    return new _RistrettoPoint(ep);\n  }\n  /** @deprecated use `import { ristretto255_hasher } from '@noble/curves/ed25519.js';` */\n  static hashToCurve(hex) {\n    return ristretto255_map(ensureBytes('ristrettoHash', hex, 64));\n  }\n  static fromBytes(bytes) {\n    abytes(bytes, 32);\n    const {\n      a,\n      d\n    } = ed25519_CURVE;\n    const P = ed25519_CURVE_p;\n    const mod = n => Fp.create(n);\n    const s = bytes255ToNumberLE(bytes);\n    // 1. Check that s_bytes is the canonical encoding of a field element, or else abort.\n    // 3. Check that s is non-negative, or else abort\n    if (!equalBytes(Fp.toBytes(s), bytes) || isNegativeLE(s, P)) throw new Error('invalid ristretto255 encoding 1');\n    const s2 = mod(s * s);\n    const u1 = mod(_1n + a * s2); // 4 (a is -1)\n    const u2 = mod(_1n - a * s2); // 5\n    const u1_2 = mod(u1 * u1);\n    const u2_2 = mod(u2 * u2);\n    const v = mod(a * d * u1_2 - u2_2); // 6\n    const {\n      isValid,\n      value: I\n    } = invertSqrt(mod(v * u2_2)); // 7\n    const Dx = mod(I * u2); // 8\n    const Dy = mod(I * Dx * v); // 9\n    let x = mod((s + s) * Dx); // 10\n    if (isNegativeLE(x, P)) x = mod(-x); // 10\n    const y = mod(u1 * Dy); // 11\n    const t = mod(x * y); // 12\n    if (!isValid || isNegativeLE(t, P) || y === _0n) throw new Error('invalid ristretto255 encoding 2');\n    return new _RistrettoPoint(new ed25519.Point(x, y, _1n, t));\n  }\n  /**\n   * Converts ristretto-encoded string to ristretto point.\n   * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-decode).\n   * @param hex Ristretto-encoded 32 bytes. Not every 32-byte string is valid ristretto encoding\n   */\n  static fromHex(hex) {\n    return _RistrettoPoint.fromBytes(ensureBytes('ristrettoHex', hex, 32));\n  }\n  static msm(points, scalars) {\n    return pippenger(_RistrettoPoint, ed25519.Point.Fn, points, scalars);\n  }\n  /**\n   * Encodes ristretto point to Uint8Array.\n   * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-encode).\n   */\n  toBytes() {\n    let {\n      X,\n      Y,\n      Z,\n      T\n    } = this.ep;\n    const P = ed25519_CURVE_p;\n    const mod = n => Fp.create(n);\n    const u1 = mod(mod(Z + Y) * mod(Z - Y)); // 1\n    const u2 = mod(X * Y); // 2\n    // Square root always exists\n    const u2sq = mod(u2 * u2);\n    const {\n      value: invsqrt\n    } = invertSqrt(mod(u1 * u2sq)); // 3\n    const D1 = mod(invsqrt * u1); // 4\n    const D2 = mod(invsqrt * u2); // 5\n    const zInv = mod(D1 * D2 * T); // 6\n    let D; // 7\n    if (isNegativeLE(T * zInv, P)) {\n      let _x = mod(Y * SQRT_M1);\n      let _y = mod(X * SQRT_M1);\n      X = _x;\n      Y = _y;\n      D = mod(D1 * INVSQRT_A_MINUS_D);\n    } else {\n      D = D2; // 8\n    }\n    if (isNegativeLE(X * zInv, P)) Y = mod(-Y); // 9\n    let s = mod((Z - Y) * D); // 10 (check footer's note, no sqrt(-a))\n    if (isNegativeLE(s, P)) s = mod(-s);\n    return Fp.toBytes(s); // 11\n  }\n  /**\n   * Compares two Ristretto points.\n   * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-equals).\n   */\n  equals(other) {\n    this.assertSame(other);\n    const {\n      X: X1,\n      Y: Y1\n    } = this.ep;\n    const {\n      X: X2,\n      Y: Y2\n    } = other.ep;\n    const mod = n => Fp.create(n);\n    // (x1 * y2 == y1 * x2) | (y1 * y2 == x1 * x2)\n    const one = mod(X1 * Y2) === mod(Y1 * X2);\n    const two = mod(Y1 * Y2) === mod(X1 * X2);\n    return one || two;\n  }\n  is0() {\n    return this.equals(_RistrettoPoint.ZERO);\n  }\n}\n// Do NOT change syntax: the following gymnastics is done,\n// because typescript strips comments, which makes bundlers disable tree-shaking.\n// prettier-ignore\n_RistrettoPoint.BASE = /* @__PURE__ */(() => new _RistrettoPoint(ed25519.Point.BASE))();\n// prettier-ignore\n_RistrettoPoint.ZERO = /* @__PURE__ */(() => new _RistrettoPoint(ed25519.Point.ZERO))();\n// prettier-ignore\n_RistrettoPoint.Fp = /* @__PURE__ */(() => Fp)();\n// prettier-ignore\n_RistrettoPoint.Fn = /* @__PURE__ */(() => Fn)();\nexport const ristretto255 = {\n  Point: _RistrettoPoint\n};\n/** Hashing to ristretto255 points / field. RFC 9380 methods. */\nexport const ristretto255_hasher = {\n  hashToCurve(msg, options) {\n    const DST = options?.DST || 'ristretto255_XMD:SHA-512_R255MAP_RO_';\n    const xmd = expand_message_xmd(msg, DST, 64, sha512);\n    return ristretto255_map(xmd);\n  },\n  hashToScalar(msg, options = {\n    DST: _DST_scalar\n  }) {\n    const xmd = expand_message_xmd(msg, options.DST, 64, sha512);\n    return Fn.create(bytesToNumberLE(xmd));\n  }\n};\n// export const ristretto255_oprf: OPRF = createORPF({\n//   name: 'ristretto255-SHA512',\n//   Point: RistrettoPoint,\n//   hash: sha512,\n//   hashToGroup: ristretto255_hasher.hashToCurve,\n//   hashToScalar: ristretto255_hasher.hashToScalar,\n// });\n/**\n * Weird / bogus points, useful for debugging.\n * All 8 ed25519 points of 8-torsion subgroup can be generated from the point\n * T = `26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05`.\n * ⟨T⟩ = { O, T, 2T, 3T, 4T, 5T, 6T, 7T }\n */\nexport const ED25519_TORSION_SUBGROUP = ['0100000000000000000000000000000000000000000000000000000000000000', 'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a', '0000000000000000000000000000000000000000000000000000000000000080', '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05', 'ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f', '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85', '0000000000000000000000000000000000000000000000000000000000000000', 'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa'];\n/** @deprecated use `ed25519.utils.toMontgomery` */\nexport function edwardsToMontgomeryPub(edwardsPub) {\n  return ed25519.utils.toMontgomery(ensureBytes('pub', edwardsPub));\n}\n/** @deprecated use `ed25519.utils.toMontgomery` */\nexport const edwardsToMontgomery = edwardsToMontgomeryPub;\n/** @deprecated use `ed25519.utils.toMontgomerySecret` */\nexport function edwardsToMontgomeryPriv(edwardsPriv) {\n  return ed25519.utils.toMontgomerySecret(ensureBytes('pub', edwardsPriv));\n}\n/** @deprecated use `ristretto255.Point` */\nexport const RistrettoPoint = _RistrettoPoint;\n/** @deprecated use `import { ed25519_hasher } from '@noble/curves/ed25519.js';` */\nexport const hashToCurve = /* @__PURE__ */(() => ed25519_hasher.hashToCurve)();\n/** @deprecated use `import { ed25519_hasher } from '@noble/curves/ed25519.js';` */\nexport const encodeToCurve = /* @__PURE__ */(() => ed25519_hasher.encodeToCurve)();\n/** @deprecated use `import { ristretto255_hasher } from '@noble/curves/ed25519.js';` */\nexport const hashToRistretto255 = /* @__PURE__ */(() => ristretto255_hasher.hashToCurve)();\n/** @deprecated use `import { ristretto255_hasher } from '@noble/curves/ed25519.js';` */\nexport const hash_to_ristretto255 = /* @__PURE__ */(() => ristretto255_hasher.hashToCurve)();", "map": {"version": 3, "names": ["sha512", "abytes", "concatBytes", "utf8ToBytes", "pippenger", "PrimeEdwardsPoint", "twistedEdwards", "_DST_scalar", "createHasher", "expand_message_xmd", "Field", "FpInvertBatch", "FpSqrtEven", "isNegativeLE", "mod", "pow2", "montgomery", "bytesToNumberLE", "ensureBytes", "equalBytes", "_0n", "BigInt", "_1n", "_2n", "_3n", "_5n", "_8n", "ed25519_CURVE_p", "ed25519_CURVE", "p", "n", "h", "a", "d", "Gx", "Gy", "ed25519_pow_2_252_3", "x", "_10n", "_20n", "_40n", "_80n", "P", "x2", "b2", "b4", "b5", "b10", "b20", "b40", "b80", "b160", "b240", "b250", "pow_p_5_8", "adjustScalarBytes", "bytes", "ED25519_SQRT_M1", "uvRatio", "u", "v", "v3", "v7", "pow", "vx2", "root1", "root2", "useRoot1", "useRoot2", "noRoot", "<PERSON><PERSON><PERSON><PERSON>", "value", "Fp", "isLE", "Fn", "ed25519<PERSON><PERSON><PERSON><PERSON>", "hash", "ed25519", "ed25519_domain", "data", "ctx", "phflag", "length", "Error", "Uint8Array", "ed25519ctx", "domain", "ed25519ph", "Object", "assign", "prehash", "x25519", "ORDER", "type", "powPminus2", "ELL2_C1", "ELL2_C2", "ELL2_C3", "sqrt", "neg", "ONE", "map_to_curve_elligator2_curve25519", "ELL2_C4", "ELL2_J", "tv1", "sqr", "mul", "xd", "add", "x1n", "tv2", "gxd", "gx1", "tv3", "y11", "y12", "e1", "eql", "y1", "cmov", "x2n", "y21", "y22", "gx2", "e2", "y2", "e3", "xn", "y", "e4", "isOdd", "xMn", "xMd", "yMn", "yMd", "ELL2_C1_EDWARDS", "map_to_curve_elligator2_edwards25519", "yn", "sub", "yd", "e", "ZERO", "xd_inv", "yd_inv", "ed25519_hasher", "Point", "scalars", "DST", "encodeDST", "m", "k", "expand", "SQRT_M1", "SQRT_AD_MINUS_ONE", "INVSQRT_A_MINUS_D", "ONE_MINUS_D_SQ", "D_MINUS_ONE_SQ", "invertSqrt", "number", "MAX_255B", "bytes255ToNumberLE", "create", "calcElligatorRistrettoMap", "r0", "r", "Ns", "c", "D", "Ns_D_is_sq", "s", "s_", "Nt", "s2", "W0", "W1", "W2", "W3", "ristretto255_map", "r1", "subarray", "R1", "r2", "R2", "_RistrettoPoint", "constructor", "ep", "fromAffine", "ap", "assertSame", "other", "init", "hashToCurve", "hex", "fromBytes", "toBytes", "u1", "u2", "u1_2", "u2_2", "I", "Dx", "<PERSON><PERSON>", "t", "fromHex", "msm", "points", "X", "Y", "Z", "T", "u2sq", "invsqrt", "D1", "D2", "zInv", "_x", "_y", "equals", "X1", "Y1", "X2", "Y2", "one", "two", "is0", "BASE", "ristretto255", "ristretto255_hasher", "msg", "options", "xmd", "hashToScalar", "ED25519_TORSION_SUBGROUP", "edwardsToMontgomeryPub", "edwards<PERSON>ub", "utils", "toMontgomery", "edwardsToMontgomery", "edwardsToMontgomeryPriv", "edwardsPriv", "toMontgomerySecret", "RistrettoPoint", "encodeToCurve", "hashToRistretto255", "hash_to_ristretto255"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\ed25519.ts"], "sourcesContent": ["/**\n * ed25519 Twisted <PERSON> curve with following addons:\n * - X25519 ECDH\n * - Ristretto cofactor elimination\n * - Elligator hash-to-group / point indistinguishability\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha512 } from '@noble/hashes/sha2.js';\nimport { abytes, concatBytes, utf8ToBytes } from '@noble/hashes/utils.js';\nimport { pippenger, type AffinePoint } from './abstract/curve.ts';\nimport {\n  PrimeEdwardsPoint,\n  twistedEdwards,\n  type CurveFn,\n  type EdwardsOpts,\n  type EdwardsPoint,\n} from './abstract/edwards.ts';\nimport {\n  _DST_scalar,\n  createHasher,\n  expand_message_xmd,\n  type H2CHasher,\n  type H2CHasherBase,\n  type H2CMethod,\n  type htfBasicOpts,\n} from './abstract/hash-to-curve.ts';\nimport {\n  Field,\n  FpInvertBatch,\n  FpSqrtEven,\n  isNegativeLE,\n  mod,\n  pow2,\n  type IField,\n} from './abstract/modular.ts';\nimport { montgomery, type MontgomeryECDH as XCurveFn } from './abstract/montgomery.ts';\nimport { bytesToNumberLE, ensureBytes, equalBytes, type Hex } from './utils.ts';\n\n// prettier-ignore\nconst _0n = /* @__PURE__ */ BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _5n = BigInt(5), _8n = BigInt(8);\n\n// P = 2n**255n-19n\nconst ed25519_CURVE_p = BigInt(\n  '0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed'\n);\n\n// N = 2n**252n + 27742317777372353535851937790883648493n\n// a = Fp.create(BigInt(-1))\n// d = -121665/121666 a.k.a. Fp.neg(121665 * Fp.inv(121666))\nconst ed25519_CURVE: EdwardsOpts = /* @__PURE__ */ (() => ({\n  p: ed25519_CURVE_p,\n  n: BigInt('0x1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed'),\n  h: _8n,\n  a: BigInt('0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec'),\n  d: BigInt('0x52036cee2b6ffe738cc740797779e89800700a4d4141d8ab75eb4dca135978a3'),\n  Gx: BigInt('0x216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a'),\n  Gy: BigInt('0x6666666666666666666666666666666666666666666666666666666666666658'),\n}))();\n\nfunction ed25519_pow_2_252_3(x: bigint) {\n  // prettier-ignore\n  const _10n = BigInt(10), _20n = BigInt(20), _40n = BigInt(40), _80n = BigInt(80);\n  const P = ed25519_CURVE_p;\n  const x2 = (x * x) % P;\n  const b2 = (x2 * x) % P; // x^3, 11\n  const b4 = (pow2(b2, _2n, P) * b2) % P; // x^15, 1111\n  const b5 = (pow2(b4, _1n, P) * x) % P; // x^31\n  const b10 = (pow2(b5, _5n, P) * b5) % P;\n  const b20 = (pow2(b10, _10n, P) * b10) % P;\n  const b40 = (pow2(b20, _20n, P) * b20) % P;\n  const b80 = (pow2(b40, _40n, P) * b40) % P;\n  const b160 = (pow2(b80, _80n, P) * b80) % P;\n  const b240 = (pow2(b160, _80n, P) * b80) % P;\n  const b250 = (pow2(b240, _10n, P) * b10) % P;\n  const pow_p_5_8 = (pow2(b250, _2n, P) * x) % P;\n  // ^ To pow to (p+3)/8, multiply it by x.\n  return { pow_p_5_8, b2 };\n}\n\nfunction adjustScalarBytes(bytes: Uint8Array): Uint8Array {\n  // Section 5: For X25519, in order to decode 32 random bytes as an integer scalar,\n  // set the three least significant bits of the first byte\n  bytes[0] &= 248; // 0b1111_1000\n  // and the most significant bit of the last to zero,\n  bytes[31] &= 127; // 0b0111_1111\n  // set the second most significant bit of the last byte to 1\n  bytes[31] |= 64; // 0b0100_0000\n  return bytes;\n}\n\n// √(-1) aka √(a) aka 2^((p-1)/4)\n// Fp.sqrt(Fp.neg(1))\nconst ED25519_SQRT_M1 = /* @__PURE__ */ BigInt(\n  '19681161376707505956807079304988542015446066515923890162744021073123829784752'\n);\n// sqrt(u/v)\nfunction uvRatio(u: bigint, v: bigint): { isValid: boolean; value: bigint } {\n  const P = ed25519_CURVE_p;\n  const v3 = mod(v * v * v, P); // v³\n  const v7 = mod(v3 * v3 * v, P); // v⁷\n  // (p+3)/8 and (p-5)/8\n  const pow = ed25519_pow_2_252_3(u * v7).pow_p_5_8;\n  let x = mod(u * v3 * pow, P); // (uv³)(uv⁷)^(p-5)/8\n  const vx2 = mod(v * x * x, P); // vx²\n  const root1 = x; // First root candidate\n  const root2 = mod(x * ED25519_SQRT_M1, P); // Second root candidate\n  const useRoot1 = vx2 === u; // If vx² = u (mod p), x is a square root\n  const useRoot2 = vx2 === mod(-u, P); // If vx² = -u, set x <-- x * 2^((p-1)/4)\n  const noRoot = vx2 === mod(-u * ED25519_SQRT_M1, P); // There is no valid root, vx² = -u√(-1)\n  if (useRoot1) x = root1;\n  if (useRoot2 || noRoot) x = root2; // We return root2 anyway, for const-time\n  if (isNegativeLE(x, P)) x = mod(-x, P);\n  return { isValid: useRoot1 || useRoot2, value: x };\n}\n\nconst Fp = /* @__PURE__ */ (() => Field(ed25519_CURVE.p, { isLE: true }))();\nconst Fn = /* @__PURE__ */ (() => Field(ed25519_CURVE.n, { isLE: true }))();\n\nconst ed25519Defaults = /* @__PURE__ */ (() => ({\n  ...ed25519_CURVE,\n  Fp,\n  hash: sha512,\n  adjustScalarBytes,\n  // dom2\n  // Ratio of u to v. Allows us to combine inversion and square root. Uses algo from RFC8032 5.1.3.\n  // Constant-time, u/√v\n  uvRatio,\n}))();\n\n/**\n * ed25519 curve with EdDSA signatures.\n * @example\n * import { ed25519 } from '@noble/curves/ed25519';\n * const { secretKey, publicKey } = ed25519.keygen();\n * const msg = new TextEncoder().encode('hello');\n * const sig = ed25519.sign(msg, priv);\n * ed25519.verify(sig, msg, pub); // Default mode: follows ZIP215\n * ed25519.verify(sig, msg, pub, { zip215: false }); // RFC8032 / FIPS 186-5\n */\nexport const ed25519: CurveFn = /* @__PURE__ */ (() => twistedEdwards(ed25519Defaults))();\n\nfunction ed25519_domain(data: Uint8Array, ctx: Uint8Array, phflag: boolean) {\n  if (ctx.length > 255) throw new Error('Context is too big');\n  return concatBytes(\n    utf8ToBytes('SigEd25519 no Ed25519 collisions'),\n    new Uint8Array([phflag ? 1 : 0, ctx.length]),\n    ctx,\n    data\n  );\n}\n\n/** Context of ed25519. Uses context for domain separation. */\nexport const ed25519ctx: CurveFn = /* @__PURE__ */ (() =>\n  twistedEdwards({\n    ...ed25519Defaults,\n    domain: ed25519_domain,\n  }))();\n\n/** Prehashed version of ed25519. Accepts already-hashed messages in sign() and verify(). */\nexport const ed25519ph: CurveFn = /* @__PURE__ */ (() =>\n  twistedEdwards(\n    Object.assign({}, ed25519Defaults, {\n      domain: ed25519_domain,\n      prehash: sha512,\n    })\n  ))();\n\n/**\n * ECDH using curve25519 aka x25519.\n * @example\n * import { x25519 } from '@noble/curves/ed25519';\n * const priv = 'a546e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449ac4';\n * const pub = 'e6db6867583030db3594c1a424b15f7c726624ec26b3353b10a903a6d0ab1c4c';\n * x25519.getSharedSecret(priv, pub) === x25519.scalarMult(priv, pub); // aliases\n * x25519.getPublicKey(priv) === x25519.scalarMultBase(priv);\n * x25519.getPublicKey(x25519.utils.randomSecretKey());\n */\nexport const x25519: XCurveFn = /* @__PURE__ */ (() => {\n  const P = Fp.ORDER;\n  return montgomery({\n    P,\n    type: 'x25519',\n    powPminus2: (x: bigint): bigint => {\n      // x^(p-2) aka x^(2^255-21)\n      const { pow_p_5_8, b2 } = ed25519_pow_2_252_3(x);\n      return mod(pow2(pow_p_5_8, _3n, P) * b2, P);\n    },\n    adjustScalarBytes,\n  });\n})();\n\n// Hash To Curve Elligator2 Map (NOTE: different from ristretto255 elligator)\n// NOTE: very important part is usage of FpSqrtEven for ELL2_C1_EDWARDS, since\n// SageMath returns different root first and everything falls apart\nconst ELL2_C1 = /* @__PURE__ */ (() => (ed25519_CURVE_p + _3n) / _8n)(); // 1. c1 = (q + 3) / 8       # Integer arithmetic\nconst ELL2_C2 = /* @__PURE__ */ (() => Fp.pow(_2n, ELL2_C1))(); // 2. c2 = 2^c1\nconst ELL2_C3 = /* @__PURE__ */ (() => Fp.sqrt(Fp.neg(Fp.ONE)))(); // 3. c3 = sqrt(-1)\n\n// prettier-ignore\nfunction map_to_curve_elligator2_curve25519(u: bigint) {\n  const ELL2_C4 = (ed25519_CURVE_p - _5n) / _8n; // 4. c4 = (q - 5) / 8       # Integer arithmetic\n  const ELL2_J = BigInt(486662);\n\n  let tv1 = Fp.sqr(u);          //  1.  tv1 = u^2\n  tv1 = Fp.mul(tv1, _2n);       //  2.  tv1 = 2 * tv1\n  let xd = Fp.add(tv1, Fp.ONE); //  3.   xd = tv1 + 1         # Nonzero: -1 is square (mod p), tv1 is not\n  let x1n = Fp.neg(ELL2_J);     //  4.  x1n = -J              # x1 = x1n / xd = -J / (1 + 2 * u^2)\n  let tv2 = Fp.sqr(xd);         //  5.  tv2 = xd^2\n  let gxd = Fp.mul(tv2, xd);    //  6.  gxd = tv2 * xd        # gxd = xd^3\n  let gx1 = Fp.mul(tv1, ELL2_J);//  7.  gx1 = J * tv1         # x1n + J * xd\n  gx1 = Fp.mul(gx1, x1n);       //  8.  gx1 = gx1 * x1n       # x1n^2 + J * x1n * xd\n  gx1 = Fp.add(gx1, tv2);       //  9.  gx1 = gx1 + tv2       # x1n^2 + J * x1n * xd + xd^2\n  gx1 = Fp.mul(gx1, x1n);       //  10. gx1 = gx1 * x1n       # x1n^3 + J * x1n^2 * xd + x1n * xd^2\n  let tv3 = Fp.sqr(gxd);        //  11. tv3 = gxd^2\n  tv2 = Fp.sqr(tv3);            //  12. tv2 = tv3^2           # gxd^4\n  tv3 = Fp.mul(tv3, gxd);       //  13. tv3 = tv3 * gxd       # gxd^3\n  tv3 = Fp.mul(tv3, gx1);       //  14. tv3 = tv3 * gx1       # gx1 * gxd^3\n  tv2 = Fp.mul(tv2, tv3);       //  15. tv2 = tv2 * tv3       # gx1 * gxd^7\n  let y11 = Fp.pow(tv2, ELL2_C4); //  16. y11 = tv2^c4        # (gx1 * gxd^7)^((p - 5) / 8)\n  y11 = Fp.mul(y11, tv3);       //  17. y11 = y11 * tv3       # gx1*gxd^3*(gx1*gxd^7)^((p-5)/8)\n  let y12 = Fp.mul(y11, ELL2_C3); //  18. y12 = y11 * c3\n  tv2 = Fp.sqr(y11);            //  19. tv2 = y11^2\n  tv2 = Fp.mul(tv2, gxd);       //  20. tv2 = tv2 * gxd\n  let e1 = Fp.eql(tv2, gx1);    //  21.  e1 = tv2 == gx1\n  let y1 = Fp.cmov(y12, y11, e1); //  22.  y1 = CMOV(y12, y11, e1)  # If g(x1) is square, this is its sqrt\n  let x2n = Fp.mul(x1n, tv1);   //  23. x2n = x1n * tv1       # x2 = x2n / xd = 2 * u^2 * x1n / xd\n  let y21 = Fp.mul(y11, u);     //  24. y21 = y11 * u\n  y21 = Fp.mul(y21, ELL2_C2);   //  25. y21 = y21 * c2\n  let y22 = Fp.mul(y21, ELL2_C3); //  26. y22 = y21 * c3\n  let gx2 = Fp.mul(gx1, tv1);   //  27. gx2 = gx1 * tv1       # g(x2) = gx2 / gxd = 2 * u^2 * g(x1)\n  tv2 = Fp.sqr(y21);            //  28. tv2 = y21^2\n  tv2 = Fp.mul(tv2, gxd);       //  29. tv2 = tv2 * gxd\n  let e2 = Fp.eql(tv2, gx2);    //  30.  e2 = tv2 == gx2\n  let y2 = Fp.cmov(y22, y21, e2); //  31.  y2 = CMOV(y22, y21, e2)  # If g(x2) is square, this is its sqrt\n  tv2 = Fp.sqr(y1);             //  32. tv2 = y1^2\n  tv2 = Fp.mul(tv2, gxd);       //  33. tv2 = tv2 * gxd\n  let e3 = Fp.eql(tv2, gx1);    //  34.  e3 = tv2 == gx1\n  let xn = Fp.cmov(x2n, x1n, e3); //  35.  xn = CMOV(x2n, x1n, e3)  # If e3, x = x1, else x = x2\n  let y = Fp.cmov(y2, y1, e3);  //  36.   y = CMOV(y2, y1, e3)    # If e3, y = y1, else y = y2\n  let e4 = Fp.isOdd!(y);         //  37.  e4 = sgn0(y) == 1        # Fix sign of y\n  y = Fp.cmov(y, Fp.neg(y), e3 !== e4); //  38.   y = CMOV(y, -y, e3 XOR e4)\n  return { xMn: xn, xMd: xd, yMn: y, yMd: _1n }; //  39. return (xn, xd, y, 1)\n}\n\nconst ELL2_C1_EDWARDS = /* @__PURE__ */ (() => FpSqrtEven(Fp, Fp.neg(BigInt(486664))))(); // sgn0(c1) MUST equal 0\nfunction map_to_curve_elligator2_edwards25519(u: bigint) {\n  const { xMn, xMd, yMn, yMd } = map_to_curve_elligator2_curve25519(u); //  1.  (xMn, xMd, yMn, yMd) =\n  // map_to_curve_elligator2_curve25519(u)\n  let xn = Fp.mul(xMn, yMd); //  2.  xn = xMn * yMd\n  xn = Fp.mul(xn, ELL2_C1_EDWARDS); //  3.  xn = xn * c1\n  let xd = Fp.mul(xMd, yMn); //  4.  xd = xMd * yMn    # xn / xd = c1 * xM / yM\n  let yn = Fp.sub(xMn, xMd); //  5.  yn = xMn - xMd\n  let yd = Fp.add(xMn, xMd); //  6.  yd = xMn + xMd    # (n / d - 1) / (n / d + 1) = (n - d) / (n + d)\n  let tv1 = Fp.mul(xd, yd); //  7. tv1 = xd * yd\n  let e = Fp.eql(tv1, Fp.ZERO); //  8.   e = tv1 == 0\n  xn = Fp.cmov(xn, Fp.ZERO, e); //  9.  xn = CMOV(xn, 0, e)\n  xd = Fp.cmov(xd, Fp.ONE, e); //  10. xd = CMOV(xd, 1, e)\n  yn = Fp.cmov(yn, Fp.ONE, e); //  11. yn = CMOV(yn, 1, e)\n  yd = Fp.cmov(yd, Fp.ONE, e); //  12. yd = CMOV(yd, 1, e)\n  const [xd_inv, yd_inv] = FpInvertBatch(Fp, [xd, yd], true); // batch division\n  return { x: Fp.mul(xn, xd_inv), y: Fp.mul(yn, yd_inv) }; //  13. return (xn, xd, yn, yd)\n}\n\n/** Hashing to ed25519 points / field. RFC 9380 methods. */\nexport const ed25519_hasher: H2CHasher<bigint> = /* @__PURE__ */ (() =>\n  createHasher(\n    ed25519.Point,\n    (scalars: bigint[]) => map_to_curve_elligator2_edwards25519(scalars[0]),\n    {\n      DST: 'edwards25519_XMD:SHA-512_ELL2_RO_',\n      encodeDST: 'edwards25519_XMD:SHA-512_ELL2_NU_',\n      p: ed25519_CURVE_p,\n      m: 1,\n      k: 128,\n      expand: 'xmd',\n      hash: sha512,\n    }\n  ))();\n\n// √(-1) aka √(a) aka 2^((p-1)/4)\nconst SQRT_M1 = ED25519_SQRT_M1;\n// √(ad - 1)\nconst SQRT_AD_MINUS_ONE = /* @__PURE__ */ BigInt(\n  '25063068953384623474111414158702152701244531502492656460079210482610430750235'\n);\n// 1 / √(a-d)\nconst INVSQRT_A_MINUS_D = /* @__PURE__ */ BigInt(\n  '54469307008909316920995813868745141605393597292927456921205312896311721017578'\n);\n// 1-d²\nconst ONE_MINUS_D_SQ = /* @__PURE__ */ BigInt(\n  '1159843021668779879193775521855586647937357759715417654439879720876111806838'\n);\n// (d-1)²\nconst D_MINUS_ONE_SQ = /* @__PURE__ */ BigInt(\n  '40440834346308536858101042469323190826248399146238708352240133220865137265952'\n);\n// Calculates 1/√(number)\nconst invertSqrt = (number: bigint) => uvRatio(_1n, number);\n\nconst MAX_255B = /* @__PURE__ */ BigInt(\n  '0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'\n);\nconst bytes255ToNumberLE = (bytes: Uint8Array) =>\n  ed25519.Point.Fp.create(bytesToNumberLE(bytes) & MAX_255B);\n\ntype ExtendedPoint = EdwardsPoint;\n\n/**\n * Computes Elligator map for Ristretto255.\n * Described in [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#appendix-B) and on\n * the [website](https://ristretto.group/formulas/elligator.html).\n */\nfunction calcElligatorRistrettoMap(r0: bigint): ExtendedPoint {\n  const { d } = ed25519_CURVE;\n  const P = ed25519_CURVE_p;\n  const mod = (n: bigint) => Fp.create(n);\n  const r = mod(SQRT_M1 * r0 * r0); // 1\n  const Ns = mod((r + _1n) * ONE_MINUS_D_SQ); // 2\n  let c = BigInt(-1); // 3\n  const D = mod((c - d * r) * mod(r + d)); // 4\n  let { isValid: Ns_D_is_sq, value: s } = uvRatio(Ns, D); // 5\n  let s_ = mod(s * r0); // 6\n  if (!isNegativeLE(s_, P)) s_ = mod(-s_);\n  if (!Ns_D_is_sq) s = s_; // 7\n  if (!Ns_D_is_sq) c = r; // 8\n  const Nt = mod(c * (r - _1n) * D_MINUS_ONE_SQ - D); // 9\n  const s2 = s * s;\n  const W0 = mod((s + s) * D); // 10\n  const W1 = mod(Nt * SQRT_AD_MINUS_ONE); // 11\n  const W2 = mod(_1n - s2); // 12\n  const W3 = mod(_1n + s2); // 13\n  return new ed25519.Point(mod(W0 * W3), mod(W2 * W1), mod(W1 * W3), mod(W0 * W2));\n}\n\nfunction ristretto255_map(bytes: Uint8Array): _RistrettoPoint {\n  abytes(bytes, 64);\n  const r1 = bytes255ToNumberLE(bytes.subarray(0, 32));\n  const R1 = calcElligatorRistrettoMap(r1);\n  const r2 = bytes255ToNumberLE(bytes.subarray(32, 64));\n  const R2 = calcElligatorRistrettoMap(r2);\n  return new _RistrettoPoint(R1.add(R2));\n}\n\n/**\n * Wrapper over Edwards Point for ristretto255.\n *\n * Each ed25519/ExtendedPoint has 8 different equivalent points. This can be\n * a source of bugs for protocols like ring signatures. Ristretto was created to solve this.\n * Ristretto point operates in X:Y:Z:T extended coordinates like ExtendedPoint,\n * but it should work in its own namespace: do not combine those two.\n * See [RFC9496](https://www.rfc-editor.org/rfc/rfc9496).\n */\nclass _RistrettoPoint extends PrimeEdwardsPoint<_RistrettoPoint> {\n  // Do NOT change syntax: the following gymnastics is done,\n  // because typescript strips comments, which makes bundlers disable tree-shaking.\n  // prettier-ignore\n  static BASE: _RistrettoPoint =\n    /* @__PURE__ */ (() => new _RistrettoPoint(ed25519.Point.BASE))();\n  // prettier-ignore\n  static ZERO: _RistrettoPoint =\n    /* @__PURE__ */ (() => new _RistrettoPoint(ed25519.Point.ZERO))();\n  // prettier-ignore\n  static Fp: IField<bigint> =\n    /* @__PURE__ */ (() => Fp)();\n  // prettier-ignore\n  static Fn: IField<bigint> =\n    /* @__PURE__ */ (() => Fn)();\n\n  constructor(ep: ExtendedPoint) {\n    super(ep);\n  }\n\n  static fromAffine(ap: AffinePoint<bigint>): _RistrettoPoint {\n    return new _RistrettoPoint(ed25519.Point.fromAffine(ap));\n  }\n\n  protected assertSame(other: _RistrettoPoint): void {\n    if (!(other instanceof _RistrettoPoint)) throw new Error('RistrettoPoint expected');\n  }\n\n  protected init(ep: EdwardsPoint): _RistrettoPoint {\n    return new _RistrettoPoint(ep);\n  }\n\n  /** @deprecated use `import { ristretto255_hasher } from '@noble/curves/ed25519.js';` */\n  static hashToCurve(hex: Hex): _RistrettoPoint {\n    return ristretto255_map(ensureBytes('ristrettoHash', hex, 64));\n  }\n\n  static fromBytes(bytes: Uint8Array): _RistrettoPoint {\n    abytes(bytes, 32);\n    const { a, d } = ed25519_CURVE;\n    const P = ed25519_CURVE_p;\n    const mod = (n: bigint) => Fp.create(n);\n    const s = bytes255ToNumberLE(bytes);\n    // 1. Check that s_bytes is the canonical encoding of a field element, or else abort.\n    // 3. Check that s is non-negative, or else abort\n    if (!equalBytes(Fp.toBytes(s), bytes) || isNegativeLE(s, P))\n      throw new Error('invalid ristretto255 encoding 1');\n    const s2 = mod(s * s);\n    const u1 = mod(_1n + a * s2); // 4 (a is -1)\n    const u2 = mod(_1n - a * s2); // 5\n    const u1_2 = mod(u1 * u1);\n    const u2_2 = mod(u2 * u2);\n    const v = mod(a * d * u1_2 - u2_2); // 6\n    const { isValid, value: I } = invertSqrt(mod(v * u2_2)); // 7\n    const Dx = mod(I * u2); // 8\n    const Dy = mod(I * Dx * v); // 9\n    let x = mod((s + s) * Dx); // 10\n    if (isNegativeLE(x, P)) x = mod(-x); // 10\n    const y = mod(u1 * Dy); // 11\n    const t = mod(x * y); // 12\n    if (!isValid || isNegativeLE(t, P) || y === _0n)\n      throw new Error('invalid ristretto255 encoding 2');\n    return new _RistrettoPoint(new ed25519.Point(x, y, _1n, t));\n  }\n\n  /**\n   * Converts ristretto-encoded string to ristretto point.\n   * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-decode).\n   * @param hex Ristretto-encoded 32 bytes. Not every 32-byte string is valid ristretto encoding\n   */\n  static fromHex(hex: Hex): _RistrettoPoint {\n    return _RistrettoPoint.fromBytes(ensureBytes('ristrettoHex', hex, 32));\n  }\n\n  static msm(points: _RistrettoPoint[], scalars: bigint[]): _RistrettoPoint {\n    return pippenger(_RistrettoPoint, ed25519.Point.Fn, points, scalars);\n  }\n\n  /**\n   * Encodes ristretto point to Uint8Array.\n   * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-encode).\n   */\n  toBytes(): Uint8Array {\n    let { X, Y, Z, T } = this.ep;\n    const P = ed25519_CURVE_p;\n    const mod = (n: bigint) => Fp.create(n);\n    const u1 = mod(mod(Z + Y) * mod(Z - Y)); // 1\n    const u2 = mod(X * Y); // 2\n    // Square root always exists\n    const u2sq = mod(u2 * u2);\n    const { value: invsqrt } = invertSqrt(mod(u1 * u2sq)); // 3\n    const D1 = mod(invsqrt * u1); // 4\n    const D2 = mod(invsqrt * u2); // 5\n    const zInv = mod(D1 * D2 * T); // 6\n    let D: bigint; // 7\n    if (isNegativeLE(T * zInv, P)) {\n      let _x = mod(Y * SQRT_M1);\n      let _y = mod(X * SQRT_M1);\n      X = _x;\n      Y = _y;\n      D = mod(D1 * INVSQRT_A_MINUS_D);\n    } else {\n      D = D2; // 8\n    }\n    if (isNegativeLE(X * zInv, P)) Y = mod(-Y); // 9\n    let s = mod((Z - Y) * D); // 10 (check footer's note, no sqrt(-a))\n    if (isNegativeLE(s, P)) s = mod(-s);\n    return Fp.toBytes(s); // 11\n  }\n\n  /**\n   * Compares two Ristretto points.\n   * Described in [RFC9496](https://www.rfc-editor.org/rfc/rfc9496#name-equals).\n   */\n  equals(other: _RistrettoPoint): boolean {\n    this.assertSame(other);\n    const { X: X1, Y: Y1 } = this.ep;\n    const { X: X2, Y: Y2 } = other.ep;\n    const mod = (n: bigint) => Fp.create(n);\n    // (x1 * y2 == y1 * x2) | (y1 * y2 == x1 * x2)\n    const one = mod(X1 * Y2) === mod(Y1 * X2);\n    const two = mod(Y1 * Y2) === mod(X1 * X2);\n    return one || two;\n  }\n\n  is0(): boolean {\n    return this.equals(_RistrettoPoint.ZERO);\n  }\n}\n\nexport const ristretto255: {\n  Point: typeof _RistrettoPoint;\n} = { Point: _RistrettoPoint };\n\n/** Hashing to ristretto255 points / field. RFC 9380 methods. */\nexport const ristretto255_hasher: H2CHasherBase<bigint> = {\n  hashToCurve(msg: Uint8Array, options?: htfBasicOpts): _RistrettoPoint {\n    const DST = options?.DST || 'ristretto255_XMD:SHA-512_R255MAP_RO_';\n    const xmd = expand_message_xmd(msg, DST, 64, sha512);\n    return ristretto255_map(xmd);\n  },\n  hashToScalar(msg: Uint8Array, options: htfBasicOpts = { DST: _DST_scalar }) {\n    const xmd = expand_message_xmd(msg, options.DST, 64, sha512);\n    return Fn.create(bytesToNumberLE(xmd));\n  },\n};\n\n// export const ristretto255_oprf: OPRF = createORPF({\n//   name: 'ristretto255-SHA512',\n//   Point: RistrettoPoint,\n//   hash: sha512,\n//   hashToGroup: ristretto255_hasher.hashToCurve,\n//   hashToScalar: ristretto255_hasher.hashToScalar,\n// });\n\n/**\n * Weird / bogus points, useful for debugging.\n * All 8 ed25519 points of 8-torsion subgroup can be generated from the point\n * T = `26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05`.\n * ⟨T⟩ = { O, T, 2T, 3T, 4T, 5T, 6T, 7T }\n */\nexport const ED25519_TORSION_SUBGROUP: string[] = [\n  '0100000000000000000000000000000000000000000000000000000000000000',\n  'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a',\n  '0000000000000000000000000000000000000000000000000000000000000080',\n  '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05',\n  'ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f',\n  '26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85',\n  '0000000000000000000000000000000000000000000000000000000000000000',\n  'c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa',\n];\n\n/** @deprecated use `ed25519.utils.toMontgomery` */\nexport function edwardsToMontgomeryPub(edwardsPub: Hex): Uint8Array {\n  return ed25519.utils.toMontgomery(ensureBytes('pub', edwardsPub));\n}\n/** @deprecated use `ed25519.utils.toMontgomery` */\nexport const edwardsToMontgomery: typeof edwardsToMontgomeryPub = edwardsToMontgomeryPub;\n\n/** @deprecated use `ed25519.utils.toMontgomerySecret` */\nexport function edwardsToMontgomeryPriv(edwardsPriv: Uint8Array): Uint8Array {\n  return ed25519.utils.toMontgomerySecret(ensureBytes('pub', edwardsPriv));\n}\n\n/** @deprecated use `ristretto255.Point` */\nexport const RistrettoPoint: typeof _RistrettoPoint = _RistrettoPoint;\n/** @deprecated use `import { ed25519_hasher } from '@noble/curves/ed25519.js';` */\nexport const hashToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() => ed25519_hasher.hashToCurve)();\n/** @deprecated use `import { ed25519_hasher } from '@noble/curves/ed25519.js';` */\nexport const encodeToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() =>\n  ed25519_hasher.encodeToCurve)();\ntype RistHasher = (msg: Uint8Array, options: htfBasicOpts) => _RistrettoPoint;\n/** @deprecated use `import { ristretto255_hasher } from '@noble/curves/ed25519.js';` */\nexport const hashToRistretto255: RistHasher = /* @__PURE__ */ (() =>\n  ristretto255_hasher.hashToCurve as RistHasher)();\n/** @deprecated use `import { ristretto255_hasher } from '@noble/curves/ed25519.js';` */\nexport const hash_to_ristretto255: RistHasher = /* @__PURE__ */ (() =>\n  ristretto255_hasher.hashToCurve as RistHasher)();\n"], "mappings": "AAAA;;;;;;;AAOA;AACA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,wBAAwB;AACzE,SAASC,SAAS,QAA0B,qBAAqB;AACjE,SACEC,iBAAiB,EACjBC,cAAc,QAIT,uBAAuB;AAC9B,SACEC,WAAW,EACXC,YAAY,EACZC,kBAAkB,QAKb,6BAA6B;AACpC,SACEC,KAAK,EACLC,aAAa,EACbC,UAAU,EACVC,YAAY,EACZC,GAAG,EACHC,IAAI,QAEC,uBAAuB;AAC9B,SAASC,UAAU,QAAyC,0BAA0B;AACtF,SAASC,eAAe,EAAEC,WAAW,EAAEC,UAAU,QAAkB,YAAY;AAE/E;AACA,MAAMC,GAAG,GAAG,eAAgBC,MAAM,CAAC,CAAC,CAAC;EAAEC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;EAAEE,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;EAAEG,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC;AACxF;AACA,MAAMI,GAAG,GAAGJ,MAAM,CAAC,CAAC,CAAC;EAAEK,GAAG,GAAGL,MAAM,CAAC,CAAC,CAAC;AAEtC;AACA,MAAMM,eAAe,GAAGN,MAAM,CAC5B,oEAAoE,CACrE;AAED;AACA;AACA;AACA,MAAMO,aAAa,GAAgB,eAAgB,CAAC,OAAO;EACzDC,CAAC,EAAEF,eAAe;EAClBG,CAAC,EAAET,MAAM,CAAC,oEAAoE,CAAC;EAC/EU,CAAC,EAAEL,GAAG;EACNM,CAAC,EAAEX,MAAM,CAAC,oEAAoE,CAAC;EAC/EY,CAAC,EAAEZ,MAAM,CAAC,oEAAoE,CAAC;EAC/Ea,EAAE,EAAEb,MAAM,CAAC,oEAAoE,CAAC;EAChFc,EAAE,EAAEd,MAAM,CAAC,oEAAoE;CAChF,CAAC,EAAC,CAAE;AAEL,SAASe,mBAAmBA,CAACC,CAAS;EACpC;EACA,MAAMC,IAAI,GAAGjB,MAAM,CAAC,EAAE,CAAC;IAAEkB,IAAI,GAAGlB,MAAM,CAAC,EAAE,CAAC;IAAEmB,IAAI,GAAGnB,MAAM,CAAC,EAAE,CAAC;IAAEoB,IAAI,GAAGpB,MAAM,CAAC,EAAE,CAAC;EAChF,MAAMqB,CAAC,GAAGf,eAAe;EACzB,MAAMgB,EAAE,GAAIN,CAAC,GAAGA,CAAC,GAAIK,CAAC;EACtB,MAAME,EAAE,GAAID,EAAE,GAAGN,CAAC,GAAIK,CAAC,CAAC,CAAC;EACzB,MAAMG,EAAE,GAAI9B,IAAI,CAAC6B,EAAE,EAAErB,GAAG,EAAEmB,CAAC,CAAC,GAAGE,EAAE,GAAIF,CAAC,CAAC,CAAC;EACxC,MAAMI,EAAE,GAAI/B,IAAI,CAAC8B,EAAE,EAAEvB,GAAG,EAAEoB,CAAC,CAAC,GAAGL,CAAC,GAAIK,CAAC,CAAC,CAAC;EACvC,MAAMK,GAAG,GAAIhC,IAAI,CAAC+B,EAAE,EAAErB,GAAG,EAAEiB,CAAC,CAAC,GAAGI,EAAE,GAAIJ,CAAC;EACvC,MAAMM,GAAG,GAAIjC,IAAI,CAACgC,GAAG,EAAET,IAAI,EAAEI,CAAC,CAAC,GAAGK,GAAG,GAAIL,CAAC;EAC1C,MAAMO,GAAG,GAAIlC,IAAI,CAACiC,GAAG,EAAET,IAAI,EAAEG,CAAC,CAAC,GAAGM,GAAG,GAAIN,CAAC;EAC1C,MAAMQ,GAAG,GAAInC,IAAI,CAACkC,GAAG,EAAET,IAAI,EAAEE,CAAC,CAAC,GAAGO,GAAG,GAAIP,CAAC;EAC1C,MAAMS,IAAI,GAAIpC,IAAI,CAACmC,GAAG,EAAET,IAAI,EAAEC,CAAC,CAAC,GAAGQ,GAAG,GAAIR,CAAC;EAC3C,MAAMU,IAAI,GAAIrC,IAAI,CAACoC,IAAI,EAAEV,IAAI,EAAEC,CAAC,CAAC,GAAGQ,GAAG,GAAIR,CAAC;EAC5C,MAAMW,IAAI,GAAItC,IAAI,CAACqC,IAAI,EAAEd,IAAI,EAAEI,CAAC,CAAC,GAAGK,GAAG,GAAIL,CAAC;EAC5C,MAAMY,SAAS,GAAIvC,IAAI,CAACsC,IAAI,EAAE9B,GAAG,EAAEmB,CAAC,CAAC,GAAGL,CAAC,GAAIK,CAAC;EAC9C;EACA,OAAO;IAAEY,SAAS;IAAEV;EAAE,CAAE;AAC1B;AAEA,SAASW,iBAAiBA,CAACC,KAAiB;EAC1C;EACA;EACAA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;EACjB;EACAA,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;EAClB;EACAA,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;EACjB,OAAOA,KAAK;AACd;AAEA;AACA;AACA,MAAMC,eAAe,GAAG,eAAgBpC,MAAM,CAC5C,+EAA+E,CAChF;AACD;AACA,SAASqC,OAAOA,CAACC,CAAS,EAAEC,CAAS;EACnC,MAAMlB,CAAC,GAAGf,eAAe;EACzB,MAAMkC,EAAE,GAAG/C,GAAG,CAAC8C,CAAC,GAAGA,CAAC,GAAGA,CAAC,EAAElB,CAAC,CAAC,CAAC,CAAC;EAC9B,MAAMoB,EAAE,GAAGhD,GAAG,CAAC+C,EAAE,GAAGA,EAAE,GAAGD,CAAC,EAAElB,CAAC,CAAC,CAAC,CAAC;EAChC;EACA,MAAMqB,GAAG,GAAG3B,mBAAmB,CAACuB,CAAC,GAAGG,EAAE,CAAC,CAACR,SAAS;EACjD,IAAIjB,CAAC,GAAGvB,GAAG,CAAC6C,CAAC,GAAGE,EAAE,GAAGE,GAAG,EAAErB,CAAC,CAAC,CAAC,CAAC;EAC9B,MAAMsB,GAAG,GAAGlD,GAAG,CAAC8C,CAAC,GAAGvB,CAAC,GAAGA,CAAC,EAAEK,CAAC,CAAC,CAAC,CAAC;EAC/B,MAAMuB,KAAK,GAAG5B,CAAC,CAAC,CAAC;EACjB,MAAM6B,KAAK,GAAGpD,GAAG,CAACuB,CAAC,GAAGoB,eAAe,EAAEf,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAMyB,QAAQ,GAAGH,GAAG,KAAKL,CAAC,CAAC,CAAC;EAC5B,MAAMS,QAAQ,GAAGJ,GAAG,KAAKlD,GAAG,CAAC,CAAC6C,CAAC,EAAEjB,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM2B,MAAM,GAAGL,GAAG,KAAKlD,GAAG,CAAC,CAAC6C,CAAC,GAAGF,eAAe,EAAEf,CAAC,CAAC,CAAC,CAAC;EACrD,IAAIyB,QAAQ,EAAE9B,CAAC,GAAG4B,KAAK;EACvB,IAAIG,QAAQ,IAAIC,MAAM,EAAEhC,CAAC,GAAG6B,KAAK,CAAC,CAAC;EACnC,IAAIrD,YAAY,CAACwB,CAAC,EAAEK,CAAC,CAAC,EAAEL,CAAC,GAAGvB,GAAG,CAAC,CAACuB,CAAC,EAAEK,CAAC,CAAC;EACtC,OAAO;IAAE4B,OAAO,EAAEH,QAAQ,IAAIC,QAAQ;IAAEG,KAAK,EAAElC;EAAC,CAAE;AACpD;AAEA,MAAMmC,EAAE,GAAG,eAAgB,CAAC,MAAM9D,KAAK,CAACkB,aAAa,CAACC,CAAC,EAAE;EAAE4C,IAAI,EAAE;AAAI,CAAE,CAAC,EAAC,CAAE;AAC3E,MAAMC,EAAE,GAAG,eAAgB,CAAC,MAAMhE,KAAK,CAACkB,aAAa,CAACE,CAAC,EAAE;EAAE2C,IAAI,EAAE;AAAI,CAAE,CAAC,EAAC,CAAE;AAE3E,MAAME,eAAe,GAAG,eAAgB,CAAC,OAAO;EAC9C,GAAG/C,aAAa;EAChB4C,EAAE;EACFI,IAAI,EAAE5E,MAAM;EACZuD,iBAAiB;EACjB;EACA;EACA;EACAG;CACD,CAAC,EAAC,CAAE;AAEL;;;;;;;;;;AAUA,OAAO,MAAMmB,OAAO,GAAY,eAAgB,CAAC,MAAMvE,cAAc,CAACqE,eAAe,CAAC,EAAC,CAAE;AAEzF,SAASG,cAAcA,CAACC,IAAgB,EAAEC,GAAe,EAAEC,MAAe;EACxE,IAAID,GAAG,CAACE,MAAM,GAAG,GAAG,EAAE,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;EAC3D,OAAOjF,WAAW,CAChBC,WAAW,CAAC,kCAAkC,CAAC,EAC/C,IAAIiF,UAAU,CAAC,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC,EAAED,GAAG,CAACE,MAAM,CAAC,CAAC,EAC5CF,GAAG,EACHD,IAAI,CACL;AACH;AAEA;AACA,OAAO,MAAMM,UAAU,GAAY,eAAgB,CAAC,MAClD/E,cAAc,CAAC;EACb,GAAGqE,eAAe;EAClBW,MAAM,EAAER;CACT,CAAC,EAAC,CAAE;AAEP;AACA,OAAO,MAAMS,SAAS,GAAY,eAAgB,CAAC,MACjDjF,cAAc,CACZkF,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEd,eAAe,EAAE;EACjCW,MAAM,EAAER,cAAc;EACtBY,OAAO,EAAE1F;CACV,CAAC,CACH,EAAC,CAAE;AAEN;;;;;;;;;;AAUA,OAAO,MAAM2F,MAAM,GAAa,eAAgB,CAAC,MAAK;EACpD,MAAMjD,CAAC,GAAG8B,EAAE,CAACoB,KAAK;EAClB,OAAO5E,UAAU,CAAC;IAChB0B,CAAC;IACDmD,IAAI,EAAE,QAAQ;IACdC,UAAU,EAAGzD,CAAS,IAAY;MAChC;MACA,MAAM;QAAEiB,SAAS;QAAEV;MAAE,CAAE,GAAGR,mBAAmB,CAACC,CAAC,CAAC;MAChD,OAAOvB,GAAG,CAACC,IAAI,CAACuC,SAAS,EAAE9B,GAAG,EAAEkB,CAAC,CAAC,GAAGE,EAAE,EAAEF,CAAC,CAAC;IAC7C,CAAC;IACDa;GACD,CAAC;AACJ,CAAC,EAAC,CAAE;AAEJ;AACA;AACA;AACA,MAAMwC,OAAO,GAAG,eAAgB,CAAC,MAAM,CAACpE,eAAe,GAAGH,GAAG,IAAIE,GAAG,EAAC,CAAE,CAAC,CAAC;AACzE,MAAMsE,OAAO,GAAG,eAAgB,CAAC,MAAMxB,EAAE,CAACT,GAAG,CAACxC,GAAG,EAAEwE,OAAO,CAAC,EAAC,CAAE,CAAC,CAAC;AAChE,MAAME,OAAO,GAAG,eAAgB,CAAC,MAAMzB,EAAE,CAAC0B,IAAI,CAAC1B,EAAE,CAAC2B,GAAG,CAAC3B,EAAE,CAAC4B,GAAG,CAAC,CAAC,EAAC,CAAE,CAAC,CAAC;AAEnE;AACA,SAASC,kCAAkCA,CAAC1C,CAAS;EACnD,MAAM2C,OAAO,GAAG,CAAC3E,eAAe,GAAGF,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/C,MAAM6E,MAAM,GAAGlF,MAAM,CAAC,MAAM,CAAC;EAE7B,IAAImF,GAAG,GAAGhC,EAAE,CAACiC,GAAG,CAAC9C,CAAC,CAAC,CAAC,CAAU;EAC9B6C,GAAG,GAAGhC,EAAE,CAACkC,GAAG,CAACF,GAAG,EAAEjF,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAIoF,EAAE,GAAGnC,EAAE,CAACoC,GAAG,CAACJ,GAAG,EAAEhC,EAAE,CAAC4B,GAAG,CAAC,CAAC,CAAC;EAC9B,IAAIS,GAAG,GAAGrC,EAAE,CAAC2B,GAAG,CAACI,MAAM,CAAC,CAAC,CAAK;EAC9B,IAAIO,GAAG,GAAGtC,EAAE,CAACiC,GAAG,CAACE,EAAE,CAAC,CAAC,CAAS;EAC9B,IAAII,GAAG,GAAGvC,EAAE,CAACkC,GAAG,CAACI,GAAG,EAAEH,EAAE,CAAC,CAAC,CAAI;EAC9B,IAAIK,GAAG,GAAGxC,EAAE,CAACkC,GAAG,CAACF,GAAG,EAAED,MAAM,CAAC,CAAC;EAC9BS,GAAG,GAAGxC,EAAE,CAACkC,GAAG,CAACM,GAAG,EAAEH,GAAG,CAAC,CAAC,CAAO;EAC9BG,GAAG,GAAGxC,EAAE,CAACoC,GAAG,CAACI,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAO;EAC9BE,GAAG,GAAGxC,EAAE,CAACkC,GAAG,CAACM,GAAG,EAAEH,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAII,GAAG,GAAGzC,EAAE,CAACiC,GAAG,CAACM,GAAG,CAAC,CAAC,CAAQ;EAC9BD,GAAG,GAAGtC,EAAE,CAACiC,GAAG,CAACQ,GAAG,CAAC,CAAC,CAAY;EAC9BA,GAAG,GAAGzC,EAAE,CAACkC,GAAG,CAACO,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAO;EAC9BE,GAAG,GAAGzC,EAAE,CAACkC,GAAG,CAACO,GAAG,EAAED,GAAG,CAAC,CAAC,CAAO;EAC9BF,GAAG,GAAGtC,EAAE,CAACkC,GAAG,CAACI,GAAG,EAAEG,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAIC,GAAG,GAAG1C,EAAE,CAACT,GAAG,CAAC+C,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC;EAChCY,GAAG,GAAG1C,EAAE,CAACkC,GAAG,CAACQ,GAAG,EAAED,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAIE,GAAG,GAAG3C,EAAE,CAACkC,GAAG,CAACQ,GAAG,EAAEjB,OAAO,CAAC,CAAC,CAAC;EAChCa,GAAG,GAAGtC,EAAE,CAACiC,GAAG,CAACS,GAAG,CAAC,CAAC,CAAY;EAC9BJ,GAAG,GAAGtC,EAAE,CAACkC,GAAG,CAACI,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAIK,EAAE,GAAG5C,EAAE,CAAC6C,GAAG,CAACP,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAI;EAC9B,IAAIM,EAAE,GAAG9C,EAAE,CAAC+C,IAAI,CAACJ,GAAG,EAAED,GAAG,EAAEE,EAAE,CAAC,CAAC,CAAC;EAChC,IAAII,GAAG,GAAGhD,EAAE,CAACkC,GAAG,CAACG,GAAG,EAAEL,GAAG,CAAC,CAAC,CAAG;EAC9B,IAAIiB,GAAG,GAAGjD,EAAE,CAACkC,GAAG,CAACQ,GAAG,EAAEvD,CAAC,CAAC,CAAC,CAAK;EAC9B8D,GAAG,GAAGjD,EAAE,CAACkC,GAAG,CAACe,GAAG,EAAEzB,OAAO,CAAC,CAAC,CAAG;EAC9B,IAAI0B,GAAG,GAAGlD,EAAE,CAACkC,GAAG,CAACe,GAAG,EAAExB,OAAO,CAAC,CAAC,CAAC;EAChC,IAAI0B,GAAG,GAAGnD,EAAE,CAACkC,GAAG,CAACM,GAAG,EAAER,GAAG,CAAC,CAAC,CAAG;EAC9BM,GAAG,GAAGtC,EAAE,CAACiC,GAAG,CAACgB,GAAG,CAAC,CAAC,CAAY;EAC9BX,GAAG,GAAGtC,EAAE,CAACkC,GAAG,CAACI,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAIa,EAAE,GAAGpD,EAAE,CAAC6C,GAAG,CAACP,GAAG,EAAEa,GAAG,CAAC,CAAC,CAAI;EAC9B,IAAIE,EAAE,GAAGrD,EAAE,CAAC+C,IAAI,CAACG,GAAG,EAAED,GAAG,EAAEG,EAAE,CAAC,CAAC,CAAC;EAChCd,GAAG,GAAGtC,EAAE,CAACiC,GAAG,CAACa,EAAE,CAAC,CAAC,CAAa;EAC9BR,GAAG,GAAGtC,EAAE,CAACkC,GAAG,CAACI,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAO;EAC9B,IAAIe,EAAE,GAAGtD,EAAE,CAAC6C,GAAG,CAACP,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAI;EAC9B,IAAIe,EAAE,GAAGvD,EAAE,CAAC+C,IAAI,CAACC,GAAG,EAAEX,GAAG,EAAEiB,EAAE,CAAC,CAAC,CAAC;EAChC,IAAIE,CAAC,GAAGxD,EAAE,CAAC+C,IAAI,CAACM,EAAE,EAAEP,EAAE,EAAEQ,EAAE,CAAC,CAAC,CAAE;EAC9B,IAAIG,EAAE,GAAGzD,EAAE,CAAC0D,KAAM,CAACF,CAAC,CAAC,CAAC,CAAS;EAC/BA,CAAC,GAAGxD,EAAE,CAAC+C,IAAI,CAACS,CAAC,EAAExD,EAAE,CAAC2B,GAAG,CAAC6B,CAAC,CAAC,EAAEF,EAAE,KAAKG,EAAE,CAAC,CAAC,CAAC;EACtC,OAAO;IAAEE,GAAG,EAAEJ,EAAE;IAAEK,GAAG,EAAEzB,EAAE;IAAE0B,GAAG,EAAEL,CAAC;IAAEM,GAAG,EAAEhH;EAAG,CAAE,CAAC,CAAC;AACjD;AAEA,MAAMiH,eAAe,GAAG,eAAgB,CAAC,MAAM3H,UAAU,CAAC4D,EAAE,EAAEA,EAAE,CAAC2B,GAAG,CAAC9E,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAE,CAAC,CAAC;AAC1F,SAASmH,oCAAoCA,CAAC7E,CAAS;EACrD,MAAM;IAAEwE,GAAG;IAAEC,GAAG;IAAEC,GAAG;IAAEC;EAAG,CAAE,GAAGjC,kCAAkC,CAAC1C,CAAC,CAAC,CAAC,CAAC;EACtE;EACA,IAAIoE,EAAE,GAAGvD,EAAE,CAACkC,GAAG,CAACyB,GAAG,EAAEG,GAAG,CAAC,CAAC,CAAC;EAC3BP,EAAE,GAAGvD,EAAE,CAACkC,GAAG,CAACqB,EAAE,EAAEQ,eAAe,CAAC,CAAC,CAAC;EAClC,IAAI5B,EAAE,GAAGnC,EAAE,CAACkC,GAAG,CAAC0B,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;EAC3B,IAAII,EAAE,GAAGjE,EAAE,CAACkE,GAAG,CAACP,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;EAC3B,IAAIO,EAAE,GAAGnE,EAAE,CAACoC,GAAG,CAACuB,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;EAC3B,IAAI5B,GAAG,GAAGhC,EAAE,CAACkC,GAAG,CAACC,EAAE,EAAEgC,EAAE,CAAC,CAAC,CAAC;EAC1B,IAAIC,CAAC,GAAGpE,EAAE,CAAC6C,GAAG,CAACb,GAAG,EAAEhC,EAAE,CAACqE,IAAI,CAAC,CAAC,CAAC;EAC9Bd,EAAE,GAAGvD,EAAE,CAAC+C,IAAI,CAACQ,EAAE,EAAEvD,EAAE,CAACqE,IAAI,EAAED,CAAC,CAAC,CAAC,CAAC;EAC9BjC,EAAE,GAAGnC,EAAE,CAAC+C,IAAI,CAACZ,EAAE,EAAEnC,EAAE,CAAC4B,GAAG,EAAEwC,CAAC,CAAC,CAAC,CAAC;EAC7BH,EAAE,GAAGjE,EAAE,CAAC+C,IAAI,CAACkB,EAAE,EAAEjE,EAAE,CAAC4B,GAAG,EAAEwC,CAAC,CAAC,CAAC,CAAC;EAC7BD,EAAE,GAAGnE,EAAE,CAAC+C,IAAI,CAACoB,EAAE,EAAEnE,EAAE,CAAC4B,GAAG,EAAEwC,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAM,CAACE,MAAM,EAAEC,MAAM,CAAC,GAAGpI,aAAa,CAAC6D,EAAE,EAAE,CAACmC,EAAE,EAAEgC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D,OAAO;IAAEtG,CAAC,EAAEmC,EAAE,CAACkC,GAAG,CAACqB,EAAE,EAAEe,MAAM,CAAC;IAAEd,CAAC,EAAExD,EAAE,CAACkC,GAAG,CAAC+B,EAAE,EAAEM,MAAM;EAAC,CAAE,CAAC,CAAC;AAC3D;AAEA;AACA,OAAO,MAAMC,cAAc,GAAsB,eAAgB,CAAC,MAChExI,YAAY,CACVqE,OAAO,CAACoE,KAAK,EACZC,OAAiB,IAAKV,oCAAoC,CAACU,OAAO,CAAC,CAAC,CAAC,CAAC,EACvE;EACEC,GAAG,EAAE,mCAAmC;EACxCC,SAAS,EAAE,mCAAmC;EAC9CvH,CAAC,EAAEF,eAAe;EAClB0H,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,GAAG;EACNC,MAAM,EAAE,KAAK;EACb3E,IAAI,EAAE5E;CACP,CACF,EAAC,CAAE;AAEN;AACA,MAAMwJ,OAAO,GAAG/F,eAAe;AAC/B;AACA,MAAMgG,iBAAiB,GAAG,eAAgBpI,MAAM,CAC9C,+EAA+E,CAChF;AACD;AACA,MAAMqI,iBAAiB,GAAG,eAAgBrI,MAAM,CAC9C,+EAA+E,CAChF;AACD;AACA,MAAMsI,cAAc,GAAG,eAAgBtI,MAAM,CAC3C,8EAA8E,CAC/E;AACD;AACA,MAAMuI,cAAc,GAAG,eAAgBvI,MAAM,CAC3C,+EAA+E,CAChF;AACD;AACA,MAAMwI,UAAU,GAAIC,MAAc,IAAKpG,OAAO,CAACpC,GAAG,EAAEwI,MAAM,CAAC;AAE3D,MAAMC,QAAQ,GAAG,eAAgB1I,MAAM,CACrC,oEAAoE,CACrE;AACD,MAAM2I,kBAAkB,GAAIxG,KAAiB,IAC3CqB,OAAO,CAACoE,KAAK,CAACzE,EAAE,CAACyF,MAAM,CAAChJ,eAAe,CAACuC,KAAK,CAAC,GAAGuG,QAAQ,CAAC;AAI5D;;;;;AAKA,SAASG,yBAAyBA,CAACC,EAAU;EAC3C,MAAM;IAAElI;EAAC,CAAE,GAAGL,aAAa;EAC3B,MAAMc,CAAC,GAAGf,eAAe;EACzB,MAAMb,GAAG,GAAIgB,CAAS,IAAK0C,EAAE,CAACyF,MAAM,CAACnI,CAAC,CAAC;EACvC,MAAMsI,CAAC,GAAGtJ,GAAG,CAAC0I,OAAO,GAAGW,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;EAClC,MAAME,EAAE,GAAGvJ,GAAG,CAAC,CAACsJ,CAAC,GAAG9I,GAAG,IAAIqI,cAAc,CAAC,CAAC,CAAC;EAC5C,IAAIW,CAAC,GAAGjJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,MAAMkJ,CAAC,GAAGzJ,GAAG,CAAC,CAACwJ,CAAC,GAAGrI,CAAC,GAAGmI,CAAC,IAAItJ,GAAG,CAACsJ,CAAC,GAAGnI,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,IAAI;IAAEqC,OAAO,EAAEkG,UAAU;IAAEjG,KAAK,EAAEkG;EAAC,CAAE,GAAG/G,OAAO,CAAC2G,EAAE,EAAEE,CAAC,CAAC,CAAC,CAAC;EACxD,IAAIG,EAAE,GAAG5J,GAAG,CAAC2J,CAAC,GAAGN,EAAE,CAAC,CAAC,CAAC;EACtB,IAAI,CAACtJ,YAAY,CAAC6J,EAAE,EAAEhI,CAAC,CAAC,EAAEgI,EAAE,GAAG5J,GAAG,CAAC,CAAC4J,EAAE,CAAC;EACvC,IAAI,CAACF,UAAU,EAAEC,CAAC,GAAGC,EAAE,CAAC,CAAC;EACzB,IAAI,CAACF,UAAU,EAAEF,CAAC,GAAGF,CAAC,CAAC,CAAC;EACxB,MAAMO,EAAE,GAAG7J,GAAG,CAACwJ,CAAC,IAAIF,CAAC,GAAG9I,GAAG,CAAC,GAAGsI,cAAc,GAAGW,CAAC,CAAC,CAAC,CAAC;EACpD,MAAMK,EAAE,GAAGH,CAAC,GAAGA,CAAC;EAChB,MAAMI,EAAE,GAAG/J,GAAG,CAAC,CAAC2J,CAAC,GAAGA,CAAC,IAAIF,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMO,EAAE,GAAGhK,GAAG,CAAC6J,EAAE,GAAGlB,iBAAiB,CAAC,CAAC,CAAC;EACxC,MAAMsB,EAAE,GAAGjK,GAAG,CAACQ,GAAG,GAAGsJ,EAAE,CAAC,CAAC,CAAC;EAC1B,MAAMI,EAAE,GAAGlK,GAAG,CAACQ,GAAG,GAAGsJ,EAAE,CAAC,CAAC,CAAC;EAC1B,OAAO,IAAI/F,OAAO,CAACoE,KAAK,CAACnI,GAAG,CAAC+J,EAAE,GAAGG,EAAE,CAAC,EAAElK,GAAG,CAACiK,EAAE,GAAGD,EAAE,CAAC,EAAEhK,GAAG,CAACgK,EAAE,GAAGE,EAAE,CAAC,EAAElK,GAAG,CAAC+J,EAAE,GAAGE,EAAE,CAAC,CAAC;AAClF;AAEA,SAASE,gBAAgBA,CAACzH,KAAiB;EACzCvD,MAAM,CAACuD,KAAK,EAAE,EAAE,CAAC;EACjB,MAAM0H,EAAE,GAAGlB,kBAAkB,CAACxG,KAAK,CAAC2H,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EACpD,MAAMC,EAAE,GAAGlB,yBAAyB,CAACgB,EAAE,CAAC;EACxC,MAAMG,EAAE,GAAGrB,kBAAkB,CAACxG,KAAK,CAAC2H,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACrD,MAAMG,EAAE,GAAGpB,yBAAyB,CAACmB,EAAE,CAAC;EACxC,OAAO,IAAIE,eAAe,CAACH,EAAE,CAACxE,GAAG,CAAC0E,EAAE,CAAC,CAAC;AACxC;AAEA;;;;;;;;;AASA,MAAMC,eAAgB,SAAQlL,iBAAkC;EAgB9DmL,YAAYC,EAAiB;IAC3B,KAAK,CAACA,EAAE,CAAC;EACX;EAEA,OAAOC,UAAUA,CAACC,EAAuB;IACvC,OAAO,IAAIJ,eAAe,CAAC1G,OAAO,CAACoE,KAAK,CAACyC,UAAU,CAACC,EAAE,CAAC,CAAC;EAC1D;EAEUC,UAAUA,CAACC,KAAsB;IACzC,IAAI,EAAEA,KAAK,YAAYN,eAAe,CAAC,EAAE,MAAM,IAAIpG,KAAK,CAAC,yBAAyB,CAAC;EACrF;EAEU2G,IAAIA,CAACL,EAAgB;IAC7B,OAAO,IAAIF,eAAe,CAACE,EAAE,CAAC;EAChC;EAEA;EACA,OAAOM,WAAWA,CAACC,GAAQ;IACzB,OAAOf,gBAAgB,CAAC/J,WAAW,CAAC,eAAe,EAAE8K,GAAG,EAAE,EAAE,CAAC,CAAC;EAChE;EAEA,OAAOC,SAASA,CAACzI,KAAiB;IAChCvD,MAAM,CAACuD,KAAK,EAAE,EAAE,CAAC;IACjB,MAAM;MAAExB,CAAC;MAAEC;IAAC,CAAE,GAAGL,aAAa;IAC9B,MAAMc,CAAC,GAAGf,eAAe;IACzB,MAAMb,GAAG,GAAIgB,CAAS,IAAK0C,EAAE,CAACyF,MAAM,CAACnI,CAAC,CAAC;IACvC,MAAM2I,CAAC,GAAGT,kBAAkB,CAACxG,KAAK,CAAC;IACnC;IACA;IACA,IAAI,CAACrC,UAAU,CAACqD,EAAE,CAAC0H,OAAO,CAACzB,CAAC,CAAC,EAAEjH,KAAK,CAAC,IAAI3C,YAAY,CAAC4J,CAAC,EAAE/H,CAAC,CAAC,EACzD,MAAM,IAAIyC,KAAK,CAAC,iCAAiC,CAAC;IACpD,MAAMyF,EAAE,GAAG9J,GAAG,CAAC2J,CAAC,GAAGA,CAAC,CAAC;IACrB,MAAM0B,EAAE,GAAGrL,GAAG,CAACQ,GAAG,GAAGU,CAAC,GAAG4I,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAMwB,EAAE,GAAGtL,GAAG,CAACQ,GAAG,GAAGU,CAAC,GAAG4I,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAMyB,IAAI,GAAGvL,GAAG,CAACqL,EAAE,GAAGA,EAAE,CAAC;IACzB,MAAMG,IAAI,GAAGxL,GAAG,CAACsL,EAAE,GAAGA,EAAE,CAAC;IACzB,MAAMxI,CAAC,GAAG9C,GAAG,CAACkB,CAAC,GAAGC,CAAC,GAAGoK,IAAI,GAAGC,IAAI,CAAC,CAAC,CAAC;IACpC,MAAM;MAAEhI,OAAO;MAAEC,KAAK,EAAEgI;IAAC,CAAE,GAAG1C,UAAU,CAAC/I,GAAG,CAAC8C,CAAC,GAAG0I,IAAI,CAAC,CAAC,CAAC,CAAC;IACzD,MAAME,EAAE,GAAG1L,GAAG,CAACyL,CAAC,GAAGH,EAAE,CAAC,CAAC,CAAC;IACxB,MAAMK,EAAE,GAAG3L,GAAG,CAACyL,CAAC,GAAGC,EAAE,GAAG5I,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAIvB,CAAC,GAAGvB,GAAG,CAAC,CAAC2J,CAAC,GAAGA,CAAC,IAAI+B,EAAE,CAAC,CAAC,CAAC;IAC3B,IAAI3L,YAAY,CAACwB,CAAC,EAAEK,CAAC,CAAC,EAAEL,CAAC,GAAGvB,GAAG,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC;IACrC,MAAM2F,CAAC,GAAGlH,GAAG,CAACqL,EAAE,GAAGM,EAAE,CAAC,CAAC,CAAC;IACxB,MAAMC,CAAC,GAAG5L,GAAG,CAACuB,CAAC,GAAG2F,CAAC,CAAC,CAAC,CAAC;IACtB,IAAI,CAAC1D,OAAO,IAAIzD,YAAY,CAAC6L,CAAC,EAAEhK,CAAC,CAAC,IAAIsF,CAAC,KAAK5G,GAAG,EAC7C,MAAM,IAAI+D,KAAK,CAAC,iCAAiC,CAAC;IACpD,OAAO,IAAIoG,eAAe,CAAC,IAAI1G,OAAO,CAACoE,KAAK,CAAC5G,CAAC,EAAE2F,CAAC,EAAE1G,GAAG,EAAEoL,CAAC,CAAC,CAAC;EAC7D;EAEA;;;;;EAKA,OAAOC,OAAOA,CAACX,GAAQ;IACrB,OAAOT,eAAe,CAACU,SAAS,CAAC/K,WAAW,CAAC,cAAc,EAAE8K,GAAG,EAAE,EAAE,CAAC,CAAC;EACxE;EAEA,OAAOY,GAAGA,CAACC,MAAyB,EAAE3D,OAAiB;IACrD,OAAO9I,SAAS,CAACmL,eAAe,EAAE1G,OAAO,CAACoE,KAAK,CAACvE,EAAE,EAAEmI,MAAM,EAAE3D,OAAO,CAAC;EACtE;EAEA;;;;EAIAgD,OAAOA,CAAA;IACL,IAAI;MAAEY,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI,CAACxB,EAAE;IAC5B,MAAM/I,CAAC,GAAGf,eAAe;IACzB,MAAMb,GAAG,GAAIgB,CAAS,IAAK0C,EAAE,CAACyF,MAAM,CAACnI,CAAC,CAAC;IACvC,MAAMqK,EAAE,GAAGrL,GAAG,CAACA,GAAG,CAACkM,CAAC,GAAGD,CAAC,CAAC,GAAGjM,GAAG,CAACkM,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,MAAMX,EAAE,GAAGtL,GAAG,CAACgM,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,MAAMG,IAAI,GAAGpM,GAAG,CAACsL,EAAE,GAAGA,EAAE,CAAC;IACzB,MAAM;MAAE7H,KAAK,EAAE4I;IAAO,CAAE,GAAGtD,UAAU,CAAC/I,GAAG,CAACqL,EAAE,GAAGe,IAAI,CAAC,CAAC,CAAC,CAAC;IACvD,MAAME,EAAE,GAAGtM,GAAG,CAACqM,OAAO,GAAGhB,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAMkB,EAAE,GAAGvM,GAAG,CAACqM,OAAO,GAAGf,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAMkB,IAAI,GAAGxM,GAAG,CAACsM,EAAE,GAAGC,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAI1C,CAAS,CAAC,CAAC;IACf,IAAI1J,YAAY,CAACoM,CAAC,GAAGK,IAAI,EAAE5K,CAAC,CAAC,EAAE;MAC7B,IAAI6K,EAAE,GAAGzM,GAAG,CAACiM,CAAC,GAAGvD,OAAO,CAAC;MACzB,IAAIgE,EAAE,GAAG1M,GAAG,CAACgM,CAAC,GAAGtD,OAAO,CAAC;MACzBsD,CAAC,GAAGS,EAAE;MACNR,CAAC,GAAGS,EAAE;MACNjD,CAAC,GAAGzJ,GAAG,CAACsM,EAAE,GAAG1D,iBAAiB,CAAC;IACjC,CAAC,MAAM;MACLa,CAAC,GAAG8C,EAAE,CAAC,CAAC;IACV;IACA,IAAIxM,YAAY,CAACiM,CAAC,GAAGQ,IAAI,EAAE5K,CAAC,CAAC,EAAEqK,CAAC,GAAGjM,GAAG,CAAC,CAACiM,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAItC,CAAC,GAAG3J,GAAG,CAAC,CAACkM,CAAC,GAAGD,CAAC,IAAIxC,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI1J,YAAY,CAAC4J,CAAC,EAAE/H,CAAC,CAAC,EAAE+H,CAAC,GAAG3J,GAAG,CAAC,CAAC2J,CAAC,CAAC;IACnC,OAAOjG,EAAE,CAAC0H,OAAO,CAACzB,CAAC,CAAC,CAAC,CAAC;EACxB;EAEA;;;;EAIAgD,MAAMA,CAAC5B,KAAsB;IAC3B,IAAI,CAACD,UAAU,CAACC,KAAK,CAAC;IACtB,MAAM;MAAEiB,CAAC,EAAEY,EAAE;MAAEX,CAAC,EAAEY;IAAE,CAAE,GAAG,IAAI,CAAClC,EAAE;IAChC,MAAM;MAAEqB,CAAC,EAAEc,EAAE;MAAEb,CAAC,EAAEc;IAAE,CAAE,GAAGhC,KAAK,CAACJ,EAAE;IACjC,MAAM3K,GAAG,GAAIgB,CAAS,IAAK0C,EAAE,CAACyF,MAAM,CAACnI,CAAC,CAAC;IACvC;IACA,MAAMgM,GAAG,GAAGhN,GAAG,CAAC4M,EAAE,GAAGG,EAAE,CAAC,KAAK/M,GAAG,CAAC6M,EAAE,GAAGC,EAAE,CAAC;IACzC,MAAMG,GAAG,GAAGjN,GAAG,CAAC6M,EAAE,GAAGE,EAAE,CAAC,KAAK/M,GAAG,CAAC4M,EAAE,GAAGE,EAAE,CAAC;IACzC,OAAOE,GAAG,IAAIC,GAAG;EACnB;EAEAC,GAAGA,CAAA;IACD,OAAO,IAAI,CAACP,MAAM,CAAClC,eAAe,CAAC1C,IAAI,CAAC;EAC1C;;AA9HA;AACA;AACA;AACO0C,eAAA,CAAA0C,IAAI,GACT,eAAgB,CAAC,MAAM,IAAI1C,eAAe,CAAC1G,OAAO,CAACoE,KAAK,CAACgF,IAAI,CAAC,EAAC,CAAE;AACnE;AACO1C,eAAA,CAAA1C,IAAI,GACT,eAAgB,CAAC,MAAM,IAAI0C,eAAe,CAAC1G,OAAO,CAACoE,KAAK,CAACJ,IAAI,CAAC,EAAC,CAAE;AACnE;AACO0C,eAAA,CAAA/G,EAAE,GACP,eAAgB,CAAC,MAAMA,EAAE,EAAC,CAAE;AAC9B;AACO+G,eAAA,CAAA7G,EAAE,GACP,eAAgB,CAAC,MAAMA,EAAE,EAAC,CAAE;AAoHhC,OAAO,MAAMwJ,YAAY,GAErB;EAAEjF,KAAK,EAAEsC;AAAe,CAAE;AAE9B;AACA,OAAO,MAAM4C,mBAAmB,GAA0B;EACxDpC,WAAWA,CAACqC,GAAe,EAAEC,OAAsB;IACjD,MAAMlF,GAAG,GAAGkF,OAAO,EAAElF,GAAG,IAAI,sCAAsC;IAClE,MAAMmF,GAAG,GAAG7N,kBAAkB,CAAC2N,GAAG,EAAEjF,GAAG,EAAE,EAAE,EAAEnJ,MAAM,CAAC;IACpD,OAAOiL,gBAAgB,CAACqD,GAAG,CAAC;EAC9B,CAAC;EACDC,YAAYA,CAACH,GAAe,EAAEC,OAAA,GAAwB;IAAElF,GAAG,EAAE5I;EAAW,CAAE;IACxE,MAAM+N,GAAG,GAAG7N,kBAAkB,CAAC2N,GAAG,EAAEC,OAAO,CAAClF,GAAG,EAAE,EAAE,EAAEnJ,MAAM,CAAC;IAC5D,OAAO0E,EAAE,CAACuF,MAAM,CAAChJ,eAAe,CAACqN,GAAG,CAAC,CAAC;EACxC;CACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;AAMA,OAAO,MAAME,wBAAwB,GAAa,CAChD,kEAAkE,EAClE,kEAAkE,EAClE,kEAAkE,EAClE,kEAAkE,EAClE,kEAAkE,EAClE,kEAAkE,EAClE,kEAAkE,EAClE,kEAAkE,CACnE;AAED;AACA,OAAM,SAAUC,sBAAsBA,CAACC,UAAe;EACpD,OAAO7J,OAAO,CAAC8J,KAAK,CAACC,YAAY,CAAC1N,WAAW,CAAC,KAAK,EAAEwN,UAAU,CAAC,CAAC;AACnE;AACA;AACA,OAAO,MAAMG,mBAAmB,GAAkCJ,sBAAsB;AAExF;AACA,OAAM,SAAUK,uBAAuBA,CAACC,WAAuB;EAC7D,OAAOlK,OAAO,CAAC8J,KAAK,CAACK,kBAAkB,CAAC9N,WAAW,CAAC,KAAK,EAAE6N,WAAW,CAAC,CAAC;AAC1E;AAEA;AACA,OAAO,MAAME,cAAc,GAA2B1D,eAAe;AACrE;AACA,OAAO,MAAMQ,WAAW,GAAsB,eAAgB,CAAC,MAAM/C,cAAc,CAAC+C,WAAW,EAAC,CAAE;AAClG;AACA,OAAO,MAAMmD,aAAa,GAAsB,eAAgB,CAAC,MAC/DlG,cAAc,CAACkG,aAAa,EAAC,CAAE;AAEjC;AACA,OAAO,MAAMC,kBAAkB,GAAe,eAAgB,CAAC,MAC7DhB,mBAAmB,CAACpC,WAAyB,EAAC,CAAE;AAClD;AACA,OAAO,MAAMqD,oBAAoB,GAAe,eAAgB,CAAC,MAC/DjB,mBAAmB,CAACpC,WAAyB,EAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}