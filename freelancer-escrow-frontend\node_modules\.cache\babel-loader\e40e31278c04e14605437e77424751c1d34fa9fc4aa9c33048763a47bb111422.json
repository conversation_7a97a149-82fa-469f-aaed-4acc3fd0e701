{"ast": null, "code": "import assert from '@noble/hashes/_assert';\nimport { pbkdf2, pbkdf2Async } from '@noble/hashes/pbkdf2';\nimport { sha256 } from '@noble/hashes/sha256';\nimport { sha512 } from '@noble/hashes/sha512';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { utils as baseUtils } from '@scure/base';\nconst isJapanese = wordlist => wordlist[0] === '\\u3042\\u3044\\u3053\\u304f\\u3057\\u3093';\nfunction nfkd(str) {\n  if (typeof str !== 'string') throw new TypeError(`Invalid mnemonic type: ${typeof str}`);\n  return str.normalize('NFKD');\n}\nfunction normalize(str) {\n  const norm = nfkd(str);\n  const words = norm.split(' ');\n  if (![12, 15, 18, 21, 24].includes(words.length)) throw new Error('Invalid mnemonic');\n  return {\n    nfkd: norm,\n    words\n  };\n}\nfunction assertEntropy(entropy) {\n  assert.bytes(entropy, 16, 20, 24, 28, 32);\n}\nexport function generateMnemonic(wordlist, strength = 128) {\n  assert.number(strength);\n  if (strength % 32 !== 0 || strength > 256) throw new TypeError('Invalid entropy');\n  return entropyToMnemonic(randomBytes(strength / 8), wordlist);\n}\nconst calcChecksum = entropy => {\n  const bitsLeft = 8 - entropy.length / 4;\n  return new Uint8Array([sha256(entropy)[0] >> bitsLeft << bitsLeft]);\n};\nfunction getCoder(wordlist) {\n  if (!Array.isArray(wordlist) || wordlist.length !== 2048 || typeof wordlist[0] !== 'string') throw new Error('Worlist: expected array of 2048 strings');\n  wordlist.forEach(i => {\n    if (typeof i !== 'string') throw new Error(`Wordlist: non-string element: ${i}`);\n  });\n  return baseUtils.chain(baseUtils.checksum(1, calcChecksum), baseUtils.radix2(11, true), baseUtils.alphabet(wordlist));\n}\nexport function mnemonicToEntropy(mnemonic, wordlist) {\n  const {\n    words\n  } = normalize(mnemonic);\n  const entropy = getCoder(wordlist).decode(words);\n  assertEntropy(entropy);\n  return entropy;\n}\nexport function entropyToMnemonic(entropy, wordlist) {\n  assertEntropy(entropy);\n  const words = getCoder(wordlist).encode(entropy);\n  return words.join(isJapanese(wordlist) ? '\\u3000' : ' ');\n}\nexport function validateMnemonic(mnemonic, wordlist) {\n  try {\n    mnemonicToEntropy(mnemonic, wordlist);\n  } catch (e) {\n    return false;\n  }\n  return true;\n}\nconst salt = passphrase => nfkd(`mnemonic${passphrase}`);\nexport function mnemonicToSeed(mnemonic, passphrase = '') {\n  return pbkdf2Async(sha512, normalize(mnemonic).nfkd, salt(passphrase), {\n    c: 2048,\n    dkLen: 64\n  });\n}\nexport function mnemonicToSeedSync(mnemonic, passphrase = '') {\n  return pbkdf2(sha512, normalize(mnemonic).nfkd, salt(passphrase), {\n    c: 2048,\n    dkLen: 64\n  });\n}", "map": {"version": 3, "names": ["assert", "pbkdf2", "pbkdf2Async", "sha256", "sha512", "randomBytes", "utils", "baseUtils", "isJapanese", "wordlist", "nfkd", "str", "TypeError", "normalize", "norm", "words", "split", "includes", "length", "Error", "assertEntropy", "entropy", "bytes", "generateMnemonic", "strength", "number", "entropyToMnemonic", "calcChecksum", "bitsLeft", "Uint8Array", "getCoder", "Array", "isArray", "for<PERSON>ach", "i", "chain", "checksum", "radix2", "alphabet", "mnemonicToEntropy", "mnemonic", "decode", "encode", "join", "validateMnemonic", "e", "salt", "passphrase", "mnemonicToSeed", "c", "dkLen", "mnemonicToSeedSync"], "sources": ["../src/index.ts"], "sourcesContent": [null], "mappings": "AACA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,SAASC,MAAM,EAAEC,WAAW,QAAQ,sBAAsB;AAC1D,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,IAAIC,SAAS,QAAQ,aAAa;AAGhD,MAAMC,UAAU,GAAIC,QAAkB,IAAKA,QAAQ,CAAC,CAAC,CAAC,KAAK,sCAAsC;AAKjG,SAASC,IAAIA,CAACC,GAAW;EACvB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIC,SAAS,CAAC,0BAA0B,OAAOD,GAAG,EAAE,CAAC;EACxF,OAAOA,GAAG,CAACE,SAAS,CAAC,MAAM,CAAC;AAC9B;AAEA,SAASA,SAASA,CAACF,GAAW;EAC5B,MAAMG,IAAI,GAAGJ,IAAI,CAACC,GAAG,CAAC;EACtB,MAAMI,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAC7B,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;EACrF,OAAO;IAAET,IAAI,EAAEI,IAAI;IAAEC;EAAK,CAAE;AAC9B;AAEA,SAASK,aAAaA,CAACC,OAAmB;EACxCrB,MAAM,CAACsB,KAAK,CAACD,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3C;AAUA,OAAM,SAAUE,gBAAgBA,CAACd,QAAkB,EAAEe,QAAA,GAAmB,GAAG;EACzExB,MAAM,CAACyB,MAAM,CAACD,QAAQ,CAAC;EACvB,IAAIA,QAAQ,GAAG,EAAE,KAAK,CAAC,IAAIA,QAAQ,GAAG,GAAG,EAAE,MAAM,IAAIZ,SAAS,CAAC,iBAAiB,CAAC;EACjF,OAAOc,iBAAiB,CAACrB,WAAW,CAACmB,QAAQ,GAAG,CAAC,CAAC,EAAEf,QAAQ,CAAC;AAC/D;AAEA,MAAMkB,YAAY,GAAIN,OAAmB,IAAI;EAE3C,MAAMO,QAAQ,GAAG,CAAC,GAAGP,OAAO,CAACH,MAAM,GAAG,CAAC;EAGvC,OAAO,IAAIW,UAAU,CAAC,CAAE1B,MAAM,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIO,QAAQ,IAAKA,QAAQ,CAAC,CAAC;AACvE,CAAC;AAED,SAASE,QAAQA,CAACrB,QAAkB;EAClC,IAAI,CAACsB,KAAK,CAACC,OAAO,CAACvB,QAAQ,CAAC,IAAIA,QAAQ,CAACS,MAAM,KAAK,IAAI,IAAI,OAAOT,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EACzF,MAAM,IAAIU,KAAK,CAAC,yCAAyC,CAAC;EAC5DV,QAAQ,CAACwB,OAAO,CAAEC,CAAC,IAAI;IACrB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIf,KAAK,CAAC,iCAAiCe,CAAC,EAAE,CAAC;EAClF,CAAC,CAAC;EACF,OAAO3B,SAAS,CAAC4B,KAAK,CACpB5B,SAAS,CAAC6B,QAAQ,CAAC,CAAC,EAAET,YAAY,CAAC,EACnCpB,SAAS,CAAC8B,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,EAC1B9B,SAAS,CAAC+B,QAAQ,CAAC7B,QAAQ,CAAC,CAC7B;AACH;AAeA,OAAM,SAAU8B,iBAAiBA,CAACC,QAAgB,EAAE/B,QAAkB;EACpE,MAAM;IAAEM;EAAK,CAAE,GAAGF,SAAS,CAAC2B,QAAQ,CAAC;EACrC,MAAMnB,OAAO,GAAGS,QAAQ,CAACrB,QAAQ,CAAC,CAACgC,MAAM,CAAC1B,KAAK,CAAC;EAChDK,aAAa,CAACC,OAAO,CAAC;EACtB,OAAOA,OAAO;AAChB;AAeA,OAAM,SAAUK,iBAAiBA,CAACL,OAAmB,EAAEZ,QAAkB;EACvEW,aAAa,CAACC,OAAO,CAAC;EACtB,MAAMN,KAAK,GAAGe,QAAQ,CAACrB,QAAQ,CAAC,CAACiC,MAAM,CAACrB,OAAO,CAAC;EAChD,OAAON,KAAK,CAAC4B,IAAI,CAACnC,UAAU,CAACC,QAAQ,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;AAC1D;AAKA,OAAM,SAAUmC,gBAAgBA,CAACJ,QAAgB,EAAE/B,QAAkB;EACnE,IAAI;IACF8B,iBAAiB,CAACC,QAAQ,EAAE/B,QAAQ,CAAC;GACtC,CAAC,OAAOoC,CAAC,EAAE;IACV,OAAO,KAAK;;EAEd,OAAO,IAAI;AACb;AAEA,MAAMC,IAAI,GAAIC,UAAkB,IAAKrC,IAAI,CAAC,WAAWqC,UAAU,EAAE,CAAC;AAYlE,OAAM,SAAUC,cAAcA,CAACR,QAAgB,EAAEO,UAAU,GAAG,EAAE;EAC9D,OAAO7C,WAAW,CAACE,MAAM,EAAES,SAAS,CAAC2B,QAAQ,CAAC,CAAC9B,IAAI,EAAEoC,IAAI,CAACC,UAAU,CAAC,EAAE;IAAEE,CAAC,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAE,CAAC;AAChG;AAYA,OAAM,SAAUC,kBAAkBA,CAACX,QAAgB,EAAEO,UAAU,GAAG,EAAE;EAClE,OAAO9C,MAAM,CAACG,MAAM,EAAES,SAAS,CAAC2B,QAAQ,CAAC,CAAC9B,IAAI,EAAEoC,IAAI,CAACC,UAAU,CAAC,EAAE;IAAEE,CAAC,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAE,CAAC;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}