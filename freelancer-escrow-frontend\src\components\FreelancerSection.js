import React from 'react';

const FreelancerSection = () => {
  const freelancers = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Full-Stack Developer',
      rating: 4.9,
      completed: 127,
      skills: ['React', 'Node.js', 'Python', 'Blockchain'],
      hourlyRate: 85,
      avatar: '👩‍💻'
    },
    {
      id: 2,
      name: '<PERSON>',
      title: 'UI/UX Designer',
      rating: 4.8,
      completed: 89,
      skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      hourlyRate: 75,
      avatar: '🎨'
    },
    {
      id: 3,
      name: '<PERSON>',
      title: 'Smart Contract Developer',
      rating: 5.0,
      completed: 45,
      skills: ['Solidity', 'Move', 'Rust', 'DeFi'],
      hourlyRate: 120,
      avatar: '⚡'
    }
  ];

  const projects = [
    {
      id: 1,
      title: 'E-commerce Website Development',
      budget: '$2,500 - $5,000',
      deadline: '2 weeks',
      skills: ['React', 'Node.js', 'MongoDB'],
      description: 'Build a modern e-commerce platform with payment integration',
      proposals: 12
    },
    {
      id: 2,
      title: 'Mobile App UI Design',
      budget: '$1,200 - $2,000',
      deadline: '1 week',
      skills: ['Figma', 'Mobile Design', 'Prototyping'],
      description: 'Design a clean and intuitive mobile app interface',
      proposals: 8
    }
  ];

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          👨‍💻 Freelancer Ecosystem
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Talented professionals offering their expertise to clients worldwide. 
          Freelancers are the heart of our platform, providing skills and delivering quality work.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Top Freelancers */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">🌟 Top Freelancers</h3>
          <div className="space-y-6">
            {freelancers.map((freelancer) => (
              <div key={freelancer.id} className="card p-6">
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">{freelancer.avatar}</div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-lg font-semibold text-gray-900">
                        {freelancer.name}
                      </h4>
                      <div className="flex items-center space-x-1">
                        <span className="text-yellow-400">⭐</span>
                        <span className="font-medium">{freelancer.rating}</span>
                      </div>
                    </div>
                    <p className="text-blue-600 font-medium mb-2">{freelancer.title}</p>
                    <p className="text-gray-600 text-sm mb-3">
                      {freelancer.completed} projects completed
                    </p>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {freelancer.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-green-600">
                        ${freelancer.hourlyRate}/hr
                      </span>
                      <button className="btn-primary text-sm py-2 px-4">
                        View Profile
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Available Projects */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">💼 Available Projects</h3>
          <div className="space-y-6">
            {projects.map((project) => (
              <div key={project.id} className="card p-6">
                <div className="mb-4">
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    {project.title}
                  </h4>
                  <p className="text-gray-600 text-sm mb-3">
                    {project.description}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {project.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <span>💰 {project.budget}</span>
                  <span>⏰ {project.deadline}</span>
                  <span>📝 {project.proposals} proposals</span>
                </div>
                <button className="btn-success w-full">
                  Submit Proposal
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Freelancer Benefits */}
      <div className="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Why Freelancers Choose Our Platform
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-4xl mb-3">🛡️</div>
            <h4 className="font-semibold text-gray-900 mb-2">Secure Payments</h4>
            <p className="text-gray-600 text-sm">
              Smart contracts ensure you get paid for completed work
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">⚡</div>
            <h4 className="font-semibold text-gray-900 mb-2">Instant Payouts</h4>
            <p className="text-gray-600 text-sm">
              Receive payments immediately upon work approval
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">🌍</div>
            <h4 className="font-semibold text-gray-900 mb-2">Global Reach</h4>
            <p className="text-gray-600 text-sm">
              Work with clients from anywhere in the world
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FreelancerSection;
