{"ast": null, "code": "/**\n * @module BIP32 hierarchical deterministic (HD) wallets over secp256k1.\n * @example\n * ```js\n * import { HDKey } from \"@scure/bip32\";\n * const hdkey1 = HDKey.fromMasterSeed(seed);\n * const hdkey2 = HDKey.fromExtendedKey(base58key);\n * const hdkey3 = HDKey.fromJSON({ xpriv: string });\n *\n * // props\n * [hdkey1.depth, hdkey1.index, hdkey1.chainCode];\n * console.log(hdkey2.privateKey, hdkey2.publicKey);\n * console.log(hdkey3.derive(\"m/0/**********'/1\"));\n * const sig = hdkey3.sign(hash);\n * hdkey3.verify(hash, sig);\n * ```\n */\n/*! scure-bip32 - MIT License (c) 2022 <PERSON><PERSON><PERSON>, <PERSON> (paulmillr.com) */\nimport { mod } from '@noble/curves/abstract/modular';\nimport { secp256k1 as secp } from '@noble/curves/secp256k1';\nimport { hmac } from '@noble/hashes/hmac';\nimport { ripemd160 } from '@noble/hashes/legacy';\nimport { sha256, sha512 } from '@noble/hashes/sha2';\nimport { abytes, bytesToHex, concatBytes, createView, hexToBytes, utf8ToBytes } from '@noble/hashes/utils';\nimport { createBase58check } from '@scure/base';\nconst Point = secp.ProjectivePoint;\nconst base58check = createBase58check(sha256);\nfunction bytesToNumber(bytes) {\n  abytes(bytes);\n  const h = bytes.length === 0 ? '0' : bytesToHex(bytes);\n  return BigInt('0x' + h);\n}\nfunction numberToBytes(num) {\n  if (typeof num !== 'bigint') throw new Error('bigint expected');\n  return hexToBytes(num.toString(16).padStart(64, '0'));\n}\nconst MASTER_SECRET = utf8ToBytes('Bitcoin seed');\n// Bitcoin hardcoded by default\nconst BITCOIN_VERSIONS = {\n  private: 0x0488ade4,\n  public: 0x0488b21e\n};\nexport const HARDENED_OFFSET = 0x80000000;\nconst hash160 = data => ripemd160(sha256(data));\nconst fromU32 = data => createView(data).getUint32(0, false);\nconst toU32 = n => {\n  if (!Number.isSafeInteger(n) || n < 0 || n > 2 ** 32 - 1) {\n    throw new Error('invalid number, should be from 0 to 2**32-1, got ' + n);\n  }\n  const buf = new Uint8Array(4);\n  createView(buf).setUint32(0, n, false);\n  return buf;\n};\nexport class HDKey {\n  get fingerprint() {\n    if (!this.pubHash) {\n      throw new Error('No publicKey set!');\n    }\n    return fromU32(this.pubHash);\n  }\n  get identifier() {\n    return this.pubHash;\n  }\n  get pubKeyHash() {\n    return this.pubHash;\n  }\n  get privateKey() {\n    return this.privKeyBytes || null;\n  }\n  get publicKey() {\n    return this.pubKey || null;\n  }\n  get privateExtendedKey() {\n    const priv = this.privateKey;\n    if (!priv) {\n      throw new Error('No private key');\n    }\n    return base58check.encode(this.serialize(this.versions.private, concatBytes(new Uint8Array([0]), priv)));\n  }\n  get publicExtendedKey() {\n    if (!this.pubKey) {\n      throw new Error('No public key');\n    }\n    return base58check.encode(this.serialize(this.versions.public, this.pubKey));\n  }\n  static fromMasterSeed(seed, versions = BITCOIN_VERSIONS) {\n    abytes(seed);\n    if (8 * seed.length < 128 || 8 * seed.length > 512) {\n      throw new Error('HDKey: seed length must be between 128 and 512 bits; 256 bits is advised, got ' + seed.length);\n    }\n    const I = hmac(sha512, MASTER_SECRET, seed);\n    return new HDKey({\n      versions,\n      chainCode: I.slice(32),\n      privateKey: I.slice(0, 32)\n    });\n  }\n  static fromExtendedKey(base58key, versions = BITCOIN_VERSIONS) {\n    // => version(4) || depth(1) || fingerprint(4) || index(4) || chain(32) || key(33)\n    const keyBuffer = base58check.decode(base58key);\n    const keyView = createView(keyBuffer);\n    const version = keyView.getUint32(0, false);\n    const opt = {\n      versions,\n      depth: keyBuffer[4],\n      parentFingerprint: keyView.getUint32(5, false),\n      index: keyView.getUint32(9, false),\n      chainCode: keyBuffer.slice(13, 45)\n    };\n    const key = keyBuffer.slice(45);\n    const isPriv = key[0] === 0;\n    if (version !== versions[isPriv ? 'private' : 'public']) {\n      throw new Error('Version mismatch');\n    }\n    if (isPriv) {\n      return new HDKey({\n        ...opt,\n        privateKey: key.slice(1)\n      });\n    } else {\n      return new HDKey({\n        ...opt,\n        publicKey: key\n      });\n    }\n  }\n  static fromJSON(json) {\n    return HDKey.fromExtendedKey(json.xpriv);\n  }\n  constructor(opt) {\n    this.depth = 0;\n    this.index = 0;\n    this.chainCode = null;\n    this.parentFingerprint = 0;\n    if (!opt || typeof opt !== 'object') {\n      throw new Error('HDKey.constructor must not be called directly');\n    }\n    this.versions = opt.versions || BITCOIN_VERSIONS;\n    this.depth = opt.depth || 0;\n    this.chainCode = opt.chainCode || null;\n    this.index = opt.index || 0;\n    this.parentFingerprint = opt.parentFingerprint || 0;\n    if (!this.depth) {\n      if (this.parentFingerprint || this.index) {\n        throw new Error('HDKey: zero depth with non-zero index/parent fingerprint');\n      }\n    }\n    if (opt.publicKey && opt.privateKey) {\n      throw new Error('HDKey: publicKey and privateKey at same time.');\n    }\n    if (opt.privateKey) {\n      if (!secp.utils.isValidPrivateKey(opt.privateKey)) {\n        throw new Error('Invalid private key');\n      }\n      this.privKey = typeof opt.privateKey === 'bigint' ? opt.privateKey : bytesToNumber(opt.privateKey);\n      this.privKeyBytes = numberToBytes(this.privKey);\n      this.pubKey = secp.getPublicKey(opt.privateKey, true);\n    } else if (opt.publicKey) {\n      this.pubKey = Point.fromHex(opt.publicKey).toRawBytes(true); // force compressed point\n    } else {\n      throw new Error('HDKey: no public or private key provided');\n    }\n    this.pubHash = hash160(this.pubKey);\n  }\n  derive(path) {\n    if (!/^[mM]'?/.test(path)) {\n      throw new Error('Path must start with \"m\" or \"M\"');\n    }\n    if (/^[mM]'?$/.test(path)) {\n      return this;\n    }\n    const parts = path.replace(/^[mM]'?\\//, '').split('/');\n    // tslint:disable-next-line\n    let child = this;\n    for (const c of parts) {\n      const m = /^(\\d+)('?)$/.exec(c);\n      const m1 = m && m[1];\n      if (!m || m.length !== 3 || typeof m1 !== 'string') throw new Error('invalid child index: ' + c);\n      let idx = +m1;\n      if (!Number.isSafeInteger(idx) || idx >= HARDENED_OFFSET) {\n        throw new Error('Invalid index');\n      }\n      // hardened key\n      if (m[2] === \"'\") {\n        idx += HARDENED_OFFSET;\n      }\n      child = child.deriveChild(idx);\n    }\n    return child;\n  }\n  deriveChild(index) {\n    if (!this.pubKey || !this.chainCode) {\n      throw new Error('No publicKey or chainCode set');\n    }\n    let data = toU32(index);\n    if (index >= HARDENED_OFFSET) {\n      // Hardened\n      const priv = this.privateKey;\n      if (!priv) {\n        throw new Error('Could not derive hardened child key');\n      }\n      // Hardened child: 0x00 || ser256(kpar) || ser32(index)\n      data = concatBytes(new Uint8Array([0]), priv, data);\n    } else {\n      // Normal child: serP(point(kpar)) || ser32(index)\n      data = concatBytes(this.pubKey, data);\n    }\n    const I = hmac(sha512, this.chainCode, data);\n    const childTweak = bytesToNumber(I.slice(0, 32));\n    const chainCode = I.slice(32);\n    if (!secp.utils.isValidPrivateKey(childTweak)) {\n      throw new Error('Tweak bigger than curve order');\n    }\n    const opt = {\n      versions: this.versions,\n      chainCode,\n      depth: this.depth + 1,\n      parentFingerprint: this.fingerprint,\n      index\n    };\n    try {\n      // Private parent key -> private child key\n      if (this.privateKey) {\n        const added = mod(this.privKey + childTweak, secp.CURVE.n);\n        if (!secp.utils.isValidPrivateKey(added)) {\n          throw new Error('The tweak was out of range or the resulted private key is invalid');\n        }\n        opt.privateKey = added;\n      } else {\n        const added = Point.fromHex(this.pubKey).add(Point.fromPrivateKey(childTweak));\n        // Cryptographically impossible: hmac-sha512 preimage would need to be found\n        if (added.equals(Point.ZERO)) {\n          throw new Error('The tweak was equal to negative P, which made the result key invalid');\n        }\n        opt.publicKey = added.toRawBytes(true);\n      }\n      return new HDKey(opt);\n    } catch (err) {\n      return this.deriveChild(index + 1);\n    }\n  }\n  sign(hash) {\n    if (!this.privateKey) {\n      throw new Error('No privateKey set!');\n    }\n    abytes(hash, 32);\n    return secp.sign(hash, this.privKey).toCompactRawBytes();\n  }\n  verify(hash, signature) {\n    abytes(hash, 32);\n    abytes(signature, 64);\n    if (!this.publicKey) {\n      throw new Error('No publicKey set!');\n    }\n    let sig;\n    try {\n      sig = secp.Signature.fromCompact(signature);\n    } catch (error) {\n      return false;\n    }\n    return secp.verify(sig, hash, this.publicKey);\n  }\n  wipePrivateData() {\n    this.privKey = undefined;\n    if (this.privKeyBytes) {\n      this.privKeyBytes.fill(0);\n      this.privKeyBytes = undefined;\n    }\n    return this;\n  }\n  toJSON() {\n    return {\n      xpriv: this.privateExtendedKey,\n      xpub: this.publicExtendedKey\n    };\n  }\n  serialize(version, key) {\n    if (!this.chainCode) {\n      throw new Error('No chainCode set');\n    }\n    abytes(key, 33);\n    // version(4) || depth(1) || fingerprint(4) || index(4) || chain(32) || key(33)\n    return concatBytes(toU32(version), new Uint8Array([this.depth]), toU32(this.parentFingerprint), toU32(this.index), this.chainCode, key);\n  }\n}", "map": {"version": 3, "names": ["mod", "secp256k1", "secp", "hmac", "ripemd160", "sha256", "sha512", "abytes", "bytesToHex", "concatBytes", "createView", "hexToBytes", "utf8ToBytes", "createBase58check", "Point", "ProjectivePoint", "base58check", "bytesToNumber", "bytes", "h", "length", "BigInt", "numberToBytes", "num", "Error", "toString", "padStart", "MASTER_SECRET", "BITCOIN_VERSIONS", "private", "public", "HARDENED_OFFSET", "hash160", "data", "fromU32", "getUint32", "toU32", "n", "Number", "isSafeInteger", "buf", "Uint8Array", "setUint32", "HDKey", "fingerprint", "pubHash", "identifier", "pubKeyHash", "privateKey", "privKeyBytes", "public<PERSON>ey", "pubKey", "privateExtendedKey", "priv", "encode", "serialize", "versions", "publicExtendedKey", "fromMasterSeed", "seed", "I", "chainCode", "slice", "fromExtendedKey", "base58key", "key<PERSON><PERSON>er", "decode", "<PERSON><PERSON><PERSON><PERSON>", "version", "opt", "depth", "parentFingerprint", "index", "key", "isPriv", "fromJSON", "json", "xpriv", "constructor", "utils", "isValidPrivateKey", "privKey", "getPublicKey", "fromHex", "toRawBytes", "derive", "path", "test", "parts", "replace", "split", "child", "c", "m", "exec", "m1", "idx", "<PERSON><PERSON><PERSON><PERSON>", "childTweak", "added", "CURVE", "add", "fromPrivateKey", "equals", "ZERO", "err", "sign", "hash", "toCompactRawBytes", "verify", "signature", "sig", "Signature", "fromCompact", "error", "wipePrivateData", "undefined", "fill", "toJSON", "xpub"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@scure\\bip32\\index.ts"], "sourcesContent": ["/**\n * @module BIP32 hierarchical deterministic (HD) wallets over secp256k1.\n * @example\n * ```js\n * import { HDKey } from \"@scure/bip32\";\n * const hdkey1 = HDKey.fromMasterSeed(seed);\n * const hdkey2 = HDKey.fromExtendedKey(base58key);\n * const hdkey3 = HDKey.fromJSON({ xpriv: string });\n *\n * // props\n * [hdkey1.depth, hdkey1.index, hdkey1.chainCode];\n * console.log(hdkey2.privateKey, hdkey2.publicKey);\n * console.log(hdkey3.derive(\"m/0/**********'/1\"));\n * const sig = hdkey3.sign(hash);\n * hdkey3.verify(hash, sig);\n * ```\n */\n/*! scure-bip32 - MIT License (c) 2022 <PERSON><PERSON><PERSON>, <PERSON> (paulmillr.com) */\nimport { mod } from '@noble/curves/abstract/modular';\nimport { secp256k1 as secp } from '@noble/curves/secp256k1';\nimport { hmac } from '@noble/hashes/hmac';\nimport { ripemd160 } from '@noble/hashes/legacy';\nimport { sha256, sha512 } from '@noble/hashes/sha2';\nimport {\n  abytes,\n  bytesToHex,\n  concatBytes,\n  createView,\n  hexToBytes,\n  utf8ToBytes,\n} from '@noble/hashes/utils';\nimport { createBase58check } from '@scure/base';\n\nconst Point = secp.ProjectivePoint;\nconst base58check = createBase58check(sha256);\n\nfunction bytesToNumber(bytes: Uint8Array): bigint {\n  abytes(bytes);\n  const h = bytes.length === 0 ? '0' : bytesToHex(bytes);\n  return BigInt('0x' + h);\n}\n\nfunction numberToBytes(num: bigint): Uint8Array {\n  if (typeof num !== 'bigint') throw new Error('bigint expected');\n  return hexToBytes(num.toString(16).padStart(64, '0'));\n}\n\nconst MASTER_SECRET = utf8ToBytes('Bitcoin seed');\n// Bitcoin hardcoded by default\nconst BITCOIN_VERSIONS: Versions = { private: 0x0488ade4, public: 0x0488b21e };\nexport const HARDENED_OFFSET: number = 0x80000000;\n\nexport interface Versions {\n  private: number;\n  public: number;\n}\n\nconst hash160 = (data: Uint8Array) => ripemd160(sha256(data));\nconst fromU32 = (data: Uint8Array) => createView(data).getUint32(0, false);\nconst toU32 = (n: number) => {\n  if (!Number.isSafeInteger(n) || n < 0 || n > 2 ** 32 - 1) {\n    throw new Error('invalid number, should be from 0 to 2**32-1, got ' + n);\n  }\n  const buf = new Uint8Array(4);\n  createView(buf).setUint32(0, n, false);\n  return buf;\n};\n\ninterface HDKeyOpt {\n  versions?: Versions;\n  depth?: number;\n  index?: number;\n  parentFingerprint?: number;\n  chainCode?: Uint8Array;\n  publicKey?: Uint8Array;\n  privateKey?: Uint8Array | bigint;\n}\n\nexport class HDKey {\n  get fingerprint(): number {\n    if (!this.pubHash) {\n      throw new Error('No publicKey set!');\n    }\n    return fromU32(this.pubHash);\n  }\n  get identifier(): Uint8Array | undefined {\n    return this.pubHash;\n  }\n  get pubKeyHash(): Uint8Array | undefined {\n    return this.pubHash;\n  }\n  get privateKey(): Uint8Array | null {\n    return this.privKeyBytes || null;\n  }\n  get publicKey(): Uint8Array | null {\n    return this.pubKey || null;\n  }\n  get privateExtendedKey(): string {\n    const priv = this.privateKey;\n    if (!priv) {\n      throw new Error('No private key');\n    }\n    return base58check.encode(\n      this.serialize(this.versions.private, concatBytes(new Uint8Array([0]), priv))\n    );\n  }\n  get publicExtendedKey(): string {\n    if (!this.pubKey) {\n      throw new Error('No public key');\n    }\n    return base58check.encode(this.serialize(this.versions.public, this.pubKey));\n  }\n\n  public static fromMasterSeed(seed: Uint8Array, versions: Versions = BITCOIN_VERSIONS): HDKey {\n    abytes(seed);\n    if (8 * seed.length < 128 || 8 * seed.length > 512) {\n      throw new Error(\n        'HDKey: seed length must be between 128 and 512 bits; 256 bits is advised, got ' +\n          seed.length\n      );\n    }\n    const I = hmac(sha512, MASTER_SECRET, seed);\n    return new HDKey({\n      versions,\n      chainCode: I.slice(32),\n      privateKey: I.slice(0, 32),\n    });\n  }\n\n  public static fromExtendedKey(base58key: string, versions: Versions = BITCOIN_VERSIONS): HDKey {\n    // => version(4) || depth(1) || fingerprint(4) || index(4) || chain(32) || key(33)\n    const keyBuffer: Uint8Array = base58check.decode(base58key);\n    const keyView = createView(keyBuffer);\n    const version = keyView.getUint32(0, false);\n    const opt = {\n      versions,\n      depth: keyBuffer[4],\n      parentFingerprint: keyView.getUint32(5, false),\n      index: keyView.getUint32(9, false),\n      chainCode: keyBuffer.slice(13, 45),\n    };\n    const key = keyBuffer.slice(45);\n    const isPriv = key[0] === 0;\n    if (version !== versions[isPriv ? 'private' : 'public']) {\n      throw new Error('Version mismatch');\n    }\n    if (isPriv) {\n      return new HDKey({ ...opt, privateKey: key.slice(1) });\n    } else {\n      return new HDKey({ ...opt, publicKey: key });\n    }\n  }\n\n  public static fromJSON(json: { xpriv: string }): HDKey {\n    return HDKey.fromExtendedKey(json.xpriv);\n  }\n  public readonly versions: Versions;\n  public readonly depth: number = 0;\n  public readonly index: number = 0;\n  public readonly chainCode: Uint8Array | null = null;\n  public readonly parentFingerprint: number = 0;\n  private privKey?: bigint;\n  private privKeyBytes?: Uint8Array;\n  private pubKey?: Uint8Array;\n  private pubHash: Uint8Array | undefined;\n\n  constructor(opt: HDKeyOpt) {\n    if (!opt || typeof opt !== 'object') {\n      throw new Error('HDKey.constructor must not be called directly');\n    }\n    this.versions = opt.versions || BITCOIN_VERSIONS;\n    this.depth = opt.depth || 0;\n    this.chainCode = opt.chainCode || null;\n    this.index = opt.index || 0;\n    this.parentFingerprint = opt.parentFingerprint || 0;\n    if (!this.depth) {\n      if (this.parentFingerprint || this.index) {\n        throw new Error('HDKey: zero depth with non-zero index/parent fingerprint');\n      }\n    }\n    if (opt.publicKey && opt.privateKey) {\n      throw new Error('HDKey: publicKey and privateKey at same time.');\n    }\n    if (opt.privateKey) {\n      if (!secp.utils.isValidPrivateKey(opt.privateKey)) {\n        throw new Error('Invalid private key');\n      }\n      this.privKey =\n        typeof opt.privateKey === 'bigint' ? opt.privateKey : bytesToNumber(opt.privateKey);\n      this.privKeyBytes = numberToBytes(this.privKey);\n      this.pubKey = secp.getPublicKey(opt.privateKey, true);\n    } else if (opt.publicKey) {\n      this.pubKey = Point.fromHex(opt.publicKey).toRawBytes(true); // force compressed point\n    } else {\n      throw new Error('HDKey: no public or private key provided');\n    }\n    this.pubHash = hash160(this.pubKey);\n  }\n\n  public derive(path: string): HDKey {\n    if (!/^[mM]'?/.test(path)) {\n      throw new Error('Path must start with \"m\" or \"M\"');\n    }\n    if (/^[mM]'?$/.test(path)) {\n      return this;\n    }\n    const parts = path.replace(/^[mM]'?\\//, '').split('/');\n    // tslint:disable-next-line\n    let child: HDKey = this;\n    for (const c of parts) {\n      const m = /^(\\d+)('?)$/.exec(c);\n      const m1 = m && m[1];\n      if (!m || m.length !== 3 || typeof m1 !== 'string')\n        throw new Error('invalid child index: ' + c);\n      let idx = +m1;\n      if (!Number.isSafeInteger(idx) || idx >= HARDENED_OFFSET) {\n        throw new Error('Invalid index');\n      }\n      // hardened key\n      if (m[2] === \"'\") {\n        idx += HARDENED_OFFSET;\n      }\n      child = child.deriveChild(idx);\n    }\n    return child;\n  }\n\n  public deriveChild(index: number): HDKey {\n    if (!this.pubKey || !this.chainCode) {\n      throw new Error('No publicKey or chainCode set');\n    }\n    let data = toU32(index);\n    if (index >= HARDENED_OFFSET) {\n      // Hardened\n      const priv = this.privateKey;\n      if (!priv) {\n        throw new Error('Could not derive hardened child key');\n      }\n      // Hardened child: 0x00 || ser256(kpar) || ser32(index)\n      data = concatBytes(new Uint8Array([0]), priv, data);\n    } else {\n      // Normal child: serP(point(kpar)) || ser32(index)\n      data = concatBytes(this.pubKey, data);\n    }\n    const I = hmac(sha512, this.chainCode, data);\n    const childTweak = bytesToNumber(I.slice(0, 32));\n    const chainCode = I.slice(32);\n    if (!secp.utils.isValidPrivateKey(childTweak)) {\n      throw new Error('Tweak bigger than curve order');\n    }\n    const opt: HDKeyOpt = {\n      versions: this.versions,\n      chainCode,\n      depth: this.depth + 1,\n      parentFingerprint: this.fingerprint,\n      index,\n    };\n    try {\n      // Private parent key -> private child key\n      if (this.privateKey) {\n        const added = mod(this.privKey! + childTweak, secp.CURVE.n);\n        if (!secp.utils.isValidPrivateKey(added)) {\n          throw new Error('The tweak was out of range or the resulted private key is invalid');\n        }\n        opt.privateKey = added;\n      } else {\n        const added = Point.fromHex(this.pubKey).add(Point.fromPrivateKey(childTweak));\n        // Cryptographically impossible: hmac-sha512 preimage would need to be found\n        if (added.equals(Point.ZERO)) {\n          throw new Error('The tweak was equal to negative P, which made the result key invalid');\n        }\n        opt.publicKey = added.toRawBytes(true);\n      }\n      return new HDKey(opt);\n    } catch (err) {\n      return this.deriveChild(index + 1);\n    }\n  }\n\n  public sign(hash: Uint8Array): Uint8Array {\n    if (!this.privateKey) {\n      throw new Error('No privateKey set!');\n    }\n    abytes(hash, 32);\n    return secp.sign(hash, this.privKey!).toCompactRawBytes();\n  }\n\n  public verify(hash: Uint8Array, signature: Uint8Array): boolean {\n    abytes(hash, 32);\n    abytes(signature, 64);\n    if (!this.publicKey) {\n      throw new Error('No publicKey set!');\n    }\n    let sig;\n    try {\n      sig = secp.Signature.fromCompact(signature);\n    } catch (error) {\n      return false;\n    }\n    return secp.verify(sig, hash, this.publicKey);\n  }\n\n  public wipePrivateData(): this {\n    this.privKey = undefined;\n    if (this.privKeyBytes) {\n      this.privKeyBytes.fill(0);\n      this.privKeyBytes = undefined;\n    }\n    return this;\n  }\n  public toJSON(): { xpriv: string; xpub: string } {\n    return {\n      xpriv: this.privateExtendedKey,\n      xpub: this.publicExtendedKey,\n    };\n  }\n\n  private serialize(version: number, key: Uint8Array) {\n    if (!this.chainCode) {\n      throw new Error('No chainCode set');\n    }\n    abytes(key, 33);\n    // version(4) || depth(1) || fingerprint(4) || index(4) || chain(32) || key(33)\n    return concatBytes(\n      toU32(version),\n      new Uint8Array([this.depth]),\n      toU32(this.parentFingerprint),\n      toU32(this.index),\n      this.chainCode,\n      key\n    );\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAiBA;AACA,SAASA,GAAG,QAAQ,gCAAgC;AACpD,SAASC,SAAS,IAAIC,IAAI,QAAQ,yBAAyB;AAC3D,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,EAAEC,MAAM,QAAQ,oBAAoB;AACnD,SACEC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,qBAAqB;AAC5B,SAASC,iBAAiB,QAAQ,aAAa;AAE/C,MAAMC,KAAK,GAAGZ,IAAI,CAACa,eAAe;AAClC,MAAMC,WAAW,GAAGH,iBAAiB,CAACR,MAAM,CAAC;AAE7C,SAASY,aAAaA,CAACC,KAAiB;EACtCX,MAAM,CAACW,KAAK,CAAC;EACb,MAAMC,CAAC,GAAGD,KAAK,CAACE,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGZ,UAAU,CAACU,KAAK,CAAC;EACtD,OAAOG,MAAM,CAAC,IAAI,GAAGF,CAAC,CAAC;AACzB;AAEA,SAASG,aAAaA,CAACC,GAAW;EAChC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EAC/D,OAAOb,UAAU,CAACY,GAAG,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AACvD;AAEA,MAAMC,aAAa,GAAGf,WAAW,CAAC,cAAc,CAAC;AACjD;AACA,MAAMgB,gBAAgB,GAAa;EAAEC,OAAO,EAAE,UAAU;EAAEC,MAAM,EAAE;AAAU,CAAE;AAC9E,OAAO,MAAMC,eAAe,GAAW,UAAU;AAOjD,MAAMC,OAAO,GAAIC,IAAgB,IAAK7B,SAAS,CAACC,MAAM,CAAC4B,IAAI,CAAC,CAAC;AAC7D,MAAMC,OAAO,GAAID,IAAgB,IAAKvB,UAAU,CAACuB,IAAI,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;AAC1E,MAAMC,KAAK,GAAIC,CAAS,IAAI;EAC1B,IAAI,CAACC,MAAM,CAACC,aAAa,CAACF,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;IACxD,MAAM,IAAIb,KAAK,CAAC,mDAAmD,GAAGa,CAAC,CAAC;EAC1E;EACA,MAAMG,GAAG,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;EAC7B/B,UAAU,CAAC8B,GAAG,CAAC,CAACE,SAAS,CAAC,CAAC,EAAEL,CAAC,EAAE,KAAK,CAAC;EACtC,OAAOG,GAAG;AACZ,CAAC;AAYD,OAAM,MAAOG,KAAK;EAChB,IAAIC,WAAWA,CAAA;IACb,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACjB,MAAM,IAAIrB,KAAK,CAAC,mBAAmB,CAAC;IACtC;IACA,OAAOU,OAAO,CAAC,IAAI,CAACW,OAAO,CAAC;EAC9B;EACA,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACD,OAAO;EACrB;EACA,IAAIE,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACF,OAAO;EACrB;EACA,IAAIG,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI;EAClC;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI;EAC5B;EACA,IAAIC,kBAAkBA,CAAA;IACpB,MAAMC,IAAI,GAAG,IAAI,CAACL,UAAU;IAC5B,IAAI,CAACK,IAAI,EAAE;MACT,MAAM,IAAI7B,KAAK,CAAC,gBAAgB,CAAC;IACnC;IACA,OAAOR,WAAW,CAACsC,MAAM,CACvB,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC3B,OAAO,EAAEpB,WAAW,CAAC,IAAIgC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEY,IAAI,CAAC,CAAC,CAC9E;EACH;EACA,IAAII,iBAAiBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACN,MAAM,EAAE;MAChB,MAAM,IAAI3B,KAAK,CAAC,eAAe,CAAC;IAClC;IACA,OAAOR,WAAW,CAACsC,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC1B,MAAM,EAAE,IAAI,CAACqB,MAAM,CAAC,CAAC;EAC9E;EAEO,OAAOO,cAAcA,CAACC,IAAgB,EAAEH,QAAA,GAAqB5B,gBAAgB;IAClFrB,MAAM,CAACoD,IAAI,CAAC;IACZ,IAAI,CAAC,GAAGA,IAAI,CAACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAGuC,IAAI,CAACvC,MAAM,GAAG,GAAG,EAAE;MAClD,MAAM,IAAII,KAAK,CACb,gFAAgF,GAC9EmC,IAAI,CAACvC,MAAM,CACd;IACH;IACA,MAAMwC,CAAC,GAAGzD,IAAI,CAACG,MAAM,EAAEqB,aAAa,EAAEgC,IAAI,CAAC;IAC3C,OAAO,IAAIhB,KAAK,CAAC;MACfa,QAAQ;MACRK,SAAS,EAAED,CAAC,CAACE,KAAK,CAAC,EAAE,CAAC;MACtBd,UAAU,EAAEY,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE;KAC1B,CAAC;EACJ;EAEO,OAAOC,eAAeA,CAACC,SAAiB,EAAER,QAAA,GAAqB5B,gBAAgB;IACpF;IACA,MAAMqC,SAAS,GAAejD,WAAW,CAACkD,MAAM,CAACF,SAAS,CAAC;IAC3D,MAAMG,OAAO,GAAGzD,UAAU,CAACuD,SAAS,CAAC;IACrC,MAAMG,OAAO,GAAGD,OAAO,CAAChC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;IAC3C,MAAMkC,GAAG,GAAG;MACVb,QAAQ;MACRc,KAAK,EAAEL,SAAS,CAAC,CAAC,CAAC;MACnBM,iBAAiB,EAAEJ,OAAO,CAAChC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;MAC9CqC,KAAK,EAAEL,OAAO,CAAChC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;MAClC0B,SAAS,EAAEI,SAAS,CAACH,KAAK,CAAC,EAAE,EAAE,EAAE;KAClC;IACD,MAAMW,GAAG,GAAGR,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IAC/B,MAAMY,MAAM,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3B,IAAIL,OAAO,KAAKZ,QAAQ,CAACkB,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;MACvD,MAAM,IAAIlD,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,IAAIkD,MAAM,EAAE;MACV,OAAO,IAAI/B,KAAK,CAAC;QAAE,GAAG0B,GAAG;QAAErB,UAAU,EAAEyB,GAAG,CAACX,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;IACxD,CAAC,MAAM;MACL,OAAO,IAAInB,KAAK,CAAC;QAAE,GAAG0B,GAAG;QAAEnB,SAAS,EAAEuB;MAAG,CAAE,CAAC;IAC9C;EACF;EAEO,OAAOE,QAAQA,CAACC,IAAuB;IAC5C,OAAOjC,KAAK,CAACoB,eAAe,CAACa,IAAI,CAACC,KAAK,CAAC;EAC1C;EAWAC,YAAYT,GAAa;IATT,KAAAC,KAAK,GAAW,CAAC;IACjB,KAAAE,KAAK,GAAW,CAAC;IACjB,KAAAX,SAAS,GAAsB,IAAI;IACnC,KAAAU,iBAAiB,GAAW,CAAC;IAO3C,IAAI,CAACF,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACnC,MAAM,IAAI7C,KAAK,CAAC,+CAA+C,CAAC;IAClE;IACA,IAAI,CAACgC,QAAQ,GAAGa,GAAG,CAACb,QAAQ,IAAI5B,gBAAgB;IAChD,IAAI,CAAC0C,KAAK,GAAGD,GAAG,CAACC,KAAK,IAAI,CAAC;IAC3B,IAAI,CAACT,SAAS,GAAGQ,GAAG,CAACR,SAAS,IAAI,IAAI;IACtC,IAAI,CAACW,KAAK,GAAGH,GAAG,CAACG,KAAK,IAAI,CAAC;IAC3B,IAAI,CAACD,iBAAiB,GAAGF,GAAG,CAACE,iBAAiB,IAAI,CAAC;IACnD,IAAI,CAAC,IAAI,CAACD,KAAK,EAAE;MACf,IAAI,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAACC,KAAK,EAAE;QACxC,MAAM,IAAIhD,KAAK,CAAC,0DAA0D,CAAC;MAC7E;IACF;IACA,IAAI6C,GAAG,CAACnB,SAAS,IAAImB,GAAG,CAACrB,UAAU,EAAE;MACnC,MAAM,IAAIxB,KAAK,CAAC,+CAA+C,CAAC;IAClE;IACA,IAAI6C,GAAG,CAACrB,UAAU,EAAE;MAClB,IAAI,CAAC9C,IAAI,CAAC6E,KAAK,CAACC,iBAAiB,CAACX,GAAG,CAACrB,UAAU,CAAC,EAAE;QACjD,MAAM,IAAIxB,KAAK,CAAC,qBAAqB,CAAC;MACxC;MACA,IAAI,CAACyD,OAAO,GACV,OAAOZ,GAAG,CAACrB,UAAU,KAAK,QAAQ,GAAGqB,GAAG,CAACrB,UAAU,GAAG/B,aAAa,CAACoD,GAAG,CAACrB,UAAU,CAAC;MACrF,IAAI,CAACC,YAAY,GAAG3B,aAAa,CAAC,IAAI,CAAC2D,OAAO,CAAC;MAC/C,IAAI,CAAC9B,MAAM,GAAGjD,IAAI,CAACgF,YAAY,CAACb,GAAG,CAACrB,UAAU,EAAE,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIqB,GAAG,CAACnB,SAAS,EAAE;MACxB,IAAI,CAACC,MAAM,GAAGrC,KAAK,CAACqE,OAAO,CAACd,GAAG,CAACnB,SAAS,CAAC,CAACkC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,MAAM,IAAI5D,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IACA,IAAI,CAACqB,OAAO,GAAGb,OAAO,CAAC,IAAI,CAACmB,MAAM,CAAC;EACrC;EAEOkC,MAAMA,CAACC,IAAY;IACxB,IAAI,CAAC,SAAS,CAACC,IAAI,CAACD,IAAI,CAAC,EAAE;MACzB,MAAM,IAAI9D,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,IAAI,UAAU,CAAC+D,IAAI,CAACD,IAAI,CAAC,EAAE;MACzB,OAAO,IAAI;IACb;IACA,MAAME,KAAK,GAAGF,IAAI,CAACG,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACtD;IACA,IAAIC,KAAK,GAAU,IAAI;IACvB,KAAK,MAAMC,CAAC,IAAIJ,KAAK,EAAE;MACrB,MAAMK,CAAC,GAAG,aAAa,CAACC,IAAI,CAACF,CAAC,CAAC;MAC/B,MAAMG,EAAE,GAAGF,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;MACpB,IAAI,CAACA,CAAC,IAAIA,CAAC,CAACzE,MAAM,KAAK,CAAC,IAAI,OAAO2E,EAAE,KAAK,QAAQ,EAChD,MAAM,IAAIvE,KAAK,CAAC,uBAAuB,GAAGoE,CAAC,CAAC;MAC9C,IAAII,GAAG,GAAG,CAACD,EAAE;MACb,IAAI,CAACzD,MAAM,CAACC,aAAa,CAACyD,GAAG,CAAC,IAAIA,GAAG,IAAIjE,eAAe,EAAE;QACxD,MAAM,IAAIP,KAAK,CAAC,eAAe,CAAC;MAClC;MACA;MACA,IAAIqE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChBG,GAAG,IAAIjE,eAAe;MACxB;MACA4D,KAAK,GAAGA,KAAK,CAACM,WAAW,CAACD,GAAG,CAAC;IAChC;IACA,OAAOL,KAAK;EACd;EAEOM,WAAWA,CAACzB,KAAa;IAC9B,IAAI,CAAC,IAAI,CAACrB,MAAM,IAAI,CAAC,IAAI,CAACU,SAAS,EAAE;MACnC,MAAM,IAAIrC,KAAK,CAAC,+BAA+B,CAAC;IAClD;IACA,IAAIS,IAAI,GAAGG,KAAK,CAACoC,KAAK,CAAC;IACvB,IAAIA,KAAK,IAAIzC,eAAe,EAAE;MAC5B;MACA,MAAMsB,IAAI,GAAG,IAAI,CAACL,UAAU;MAC5B,IAAI,CAACK,IAAI,EAAE;QACT,MAAM,IAAI7B,KAAK,CAAC,qCAAqC,CAAC;MACxD;MACA;MACAS,IAAI,GAAGxB,WAAW,CAAC,IAAIgC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEY,IAAI,EAAEpB,IAAI,CAAC;IACrD,CAAC,MAAM;MACL;MACAA,IAAI,GAAGxB,WAAW,CAAC,IAAI,CAAC0C,MAAM,EAAElB,IAAI,CAAC;IACvC;IACA,MAAM2B,CAAC,GAAGzD,IAAI,CAACG,MAAM,EAAE,IAAI,CAACuD,SAAS,EAAE5B,IAAI,CAAC;IAC5C,MAAMiE,UAAU,GAAGjF,aAAa,CAAC2C,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAChD,MAAMD,SAAS,GAAGD,CAAC,CAACE,KAAK,CAAC,EAAE,CAAC;IAC7B,IAAI,CAAC5D,IAAI,CAAC6E,KAAK,CAACC,iBAAiB,CAACkB,UAAU,CAAC,EAAE;MAC7C,MAAM,IAAI1E,KAAK,CAAC,+BAA+B,CAAC;IAClD;IACA,MAAM6C,GAAG,GAAa;MACpBb,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBK,SAAS;MACTS,KAAK,EAAE,IAAI,CAACA,KAAK,GAAG,CAAC;MACrBC,iBAAiB,EAAE,IAAI,CAAC3B,WAAW;MACnC4B;KACD;IACD,IAAI;MACF;MACA,IAAI,IAAI,CAACxB,UAAU,EAAE;QACnB,MAAMmD,KAAK,GAAGnG,GAAG,CAAC,IAAI,CAACiF,OAAQ,GAAGiB,UAAU,EAAEhG,IAAI,CAACkG,KAAK,CAAC/D,CAAC,CAAC;QAC3D,IAAI,CAACnC,IAAI,CAAC6E,KAAK,CAACC,iBAAiB,CAACmB,KAAK,CAAC,EAAE;UACxC,MAAM,IAAI3E,KAAK,CAAC,mEAAmE,CAAC;QACtF;QACA6C,GAAG,CAACrB,UAAU,GAAGmD,KAAK;MACxB,CAAC,MAAM;QACL,MAAMA,KAAK,GAAGrF,KAAK,CAACqE,OAAO,CAAC,IAAI,CAAChC,MAAM,CAAC,CAACkD,GAAG,CAACvF,KAAK,CAACwF,cAAc,CAACJ,UAAU,CAAC,CAAC;QAC9E;QACA,IAAIC,KAAK,CAACI,MAAM,CAACzF,KAAK,CAAC0F,IAAI,CAAC,EAAE;UAC5B,MAAM,IAAIhF,KAAK,CAAC,sEAAsE,CAAC;QACzF;QACA6C,GAAG,CAACnB,SAAS,GAAGiD,KAAK,CAACf,UAAU,CAAC,IAAI,CAAC;MACxC;MACA,OAAO,IAAIzC,KAAK,CAAC0B,GAAG,CAAC;IACvB,CAAC,CAAC,OAAOoC,GAAG,EAAE;MACZ,OAAO,IAAI,CAACR,WAAW,CAACzB,KAAK,GAAG,CAAC,CAAC;IACpC;EACF;EAEOkC,IAAIA,CAACC,IAAgB;IAC1B,IAAI,CAAC,IAAI,CAAC3D,UAAU,EAAE;MACpB,MAAM,IAAIxB,KAAK,CAAC,oBAAoB,CAAC;IACvC;IACAjB,MAAM,CAACoG,IAAI,EAAE,EAAE,CAAC;IAChB,OAAOzG,IAAI,CAACwG,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC1B,OAAQ,CAAC,CAAC2B,iBAAiB,EAAE;EAC3D;EAEOC,MAAMA,CAACF,IAAgB,EAAEG,SAAqB;IACnDvG,MAAM,CAACoG,IAAI,EAAE,EAAE,CAAC;IAChBpG,MAAM,CAACuG,SAAS,EAAE,EAAE,CAAC;IACrB,IAAI,CAAC,IAAI,CAAC5D,SAAS,EAAE;MACnB,MAAM,IAAI1B,KAAK,CAAC,mBAAmB,CAAC;IACtC;IACA,IAAIuF,GAAG;IACP,IAAI;MACFA,GAAG,GAAG7G,IAAI,CAAC8G,SAAS,CAACC,WAAW,CAACH,SAAS,CAAC;IAC7C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO,KAAK;IACd;IACA,OAAOhH,IAAI,CAAC2G,MAAM,CAACE,GAAG,EAAEJ,IAAI,EAAE,IAAI,CAACzD,SAAS,CAAC;EAC/C;EAEOiE,eAAeA,CAAA;IACpB,IAAI,CAAClC,OAAO,GAAGmC,SAAS;IACxB,IAAI,IAAI,CAACnE,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACoE,IAAI,CAAC,CAAC,CAAC;MACzB,IAAI,CAACpE,YAAY,GAAGmE,SAAS;IAC/B;IACA,OAAO,IAAI;EACb;EACOE,MAAMA,CAAA;IACX,OAAO;MACLzC,KAAK,EAAE,IAAI,CAACzB,kBAAkB;MAC9BmE,IAAI,EAAE,IAAI,CAAC9D;KACZ;EACH;EAEQF,SAASA,CAACa,OAAe,EAAEK,GAAe;IAChD,IAAI,CAAC,IAAI,CAACZ,SAAS,EAAE;MACnB,MAAM,IAAIrC,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACAjB,MAAM,CAACkE,GAAG,EAAE,EAAE,CAAC;IACf;IACA,OAAOhE,WAAW,CAChB2B,KAAK,CAACgC,OAAO,CAAC,EACd,IAAI3B,UAAU,CAAC,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAAC,EAC5BlC,KAAK,CAAC,IAAI,CAACmC,iBAAiB,CAAC,EAC7BnC,KAAK,CAAC,IAAI,CAACoC,KAAK,CAAC,EACjB,IAAI,CAACX,SAAS,EACdY,GAAG,CACJ;EACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}