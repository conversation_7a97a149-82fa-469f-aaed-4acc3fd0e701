{"ast": null, "code": "/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNA<PERSON>, pippenger.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { bitLen, bitMask, validateObject } from \"../utils.js\";\nimport { Field, FpInvertBatch, nLength, validateField } from \"./modular.js\";\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nexport function negateCt(condition, item) {\n  const neg = item.negate();\n  return condition ? neg : item;\n}\n/**\n * Takes a bunch of Projective Points but executes only one\n * inversion on all of them. Inversion is very slow operation,\n * so this improves performance massively.\n * Optimization: converts a list of projective points to a list of identical points with Z=1.\n */\nexport function normalizeZ(c, points) {\n  const invertedZs = FpInvertBatch(c.Fp, points.map(p => p.Z));\n  return points.map((p, i) => c.fromAffine(p.toAffine(invertedZs[i])));\n}\nfunction validateW(W, bits) {\n  if (!Number.isSafeInteger(W) || W <= 0 || W > bits) throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, scalarBits) {\n  validateW(W, scalarBits);\n  const windows = Math.ceil(scalarBits / W) + 1; // W=8 33. Not 32, because we skip zero\n  const windowSize = 2 ** (W - 1); // W=8 128. Not 256, because we skip zero\n  const maxNumber = 2 ** W; // W=8 256\n  const mask = bitMask(W); // W=8 255 == mask 0b11111111\n  const shiftBy = BigInt(W); // W=8 8\n  return {\n    windows,\n    windowSize,\n    mask,\n    maxNumber,\n    shiftBy\n  };\n}\nfunction calcOffsets(n, window, wOpts) {\n  const {\n    windowSize,\n    mask,\n    maxNumber,\n    shiftBy\n  } = wOpts;\n  let wbits = Number(n & mask); // extract W bits.\n  let nextN = n >> shiftBy; // shift number by W bits.\n  // What actually happens here:\n  // const highestBit = Number(mask ^ (mask >> 1n));\n  // let wbits2 = wbits - 1; // skip zero\n  // if (wbits2 & highestBit) { wbits2 ^= Number(mask); // (~);\n  // split if bits > max: +224 => 256-32\n  if (wbits > windowSize) {\n    // we skip zero, which means instead of `>= size-1`, we do `> size`\n    wbits -= maxNumber; // -32, can be maxNumber - wbits, but then we need to set isNeg here.\n    nextN += _1n; // +256 (carry)\n  }\n  const offsetStart = window * windowSize;\n  const offset = offsetStart + Math.abs(wbits) - 1; // -1 because we skip zero\n  const isZero = wbits === 0; // is current window slice a 0?\n  const isNeg = wbits < 0; // is current window slice negative?\n  const isNegF = window % 2 !== 0; // fake random statement for noise\n  const offsetF = offsetStart; // fake offset for noise\n  return {\n    nextN,\n    offset,\n    isZero,\n    isNeg,\n    isNegF,\n    offsetF\n  };\n}\nfunction validateMSMPoints(points, c) {\n  if (!Array.isArray(points)) throw new Error('array expected');\n  points.forEach((p, i) => {\n    if (!(p instanceof c)) throw new Error('invalid point at index ' + i);\n  });\n}\nfunction validateMSMScalars(scalars, field) {\n  if (!Array.isArray(scalars)) throw new Error('array of scalars expected');\n  scalars.forEach((s, i) => {\n    if (!field.isValid(s)) throw new Error('invalid scalar at index ' + i);\n  });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes.\n// Allows to make points frozen / immutable.\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap();\nfunction getW(P) {\n  // To disable precomputes:\n  // return 1;\n  return pointWindowSizes.get(P) || 1;\n}\nfunction assert0(n) {\n  if (n !== _0n) throw new Error('invalid wNAF');\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Table generation takes **30MB of ram and 10ms on high-end CPU**,\n * but may take much longer on slow devices. Actual generation will happen on\n * first call of `multiply()`. By default, `BASE` point is precomputed.\n *\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport class wNAF {\n  // Parametrized with a given Point class (not individual point)\n  constructor(Point, bits) {\n    this.BASE = Point.BASE;\n    this.ZERO = Point.ZERO;\n    this.Fn = Point.Fn;\n    this.bits = bits;\n  }\n  // non-const time multiplication ladder\n  _unsafeLadder(elm, n, p = this.ZERO) {\n    let d = elm;\n    while (n > _0n) {\n      if (n & _1n) p = p.add(d);\n      d = d.double();\n      n >>= _1n;\n    }\n    return p;\n  }\n  /**\n   * Creates a wNAF precomputation window. Used for caching.\n   * Default window size is set by `utils.precompute()` and is equal to 8.\n   * Number of precomputed points depends on the curve size:\n   * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n   * - 𝑊 is the window size\n   * - 𝑛 is the bitlength of the curve order.\n   * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n   * @param point Point instance\n   * @param W window size\n   * @returns precomputed point tables flattened to a single array\n   */\n  precomputeWindow(point, W) {\n    const {\n      windows,\n      windowSize\n    } = calcWOpts(W, this.bits);\n    const points = [];\n    let p = point;\n    let base = p;\n    for (let window = 0; window < windows; window++) {\n      base = p;\n      points.push(base);\n      // i=1, bc we skip 0\n      for (let i = 1; i < windowSize; i++) {\n        base = base.add(p);\n        points.push(base);\n      }\n      p = base.double();\n    }\n    return points;\n  }\n  /**\n   * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n   * More compact implementation:\n   * https://github.com/paulmillr/noble-secp256k1/blob/47cb1669b6e506ad66b35fe7d76132ae97465da2/index.ts#L502-L541\n   * @returns real and fake (for const-time) points\n   */\n  wNAF(W, precomputes, n) {\n    // Scalar should be smaller than field order\n    if (!this.Fn.isValid(n)) throw new Error('invalid scalar');\n    // Accumulators\n    let p = this.ZERO;\n    let f = this.BASE;\n    // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n    // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n    // there is negate now: it is possible that negated element from low value\n    // would be the same as high element, which will create carry into next window.\n    // It's not obvious how this can fail, but still worth investigating later.\n    const wo = calcWOpts(W, this.bits);\n    for (let window = 0; window < wo.windows; window++) {\n      // (n === _0n) is handled and not early-exited. isEven and offsetF are used for noise\n      const {\n        nextN,\n        offset,\n        isZero,\n        isNeg,\n        isNegF,\n        offsetF\n      } = calcOffsets(n, window, wo);\n      n = nextN;\n      if (isZero) {\n        // bits are 0: add garbage to fake point\n        // Important part for const-time getPublicKey: add random \"noise\" point to f.\n        f = f.add(negateCt(isNegF, precomputes[offsetF]));\n      } else {\n        // bits are 1: add to result point\n        p = p.add(negateCt(isNeg, precomputes[offset]));\n      }\n    }\n    assert0(n);\n    // Return both real and fake points: JIT won't eliminate f.\n    // At this point there is a way to F be infinity-point even if p is not,\n    // which makes it less const-time: around 1 bigint multiply.\n    return {\n      p,\n      f\n    };\n  }\n  /**\n   * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n   * @param acc accumulator point to add result of multiplication\n   * @returns point\n   */\n  wNAFUnsafe(W, precomputes, n, acc = this.ZERO) {\n    const wo = calcWOpts(W, this.bits);\n    for (let window = 0; window < wo.windows; window++) {\n      if (n === _0n) break; // Early-exit, skip 0 value\n      const {\n        nextN,\n        offset,\n        isZero,\n        isNeg\n      } = calcOffsets(n, window, wo);\n      n = nextN;\n      if (isZero) {\n        // Window bits are 0: skip processing.\n        // Move to next window.\n        continue;\n      } else {\n        const item = precomputes[offset];\n        acc = acc.add(isNeg ? item.negate() : item); // Re-using acc allows to save adds in MSM\n      }\n    }\n    assert0(n);\n    return acc;\n  }\n  getPrecomputes(W, point, transform) {\n    // Calculate precomputes on a first run, reuse them after\n    let comp = pointPrecomputes.get(point);\n    if (!comp) {\n      comp = this.precomputeWindow(point, W);\n      if (W !== 1) {\n        // Doing transform outside of if brings 15% perf hit\n        if (typeof transform === 'function') comp = transform(comp);\n        pointPrecomputes.set(point, comp);\n      }\n    }\n    return comp;\n  }\n  cached(point, scalar, transform) {\n    const W = getW(point);\n    return this.wNAF(W, this.getPrecomputes(W, point, transform), scalar);\n  }\n  unsafe(point, scalar, transform, prev) {\n    const W = getW(point);\n    if (W === 1) return this._unsafeLadder(point, scalar, prev); // For W=1 ladder is ~x2 faster\n    return this.wNAFUnsafe(W, this.getPrecomputes(W, point, transform), scalar, prev);\n  }\n  // We calculate precomputes for elliptic curve point multiplication\n  // using windowed method. This specifies window size and\n  // stores precomputed values. Usually only base point would be precomputed.\n  createCache(P, W) {\n    validateW(W, this.bits);\n    pointWindowSizes.set(P, W);\n    pointPrecomputes.delete(P);\n  }\n  hasCache(elm) {\n    return getW(elm) !== 1;\n  }\n}\n/**\n * Endomorphism-specific multiplication for Koblitz curves.\n * Cost: 128 dbl, 0-256 adds.\n */\nexport function mulEndoUnsafe(Point, point, k1, k2) {\n  let acc = point;\n  let p1 = Point.ZERO;\n  let p2 = Point.ZERO;\n  while (k1 > _0n || k2 > _0n) {\n    if (k1 & _1n) p1 = p1.add(acc);\n    if (k2 & _1n) p2 = p2.add(acc);\n    acc = acc.double();\n    k1 >>= _1n;\n    k2 >>= _1n;\n  }\n  return {\n    p1,\n    p2\n  };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster than precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka secret keys / bigints)\n */\nexport function pippenger(c, fieldN, points, scalars) {\n  // If we split scalars by some window (let's say 8 bits), every chunk will only\n  // take 256 buckets even if there are 4096 scalars, also re-uses double.\n  // TODO:\n  // - https://eprint.iacr.org/2024/750.pdf\n  // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n  // 0 is accepted in scalars\n  validateMSMPoints(points, c);\n  validateMSMScalars(scalars, fieldN);\n  const plength = points.length;\n  const slength = scalars.length;\n  if (plength !== slength) throw new Error('arrays of points and scalars must have equal length');\n  // if (plength === 0) throw new Error('array must be of length >= 2');\n  const zero = c.ZERO;\n  const wbits = bitLen(BigInt(plength));\n  let windowSize = 1; // bits\n  if (wbits > 12) windowSize = wbits - 3;else if (wbits > 4) windowSize = wbits - 2;else if (wbits > 0) windowSize = 2;\n  const MASK = bitMask(windowSize);\n  const buckets = new Array(Number(MASK) + 1).fill(zero); // +1 for zero array\n  const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n  let sum = zero;\n  for (let i = lastBits; i >= 0; i -= windowSize) {\n    buckets.fill(zero);\n    for (let j = 0; j < slength; j++) {\n      const scalar = scalars[j];\n      const wbits = Number(scalar >> BigInt(i) & MASK);\n      buckets[wbits] = buckets[wbits].add(points[j]);\n    }\n    let resI = zero; // not using this will do small speed-up, but will lose ct\n    // Skip first bucket, because it is zero\n    for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n      sumI = sumI.add(buckets[j]);\n      resI = resI.add(sumI);\n    }\n    sum = sum.add(resI);\n    if (i !== 0) for (let j = 0; j < windowSize; j++) sum = sum.double();\n  }\n  return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n  /**\n   * Performance Analysis of Window-based Precomputation\n   *\n   * Base Case (256-bit scalar, 8-bit window):\n   * - Standard precomputation requires:\n   *   - 31 additions per scalar × 256 scalars = 7,936 ops\n   *   - Plus 255 summary additions = 8,191 total ops\n   *   Note: Summary additions can be optimized via accumulator\n   *\n   * Chunked Precomputation Analysis:\n   * - Using 32 chunks requires:\n   *   - 255 additions per chunk\n   *   - 256 doublings\n   *   - Total: (255 × 32) + 256 = 8,416 ops\n   *\n   * Memory Usage Comparison:\n   * Window Size | Standard Points | Chunked Points\n   * ------------|-----------------|---------------\n   *     4-bit   |     520         |      15\n   *     8-bit   |    4,224        |     255\n   *    10-bit   |   13,824        |   1,023\n   *    16-bit   |  557,056        |  65,535\n   *\n   * Key Advantages:\n   * 1. Enables larger window sizes due to reduced memory overhead\n   * 2. More efficient for smaller scalar counts:\n   *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n   *    - ~2x faster than standard 8,191 ops\n   *\n   * Limitations:\n   * - Not suitable for plain precomputes (requires 256 constant doublings)\n   * - Performance degrades with larger scalar counts:\n   *   - Optimal for ~256 scalars\n   *   - Less efficient for 4096+ scalars (Pippenger preferred)\n   */\n  validateW(windowSize, fieldN.BITS);\n  validateMSMPoints(points, c);\n  const zero = c.ZERO;\n  const tableSize = 2 ** windowSize - 1; // table size (without zero)\n  const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n  const MASK = bitMask(windowSize);\n  const tables = points.map(p => {\n    const res = [];\n    for (let i = 0, acc = p; i < tableSize; i++) {\n      res.push(acc);\n      acc = acc.add(p);\n    }\n    return res;\n  });\n  return scalars => {\n    validateMSMScalars(scalars, fieldN);\n    if (scalars.length > points.length) throw new Error('array of scalars must be smaller than array of points');\n    let res = zero;\n    for (let i = 0; i < chunks; i++) {\n      // No need to double if accumulator is still zero.\n      if (res !== zero) for (let j = 0; j < windowSize; j++) res = res.double();\n      const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n      for (let j = 0; j < scalars.length; j++) {\n        const n = scalars[j];\n        const curr = Number(n >> shiftBy & MASK);\n        if (!curr) continue; // skip zero scalars chunks\n        res = res.add(tables[j][curr - 1]);\n      }\n    }\n    return res;\n  };\n}\n// TODO: remove\n/** @deprecated */\nexport function validateBasic(curve) {\n  validateField(curve.Fp);\n  validateObject(curve, {\n    n: 'bigint',\n    h: 'bigint',\n    Gx: 'field',\n    Gy: 'field'\n  }, {\n    nBitLength: 'isSafeInteger',\n    nByteLength: 'isSafeInteger'\n  });\n  // Set defaults\n  return Object.freeze({\n    ...nLength(curve.n, curve.nBitLength),\n    ...curve,\n    ...{\n      p: curve.Fp.ORDER\n    }\n  });\n}\nfunction createField(order, field, isLE) {\n  if (field) {\n    if (field.ORDER !== order) throw new Error('Field.ORDER must match order: Fp == p, Fn == n');\n    validateField(field);\n    return field;\n  } else {\n    return Field(order, {\n      isLE\n    });\n  }\n}\n/** Validates CURVE opts and creates fields */\nexport function _createCurveFields(type, CURVE, curveOpts = {}, FpFnLE) {\n  if (FpFnLE === undefined) FpFnLE = type === 'edwards';\n  if (!CURVE || typeof CURVE !== 'object') throw new Error(`expected valid ${type} CURVE object`);\n  for (const p of ['p', 'n', 'h']) {\n    const val = CURVE[p];\n    if (!(typeof val === 'bigint' && val > _0n)) throw new Error(`CURVE.${p} must be positive bigint`);\n  }\n  const Fp = createField(CURVE.p, curveOpts.Fp, FpFnLE);\n  const Fn = createField(CURVE.n, curveOpts.Fn, FpFnLE);\n  const _b = type === 'weierstrass' ? 'b' : 'd';\n  const params = ['Gx', 'Gy', 'a', _b];\n  for (const p of params) {\n    // @ts-ignore\n    if (!Fp.isValid(CURVE[p])) throw new Error(`CURVE.${p} must be valid field element of CURVE.Fp`);\n  }\n  CURVE = Object.freeze(Object.assign({}, CURVE));\n  return {\n    CURVE,\n    Fp,\n    Fn\n  };\n}", "map": {"version": 3, "names": ["bitLen", "bitMask", "validateObject", "Field", "FpInvertBatch", "nLength", "validateField", "_0n", "BigInt", "_1n", "negateCt", "condition", "item", "neg", "negate", "normalizeZ", "c", "points", "invertedZs", "Fp", "map", "p", "Z", "i", "fromAffine", "toAffine", "validateW", "W", "bits", "Number", "isSafeInteger", "Error", "calcWOpts", "scalarBits", "windows", "Math", "ceil", "windowSize", "maxNumber", "mask", "shiftBy", "calcOffsets", "n", "window", "wOpts", "wbits", "nextN", "offsetStart", "offset", "abs", "isZero", "isNeg", "isNegF", "offsetF", "validateMSMPoints", "Array", "isArray", "for<PERSON>ach", "validateMSMScalars", "scalars", "field", "s", "<PERSON><PERSON><PERSON><PERSON>", "pointPrecomputes", "WeakMap", "pointWindowSizes", "getW", "P", "get", "assert0", "wNAF", "constructor", "Point", "BASE", "ZERO", "Fn", "_unsafeLadder", "elm", "d", "add", "double", "precomputeWindow", "point", "base", "push", "precomputes", "f", "wo", "wNAFUnsafe", "acc", "getPrecomputes", "transform", "comp", "set", "cached", "scalar", "unsafe", "prev", "createCache", "delete", "<PERSON><PERSON><PERSON>", "mulEndoUnsafe", "k1", "k2", "p1", "p2", "pippenger", "fieldN", "plength", "length", "slength", "zero", "MASK", "buckets", "fill", "lastBits", "floor", "BITS", "sum", "j", "resI", "sumI", "precomputeMSMUnsafe", "tableSize", "chunks", "tables", "res", "curr", "validateBasic", "curve", "h", "Gx", "Gy", "nBitLength", "nByteLength", "Object", "freeze", "ORDER", "createField", "order", "isLE", "_createCurveFields", "type", "CURVE", "curveOpts", "FpFnLE", "undefined", "val", "_b", "params", "assign"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\abstract\\curve.ts"], "sourcesContent": ["/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNA<PERSON>, pippenger.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { bitLen, bitMask, validateObject } from '../utils.ts';\nimport { Field, FpInvertBatch, nLength, validateField, type IField } from './modular.ts';\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n\nexport type AffinePoint<T> = {\n  x: T;\n  y: T;\n} & { Z?: never };\n\n// This was initialy do this way to re-use montgomery ladder in field (add->mul,double->sqr), but\n// that didn't happen and there is probably not much reason to have separate Group like this?\nexport interface Group<T extends Group<T>> {\n  double(): T;\n  negate(): T;\n  add(other: T): T;\n  subtract(other: T): T;\n  equals(other: T): boolean;\n  multiply(scalar: bigint): T;\n  toAffine?(invertedZ?: any): AffinePoint<any>;\n}\n\n// We can't \"abstract out\" coordinates (X, Y, Z; and T in Edwards): argument names of constructor\n// are not accessible. See Typescript gh-56093, gh-41594.\n//\n// We have to use recursive types, so it will return actual point, not constained `CurvePoint`.\n// If, at any point, P is `any`, it will erase all types and replace it\n// with `any`, because of recursion, `any implements CurvePoint`,\n// but we lose all constrains on methods.\n\n/** Base interface for all elliptic curve Points. */\nexport interface CurvePoint<F, P extends CurvePoint<F, P>> extends Group<P> {\n  /** Affine x coordinate. Different from projective / extended X coordinate. */\n  x: F;\n  /** Affine y coordinate. Different from projective / extended Y coordinate. */\n  y: F;\n  Z?: F;\n  double(): P;\n  negate(): P;\n  add(other: P): P;\n  subtract(other: P): P;\n  equals(other: P): boolean;\n  multiply(scalar: bigint): P;\n  assertValidity(): void;\n  clearCofactor(): P;\n  is0(): boolean;\n  isTorsionFree(): boolean;\n  isSmallOrder(): boolean;\n  multiplyUnsafe(scalar: bigint): P;\n  /**\n   * Massively speeds up `p.multiply(n)` by using precompute tables (caching). See {@link wNAF}.\n   * @param isLazy calculate cache now. Default (true) ensures it's deferred to first `multiply()`\n   */\n  precompute(windowSize?: number, isLazy?: boolean): P;\n  /** Converts point to 2D xy affine coordinates */\n  toAffine(invertedZ?: F): AffinePoint<F>;\n  toBytes(): Uint8Array;\n  toHex(): string;\n}\n\n/** Base interface for all elliptic curve Point constructors. */\nexport interface CurvePointCons<P extends CurvePoint<any, P>> {\n  [Symbol.hasInstance]: (item: unknown) => boolean;\n  BASE: P;\n  ZERO: P;\n  /** Field for basic curve math */\n  Fp: IField<P_F<P>>;\n  /** Scalar field, for scalars in multiply and others */\n  Fn: IField<bigint>;\n  /** Creates point from x, y. Does NOT validate if the point is valid. Use `.assertValidity()`. */\n  fromAffine(p: AffinePoint<P_F<P>>): P;\n  fromBytes(bytes: Uint8Array): P;\n  fromHex(hex: Uint8Array | string): P;\n}\n\n// Type inference helpers: PC - PointConstructor, P - Point, Fp - Field element\n// Short names, because we use them a lot in result types:\n// * we can't do 'P = GetCurvePoint<PC>': this is default value and doesn't constrain anything\n// * we can't do 'type X = GetCurvePoint<PC>': it won't be accesible for arguments/return types\n// * `CurvePointCons<P extends CurvePoint<any, P>>` constraints from interface definition\n//   won't propagate, if `PC extends CurvePointCons<any>`: the P would be 'any', which is incorrect\n// * PC could be super specific with super specific P, which implements CurvePoint<any, P>.\n//   this means we need to do stuff like\n//   `function test<P extends CurvePoint<any, P>, PC extends CurvePointCons<P>>(`\n//   if we want type safety around P, otherwise PC_P<PC> will be any\n\n/** Returns Fp type from Point (P_F<P> == P.F) */\nexport type P_F<P extends CurvePoint<any, P>> = P extends CurvePoint<infer F, P> ? F : never;\n/** Returns Fp type from PointCons (PC_F<PC> == PC.P.F) */\nexport type PC_F<PC extends CurvePointCons<CurvePoint<any, any>>> = PC['Fp']['ZERO'];\n/** Returns Point type from PointCons (PC_P<PC> == PC.P) */\nexport type PC_P<PC extends CurvePointCons<CurvePoint<any, any>>> = PC['ZERO'];\n\n// Ugly hack to get proper type inference, because in typescript fails to infer resursively.\n// The hack allows to do up to 10 chained operations without applying type erasure.\n//\n// Types which won't work:\n// * `CurvePointCons<CurvePoint<any, any>>`, will return `any` after 1 operation\n// * `CurvePointCons<any>: WeierstrassPointCons<bigint> extends CurvePointCons<any> = false`\n// * `P extends CurvePoint, PC extends CurvePointCons<P>`\n//     * It can't infer P from PC alone\n//     * Too many relations between F, P & PC\n//     * It will infer P/F if `arg: CurvePointCons<F, P>`, but will fail if PC is generic\n//     * It will work correctly if there is an additional argument of type P\n//     * But generally, we don't want to parametrize `CurvePointCons` over `F`: it will complicate\n//       types, making them un-inferable\n// prettier-ignore\nexport type PC_ANY = CurvePointCons<\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any,\n  CurvePoint<any, any>\n  >>>>>>>>>\n>;\n\nexport interface CurveLengths {\n  secretKey?: number;\n  publicKey?: number;\n  publicKeyUncompressed?: number;\n  publicKeyHasPrefix?: boolean;\n  signature?: number;\n  seed?: number;\n}\nexport type GroupConstructor<T> = {\n  BASE: T;\n  ZERO: T;\n};\n/** @deprecated */\nexport type ExtendedGroupConstructor<T> = GroupConstructor<T> & {\n  Fp: IField<any>;\n  Fn: IField<bigint>;\n  fromAffine(ap: AffinePoint<any>): T;\n};\nexport type Mapper<T> = (i: T[]) => T[];\n\nexport function negateCt<T extends { negate: () => T }>(condition: boolean, item: T): T {\n  const neg = item.negate();\n  return condition ? neg : item;\n}\n\n/**\n * Takes a bunch of Projective Points but executes only one\n * inversion on all of them. Inversion is very slow operation,\n * so this improves performance massively.\n * Optimization: converts a list of projective points to a list of identical points with Z=1.\n */\nexport function normalizeZ<P extends CurvePoint<any, P>, PC extends CurvePointCons<P>>(\n  c: PC,\n  points: P[]\n): P[] {\n  const invertedZs = FpInvertBatch(\n    c.Fp,\n    points.map((p) => p.Z!)\n  );\n  return points.map((p, i) => c.fromAffine(p.toAffine(invertedZs[i])));\n}\n\nfunction validateW(W: number, bits: number) {\n  if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n    throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\n\n/** Internal wNAF opts for specific W and scalarBits */\nexport type WOpts = {\n  windows: number;\n  windowSize: number;\n  mask: bigint;\n  maxNumber: number;\n  shiftBy: bigint;\n};\n\nfunction calcWOpts(W: number, scalarBits: number): WOpts {\n  validateW(W, scalarBits);\n  const windows = Math.ceil(scalarBits / W) + 1; // W=8 33. Not 32, because we skip zero\n  const windowSize = 2 ** (W - 1); // W=8 128. Not 256, because we skip zero\n  const maxNumber = 2 ** W; // W=8 256\n  const mask = bitMask(W); // W=8 255 == mask 0b11111111\n  const shiftBy = BigInt(W); // W=8 8\n  return { windows, windowSize, mask, maxNumber, shiftBy };\n}\n\nfunction calcOffsets(n: bigint, window: number, wOpts: WOpts) {\n  const { windowSize, mask, maxNumber, shiftBy } = wOpts;\n  let wbits = Number(n & mask); // extract W bits.\n  let nextN = n >> shiftBy; // shift number by W bits.\n\n  // What actually happens here:\n  // const highestBit = Number(mask ^ (mask >> 1n));\n  // let wbits2 = wbits - 1; // skip zero\n  // if (wbits2 & highestBit) { wbits2 ^= Number(mask); // (~);\n\n  // split if bits > max: +224 => 256-32\n  if (wbits > windowSize) {\n    // we skip zero, which means instead of `>= size-1`, we do `> size`\n    wbits -= maxNumber; // -32, can be maxNumber - wbits, but then we need to set isNeg here.\n    nextN += _1n; // +256 (carry)\n  }\n  const offsetStart = window * windowSize;\n  const offset = offsetStart + Math.abs(wbits) - 1; // -1 because we skip zero\n  const isZero = wbits === 0; // is current window slice a 0?\n  const isNeg = wbits < 0; // is current window slice negative?\n  const isNegF = window % 2 !== 0; // fake random statement for noise\n  const offsetF = offsetStart; // fake offset for noise\n  return { nextN, offset, isZero, isNeg, isNegF, offsetF };\n}\n\nfunction validateMSMPoints(points: any[], c: any) {\n  if (!Array.isArray(points)) throw new Error('array expected');\n  points.forEach((p, i) => {\n    if (!(p instanceof c)) throw new Error('invalid point at index ' + i);\n  });\n}\nfunction validateMSMScalars(scalars: any[], field: any) {\n  if (!Array.isArray(scalars)) throw new Error('array of scalars expected');\n  scalars.forEach((s, i) => {\n    if (!field.isValid(s)) throw new Error('invalid scalar at index ' + i);\n  });\n}\n\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes.\n// Allows to make points frozen / immutable.\nconst pointPrecomputes = new WeakMap<any, any[]>();\nconst pointWindowSizes = new WeakMap<any, number>();\n\nfunction getW(P: any): number {\n  // To disable precomputes:\n  // return 1;\n  return pointWindowSizes.get(P) || 1;\n}\n\nfunction assert0(n: bigint): void {\n  if (n !== _0n) throw new Error('invalid wNAF');\n}\n\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Table generation takes **30MB of ram and 10ms on high-end CPU**,\n * but may take much longer on slow devices. Actual generation will happen on\n * first call of `multiply()`. By default, `BASE` point is precomputed.\n *\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport class wNAF<PC extends PC_ANY> {\n  private readonly BASE: PC_P<PC>;\n  private readonly ZERO: PC_P<PC>;\n  private readonly Fn: PC['Fn'];\n  readonly bits: number;\n\n  // Parametrized with a given Point class (not individual point)\n  constructor(Point: PC, bits: number) {\n    this.BASE = Point.BASE;\n    this.ZERO = Point.ZERO;\n    this.Fn = Point.Fn;\n    this.bits = bits;\n  }\n\n  // non-const time multiplication ladder\n  _unsafeLadder(elm: PC_P<PC>, n: bigint, p: PC_P<PC> = this.ZERO): PC_P<PC> {\n    let d: PC_P<PC> = elm;\n    while (n > _0n) {\n      if (n & _1n) p = p.add(d);\n      d = d.double();\n      n >>= _1n;\n    }\n    return p;\n  }\n\n  /**\n   * Creates a wNAF precomputation window. Used for caching.\n   * Default window size is set by `utils.precompute()` and is equal to 8.\n   * Number of precomputed points depends on the curve size:\n   * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n   * - 𝑊 is the window size\n   * - 𝑛 is the bitlength of the curve order.\n   * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n   * @param point Point instance\n   * @param W window size\n   * @returns precomputed point tables flattened to a single array\n   */\n  private precomputeWindow(point: PC_P<PC>, W: number): PC_P<PC>[] {\n    const { windows, windowSize } = calcWOpts(W, this.bits);\n    const points: PC_P<PC>[] = [];\n    let p: PC_P<PC> = point;\n    let base = p;\n    for (let window = 0; window < windows; window++) {\n      base = p;\n      points.push(base);\n      // i=1, bc we skip 0\n      for (let i = 1; i < windowSize; i++) {\n        base = base.add(p);\n        points.push(base);\n      }\n      p = base.double();\n    }\n    return points;\n  }\n\n  /**\n   * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n   * More compact implementation:\n   * https://github.com/paulmillr/noble-secp256k1/blob/47cb1669b6e506ad66b35fe7d76132ae97465da2/index.ts#L502-L541\n   * @returns real and fake (for const-time) points\n   */\n  private wNAF(W: number, precomputes: PC_P<PC>[], n: bigint): { p: PC_P<PC>; f: PC_P<PC> } {\n    // Scalar should be smaller than field order\n    if (!this.Fn.isValid(n)) throw new Error('invalid scalar');\n    // Accumulators\n    let p = this.ZERO;\n    let f = this.BASE;\n    // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n    // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n    // there is negate now: it is possible that negated element from low value\n    // would be the same as high element, which will create carry into next window.\n    // It's not obvious how this can fail, but still worth investigating later.\n    const wo = calcWOpts(W, this.bits);\n    for (let window = 0; window < wo.windows; window++) {\n      // (n === _0n) is handled and not early-exited. isEven and offsetF are used for noise\n      const { nextN, offset, isZero, isNeg, isNegF, offsetF } = calcOffsets(n, window, wo);\n      n = nextN;\n      if (isZero) {\n        // bits are 0: add garbage to fake point\n        // Important part for const-time getPublicKey: add random \"noise\" point to f.\n        f = f.add(negateCt(isNegF, precomputes[offsetF]));\n      } else {\n        // bits are 1: add to result point\n        p = p.add(negateCt(isNeg, precomputes[offset]));\n      }\n    }\n    assert0(n);\n    // Return both real and fake points: JIT won't eliminate f.\n    // At this point there is a way to F be infinity-point even if p is not,\n    // which makes it less const-time: around 1 bigint multiply.\n    return { p, f };\n  }\n\n  /**\n   * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n   * @param acc accumulator point to add result of multiplication\n   * @returns point\n   */\n  private wNAFUnsafe(\n    W: number,\n    precomputes: PC_P<PC>[],\n    n: bigint,\n    acc: PC_P<PC> = this.ZERO\n  ): PC_P<PC> {\n    const wo = calcWOpts(W, this.bits);\n    for (let window = 0; window < wo.windows; window++) {\n      if (n === _0n) break; // Early-exit, skip 0 value\n      const { nextN, offset, isZero, isNeg } = calcOffsets(n, window, wo);\n      n = nextN;\n      if (isZero) {\n        // Window bits are 0: skip processing.\n        // Move to next window.\n        continue;\n      } else {\n        const item = precomputes[offset];\n        acc = acc.add(isNeg ? item.negate() : item); // Re-using acc allows to save adds in MSM\n      }\n    }\n    assert0(n);\n    return acc;\n  }\n\n  private getPrecomputes(W: number, point: PC_P<PC>, transform?: Mapper<PC_P<PC>>): PC_P<PC>[] {\n    // Calculate precomputes on a first run, reuse them after\n    let comp = pointPrecomputes.get(point);\n    if (!comp) {\n      comp = this.precomputeWindow(point, W) as PC_P<PC>[];\n      if (W !== 1) {\n        // Doing transform outside of if brings 15% perf hit\n        if (typeof transform === 'function') comp = transform(comp);\n        pointPrecomputes.set(point, comp);\n      }\n    }\n    return comp;\n  }\n\n  cached(\n    point: PC_P<PC>,\n    scalar: bigint,\n    transform?: Mapper<PC_P<PC>>\n  ): { p: PC_P<PC>; f: PC_P<PC> } {\n    const W = getW(point);\n    return this.wNAF(W, this.getPrecomputes(W, point, transform), scalar);\n  }\n\n  unsafe(point: PC_P<PC>, scalar: bigint, transform?: Mapper<PC_P<PC>>, prev?: PC_P<PC>): PC_P<PC> {\n    const W = getW(point);\n    if (W === 1) return this._unsafeLadder(point, scalar, prev); // For W=1 ladder is ~x2 faster\n    return this.wNAFUnsafe(W, this.getPrecomputes(W, point, transform), scalar, prev);\n  }\n\n  // We calculate precomputes for elliptic curve point multiplication\n  // using windowed method. This specifies window size and\n  // stores precomputed values. Usually only base point would be precomputed.\n  createCache(P: PC_P<PC>, W: number): void {\n    validateW(W, this.bits);\n    pointWindowSizes.set(P, W);\n    pointPrecomputes.delete(P);\n  }\n\n  hasCache(elm: PC_P<PC>): boolean {\n    return getW(elm) !== 1;\n  }\n}\n\n/**\n * Endomorphism-specific multiplication for Koblitz curves.\n * Cost: 128 dbl, 0-256 adds.\n */\nexport function mulEndoUnsafe<P extends CurvePoint<any, P>, PC extends CurvePointCons<P>>(\n  Point: PC,\n  point: P,\n  k1: bigint,\n  k2: bigint\n): { p1: P; p2: P } {\n  let acc = point;\n  let p1 = Point.ZERO;\n  let p2 = Point.ZERO;\n  while (k1 > _0n || k2 > _0n) {\n    if (k1 & _1n) p1 = p1.add(acc);\n    if (k2 & _1n) p2 = p2.add(acc);\n    acc = acc.double();\n    k1 >>= _1n;\n    k2 >>= _1n;\n  }\n  return { p1, p2 };\n}\n\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster than precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka secret keys / bigints)\n */\nexport function pippenger<P extends CurvePoint<any, P>, PC extends CurvePointCons<P>>(\n  c: PC,\n  fieldN: IField<bigint>,\n  points: P[],\n  scalars: bigint[]\n): P {\n  // If we split scalars by some window (let's say 8 bits), every chunk will only\n  // take 256 buckets even if there are 4096 scalars, also re-uses double.\n  // TODO:\n  // - https://eprint.iacr.org/2024/750.pdf\n  // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n  // 0 is accepted in scalars\n  validateMSMPoints(points, c);\n  validateMSMScalars(scalars, fieldN);\n  const plength = points.length;\n  const slength = scalars.length;\n  if (plength !== slength) throw new Error('arrays of points and scalars must have equal length');\n  // if (plength === 0) throw new Error('array must be of length >= 2');\n  const zero = c.ZERO;\n  const wbits = bitLen(BigInt(plength));\n  let windowSize = 1; // bits\n  if (wbits > 12) windowSize = wbits - 3;\n  else if (wbits > 4) windowSize = wbits - 2;\n  else if (wbits > 0) windowSize = 2;\n  const MASK = bitMask(windowSize);\n  const buckets = new Array(Number(MASK) + 1).fill(zero); // +1 for zero array\n  const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n  let sum = zero;\n  for (let i = lastBits; i >= 0; i -= windowSize) {\n    buckets.fill(zero);\n    for (let j = 0; j < slength; j++) {\n      const scalar = scalars[j];\n      const wbits = Number((scalar >> BigInt(i)) & MASK);\n      buckets[wbits] = buckets[wbits].add(points[j]);\n    }\n    let resI = zero; // not using this will do small speed-up, but will lose ct\n    // Skip first bucket, because it is zero\n    for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n      sumI = sumI.add(buckets[j]);\n      resI = resI.add(sumI);\n    }\n    sum = sum.add(resI);\n    if (i !== 0) for (let j = 0; j < windowSize; j++) sum = sum.double();\n  }\n  return sum as P;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe<P extends CurvePoint<any, P>, PC extends CurvePointCons<P>>(\n  c: PC,\n  fieldN: IField<bigint>,\n  points: P[],\n  windowSize: number\n): (scalars: bigint[]) => P {\n  /**\n   * Performance Analysis of Window-based Precomputation\n   *\n   * Base Case (256-bit scalar, 8-bit window):\n   * - Standard precomputation requires:\n   *   - 31 additions per scalar × 256 scalars = 7,936 ops\n   *   - Plus 255 summary additions = 8,191 total ops\n   *   Note: Summary additions can be optimized via accumulator\n   *\n   * Chunked Precomputation Analysis:\n   * - Using 32 chunks requires:\n   *   - 255 additions per chunk\n   *   - 256 doublings\n   *   - Total: (255 × 32) + 256 = 8,416 ops\n   *\n   * Memory Usage Comparison:\n   * Window Size | Standard Points | Chunked Points\n   * ------------|-----------------|---------------\n   *     4-bit   |     520         |      15\n   *     8-bit   |    4,224        |     255\n   *    10-bit   |   13,824        |   1,023\n   *    16-bit   |  557,056        |  65,535\n   *\n   * Key Advantages:\n   * 1. Enables larger window sizes due to reduced memory overhead\n   * 2. More efficient for smaller scalar counts:\n   *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n   *    - ~2x faster than standard 8,191 ops\n   *\n   * Limitations:\n   * - Not suitable for plain precomputes (requires 256 constant doublings)\n   * - Performance degrades with larger scalar counts:\n   *   - Optimal for ~256 scalars\n   *   - Less efficient for 4096+ scalars (Pippenger preferred)\n   */\n  validateW(windowSize, fieldN.BITS);\n  validateMSMPoints(points, c);\n  const zero = c.ZERO;\n  const tableSize = 2 ** windowSize - 1; // table size (without zero)\n  const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n  const MASK = bitMask(windowSize);\n  const tables = points.map((p: P) => {\n    const res = [];\n    for (let i = 0, acc = p; i < tableSize; i++) {\n      res.push(acc);\n      acc = acc.add(p);\n    }\n    return res;\n  });\n  return (scalars: bigint[]): P => {\n    validateMSMScalars(scalars, fieldN);\n    if (scalars.length > points.length)\n      throw new Error('array of scalars must be smaller than array of points');\n    let res = zero;\n    for (let i = 0; i < chunks; i++) {\n      // No need to double if accumulator is still zero.\n      if (res !== zero) for (let j = 0; j < windowSize; j++) res = res.double();\n      const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n      for (let j = 0; j < scalars.length; j++) {\n        const n = scalars[j];\n        const curr = Number((n >> shiftBy) & MASK);\n        if (!curr) continue; // skip zero scalars chunks\n        res = res.add(tables[j][curr - 1]);\n      }\n    }\n    return res;\n  };\n}\n\n// TODO: remove\n/**\n * Generic BasicCurve interface: works even for polynomial fields (BLS): P, n, h would be ok.\n * Though generator can be different (Fp2 / Fp6 for BLS).\n */\nexport type BasicCurve<T> = {\n  Fp: IField<T>; // Field over which we'll do calculations (Fp)\n  n: bigint; // Curve order, total count of valid points in the field\n  nBitLength?: number; // bit length of curve order\n  nByteLength?: number; // byte length of curve order\n  h: bigint; // cofactor. we can assign default=1, but users will just ignore it w/o validation\n  hEff?: bigint; // Number to multiply to clear cofactor\n  Gx: T; // base point X coordinate\n  Gy: T; // base point Y coordinate\n  allowInfinityPoint?: boolean; // bls12-381 requires it. ZERO point is valid, but invalid pubkey\n};\n\n// TODO: remove\n/** @deprecated */\nexport function validateBasic<FP, T>(\n  curve: BasicCurve<FP> & T\n): Readonly<\n  {\n    readonly nBitLength: number;\n    readonly nByteLength: number;\n  } & BasicCurve<FP> &\n    T & {\n      p: bigint;\n    }\n> {\n  validateField(curve.Fp);\n  validateObject(\n    curve,\n    {\n      n: 'bigint',\n      h: 'bigint',\n      Gx: 'field',\n      Gy: 'field',\n    },\n    {\n      nBitLength: 'isSafeInteger',\n      nByteLength: 'isSafeInteger',\n    }\n  );\n  // Set defaults\n  return Object.freeze({\n    ...nLength(curve.n, curve.nBitLength),\n    ...curve,\n    ...{ p: curve.Fp.ORDER },\n  } as const);\n}\n\nexport type ValidCurveParams<T> = {\n  p: bigint;\n  n: bigint;\n  h: bigint;\n  a: T;\n  b?: T;\n  d?: T;\n  Gx: T;\n  Gy: T;\n};\n\nfunction createField<T>(order: bigint, field?: IField<T>, isLE?: boolean): IField<T> {\n  if (field) {\n    if (field.ORDER !== order) throw new Error('Field.ORDER must match order: Fp == p, Fn == n');\n    validateField(field);\n    return field;\n  } else {\n    return Field(order, { isLE }) as unknown as IField<T>;\n  }\n}\nexport type FpFn<T> = { Fp: IField<T>; Fn: IField<bigint> };\n\n/** Validates CURVE opts and creates fields */\nexport function _createCurveFields<T>(\n  type: 'weierstrass' | 'edwards',\n  CURVE: ValidCurveParams<T>,\n  curveOpts: Partial<FpFn<T>> = {},\n  FpFnLE?: boolean\n): FpFn<T> & { CURVE: ValidCurveParams<T> } {\n  if (FpFnLE === undefined) FpFnLE = type === 'edwards';\n  if (!CURVE || typeof CURVE !== 'object') throw new Error(`expected valid ${type} CURVE object`);\n  for (const p of ['p', 'n', 'h'] as const) {\n    const val = CURVE[p];\n    if (!(typeof val === 'bigint' && val > _0n))\n      throw new Error(`CURVE.${p} must be positive bigint`);\n  }\n  const Fp = createField(CURVE.p, curveOpts.Fp, FpFnLE);\n  const Fn = createField(CURVE.n, curveOpts.Fn, FpFnLE);\n  const _b: 'b' | 'd' = type === 'weierstrass' ? 'b' : 'd';\n  const params = ['Gx', 'Gy', 'a', _b] as const;\n  for (const p of params) {\n    // @ts-ignore\n    if (!Fp.isValid(CURVE[p]))\n      throw new Error(`CURVE.${p} must be valid field element of CURVE.Fp`);\n  }\n  CURVE = Object.freeze(Object.assign({}, CURVE));\n  return { CURVE, Fp, Fn };\n}\n"], "mappings": "AAAA;;;;;AAKA;AACA,SAASA,MAAM,EAAEC,OAAO,EAAEC,cAAc,QAAQ,aAAa;AAC7D,SAASC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,aAAa,QAAqB,cAAc;AAExF,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;AA0IrB,OAAM,SAAUE,QAAQA,CAAgCC,SAAkB,EAAEC,IAAO;EACjF,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,EAAE;EACzB,OAAOH,SAAS,GAAGE,GAAG,GAAGD,IAAI;AAC/B;AAEA;;;;;;AAMA,OAAM,SAAUG,UAAUA,CACxBC,CAAK,EACLC,MAAW;EAEX,MAAMC,UAAU,GAAGd,aAAa,CAC9BY,CAAC,CAACG,EAAE,EACJF,MAAM,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,CAAE,CAAC,CACxB;EACD,OAAOL,MAAM,CAACG,GAAG,CAAC,CAACC,CAAC,EAAEE,CAAC,KAAKP,CAAC,CAACQ,UAAU,CAACH,CAAC,CAACI,QAAQ,CAACP,UAAU,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE;AAEA,SAASG,SAASA,CAACC,CAAS,EAAEC,IAAY;EACxC,IAAI,CAACC,MAAM,CAACC,aAAa,CAACH,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGC,IAAI,EAChD,MAAM,IAAIG,KAAK,CAAC,oCAAoC,GAAGH,IAAI,GAAG,WAAW,GAAGD,CAAC,CAAC;AAClF;AAWA,SAASK,SAASA,CAACL,CAAS,EAAEM,UAAkB;EAC9CP,SAAS,CAACC,CAAC,EAAEM,UAAU,CAAC;EACxB,MAAMC,OAAO,GAAGC,IAAI,CAACC,IAAI,CAACH,UAAU,GAAGN,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMU,UAAU,GAAG,CAAC,KAAKV,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACjC,MAAMW,SAAS,GAAG,CAAC,IAAIX,CAAC,CAAC,CAAC;EAC1B,MAAMY,IAAI,GAAGtC,OAAO,CAAC0B,CAAC,CAAC,CAAC,CAAC;EACzB,MAAMa,OAAO,GAAGhC,MAAM,CAACmB,CAAC,CAAC,CAAC,CAAC;EAC3B,OAAO;IAAEO,OAAO;IAAEG,UAAU;IAAEE,IAAI;IAAED,SAAS;IAAEE;EAAO,CAAE;AAC1D;AAEA,SAASC,WAAWA,CAACC,CAAS,EAAEC,MAAc,EAAEC,KAAY;EAC1D,MAAM;IAAEP,UAAU;IAAEE,IAAI;IAAED,SAAS;IAAEE;EAAO,CAAE,GAAGI,KAAK;EACtD,IAAIC,KAAK,GAAGhB,MAAM,CAACa,CAAC,GAAGH,IAAI,CAAC,CAAC,CAAC;EAC9B,IAAIO,KAAK,GAAGJ,CAAC,IAAIF,OAAO,CAAC,CAAC;EAE1B;EACA;EACA;EACA;EAEA;EACA,IAAIK,KAAK,GAAGR,UAAU,EAAE;IACtB;IACAQ,KAAK,IAAIP,SAAS,CAAC,CAAC;IACpBQ,KAAK,IAAIrC,GAAG,CAAC,CAAC;EAChB;EACA,MAAMsC,WAAW,GAAGJ,MAAM,GAAGN,UAAU;EACvC,MAAMW,MAAM,GAAGD,WAAW,GAAGZ,IAAI,CAACc,GAAG,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAClD,MAAMK,MAAM,GAAGL,KAAK,KAAK,CAAC,CAAC,CAAC;EAC5B,MAAMM,KAAK,GAAGN,KAAK,GAAG,CAAC,CAAC,CAAC;EACzB,MAAMO,MAAM,GAAGT,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;EACjC,MAAMU,OAAO,GAAGN,WAAW,CAAC,CAAC;EAC7B,OAAO;IAAED,KAAK;IAAEE,MAAM;IAAEE,MAAM;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAAO,CAAE;AAC1D;AAEA,SAASC,iBAAiBA,CAACrC,MAAa,EAAED,CAAM;EAC9C,IAAI,CAACuC,KAAK,CAACC,OAAO,CAACvC,MAAM,CAAC,EAAE,MAAM,IAAIc,KAAK,CAAC,gBAAgB,CAAC;EAC7Dd,MAAM,CAACwC,OAAO,CAAC,CAACpC,CAAC,EAAEE,CAAC,KAAI;IACtB,IAAI,EAAEF,CAAC,YAAYL,CAAC,CAAC,EAAE,MAAM,IAAIe,KAAK,CAAC,yBAAyB,GAAGR,CAAC,CAAC;EACvE,CAAC,CAAC;AACJ;AACA,SAASmC,kBAAkBA,CAACC,OAAc,EAAEC,KAAU;EACpD,IAAI,CAACL,KAAK,CAACC,OAAO,CAACG,OAAO,CAAC,EAAE,MAAM,IAAI5B,KAAK,CAAC,2BAA2B,CAAC;EACzE4B,OAAO,CAACF,OAAO,CAAC,CAACI,CAAC,EAAEtC,CAAC,KAAI;IACvB,IAAI,CAACqC,KAAK,CAACE,OAAO,CAACD,CAAC,CAAC,EAAE,MAAM,IAAI9B,KAAK,CAAC,0BAA0B,GAAGR,CAAC,CAAC;EACxE,CAAC,CAAC;AACJ;AAEA;AACA;AACA;AACA,MAAMwC,gBAAgB,GAAG,IAAIC,OAAO,EAAc;AAClD,MAAMC,gBAAgB,GAAG,IAAID,OAAO,EAAe;AAEnD,SAASE,IAAIA,CAACC,CAAM;EAClB;EACA;EACA,OAAOF,gBAAgB,CAACG,GAAG,CAACD,CAAC,CAAC,IAAI,CAAC;AACrC;AAEA,SAASE,OAAOA,CAAC3B,CAAS;EACxB,IAAIA,CAAC,KAAKnC,GAAG,EAAE,MAAM,IAAIwB,KAAK,CAAC,cAAc,CAAC;AAChD;AAEA;;;;;;;;;;;;;;;;;;AAkBA,OAAM,MAAOuC,IAAI;EAMf;EACAC,YAAYC,KAAS,EAAE5C,IAAY;IACjC,IAAI,CAAC6C,IAAI,GAAGD,KAAK,CAACC,IAAI;IACtB,IAAI,CAACC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACtB,IAAI,CAACC,EAAE,GAAGH,KAAK,CAACG,EAAE;IAClB,IAAI,CAAC/C,IAAI,GAAGA,IAAI;EAClB;EAEA;EACAgD,aAAaA,CAACC,GAAa,EAAEnC,CAAS,EAAErB,CAAA,GAAc,IAAI,CAACqD,IAAI;IAC7D,IAAII,CAAC,GAAaD,GAAG;IACrB,OAAOnC,CAAC,GAAGnC,GAAG,EAAE;MACd,IAAImC,CAAC,GAAGjC,GAAG,EAAEY,CAAC,GAAGA,CAAC,CAAC0D,GAAG,CAACD,CAAC,CAAC;MACzBA,CAAC,GAAGA,CAAC,CAACE,MAAM,EAAE;MACdtC,CAAC,KAAKjC,GAAG;IACX;IACA,OAAOY,CAAC;EACV;EAEA;;;;;;;;;;;;EAYQ4D,gBAAgBA,CAACC,KAAe,EAAEvD,CAAS;IACjD,MAAM;MAAEO,OAAO;MAAEG;IAAU,CAAE,GAAGL,SAAS,CAACL,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;IACvD,MAAMX,MAAM,GAAe,EAAE;IAC7B,IAAII,CAAC,GAAa6D,KAAK;IACvB,IAAIC,IAAI,GAAG9D,CAAC;IACZ,KAAK,IAAIsB,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGT,OAAO,EAAES,MAAM,EAAE,EAAE;MAC/CwC,IAAI,GAAG9D,CAAC;MACRJ,MAAM,CAACmE,IAAI,CAACD,IAAI,CAAC;MACjB;MACA,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,UAAU,EAAEd,CAAC,EAAE,EAAE;QACnC4D,IAAI,GAAGA,IAAI,CAACJ,GAAG,CAAC1D,CAAC,CAAC;QAClBJ,MAAM,CAACmE,IAAI,CAACD,IAAI,CAAC;MACnB;MACA9D,CAAC,GAAG8D,IAAI,CAACH,MAAM,EAAE;IACnB;IACA,OAAO/D,MAAM;EACf;EAEA;;;;;;EAMQqD,IAAIA,CAAC3C,CAAS,EAAE0D,WAAuB,EAAE3C,CAAS;IACxD;IACA,IAAI,CAAC,IAAI,CAACiC,EAAE,CAACb,OAAO,CAACpB,CAAC,CAAC,EAAE,MAAM,IAAIX,KAAK,CAAC,gBAAgB,CAAC;IAC1D;IACA,IAAIV,CAAC,GAAG,IAAI,CAACqD,IAAI;IACjB,IAAIY,CAAC,GAAG,IAAI,CAACb,IAAI;IACjB;IACA;IACA;IACA;IACA;IACA,MAAMc,EAAE,GAAGvD,SAAS,CAACL,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;IAClC,KAAK,IAAIe,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG4C,EAAE,CAACrD,OAAO,EAAES,MAAM,EAAE,EAAE;MAClD;MACA,MAAM;QAAEG,KAAK;QAAEE,MAAM;QAAEE,MAAM;QAAEC,KAAK;QAAEC,MAAM;QAAEC;MAAO,CAAE,GAAGZ,WAAW,CAACC,CAAC,EAAEC,MAAM,EAAE4C,EAAE,CAAC;MACpF7C,CAAC,GAAGI,KAAK;MACT,IAAII,MAAM,EAAE;QACV;QACA;QACAoC,CAAC,GAAGA,CAAC,CAACP,GAAG,CAACrE,QAAQ,CAAC0C,MAAM,EAAEiC,WAAW,CAAChC,OAAO,CAAC,CAAC,CAAC;MACnD,CAAC,MAAM;QACL;QACAhC,CAAC,GAAGA,CAAC,CAAC0D,GAAG,CAACrE,QAAQ,CAACyC,KAAK,EAAEkC,WAAW,CAACrC,MAAM,CAAC,CAAC,CAAC;MACjD;IACF;IACAqB,OAAO,CAAC3B,CAAC,CAAC;IACV;IACA;IACA;IACA,OAAO;MAAErB,CAAC;MAAEiE;IAAC,CAAE;EACjB;EAEA;;;;;EAKQE,UAAUA,CAChB7D,CAAS,EACT0D,WAAuB,EACvB3C,CAAS,EACT+C,GAAA,GAAgB,IAAI,CAACf,IAAI;IAEzB,MAAMa,EAAE,GAAGvD,SAAS,CAACL,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;IAClC,KAAK,IAAIe,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG4C,EAAE,CAACrD,OAAO,EAAES,MAAM,EAAE,EAAE;MAClD,IAAID,CAAC,KAAKnC,GAAG,EAAE,MAAM,CAAC;MACtB,MAAM;QAAEuC,KAAK;QAAEE,MAAM;QAAEE,MAAM;QAAEC;MAAK,CAAE,GAAGV,WAAW,CAACC,CAAC,EAAEC,MAAM,EAAE4C,EAAE,CAAC;MACnE7C,CAAC,GAAGI,KAAK;MACT,IAAII,MAAM,EAAE;QACV;QACA;QACA;MACF,CAAC,MAAM;QACL,MAAMtC,IAAI,GAAGyE,WAAW,CAACrC,MAAM,CAAC;QAChCyC,GAAG,GAAGA,GAAG,CAACV,GAAG,CAAC5B,KAAK,GAAGvC,IAAI,CAACE,MAAM,EAAE,GAAGF,IAAI,CAAC,CAAC,CAAC;MAC/C;IACF;IACAyD,OAAO,CAAC3B,CAAC,CAAC;IACV,OAAO+C,GAAG;EACZ;EAEQC,cAAcA,CAAC/D,CAAS,EAAEuD,KAAe,EAAES,SAA4B;IAC7E;IACA,IAAIC,IAAI,GAAG7B,gBAAgB,CAACK,GAAG,CAACc,KAAK,CAAC;IACtC,IAAI,CAACU,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI,CAACX,gBAAgB,CAACC,KAAK,EAAEvD,CAAC,CAAe;MACpD,IAAIA,CAAC,KAAK,CAAC,EAAE;QACX;QACA,IAAI,OAAOgE,SAAS,KAAK,UAAU,EAAEC,IAAI,GAAGD,SAAS,CAACC,IAAI,CAAC;QAC3D7B,gBAAgB,CAAC8B,GAAG,CAACX,KAAK,EAAEU,IAAI,CAAC;MACnC;IACF;IACA,OAAOA,IAAI;EACb;EAEAE,MAAMA,CACJZ,KAAe,EACfa,MAAc,EACdJ,SAA4B;IAE5B,MAAMhE,CAAC,GAAGuC,IAAI,CAACgB,KAAK,CAAC;IACrB,OAAO,IAAI,CAACZ,IAAI,CAAC3C,CAAC,EAAE,IAAI,CAAC+D,cAAc,CAAC/D,CAAC,EAAEuD,KAAK,EAAES,SAAS,CAAC,EAAEI,MAAM,CAAC;EACvE;EAEAC,MAAMA,CAACd,KAAe,EAAEa,MAAc,EAAEJ,SAA4B,EAAEM,IAAe;IACnF,MAAMtE,CAAC,GAAGuC,IAAI,CAACgB,KAAK,CAAC;IACrB,IAAIvD,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAACiD,aAAa,CAACM,KAAK,EAAEa,MAAM,EAAEE,IAAI,CAAC,CAAC,CAAC;IAC7D,OAAO,IAAI,CAACT,UAAU,CAAC7D,CAAC,EAAE,IAAI,CAAC+D,cAAc,CAAC/D,CAAC,EAAEuD,KAAK,EAAES,SAAS,CAAC,EAAEI,MAAM,EAAEE,IAAI,CAAC;EACnF;EAEA;EACA;EACA;EACAC,WAAWA,CAAC/B,CAAW,EAAExC,CAAS;IAChCD,SAAS,CAACC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;IACvBqC,gBAAgB,CAAC4B,GAAG,CAAC1B,CAAC,EAAExC,CAAC,CAAC;IAC1BoC,gBAAgB,CAACoC,MAAM,CAAChC,CAAC,CAAC;EAC5B;EAEAiC,QAAQA,CAACvB,GAAa;IACpB,OAAOX,IAAI,CAACW,GAAG,CAAC,KAAK,CAAC;EACxB;;AAGF;;;;AAIA,OAAM,SAAUwB,aAAaA,CAC3B7B,KAAS,EACTU,KAAQ,EACRoB,EAAU,EACVC,EAAU;EAEV,IAAId,GAAG,GAAGP,KAAK;EACf,IAAIsB,EAAE,GAAGhC,KAAK,CAACE,IAAI;EACnB,IAAI+B,EAAE,GAAGjC,KAAK,CAACE,IAAI;EACnB,OAAO4B,EAAE,GAAG/F,GAAG,IAAIgG,EAAE,GAAGhG,GAAG,EAAE;IAC3B,IAAI+F,EAAE,GAAG7F,GAAG,EAAE+F,EAAE,GAAGA,EAAE,CAACzB,GAAG,CAACU,GAAG,CAAC;IAC9B,IAAIc,EAAE,GAAG9F,GAAG,EAAEgG,EAAE,GAAGA,EAAE,CAAC1B,GAAG,CAACU,GAAG,CAAC;IAC9BA,GAAG,GAAGA,GAAG,CAACT,MAAM,EAAE;IAClBsB,EAAE,KAAK7F,GAAG;IACV8F,EAAE,KAAK9F,GAAG;EACZ;EACA,OAAO;IAAE+F,EAAE;IAAEC;EAAE,CAAE;AACnB;AAEA;;;;;;;;;;AAUA,OAAM,SAAUC,SAASA,CACvB1F,CAAK,EACL2F,MAAsB,EACtB1F,MAAW,EACX0C,OAAiB;EAEjB;EACA;EACA;EACA;EACA;EACA;EACAL,iBAAiB,CAACrC,MAAM,EAAED,CAAC,CAAC;EAC5B0C,kBAAkB,CAACC,OAAO,EAAEgD,MAAM,CAAC;EACnC,MAAMC,OAAO,GAAG3F,MAAM,CAAC4F,MAAM;EAC7B,MAAMC,OAAO,GAAGnD,OAAO,CAACkD,MAAM;EAC9B,IAAID,OAAO,KAAKE,OAAO,EAAE,MAAM,IAAI/E,KAAK,CAAC,qDAAqD,CAAC;EAC/F;EACA,MAAMgF,IAAI,GAAG/F,CAAC,CAAC0D,IAAI;EACnB,MAAM7B,KAAK,GAAG7C,MAAM,CAACQ,MAAM,CAACoG,OAAO,CAAC,CAAC;EACrC,IAAIvE,UAAU,GAAG,CAAC,CAAC,CAAC;EACpB,IAAIQ,KAAK,GAAG,EAAE,EAAER,UAAU,GAAGQ,KAAK,GAAG,CAAC,CAAC,KAClC,IAAIA,KAAK,GAAG,CAAC,EAAER,UAAU,GAAGQ,KAAK,GAAG,CAAC,CAAC,KACtC,IAAIA,KAAK,GAAG,CAAC,EAAER,UAAU,GAAG,CAAC;EAClC,MAAM2E,IAAI,GAAG/G,OAAO,CAACoC,UAAU,CAAC;EAChC,MAAM4E,OAAO,GAAG,IAAI1D,KAAK,CAAC1B,MAAM,CAACmF,IAAI,CAAC,GAAG,CAAC,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC;EACxD,MAAMI,QAAQ,GAAGhF,IAAI,CAACiF,KAAK,CAAC,CAACT,MAAM,CAACU,IAAI,GAAG,CAAC,IAAIhF,UAAU,CAAC,GAAGA,UAAU;EACxE,IAAIiF,GAAG,GAAGP,IAAI;EACd,KAAK,IAAIxF,CAAC,GAAG4F,QAAQ,EAAE5F,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAIc,UAAU,EAAE;IAC9C4E,OAAO,CAACC,IAAI,CAACH,IAAI,CAAC;IAClB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,OAAO,EAAES,CAAC,EAAE,EAAE;MAChC,MAAMxB,MAAM,GAAGpC,OAAO,CAAC4D,CAAC,CAAC;MACzB,MAAM1E,KAAK,GAAGhB,MAAM,CAAEkE,MAAM,IAAIvF,MAAM,CAACe,CAAC,CAAC,GAAIyF,IAAI,CAAC;MAClDC,OAAO,CAACpE,KAAK,CAAC,GAAGoE,OAAO,CAACpE,KAAK,CAAC,CAACkC,GAAG,CAAC9D,MAAM,CAACsG,CAAC,CAAC,CAAC;IAChD;IACA,IAAIC,IAAI,GAAGT,IAAI,CAAC,CAAC;IACjB;IACA,KAAK,IAAIQ,CAAC,GAAGN,OAAO,CAACJ,MAAM,GAAG,CAAC,EAAEY,IAAI,GAAGV,IAAI,EAAEQ,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxDE,IAAI,GAAGA,IAAI,CAAC1C,GAAG,CAACkC,OAAO,CAACM,CAAC,CAAC,CAAC;MAC3BC,IAAI,GAAGA,IAAI,CAACzC,GAAG,CAAC0C,IAAI,CAAC;IACvB;IACAH,GAAG,GAAGA,GAAG,CAACvC,GAAG,CAACyC,IAAI,CAAC;IACnB,IAAIjG,CAAC,KAAK,CAAC,EAAE,KAAK,IAAIgG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlF,UAAU,EAAEkF,CAAC,EAAE,EAAED,GAAG,GAAGA,GAAG,CAACtC,MAAM,EAAE;EACtE;EACA,OAAOsC,GAAQ;AACjB;AACA;;;;;;;AAOA,OAAM,SAAUI,mBAAmBA,CACjC1G,CAAK,EACL2F,MAAsB,EACtB1F,MAAW,EACXoB,UAAkB;EAElB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmCAX,SAAS,CAACW,UAAU,EAAEsE,MAAM,CAACU,IAAI,CAAC;EAClC/D,iBAAiB,CAACrC,MAAM,EAAED,CAAC,CAAC;EAC5B,MAAM+F,IAAI,GAAG/F,CAAC,CAAC0D,IAAI;EACnB,MAAMiD,SAAS,GAAG,CAAC,IAAItF,UAAU,GAAG,CAAC,CAAC,CAAC;EACvC,MAAMuF,MAAM,GAAGzF,IAAI,CAACC,IAAI,CAACuE,MAAM,CAACU,IAAI,GAAGhF,UAAU,CAAC,CAAC,CAAC;EACpD,MAAM2E,IAAI,GAAG/G,OAAO,CAACoC,UAAU,CAAC;EAChC,MAAMwF,MAAM,GAAG5G,MAAM,CAACG,GAAG,CAAEC,CAAI,IAAI;IACjC,MAAMyG,GAAG,GAAG,EAAE;IACd,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEkE,GAAG,GAAGpE,CAAC,EAAEE,CAAC,GAAGoG,SAAS,EAAEpG,CAAC,EAAE,EAAE;MAC3CuG,GAAG,CAAC1C,IAAI,CAACK,GAAG,CAAC;MACbA,GAAG,GAAGA,GAAG,CAACV,GAAG,CAAC1D,CAAC,CAAC;IAClB;IACA,OAAOyG,GAAG;EACZ,CAAC,CAAC;EACF,OAAQnE,OAAiB,IAAO;IAC9BD,kBAAkB,CAACC,OAAO,EAAEgD,MAAM,CAAC;IACnC,IAAIhD,OAAO,CAACkD,MAAM,GAAG5F,MAAM,CAAC4F,MAAM,EAChC,MAAM,IAAI9E,KAAK,CAAC,uDAAuD,CAAC;IAC1E,IAAI+F,GAAG,GAAGf,IAAI;IACd,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,MAAM,EAAErG,CAAC,EAAE,EAAE;MAC/B;MACA,IAAIuG,GAAG,KAAKf,IAAI,EAAE,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlF,UAAU,EAAEkF,CAAC,EAAE,EAAEO,GAAG,GAAGA,GAAG,CAAC9C,MAAM,EAAE;MACzE,MAAMxC,OAAO,GAAGhC,MAAM,CAACoH,MAAM,GAAGvF,UAAU,GAAG,CAACd,CAAC,GAAG,CAAC,IAAIc,UAAU,CAAC;MAClE,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,OAAO,CAACkD,MAAM,EAAEU,CAAC,EAAE,EAAE;QACvC,MAAM7E,CAAC,GAAGiB,OAAO,CAAC4D,CAAC,CAAC;QACpB,MAAMQ,IAAI,GAAGlG,MAAM,CAAEa,CAAC,IAAIF,OAAO,GAAIwE,IAAI,CAAC;QAC1C,IAAI,CAACe,IAAI,EAAE,SAAS,CAAC;QACrBD,GAAG,GAAGA,GAAG,CAAC/C,GAAG,CAAC8C,MAAM,CAACN,CAAC,CAAC,CAACQ,IAAI,GAAG,CAAC,CAAC,CAAC;MACpC;IACF;IACA,OAAOD,GAAG;EACZ,CAAC;AACH;AAmBA;AACA;AACA,OAAM,SAAUE,aAAaA,CAC3BC,KAAyB;EAUzB3H,aAAa,CAAC2H,KAAK,CAAC9G,EAAE,CAAC;EACvBjB,cAAc,CACZ+H,KAAK,EACL;IACEvF,CAAC,EAAE,QAAQ;IACXwF,CAAC,EAAE,QAAQ;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE;GACL,EACD;IACEC,UAAU,EAAE,eAAe;IAC3BC,WAAW,EAAE;GACd,CACF;EACD;EACA,OAAOC,MAAM,CAACC,MAAM,CAAC;IACnB,GAAGnI,OAAO,CAAC4H,KAAK,CAACvF,CAAC,EAAEuF,KAAK,CAACI,UAAU,CAAC;IACrC,GAAGJ,KAAK;IACR,GAAG;MAAE5G,CAAC,EAAE4G,KAAK,CAAC9G,EAAE,CAACsH;IAAK;GACd,CAAC;AACb;AAaA,SAASC,WAAWA,CAAIC,KAAa,EAAE/E,KAAiB,EAAEgF,IAAc;EACtE,IAAIhF,KAAK,EAAE;IACT,IAAIA,KAAK,CAAC6E,KAAK,KAAKE,KAAK,EAAE,MAAM,IAAI5G,KAAK,CAAC,gDAAgD,CAAC;IAC5FzB,aAAa,CAACsD,KAAK,CAAC;IACpB,OAAOA,KAAK;EACd,CAAC,MAAM;IACL,OAAOzD,KAAK,CAACwI,KAAK,EAAE;MAAEC;IAAI,CAAE,CAAyB;EACvD;AACF;AAGA;AACA,OAAM,SAAUC,kBAAkBA,CAChCC,IAA+B,EAC/BC,KAA0B,EAC1BC,SAAA,GAA8B,EAAE,EAChCC,MAAgB;EAEhB,IAAIA,MAAM,KAAKC,SAAS,EAAED,MAAM,GAAGH,IAAI,KAAK,SAAS;EACrD,IAAI,CAACC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,MAAM,IAAIhH,KAAK,CAAC,kBAAkB+G,IAAI,eAAe,CAAC;EAC/F,KAAK,MAAMzH,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAU,EAAE;IACxC,MAAM8H,GAAG,GAAGJ,KAAK,CAAC1H,CAAC,CAAC;IACpB,IAAI,EAAE,OAAO8H,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG5I,GAAG,CAAC,EACzC,MAAM,IAAIwB,KAAK,CAAC,SAASV,CAAC,0BAA0B,CAAC;EACzD;EACA,MAAMF,EAAE,GAAGuH,WAAW,CAACK,KAAK,CAAC1H,CAAC,EAAE2H,SAAS,CAAC7H,EAAE,EAAE8H,MAAM,CAAC;EACrD,MAAMtE,EAAE,GAAG+D,WAAW,CAACK,KAAK,CAACrG,CAAC,EAAEsG,SAAS,CAACrE,EAAE,EAAEsE,MAAM,CAAC;EACrD,MAAMG,EAAE,GAAcN,IAAI,KAAK,aAAa,GAAG,GAAG,GAAG,GAAG;EACxD,MAAMO,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAED,EAAE,CAAU;EAC7C,KAAK,MAAM/H,CAAC,IAAIgI,MAAM,EAAE;IACtB;IACA,IAAI,CAAClI,EAAE,CAAC2C,OAAO,CAACiF,KAAK,CAAC1H,CAAC,CAAC,CAAC,EACvB,MAAM,IAAIU,KAAK,CAAC,SAASV,CAAC,0CAA0C,CAAC;EACzE;EACA0H,KAAK,GAAGR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACe,MAAM,CAAC,EAAE,EAAEP,KAAK,CAAC,CAAC;EAC/C,OAAO;IAAEA,KAAK;IAAE5H,EAAE;IAAEwD;EAAE,CAAE;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}