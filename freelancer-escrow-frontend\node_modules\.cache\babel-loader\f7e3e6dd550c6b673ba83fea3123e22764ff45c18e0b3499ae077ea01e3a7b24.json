{"ast": null, "code": "var n = (e => (e[e.Unauthorized = 4100] = \"Unauthorized\", e[e.InternalError = -30001] = \"InternalError\", e))(n || {}),\n  t = Object.freeze({\n    4100: {\n      status: \"Unauthorized\",\n      message: \"The requested method and/or account has not been authorized by the user.\"\n    },\n    [-30001]: {\n      status: \"Internal error\",\n      message: \"Something went wrong within the wallet.\"\n    }\n  }),\n  r = class s extends Error {\n    constructor(e, o) {\n      super(o ?? t[e]?.message ?? \"Unknown error occurred\");\n      this.code = e, this.status = t[e]?.status ?? \"Unknown error\", this.name = \"AptosWalletError\", Object.setPrototypeOf(this, s.prototype);\n    }\n  };\nexport { n as a, t as b, r as c };", "map": {"version": 3, "names": ["n", "e", "Unauthorized", "InternalError", "t", "Object", "freeze", "status", "message", "r", "s", "Error", "constructor", "o", "code", "name", "setPrototypeOf", "prototype", "a", "b", "c"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\errors.ts"], "sourcesContent": ["export enum AptosWalletErrorCode {\n  Unauthorized = 4100,\n  InternalError = -30001\n}\n\nexport const AptosWalletErrors = Object.freeze({\n  [AptosWalletErrorCode.Unauthorized]: {\n    status: 'Unauthorized',\n    message: 'The requested method and/or account has not been authorized by the user.'\n  },\n  [AptosWalletErrorCode.InternalError]: {\n    status: 'Internal error',\n    message: 'Something went wrong within the wallet.'\n  }\n})\n\nexport class AptosWalletError extends Error {\n  readonly code: number\n  readonly status: string\n\n  constructor(code: number, message?: string) {\n    super(\n      message ??\n        AptosWalletErrors[code as keyof typeof AptosWalletErrors]?.message ??\n        'Unknown error occurred'\n    )\n    this.code = code\n    this.status =\n      AptosWalletErrors[code as keyof typeof AptosWalletErrors]?.status ?? 'Unknown error'\n    this.name = 'AptosWalletError'\n    Object.setPrototypeOf(this, AptosWalletError.prototype)\n  }\n}\n"], "mappings": "AAAO,IAAKA,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAA,CAAA,CAAAC,YAAA,GAAe,QAAf,gBACAD,CAAA,CAAAA,CAAA,CAAAE,aAAA,GAAgB,UAAhB,iBAFUF,CAAA,GAAAD,CAAA;EAKCI,CAAA,GAAoBC,MAAA,CAAOC,MAAA,CAAO;IAC5C,MAAoC;MACnCC,MAAA,EAAQ;MACRC,OAAA,EAAS;IACX;IACA,CAAC,MAAkC,GAAG;MACpCD,MAAA,EAAQ;MACRC,OAAA,EAAS;IACX;EACF,CAAC;EAEYC,CAAA,GAAN,MAAMC,CAAA,SAAyBC,KAAM;IAI1CC,YAAYX,CAAA,EAAcY,CAAA,EAAkB;MAC1C,MACEA,CAAA,IACET,CAAA,CAAkBH,CAAsC,GAAGO,OAAA,IAC3D,wBACJ;MACA,KAAKM,IAAA,GAAOb,CAAA,EACZ,KAAKM,MAAA,GACHH,CAAA,CAAkBH,CAAsC,GAAGM,MAAA,IAAU,iBACvE,KAAKQ,IAAA,GAAO,oBACZV,MAAA,CAAOW,cAAA,CAAe,MAAMN,CAAA,CAAiBO,SAAS,CACxD;IAAA;EACF;AAAA,SAAAjB,CAAA,IAAAkB,CAAA,EAAAd,CAAA,IAAAe,CAAA,EAAAV,CAAA,IAAAW,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}