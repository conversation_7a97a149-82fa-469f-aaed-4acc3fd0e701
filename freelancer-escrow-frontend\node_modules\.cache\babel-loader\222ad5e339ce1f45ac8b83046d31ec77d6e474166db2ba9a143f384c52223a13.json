{"ast": null, "code": "import { b as E } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as U } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as d } from \"./chunk-F7EMGK4M.mjs\";\nimport { d as p, f as A, g as K, h as v, i as H, j as S } from \"./chunk-LR65XHSF.mjs\";\nimport { a as h } from \"./chunk-77NXCSLY.mjs\";\nimport { a as f } from \"./chunk-A63SMUOU.mjs\";\nimport { b as s } from \"./chunk-BCUSI3N6.mjs\";\nimport { ed25519 as y } from \"@noble/curves/ed25519\";\nvar P = [237, 211, 245, 92, 26, 99, 18, 88, 214, 156, 247, 162, 222, 249, 222, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16],\n  n = class n extends E {\n    constructor(e) {\n      super();\n      let t = s.fromHexInput(e);\n      if (t.toUint8Array().length !== n.LENGTH) throw new Error(`PublicKey length should be ${n.LENGTH}`);\n      this.key = t;\n    }\n    verifySignature(e) {\n      let {\n        message: t,\n        signature: i\n      } = e;\n      if (!(i instanceof c)) return !1;\n      let o = d(t),\n        u = s.fromHexInput(o).toUint8Array(),\n        l = i.toUint8Array(),\n        x = this.key.toUint8Array();\n      return i.isCanonicalSignature() ? y.verify(l, u, x) : !1;\n    }\n    authKey() {\n      return h.fromSchemeAndBytes({\n        scheme: 0,\n        input: this.toUint8Array()\n      });\n    }\n    toUint8Array() {\n      return this.key.toUint8Array();\n    }\n    serialize(e) {\n      e.serializeBytes(this.key.toUint8Array());\n    }\n    static deserialize(e) {\n      let t = e.deserializeBytes();\n      return new n(t);\n    }\n    static isPublicKey(e) {\n      return e instanceof n;\n    }\n  };\nn.LENGTH = 32;\nvar m = n,\n  r = class r extends f {\n    constructor(e) {\n      super();\n      let t = s.fromHexInput(e);\n      if (t.toUint8Array().length !== r.LENGTH) throw new Error(`PrivateKey length should be ${r.LENGTH}`);\n      this.signingKey = t;\n    }\n    static generate() {\n      let e = y.utils.randomPrivateKey();\n      return new r(e);\n    }\n    static fromDerivationPath(e, t) {\n      if (!A(e)) throw new Error(`Invalid derivation path ${e}`);\n      return r.fromDerivationPathInner(e, S(t));\n    }\n    static fromDerivationPathInner(e, t, i = p) {\n      let {\n          key: o,\n          chainCode: u\n        } = K(r.SLIP_0010_SEED, t),\n        l = H(e).map(g => parseInt(g, 10)),\n        {\n          key: x\n        } = l.reduce((g, z) => v(g, z + i), {\n          key: o,\n          chainCode: u\n        });\n      return new r(x);\n    }\n    publicKey() {\n      let e = y.getPublicKey(this.signingKey.toUint8Array());\n      return new m(e);\n    }\n    sign(e) {\n      let t = d(e),\n        i = s.fromHexInput(t).toUint8Array(),\n        o = y.sign(i, this.signingKey.toUint8Array());\n      return new c(o);\n    }\n    toUint8Array() {\n      return this.signingKey.toUint8Array();\n    }\n    toString() {\n      return this.signingKey.toString();\n    }\n    serialize(e) {\n      e.serializeBytes(this.toUint8Array());\n    }\n    static deserialize(e) {\n      let t = e.deserializeBytes();\n      return new r(t);\n    }\n    static isPrivateKey(e) {\n      return e instanceof r;\n    }\n  };\nr.LENGTH = 32, r.SLIP_0010_SEED = \"ed25519 seed\";\nvar b = r,\n  a = class a extends U {\n    constructor(e) {\n      super();\n      let t = s.fromHexInput(e);\n      if (t.toUint8Array().length !== a.LENGTH) throw new Error(`Signature length should be ${a.LENGTH}`);\n      this.data = t;\n    }\n    toUint8Array() {\n      return this.data.toUint8Array();\n    }\n    serialize(e) {\n      e.serializeBytes(this.data.toUint8Array());\n    }\n    static deserialize(e) {\n      let t = e.deserializeBytes();\n      return new a(t);\n    }\n    isCanonicalSignature() {\n      let e = this.toUint8Array().slice(32);\n      for (let t = e.length - 1; t >= 0; t -= 1) {\n        if (e[t] < P[t]) return !0;\n        if (e[t] > P[t]) return !1;\n      }\n      return !1;\n    }\n  };\na.LENGTH = 64;\nvar c = a;\nexport { m as a, b, c };", "map": {"version": 3, "names": ["ed25519", "y", "P", "n", "E", "constructor", "e", "t", "s", "fromHexInput", "toUint8Array", "length", "LENGTH", "Error", "key", "verifySignature", "message", "signature", "i", "c", "o", "d", "u", "l", "x", "isCanonicalSignature", "verify", "auth<PERSON><PERSON>", "h", "fromSchemeAndBytes", "scheme", "input", "serialize", "serializeBytes", "deserialize", "deserializeBytes", "isPublicKey", "m", "r", "f", "<PERSON><PERSON><PERSON>", "generate", "utils", "randomPrivateKey", "fromDerivationPath", "A", "fromDerivationPathInner", "S", "p", "chainCode", "K", "SLIP_0010_SEED", "H", "map", "g", "parseInt", "reduce", "z", "v", "public<PERSON>ey", "getPublicKey", "sign", "toString", "isPrivateKey", "b", "a", "U", "data", "slice"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\ed25519.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { ed25519 } from \"@noble/curves/ed25519\";\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Hex } from \"../hex\";\nimport { HexInput, SigningScheme as AuthenticationKeyScheme } from \"../../types\";\nimport { CKDPriv, deriveKey, HARDENED_OFFSET, isValidHardenedPath, mnemonicToSeed, splitPath } from \"./hdKey\";\nimport { PrivateKey } from \"./privateKey\";\nimport { AccountPublicKey, VerifySignatureArgs } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { convertSigningMessage } from \"./utils\";\n\n/**\n * L is the value that greater than or equal to will produce a non-canonical signature, and must be rejected\n */\nconst L: number[] = [\n  0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10,\n];\n\n/**\n * Represents the public key of an Ed25519 key pair.\n *\n * Since [AIP-55](https://github.com/aptos-foundation/AIPs/pull/263) Aptos supports\n * `Legacy` and `Unified` authentication keys.\n *\n * Ed25519 scheme is represented in the SDK as `Legacy authentication key` and also\n * as `AnyPublicKey` that represents any `Unified authentication key`\n */\nexport class Ed25519PublicKey extends AccountPublicKey {\n  /**\n   * Length of an Ed25519 public key\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * Bytes of the public key\n   * @private\n   */\n  private readonly key: Hex;\n\n  /**\n   * Create a new PublicKey instance from a Uint8Array or String.\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   */\n  constructor(hexInput: HexInput) {\n    super();\n\n    const hex = Hex.fromHexInput(hexInput);\n    if (hex.toUint8Array().length !== Ed25519PublicKey.LENGTH) {\n      throw new Error(`PublicKey length should be ${Ed25519PublicKey.LENGTH}`);\n    }\n    this.key = hex;\n  }\n\n  // region AccountPublicKey\n\n  /**\n   * Verifies a signed data with a public key\n   * @param args.message a signed message as a Hex string or Uint8Array\n   * @param args.signature the signature of the message\n   */\n  verifySignature(args: VerifySignatureArgs): boolean {\n    const { message, signature } = args;\n    if (!(signature instanceof Ed25519Signature)) {\n      return false;\n    }\n    const messageToVerify = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToVerify).toUint8Array();\n    const signatureBytes = signature.toUint8Array();\n    const publicKeyBytes = this.key.toUint8Array();\n    // Also verify malleability\n    if (!signature.isCanonicalSignature()) {\n      return false;\n    }\n\n    return ed25519.verify(signatureBytes, messageBytes, publicKeyBytes);\n  }\n\n  authKey(): AuthenticationKey {\n    return AuthenticationKey.fromSchemeAndBytes({\n      scheme: AuthenticationKeyScheme.Ed25519,\n      input: this.toUint8Array(),\n    });\n  }\n\n  /**\n   * Get the public key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the public key\n   */\n  toUint8Array(): Uint8Array {\n    return this.key.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.key.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Ed25519PublicKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Ed25519PublicKey(bytes);\n  }\n\n  // endregion\n\n  /**\n   * @deprecated use `instanceof Ed25519PublicKey` instead.\n   */\n  static isPublicKey(publicKey: AccountPublicKey): publicKey is Ed25519PublicKey {\n    return publicKey instanceof Ed25519PublicKey;\n  }\n}\n\n/**\n * Represents the private key of an Ed25519 key pair.\n */\nexport class Ed25519PrivateKey extends Serializable implements PrivateKey {\n  /**\n   * Length of an Ed25519 private key\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The Ed25519 key seed to use for BIP-32 compatibility\n   * See more {@link https://github.com/satoshilabs/slips/blob/master/slip-0010.md}\n   */\n  static readonly SLIP_0010_SEED = \"ed25519 seed\";\n\n  /**\n   * The Ed25519 signing key\n   * @private\n   */\n  private readonly signingKey: Hex;\n\n  // region Constructors\n\n  /**\n   * Create a new PrivateKey instance from a Uint8Array or String.\n   *\n   * @param hexInput HexInput (string or Uint8Array)\n   */\n  constructor(hexInput: HexInput) {\n    super();\n\n    const privateKeyHex = Hex.fromHexInput(hexInput);\n    if (privateKeyHex.toUint8Array().length !== Ed25519PrivateKey.LENGTH) {\n      throw new Error(`PrivateKey length should be ${Ed25519PrivateKey.LENGTH}`);\n    }\n\n    // Create keyPair from Private key in Uint8Array format\n    this.signingKey = privateKeyHex;\n  }\n\n  /**\n   * Generate a new random private key.\n   *\n   * @returns Ed25519PrivateKey\n   */\n  static generate(): Ed25519PrivateKey {\n    const keyPair = ed25519.utils.randomPrivateKey();\n    return new Ed25519PrivateKey(keyPair);\n  }\n\n  /**\n   * Derives a private key from a mnemonic seed phrase.\n   *\n   * To derive multiple keys from the same phrase, change the path\n   *\n   * IMPORTANT: Ed25519 supports hardened derivation only (since it lacks a key homomorphism,\n   * so non-hardened derivation cannot work)\n   *\n   * @param path the BIP44 path\n   * @param mnemonics the mnemonic seed phrase\n   */\n  static fromDerivationPath(path: string, mnemonics: string): Ed25519PrivateKey {\n    if (!isValidHardenedPath(path)) {\n      throw new Error(`Invalid derivation path ${path}`);\n    }\n    return Ed25519PrivateKey.fromDerivationPathInner(path, mnemonicToSeed(mnemonics));\n  }\n\n  /**\n   * A private inner function so we can separate from the main fromDerivationPath() method\n   * to add tests to verify we create the keys correctly.\n   *\n   * @param path the BIP44 path\n   * @param seed the seed phrase created by the mnemonics\n   * @param offset the offset used for key derivation, defaults to 0x80000000\n   * @returns\n   */\n  private static fromDerivationPathInner(path: string, seed: Uint8Array, offset = HARDENED_OFFSET): Ed25519PrivateKey {\n    const { key, chainCode } = deriveKey(Ed25519PrivateKey.SLIP_0010_SEED, seed);\n\n    const segments = splitPath(path).map((el) => parseInt(el, 10));\n\n    // Derive the child key based on the path\n    const { key: privateKey } = segments.reduce((parentKeys, segment) => CKDPriv(parentKeys, segment + offset), {\n      key,\n      chainCode,\n    });\n    return new Ed25519PrivateKey(privateKey);\n  }\n\n  // endregion\n\n  // region PrivateKey\n\n  /**\n   * Derive the Ed25519PublicKey for this private key.\n   *\n   * @returns Ed25519PublicKey\n   */\n  publicKey(): Ed25519PublicKey {\n    const bytes = ed25519.getPublicKey(this.signingKey.toUint8Array());\n    return new Ed25519PublicKey(bytes);\n  }\n\n  /**\n   * Sign the given message with the private key.\n   *\n   * @param message a message as a string or Uint8Array\n   * @returns Signature\n   */\n  sign(message: HexInput): Ed25519Signature {\n    const messageToSign = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToSign).toUint8Array();\n    const signatureBytes = ed25519.sign(messageBytes, this.signingKey.toUint8Array());\n    return new Ed25519Signature(signatureBytes);\n  }\n\n  /**\n   * Get the private key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the private key\n   */\n  toUint8Array(): Uint8Array {\n    return this.signingKey.toUint8Array();\n  }\n\n  /**\n   * Get the private key as a hex string with the 0x prefix.\n   *\n   * @returns string representation of the private key\n   */\n  toString(): string {\n    return this.signingKey.toString();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Ed25519PrivateKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Ed25519PrivateKey(bytes);\n  }\n\n  // endregion\n\n  /**\n   * @deprecated use `instanceof Ed25519PrivateKey` instead.\n   */\n  static isPrivateKey(privateKey: PrivateKey): privateKey is Ed25519PrivateKey {\n    return privateKey instanceof Ed25519PrivateKey;\n  }\n}\n\n/**\n * A signature of a message signed using an Ed25519 private key\n */\nexport class Ed25519Signature extends Signature {\n  /**\n   * Length of an Ed25519 signature\n   */\n  static readonly LENGTH = 64;\n\n  /**\n   * The signature bytes\n   * @private\n   */\n  private readonly data: Hex;\n\n  // region Constructors\n\n  constructor(hexInput: HexInput) {\n    super();\n    const data = Hex.fromHexInput(hexInput);\n    if (data.toUint8Array().length !== Ed25519Signature.LENGTH) {\n      throw new Error(`Signature length should be ${Ed25519Signature.LENGTH}`);\n    }\n    this.data = data;\n  }\n\n  // endregion\n\n  // region Signature\n\n  toUint8Array(): Uint8Array {\n    return this.data.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.data.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Ed25519Signature {\n    const bytes = deserializer.deserializeBytes();\n    return new Ed25519Signature(bytes);\n  }\n\n  /**\n   * Checks if an ED25519 signature is non-canonical.\n   *\n   * Comes from Aptos Core\n   * https://github.com/aptos-labs/aptos-core/blob/main/crates/aptos-crypto/src/ed25519/ed25519_sigs.rs#L47-L85\n   */\n  isCanonicalSignature(): boolean {\n    const s = this.toUint8Array().slice(32);\n\n    for (let i = s.length - 1; i >= 0; i -= 1) {\n      if (s[i] < L[i]) {\n        return true;\n      }\n      if (s[i] > L[i]) {\n        return false;\n      }\n    }\n    // As this stage S == L which implies a non-canonical S.\n    return false;\n  }\n\n  // endregion\n}\n"], "mappings": ";;;;;;;AAGA,SAASA,OAAA,IAAAC,CAAA,QAAe;AAexB,IAAMC,CAAA,GAAc,CAClB,KAAM,KAAM,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAM,GAAM,GAAM,GAC5G,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,EAC1E;EAWaC,CAAA,GAAN,MAAMA,CAAA,SAAyBC,CAAiB;IAiBrDC,YAAYC,CAAA,EAAoB;MAC9B,MAAM;MAEN,IAAMC,CAAA,GAAMC,CAAA,CAAIC,YAAA,CAAaH,CAAQ;MACrC,IAAIC,CAAA,CAAIG,YAAA,CAAa,EAAEC,MAAA,KAAWR,CAAA,CAAiBS,MAAA,EACjD,MAAM,IAAIC,KAAA,CAAM,8BAA8BV,CAAA,CAAiBS,MAAM,EAAE;MAEzE,KAAKE,GAAA,GAAMP,CACb;IAAA;IASAQ,gBAAgBT,CAAA,EAAoC;MAClD,IAAM;QAAEU,OAAA,EAAAT,CAAA;QAASU,SAAA,EAAAC;MAAU,IAAIZ,CAAA;MAC/B,IAAI,EAAEY,CAAA,YAAqBC,CAAA,GACzB,OAAO;MAET,IAAMC,CAAA,GAAkBC,CAAA,CAAsBd,CAAO;QAC/Ce,CAAA,GAAed,CAAA,CAAIC,YAAA,CAAaW,CAAe,EAAEV,YAAA,CAAa;QAC9Da,CAAA,GAAiBL,CAAA,CAAUR,YAAA,CAAa;QACxCc,CAAA,GAAiB,KAAKV,GAAA,CAAIJ,YAAA,CAAa;MAE7C,OAAKQ,CAAA,CAAUO,oBAAA,CAAqB,IAI7BxB,CAAA,CAAQyB,MAAA,CAAOH,CAAA,EAAgBD,CAAA,EAAcE,CAAc,IAHzD,EAIX;IAAA;IAEAG,QAAA,EAA6B;MAC3B,OAAOC,CAAA,CAAkBC,kBAAA,CAAmB;QAC1CC,MAAA;QACAC,KAAA,EAAO,KAAKrB,YAAA,CAAa;MAC3B,CAAC,CACH;IAAA;IAOAA,aAAA,EAA2B;MACzB,OAAO,KAAKI,GAAA,CAAIJ,YAAA,CAAa,CAC/B;IAAA;IAMAsB,UAAU1B,CAAA,EAA8B;MACtCA,CAAA,CAAW2B,cAAA,CAAe,KAAKnB,GAAA,CAAIJ,YAAA,CAAa,CAAC,CACnD;IAAA;IAEA,OAAOwB,YAAY5B,CAAA,EAA8C;MAC/D,IAAMC,CAAA,GAAQD,CAAA,CAAa6B,gBAAA,CAAiB;MAC5C,OAAO,IAAIhC,CAAA,CAAiBI,CAAK,CACnC;IAAA;IAOA,OAAO6B,YAAY9B,CAAA,EAA4D;MAC7E,OAAOA,CAAA,YAAqBH,CAC9B;IAAA;EACF;AAxFaA,CAAA,CAIKS,MAAA,GAAiB;AAJ5B,IAAMyB,CAAA,GAANlC,CAAA;EA6FMmC,CAAA,GAAN,MAAMA,CAAA,SAA0BC,CAAmC;IAyBxElC,YAAYC,CAAA,EAAoB;MAC9B,MAAM;MAEN,IAAMC,CAAA,GAAgBC,CAAA,CAAIC,YAAA,CAAaH,CAAQ;MAC/C,IAAIC,CAAA,CAAcG,YAAA,CAAa,EAAEC,MAAA,KAAW2B,CAAA,CAAkB1B,MAAA,EAC5D,MAAM,IAAIC,KAAA,CAAM,+BAA+ByB,CAAA,CAAkB1B,MAAM,EAAE;MAI3E,KAAK4B,UAAA,GAAajC,CACpB;IAAA;IAOA,OAAOkC,SAAA,EAA8B;MACnC,IAAMnC,CAAA,GAAUL,CAAA,CAAQyC,KAAA,CAAMC,gBAAA,CAAiB;MAC/C,OAAO,IAAIL,CAAA,CAAkBhC,CAAO,CACtC;IAAA;IAaA,OAAOsC,mBAAmBtC,CAAA,EAAcC,CAAA,EAAsC;MAC5E,IAAI,CAACsC,CAAA,CAAoBvC,CAAI,GAC3B,MAAM,IAAIO,KAAA,CAAM,2BAA2BP,CAAI,EAAE;MAEnD,OAAOgC,CAAA,CAAkBQ,uBAAA,CAAwBxC,CAAA,EAAMyC,CAAA,CAAexC,CAAS,CAAC,CAClF;IAAA;IAWA,OAAeuC,wBAAwBxC,CAAA,EAAcC,CAAA,EAAkBW,CAAA,GAAS8B,CAAA,EAAoC;MAClH,IAAM;UAAElC,GAAA,EAAAM,CAAA;UAAK6B,SAAA,EAAA3B;QAAU,IAAI4B,CAAA,CAAUZ,CAAA,CAAkBa,cAAA,EAAgB5C,CAAI;QAErEgB,CAAA,GAAW6B,CAAA,CAAU9C,CAAI,EAAE+C,GAAA,CAAKC,CAAA,IAAOC,QAAA,CAASD,CAAA,EAAI,EAAE,CAAC;QAGvD;UAAExC,GAAA,EAAKU;QAAW,IAAID,CAAA,CAASiC,MAAA,CAAO,CAACF,CAAA,EAAYG,CAAA,KAAYC,CAAA,CAAQJ,CAAA,EAAYG,CAAA,GAAUvC,CAAM,GAAG;UAC1GJ,GAAA,EAAAM,CAAA;UACA6B,SAAA,EAAA3B;QACF,CAAC;MACD,OAAO,IAAIgB,CAAA,CAAkBd,CAAU,CACzC;IAAA;IAWAmC,UAAA,EAA8B;MAC5B,IAAMrD,CAAA,GAAQL,CAAA,CAAQ2D,YAAA,CAAa,KAAKpB,UAAA,CAAW9B,YAAA,CAAa,CAAC;MACjE,OAAO,IAAI2B,CAAA,CAAiB/B,CAAK,CACnC;IAAA;IAQAuD,KAAKvD,CAAA,EAAqC;MACxC,IAAMC,CAAA,GAAgBc,CAAA,CAAsBf,CAAO;QAC7CY,CAAA,GAAeV,CAAA,CAAIC,YAAA,CAAaF,CAAa,EAAEG,YAAA,CAAa;QAC5DU,CAAA,GAAiBnB,CAAA,CAAQ4D,IAAA,CAAK3C,CAAA,EAAc,KAAKsB,UAAA,CAAW9B,YAAA,CAAa,CAAC;MAChF,OAAO,IAAIS,CAAA,CAAiBC,CAAc,CAC5C;IAAA;IAOAV,aAAA,EAA2B;MACzB,OAAO,KAAK8B,UAAA,CAAW9B,YAAA,CAAa,CACtC;IAAA;IAOAoD,SAAA,EAAmB;MACjB,OAAO,KAAKtB,UAAA,CAAWsB,QAAA,CAAS,CAClC;IAAA;IAMA9B,UAAU1B,CAAA,EAA8B;MACtCA,CAAA,CAAW2B,cAAA,CAAe,KAAKvB,YAAA,CAAa,CAAC,CAC/C;IAAA;IAEA,OAAOwB,YAAY5B,CAAA,EAA+C;MAChE,IAAMC,CAAA,GAAQD,CAAA,CAAa6B,gBAAA,CAAiB;MAC5C,OAAO,IAAIG,CAAA,CAAkB/B,CAAK,CACpC;IAAA;IAOA,OAAOwD,aAAazD,CAAA,EAAyD;MAC3E,OAAOA,CAAA,YAAsBgC,CAC/B;IAAA;EACF;AAzJaA,CAAA,CAIK1B,MAAA,GAAiB,IAJtB0B,CAAA,CAUKa,cAAA,GAAiB;AAV5B,IAAMa,CAAA,GAAN1B,CAAA;EA8JM2B,CAAA,GAAN,MAAMA,CAAA,SAAyBC,CAAU;IAc9C7D,YAAYC,CAAA,EAAoB;MAC9B,MAAM;MACN,IAAMC,CAAA,GAAOC,CAAA,CAAIC,YAAA,CAAaH,CAAQ;MACtC,IAAIC,CAAA,CAAKG,YAAA,CAAa,EAAEC,MAAA,KAAWsD,CAAA,CAAiBrD,MAAA,EAClD,MAAM,IAAIC,KAAA,CAAM,8BAA8BoD,CAAA,CAAiBrD,MAAM,EAAE;MAEzE,KAAKuD,IAAA,GAAO5D,CACd;IAAA;IAMAG,aAAA,EAA2B;MACzB,OAAO,KAAKyD,IAAA,CAAKzD,YAAA,CAAa,CAChC;IAAA;IAMAsB,UAAU1B,CAAA,EAA8B;MACtCA,CAAA,CAAW2B,cAAA,CAAe,KAAKkC,IAAA,CAAKzD,YAAA,CAAa,CAAC,CACpD;IAAA;IAEA,OAAOwB,YAAY5B,CAAA,EAA8C;MAC/D,IAAMC,CAAA,GAAQD,CAAA,CAAa6B,gBAAA,CAAiB;MAC5C,OAAO,IAAI8B,CAAA,CAAiB1D,CAAK,CACnC;IAAA;IAQAkB,qBAAA,EAAgC;MAC9B,IAAMnB,CAAA,GAAI,KAAKI,YAAA,CAAa,EAAE0D,KAAA,CAAM,EAAE;MAEtC,SAAS7D,CAAA,GAAID,CAAA,CAAEK,MAAA,GAAS,GAAGJ,CAAA,IAAK,GAAGA,CAAA,IAAK,GAAG;QACzC,IAAID,CAAA,CAAEC,CAAC,IAAIL,CAAA,CAAEK,CAAC,GACZ,OAAO;QAET,IAAID,CAAA,CAAEC,CAAC,IAAIL,CAAA,CAAEK,CAAC,GACZ,OAAO,EAEX;MAAA;MAEA,OAAO,EACT;IAAA;EAGF;AAlEa0D,CAAA,CAIKrD,MAAA,GAAS;AAJpB,IAAMO,CAAA,GAAN8C,CAAA;AAAA,SAAA5B,CAAA,IAAA4B,CAAA,EAAAD,CAAA,EAAA7C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}