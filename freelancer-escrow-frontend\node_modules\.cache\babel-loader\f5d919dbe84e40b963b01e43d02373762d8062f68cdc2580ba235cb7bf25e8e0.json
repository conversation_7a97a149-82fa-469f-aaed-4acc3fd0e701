{"ast": null, "code": "import { a as y } from \"./chunk-SFUTXSDK.mjs\";\nimport { e as o } from \"./chunk-AYKZA676.mjs\";\nimport { a as g, b as u } from \"./chunk-L22R3OIW.mjs\";\nvar l = class a {\n  constructor(i) {\n    let {\n      multiKey: s,\n      signers: e\n    } = i;\n    this.publicKey = s, this.signingScheme = 3, this.accountAddress = this.publicKey.authKey().derivedAddress();\n    let r = [];\n    for (let t of e) r.push(this.publicKey.getIndex(t.publicKey));\n    let n = e.map((t, c) => [t, r[c]]);\n    n.sort((t, c) => t[1] - c[1]), this.signers = n.map(t => t[0]), this.signerIndicies = n.map(t => t[1]), this.signaturesBitmap = this.publicKey.createBitmap({\n      bits: r\n    });\n  }\n  static fromPublicKeysAndSigners(i) {\n    let {\n        publicKeys: s,\n        signaturesRequired: e,\n        signers: r\n      } = i,\n      n = new g({\n        publicKeys: s,\n        signaturesRequired: e\n      });\n    return new a({\n      multiKey: n,\n      signers: r\n    });\n  }\n  static isMultiKeySigner(i) {\n    return i instanceof a;\n  }\n  signWithAuthenticator(i) {\n    return new o(this.publicKey, this.sign(i));\n  }\n  signTransactionWithAuthenticator(i) {\n    return new o(this.publicKey, this.signTransaction(i));\n  }\n  async waitForProofFetch() {\n    let s = this.signers.filter(e => e instanceof y).map(async e => e.waitForProofFetch());\n    await Promise.all(s);\n  }\n  sign(i) {\n    let s = [];\n    for (let e of this.signers) s.push(e.sign(i));\n    return new u({\n      signatures: s,\n      bitmap: this.signaturesBitmap\n    });\n  }\n  signTransaction(i) {\n    let s = [];\n    for (let e of this.signers) s.push(e.signTransaction(i));\n    return new u({\n      signatures: s,\n      bitmap: this.signaturesBitmap\n    });\n  }\n  verifySignature(i) {\n    let {\n      message: s,\n      signature: e\n    } = i;\n    if (!this.signerIndicies.every((n, t) => t === 0 || n >= this.signerIndicies[t - 1])) return !1;\n    for (let n = 0; n < e.signatures.length; n += 1) {\n      let t = e.signatures[n];\n      if (!this.publicKey.publicKeys[this.signerIndicies[n]].verifySignature({\n        message: s,\n        signature: t\n      })) return !1;\n    }\n    return !0;\n  }\n};\nexport { l as a };", "map": {"version": 3, "names": ["l", "a", "constructor", "i", "multiKey", "s", "signers", "e", "public<PERSON>ey", "signingScheme", "accountAddress", "auth<PERSON><PERSON>", "derivedAddress", "r", "t", "push", "getIndex", "n", "map", "c", "sort", "signerIndicies", "signaturesBitmap", "createBitmap", "bits", "fromPublicKeysAndSigners", "publicKeys", "signaturesRequired", "g", "isMultiKeySigner", "signWithAuthenticator", "o", "sign", "signTransactionWithAuthenticator", "signTransaction", "waitForProofFetch", "filter", "y", "Promise", "all", "u", "signatures", "bitmap", "verifySignature", "message", "signature", "every", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\account\\MultiKeyAccount.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Account } from \"./Account\";\nimport { MultiKey, MultiKeySignature, PublicKey } from \"../core/crypto\";\nimport { AccountAddress } from \"../core/accountAddress\";\nimport { HexInput, SigningScheme } from \"../types\";\nimport { AccountAuthenticatorMultiKey } from \"../transactions/authenticator/account\";\nimport { AnyRawTransaction } from \"../transactions/types\";\nimport { KeylessAccount } from \"./KeylessAccount\";\n\nexport interface VerifyMultiKeySignatureArgs {\n  message: HexInput;\n  signature: MultiKeySignature;\n}\n\n/**\n * Signer implementation for the MultiKey authentication scheme.\n *\n * This accounts to use a M of N signing scheme. M and N are specified in the {@link MultiKey}\n * It signs messages via the array of M number of Accounts that individually correspond to a public key in the {@link MultiKey}.\n *\n * Note: Generating a signer instance does not create the account on-chain.\n */\nexport class MultiKeyAccount implements Account {\n  /**\n   * Public key associated with the account\n   */\n  readonly publicKey: MultiKey;\n\n  /**\n   * Account address associated with the account\n   */\n  readonly accountAddress: AccountAddress;\n\n  /**\n   * Signing scheme used to sign transactions\n   */\n  readonly signingScheme: SigningScheme;\n\n  /**\n   * The signers used to sign messages.  These signers should correspond to public keys in the\n   * MultiKeyAccount's public key.  The number of signers should be equal or greater\n   * than this.publicKey.signaturesRequired\n   */\n  readonly signers: Account[];\n\n  /**\n   * An array of indicies where for signer[i], signerIndicies[i] is the index of the corresponding public key in\n   * publicKey.publicKeys.  Used to derive the right public key to use for verification.\n   */\n  readonly signerIndicies: number[];\n\n  readonly signaturesBitmap: Uint8Array;\n\n  /**\n   * constructor for MultiKeyAccount\n   *\n   * @param args.multiKey the multikey of the account which consists of N public keys and a number M which is\n   * the number of required signatures.\n   * @param args.signers an array of M signers that will be used to sign the transaction\n   * @returns MultiKeyAccount\n   */\n  constructor(args: { multiKey: MultiKey; signers: Account[] }) {\n    const { multiKey, signers } = args;\n\n    this.publicKey = multiKey;\n    this.signingScheme = SigningScheme.MultiKey;\n\n    this.accountAddress = this.publicKey.authKey().derivedAddress();\n\n    // Get the index of each respective signer in the bitmap\n    const bitPositions: number[] = [];\n    for (const signer of signers) {\n      bitPositions.push(this.publicKey.getIndex(signer.publicKey));\n    }\n    // Zip signers and bit positions and sort signers by bit positions in order\n    // to ensure the signature is signed in ascending order according to the bitmap.\n    // Authentication on chain will fail otherwise.\n    const signersAndBitPosition: [Account, number][] = signers.map((signer, index) => [signer, bitPositions[index]]);\n    signersAndBitPosition.sort((a, b) => a[1] - b[1]);\n    this.signers = signersAndBitPosition.map((value) => value[0]);\n    this.signerIndicies = signersAndBitPosition.map((value) => value[1]);\n    this.signaturesBitmap = this.publicKey.createBitmap({ bits: bitPositions });\n  }\n\n  /**\n   * Static constructor for MultiKeyAccount\n   *\n   * @param args.publicKeys the N public keys of the MultiKeyAccount\n   * @param args.signaturesRequired the number of signatures required\n   * @param args.signers an array of M signers that will be used to sign the transaction\n   * @returns MultiKeyAccount\n   */\n  static fromPublicKeysAndSigners(args: {\n    publicKeys: PublicKey[];\n    signaturesRequired: number;\n    signers: Account[];\n  }): MultiKeyAccount {\n    const { publicKeys, signaturesRequired, signers } = args;\n    const multiKey = new MultiKey({ publicKeys, signaturesRequired });\n    return new MultiKeyAccount({ multiKey, signers });\n  }\n\n  static isMultiKeySigner(account: Account): account is MultiKeyAccount {\n    return account instanceof MultiKeyAccount;\n  }\n\n  /**\n   * Sign a message using the account's signers.\n   * @param message the signing message, as binary input\n   * @return the AccountAuthenticator containing the signature, together with the account's public key\n   */\n  signWithAuthenticator(message: HexInput): AccountAuthenticatorMultiKey {\n    return new AccountAuthenticatorMultiKey(this.publicKey, this.sign(message));\n  }\n\n  /**\n   * Sign a transaction using the account's signers.\n   * @param transaction the raw transaction\n   * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key\n   */\n  signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorMultiKey {\n    return new AccountAuthenticatorMultiKey(this.publicKey, this.signTransaction(transaction));\n  }\n\n  /**\n   * Waits for any proofs on any KeylessAccount signers to be fetched. If the proof is fetched a syncronously, call this\n   * to ensure signing with the KeylessAccount does not fail as the proof must be ready.\n   * @return\n   */\n  async waitForProofFetch() {\n    const keylessSigners = this.signers.filter((signer) => signer instanceof KeylessAccount) as KeylessAccount[];\n    const promises = keylessSigners.map(async (signer) => signer.waitForProofFetch());\n    await Promise.all(promises);\n  }\n\n  /**\n   * Sign the given message using the MultiKeyAccount's signers\n   * @param message in HexInput format\n   * @returns MultiKeySignature\n   */\n  sign(data: HexInput): MultiKeySignature {\n    const signatures = [];\n    for (const signer of this.signers) {\n      signatures.push(signer.sign(data));\n    }\n    return new MultiKeySignature({ signatures, bitmap: this.signaturesBitmap });\n  }\n\n  /**\n   * Sign the given transaction using the MultiKeyAccount's signers\n   * @param transaction the transaction to be signed\n   * @returns MultiKeySignature\n   */\n  signTransaction(transaction: AnyRawTransaction): MultiKeySignature {\n    const signatures = [];\n    for (const signer of this.signers) {\n      signatures.push(signer.signTransaction(transaction));\n    }\n    return new MultiKeySignature({ signatures, bitmap: this.signaturesBitmap });\n  }\n\n  /**\n   * Verify the given message and signature with the public key.\n   *\n   * @param args.message raw message data in HexInput format\n   * @param args.signatures signed message MultiKeySignature\n   * @returns boolean\n   */\n  verifySignature(args: VerifyMultiKeySignatureArgs): boolean {\n    const { message, signature } = args;\n    const isSignerIndiciesSorted = this.signerIndicies.every(\n      (value, i) => i === 0 || value >= this.signerIndicies[i - 1],\n    );\n    if (!isSignerIndiciesSorted) {\n      return false;\n    }\n    for (let i = 0; i < signature.signatures.length; i += 1) {\n      const singleSignature = signature.signatures[i];\n      const publicKey = this.publicKey.publicKeys[this.signerIndicies[i]];\n      if (!publicKey.verifySignature({ message, signature: singleSignature })) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n"], "mappings": ";;;AAwBO,IAAMA,CAAA,GAAN,MAAMC,CAAmC;EAuC9CC,YAAYC,CAAA,EAAkD;IAC5D,IAAM;MAAEC,QAAA,EAAAC,CAAA;MAAUC,OAAA,EAAAC;IAAQ,IAAIJ,CAAA;IAE9B,KAAKK,SAAA,GAAYH,CAAA,EACjB,KAAKI,aAAA,GAAgB,GAErB,KAAKC,cAAA,GAAiB,KAAKF,SAAA,CAAUG,OAAA,CAAQ,EAAEC,cAAA,CAAe;IAG9D,IAAMC,CAAA,GAAyB,EAAC;IAChC,SAAWC,CAAA,IAAUP,CAAA,EACnBM,CAAA,CAAaE,IAAA,CAAK,KAAKP,SAAA,CAAUQ,QAAA,CAASF,CAAA,CAAON,SAAS,CAAC;IAK7D,IAAMS,CAAA,GAA6CV,CAAA,CAAQW,GAAA,CAAI,CAACJ,CAAA,EAAQK,CAAA,KAAU,CAACL,CAAA,EAAQD,CAAA,CAAaM,CAAK,CAAC,CAAC;IAC/GF,CAAA,CAAsBG,IAAA,CAAK,CAACN,CAAA,EAAGK,CAAA,KAAML,CAAA,CAAE,CAAC,IAAIK,CAAA,CAAE,CAAC,CAAC,GAChD,KAAKb,OAAA,GAAUW,CAAA,CAAsBC,GAAA,CAAKJ,CAAA,IAAUA,CAAA,CAAM,CAAC,CAAC,GAC5D,KAAKO,cAAA,GAAiBJ,CAAA,CAAsBC,GAAA,CAAKJ,CAAA,IAAUA,CAAA,CAAM,CAAC,CAAC,GACnE,KAAKQ,gBAAA,GAAmB,KAAKd,SAAA,CAAUe,YAAA,CAAa;MAAEC,IAAA,EAAMX;IAAa,CAAC,CAC5E;EAAA;EAUA,OAAOY,yBAAyBtB,CAAA,EAIZ;IAClB,IAAM;QAAEuB,UAAA,EAAArB,CAAA;QAAYsB,kBAAA,EAAApB,CAAA;QAAoBD,OAAA,EAAAO;MAAQ,IAAIV,CAAA;MAC9Cc,CAAA,GAAW,IAAIW,CAAA,CAAS;QAAEF,UAAA,EAAArB,CAAA;QAAYsB,kBAAA,EAAApB;MAAmB,CAAC;IAChE,OAAO,IAAIN,CAAA,CAAgB;MAAEG,QAAA,EAAAa,CAAA;MAAUX,OAAA,EAAAO;IAAQ,CAAC,CAClD;EAAA;EAEA,OAAOgB,iBAAiB1B,CAAA,EAA8C;IACpE,OAAOA,CAAA,YAAmBF,CAC5B;EAAA;EAOA6B,sBAAsB3B,CAAA,EAAiD;IACrE,OAAO,IAAI4B,CAAA,CAA6B,KAAKvB,SAAA,EAAW,KAAKwB,IAAA,CAAK7B,CAAO,CAAC,CAC5E;EAAA;EAOA8B,iCAAiC9B,CAAA,EAA8D;IAC7F,OAAO,IAAI4B,CAAA,CAA6B,KAAKvB,SAAA,EAAW,KAAK0B,eAAA,CAAgB/B,CAAW,CAAC,CAC3F;EAAA;EAOA,MAAMgC,kBAAA,EAAoB;IAExB,IAAM9B,CAAA,GADiB,KAAKC,OAAA,CAAQ8B,MAAA,CAAQ7B,CAAA,IAAWA,CAAA,YAAkB8B,CAAc,EACvDnB,GAAA,CAAI,MAAOX,CAAA,IAAWA,CAAA,CAAO4B,iBAAA,CAAkB,CAAC;IAChF,MAAMG,OAAA,CAAQC,GAAA,CAAIlC,CAAQ,CAC5B;EAAA;EAOA2B,KAAK7B,CAAA,EAAmC;IACtC,IAAME,CAAA,GAAa,EAAC;IACpB,SAAWE,CAAA,IAAU,KAAKD,OAAA,EACxBD,CAAA,CAAWU,IAAA,CAAKR,CAAA,CAAOyB,IAAA,CAAK7B,CAAI,CAAC;IAEnC,OAAO,IAAIqC,CAAA,CAAkB;MAAEC,UAAA,EAAApC,CAAA;MAAYqC,MAAA,EAAQ,KAAKpB;IAAiB,CAAC,CAC5E;EAAA;EAOAY,gBAAgB/B,CAAA,EAAmD;IACjE,IAAME,CAAA,GAAa,EAAC;IACpB,SAAWE,CAAA,IAAU,KAAKD,OAAA,EACxBD,CAAA,CAAWU,IAAA,CAAKR,CAAA,CAAO2B,eAAA,CAAgB/B,CAAW,CAAC;IAErD,OAAO,IAAIqC,CAAA,CAAkB;MAAEC,UAAA,EAAApC,CAAA;MAAYqC,MAAA,EAAQ,KAAKpB;IAAiB,CAAC,CAC5E;EAAA;EASAqB,gBAAgBxC,CAAA,EAA4C;IAC1D,IAAM;MAAEyC,OAAA,EAAAvC,CAAA;MAASwC,SAAA,EAAAtC;IAAU,IAAIJ,CAAA;IAI/B,IAAI,CAH2B,KAAKkB,cAAA,CAAeyB,KAAA,CACjD,CAAC7B,CAAA,EAAOH,CAAA,KAAMA,CAAA,KAAM,KAAKG,CAAA,IAAS,KAAKI,cAAA,CAAeP,CAAA,GAAI,CAAC,CAC7D,GAEE,OAAO;IAET,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIV,CAAA,CAAUkC,UAAA,CAAWM,MAAA,EAAQ9B,CAAA,IAAK,GAAG;MACvD,IAAMH,CAAA,GAAkBP,CAAA,CAAUkC,UAAA,CAAWxB,CAAC;MAE9C,IAAI,CADc,KAAKT,SAAA,CAAUkB,UAAA,CAAW,KAAKL,cAAA,CAAeJ,CAAC,CAAC,EACnD0B,eAAA,CAAgB;QAAEC,OAAA,EAAAvC,CAAA;QAASwC,SAAA,EAAW/B;MAAgB,CAAC,GACpE,OAAO,EAEX;IAAA;IACA,OAAO,EACT;EAAA;AACF;AAAA,SAAAd,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}