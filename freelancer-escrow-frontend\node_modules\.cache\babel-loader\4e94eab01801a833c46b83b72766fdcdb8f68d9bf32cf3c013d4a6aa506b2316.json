{"ast": null, "code": "import { a as i } from \"./chunk-H6YNXJNF.mjs\";\nimport { a as r } from \"./chunk-A63SMUOU.mjs\";\nvar t = class l extends r {\n  constructor(e) {\n    super(), this.value = new i(e);\n  }\n  serialize(e) {\n    e.serialize(this.value);\n  }\n  serializeForEntryFunction(e) {\n    e.serializeU32AsUleb128(this.value.value.length), e.serialize(this);\n  }\n  static deserialize(e, s) {\n    let a = i.deserialize(e, s);\n    return new l(a.value);\n  }\n};\nexport { t as a };", "map": {"version": 3, "names": ["t", "l", "r", "constructor", "e", "value", "i", "serialize", "serializeForEntryFunction", "serializeU32AsUleb128", "length", "deserialize", "s", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\serializable\\entryFunctionBytes.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../serializer\";\nimport { Deserializer } from \"../deserializer\";\nimport { FixedBytes } from \"./fixedBytes\";\nimport { EntryFunctionArgument } from \"../../transactions/instances/transactionArgument\";\nimport { HexInput } from \"../../types\";\n\n/**\n * This class exists solely to represent a sequence of fixed bytes as a serialized entry function, because\n * serializing an entry function appends a prefix that's *only* used for entry function arguments.\n *\n * NOTE: Attempting to use this class for a serialized script function will result in erroneous\n * and unexpected behavior.\n *\n * If you wish to convert this class back to a TransactionArgument, you must know the type\n * of the argument beforehand, and use the appropriate class to deserialize the bytes within\n * an instance of this class.\n */\nexport class EntryFunctionBytes extends Serializable implements EntryFunctionArgument {\n  public readonly value: FixedBytes;\n\n  private constructor(value: HexInput) {\n    super();\n    this.value = new FixedBytes(value);\n  }\n\n  // Note that to see the Move, BCS-serialized representation of the underlying fixed byte vector,\n  // we must not serialize the length prefix.\n  //\n  // In other words, this class is only used to represent a sequence of bytes that are already\n  // BCS-serialized as a type. To represent those bytes accurately, the BCS-serialized form is the same exact\n  // representation.\n  serialize(serializer: Serializer): void {\n    serializer.serialize(this.value);\n  }\n\n  // When we serialize these bytes as an entry function argument, we need to\n  // serialize the length prefix. This essentially converts the underlying fixed byte vector to a type-agnostic\n  // byte vector to an `any` type.\n  // NOTE: This, and the lack of a `serializeForScriptFunction`, is the only meaningful difference between this\n  // class and FixedBytes.\n  serializeForEntryFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.value.value.length);\n    serializer.serialize(this);\n  }\n\n  /**\n   * The only way to create an instance of this class is to use this static method.\n   *\n   * This function should only be used when deserializing a sequence of EntryFunctionPayload arguments.\n   * @param deserializer the deserializer instance with the buffered bytes\n   * @param length the length of the bytes to deserialize\n   * @returns an instance of this class, which will now only be usable as an EntryFunctionArgument\n   */\n  static deserialize(deserializer: Deserializer, length: number): EntryFunctionBytes {\n    const fixedBytes = FixedBytes.deserialize(deserializer, length);\n    return new EntryFunctionBytes(fixedBytes.value);\n  }\n}\n"], "mappings": ";;AAoBO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAA2BC,CAA8C;EAG5EC,YAAYC,CAAA,EAAiB;IACnC,MAAM,GACN,KAAKC,KAAA,GAAQ,IAAIC,CAAA,CAAWF,CAAK,CACnC;EAAA;EAQAG,UAAUH,CAAA,EAA8B;IACtCA,CAAA,CAAWG,SAAA,CAAU,KAAKF,KAAK,CACjC;EAAA;EAOAG,0BAA0BJ,CAAA,EAA8B;IACtDA,CAAA,CAAWK,qBAAA,CAAsB,KAAKJ,KAAA,CAAMA,KAAA,CAAMK,MAAM,GACxDN,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;EAAA;EAUA,OAAOI,YAAYP,CAAA,EAA4BQ,CAAA,EAAoC;IACjF,IAAMC,CAAA,GAAaP,CAAA,CAAWK,WAAA,CAAYP,CAAA,EAAcQ,CAAM;IAC9D,OAAO,IAAIX,CAAA,CAAmBY,CAAA,CAAWR,KAAK,CAChD;EAAA;AACF;AAAA,SAAAL,CAAA,IAAAa,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}