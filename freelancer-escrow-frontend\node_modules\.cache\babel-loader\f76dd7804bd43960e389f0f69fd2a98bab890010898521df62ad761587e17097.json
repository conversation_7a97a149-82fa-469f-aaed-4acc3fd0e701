{"ast": null, "code": "var s = {\n    mainnet: \"https://api.mainnet.aptoslabs.com/v1/graphql\",\n    testnet: \"https://api.testnet.aptoslabs.com/v1/graphql\",\n    devnet: \"https://api.devnet.aptoslabs.com/v1/graphql\",\n    local: \"http://127.0.0.1:8090/v1/graphql\"\n  },\n  o = {\n    mainnet: \"https://api.mainnet.aptoslabs.com/v1\",\n    testnet: \"https://api.testnet.aptoslabs.com/v1\",\n    devnet: \"https://api.devnet.aptoslabs.com/v1\",\n    local: \"http://127.0.0.1:8080/v1\"\n  },\n  p = {\n    mainnet: \"https://faucet.mainnet.aptoslabs.com\",\n    testnet: \"https://faucet.testnet.aptoslabs.com\",\n    devnet: \"https://faucet.devnet.aptoslabs.com\",\n    local: \"http://127.0.0.1:8081\"\n  },\n  a = {\n    mainnet: \"https://api.mainnet.aptoslabs.com/keyless/pepper/v0\",\n    testnet: \"https://api.testnet.aptoslabs.com/keyless/pepper/v0\",\n    devnet: \"https://api.devnet.aptoslabs.com/keyless/pepper/v0\",\n    local: \"https://api.devnet.aptoslabs.com/keyless/pepper/v0\"\n  },\n  n = {\n    mainnet: \"https://api.mainnet.aptoslabs.com/keyless/prover/v0\",\n    testnet: \"https://api.testnet.aptoslabs.com/keyless/prover/v0\",\n    devnet: \"https://api.devnet.aptoslabs.com/keyless/prover/v0\",\n    local: \"https://api.devnet.aptoslabs.com/keyless/prover/v0\"\n  },\n  e = (t => (t.MAINNET = \"mainnet\", t.TESTNET = \"testnet\", t.DEVNET = \"devnet\", t.LOCAL = \"local\", t.CUSTOM = \"custom\", t))(e || {}),\n  r = {\n    mainnet: 1,\n    testnet: 2,\n    local: 4\n  },\n  c = {\n    mainnet: \"mainnet\",\n    testnet: \"testnet\",\n    devnet: \"devnet\",\n    local: \"local\",\n    custom: \"custom\"\n  };\nexport { s as a, o as b, p as c, a as d, n as e, e as f, r as g, c as h };", "map": {"version": 3, "names": ["s", "mainnet", "testnet", "devnet", "local", "o", "p", "a", "n", "e", "t", "MAINNET", "TESTNET", "DEVNET", "LOCAL", "CUSTOM", "r", "c", "custom", "b", "d", "f", "g", "h"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\utils\\apiEndpoints.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nexport const NetworkToIndexerAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/v1/graphql\",\n  testnet: \"https://api.testnet.aptoslabs.com/v1/graphql\",\n  devnet: \"https://api.devnet.aptoslabs.com/v1/graphql\",\n  local: \"http://127.0.0.1:8090/v1/graphql\",\n};\n\nexport const NetworkToNodeAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/v1\",\n  testnet: \"https://api.testnet.aptoslabs.com/v1\",\n  devnet: \"https://api.devnet.aptoslabs.com/v1\",\n  local: \"http://127.0.0.1:8080/v1\",\n};\n\nexport const NetworkToFaucetAPI: Record<string, string> = {\n  mainnet: \"https://faucet.mainnet.aptoslabs.com\",\n  testnet: \"https://faucet.testnet.aptoslabs.com\",\n  devnet: \"https://faucet.devnet.aptoslabs.com\",\n  local: \"http://127.0.0.1:8081\",\n};\n\nexport const NetworkToPepperAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/keyless/pepper/v0\",\n  testnet: \"https://api.testnet.aptoslabs.com/keyless/pepper/v0\",\n  devnet: \"https://api.devnet.aptoslabs.com/keyless/pepper/v0\",\n  // Use the devnet service for local environment\n  local: \"https://api.devnet.aptoslabs.com/keyless/pepper/v0\",\n};\n\nexport const NetworkToProverAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/keyless/prover/v0\",\n  testnet: \"https://api.testnet.aptoslabs.com/keyless/prover/v0\",\n  devnet: \"https://api.devnet.aptoslabs.com/keyless/prover/v0\",\n  // Use the devnet service for local environment\n  local: \"https://api.devnet.aptoslabs.com/keyless/prover/v0\",\n};\n\nexport enum Network {\n  MAINNET = \"mainnet\",\n  TESTNET = \"testnet\",\n  DEVNET = \"devnet\",\n  LOCAL = \"local\",\n  CUSTOM = \"custom\",\n}\n\nexport const NetworkToChainId: Record<string, number> = {\n  mainnet: 1,\n  testnet: 2,\n  local: 4,\n};\n\nexport const NetworkToNetworkName: Record<string, Network> = {\n  mainnet: Network.MAINNET,\n  testnet: Network.TESTNET,\n  devnet: Network.DEVNET,\n  local: Network.LOCAL,\n  custom: Network.CUSTOM,\n};\n"], "mappings": "AAGO,IAAMA,CAAA,GAA8C;IACzDC,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;IACRC,KAAA,EAAO;EACT;EAEaC,CAAA,GAA2C;IACtDJ,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;IACRC,KAAA,EAAO;EACT;EAEaE,CAAA,GAA6C;IACxDL,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;IACRC,KAAA,EAAO;EACT;EAEaG,CAAA,GAA6C;IACxDN,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;IAERC,KAAA,EAAO;EACT;EAEaI,CAAA,GAA6C;IACxDP,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;IAERC,KAAA,EAAO;EACT;EAEYK,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,OAAA,GAAU,WACVD,CAAA,CAAAE,OAAA,GAAU,WACVF,CAAA,CAAAG,MAAA,GAAS,UACTH,CAAA,CAAAI,KAAA,GAAQ,SACRJ,CAAA,CAAAK,MAAA,GAAS,UALCL,CAAA,GAAAD,CAAA;EAQCO,CAAA,GAA2C;IACtDf,OAAA,EAAS;IACTC,OAAA,EAAS;IACTE,KAAA,EAAO;EACT;EAEaa,CAAA,GAAgD;IAC3DhB,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,MAAA,EAAQ;IACRC,KAAA,EAAO;IACPc,MAAA,EAAQ;EACV;AAAA,SAAAlB,CAAA,IAAAO,CAAA,EAAAF,CAAA,IAAAc,CAAA,EAAAb,CAAA,IAAAW,CAAA,EAAAV,CAAA,IAAAa,CAAA,EAAAZ,CAAA,IAAAC,CAAA,EAAAA,CAAA,IAAAY,CAAA,EAAAL,CAAA,IAAAM,CAAA,EAAAL,CAAA,IAAAM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}