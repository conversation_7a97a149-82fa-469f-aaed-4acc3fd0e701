{"ast": null, "code": "// src/index.browser.ts\nimport axios from \"axios\";\nasync function aptosClient(options) {\n  var _a;\n  const {\n    params,\n    method,\n    url,\n    headers,\n    body,\n    overrides\n  } = options;\n  const requestConfig = {\n    headers,\n    method,\n    url,\n    params,\n    data: body,\n    withCredentials: (_a = overrides == null ? void 0 : overrides.WITH_CREDENTIALS) != null ? _a : true\n  };\n  try {\n    const response = await axios(requestConfig);\n    return {\n      status: response.status,\n      statusText: response.statusText,\n      data: response.data,\n      headers: response.headers,\n      config: response.config\n    };\n  } catch (error) {\n    const axiosError = error;\n    if (axiosError.response) {\n      return axiosError.response;\n    }\n    throw error;\n  }\n}\nexport { aptosClient as default };", "map": {"version": 3, "names": ["axios", "aptosClient", "options", "_a", "params", "method", "url", "headers", "body", "overrides", "requestConfig", "data", "withCredentials", "WITH_CREDENTIALS", "response", "status", "statusText", "config", "error", "axiosError"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\aptos-client\\src\\index.browser.ts"], "sourcesContent": ["import axios, { AxiosRequestConfig, AxiosError } from \"axios\";\nimport { AptosClientRequest, AptosClientResponse } from \"./types\";\n\nexport default async function aptosClient<Res>(options: AptosClientRequest): Promise<AptosClientResponse<Res>> {\n  const { params, method, url, headers, body, overrides } = options;\n  const requestConfig: AxiosRequestConfig = {\n    headers,\n    method,\n    url,\n    params,\n    data: body,\n    withCredentials: overrides?.WITH_CREDENTIALS ?? true,\n  };\n\n  try {\n    const response = await axios(requestConfig);\n    return {\n      status: response.status,\n      statusText: response.statusText!,\n      data: response.data,\n      headers: response.headers,\n      config: response.config,\n    };\n  } catch (error) {\n    const axiosError = error as AxiosError<Res>;\n    if (axiosError.response) {\n      return axiosError.response;\n    }\n    throw error;\n  }\n}\n"], "mappings": ";AAAA,OAAOA,KAAA,MAA+C;AAGtD,eAAOC,YAAwCC,OAAA,EAAgE;EAH/G,IAAAC,EAAA;EAIE,MAAM;IAAEC,MAAA;IAAQC,MAAA;IAAQC,GAAA;IAAKC,OAAA;IAASC,IAAA;IAAMC;EAAU,IAAIP,OAAA;EAC1D,MAAMQ,aAAA,GAAoC;IACxCH,OAAA;IACAF,MAAA;IACAC,GAAA;IACAF,MAAA;IACAO,IAAA,EAAMH,IAAA;IACNI,eAAA,GAAiBT,EAAA,GAAAM,SAAA,oBAAAA,SAAA,CAAWI,gBAAA,KAAX,OAAAV,EAAA,GAA+B;EAClD;EAEA,IAAI;IACF,MAAMW,QAAA,GAAW,MAAMd,KAAA,CAAMU,aAAa;IAC1C,OAAO;MACLK,MAAA,EAAQD,QAAA,CAASC,MAAA;MACjBC,UAAA,EAAYF,QAAA,CAASE,UAAA;MACrBL,IAAA,EAAMG,QAAA,CAASH,IAAA;MACfJ,OAAA,EAASO,QAAA,CAASP,OAAA;MAClBU,MAAA,EAAQH,QAAA,CAASG;IACnB;EACF,SAASC,KAAA,EAAO;IACd,MAAMC,UAAA,GAAaD,KAAA;IACnB,IAAIC,UAAA,CAAWL,QAAA,EAAU;MACvB,OAAOK,UAAA,CAAWL,QAAA;IACpB;IACA,MAAMI,KAAA;EACR;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}