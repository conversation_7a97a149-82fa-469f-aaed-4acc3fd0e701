{"ast": null, "code": "var n = \"aptos:onAccountChange\";\nexport { n as a };", "map": {"version": 3, "names": ["n", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosOnAccountChange.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AccountInfo } from '../AccountInfo'\n\n/** Version of the feature. */\nexport type AptosOnAccountChangeVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosOnAccountChangeNamespace = 'aptos:onAccountChange'\n/** TODO: docs */\nexport type AptosOnAccountChangeFeature = {\n  /** Namespace for the feature. */\n  [AptosOnAccountChangeNamespace]: {\n    /** Version of the feature API. */\n    version: AptosOnAccountChangeVersion\n    onAccountChange: AptosOnAccountChangeMethod\n  }\n}\n/** TODO: docs */\nexport type AptosOnAccountChangeMethod = (input: AptosOnAccountChangeInput) => Promise<void>\n/** TODO: docs */\nexport type AptosOnAccountChangeInput = (newAccount: AccountInfo) => void\n"], "mappings": "AAQO,IAAMA,CAAA,GAAgC;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}