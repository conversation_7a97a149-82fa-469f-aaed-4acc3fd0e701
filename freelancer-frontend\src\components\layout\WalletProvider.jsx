import { Network } from '@aptos-labs/ts-sdk';
import { AptosWalletAdapterProvider } from '@aptos-labs/wallet-adapter-react';

// For now, use empty wallets array to avoid import issues
// Wallets will be auto-detected by the adapter
const wallets = [];

const WalletProvider = ({ children }) => {
  return (
    <AptosWalletAdapterProvider
      plugins={wallets}
      autoConnect={true}
      dappConfig={{
        network: Network.DEVNET,
        aptosConnectDappId: 'freelancer-platform',
        mizuwallet: {
          manifestURL: 'https://assets.mz.xyz/static/config/mizuwallet-connect-manifest.json',
        },
      }}
      onError={(error) => {
        console.error('Wallet connection error:', error);
      }}
    >
      {children}
    </AptosWalletAdapterProvider>
  );
};

export default WalletProvider;
