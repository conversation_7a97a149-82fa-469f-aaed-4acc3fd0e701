{"ast": null, "code": "var r = (e => (e.APPROVED = \"Approved\", e.REJECTED = \"Rejected\", e))(r || {});\nexport { r as a };", "map": {"version": 3, "names": ["r", "e", "APPROVED", "REJECTED", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\misc.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AccountAddress, PublicKey, Network } from '@aptos-labs/ts-sdk'\n\n/** TODO: docs */\nexport type TransactionHash = `0x${string}`\n\n/** TODO: docs */\nexport interface NetworkInfo {\n  name: Network // Name of the network.\n  chainId: number // Chain ID of the network.\n  url?: string // RPC URL of the network.\n}\n\nexport enum UserResponseStatus {\n  APPROVED = 'Approved',\n  REJECTED = 'Rejected'\n}\n\nexport interface UserApproval<TResponseArgs> {\n  status: UserResponseStatus.APPROVED\n  args: TResponseArgs\n}\n\nexport interface UserRejection {\n  status: UserResponseStatus.REJECTED\n}\n\nexport type UserResponse<TResponseArgs> = UserApproval<TResponseArgs> | UserRejection\n"], "mappings": "AAeO,IAAKA,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,QAAA,GAAW,YACXD,CAAA,CAAAE,QAAA,GAAW,YAFDF,CAAA,GAAAD,CAAA;AAAA,SAAAA,CAAA,IAAAI,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}