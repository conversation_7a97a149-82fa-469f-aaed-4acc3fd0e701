import React from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';

const Input = ({
  label,
  error,
  success,
  help,
  required = false,
  className = '',
  type = 'text',
  icon,
  iconPosition = 'left',
  ...props
}) => {
  const inputClasses = [
    'form-input',
    error ? 'error' : '',
    success ? 'success' : '',
    icon ? (iconPosition === 'left' ? 'pl-10' : 'pr-10') : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className="form-group">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-secondary-400">{icon}</span>
          </div>
        )}
        
        <input
          type={type}
          className={inputClasses}
          {...props}
        />
        
        {icon && iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-secondary-400">{icon}</span>
          </div>
        )}
        
        {error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <AlertCircle className="w-5 h-5 text-red-500" />
          </div>
        )}
        
        {success && !error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <CheckCircle className="w-5 h-5 text-green-500" />
          </div>
        )}
      </div>
      
      {error && (
        <p className="form-error">{error}</p>
      )}
      
      {help && !error && (
        <p className="form-help">{help}</p>
      )}
    </div>
  );
};

const Textarea = ({
  label,
  error,
  success,
  help,
  required = false,
  className = '',
  rows = 4,
  ...props
}) => {
  const textareaClasses = [
    'form-textarea',
    error ? 'error' : '',
    success ? 'success' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className="form-group">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <textarea
          rows={rows}
          className={textareaClasses}
          {...props}
        />
        
        {error && (
          <div className="absolute top-3 right-3 pointer-events-none">
            <AlertCircle className="w-5 h-5 text-red-500" />
          </div>
        )}
        
        {success && !error && (
          <div className="absolute top-3 right-3 pointer-events-none">
            <CheckCircle className="w-5 h-5 text-green-500" />
          </div>
        )}
      </div>
      
      {error && (
        <p className="form-error">{error}</p>
      )}
      
      {help && !error && (
        <p className="form-help">{help}</p>
      )}
    </div>
  );
};

const Select = ({
  label,
  error,
  success,
  help,
  required = false,
  className = '',
  children,
  ...props
}) => {
  const selectClasses = [
    'form-select',
    error ? 'error' : '',
    success ? 'success' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className="form-group">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <select
          className={selectClasses}
          {...props}
        >
          {children}
        </select>
        
        {error && (
          <div className="absolute inset-y-0 right-8 flex items-center pointer-events-none">
            <AlertCircle className="w-5 h-5 text-red-500" />
          </div>
        )}
        
        {success && !error && (
          <div className="absolute inset-y-0 right-8 flex items-center pointer-events-none">
            <CheckCircle className="w-5 h-5 text-green-500" />
          </div>
        )}
      </div>
      
      {error && (
        <p className="form-error">{error}</p>
      )}
      
      {help && !error && (
        <p className="form-help">{help}</p>
      )}
    </div>
  );
};

Input.Textarea = Textarea;
Input.Select = Select;

export default Input;
