{"ast": null, "code": "import { decode as t } from \"js-base64\";\nasync function c(o) {\n  return new Promise(e => {\n    setTimeout(e, o);\n  });\n}\nvar d = () => Math.floor(Date.now() / 1e3);\nfunction i(o) {\n  let e = new Date(o * 1e3);\n  return e.setMinutes(0), e.setSeconds(0), e.setMilliseconds(0), Math.floor(e.getTime() / 1e3);\n}\nfunction u(o) {\n  let e = o.replace(/-/g, \"+\").replace(/_/g, \"/\"),\n    n = e + \"==\".substring(0, (3 - e.length % 3) % 3);\n  return t(n);\n}\nexport { c as a, d as b, i as c, u as d };", "map": {"version": 3, "names": ["decode", "t", "c", "o", "Promise", "e", "setTimeout", "d", "nowInSeconds", "Math", "floor", "Date", "now", "i", "setMinutes", "setSeconds", "setMilliseconds", "getTime", "u", "replace", "n", "substring", "length", "a", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\utils\\helpers.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { decode } from \"js-base64\";\n\n/**\n * Sleep the current thread for the given amount of time\n * @param timeMs time in milliseconds to sleep\n */\nexport async function sleep(timeMs: number): Promise<null> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeMs);\n  });\n}\n\nexport const nowInSeconds = () => Math.floor(Date.now() / 1000);\n\nexport function floorToWholeHour(timestampInSeconds: number): number {\n  const date = new Date(timestampInSeconds * 1000);\n  // Reset minutes and seconds to zero\n  date.setMinutes(0);\n  date.setSeconds(0);\n  date.setMilliseconds(0);\n  return Math.floor(date.getTime() / 1000);\n}\n\nexport function base64UrlDecode(base64Url: string): string {\n  // Replace base64url-specific characters\n  const base64 = base64Url.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  // Pad the string with '=' characters if needed\n  const paddedBase64 = base64 + \"==\".substring(0, (3 - (base64.length % 3)) % 3);\n  const decodedString = decode(paddedBase64);\n  return decodedString;\n}\n"], "mappings": "AAGA,SAASA,MAAA,IAAAC,CAAA,QAAc;AAMvB,eAAsBC,EAAMC,CAAA,EAA+B;EACzD,OAAO,IAAIC,OAAA,CAASC,CAAA,IAAY;IAC9BC,UAAA,CAAWD,CAAA,EAASF,CAAM,CAC5B;EAAA,CAAC,CACH;AAAA;AAEO,IAAMI,CAAA,GAAeC,CAAA,KAAMC,IAAA,CAAKC,KAAA,CAAMC,IAAA,CAAKC,GAAA,CAAI,IAAI,GAAI;AAEvD,SAASC,EAAiBV,CAAA,EAAoC;EACnE,IAAME,CAAA,GAAO,IAAIM,IAAA,CAAKR,CAAA,GAAqB,GAAI;EAE/C,OAAAE,CAAA,CAAKS,UAAA,CAAW,CAAC,GACjBT,CAAA,CAAKU,UAAA,CAAW,CAAC,GACjBV,CAAA,CAAKW,eAAA,CAAgB,CAAC,GACfP,IAAA,CAAKC,KAAA,CAAML,CAAA,CAAKY,OAAA,CAAQ,IAAI,GAAI,CACzC;AAAA;AAEO,SAASC,EAAgBf,CAAA,EAA2B;EAEzD,IAAME,CAAA,GAASF,CAAA,CAAUgB,OAAA,CAAQ,MAAM,GAAG,EAAEA,OAAA,CAAQ,MAAM,GAAG;IAEvDC,CAAA,GAAef,CAAA,GAAS,KAAKgB,SAAA,CAAU,IAAI,IAAKhB,CAAA,CAAOiB,MAAA,GAAS,KAAM,CAAC;EAE7E,OADsBrB,CAAA,CAAOmB,CAAY,CAE3C;AAAA;AAAA,SAAAlB,CAAA,IAAAqB,CAAA,EAAAhB,CAAA,IAAAiB,CAAA,EAAAX,CAAA,IAAAX,CAAA,EAAAgB,CAAA,IAAAX,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}