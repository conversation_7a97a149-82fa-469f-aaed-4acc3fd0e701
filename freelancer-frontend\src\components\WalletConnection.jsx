import React from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import { Wallet, LogOut, Copy, Check } from 'lucide-react';

const WalletConnection = () => {
  const { 
    connect, 
    disconnect, 
    account, 
    connected, 
    connecting, 
    wallets 
  } = useWallet();

  const [copied, setCopied] = React.useState(false);

  const handleConnect = async (walletName) => {
    try {
      await connect(walletName);
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };

  const copyAddress = async () => {
    if (account?.address) {
      await navigator.clipboard.writeText(account.address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const truncateAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  if (connected && account) {
    return (
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border border-secondary-200">
          <Wallet className="w-4 h-4 text-primary-600" />
          <span className="text-sm font-medium text-secondary-700">
            {truncateAddress(account.address)}
          </span>
          <button
            onClick={copyAddress}
            className="p-1 hover:bg-secondary-100 rounded transition-colors"
            title="Copy address"
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 text-secondary-500" />
            )}
          </button>
        </div>
        <button
          onClick={handleDisconnect}
          className="flex items-center space-x-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg transition-colors"
        >
          <LogOut className="w-4 h-4" />
          <span className="text-sm font-medium">Disconnect</span>
        </button>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-4">
      {wallets.length > 0 ? (
        <div className="relative group">
          <button
            disabled={connecting}
            className="btn-primary flex items-center space-x-2"
          >
            <Wallet className="w-4 h-4" />
            <span>{connecting ? 'Connecting...' : 'Connect Wallet'}</span>
          </button>
          <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            {wallets.map((wallet) => (
              <button
                key={wallet.name}
                onClick={() => handleConnect(wallet.name)}
                disabled={connecting}
                className="w-full px-4 py-3 text-left hover:bg-secondary-50 first:rounded-t-lg last:rounded-b-lg transition-colors flex items-center space-x-3"
              >
                <img 
                  src={wallet.icon} 
                  alt={wallet.name} 
                  className="w-6 h-6"
                />
                <span className="text-sm font-medium text-secondary-700">
                  {wallet.name}
                </span>
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-sm text-secondary-500">
          No wallets detected. Please install a wallet extension.
        </div>
      )}
    </div>
  );
};

export default WalletConnection;
