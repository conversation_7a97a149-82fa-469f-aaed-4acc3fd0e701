{"ast": null, "code": "import { b, c as K } from \"./chunk-YTQVMLFD.mjs\";\nimport { b as h, c as d, e as m } from \"./chunk-IVVWQKCF.mjs\";\nimport { a as c, b as u } from \"./chunk-U6X2FYNI.mjs\";\nimport { b as s } from \"./chunk-A2Z7I2EY.mjs\";\nimport { a as l } from \"./chunk-DZXM2MQY.mjs\";\nimport { a as y } from \"./chunk-A63SMUOU.mjs\";\nimport { b as p } from \"./chunk-BCUSI3N6.mjs\";\nimport { randomBytes as E } from \"@noble/hashes/utils\";\nvar S = 1209600,\n  i = class i extends y {\n    constructor(e) {\n      super();\n      let {\n        privateKey: r,\n        expiryDateSecs: a,\n        blinder: n\n      } = e;\n      this.privateKey = r, this.publicKey = new c(r.publicKey()), this.expiryDateSecs = a || K(b() + S), this.blinder = n !== void 0 ? p.fromHexInput(n).toUint8Array() : v();\n      let t = h(this.publicKey.bcsToBytes(), 93);\n      t.push(BigInt(this.expiryDateSecs)), t.push(d(this.blinder));\n      let x = m(t);\n      this.nonce = x.toString();\n    }\n    getPublicKey() {\n      return this.publicKey;\n    }\n    isExpired() {\n      return Math.floor(Date.now() / 1e3) > this.expiryDateSecs;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(this.publicKey.variant), e.serializeBytes(this.privateKey.toUint8Array()), e.serializeU64(this.expiryDateSecs), e.serializeFixedBytes(this.blinder);\n    }\n    static deserialize(e) {\n      let r = e.deserializeUleb128AsU32(),\n        a;\n      switch (r) {\n        case 0:\n          a = s.deserialize(e);\n          break;\n        default:\n          throw new Error(`Unknown variant index for EphemeralPublicKey: ${r}`);\n      }\n      let n = e.deserializeU64(),\n        t = e.deserializeFixedBytes(31);\n      return new i({\n        privateKey: a,\n        expiryDateSecs: Number(n),\n        blinder: t\n      });\n    }\n    static fromBytes(e) {\n      return i.deserialize(new l(e));\n    }\n    static generate(e) {\n      let r;\n      switch (e?.scheme) {\n        case 0:\n        default:\n          r = s.generate();\n      }\n      return new i({\n        privateKey: r,\n        expiryDateSecs: e?.expiryDateSecs\n      });\n    }\n    sign(e) {\n      if (this.isExpired()) throw new Error(\"EphemeralKeyPair has expired\");\n      return new u(this.privateKey.sign(e));\n    }\n  };\ni.BLINDER_LENGTH = 31;\nvar o = i;\nfunction v() {\n  return E(o.BLINDER_LENGTH);\n}\nexport { o as a };", "map": {"version": 3, "names": ["randomBytes", "E", "S", "i", "y", "constructor", "e", "privateKey", "r", "expiryDateSecs", "a", "blinder", "n", "public<PERSON>ey", "c", "K", "b", "p", "fromHexInput", "toUint8Array", "v", "t", "h", "bcsToBytes", "push", "BigInt", "d", "x", "m", "nonce", "toString", "getPublicKey", "isExpired", "Math", "floor", "Date", "now", "serialize", "serializeU32AsUleb128", "variant", "serializeBytes", "serializeU64", "serializeFixedBytes", "deserialize", "deserializeUleb128AsU32", "s", "Error", "deserializeU64", "deserializeFixedBytes", "Number", "fromBytes", "l", "generate", "scheme", "sign", "u", "BLINDER_LENGTH", "o"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\account\\EphemeralKeyPair.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { randomBytes } from \"@noble/hashes/utils\";\n\nimport { Ed25519Private<PERSON>ey, EphemeralPublicKey, EphemeralSignature, PrivateKey } from \"../core/crypto\";\nimport { Hex } from \"../core/hex\";\nimport { bytesToBigIntLE, padAndPackBytesWithLen, poseidonHash } from \"../core/crypto/poseidon\";\nimport { EphemeralPublicKeyVariant, HexInput } from \"../types\";\nimport { Deserializer, Serializable, Serializer } from \"../bcs\";\nimport { floorToWholeHour, nowInSeconds } from \"../utils/helpers\";\n\nconst TWO_WEEKS_IN_SECONDS = 1_209_600;\n\n/**\n * A class which contains a key pair that is used in signing transactions via the Keyless authentication scheme. This key pair\n * is ephemeral and has an expiration time.  For more details on how this class is used -\n * https://aptos.dev/guides/keyless-accounts/#1-present-the-user-with-a-sign-in-with-idp-button-on-the-ui\n */\nexport class EphemeralKeyPair extends Serializable {\n  static readonly BLINDER_LENGTH: number = 31;\n\n  /**\n   * A byte array of length BLINDER_LENGTH used to obfuscate the public key from the IdP.\n   * Used in calculating the nonce passed to the IdP and as a secret witness in proof generation.\n   */\n  readonly blinder: Uint8Array;\n\n  /**\n   * A timestamp in seconds indicating when the ephemeral key pair is expired.  After expiry, a new\n   * EphemeralKeyPair must be generated and a new JWT needs to be created.\n   */\n  readonly expiryDateSecs: number;\n\n  /**\n   * The value passed to the IdP when the user authenticates.  It comprises of a hash of the\n   * ephermeral public key, expiry date, and blinder.\n   */\n  readonly nonce: string;\n\n  /**\n   * A private key used to sign transactions.  This private key is not tied to any account on the chain as it\n   * is ephemeral (not permanent) in nature.\n   */\n  private privateKey: PrivateKey;\n\n  /**\n   * A public key used to verify transactions.  This public key is not tied to any account on the chain as it\n   * is ephemeral (not permanent) in nature.\n   */\n  private publicKey: EphemeralPublicKey;\n\n  constructor(args: { privateKey: PrivateKey; expiryDateSecs?: number; blinder?: HexInput }) {\n    super();\n    const { privateKey, expiryDateSecs, blinder } = args;\n    this.privateKey = privateKey;\n    this.publicKey = new EphemeralPublicKey(privateKey.publicKey());\n    // By default, we set the expiry date to be two weeks in the future floored to the nearest hour\n    this.expiryDateSecs = expiryDateSecs || floorToWholeHour(nowInSeconds() + TWO_WEEKS_IN_SECONDS);\n    // Generate the blinder if not provided\n    this.blinder = blinder !== undefined ? Hex.fromHexInput(blinder).toUint8Array() : generateBlinder();\n    // Calculate the nonce\n    const fields = padAndPackBytesWithLen(this.publicKey.bcsToBytes(), 93);\n    fields.push(BigInt(this.expiryDateSecs));\n    fields.push(bytesToBigIntLE(this.blinder));\n    const nonceHash = poseidonHash(fields);\n    this.nonce = nonceHash.toString();\n  }\n\n  /**\n   * Returns the public key of the key pair.\n   * @return EphemeralPublicKey\n   */\n  getPublicKey(): EphemeralPublicKey {\n    return this.publicKey;\n  }\n\n  /**\n   * Returns the public key of the key pair.\n   * @return boolean\n   */\n  isExpired(): boolean {\n    const currentTimeSecs: number = Math.floor(Date.now() / 1000);\n    return currentTimeSecs > this.expiryDateSecs;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.publicKey.variant);\n    serializer.serializeBytes(this.privateKey.toUint8Array());\n    serializer.serializeU64(this.expiryDateSecs);\n    serializer.serializeFixedBytes(this.blinder);\n  }\n\n  static deserialize(deserializer: Deserializer): EphemeralKeyPair {\n    const variantIndex = deserializer.deserializeUleb128AsU32();\n    let privateKey: PrivateKey;\n    switch (variantIndex) {\n      case EphemeralPublicKeyVariant.Ed25519:\n        privateKey = Ed25519PrivateKey.deserialize(deserializer);\n        break;\n      default:\n        throw new Error(`Unknown variant index for EphemeralPublicKey: ${variantIndex}`);\n    }\n    const expiryDateSecs = deserializer.deserializeU64();\n    const blinder = deserializer.deserializeFixedBytes(31);\n    return new EphemeralKeyPair({ privateKey, expiryDateSecs: Number(expiryDateSecs), blinder });\n  }\n\n  static fromBytes(bytes: Uint8Array): EphemeralKeyPair {\n    return EphemeralKeyPair.deserialize(new Deserializer(bytes));\n  }\n\n  /**\n   * Returns the public key of the key pair.\n   * @param scheme the type of keypair to use for the EphemeralKeyPair.  Only Ed25519 supported for now.\n   * @param expiryDateSecs the date of expiry.\n   * @return boolean\n   */\n  static generate(args?: { scheme?: EphemeralPublicKeyVariant; expiryDateSecs?: number }): EphemeralKeyPair {\n    let privateKey: PrivateKey;\n\n    switch (args?.scheme) {\n      case EphemeralPublicKeyVariant.Ed25519:\n      default:\n        privateKey = Ed25519PrivateKey.generate();\n    }\n\n    return new EphemeralKeyPair({ privateKey, expiryDateSecs: args?.expiryDateSecs });\n  }\n\n  /**\n   * Sign the given message with the private key.\n   * @param data in HexInput format\n   * @returns EphemeralSignature\n   */\n  sign(data: HexInput): EphemeralSignature {\n    if (this.isExpired()) {\n      throw new Error(\"EphemeralKeyPair has expired\");\n    }\n    return new EphemeralSignature(this.privateKey.sign(data));\n  }\n}\n\n/**\n * Generates a random byte array of length EphemeralKeyPair.BLINDER_LENGTH\n * @returns Uint8Array\n */\nfunction generateBlinder(): Uint8Array {\n  return randomBytes(EphemeralKeyPair.BLINDER_LENGTH);\n}\n"], "mappings": ";;;;;;;AAGA,SAASA,WAAA,IAAAC,CAAA,QAAmB;AAS5B,IAAMC,CAAA,GAAuB;EAOhBC,CAAA,GAAN,MAAMA,CAAA,SAAyBC,CAAa;IAiCjDC,YAAYC,CAAA,EAA+E;MACzF,MAAM;MACN,IAAM;QAAEC,UAAA,EAAAC,CAAA;QAAYC,cAAA,EAAAC,CAAA;QAAgBC,OAAA,EAAAC;MAAQ,IAAIN,CAAA;MAChD,KAAKC,UAAA,GAAaC,CAAA,EAClB,KAAKK,SAAA,GAAY,IAAIC,CAAA,CAAmBN,CAAA,CAAWK,SAAA,CAAU,CAAC,GAE9D,KAAKJ,cAAA,GAAiBC,CAAA,IAAkBK,CAAA,CAAiBC,CAAA,CAAa,IAAId,CAAoB,GAE9F,KAAKS,OAAA,GAAUC,CAAA,KAAY,SAAYK,CAAA,CAAIC,YAAA,CAAaN,CAAO,EAAEO,YAAA,CAAa,IAAIC,CAAA,CAAgB;MAElG,IAAMC,CAAA,GAASC,CAAA,CAAuB,KAAKT,SAAA,CAAUU,UAAA,CAAW,GAAG,EAAE;MACrEF,CAAA,CAAOG,IAAA,CAAKC,MAAA,CAAO,KAAKhB,cAAc,CAAC,GACvCY,CAAA,CAAOG,IAAA,CAAKE,CAAA,CAAgB,KAAKf,OAAO,CAAC;MACzC,IAAMgB,CAAA,GAAYC,CAAA,CAAaP,CAAM;MACrC,KAAKQ,KAAA,GAAQF,CAAA,CAAUG,QAAA,CAAS,CAClC;IAAA;IAMAC,aAAA,EAAmC;MACjC,OAAO,KAAKlB,SACd;IAAA;IAMAmB,UAAA,EAAqB;MAEnB,OADgCC,IAAA,CAAKC,KAAA,CAAMC,IAAA,CAAKC,GAAA,CAAI,IAAI,GAAI,IACnC,KAAK3B,cAChC;IAAA;IAEA4B,UAAU/B,CAAA,EAA8B;MACtCA,CAAA,CAAWgC,qBAAA,CAAsB,KAAKzB,SAAA,CAAU0B,OAAO,GACvDjC,CAAA,CAAWkC,cAAA,CAAe,KAAKjC,UAAA,CAAWY,YAAA,CAAa,CAAC,GACxDb,CAAA,CAAWmC,YAAA,CAAa,KAAKhC,cAAc,GAC3CH,CAAA,CAAWoC,mBAAA,CAAoB,KAAK/B,OAAO,CAC7C;IAAA;IAEA,OAAOgC,YAAYrC,CAAA,EAA8C;MAC/D,IAAME,CAAA,GAAeF,CAAA,CAAasC,uBAAA,CAAwB;QACtDlC,CAAA;MACJ,QAAQF,CAAA;QACN;UACEE,CAAA,GAAamC,CAAA,CAAkBF,WAAA,CAAYrC,CAAY;UACvD;QACF;UACE,MAAM,IAAIwC,KAAA,CAAM,iDAAiDtC,CAAY,EAAE,CACnF;MAAA;MACA,IAAMI,CAAA,GAAiBN,CAAA,CAAayC,cAAA,CAAe;QAC7C1B,CAAA,GAAUf,CAAA,CAAa0C,qBAAA,CAAsB,EAAE;MACrD,OAAO,IAAI7C,CAAA,CAAiB;QAAEI,UAAA,EAAAG,CAAA;QAAYD,cAAA,EAAgBwC,MAAA,CAAOrC,CAAc;QAAGD,OAAA,EAAAU;MAAQ,CAAC,CAC7F;IAAA;IAEA,OAAO6B,UAAU5C,CAAA,EAAqC;MACpD,OAAOH,CAAA,CAAiBwC,WAAA,CAAY,IAAIQ,CAAA,CAAa7C,CAAK,CAAC,CAC7D;IAAA;IAQA,OAAO8C,SAAS9C,CAAA,EAA0F;MACxG,IAAIE,CAAA;MAEJ,QAAQF,CAAA,EAAM+C,MAAA;QACZ;QACA;UACE7C,CAAA,GAAaqC,CAAA,CAAkBO,QAAA,CAAS,CAC5C;MAAA;MAEA,OAAO,IAAIjD,CAAA,CAAiB;QAAEI,UAAA,EAAAC,CAAA;QAAYC,cAAA,EAAgBH,CAAA,EAAMG;MAAe,CAAC,CAClF;IAAA;IAOA6C,KAAKhD,CAAA,EAAoC;MACvC,IAAI,KAAK0B,SAAA,CAAU,GACjB,MAAM,IAAIc,KAAA,CAAM,8BAA8B;MAEhD,OAAO,IAAIS,CAAA,CAAmB,KAAKhD,UAAA,CAAW+C,IAAA,CAAKhD,CAAI,CAAC,CAC1D;IAAA;EACF;AA1HaH,CAAA,CACKqD,cAAA,GAAyB;AADpC,IAAMC,CAAA,GAANtD,CAAA;AAgIP,SAASiB,EAAA,EAA8B;EACrC,OAAOnB,CAAA,CAAYwD,CAAA,CAAiBD,cAAc,CACpD;AAAA;AAAA,SAAAC,CAAA,IAAA/C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}