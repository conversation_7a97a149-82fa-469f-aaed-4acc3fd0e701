{"ast": null, "code": "/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { abytes as abytes_, bytesToHex as bytesToHex_, concatBytes as concatBytes_, hexToBytes as hexToBytes_, isBytes as isBytes_ } from '@noble/hashes/utils.js';\nexport { abytes, anumber, bytesToHex, bytesToUtf8, concatBytes, hexToBytes, isBytes, randomBytes, utf8ToBytes } from '@noble/hashes/utils.js';\nconst _0n = /* @__PURE__ */BigInt(0);\nconst _1n = /* @__PURE__ */BigInt(1);\nexport function abool(title, value) {\n  if (typeof value !== 'boolean') throw new Error(title + ' boolean expected, got ' + value);\n}\n// tmp name until v2\nexport function _abool2(value, title = '') {\n  if (typeof value !== 'boolean') {\n    const prefix = title && `\"${title}\"`;\n    throw new Error(prefix + 'expected boolean, got type=' + typeof value);\n  }\n  return value;\n}\n// tmp name until v2\n/** Asserts something is Uint8Array. */\nexport function _abytes2(value, length, title = '') {\n  const bytes = isBytes_(value);\n  const len = value?.length;\n  const needsLen = length !== undefined;\n  if (!bytes || needsLen && len !== length) {\n    const prefix = title && `\"${title}\" `;\n    const ofLen = needsLen ? ` of length ${length}` : '';\n    const got = bytes ? `length=${len}` : `type=${typeof value}`;\n    throw new Error(prefix + 'expected Uint8Array' + ofLen + ', got ' + got);\n  }\n  return value;\n}\n// Used in weierstrass, der\nexport function numberToHexUnpadded(num) {\n  const hex = num.toString(16);\n  return hex.length & 1 ? '0' + hex : hex;\n}\nexport function hexToNumber(hex) {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n  return hexToNumber(bytesToHex_(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n  abytes_(bytes);\n  return hexToNumber(bytesToHex_(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n  return hexToBytes_(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n  return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n  return hexToBytes_(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'secret key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n  let res;\n  if (typeof hex === 'string') {\n    try {\n      res = hexToBytes_(hex);\n    } catch (e) {\n      throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n    }\n  } else if (isBytes_(hex)) {\n    // Uint8Array.from() instead of hash.slice() because node.js Buffer\n    // is instance of Uint8Array, and its slice() creates **mutable** copy\n    res = Uint8Array.from(hex);\n  } else {\n    throw new Error(title + ' must be hex string or Uint8Array');\n  }\n  const len = res.length;\n  if (typeof expectedLength === 'number' && len !== expectedLength) throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n  return res;\n}\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a, b) {\n  if (a.length !== b.length) return false;\n  let diff = 0;\n  for (let i = 0; i < a.length; i++) diff |= a[i] ^ b[i];\n  return diff === 0;\n}\n/**\n * Copies Uint8Array. We can't use u8a.slice(), because u8a can be Buffer,\n * and Buffer#slice creates mutable copy. Never use Buffers!\n */\nexport function copyBytes(bytes) {\n  return Uint8Array.from(bytes);\n}\n/**\n * Decodes 7-bit ASCII string to Uint8Array, throws on non-ascii symbols\n * Should be safe to use for things expected to be ASCII.\n * Returns exact same result as utf8ToBytes for ASCII or throws.\n */\nexport function asciiToBytes(ascii) {\n  return Uint8Array.from(ascii, (c, i) => {\n    const charCode = c.charCodeAt(0);\n    if (c.length !== 1 || charCode > 127) {\n      throw new Error(`string contains non-ASCII character \"${ascii[i]}\" with code ${charCode} at position ${i}`);\n    }\n    return charCode;\n  });\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\n// export const utf8ToBytes: typeof utf8ToBytes_ = utf8ToBytes_;\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\n// export const bytesToUtf8: typeof bytesToUtf8_ = bytesToUtf8_;\n// Is positive bigint\nconst isPosBig = n => typeof n === 'bigint' && _0n <= n;\nexport function inRange(n, min, max) {\n  return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title, n, min, max) {\n  // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n  // consider P=256n, min=0n, max=P\n  // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n  // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n  // - our way is the cleanest:               `inRange('x', x, 0n, P)\n  if (!inRange(n, min, max)) throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n * TODO: merge with nLength in modular\n */\nexport function bitLen(n) {\n  let len;\n  for (len = 0; n > _0n; n >>= _1n, len += 1);\n  return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n  return n >> BigInt(pos) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n, pos, value) {\n  return n | (value ? _1n : _0n) << BigInt(pos);\n}\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = n => (_1n << BigInt(n)) - _1n;\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n  if (typeof hashLen !== 'number' || hashLen < 2) throw new Error('hashLen must be a number');\n  if (typeof qByteLen !== 'number' || qByteLen < 2) throw new Error('qByteLen must be a number');\n  if (typeof hmacFn !== 'function') throw new Error('hmacFn must be a function');\n  // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n  const u8n = len => new Uint8Array(len); // creates Uint8Array\n  const u8of = byte => Uint8Array.of(byte); // another shortcut\n  let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n  let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n  let i = 0; // Iterations counter, will throw when over 1000\n  const reset = () => {\n    v.fill(1);\n    k.fill(0);\n    i = 0;\n  };\n  const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n  const reseed = (seed = u8n(0)) => {\n    // HMAC-DRBG reseed() function. Steps D-G\n    k = h(u8of(0x00), seed); // k = hmac(k || v || 0x00 || seed)\n    v = h(); // v = hmac(k || v)\n    if (seed.length === 0) return;\n    k = h(u8of(0x01), seed); // k = hmac(k || v || 0x01 || seed)\n    v = h(); // v = hmac(k || v)\n  };\n  const gen = () => {\n    // HMAC-DRBG generate() function\n    if (i++ >= 1000) throw new Error('drbg: tried 1000 values');\n    let len = 0;\n    const out = [];\n    while (len < qByteLen) {\n      v = h();\n      const sl = v.slice();\n      out.push(sl);\n      len += v.length;\n    }\n    return concatBytes_(...out);\n  };\n  const genUntil = (seed, pred) => {\n    reset();\n    reseed(seed); // Steps D-G\n    let res = undefined; // Step H: grind until k is in [1..n-1]\n    while (!(res = pred(gen()))) reseed();\n    reset();\n    return res;\n  };\n  return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n  bigint: val => typeof val === 'bigint',\n  function: val => typeof val === 'function',\n  boolean: val => typeof val === 'boolean',\n  string: val => typeof val === 'string',\n  stringOrUint8Array: val => typeof val === 'string' || isBytes_(val),\n  isSafeInteger: val => Number.isSafeInteger(val),\n  array: val => Array.isArray(val),\n  field: (val, object) => object.Fp.isValid(val),\n  hash: val => typeof val === 'function' && Number.isSafeInteger(val.outputLen)\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n  const checkField = (fieldName, type, isOptional) => {\n    const checkVal = validatorFns[type];\n    if (typeof checkVal !== 'function') throw new Error('invalid validator function');\n    const val = object[fieldName];\n    if (isOptional && val === undefined) return;\n    if (!checkVal(val, object)) {\n      throw new Error('param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val);\n    }\n  };\n  for (const [fieldName, type] of Object.entries(validators)) checkField(fieldName, type, false);\n  for (const [fieldName, type] of Object.entries(optValidators)) checkField(fieldName, type, true);\n  return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\nexport function isHash(val) {\n  return typeof val === 'function' && Number.isSafeInteger(val.outputLen);\n}\nexport function _validateObject(object, fields, optFields = {}) {\n  if (!object || typeof object !== 'object') throw new Error('expected valid options object');\n  function checkField(fieldName, expectedType, isOpt) {\n    const val = object[fieldName];\n    if (isOpt && val === undefined) return;\n    const current = typeof val;\n    if (current !== expectedType || val === null) throw new Error(`param \"${fieldName}\" is invalid: expected ${expectedType}, got ${current}`);\n  }\n  Object.entries(fields).forEach(([k, v]) => checkField(k, v, false));\n  Object.entries(optFields).forEach(([k, v]) => checkField(k, v, true));\n}\n/**\n * throws not implemented error\n */\nexport const notImplemented = () => {\n  throw new Error('not implemented');\n};\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized(fn) {\n  const map = new WeakMap();\n  return (arg, ...args) => {\n    const val = map.get(arg);\n    if (val !== undefined) return val;\n    const computed = fn(arg, ...args);\n    map.set(arg, computed);\n    return computed;\n  };\n}", "map": {"version": 3, "names": ["abytes", "abytes_", "bytesToHex", "bytesToHex_", "concatBytes", "concatBytes_", "hexToBytes", "hexToBytes_", "isBytes", "isBytes_", "anumber", "bytesToUtf8", "randomBytes", "utf8ToBytes", "_0n", "BigInt", "_1n", "abool", "title", "value", "Error", "_abool2", "prefix", "_abytes2", "length", "bytes", "len", "needsLen", "undefined", "ofLen", "got", "numberToHexUnpadded", "num", "hex", "toString", "hexToNumber", "bytesToNumberBE", "bytesToNumberLE", "Uint8Array", "from", "reverse", "numberToBytesBE", "n", "padStart", "numberToBytesLE", "numberToVarBytesBE", "ensureBytes", "<PERSON><PERSON><PERSON><PERSON>", "res", "e", "equalBytes", "a", "b", "diff", "i", "copyBytes", "asciiToBytes", "ascii", "c", "charCode", "charCodeAt", "isPosBig", "inRange", "min", "max", "aInRange", "bitLen", "bitGet", "pos", "bitSet", "bitMask", "createHmacDrbg", "hashLen", "qByteLen", "hmacFn", "u8n", "u8of", "byte", "of", "v", "k", "reset", "fill", "h", "reseed", "seed", "gen", "out", "sl", "slice", "push", "genUntil", "pred", "validatorFns", "bigint", "val", "function", "boolean", "string", "stringOrUint8Array", "isSafeInteger", "Number", "array", "Array", "isArray", "field", "object", "Fp", "<PERSON><PERSON><PERSON><PERSON>", "hash", "outputLen", "validateObject", "validators", "optValidators", "checkField", "fieldName", "type", "isOptional", "checkVal", "String", "Object", "entries", "isHash", "_validateObject", "fields", "optFields", "expectedType", "isOpt", "current", "for<PERSON>ach", "notImplemented", "memoized", "fn", "map", "WeakMap", "arg", "args", "get", "computed", "set"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\utils.ts"], "sourcesContent": ["/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  abytes as abytes_,\n  bytesToHex as bytesToHex_,\n  concatBytes as concatBytes_,\n  hexToBytes as hexToBytes_,\n  isBytes as isBytes_,\n} from '@noble/hashes/utils.js';\nexport {\n  abytes,\n  anumber,\n  bytesToHex,\n  bytesToUtf8,\n  concatBytes,\n  hexToBytes,\n  isBytes,\n  randomBytes,\n  utf8ToBytes,\n} from '@noble/hashes/utils.js';\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nexport type Hex = Uint8Array | string; // hex strings are accepted for simplicity\nexport type PrivKey = Hex | bigint; // bigints are accepted to ease learning curve\nexport type CHash = {\n  (message: Uint8Array | string): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create(opts?: { dkLen?: number }): any; // For shake\n};\nexport type FHash = (message: Uint8Array | string) => Uint8Array;\n\nexport function abool(title: string, value: boolean): void {\n  if (typeof value !== 'boolean') throw new Error(title + ' boolean expected, got ' + value);\n}\n\n// tmp name until v2\nexport function _abool2(value: boolean, title: string = ''): boolean {\n  if (typeof value !== 'boolean') {\n    const prefix = title && `\"${title}\"`;\n    throw new Error(prefix + 'expected boolean, got type=' + typeof value);\n  }\n  return value;\n}\n\n// tmp name until v2\n/** Asserts something is Uint8Array. */\nexport function _abytes2(value: Uint8Array, length?: number, title: string = ''): Uint8Array {\n  const bytes = isBytes_(value);\n  const len = value?.length;\n  const needsLen = length !== undefined;\n  if (!bytes || (needsLen && len !== length)) {\n    const prefix = title && `\"${title}\" `;\n    const ofLen = needsLen ? ` of length ${length}` : '';\n    const got = bytes ? `length=${len}` : `type=${typeof value}`;\n    throw new Error(prefix + 'expected Uint8Array' + ofLen + ', got ' + got);\n  }\n  return value;\n}\n\n// Used in weierstrass, der\nexport function numberToHexUnpadded(num: number | bigint): string {\n  const hex = num.toString(16);\n  return hex.length & 1 ? '0' + hex : hex;\n}\n\nexport function hexToNumber(hex: string): bigint {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes: Uint8Array): bigint {\n  return hexToNumber(bytesToHex_(bytes));\n}\nexport function bytesToNumberLE(bytes: Uint8Array): bigint {\n  abytes_(bytes);\n  return hexToNumber(bytesToHex_(Uint8Array.from(bytes).reverse()));\n}\n\nexport function numberToBytesBE(n: number | bigint, len: number): Uint8Array {\n  return hexToBytes_(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n: number | bigint, len: number): Uint8Array {\n  return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n: number | bigint): Uint8Array {\n  return hexToBytes_(numberToHexUnpadded(n));\n}\n\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'secret key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title: string, hex: Hex, expectedLength?: number): Uint8Array {\n  let res: Uint8Array;\n  if (typeof hex === 'string') {\n    try {\n      res = hexToBytes_(hex);\n    } catch (e) {\n      throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n    }\n  } else if (isBytes_(hex)) {\n    // Uint8Array.from() instead of hash.slice() because node.js Buffer\n    // is instance of Uint8Array, and its slice() creates **mutable** copy\n    res = Uint8Array.from(hex);\n  } else {\n    throw new Error(title + ' must be hex string or Uint8Array');\n  }\n  const len = res.length;\n  if (typeof expectedLength === 'number' && len !== expectedLength)\n    throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n  return res;\n}\n\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a: Uint8Array, b: Uint8Array): boolean {\n  if (a.length !== b.length) return false;\n  let diff = 0;\n  for (let i = 0; i < a.length; i++) diff |= a[i] ^ b[i];\n  return diff === 0;\n}\n/**\n * Copies Uint8Array. We can't use u8a.slice(), because u8a can be Buffer,\n * and Buffer#slice creates mutable copy. Never use Buffers!\n */\nexport function copyBytes(bytes: Uint8Array): Uint8Array {\n  return Uint8Array.from(bytes);\n}\n\n/**\n * Decodes 7-bit ASCII string to Uint8Array, throws on non-ascii symbols\n * Should be safe to use for things expected to be ASCII.\n * Returns exact same result as utf8ToBytes for ASCII or throws.\n */\nexport function asciiToBytes(ascii: string): Uint8Array {\n  return Uint8Array.from(ascii, (c, i) => {\n    const charCode = c.charCodeAt(0);\n    if (c.length !== 1 || charCode > 127) {\n      throw new Error(\n        `string contains non-ASCII character \"${ascii[i]}\" with code ${charCode} at position ${i}`\n      );\n    }\n    return charCode;\n  });\n}\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\n// export const utf8ToBytes: typeof utf8ToBytes_ = utf8ToBytes_;\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\n// export const bytesToUtf8: typeof bytesToUtf8_ = bytesToUtf8_;\n\n// Is positive bigint\nconst isPosBig = (n: bigint) => typeof n === 'bigint' && _0n <= n;\n\nexport function inRange(n: bigint, min: bigint, max: bigint): boolean {\n  return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title: string, n: bigint, min: bigint, max: bigint): void {\n  // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n  // consider P=256n, min=0n, max=P\n  // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n  // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n  // - our way is the cleanest:               `inRange('x', x, 0n, P)\n  if (!inRange(n, min, max))\n    throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n\n// Bit operations\n\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n * TODO: merge with nLength in modular\n */\nexport function bitLen(n: bigint): number {\n  let len;\n  for (len = 0; n > _0n; n >>= _1n, len += 1);\n  return len;\n}\n\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n: bigint, pos: number): bigint {\n  return (n >> BigInt(pos)) & _1n;\n}\n\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n: bigint, pos: number, value: boolean): bigint {\n  return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n: number): bigint => (_1n << BigInt(n)) - _1n;\n\n// DRBG\n\ntype Pred<T> = (v: Uint8Array) => T | undefined;\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg<T>(\n  hashLen: number,\n  qByteLen: number,\n  hmacFn: (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array\n): (seed: Uint8Array, predicate: Pred<T>) => T {\n  if (typeof hashLen !== 'number' || hashLen < 2) throw new Error('hashLen must be a number');\n  if (typeof qByteLen !== 'number' || qByteLen < 2) throw new Error('qByteLen must be a number');\n  if (typeof hmacFn !== 'function') throw new Error('hmacFn must be a function');\n  // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n  const u8n = (len: number) => new Uint8Array(len); // creates Uint8Array\n  const u8of = (byte: number) => Uint8Array.of(byte); // another shortcut\n  let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n  let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n  let i = 0; // Iterations counter, will throw when over 1000\n  const reset = () => {\n    v.fill(1);\n    k.fill(0);\n    i = 0;\n  };\n  const h = (...b: Uint8Array[]) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n  const reseed = (seed = u8n(0)) => {\n    // HMAC-DRBG reseed() function. Steps D-G\n    k = h(u8of(0x00), seed); // k = hmac(k || v || 0x00 || seed)\n    v = h(); // v = hmac(k || v)\n    if (seed.length === 0) return;\n    k = h(u8of(0x01), seed); // k = hmac(k || v || 0x01 || seed)\n    v = h(); // v = hmac(k || v)\n  };\n  const gen = () => {\n    // HMAC-DRBG generate() function\n    if (i++ >= 1000) throw new Error('drbg: tried 1000 values');\n    let len = 0;\n    const out: Uint8Array[] = [];\n    while (len < qByteLen) {\n      v = h();\n      const sl = v.slice();\n      out.push(sl);\n      len += v.length;\n    }\n    return concatBytes_(...out);\n  };\n  const genUntil = (seed: Uint8Array, pred: Pred<T>): T => {\n    reset();\n    reseed(seed); // Steps D-G\n    let res: T | undefined = undefined; // Step H: grind until k is in [1..n-1]\n    while (!(res = pred(gen()))) reseed();\n    reset();\n    return res;\n  };\n  return genUntil;\n}\n\n// Validating curves and fields\n\nconst validatorFns = {\n  bigint: (val: any): boolean => typeof val === 'bigint',\n  function: (val: any): boolean => typeof val === 'function',\n  boolean: (val: any): boolean => typeof val === 'boolean',\n  string: (val: any): boolean => typeof val === 'string',\n  stringOrUint8Array: (val: any): boolean => typeof val === 'string' || isBytes_(val),\n  isSafeInteger: (val: any): boolean => Number.isSafeInteger(val),\n  array: (val: any): boolean => Array.isArray(val),\n  field: (val: any, object: any): any => (object as any).Fp.isValid(val),\n  hash: (val: any): boolean => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n} as const;\ntype Validator = keyof typeof validatorFns;\ntype ValMap<T extends Record<string, any>> = { [K in keyof T]?: Validator };\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\n\nexport function validateObject<T extends Record<string, any>>(\n  object: T,\n  validators: ValMap<T>,\n  optValidators: ValMap<T> = {}\n): T {\n  const checkField = (fieldName: keyof T, type: Validator, isOptional: boolean) => {\n    const checkVal = validatorFns[type];\n    if (typeof checkVal !== 'function') throw new Error('invalid validator function');\n\n    const val = object[fieldName as keyof typeof object];\n    if (isOptional && val === undefined) return;\n    if (!checkVal(val, object)) {\n      throw new Error(\n        'param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val\n      );\n    }\n  };\n  for (const [fieldName, type] of Object.entries(validators)) checkField(fieldName, type!, false);\n  for (const [fieldName, type] of Object.entries(optValidators)) checkField(fieldName, type!, true);\n  return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n\nexport function isHash(val: CHash): boolean {\n  return typeof val === 'function' && Number.isSafeInteger(val.outputLen);\n}\nexport function _validateObject(\n  object: Record<string, any>,\n  fields: Record<string, string>,\n  optFields: Record<string, string> = {}\n): void {\n  if (!object || typeof object !== 'object') throw new Error('expected valid options object');\n  type Item = keyof typeof object;\n  function checkField(fieldName: Item, expectedType: string, isOpt: boolean) {\n    const val = object[fieldName];\n    if (isOpt && val === undefined) return;\n    const current = typeof val;\n    if (current !== expectedType || val === null)\n      throw new Error(`param \"${fieldName}\" is invalid: expected ${expectedType}, got ${current}`);\n  }\n  Object.entries(fields).forEach(([k, v]) => checkField(k, v, false));\n  Object.entries(optFields).forEach(([k, v]) => checkField(k, v, true));\n}\n\n/**\n * throws not implemented error\n */\nexport const notImplemented = (): never => {\n  throw new Error('not implemented');\n};\n\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized<T extends object, R, O extends any[]>(\n  fn: (arg: T, ...args: O) => R\n): (arg: T, ...args: O) => R {\n  const map = new WeakMap<T, R>();\n  return (arg: T, ...args: O): R => {\n    const val = map.get(arg);\n    if (val !== undefined) return val;\n    const computed = fn(arg, ...args);\n    map.set(arg, computed);\n    return computed;\n  };\n}\n"], "mappings": "AAAA;;;;AAIA;AACA,SACEA,MAAM,IAAIC,OAAO,EACjBC,UAAU,IAAIC,WAAW,EACzBC,WAAW,IAAIC,YAAY,EAC3BC,UAAU,IAAIC,WAAW,EACzBC,OAAO,IAAIC,QAAQ,QACd,wBAAwB;AAC/B,SACET,MAAM,EACNU,OAAO,EACPR,UAAU,EACVS,WAAW,EACXP,WAAW,EACXE,UAAU,EACVE,OAAO,EACPI,WAAW,EACXC,WAAW,QACN,wBAAwB;AAC/B,MAAMC,GAAG,GAAG,eAAgBC,MAAM,CAAC,CAAC,CAAC;AACrC,MAAMC,GAAG,GAAG,eAAgBD,MAAM,CAAC,CAAC,CAAC;AAWrC,OAAM,SAAUE,KAAKA,CAACC,KAAa,EAAEC,KAAc;EACjD,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,MAAM,IAAIC,KAAK,CAACF,KAAK,GAAG,yBAAyB,GAAGC,KAAK,CAAC;AAC5F;AAEA;AACA,OAAM,SAAUE,OAAOA,CAACF,KAAc,EAAED,KAAA,GAAgB,EAAE;EACxD,IAAI,OAAOC,KAAK,KAAK,SAAS,EAAE;IAC9B,MAAMG,MAAM,GAAGJ,KAAK,IAAI,IAAIA,KAAK,GAAG;IACpC,MAAM,IAAIE,KAAK,CAACE,MAAM,GAAG,6BAA6B,GAAG,OAAOH,KAAK,CAAC;EACxE;EACA,OAAOA,KAAK;AACd;AAEA;AACA;AACA,OAAM,SAAUI,QAAQA,CAACJ,KAAiB,EAAEK,MAAe,EAAEN,KAAA,GAAgB,EAAE;EAC7E,MAAMO,KAAK,GAAGhB,QAAQ,CAACU,KAAK,CAAC;EAC7B,MAAMO,GAAG,GAAGP,KAAK,EAAEK,MAAM;EACzB,MAAMG,QAAQ,GAAGH,MAAM,KAAKI,SAAS;EACrC,IAAI,CAACH,KAAK,IAAKE,QAAQ,IAAID,GAAG,KAAKF,MAAO,EAAE;IAC1C,MAAMF,MAAM,GAAGJ,KAAK,IAAI,IAAIA,KAAK,IAAI;IACrC,MAAMW,KAAK,GAAGF,QAAQ,GAAG,cAAcH,MAAM,EAAE,GAAG,EAAE;IACpD,MAAMM,GAAG,GAAGL,KAAK,GAAG,UAAUC,GAAG,EAAE,GAAG,QAAQ,OAAOP,KAAK,EAAE;IAC5D,MAAM,IAAIC,KAAK,CAACE,MAAM,GAAG,qBAAqB,GAAGO,KAAK,GAAG,QAAQ,GAAGC,GAAG,CAAC;EAC1E;EACA,OAAOX,KAAK;AACd;AAEA;AACA,OAAM,SAAUY,mBAAmBA,CAACC,GAAoB;EACtD,MAAMC,GAAG,GAAGD,GAAG,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC5B,OAAOD,GAAG,CAACT,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGS,GAAG,GAAGA,GAAG;AACzC;AAEA,OAAM,SAAUE,WAAWA,CAACF,GAAW;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIb,KAAK,CAAC,2BAA2B,GAAG,OAAOa,GAAG,CAAC;EACtF,OAAOA,GAAG,KAAK,EAAE,GAAGnB,GAAG,GAAGC,MAAM,CAAC,IAAI,GAAGkB,GAAG,CAAC,CAAC,CAAC;AAChD;AAEA;AACA,OAAM,SAAUG,eAAeA,CAACX,KAAiB;EAC/C,OAAOU,WAAW,CAAChC,WAAW,CAACsB,KAAK,CAAC,CAAC;AACxC;AACA,OAAM,SAAUY,eAAeA,CAACZ,KAAiB;EAC/CxB,OAAO,CAACwB,KAAK,CAAC;EACd,OAAOU,WAAW,CAAChC,WAAW,CAACmC,UAAU,CAACC,IAAI,CAACd,KAAK,CAAC,CAACe,OAAO,EAAE,CAAC,CAAC;AACnE;AAEA,OAAM,SAAUC,eAAeA,CAACC,CAAkB,EAAEhB,GAAW;EAC7D,OAAOnB,WAAW,CAACmC,CAAC,CAACR,QAAQ,CAAC,EAAE,CAAC,CAACS,QAAQ,CAACjB,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC3D;AACA,OAAM,SAAUkB,eAAeA,CAACF,CAAkB,EAAEhB,GAAW;EAC7D,OAAOe,eAAe,CAACC,CAAC,EAAEhB,GAAG,CAAC,CAACc,OAAO,EAAE;AAC1C;AACA;AACA,OAAM,SAAUK,kBAAkBA,CAACH,CAAkB;EACnD,OAAOnC,WAAW,CAACwB,mBAAmB,CAACW,CAAC,CAAC,CAAC;AAC5C;AAEA;;;;;;;;;AASA,OAAM,SAAUI,WAAWA,CAAC5B,KAAa,EAAEe,GAAQ,EAAEc,cAAuB;EAC1E,IAAIC,GAAe;EACnB,IAAI,OAAOf,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI;MACFe,GAAG,GAAGzC,WAAW,CAAC0B,GAAG,CAAC;IACxB,CAAC,CAAC,OAAOgB,CAAC,EAAE;MACV,MAAM,IAAI7B,KAAK,CAACF,KAAK,GAAG,4CAA4C,GAAG+B,CAAC,CAAC;IAC3E;EACF,CAAC,MAAM,IAAIxC,QAAQ,CAACwB,GAAG,CAAC,EAAE;IACxB;IACA;IACAe,GAAG,GAAGV,UAAU,CAACC,IAAI,CAACN,GAAG,CAAC;EAC5B,CAAC,MAAM;IACL,MAAM,IAAIb,KAAK,CAACF,KAAK,GAAG,mCAAmC,CAAC;EAC9D;EACA,MAAMQ,GAAG,GAAGsB,GAAG,CAACxB,MAAM;EACtB,IAAI,OAAOuB,cAAc,KAAK,QAAQ,IAAIrB,GAAG,KAAKqB,cAAc,EAC9D,MAAM,IAAI3B,KAAK,CAACF,KAAK,GAAG,aAAa,GAAG6B,cAAc,GAAG,iBAAiB,GAAGrB,GAAG,CAAC;EACnF,OAAOsB,GAAG;AACZ;AAEA;AACA,OAAM,SAAUE,UAAUA,CAACC,CAAa,EAAEC,CAAa;EACrD,IAAID,CAAC,CAAC3B,MAAM,KAAK4B,CAAC,CAAC5B,MAAM,EAAE,OAAO,KAAK;EACvC,IAAI6B,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,CAAC3B,MAAM,EAAE8B,CAAC,EAAE,EAAED,IAAI,IAAIF,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC;EACtD,OAAOD,IAAI,KAAK,CAAC;AACnB;AACA;;;;AAIA,OAAM,SAAUE,SAASA,CAAC9B,KAAiB;EACzC,OAAOa,UAAU,CAACC,IAAI,CAACd,KAAK,CAAC;AAC/B;AAEA;;;;;AAKA,OAAM,SAAU+B,YAAYA,CAACC,KAAa;EACxC,OAAOnB,UAAU,CAACC,IAAI,CAACkB,KAAK,EAAE,CAACC,CAAC,EAAEJ,CAAC,KAAI;IACrC,MAAMK,QAAQ,GAAGD,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC;IAChC,IAAIF,CAAC,CAAClC,MAAM,KAAK,CAAC,IAAImC,QAAQ,GAAG,GAAG,EAAE;MACpC,MAAM,IAAIvC,KAAK,CACb,wCAAwCqC,KAAK,CAACH,CAAC,CAAC,eAAeK,QAAQ,gBAAgBL,CAAC,EAAE,CAC3F;IACH;IACA,OAAOK,QAAQ;EACjB,CAAC,CAAC;AACJ;AAEA;;;AAGA;AACA;;;;AAIA;AAEA;AACA,MAAME,QAAQ,GAAInB,CAAS,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAI5B,GAAG,IAAI4B,CAAC;AAEjE,OAAM,SAAUoB,OAAOA,CAACpB,CAAS,EAAEqB,GAAW,EAAEC,GAAW;EACzD,OAAOH,QAAQ,CAACnB,CAAC,CAAC,IAAImB,QAAQ,CAACE,GAAG,CAAC,IAAIF,QAAQ,CAACG,GAAG,CAAC,IAAID,GAAG,IAAIrB,CAAC,IAAIA,CAAC,GAAGsB,GAAG;AAC7E;AAEA;;;;;AAKA,OAAM,SAAUC,QAAQA,CAAC/C,KAAa,EAAEwB,CAAS,EAAEqB,GAAW,EAAEC,GAAW;EACzE;EACA;EACA;EACA;EACA;EACA,IAAI,CAACF,OAAO,CAACpB,CAAC,EAAEqB,GAAG,EAAEC,GAAG,CAAC,EACvB,MAAM,IAAI5C,KAAK,CAAC,iBAAiB,GAAGF,KAAK,GAAG,IAAI,GAAG6C,GAAG,GAAG,UAAU,GAAGC,GAAG,GAAG,QAAQ,GAAGtB,CAAC,CAAC;AAC7F;AAEA;AAEA;;;;;AAKA,OAAM,SAAUwB,MAAMA,CAACxB,CAAS;EAC9B,IAAIhB,GAAG;EACP,KAAKA,GAAG,GAAG,CAAC,EAAEgB,CAAC,GAAG5B,GAAG,EAAE4B,CAAC,KAAK1B,GAAG,EAAEU,GAAG,IAAI,CAAC,CAAC;EAC3C,OAAOA,GAAG;AACZ;AAEA;;;;;AAKA,OAAM,SAAUyC,MAAMA,CAACzB,CAAS,EAAE0B,GAAW;EAC3C,OAAQ1B,CAAC,IAAI3B,MAAM,CAACqD,GAAG,CAAC,GAAIpD,GAAG;AACjC;AAEA;;;AAGA,OAAM,SAAUqD,MAAMA,CAAC3B,CAAS,EAAE0B,GAAW,EAAEjD,KAAc;EAC3D,OAAOuB,CAAC,GAAI,CAACvB,KAAK,GAAGH,GAAG,GAAGF,GAAG,KAAKC,MAAM,CAACqD,GAAG,CAAE;AACjD;AAEA;;;;AAIA,OAAO,MAAME,OAAO,GAAI5B,CAAS,IAAa,CAAC1B,GAAG,IAAID,MAAM,CAAC2B,CAAC,CAAC,IAAI1B,GAAG;AAKtE;;;;;;;AAOA,OAAM,SAAUuD,cAAcA,CAC5BC,OAAe,EACfC,QAAgB,EAChBC,MAAkE;EAElE,IAAI,OAAOF,OAAO,KAAK,QAAQ,IAAIA,OAAO,GAAG,CAAC,EAAE,MAAM,IAAIpD,KAAK,CAAC,0BAA0B,CAAC;EAC3F,IAAI,OAAOqD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE,MAAM,IAAIrD,KAAK,CAAC,2BAA2B,CAAC;EAC9F,IAAI,OAAOsD,MAAM,KAAK,UAAU,EAAE,MAAM,IAAItD,KAAK,CAAC,2BAA2B,CAAC;EAC9E;EACA,MAAMuD,GAAG,GAAIjD,GAAW,IAAK,IAAIY,UAAU,CAACZ,GAAG,CAAC,CAAC,CAAC;EAClD,MAAMkD,IAAI,GAAIC,IAAY,IAAKvC,UAAU,CAACwC,EAAE,CAACD,IAAI,CAAC,CAAC,CAAC;EACpD,IAAIE,CAAC,GAAGJ,GAAG,CAACH,OAAO,CAAC,CAAC,CAAC;EACtB,IAAIQ,CAAC,GAAGL,GAAG,CAACH,OAAO,CAAC,CAAC,CAAC;EACtB,IAAIlB,CAAC,GAAG,CAAC,CAAC,CAAC;EACX,MAAM2B,KAAK,GAAGA,CAAA,KAAK;IACjBF,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;IACTF,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;IACT5B,CAAC,GAAG,CAAC;EACP,CAAC;EACD,MAAM6B,CAAC,GAAGA,CAAC,GAAG/B,CAAe,KAAKsB,MAAM,CAACM,CAAC,EAAED,CAAC,EAAE,GAAG3B,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMgC,MAAM,GAAGA,CAACC,IAAI,GAAGV,GAAG,CAAC,CAAC,CAAC,KAAI;IAC/B;IACAK,CAAC,GAAGG,CAAC,CAACP,IAAI,CAAC,IAAI,CAAC,EAAES,IAAI,CAAC,CAAC,CAAC;IACzBN,CAAC,GAAGI,CAAC,EAAE,CAAC,CAAC;IACT,IAAIE,IAAI,CAAC7D,MAAM,KAAK,CAAC,EAAE;IACvBwD,CAAC,GAAGG,CAAC,CAACP,IAAI,CAAC,IAAI,CAAC,EAAES,IAAI,CAAC,CAAC,CAAC;IACzBN,CAAC,GAAGI,CAAC,EAAE,CAAC,CAAC;EACX,CAAC;EACD,MAAMG,GAAG,GAAGA,CAAA,KAAK;IACf;IACA,IAAIhC,CAAC,EAAE,IAAI,IAAI,EAAE,MAAM,IAAIlC,KAAK,CAAC,yBAAyB,CAAC;IAC3D,IAAIM,GAAG,GAAG,CAAC;IACX,MAAM6D,GAAG,GAAiB,EAAE;IAC5B,OAAO7D,GAAG,GAAG+C,QAAQ,EAAE;MACrBM,CAAC,GAAGI,CAAC,EAAE;MACP,MAAMK,EAAE,GAAGT,CAAC,CAACU,KAAK,EAAE;MACpBF,GAAG,CAACG,IAAI,CAACF,EAAE,CAAC;MACZ9D,GAAG,IAAIqD,CAAC,CAACvD,MAAM;IACjB;IACA,OAAOnB,YAAY,CAAC,GAAGkF,GAAG,CAAC;EAC7B,CAAC;EACD,MAAMI,QAAQ,GAAGA,CAACN,IAAgB,EAAEO,IAAa,KAAO;IACtDX,KAAK,EAAE;IACPG,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC;IACd,IAAIrC,GAAG,GAAkBpB,SAAS,CAAC,CAAC;IACpC,OAAO,EAAEoB,GAAG,GAAG4C,IAAI,CAACN,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,EAAE;IACrCH,KAAK,EAAE;IACP,OAAOjC,GAAG;EACZ,CAAC;EACD,OAAO2C,QAAQ;AACjB;AAEA;AAEA,MAAME,YAAY,GAAG;EACnBC,MAAM,EAAGC,GAAQ,IAAc,OAAOA,GAAG,KAAK,QAAQ;EACtDC,QAAQ,EAAGD,GAAQ,IAAc,OAAOA,GAAG,KAAK,UAAU;EAC1DE,OAAO,EAAGF,GAAQ,IAAc,OAAOA,GAAG,KAAK,SAAS;EACxDG,MAAM,EAAGH,GAAQ,IAAc,OAAOA,GAAG,KAAK,QAAQ;EACtDI,kBAAkB,EAAGJ,GAAQ,IAAc,OAAOA,GAAG,KAAK,QAAQ,IAAItF,QAAQ,CAACsF,GAAG,CAAC;EACnFK,aAAa,EAAGL,GAAQ,IAAcM,MAAM,CAACD,aAAa,CAACL,GAAG,CAAC;EAC/DO,KAAK,EAAGP,GAAQ,IAAcQ,KAAK,CAACC,OAAO,CAACT,GAAG,CAAC;EAChDU,KAAK,EAAEA,CAACV,GAAQ,EAAEW,MAAW,KAAWA,MAAc,CAACC,EAAE,CAACC,OAAO,CAACb,GAAG,CAAC;EACtEc,IAAI,EAAGd,GAAQ,IAAc,OAAOA,GAAG,KAAK,UAAU,IAAIM,MAAM,CAACD,aAAa,CAACL,GAAG,CAACe,SAAS;CACpF;AAGV;AAEA,OAAM,SAAUC,cAAcA,CAC5BL,MAAS,EACTM,UAAqB,EACrBC,aAAA,GAA2B,EAAE;EAE7B,MAAMC,UAAU,GAAGA,CAACC,SAAkB,EAAEC,IAAe,EAAEC,UAAmB,KAAI;IAC9E,MAAMC,QAAQ,GAAGzB,YAAY,CAACuB,IAAI,CAAC;IACnC,IAAI,OAAOE,QAAQ,KAAK,UAAU,EAAE,MAAM,IAAIlG,KAAK,CAAC,4BAA4B,CAAC;IAEjF,MAAM2E,GAAG,GAAGW,MAAM,CAACS,SAAgC,CAAC;IACpD,IAAIE,UAAU,IAAItB,GAAG,KAAKnE,SAAS,EAAE;IACrC,IAAI,CAAC0F,QAAQ,CAACvB,GAAG,EAAEW,MAAM,CAAC,EAAE;MAC1B,MAAM,IAAItF,KAAK,CACb,QAAQ,GAAGmG,MAAM,CAACJ,SAAS,CAAC,GAAG,wBAAwB,GAAGC,IAAI,GAAG,QAAQ,GAAGrB,GAAG,CAChF;IACH;EACF,CAAC;EACD,KAAK,MAAM,CAACoB,SAAS,EAAEC,IAAI,CAAC,IAAII,MAAM,CAACC,OAAO,CAACT,UAAU,CAAC,EAAEE,UAAU,CAACC,SAAS,EAAEC,IAAK,EAAE,KAAK,CAAC;EAC/F,KAAK,MAAM,CAACD,SAAS,EAAEC,IAAI,CAAC,IAAII,MAAM,CAACC,OAAO,CAACR,aAAa,CAAC,EAAEC,UAAU,CAACC,SAAS,EAAEC,IAAK,EAAE,IAAI,CAAC;EACjG,OAAOV,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,OAAM,SAAUgB,MAAMA,CAAC3B,GAAU;EAC/B,OAAO,OAAOA,GAAG,KAAK,UAAU,IAAIM,MAAM,CAACD,aAAa,CAACL,GAAG,CAACe,SAAS,CAAC;AACzE;AACA,OAAM,SAAUa,eAAeA,CAC7BjB,MAA2B,EAC3BkB,MAA8B,EAC9BC,SAAA,GAAoC,EAAE;EAEtC,IAAI,CAACnB,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,MAAM,IAAItF,KAAK,CAAC,+BAA+B,CAAC;EAE3F,SAAS8F,UAAUA,CAACC,SAAe,EAAEW,YAAoB,EAAEC,KAAc;IACvE,MAAMhC,GAAG,GAAGW,MAAM,CAACS,SAAS,CAAC;IAC7B,IAAIY,KAAK,IAAIhC,GAAG,KAAKnE,SAAS,EAAE;IAChC,MAAMoG,OAAO,GAAG,OAAOjC,GAAG;IAC1B,IAAIiC,OAAO,KAAKF,YAAY,IAAI/B,GAAG,KAAK,IAAI,EAC1C,MAAM,IAAI3E,KAAK,CAAC,UAAU+F,SAAS,0BAA0BW,YAAY,SAASE,OAAO,EAAE,CAAC;EAChG;EACAR,MAAM,CAACC,OAAO,CAACG,MAAM,CAAC,CAACK,OAAO,CAAC,CAAC,CAACjD,CAAC,EAAED,CAAC,CAAC,KAAKmC,UAAU,CAAClC,CAAC,EAAED,CAAC,EAAE,KAAK,CAAC,CAAC;EACnEyC,MAAM,CAACC,OAAO,CAACI,SAAS,CAAC,CAACI,OAAO,CAAC,CAAC,CAACjD,CAAC,EAAED,CAAC,CAAC,KAAKmC,UAAU,CAAClC,CAAC,EAAED,CAAC,EAAE,IAAI,CAAC,CAAC;AACvE;AAEA;;;AAGA,OAAO,MAAMmD,cAAc,GAAGA,CAAA,KAAY;EACxC,MAAM,IAAI9G,KAAK,CAAC,iBAAiB,CAAC;AACpC,CAAC;AAED;;;;AAIA,OAAM,SAAU+G,QAAQA,CACtBC,EAA6B;EAE7B,MAAMC,GAAG,GAAG,IAAIC,OAAO,EAAQ;EAC/B,OAAO,CAACC,GAAM,EAAE,GAAGC,IAAO,KAAO;IAC/B,MAAMzC,GAAG,GAAGsC,GAAG,CAACI,GAAG,CAACF,GAAG,CAAC;IACxB,IAAIxC,GAAG,KAAKnE,SAAS,EAAE,OAAOmE,GAAG;IACjC,MAAM2C,QAAQ,GAAGN,EAAE,CAACG,GAAG,EAAE,GAAGC,IAAI,CAAC;IACjCH,GAAG,CAACM,GAAG,CAACJ,GAAG,EAAEG,QAAQ,CAAC;IACtB,OAAOA,QAAQ;EACjB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}