import React, { useState, useEffect } from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import { useEscrow } from '../contexts/EscrowContext';
import { 
  Activity, 
  Shield, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  ExternalLink,
  Copy,
  Check,
  Zap
} from 'lucide-react';
import aptosService from '../services/aptosService';

const BlockchainStatus = () => {
  const { account } = useWallet();
  const { escrows, balance } = useEscrow();
  const [copied, setCopied] = useState('');
  const [networkStats, setNetworkStats] = useState({
    totalEscrows: 0,
    totalVolume: 0,
    totalCompleted: 0,
    platformFees: 0
  });

  // Mock transaction history
  const [transactions] = useState([
    {
      id: '0x9b1e9550568a74eb4de879485d84121474208d6785d42a6a363b94ebac00b59d',
      type: 'Contract Deployment',
      status: 'success',
      timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000,
      amount: 0,
      gasUsed: 4818
    },
    {
      id: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
      type: 'Create Escrow',
      status: 'success',
      timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000,
      amount: 0,
      gasUsed: 2500
    },
    {
      id: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
      type: 'Fund Escrow',
      status: 'success',
      timestamp: Date.now() - 12 * 60 * 60 * 1000,
      amount: 5.125,
      gasUsed: 1800
    }
  ]);

  useEffect(() => {
    // Calculate network stats from escrows
    const stats = escrows.reduce((acc, escrow) => {
      acc.totalEscrows += 1;
      acc.totalVolume += escrow.amount;
      acc.platformFees += escrow.platform_fee;
      if (escrow.status === aptosService.ESCROW_STATUS.COMPLETED) {
        acc.totalCompleted += 1;
      }
      return acc;
    }, { totalEscrows: 0, totalVolume: 0, totalCompleted: 0, platformFees: 0 });

    setNetworkStats(stats);
  }, [escrows]);

  const copyToClipboard = async (text, type) => {
    await navigator.clipboard.writeText(text);
    setCopied(type);
    setTimeout(() => setCopied(''), 2000);
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'Contract Deployment':
        return <Zap className="w-4 h-4 text-purple-600" />;
      case 'Create Escrow':
        return <Shield className="w-4 h-4 text-blue-600" />;
      case 'Fund Escrow':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      default:
        return <Activity className="w-4 h-4 text-secondary-600" />;
    }
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const truncateHash = (hash) => {
    return `${hash.slice(0, 8)}...${hash.slice(-8)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Blockchain Status</h1>
        <p className="text-secondary-600">Monitor smart contract interactions and network activity</p>
      </div>

      {/* Network Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Escrows</p>
              <p className="text-2xl font-bold text-secondary-900">{networkStats.totalEscrows}</p>
            </div>
            <Shield className="w-8 h-8 text-primary-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Volume</p>
              <p className="text-2xl font-bold text-secondary-900">{networkStats.totalVolume.toFixed(2)} APT</p>
            </div>
            <Activity className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Completed</p>
              <p className="text-2xl font-bold text-secondary-900">{networkStats.totalCompleted}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Platform Fees</p>
              <p className="text-2xl font-bold text-secondary-900">{networkStats.platformFees.toFixed(4)} APT</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-yellow-600" />
          </div>
        </div>
      </div>

      {/* Contract Information */}
      <div className="card p-6">
        <h2 className="text-lg font-semibold text-secondary-900 mb-4">Smart Contract Information</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg">
            <div>
              <p className="font-medium text-secondary-900">Contract Address</p>
              <p className="text-sm text-secondary-600 font-mono">
                0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => copyToClipboard('0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22', 'contract')}
                className="p-2 hover:bg-secondary-200 rounded transition-colors"
                title="Copy contract address"
              >
                {copied === 'contract' ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 text-secondary-500" />
                )}
              </button>
              <a
                href="https://explorer.aptoslabs.com/account/0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22?network=devnet"
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 hover:bg-secondary-200 rounded transition-colors"
                title="View on Aptos Explorer"
              >
                <ExternalLink className="w-4 h-4 text-secondary-500" />
              </a>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-sm font-medium text-green-800">Network</p>
              <p className="text-lg font-bold text-green-900">Devnet</p>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-blue-800">Platform Fee</p>
              <p className="text-lg font-bold text-blue-900">2.5%</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <p className="text-sm font-medium text-purple-800">Min Amount</p>
              <p className="text-lg font-bold text-purple-900">0.01 APT</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="card p-6">
        <h2 className="text-lg font-semibold text-secondary-900 mb-4">Recent Transactions</h2>
        <div className="space-y-3">
          {transactions.map((tx) => (
            <div key={tx.id} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
              <div className="flex items-center space-x-3">
                {getTransactionIcon(tx.type)}
                <div>
                  <p className="font-medium text-secondary-900">{tx.type}</p>
                  <p className="text-sm text-secondary-600">{formatDate(tx.timestamp)}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {tx.amount > 0 && (
                  <div className="text-right">
                    <p className="font-medium text-secondary-900">{tx.amount} APT</p>
                    <p className="text-xs text-secondary-500">Gas: {tx.gasUsed}</p>
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    Success
                  </span>
                  <button
                    onClick={() => copyToClipboard(tx.id, tx.id)}
                    className="p-1 hover:bg-secondary-200 rounded transition-colors"
                    title="Copy transaction hash"
                  >
                    {copied === tx.id ? (
                      <Check className="w-3 h-3 text-green-600" />
                    ) : (
                      <Copy className="w-3 h-3 text-secondary-500" />
                    )}
                  </button>
                  <a
                    href={`https://explorer.aptoslabs.com/txn/${tx.id}?network=devnet`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-1 hover:bg-secondary-200 rounded transition-colors"
                    title="View on Aptos Explorer"
                  >
                    <ExternalLink className="w-3 h-3 text-secondary-500" />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Escrow Status Overview */}
      <div className="card p-6">
        <h2 className="text-lg font-semibold text-secondary-900 mb-4">Escrow Status Overview</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(aptosService.STATUS_LABELS).map(([status, label]) => {
            const count = escrows.filter(e => e.status === parseInt(status)).length;
            const percentage = escrows.length > 0 ? (count / escrows.length * 100).toFixed(1) : 0;
            
            return (
              <div key={status} className="text-center p-4 bg-secondary-50 rounded-lg">
                <p className="text-2xl font-bold text-secondary-900">{count}</p>
                <p className="text-sm font-medium text-secondary-700">{label}</p>
                <p className="text-xs text-secondary-500">{percentage}%</p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Account Information */}
      {account && (
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-secondary-900 mb-4">Your Account</h2>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg">
              <div>
                <p className="font-medium text-secondary-900">Wallet Address</p>
                <p className="text-sm text-secondary-600 font-mono">{account.address}</p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => copyToClipboard(account.address, 'wallet')}
                  className="p-2 hover:bg-secondary-200 rounded transition-colors"
                  title="Copy wallet address"
                >
                  {copied === 'wallet' ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4 text-secondary-500" />
                  )}
                </button>
                <a
                  href={`https://explorer.aptoslabs.com/account/${account.address}?network=devnet`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 hover:bg-secondary-200 rounded transition-colors"
                  title="View on Aptos Explorer"
                >
                  <ExternalLink className="w-4 h-4 text-secondary-500" />
                </a>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <p className="text-sm font-medium text-green-800">Balance</p>
                <p className="text-lg font-bold text-green-900">{balance} APT</p>
              </div>
              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-800">Your Escrows</p>
                <p className="text-lg font-bold text-blue-900">
                  {escrows.filter(e => e.client === account.address || e.freelancer === account.address).length}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlockchainStatus;
