import React, { useState } from 'react';

const EscrowSection = () => {
  const [selectedEscrow, setSelectedEscrow] = useState(null);

  const escrows = [
    {
      id: 1,
      title: 'Website Development Project',
      client: 'TechCorp Inc.',
      freelancer: '<PERSON>',
      amount: 2500,
      status: 'active',
      progress: 65,
      milestone: 'Backend API Development',
      deadline: '2024-01-15',
      created: '2024-01-01'
    },
    {
      id: 2,
      title: 'Mobile App Design',
      client: 'StartupXYZ',
      freelancer: '<PERSON>',
      amount: 1800,
      status: 'review',
      progress: 100,
      milestone: 'Final Design Review',
      deadline: '2024-01-10',
      created: '2023-12-20'
    },
    {
      id: 3,
      title: 'Smart Contract Audit',
      client: 'DeFi Protocol',
      freelancer: '<PERSON>',
      amount: 5000,
      status: 'funded',
      progress: 0,
      milestone: 'Initial Code Review',
      deadline: '2024-02-01',
      created: '2024-01-05'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'funded': return 'bg-blue-100 text-blue-800';
      case 'active': return 'bg-yellow-100 text-yellow-800';
      case 'review': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'funded': return '💰';
      case 'active': return '⚡';
      case 'review': return '👀';
      case 'completed': return '✅';
      default: return '📋';
    }
  };

  const workflowSteps = [
    {
      step: 1,
      title: 'Project Created',
      description: 'Client creates project and deposits funds',
      icon: '📝',
      status: 'completed'
    },
    {
      step: 2,
      title: 'Funds Secured',
      description: 'Smart contract locks funds in escrow',
      icon: '🔒',
      status: 'completed'
    },
    {
      step: 3,
      title: 'Work in Progress',
      description: 'Freelancer works on project milestones',
      icon: '⚡',
      status: 'active'
    },
    {
      step: 4,
      title: 'Work Submitted',
      description: 'Freelancer submits completed work',
      icon: '📤',
      status: 'pending'
    },
    {
      step: 5,
      title: 'Payment Released',
      description: 'Automatic payment upon approval',
      icon: '💸',
      status: 'pending'
    }
  ];

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          🛡️ Smart Escrow - The Trusted Middleman
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Our smart contract escrow system acts as a neutral middleman, securing funds and ensuring 
          fair transactions between clients and freelancers. No disputes, no delays, just trust.
        </p>
      </div>

      {/* Escrow Workflow */}
      <div className="mb-16 bg-white rounded-2xl shadow-lg p-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">
          How Smart Escrow Works
        </h3>
        <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0 md:space-x-4">
          {workflowSteps.map((step, index) => (
            <div key={step.step} className="flex flex-col items-center text-center max-w-xs">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl mb-3 ${
                step.status === 'completed' ? 'bg-green-100' :
                step.status === 'active' ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                {step.icon}
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mb-2 ${
                step.status === 'completed' ? 'bg-green-500' :
                step.status === 'active' ? 'bg-blue-500' : 'bg-gray-400'
              }`}>
                {step.step}
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">{step.title}</h4>
              <p className="text-gray-600 text-sm">{step.description}</p>
              {index < workflowSteps.length - 1 && (
                <div className="hidden md:block absolute top-8 left-full w-8 h-0.5 bg-gray-300 transform translate-x-2"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Active Escrows */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">🔄 Active Escrows</h3>
          <div className="space-y-4">
            {escrows.map((escrow) => (
              <div 
                key={escrow.id} 
                className={`card p-6 cursor-pointer transition-all ${
                  selectedEscrow?.id === escrow.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedEscrow(escrow)}
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-900">{escrow.title}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(escrow.status)}`}>
                    {getStatusIcon(escrow.status)} {escrow.status.toUpperCase()}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                  <div>👤 Client: {escrow.client}</div>
                  <div>💼 Freelancer: {escrow.freelancer}</div>
                  <div>💰 Amount: ${escrow.amount}</div>
                  <div>📅 Deadline: {escrow.deadline}</div>
                </div>
                <div className="mb-3">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span>Progress: {escrow.milestone}</span>
                    <span>{escrow.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${escrow.progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Escrow Details */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">📋 Escrow Details</h3>
          {selectedEscrow ? (
            <div className="card p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {selectedEscrow.title}
              </h4>
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Escrow ID:</span>
                  <span className="font-mono text-sm">#{selectedEscrow.id.toString().padStart(6, '0')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="font-semibold">${selectedEscrow.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Platform Fee (2.5%):</span>
                  <span>${(selectedEscrow.amount * 0.025).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Freelancer Receives:</span>
                  <span className="font-semibold text-green-600">
                    ${(selectedEscrow.amount * 0.975).toFixed(2)}
                  </span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Created:</span>
                    <span>{selectedEscrow.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedEscrow.status)}`}>
                      {selectedEscrow.status.toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <button className="btn-primary w-full">View on Blockchain</button>
                <button className="btn-secondary w-full">Download Contract</button>
              </div>
            </div>
          ) : (
            <div className="card p-8 text-center text-gray-500">
              <div className="text-4xl mb-3">👆</div>
              <p>Select an escrow from the list to view details</p>
            </div>
          )}
        </div>
      </div>

      {/* Escrow Benefits */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Why Smart Escrow is Essential
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-4xl mb-3">🔒</div>
            <h4 className="font-semibold text-gray-900 mb-2">Secure Funds</h4>
            <p className="text-gray-600 text-sm">
              Funds are locked in smart contracts until work is completed
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">⚖️</div>
            <h4 className="font-semibold text-gray-900 mb-2">Fair Disputes</h4>
            <p className="text-gray-600 text-sm">
              Automated dispute resolution based on predefined criteria
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">⚡</div>
            <h4 className="font-semibold text-gray-900 mb-2">Instant Release</h4>
            <p className="text-gray-600 text-sm">
              Payments are released automatically upon work approval
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">📊</div>
            <h4 className="font-semibold text-gray-900 mb-2">Full Transparency</h4>
            <p className="text-gray-600 text-sm">
              All transactions are recorded on the blockchain
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EscrowSection;
