{"ast": null, "code": "import { a as T, c as an, d as rn, e as N } from \"./chunk-HCN7YJGB.mjs\";\nimport { q as en, t as y } from \"./chunk-LVSOUCFD.mjs\";\nimport { a as nn } from \"./chunk-DLDGUTFV.mjs\";\nimport { b as tn } from \"./chunk-6JL2U7JB.mjs\";\nimport { a as Y } from \"./chunk-K4CTCBLY.mjs\";\nimport { a as f } from \"./chunk-4IBJW3PB.mjs\";\nimport { a as J } from \"./chunk-BVB3QII3.mjs\";\nimport { a as Q } from \"./chunk-M3JHXCGV.mjs\";\nimport { a as L, c as Z, d as j } from \"./chunk-JPDT6E3B.mjs\";\nimport { c as q, d as v, e as H, f as F, g as K, h as X, i as z } from \"./chunk-F74FF323.mjs\";\nimport { a as V } from \"./chunk-BOYYQAB4.mjs\";\nimport { b as D, d as R, e as $, f as M } from \"./chunk-K7DBDI2I.mjs\";\nimport { a as O, b as l, d as A } from \"./chunk-AYKZA676.mjs\";\nimport { a as E, b as p } from \"./chunk-QVWBJJRF.mjs\";\nimport { a as b, c as W } from \"./chunk-6ZQWPHLV.mjs\";\nimport { i as B, j as k } from \"./chunk-GFRNBBTY.mjs\";\nimport { a as P, c as S } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b as u } from \"./chunk-BF46IXHH.mjs\";\nimport { a as _ } from \"./chunk-AH44UPM4.mjs\";\nimport { a as C } from \"./chunk-WV5AD4IE.mjs\";\nimport { g as x } from \"./chunk-SCHZ67F3.mjs\";\nimport { b as U, c as G } from \"./chunk-YE5B2S5L.mjs\";\nimport { b as I } from \"./chunk-BCUSI3N6.mjs\";\nimport { sha3_256 as mn } from \"@noble/hashes/sha3\";\nasync function Vn(n) {\n  if (en(n)) return yn(n);\n  let {\n      moduleAddress: t,\n      moduleName: e,\n      functionName: i\n    } = y(n.function),\n    a = await sn({\n      key: \"entry-function\",\n      moduleAddress: t,\n      moduleName: e,\n      functionName: i,\n      aptosConfig: n.aptosConfig,\n      abi: n.abi,\n      fetch: an\n    });\n  return An({\n    ...n,\n    abi: a\n  });\n}\nfunction An(n) {\n  let t = n.abi,\n    {\n      moduleAddress: e,\n      moduleName: i,\n      functionName: a\n    } = y(n.function),\n    r = T(n.typeArguments);\n  if (r.length !== t.typeParameters.length) throw new Error(`Type argument count mismatch, expected ${t.typeParameters.length}, received ${r.length}`);\n  let o = n.functionArguments.map((s, d) => N(n.function, t, s, d, r));\n  if (o.length !== t.parameters.length) throw new Error(`Too few arguments for '${e}::${i}::${a}', expected ${t.parameters.length} but got ${o.length}`);\n  let c = F.build(`${e}::${i}`, a, r, o);\n  if (\"multisigAddress\" in n) {\n    let s = u.from(n.multisigAddress);\n    return new H(new X(s, new z(c)));\n  }\n  return new v(c);\n}\nasync function qn(n) {\n  let {\n      moduleAddress: t,\n      moduleName: e,\n      functionName: i\n    } = y(n.function),\n    a = await sn({\n      key: \"view-function\",\n      moduleAddress: t,\n      moduleName: e,\n      functionName: i,\n      aptosConfig: n.aptosConfig,\n      abi: n.abi,\n      fetch: rn\n    });\n  return fn({\n    abi: a,\n    ...n\n  });\n}\nfunction fn(n) {\n  let t = n.abi,\n    {\n      moduleAddress: e,\n      moduleName: i,\n      functionName: a\n    } = y(n.function),\n    r = T(n.typeArguments);\n  if (r.length !== t.typeParameters.length) throw new Error(`Type argument count mismatch, expected ${t.typeParameters.length}, received ${r.length}`);\n  let o = n?.functionArguments?.map((c, s) => N(n.function, t, c, s, r)) ?? [];\n  if (o.length !== t.parameters.length) throw new Error(`Too few arguments for '${e}::${i}::${a}', expected ${t.parameters.length} but got ${o.length}`);\n  return F.build(`${e}::${i}`, a, r, o);\n}\nfunction yn(n) {\n  return new q(new K(I.fromHexInput(n.bytecode).toUint8Array(), T(n.typeArguments), n.functionArguments));\n}\nasync function gn(n) {\n  let {\n      aptosConfig: t,\n      sender: e,\n      payload: i,\n      options: a,\n      feePayerAddress: r\n    } = n,\n    o = async () => x[t.network] ? {\n      chainId: x[t.network]\n    } : {\n      chainId: (await Y({\n        aptosConfig: t\n      })).chain_id\n    },\n    c = async () => a?.gasUnitPrice ? {\n      gasEstimate: a.gasUnitPrice\n    } : {\n      gasEstimate: (await tn({\n        aptosConfig: t\n      })).gas_estimate\n    },\n    s = async () => {\n      let g = async () => a?.accountSequenceNumber !== void 0 ? a.accountSequenceNumber : (await nn({\n        aptosConfig: t,\n        accountAddress: e\n      })).sequence_number;\n      if (r && u.from(r).equals(u.ZERO)) try {\n        return await g();\n      } catch {\n        return 0;\n      } else return g();\n    },\n    [{\n      chainId: d\n    }, {\n      gasEstimate: m\n    }, h] = await Promise.all([o(), c(), s()]),\n    {\n      maxGasAmount: cn,\n      gasUnitPrice: un,\n      expireTimestamp: dn\n    } = {\n      maxGasAmount: a?.maxGasAmount ? BigInt(a.maxGasAmount) : BigInt(2e5),\n      gasUnitPrice: a?.gasUnitPrice ?? BigInt(m),\n      expireTimestamp: a?.expireTimestamp ?? BigInt(Math.floor(Date.now() / 1e3) + 20)\n    };\n  return new L(u.from(e), BigInt(h), i, BigInt(cn), BigInt(un), BigInt(dn), new V(d));\n}\nasync function vn(n) {\n  let {\n      aptosConfig: t,\n      sender: e,\n      payload: i,\n      options: a,\n      feePayerAddress: r\n    } = n,\n    o = await gn({\n      aptosConfig: t,\n      sender: e,\n      payload: i,\n      options: a,\n      feePayerAddress: r\n    });\n  if (\"secondarySignerAddresses\" in n) {\n    let c = n.secondarySignerAddresses?.map(s => u.from(s)) ?? [];\n    return new Q(o, c, n.feePayerAddress ? u.from(n.feePayerAddress) : void 0);\n  }\n  return new J(o, n.feePayerAddress ? u.from(n.feePayerAddress) : void 0);\n}\nfunction Hn(n) {\n  let {\n      signerPublicKey: t,\n      transaction: e,\n      secondarySignersPublicKeys: i,\n      feePayerPublicKey: a\n    } = n,\n    r = w(t);\n  if (e.feePayerAddress) {\n    let c = new j(e.rawTransaction, e.secondarySignerAddresses ?? [], e.feePayerAddress),\n      s = [];\n    i && (s = i.map(h => w(h)));\n    let d = w(a),\n      m = new $(r, e.secondarySignerAddresses ?? [], s, {\n        address: e.feePayerAddress,\n        authenticator: d\n      });\n    return new f(c.raw_txn, m).bcsToBytes();\n  }\n  if (e.secondarySignerAddresses) {\n    let c = new Z(e.rawTransaction, e.secondarySignerAddresses),\n      s = [];\n    s = i.map(m => w(m));\n    let d = new R(r, e.secondarySignerAddresses, s);\n    return new f(c.raw_txn, d).bcsToBytes();\n  }\n  let o;\n  if (r instanceof l) o = new D(r.public_key, r.signature);else if (r instanceof A) o = new M(r);else throw new Error(\"Invalid public key\");\n  return new f(e.rawTransaction, o).bcsToBytes();\n}\nfunction w(n) {\n  if ((n instanceof B || n instanceof b) && (n = new E(n)), n instanceof E) {\n    if (n.publicKey instanceof P) return new A(n, new p(new S(new Uint8Array(64))));\n    if (n.publicKey instanceof b) return new A(n, new p(new W(new Uint8Array(64))));\n    if (n.publicKey instanceof B) return new A(n, new p(k.getSimulationSignature()));\n  }\n  return new l(new P(n.toUint8Array()), new S(new Uint8Array(64)));\n}\nfunction pn(n) {\n  let {\n      transaction: t,\n      feePayerAuthenticator: e,\n      additionalSignersAuthenticators: i\n    } = n,\n    a = C(O, n.senderAuthenticator),\n    r;\n  if (t.feePayerAddress) {\n    if (!e) throw new Error(\"Must provide a feePayerAuthenticator argument to generate a signed fee payer transaction\");\n    r = new $(a, t.secondarySignerAddresses ?? [], i ?? [], {\n      address: t.feePayerAddress,\n      authenticator: e\n    });\n  } else if (t.secondarySignerAddresses) {\n    if (!i) throw new Error(\"Must provide a additionalSignersAuthenticators argument to generate a signed multi agent transaction\");\n    r = new R(a, t.secondarySignerAddresses, i);\n  } else a instanceof l ? r = new D(a.public_key, a.signature) : r = new M(a);\n  return new f(t.rawTransaction, r).bcsToBytes();\n}\nfunction on(n) {\n  let t = mn.create();\n  for (let e of n) t.update(e);\n  return t.digest();\n}\nvar ln = on([\"APTOS::Transaction\"]);\nfunction Kn(n) {\n  let t = pn(n);\n  return new I(on([ln, new Uint8Array([0]), t])).toString();\n}\nasync function sn({\n  key: n,\n  moduleAddress: t,\n  moduleName: e,\n  functionName: i,\n  aptosConfig: a,\n  abi: r,\n  fetch: o\n}) {\n  return r !== void 0 ? r : _(async () => o(t, e, i, a), `${n}-${a.network}-${t}-${e}-${i}`, 1e3 * 60 * 5)();\n}\nexport { Vn as a, An as b, qn as c, fn as d, gn as e, vn as f, Hn as g, w as h, pn as i, on as j, Kn as k };", "map": {"version": 3, "names": ["sha3_256", "mn", "Vn", "n", "en", "yn", "moduleAddress", "t", "moduleName", "e", "functionName", "i", "y", "function", "a", "sn", "key", "aptosConfig", "abi", "fetch", "an", "An", "r", "T", "typeArguments", "length", "typeParameters", "Error", "o", "functionArguments", "map", "s", "d", "N", "parameters", "c", "F", "build", "u", "from", "multisigAddress", "H", "X", "z", "v", "qn", "rn", "fn", "q", "K", "I", "fromHexInput", "bytecode", "toUint8Array", "gn", "sender", "payload", "options", "feePayer<PERSON>dd<PERSON>", "get<PERSON>hainId", "x", "network", "chainId", "Y", "chain_id", "getGasUnitPrice", "gasUnitPrice", "gasEstimate", "tn", "gas_estimate", "getSequenceNumberForAny", "g", "getSequenceNumber", "accountSequenceNumber", "nn", "accountAddress", "sequence_number", "equals", "ZERO", "m", "h", "Promise", "all", "maxGasAmount", "cn", "un", "expireTimestamp", "dn", "BigInt", "Math", "floor", "Date", "now", "L", "V", "vn", "secondarySignerAddresses", "Q", "J", "Hn", "signer<PERSON><PERSON><PERSON><PERSON><PERSON>", "transaction", "secondarySignersPublicKeys", "feePayerPublicKey", "w", "j", "rawTransaction", "$", "address", "authenticator", "f", "raw_txn", "bcsToBytes", "Z", "R", "l", "D", "public_key", "signature", "A", "M", "B", "b", "E", "public<PERSON>ey", "P", "p", "S", "Uint8Array", "W", "k", "getSimulationSignature", "pn", "feePayerAuthenticator", "additionalSignersAuthenticators", "C", "O", "senderAuthenticator", "on", "create", "update", "digest", "ln", "Kn", "toString", "_"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\transactionBuilder\\transactionBuilder.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This file handles the transaction creation lifecycle.\n * It holds different operations to generate a transaction payload, a raw transaction,\n * and a signed transaction that can be simulated, signed and submitted to chain.\n */\nimport { sha3_256 as sha3Hash } from \"@noble/hashes/sha3\";\nimport { AptosConfig } from \"../../api/aptosConfig\";\nimport { AccountAddress, AccountAddressInput, Hex, PublicKey } from \"../../core\";\nimport {\n  AnyPublicKey,\n  AnySignature,\n  KeylessPublicKey,\n  KeylessSignature,\n  Secp256k1PublicKey,\n  Secp256k1Signature,\n} from \"../../core/crypto\";\nimport { Ed25519PublicKey, Ed25519Signature } from \"../../core/crypto/ed25519\";\nimport { getInfo } from \"../../internal/account\";\nimport { getLedgerInfo } from \"../../internal/general\";\nimport { getGasPriceEstimation } from \"../../internal/transaction\";\nimport { NetworkToChainId } from \"../../utils/apiEndpoints\";\nimport { DEFAULT_MAX_GAS_AMOUNT, DEFAULT_TXN_EXP_SEC_FROM_NOW } from \"../../utils/const\";\nimport { normalizeBundle } from \"../../utils/normalizeBundle\";\nimport {\n  AccountAuthenticator,\n  AccountAuthenticatorEd25519,\n  AccountAuthenticatorSingleKey,\n} from \"../authenticator/account\";\nimport {\n  TransactionAuthenticator,\n  TransactionAuthenticatorEd25519,\n  TransactionAuthenticatorFeePayer,\n  TransactionAuthenticatorMultiAgent,\n  TransactionAuthenticatorSingleSender,\n} from \"../authenticator/transaction\";\nimport {\n  ChainId,\n  EntryFunction,\n  FeePayerRawTransaction,\n  MultiAgentRawTransaction,\n  MultiSig,\n  MultiSigTransactionPayload,\n  RawTransaction,\n  Script,\n  TransactionPayloadEntryFunction,\n  TransactionPayloadMultiSig,\n  TransactionPayloadScript,\n} from \"../instances\";\nimport { SignedTransaction } from \"../instances/signedTransaction\";\nimport {\n  AnyRawTransaction,\n  AnyTransactionPayloadInstance,\n  EntryFunctionArgumentTypes,\n  InputGenerateMultiAgentRawTransactionArgs,\n  InputGenerateRawTransactionArgs,\n  InputGenerateSingleSignerRawTransactionArgs,\n  InputGenerateTransactionOptions,\n  InputScriptData,\n  InputSimulateTransactionData,\n  InputMultiSigDataWithRemoteABI,\n  InputEntryFunctionDataWithRemoteABI,\n  InputGenerateTransactionPayloadDataWithRemoteABI,\n  InputSubmitTransactionData,\n  InputGenerateTransactionPayloadDataWithABI,\n  InputEntryFunctionDataWithABI,\n  InputMultiSigDataWithABI,\n  InputViewFunctionDataWithRemoteABI,\n  InputViewFunctionDataWithABI,\n  FunctionABI,\n} from \"../types\";\nimport { convertArgument, fetchEntryFunctionAbi, fetchViewFunctionAbi, standardizeTypeTags } from \"./remoteAbi\";\nimport { memoizeAsync } from \"../../utils/memoize\";\nimport { getFunctionParts, isScriptDataInput } from \"./helpers\";\nimport { SimpleTransaction } from \"../instances/simpleTransaction\";\nimport { MultiAgentTransaction } from \"../instances/multiAgentTransaction\";\n\n/**\n * We are defining function signatures, each with its specific input and output.\n * These are the possible function signature for our `generateTransactionPayload` function.\n * When we call our `generateTransactionPayload` function with the relevant type properties,\n * Typescript can infer the return type based on the appropriate function overload.\n */\nexport async function generateTransactionPayload(args: InputScriptData): Promise<TransactionPayloadScript>;\nexport async function generateTransactionPayload(\n  args: InputEntryFunctionDataWithRemoteABI,\n): Promise<TransactionPayloadEntryFunction>;\nexport async function generateTransactionPayload(\n  args: InputMultiSigDataWithRemoteABI,\n): Promise<TransactionPayloadMultiSig>;\n\n/**\n * Builds a transaction payload based on the data argument and returns\n * a transaction payload - TransactionPayloadScript | TransactionPayloadMultiSig | TransactionPayloadEntryFunction\n *\n * This uses the RemoteABI by default, and the remote ABI can be skipped by using generateTransactionPayloadWithABI\n *\n * @param args.data GenerateTransactionPayloadData\n *\n * @return TransactionPayload\n */\nexport async function generateTransactionPayload(\n  args: InputGenerateTransactionPayloadDataWithRemoteABI,\n): Promise<AnyTransactionPayloadInstance> {\n  if (isScriptDataInput(args)) {\n    return generateTransactionPayloadScript(args);\n  }\n  const { moduleAddress, moduleName, functionName } = getFunctionParts(args.function);\n\n  const functionAbi = await fetchAbi({\n    key: \"entry-function\",\n    moduleAddress,\n    moduleName,\n    functionName,\n    aptosConfig: args.aptosConfig,\n    abi: args.abi,\n    fetch: fetchEntryFunctionAbi,\n  });\n\n  // Fill in the ABI\n  return generateTransactionPayloadWithABI({ ...args, abi: functionAbi });\n}\n\nexport function generateTransactionPayloadWithABI(args: InputEntryFunctionDataWithABI): TransactionPayloadEntryFunction;\nexport function generateTransactionPayloadWithABI(args: InputMultiSigDataWithABI): TransactionPayloadMultiSig;\nexport function generateTransactionPayloadWithABI(\n  args: InputGenerateTransactionPayloadDataWithABI,\n): AnyTransactionPayloadInstance {\n  const functionAbi = args.abi;\n  const { moduleAddress, moduleName, functionName } = getFunctionParts(args.function);\n\n  // Ensure that all type arguments are typed properly\n  const typeArguments = standardizeTypeTags(args.typeArguments);\n\n  // Check the type argument count against the ABI\n  if (typeArguments.length !== functionAbi.typeParameters.length) {\n    throw new Error(\n      `Type argument count mismatch, expected ${functionAbi.typeParameters.length}, received ${typeArguments.length}`,\n    );\n  }\n\n  // Check all BCS types, and convert any non-BCS types\n  const functionArguments: Array<EntryFunctionArgumentTypes> = args.functionArguments.map((arg, i) =>\n    convertArgument(args.function, functionAbi, arg, i, typeArguments),\n  );\n\n  // Check that all arguments are accounted for\n  if (functionArguments.length !== functionAbi.parameters.length) {\n    throw new Error(\n      // eslint-disable-next-line max-len\n      `Too few arguments for '${moduleAddress}::${moduleName}::${functionName}', expected ${functionAbi.parameters.length} but got ${functionArguments.length}`,\n    );\n  }\n\n  // Generate entry function payload\n  const entryFunctionPayload = EntryFunction.build(\n    `${moduleAddress}::${moduleName}`,\n    functionName,\n    typeArguments,\n    functionArguments,\n  );\n\n  // Send it as multi sig if it's a multisig payload\n  if (\"multisigAddress\" in args) {\n    const multisigAddress = AccountAddress.from(args.multisigAddress);\n    return new TransactionPayloadMultiSig(\n      new MultiSig(multisigAddress, new MultiSigTransactionPayload(entryFunctionPayload)),\n    );\n  }\n\n  // Otherwise send as an entry function\n  return new TransactionPayloadEntryFunction(entryFunctionPayload);\n}\n\nexport async function generateViewFunctionPayload(args: InputViewFunctionDataWithRemoteABI): Promise<EntryFunction> {\n  const { moduleAddress, moduleName, functionName } = getFunctionParts(args.function);\n\n  const functionAbi = await fetchAbi({\n    key: \"view-function\",\n    moduleAddress,\n    moduleName,\n    functionName,\n    aptosConfig: args.aptosConfig,\n    abi: args.abi,\n    fetch: fetchViewFunctionAbi,\n  });\n\n  // Fill in the ABI\n  return generateViewFunctionPayloadWithABI({ abi: functionAbi, ...args });\n}\n\nexport function generateViewFunctionPayloadWithABI(args: InputViewFunctionDataWithABI): EntryFunction {\n  const functionAbi = args.abi;\n  const { moduleAddress, moduleName, functionName } = getFunctionParts(args.function);\n\n  // Ensure that all type arguments are typed properly\n  const typeArguments = standardizeTypeTags(args.typeArguments);\n\n  // Check the type argument count against the ABI\n  if (typeArguments.length !== functionAbi.typeParameters.length) {\n    throw new Error(\n      `Type argument count mismatch, expected ${functionAbi.typeParameters.length}, received ${typeArguments.length}`,\n    );\n  }\n\n  // Check all BCS types, and convert any non-BCS types\n  const functionArguments: Array<EntryFunctionArgumentTypes> =\n    args?.functionArguments?.map((arg, i) => convertArgument(args.function, functionAbi, arg, i, typeArguments)) ?? [];\n\n  // Check that all arguments are accounted for\n  if (functionArguments.length !== functionAbi.parameters.length) {\n    throw new Error(\n      // eslint-disable-next-line max-len\n      `Too few arguments for '${moduleAddress}::${moduleName}::${functionName}', expected ${functionAbi.parameters.length} but got ${functionArguments.length}`,\n    );\n  }\n\n  // Generate entry function payload\n  return EntryFunction.build(`${moduleAddress}::${moduleName}`, functionName, typeArguments, functionArguments);\n}\n\nfunction generateTransactionPayloadScript(args: InputScriptData) {\n  return new TransactionPayloadScript(\n    new Script(\n      Hex.fromHexInput(args.bytecode).toUint8Array(),\n      standardizeTypeTags(args.typeArguments),\n      args.functionArguments,\n    ),\n  );\n}\n\n/**\n * Generates a raw transaction\n *\n * @param args.aptosConfig AptosConfig\n * @param args.sender The transaction's sender account address as a hex input\n * @param args.payload The transaction payload - can create by using generateTransactionPayload()\n *\n * @returns RawTransaction\n */\nexport async function generateRawTransaction(args: {\n  aptosConfig: AptosConfig;\n  sender: AccountAddressInput;\n  payload: AnyTransactionPayloadInstance;\n  options?: InputGenerateTransactionOptions;\n  feePayerAddress?: AccountAddressInput;\n}): Promise<RawTransaction> {\n  const { aptosConfig, sender, payload, options, feePayerAddress } = args;\n\n  const getChainId = async () => {\n    if (NetworkToChainId[aptosConfig.network]) {\n      return { chainId: NetworkToChainId[aptosConfig.network] };\n    }\n    const info = await getLedgerInfo({ aptosConfig });\n    return { chainId: info.chain_id };\n  };\n\n  const getGasUnitPrice = async () => {\n    if (options?.gasUnitPrice) {\n      return { gasEstimate: options.gasUnitPrice };\n    }\n    const estimation = await getGasPriceEstimation({ aptosConfig });\n    return { gasEstimate: estimation.gas_estimate };\n  };\n\n  const getSequenceNumberForAny = async () => {\n    const getSequenceNumber = async () => {\n      if (options?.accountSequenceNumber !== undefined) {\n        return options.accountSequenceNumber;\n      }\n\n      return (await getInfo({ aptosConfig, accountAddress: sender })).sequence_number;\n    };\n\n    /**\n     * Check if is sponsored transaction to honor AIP-52\n     * {@link https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-52.md}\n     */\n    if (feePayerAddress && AccountAddress.from(feePayerAddress).equals(AccountAddress.ZERO)) {\n      // Handle sponsored transaction generation with the option that\n      // the main signer has not been created on chain\n      try {\n        // Check if main signer has been created on chain, if not assign sequence number 0\n        return await getSequenceNumber();\n      } catch (e: any) {\n        return 0;\n      }\n    } else {\n      return getSequenceNumber();\n    }\n  };\n  const [{ chainId }, { gasEstimate }, sequenceNumber] = await Promise.all([\n    getChainId(),\n    getGasUnitPrice(),\n    getSequenceNumberForAny(),\n  ]);\n\n  const { maxGasAmount, gasUnitPrice, expireTimestamp } = {\n    maxGasAmount: options?.maxGasAmount ? BigInt(options.maxGasAmount) : BigInt(DEFAULT_MAX_GAS_AMOUNT),\n    gasUnitPrice: options?.gasUnitPrice ?? BigInt(gasEstimate),\n    expireTimestamp: options?.expireTimestamp ?? BigInt(Math.floor(Date.now() / 1000) + DEFAULT_TXN_EXP_SEC_FROM_NOW),\n  };\n\n  return new RawTransaction(\n    AccountAddress.from(sender),\n    BigInt(sequenceNumber),\n    payload,\n    BigInt(maxGasAmount),\n    BigInt(gasUnitPrice),\n    BigInt(expireTimestamp),\n    new ChainId(chainId),\n  );\n}\n\n/**\n * We are defining function signatures, each with its specific input and output.\n * These are the possible function signature for our `generateTransaction` function.\n * When we call our `generateTransaction` function with the relevant type properties,\n * Typescript can infer the return type based on the appropriate function overload.\n */\nexport async function buildTransaction(args: InputGenerateSingleSignerRawTransactionArgs): Promise<SimpleTransaction>;\nexport async function buildTransaction(args: InputGenerateMultiAgentRawTransactionArgs): Promise<MultiAgentTransaction>;\n\n/**\n * Generates a transaction based on the provided arguments\n *\n * Note: we can start with one function to support all different payload/transaction types,\n * and if to complex to use, we could have function for each type\n *\n * @param args.aptosConfig AptosConfig\n * @param args.sender The transaction's sender account address as a hex input\n * @param args.payload The transaction payload - can create by using generateTransactionPayload()\n * @param args.options optional. Transaction options object\n * @param args.secondarySignerAddresses optional. For when want to create a multi signers transaction\n * @param args.feePayerAddress optional. For when want to create a fee payer (aka sponsored) transaction\n *\n * @return An instance of a RawTransaction, plus optional secondary/fee payer addresses\n * ```\n * {\n *  rawTransaction: RawTransaction,\n *  secondarySignerAddresses? : Array<AccountAddress>,\n *  feePayerAddress?: AccountAddress\n * }\n * ```\n */\nexport async function buildTransaction(args: InputGenerateRawTransactionArgs): Promise<AnyRawTransaction> {\n  const { aptosConfig, sender, payload, options, feePayerAddress } = args;\n  // generate raw transaction\n  const rawTxn = await generateRawTransaction({\n    aptosConfig,\n    sender,\n    payload,\n    options,\n    feePayerAddress,\n  });\n\n  // if multi agent transaction\n  if (\"secondarySignerAddresses\" in args) {\n    const signers: Array<AccountAddress> =\n      args.secondarySignerAddresses?.map((signer) => AccountAddress.from(signer)) ?? [];\n\n    return new MultiAgentTransaction(\n      rawTxn,\n      signers,\n      args.feePayerAddress ? AccountAddress.from(args.feePayerAddress) : undefined,\n    );\n  }\n  // return the raw transaction\n  return new SimpleTransaction(rawTxn, args.feePayerAddress ? AccountAddress.from(args.feePayerAddress) : undefined);\n}\n\n/**\n * Simulate a transaction before signing and submit to chain\n *\n * @param args.transaction A aptos transaction type to sign\n * @param args.signerPublicKey The signer public key\n * @param args.secondarySignersPublicKeys optional. The secondary signers public keys if multi signers transaction\n * @param args.feePayerPublicKey optional. The fee payer public key is a fee payer (aka sponsored) transaction\n * @param args.options optional. SimulateTransactionOptions\n *\n * @returns A signed serialized transaction that can be simulated\n */\nexport function generateSignedTransactionForSimulation(args: InputSimulateTransactionData): Uint8Array {\n  const { signerPublicKey, transaction, secondarySignersPublicKeys, feePayerPublicKey } = args;\n\n  const accountAuthenticator = getAuthenticatorForSimulation(signerPublicKey);\n\n  // fee payer transaction\n  if (transaction.feePayerAddress) {\n    const transactionToSign = new FeePayerRawTransaction(\n      transaction.rawTransaction,\n      transaction.secondarySignerAddresses ?? [],\n      transaction.feePayerAddress,\n    );\n    let secondaryAccountAuthenticators: Array<AccountAuthenticator> = [];\n    if (secondarySignersPublicKeys) {\n      secondaryAccountAuthenticators = secondarySignersPublicKeys.map((publicKey) =>\n        getAuthenticatorForSimulation(publicKey),\n      );\n    }\n    const feePayerAuthenticator = getAuthenticatorForSimulation(feePayerPublicKey!);\n\n    const transactionAuthenticator = new TransactionAuthenticatorFeePayer(\n      accountAuthenticator,\n      transaction.secondarySignerAddresses ?? [],\n      secondaryAccountAuthenticators,\n      {\n        address: transaction.feePayerAddress,\n        authenticator: feePayerAuthenticator,\n      },\n    );\n    return new SignedTransaction(transactionToSign.raw_txn, transactionAuthenticator).bcsToBytes();\n  }\n\n  // multi agent transaction\n  if (transaction.secondarySignerAddresses) {\n    const transactionToSign = new MultiAgentRawTransaction(\n      transaction.rawTransaction,\n      transaction.secondarySignerAddresses,\n    );\n\n    let secondaryAccountAuthenticators: Array<AccountAuthenticator> = [];\n\n    secondaryAccountAuthenticators = secondarySignersPublicKeys!.map((publicKey) =>\n      getAuthenticatorForSimulation(publicKey),\n    );\n\n    const transactionAuthenticator = new TransactionAuthenticatorMultiAgent(\n      accountAuthenticator,\n      transaction.secondarySignerAddresses,\n      secondaryAccountAuthenticators,\n    );\n\n    return new SignedTransaction(transactionToSign.raw_txn, transactionAuthenticator).bcsToBytes();\n  }\n\n  // single signer raw transaction\n  let transactionAuthenticator;\n  if (accountAuthenticator instanceof AccountAuthenticatorEd25519) {\n    transactionAuthenticator = new TransactionAuthenticatorEd25519(\n      accountAuthenticator.public_key,\n      accountAuthenticator.signature,\n    );\n  } else if (accountAuthenticator instanceof AccountAuthenticatorSingleKey) {\n    transactionAuthenticator = new TransactionAuthenticatorSingleSender(accountAuthenticator);\n  } else {\n    throw new Error(\"Invalid public key\");\n  }\n  return new SignedTransaction(transaction.rawTransaction, transactionAuthenticator).bcsToBytes();\n}\n\nexport function getAuthenticatorForSimulation(publicKey: PublicKey) {\n  if (publicKey instanceof KeylessPublicKey || publicKey instanceof Secp256k1PublicKey) {\n    // eslint-disable-next-line no-param-reassign\n    publicKey = new AnyPublicKey(publicKey);\n  }\n\n  // TODO add support for AnyMultiKey\n  if (publicKey instanceof AnyPublicKey) {\n    if (publicKey.publicKey instanceof Ed25519PublicKey) {\n      return new AccountAuthenticatorSingleKey(publicKey, new AnySignature(new Ed25519Signature(new Uint8Array(64))));\n    }\n    if (publicKey.publicKey instanceof Secp256k1PublicKey) {\n      return new AccountAuthenticatorSingleKey(publicKey, new AnySignature(new Secp256k1Signature(new Uint8Array(64))));\n    }\n    if (publicKey.publicKey instanceof KeylessPublicKey) {\n      return new AccountAuthenticatorSingleKey(publicKey, new AnySignature(KeylessSignature.getSimulationSignature()));\n    }\n  }\n\n  // legacy code\n  return new AccountAuthenticatorEd25519(\n    new Ed25519PublicKey(publicKey.toUint8Array()),\n    new Ed25519Signature(new Uint8Array(64)),\n  );\n}\n\n/**\n * Prepare a transaction to be submitted to chain\n *\n * @param args.transaction A aptos transaction type\n * @param args.senderAuthenticator The account authenticator of the transaction sender\n * @param args.secondarySignerAuthenticators optional. For when the transaction is a multi signers transaction\n *\n * @returns A SignedTransaction\n */\nexport function generateSignedTransaction(args: InputSubmitTransactionData): Uint8Array {\n  const { transaction, feePayerAuthenticator, additionalSignersAuthenticators } = args;\n  const senderAuthenticator = normalizeBundle(AccountAuthenticator, args.senderAuthenticator);\n\n  let txnAuthenticator: TransactionAuthenticator;\n  if (transaction.feePayerAddress) {\n    if (!feePayerAuthenticator) {\n      throw new Error(\"Must provide a feePayerAuthenticator argument to generate a signed fee payer transaction\");\n    }\n    txnAuthenticator = new TransactionAuthenticatorFeePayer(\n      senderAuthenticator,\n      transaction.secondarySignerAddresses ?? [],\n      additionalSignersAuthenticators ?? [],\n      {\n        address: transaction.feePayerAddress,\n        authenticator: feePayerAuthenticator,\n      },\n    );\n  } else if (transaction.secondarySignerAddresses) {\n    if (!additionalSignersAuthenticators) {\n      throw new Error(\n        \"Must provide a additionalSignersAuthenticators argument to generate a signed multi agent transaction\",\n      );\n    }\n    txnAuthenticator = new TransactionAuthenticatorMultiAgent(\n      senderAuthenticator,\n      transaction.secondarySignerAddresses,\n      additionalSignersAuthenticators,\n    );\n  } else if (senderAuthenticator instanceof AccountAuthenticatorEd25519) {\n    txnAuthenticator = new TransactionAuthenticatorEd25519(\n      senderAuthenticator.public_key,\n      senderAuthenticator.signature,\n    );\n  } else {\n    txnAuthenticator = new TransactionAuthenticatorSingleSender(senderAuthenticator);\n  }\n\n  return new SignedTransaction(transaction.rawTransaction, txnAuthenticator).bcsToBytes();\n}\n\n/**\n * Hashes the set of values with a SHA-3 256 hash\n * @param input array of UTF-8 strings or Uint8array byte arrays\n */\nexport function hashValues(input: (Uint8Array | string)[]): Uint8Array {\n  const hash = sha3Hash.create();\n  for (const item of input) {\n    hash.update(item);\n  }\n  return hash.digest();\n}\n\n/**\n * The domain separated prefix for hashing transacitons\n */\nconst TRANSACTION_PREFIX = hashValues([\"APTOS::Transaction\"]);\n\n/**\n * Generates a user transaction hash for the given transaction payload.  It must already have an authenticator\n * @param args InputSubmitTransactionData\n */\nexport function generateUserTransactionHash(args: InputSubmitTransactionData): string {\n  const signedTransaction = generateSignedTransaction(args);\n\n  // Transaction signature is defined as, the domain separated prefix based on struct (Transaction)\n  // Then followed by the type of the transaction for the enum, UserTransaction is 0\n  // Then followed by BCS encoded bytes of the signed transaction\n  return new Hex(hashValues([TRANSACTION_PREFIX, new Uint8Array([0]), signedTransaction])).toString();\n}\n\n/**\n * Fetches and caches ABIs with allowing for pass-through on provided ABIs\n * @param key\n * @param moduleAddress\n * @param moduleName\n * @param functionName\n * @param aptosConfig\n * @param abi\n * @param fetch\n */\nasync function fetchAbi<T extends FunctionABI>({\n  key,\n  moduleAddress,\n  moduleName,\n  functionName,\n  aptosConfig,\n  abi,\n  fetch,\n}: {\n  key: string;\n  moduleAddress: string;\n  moduleName: string;\n  functionName: string;\n  aptosConfig: AptosConfig;\n  abi?: T;\n  fetch: (moduleAddress: string, moduleName: string, functionName: string, aptosConfig: AptosConfig) => Promise<T>;\n}): Promise<T> {\n  if (abi !== undefined) {\n    return abi;\n  }\n\n  // We fetch the entry function ABI, and then pretend that we already had the ABI\n  return memoizeAsync(\n    async () => fetch(moduleAddress, moduleName, functionName, aptosConfig),\n    `${key}-${aptosConfig.network}-${moduleAddress}-${moduleName}-${functionName}`,\n    1000 * 60 * 5, // 5 minutes\n  )();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,SAASA,QAAA,IAAYC,EAAA,QAAgB;AA+FrC,eAAsBC,GACpBC,CAAA,EACwC;EACxC,IAAIC,EAAA,CAAkBD,CAAI,GACxB,OAAOE,EAAA,CAAiCF,CAAI;EAE9C,IAAM;MAAEG,aAAA,EAAAC,CAAA;MAAeC,UAAA,EAAAC,CAAA;MAAYC,YAAA,EAAAC;IAAa,IAAIC,CAAA,CAAiBT,CAAA,CAAKU,QAAQ;IAE5EC,CAAA,GAAc,MAAMC,EAAA,CAAS;MACjCC,GAAA,EAAK;MACLV,aAAA,EAAAC,CAAA;MACAC,UAAA,EAAAC,CAAA;MACAC,YAAA,EAAAC,CAAA;MACAM,WAAA,EAAad,CAAA,CAAKc,WAAA;MAClBC,GAAA,EAAKf,CAAA,CAAKe,GAAA;MACVC,KAAA,EAAOC;IACT,CAAC;EAGD,OAAOC,EAAA,CAAkC;IAAE,GAAGlB,CAAA;IAAMe,GAAA,EAAKJ;EAAY,CAAC,CACxE;AAAA;AAIO,SAASO,GACdlB,CAAA,EAC+B;EAC/B,IAAMI,CAAA,GAAcJ,CAAA,CAAKe,GAAA;IACnB;MAAEZ,aAAA,EAAAG,CAAA;MAAeD,UAAA,EAAAG,CAAA;MAAYD,YAAA,EAAAI;IAAa,IAAIF,CAAA,CAAiBT,CAAA,CAAKU,QAAQ;IAG5ES,CAAA,GAAgBC,CAAA,CAAoBpB,CAAA,CAAKqB,aAAa;EAG5D,IAAIF,CAAA,CAAcG,MAAA,KAAWlB,CAAA,CAAYmB,cAAA,CAAeD,MAAA,EACtD,MAAM,IAAIE,KAAA,CACR,0CAA0CpB,CAAA,CAAYmB,cAAA,CAAeD,MAAM,cAAcH,CAAA,CAAcG,MAAM,EAC/G;EAIF,IAAMG,CAAA,GAAuDzB,CAAA,CAAK0B,iBAAA,CAAkBC,GAAA,CAAI,CAACC,CAAA,EAAKC,CAAA,KAC5FC,CAAA,CAAgB9B,CAAA,CAAKU,QAAA,EAAUN,CAAA,EAAawB,CAAA,EAAKC,CAAA,EAAGV,CAAa,CACnE;EAGA,IAAIM,CAAA,CAAkBH,MAAA,KAAWlB,CAAA,CAAY2B,UAAA,CAAWT,MAAA,EACtD,MAAM,IAAIE,KAAA,CAER,0BAA0BlB,CAAa,KAAKE,CAAU,KAAKG,CAAY,eAAeP,CAAA,CAAY2B,UAAA,CAAWT,MAAM,YAAYG,CAAA,CAAkBH,MAAM,EACzJ;EAIF,IAAMU,CAAA,GAAuBC,CAAA,CAAcC,KAAA,CACzC,GAAG5B,CAAa,KAAKE,CAAU,IAC/BG,CAAA,EACAQ,CAAA,EACAM,CACF;EAGA,IAAI,qBAAqBzB,CAAA,EAAM;IAC7B,IAAM4B,CAAA,GAAkBO,CAAA,CAAeC,IAAA,CAAKpC,CAAA,CAAKqC,eAAe;IAChE,OAAO,IAAIC,CAAA,CACT,IAAIC,CAAA,CAASX,CAAA,EAAiB,IAAIY,CAAA,CAA2BR,CAAoB,CAAC,CACpF,CACF;EAAA;EAGA,OAAO,IAAIS,CAAA,CAAgCT,CAAoB,CACjE;AAAA;AAEA,eAAsBU,GAA4B1C,CAAA,EAAkE;EAClH,IAAM;MAAEG,aAAA,EAAAC,CAAA;MAAeC,UAAA,EAAAC,CAAA;MAAYC,YAAA,EAAAC;IAAa,IAAIC,CAAA,CAAiBT,CAAA,CAAKU,QAAQ;IAE5EC,CAAA,GAAc,MAAMC,EAAA,CAAS;MACjCC,GAAA,EAAK;MACLV,aAAA,EAAAC,CAAA;MACAC,UAAA,EAAAC,CAAA;MACAC,YAAA,EAAAC,CAAA;MACAM,WAAA,EAAad,CAAA,CAAKc,WAAA;MAClBC,GAAA,EAAKf,CAAA,CAAKe,GAAA;MACVC,KAAA,EAAO2B;IACT,CAAC;EAGD,OAAOC,EAAA,CAAmC;IAAE7B,GAAA,EAAKJ,CAAA;IAAa,GAAGX;EAAK,CAAC,CACzE;AAAA;AAEO,SAAS4C,GAAmC5C,CAAA,EAAmD;EACpG,IAAMI,CAAA,GAAcJ,CAAA,CAAKe,GAAA;IACnB;MAAEZ,aAAA,EAAAG,CAAA;MAAeD,UAAA,EAAAG,CAAA;MAAYD,YAAA,EAAAI;IAAa,IAAIF,CAAA,CAAiBT,CAAA,CAAKU,QAAQ;IAG5ES,CAAA,GAAgBC,CAAA,CAAoBpB,CAAA,CAAKqB,aAAa;EAG5D,IAAIF,CAAA,CAAcG,MAAA,KAAWlB,CAAA,CAAYmB,cAAA,CAAeD,MAAA,EACtD,MAAM,IAAIE,KAAA,CACR,0CAA0CpB,CAAA,CAAYmB,cAAA,CAAeD,MAAM,cAAcH,CAAA,CAAcG,MAAM,EAC/G;EAIF,IAAMG,CAAA,GACJzB,CAAA,EAAM0B,iBAAA,EAAmBC,GAAA,CAAI,CAACK,CAAA,EAAKJ,CAAA,KAAME,CAAA,CAAgB9B,CAAA,CAAKU,QAAA,EAAUN,CAAA,EAAa4B,CAAA,EAAKJ,CAAA,EAAGT,CAAa,CAAC,KAAK,EAAC;EAGnH,IAAIM,CAAA,CAAkBH,MAAA,KAAWlB,CAAA,CAAY2B,UAAA,CAAWT,MAAA,EACtD,MAAM,IAAIE,KAAA,CAER,0BAA0BlB,CAAa,KAAKE,CAAU,KAAKG,CAAY,eAAeP,CAAA,CAAY2B,UAAA,CAAWT,MAAM,YAAYG,CAAA,CAAkBH,MAAM,EACzJ;EAIF,OAAOW,CAAA,CAAcC,KAAA,CAAM,GAAG5B,CAAa,KAAKE,CAAU,IAAIG,CAAA,EAAcQ,CAAA,EAAeM,CAAiB,CAC9G;AAAA;AAEA,SAASvB,GAAiCF,CAAA,EAAuB;EAC/D,OAAO,IAAI6C,CAAA,CACT,IAAIC,CAAA,CACFC,CAAA,CAAIC,YAAA,CAAahD,CAAA,CAAKiD,QAAQ,EAAEC,YAAA,CAAa,GAC7C9B,CAAA,CAAoBpB,CAAA,CAAKqB,aAAa,GACtCrB,CAAA,CAAK0B,iBACP,CACF,CACF;AAAA;AAWA,eAAsByB,GAAuBnD,CAAA,EAMjB;EAC1B,IAAM;MAAEc,WAAA,EAAAV,CAAA;MAAagD,MAAA,EAAA9C,CAAA;MAAQ+C,OAAA,EAAA7C,CAAA;MAAS8C,OAAA,EAAA3C,CAAA;MAAS4C,eAAA,EAAApC;IAAgB,IAAInB,CAAA;IAE7DyB,CAAA,GAAa,MAAA+B,CAAA,KACbC,CAAA,CAAiBrD,CAAA,CAAYsD,OAAO,IAC/B;MAAEC,OAAA,EAASF,CAAA,CAAiBrD,CAAA,CAAYsD,OAAO;IAAE,IAGnD;MAAEC,OAAA,GADI,MAAMC,CAAA,CAAc;QAAE9C,WAAA,EAAAV;MAAY,CAAC,GACzByD;IAAS;IAG5B7B,CAAA,GAAkB,MAAA8B,CAAA,KAClBnD,CAAA,EAASoD,YAAA,GACJ;MAAEC,WAAA,EAAarD,CAAA,CAAQoD;IAAa,IAGtC;MAAEC,WAAA,GADU,MAAMC,EAAA,CAAsB;QAAEnD,WAAA,EAAAV;MAAY,CAAC,GAC7B8D;IAAa;IAG1CtC,CAAA,GAA0B,MAAAuC,CAAA,KAAY;MAC1C,IAAMC,CAAA,GAAoB,MAAAC,CAAA,KACpB1D,CAAA,EAAS2D,qBAAA,KAA0B,SAC9B3D,CAAA,CAAQ2D,qBAAA,IAGT,MAAMC,EAAA,CAAQ;QAAEzD,WAAA,EAAAV,CAAA;QAAaoE,cAAA,EAAgBlE;MAAO,CAAC,GAAGmE,eAAA;MAOlE,IAAItD,CAAA,IAAmBgB,CAAA,CAAeC,IAAA,CAAKjB,CAAe,EAAEuD,MAAA,CAAOvC,CAAA,CAAewC,IAAI,GAGpF,IAAI;QAEF,OAAO,MAAMP,CAAA,CAAkB,CACjC;MAAA,QAAiB;QACf,OAAO,CACT;MAAA,OAEA,OAAOA,CAAA,CAAkB,CAE7B;IAAA;IACM,CAAC;MAAET,OAAA,EAAA9B;IAAQ,GAAG;MAAEmC,WAAA,EAAAY;IAAY,GAAGC,CAAc,IAAI,MAAMC,OAAA,CAAQC,GAAA,CAAI,CACvEtD,CAAA,CAAW,GACXO,CAAA,CAAgB,GAChBJ,CAAA,CAAwB,CAC1B,CAAC;IAEK;MAAEoD,YAAA,EAAAC,EAAA;MAAclB,YAAA,EAAAmB,EAAA;MAAcC,eAAA,EAAAC;IAAgB,IAAI;MACtDJ,YAAA,EAAcrE,CAAA,EAASqE,YAAA,GAAeK,MAAA,CAAO1E,CAAA,CAAQqE,YAAY,IAAIK,MAAA,CAAO,GAAsB;MAClGtB,YAAA,EAAcpD,CAAA,EAASoD,YAAA,IAAgBsB,MAAA,CAAOT,CAAW;MACzDO,eAAA,EAAiBxE,CAAA,EAASwE,eAAA,IAAmBE,MAAA,CAAOC,IAAA,CAAKC,KAAA,CAAMC,IAAA,CAAKC,GAAA,CAAI,IAAI,GAAI,IAAI,EAA4B;IAClH;EAEA,OAAO,IAAIC,CAAA,CACTvD,CAAA,CAAeC,IAAA,CAAK9B,CAAM,GAC1B+E,MAAA,CAAOR,CAAc,GACrBrE,CAAA,EACA6E,MAAA,CAAOJ,EAAY,GACnBI,MAAA,CAAOH,EAAY,GACnBG,MAAA,CAAOD,EAAe,GACtB,IAAIO,CAAA,CAAQ9D,CAAO,CACrB,CACF;AAAA;AAiCA,eAAsB+D,GAAiB5F,CAAA,EAAmE;EACxG,IAAM;MAAEc,WAAA,EAAAV,CAAA;MAAagD,MAAA,EAAA9C,CAAA;MAAQ+C,OAAA,EAAA7C,CAAA;MAAS8C,OAAA,EAAA3C,CAAA;MAAS4C,eAAA,EAAApC;IAAgB,IAAInB,CAAA;IAE7DyB,CAAA,GAAS,MAAM0B,EAAA,CAAuB;MAC1CrC,WAAA,EAAAV,CAAA;MACAgD,MAAA,EAAA9C,CAAA;MACA+C,OAAA,EAAA7C,CAAA;MACA8C,OAAA,EAAA3C,CAAA;MACA4C,eAAA,EAAApC;IACF,CAAC;EAGD,IAAI,8BAA8BnB,CAAA,EAAM;IACtC,IAAMgC,CAAA,GACJhC,CAAA,CAAK6F,wBAAA,EAA0BlE,GAAA,CAAKC,CAAA,IAAWO,CAAA,CAAeC,IAAA,CAAKR,CAAM,CAAC,KAAK,EAAC;IAElF,OAAO,IAAIkE,CAAA,CACTrE,CAAA,EACAO,CAAA,EACAhC,CAAA,CAAKuD,eAAA,GAAkBpB,CAAA,CAAeC,IAAA,CAAKpC,CAAA,CAAKuD,eAAe,IAAI,MACrE,CACF;EAAA;EAEA,OAAO,IAAIwC,CAAA,CAAkBtE,CAAA,EAAQzB,CAAA,CAAKuD,eAAA,GAAkBpB,CAAA,CAAeC,IAAA,CAAKpC,CAAA,CAAKuD,eAAe,IAAI,MAAS,CACnH;AAAA;AAaO,SAASyC,GAAuChG,CAAA,EAAgD;EACrG,IAAM;MAAEiG,eAAA,EAAA7F,CAAA;MAAiB8F,WAAA,EAAA5F,CAAA;MAAa6F,0BAAA,EAAA3F,CAAA;MAA4B4F,iBAAA,EAAAzF;IAAkB,IAAIX,CAAA;IAElFmB,CAAA,GAAuBkF,CAAA,CAA8BjG,CAAe;EAG1E,IAAIE,CAAA,CAAYiD,eAAA,EAAiB;IAC/B,IAAMvB,CAAA,GAAoB,IAAIsE,CAAA,CAC5BhG,CAAA,CAAYiG,cAAA,EACZjG,CAAA,CAAYuF,wBAAA,IAA4B,EAAC,EACzCvF,CAAA,CAAYiD,eACd;MACI3B,CAAA,GAA8D,EAAC;IAC/DpB,CAAA,KACFoB,CAAA,GAAiCpB,CAAA,CAA2BmB,GAAA,CAAKkD,CAAA,IAC/DwB,CAAA,CAA8BxB,CAAS,CACzC;IAEF,IAAMhD,CAAA,GAAwBwE,CAAA,CAA8B1F,CAAkB;MAExEiE,CAAA,GAA2B,IAAI4B,CAAA,CACnCrF,CAAA,EACAb,CAAA,CAAYuF,wBAAA,IAA4B,EAAC,EACzCjE,CAAA,EACA;QACE6E,OAAA,EAASnG,CAAA,CAAYiD,eAAA;QACrBmD,aAAA,EAAe7E;MACjB,CACF;IACA,OAAO,IAAI8E,CAAA,CAAkB3E,CAAA,CAAkB4E,OAAA,EAAShC,CAAwB,EAAEiC,UAAA,CAAW,CAC/F;EAAA;EAGA,IAAIvG,CAAA,CAAYuF,wBAAA,EAA0B;IACxC,IAAM7D,CAAA,GAAoB,IAAI8E,CAAA,CAC5BxG,CAAA,CAAYiG,cAAA,EACZjG,CAAA,CAAYuF,wBACd;MAEIjE,CAAA,GAA8D,EAAC;IAEnEA,CAAA,GAAiCpB,CAAA,CAA4BmB,GAAA,CAAKiD,CAAA,IAChEyB,CAAA,CAA8BzB,CAAS,CACzC;IAEA,IAAM/C,CAAA,GAA2B,IAAIkF,CAAA,CACnC5F,CAAA,EACAb,CAAA,CAAYuF,wBAAA,EACZjE,CACF;IAEA,OAAO,IAAI+E,CAAA,CAAkB3E,CAAA,CAAkB4E,OAAA,EAAS/E,CAAwB,EAAEgF,UAAA,CAAW,CAC/F;EAAA;EAGA,IAAIpF,CAAA;EACJ,IAAIN,CAAA,YAAgC6F,CAAA,EAClCvF,CAAA,GAA2B,IAAIwF,CAAA,CAC7B9F,CAAA,CAAqB+F,UAAA,EACrB/F,CAAA,CAAqBgG,SACvB,WACShG,CAAA,YAAgCiG,CAAA,EACzC3F,CAAA,GAA2B,IAAI4F,CAAA,CAAqClG,CAAoB,OAExF,MAAM,IAAIK,KAAA,CAAM,oBAAoB;EAEtC,OAAO,IAAImF,CAAA,CAAkBrG,CAAA,CAAYiG,cAAA,EAAgB9E,CAAwB,EAAEoF,UAAA,CAAW,CAChG;AAAA;AAEO,SAASR,EAA8BrG,CAAA,EAAsB;EAOlE,KANIA,CAAA,YAAqBsH,CAAA,IAAoBtH,CAAA,YAAqBuH,CAAA,MAEhEvH,CAAA,GAAY,IAAIwH,CAAA,CAAaxH,CAAS,IAIpCA,CAAA,YAAqBwH,CAAA,EAAc;IACrC,IAAIxH,CAAA,CAAUyH,SAAA,YAAqBC,CAAA,EACjC,OAAO,IAAIN,CAAA,CAA8BpH,CAAA,EAAW,IAAI2H,CAAA,CAAa,IAAIC,CAAA,CAAiB,IAAIC,UAAA,CAAW,EAAE,CAAC,CAAC,CAAC;IAEhH,IAAI7H,CAAA,CAAUyH,SAAA,YAAqBF,CAAA,EACjC,OAAO,IAAIH,CAAA,CAA8BpH,CAAA,EAAW,IAAI2H,CAAA,CAAa,IAAIG,CAAA,CAAmB,IAAID,UAAA,CAAW,EAAE,CAAC,CAAC,CAAC;IAElH,IAAI7H,CAAA,CAAUyH,SAAA,YAAqBH,CAAA,EACjC,OAAO,IAAIF,CAAA,CAA8BpH,CAAA,EAAW,IAAI2H,CAAA,CAAaI,CAAA,CAAiBC,sBAAA,CAAuB,CAAC,CAAC,CAEnH;EAAA;EAGA,OAAO,IAAIhB,CAAA,CACT,IAAIU,CAAA,CAAiB1H,CAAA,CAAUkD,YAAA,CAAa,CAAC,GAC7C,IAAI0E,CAAA,CAAiB,IAAIC,UAAA,CAAW,EAAE,CAAC,CACzC,CACF;AAAA;AAWO,SAASI,GAA0BjI,CAAA,EAA8C;EACtF,IAAM;MAAEkG,WAAA,EAAA9F,CAAA;MAAa8H,qBAAA,EAAA5H,CAAA;MAAuB6H,+BAAA,EAAA3H;IAAgC,IAAIR,CAAA;IAC1EW,CAAA,GAAsByH,CAAA,CAAgBC,CAAA,EAAsBrI,CAAA,CAAKsI,mBAAmB;IAEtFnH,CAAA;EACJ,IAAIf,CAAA,CAAYmD,eAAA,EAAiB;IAC/B,IAAI,CAACjD,CAAA,EACH,MAAM,IAAIkB,KAAA,CAAM,0FAA0F;IAE5GL,CAAA,GAAmB,IAAIqF,CAAA,CACrB7F,CAAA,EACAP,CAAA,CAAYyF,wBAAA,IAA4B,EAAC,EACzCrF,CAAA,IAAmC,EAAC,EACpC;MACEiG,OAAA,EAASrG,CAAA,CAAYmD,eAAA;MACrBmD,aAAA,EAAepG;IACjB,CACF,CACF;EAAA,WAAWF,CAAA,CAAYyF,wBAAA,EAA0B;IAC/C,IAAI,CAACrF,CAAA,EACH,MAAM,IAAIgB,KAAA,CACR,sGACF;IAEFL,CAAA,GAAmB,IAAI4F,CAAA,CACrBpG,CAAA,EACAP,CAAA,CAAYyF,wBAAA,EACZrF,CACF,CACF;EAAA,OAAWG,CAAA,YAA+BqG,CAAA,GACxC7F,CAAA,GAAmB,IAAI8F,CAAA,CACrBtG,CAAA,CAAoBuG,UAAA,EACpBvG,CAAA,CAAoBwG,SACtB,IAEAhG,CAAA,GAAmB,IAAIkG,CAAA,CAAqC1G,CAAmB;EAGjF,OAAO,IAAIgG,CAAA,CAAkBvG,CAAA,CAAYmG,cAAA,EAAgBpF,CAAgB,EAAE0F,UAAA,CAAW,CACxF;AAAA;AAMO,SAAS0B,GAAWvI,CAAA,EAA4C;EACrE,IAAMI,CAAA,GAAON,EAAA,CAAS0I,MAAA,CAAO;EAC7B,SAAWlI,CAAA,IAAQN,CAAA,EACjBI,CAAA,CAAKqI,MAAA,CAAOnI,CAAI;EAElB,OAAOF,CAAA,CAAKsI,MAAA,CAAO,CACrB;AAAA;AAKA,IAAMC,EAAA,GAAqBJ,EAAA,CAAW,CAAC,oBAAoB,CAAC;AAMrD,SAASK,GAA4B5I,CAAA,EAA0C;EACpF,IAAMI,CAAA,GAAoB6H,EAAA,CAA0BjI,CAAI;EAKxD,OAAO,IAAI+C,CAAA,CAAIwF,EAAA,CAAW,CAACI,EAAA,EAAoB,IAAId,UAAA,CAAW,CAAC,CAAC,CAAC,GAAGzH,CAAiB,CAAC,CAAC,EAAEyI,QAAA,CAAS,CACpG;AAAA;AAYA,eAAejI,GAAgC;EAC7CC,GAAA,EAAAb,CAAA;EACAG,aAAA,EAAAC,CAAA;EACAC,UAAA,EAAAC,CAAA;EACAC,YAAA,EAAAC,CAAA;EACAM,WAAA,EAAAH,CAAA;EACAI,GAAA,EAAAI,CAAA;EACAH,KAAA,EAAAS;AACF,GAQe;EACb,OAAIN,CAAA,KAAQ,SACHA,CAAA,GAIF2H,CAAA,CACL,YAAYrH,CAAA,CAAMrB,CAAA,EAAeE,CAAA,EAAYE,CAAA,EAAcG,CAAW,GACtE,GAAGX,CAAG,IAAIW,CAAA,CAAY+C,OAAO,IAAItD,CAAa,IAAIE,CAAU,IAAIE,CAAY,IAC5E,MAAO,KAAK,CACd,EAAE,CACJ;AAAA;AAAA,SAAAT,EAAA,IAAAY,CAAA,EAAAO,EAAA,IAAAqG,CAAA,EAAA7E,EAAA,IAAAV,CAAA,EAAAY,EAAA,IAAAf,CAAA,EAAAsB,EAAA,IAAA7C,CAAA,EAAAsF,EAAA,IAAAe,CAAA,EAAAX,EAAA,IAAA5B,CAAA,EAAAiC,CAAA,IAAAxB,CAAA,EAAAoD,EAAA,IAAAzH,CAAA,EAAA+H,EAAA,IAAAjC,CAAA,EAAAsC,EAAA,IAAAb,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}