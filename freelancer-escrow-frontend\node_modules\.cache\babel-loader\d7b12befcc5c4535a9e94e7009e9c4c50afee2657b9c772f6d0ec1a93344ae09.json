{"ast": null, "code": "import { a as r } from \"./chunk-A63SMUOU.mjs\";\nimport { b as t } from \"./chunk-BCUSI3N6.mjs\";\nvar o = class extends r {\n  toString() {\n    let i = this.toUint8Array();\n    return t.fromHexInput(i).toString();\n  }\n};\nexport { o as a };", "map": {"version": 3, "names": ["o", "r", "toString", "i", "toUint8Array", "t", "fromHexInput", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\signature.ts"], "sourcesContent": ["import { Serializable } from \"../../bcs\";\nimport { Hex } from \"../hex\";\n\n/**\n * An abstract representation of a crypto signature,\n * associated to a specific signature scheme e.g. Ed25519 or Secp256k1\n *\n * This is the product of signing a message directly from a PrivateKey\n * and can be verified against a CryptoPublicKey.\n */\nexport abstract class Signature extends Serializable {\n  /**\n   * Get the raw signature bytes\n   */\n  abstract toUint8Array(): Uint8Array;\n\n  /**\n   * Get the signature as a hex string with a 0x prefix e.g. 0x123456...\n   */\n  toString(): string {\n    const bytes = this.toUint8Array();\n    return Hex.fromHexInput(bytes).toString();\n  }\n}\n\n/**\n * An abstract representation of an account signature,\n * associated to a specific authentication scheme e.g. Ed25519 or SingleKey\n *\n * This is the product of signing a message through an account,\n * and can be verified against an AccountPublicKey.\n */\n// export abstract class AccountSignature extends Serializable {\n//   /**\n//    * Get the raw signature bytes\n//    */\n//   abstract toUint8Array(): Uint8Array;\n//\n//   /**\n//    * Get the signature as a hex string with a 0x prefix e.g. 0x123456...\n//    */\n//   toString(): string {\n//     const bytes = this.toUint8Array();\n//     return Hex.fromHexInput(bytes).toString();\n//   }\n// }\n"], "mappings": ";;AAUO,IAAeA,CAAA,GAAf,cAAiCC,CAAa;EASnDC,SAAA,EAAmB;IACjB,IAAMC,CAAA,GAAQ,KAAKC,YAAA,CAAa;IAChC,OAAOC,CAAA,CAAIC,YAAA,CAAaH,CAAK,EAAED,QAAA,CAAS,CAC1C;EAAA;AACF;AAAA,SAAAF,CAAA,IAAAO,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}