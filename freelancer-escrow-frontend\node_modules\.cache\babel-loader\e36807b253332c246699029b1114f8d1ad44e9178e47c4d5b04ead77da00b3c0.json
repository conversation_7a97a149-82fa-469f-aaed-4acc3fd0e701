{"ast": null, "code": "var o = \"1.18.1\";\nexport { o as a };", "map": {"version": 3, "names": ["o", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\version.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * The current version of the SDK\n *\n * hardcoded for now, we would want to have it injected dynamically\n */\nexport const VERSION = \"1.18.1\";\n"], "mappings": "AAQO,IAAMA,CAAA,GAAU;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}