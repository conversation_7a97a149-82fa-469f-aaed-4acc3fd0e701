{"ast": null, "code": "var h = Object.defineProperty;\nvar i = Object.getOwnPropertyDescriptor;\nvar j = (g, b, d, c) => {\n  for (var a = c > 1 ? void 0 : c ? i(b, d) : b, e = g.length - 1, f; e >= 0; e--) (f = g[e]) && (a = (c ? f(b, d, a) : f(a)) || a);\n  return c && a && h(b, d, a), a;\n};\nexport { j as a };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["var h=Object.defineProperty;var i=Object.getOwnPropertyDescriptor;var j=(g,b,d,c)=>{for(var a=c>1?void 0:c?i(b,d):b,e=g.length-1,f;e>=0;e--)(f=g[e])&&(a=(c?f(b,d,a):f(a))||a);return c&&a&&h(b,d,a),a};export{j as a};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}