{"ast": null, "code": "import { a as t, c as n } from \"./chunk-A2Z7I2EY.mjs\";\nimport { a as l } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as u } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as s } from \"./chunk-DZXM2MQY.mjs\";\nimport { b as o } from \"./chunk-BCUSI3N6.mjs\";\nvar c = class i extends l {\n    constructor(e) {\n      super();\n      let r = e.constructor.name;\n      switch (r) {\n        case t.name:\n          this.publicKey = e, this.variant = 0;\n          break;\n        default:\n          throw new Error(`Unsupported key for EphemeralPublicKey - ${r}`);\n      }\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    verifySignature(e) {\n      let {\n        message: r,\n        signature: a\n      } = e;\n      return this.publicKey.verifySignature({\n        message: r,\n        signature: a\n      });\n    }\n    serialize(e) {\n      if (this.publicKey instanceof t) e.serializeU32AsUleb128(0), this.publicKey.serialize(e);else throw new Error(\"Unknown public key type\");\n    }\n    static deserialize(e) {\n      let r = e.deserializeUleb128AsU32();\n      switch (r) {\n        case 0:\n          return new i(t.deserialize(e));\n        default:\n          throw new Error(`Unknown variant index for EphemeralPublicKey: ${r}`);\n      }\n    }\n    static isPublicKey(e) {\n      return e instanceof i;\n    }\n  },\n  p = class i extends u {\n    constructor(e) {\n      super();\n      let r = e.constructor.name;\n      switch (r) {\n        case n.name:\n          this.signature = e;\n          break;\n        default:\n          throw new Error(`Unsupported signature for EphemeralSignature - ${r}`);\n      }\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    static fromHex(e) {\n      let r = o.fromHexInput(e),\n        a = new s(r.toUint8Array());\n      return i.deserialize(a);\n    }\n    serialize(e) {\n      if (this.signature instanceof n) e.serializeU32AsUleb128(0), this.signature.serialize(e);else throw new Error(\"Unknown signature type\");\n    }\n    static deserialize(e) {\n      let r = e.deserializeUleb128AsU32();\n      switch (r) {\n        case 0:\n          return new i(n.deserialize(e));\n        default:\n          throw new Error(`Unknown variant index for EphemeralSignature: ${r}`);\n      }\n    }\n  };\nexport { c as a, p as b };", "map": {"version": 3, "names": ["c", "i", "l", "constructor", "e", "r", "name", "t", "public<PERSON>ey", "variant", "Error", "toUint8Array", "bcsToBytes", "verifySignature", "message", "signature", "a", "serialize", "serializeU32AsUleb128", "deserialize", "deserializeUleb128AsU32", "isPublicKey", "p", "u", "n", "fromHex", "o", "fromHexInput", "s", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\ephemeral.ts"], "sourcesContent": ["import { Serializer, Deserializer } from \"../../bcs\";\nimport { EphemeralPublicKeyVariant, EphemeralSignatureVariant, HexInput } from \"../../types\";\nimport { PublicKey } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { Ed25519PublicKey, Ed25519Signature } from \"./ed25519\";\nimport { Hex } from \"../hex\";\n\n/**\n * Represents ephemeral public keys for Aptos Keyless accounts.\n *\n * These are not public keys used as a public key on an account.  They are only used ephemerally on Keyless accounts.\n */\nexport class EphemeralPublicKey extends PublicKey {\n  /**\n   * The public key itself\n   */\n  public readonly publicKey: PublicKey;\n\n  /**\n   * An enum indicating the scheme of the ephemeral public key\n   */\n  public readonly variant: EphemeralPublicKeyVariant;\n\n  constructor(publicKey: PublicKey) {\n    super();\n    const publicKeyType = publicKey.constructor.name;\n    switch (publicKeyType) {\n      case Ed25519PublicKey.name:\n        this.publicKey = publicKey;\n        this.variant = EphemeralPublicKeyVariant.Ed25519;\n        break;\n      default:\n        throw new Error(`Unsupported key for EphemeralPublicKey - ${publicKeyType}`);\n    }\n  }\n\n  /**\n   * Get the public key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the public key\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  /**\n   * Verifies a signed data with a the ephemeral public key\n   *\n   * @param args.message message\n   * @param args.signature The signature that was signed by the private key of the ephemeral public key\n   * @returns true if the signature is valid\n   */\n  verifySignature(args: { message: HexInput; signature: EphemeralSignature }): boolean {\n    const { message, signature } = args;\n    return this.publicKey.verifySignature({ message, signature });\n  }\n\n  serialize(serializer: Serializer): void {\n    if (this.publicKey instanceof Ed25519PublicKey) {\n      serializer.serializeU32AsUleb128(EphemeralPublicKeyVariant.Ed25519);\n      this.publicKey.serialize(serializer);\n    } else {\n      throw new Error(\"Unknown public key type\");\n    }\n  }\n\n  static deserialize(deserializer: Deserializer): EphemeralPublicKey {\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case EphemeralPublicKeyVariant.Ed25519:\n        return new EphemeralPublicKey(Ed25519PublicKey.deserialize(deserializer));\n      default:\n        throw new Error(`Unknown variant index for EphemeralPublicKey: ${index}`);\n    }\n  }\n\n  static isPublicKey(publicKey: PublicKey): publicKey is EphemeralPublicKey {\n    return publicKey instanceof EphemeralPublicKey;\n  }\n}\n\n/**\n * Represents ephemeral signatures used in Aptos Keyless accounts.\n *\n * These signatures are used inside of KeylessSignature\n */\nexport class EphemeralSignature extends Signature {\n  /**\n   * The signature signed by the private key of an EphemeralKeyPair\n   */\n  public readonly signature: Signature;\n\n  constructor(signature: Signature) {\n    super();\n    const signatureType = signature.constructor.name;\n    switch (signatureType) {\n      case Ed25519Signature.name:\n        this.signature = signature;\n        break;\n      default:\n        throw new Error(`Unsupported signature for EphemeralSignature - ${signatureType}`);\n    }\n  }\n\n  /**\n   * Get the public key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the public key\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  static fromHex(hexInput: HexInput): EphemeralSignature {\n    const data = Hex.fromHexInput(hexInput);\n    const deserializer = new Deserializer(data.toUint8Array());\n    return EphemeralSignature.deserialize(deserializer);\n  }\n\n  serialize(serializer: Serializer): void {\n    if (this.signature instanceof Ed25519Signature) {\n      serializer.serializeU32AsUleb128(EphemeralSignatureVariant.Ed25519);\n      this.signature.serialize(serializer);\n    } else {\n      throw new Error(\"Unknown signature type\");\n    }\n  }\n\n  static deserialize(deserializer: Deserializer): EphemeralSignature {\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case EphemeralSignatureVariant.Ed25519:\n        return new EphemeralSignature(Ed25519Signature.deserialize(deserializer));\n      default:\n        throw new Error(`Unknown variant index for EphemeralSignature: ${index}`);\n    }\n  }\n}\n"], "mappings": ";;;;;AAYO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAA2BC,CAAU;IAWhDC,YAAYC,CAAA,EAAsB;MAChC,MAAM;MACN,IAAMC,CAAA,GAAgBD,CAAA,CAAUD,WAAA,CAAYG,IAAA;MAC5C,QAAQD,CAAA;QACN,KAAKE,CAAA,CAAiBD,IAAA;UACpB,KAAKE,SAAA,GAAYJ,CAAA,EACjB,KAAKK,OAAA,GAAU;UACf;QACF;UACE,MAAM,IAAIC,KAAA,CAAM,4CAA4CL,CAAa,EAAE,CAC/E;MAAA;IACF;IAOAM,aAAA,EAA2B;MACzB,OAAO,KAAKC,UAAA,CAAW,CACzB;IAAA;IASAC,gBAAgBT,CAAA,EAAqE;MACnF,IAAM;QAAEU,OAAA,EAAAT,CAAA;QAASU,SAAA,EAAAC;MAAU,IAAIZ,CAAA;MAC/B,OAAO,KAAKI,SAAA,CAAUK,eAAA,CAAgB;QAAEC,OAAA,EAAAT,CAAA;QAASU,SAAA,EAAAC;MAAU,CAAC,CAC9D;IAAA;IAEAC,UAAUb,CAAA,EAA8B;MACtC,IAAI,KAAKI,SAAA,YAAqBD,CAAA,EAC5BH,CAAA,CAAWc,qBAAA,EAAuD,GAClE,KAAKV,SAAA,CAAUS,SAAA,CAAUb,CAAU,OAEnC,MAAM,IAAIM,KAAA,CAAM,yBAAyB,CAE7C;IAAA;IAEA,OAAOS,YAAYf,CAAA,EAAgD;MACjE,IAAMC,CAAA,GAAQD,CAAA,CAAagB,uBAAA,CAAwB;MACnD,QAAQf,CAAA;QACN;UACE,OAAO,IAAIJ,CAAA,CAAmBM,CAAA,CAAiBY,WAAA,CAAYf,CAAY,CAAC;QAC1E;UACE,MAAM,IAAIM,KAAA,CAAM,iDAAiDL,CAAK,EAAE,CAC5E;MAAA;IACF;IAEA,OAAOgB,YAAYjB,CAAA,EAAuD;MACxE,OAAOA,CAAA,YAAqBH,CAC9B;IAAA;EACF;EAOaqB,CAAA,GAAN,MAAMrB,CAAA,SAA2BsB,CAAU;IAMhDpB,YAAYC,CAAA,EAAsB;MAChC,MAAM;MACN,IAAMC,CAAA,GAAgBD,CAAA,CAAUD,WAAA,CAAYG,IAAA;MAC5C,QAAQD,CAAA;QACN,KAAKmB,CAAA,CAAiBlB,IAAA;UACpB,KAAKS,SAAA,GAAYX,CAAA;UACjB;QACF;UACE,MAAM,IAAIM,KAAA,CAAM,kDAAkDL,CAAa,EAAE,CACrF;MAAA;IACF;IAOAM,aAAA,EAA2B;MACzB,OAAO,KAAKC,UAAA,CAAW,CACzB;IAAA;IAEA,OAAOa,QAAQrB,CAAA,EAAwC;MACrD,IAAMC,CAAA,GAAOqB,CAAA,CAAIC,YAAA,CAAavB,CAAQ;QAChCY,CAAA,GAAe,IAAIY,CAAA,CAAavB,CAAA,CAAKM,YAAA,CAAa,CAAC;MACzD,OAAOV,CAAA,CAAmBkB,WAAA,CAAYH,CAAY,CACpD;IAAA;IAEAC,UAAUb,CAAA,EAA8B;MACtC,IAAI,KAAKW,SAAA,YAAqBS,CAAA,EAC5BpB,CAAA,CAAWc,qBAAA,EAAuD,GAClE,KAAKH,SAAA,CAAUE,SAAA,CAAUb,CAAU,OAEnC,MAAM,IAAIM,KAAA,CAAM,wBAAwB,CAE5C;IAAA;IAEA,OAAOS,YAAYf,CAAA,EAAgD;MACjE,IAAMC,CAAA,GAAQD,CAAA,CAAagB,uBAAA,CAAwB;MACnD,QAAQf,CAAA;QACN;UACE,OAAO,IAAIJ,CAAA,CAAmBuB,CAAA,CAAiBL,WAAA,CAAYf,CAAY,CAAC;QAC1E;UACE,MAAM,IAAIM,KAAA,CAAM,iDAAiDL,CAAK,EAAE,CAC5E;MAAA;IACF;EACF;AAAA,SAAAL,CAAA,IAAAgB,CAAA,EAAAM,CAAA,IAAAO,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}