{"ast": null, "code": "import { a, c } from \"./chunk-6ZQWPHLV.mjs\";\nimport { i as o, j as l } from \"./chunk-GFRNBBTY.mjs\";\nimport { a as n, c as s } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as p } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as y } from \"./chunk-77NXCSLY.mjs\";\nvar f = class t extends b {\n    constructor(e) {\n      if (super(), this.publicKey = e, e instanceof n) this.variant = 0;else if (e instanceof a) this.variant = 1;else if (e instanceof o) this.variant = 3;else throw new Error(\"Unsupported public key type\");\n    }\n    verifySignature(e) {\n      let {\n        message: r,\n        signature: i\n      } = e;\n      return i instanceof u ? this.publicKey.verifySignature({\n        message: r,\n        signature: i.signature\n      }) : !1;\n    }\n    authKey() {\n      return y.fromSchemeAndBytes({\n        scheme: 2,\n        input: this.toUint8Array()\n      });\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(this.variant), this.publicKey.serialize(e);\n    }\n    static deserialize(e) {\n      let r = e.deserializeUleb128AsU32(),\n        i;\n      switch (r) {\n        case 0:\n          i = n.deserialize(e);\n          break;\n        case 1:\n          i = a.deserialize(e);\n          break;\n        case 3:\n          i = o.deserialize(e);\n          break;\n        default:\n          throw new Error(`Unknown variant index for AnyPublicKey: ${r}`);\n      }\n      return new t(i);\n    }\n    static isPublicKey(e) {\n      return e instanceof t;\n    }\n    isEd25519() {\n      return this.publicKey instanceof n;\n    }\n    isSecp256k1PublicKey() {\n      return this.publicKey instanceof a;\n    }\n  },\n  u = class t extends p {\n    constructor(e) {\n      if (super(), this.signature = e, e instanceof s) this.variant = 0;else if (e instanceof c) this.variant = 1;else if (e instanceof l) this.variant = 3;else throw new Error(\"Unsupported signature type\");\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(this.variant), this.signature.serialize(e);\n    }\n    static deserialize(e) {\n      let r = e.deserializeUleb128AsU32(),\n        i;\n      switch (r) {\n        case 0:\n          i = s.deserialize(e);\n          break;\n        case 1:\n          i = c.deserialize(e);\n          break;\n        case 3:\n          i = l.deserialize(e);\n          break;\n        default:\n          throw new Error(`Unknown variant index for AnySignature: ${r}`);\n      }\n      return new t(i);\n    }\n  };\nexport { f as a, u as b };", "map": {"version": 3, "names": ["f", "t", "b", "constructor", "e", "public<PERSON>ey", "n", "variant", "a", "o", "Error", "verifySignature", "message", "r", "signature", "i", "u", "auth<PERSON><PERSON>", "y", "fromSchemeAndBytes", "scheme", "input", "toUint8Array", "bcsToBytes", "serialize", "serializeU32AsUleb128", "deserialize", "deserializeUleb128AsU32", "isPublicKey", "isEd25519", "isSecp256k1PublicKey", "p", "s", "c", "l"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\singleKey.ts"], "sourcesContent": ["import { Deserializer, Serializer } from \"../../bcs\";\nimport { AnyPublicKeyVariant, AnySignatureVariant, SigningScheme as AuthenticationKeyScheme } from \"../../types\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Ed25519PublicKey, Ed25519Signature } from \"./ed25519\";\nimport { AccountPublicKey, PublicKey, VerifySignatureArgs } from \"./publicKey\";\nimport { Secp256k1PublicKey, Secp256k1Signature } from \"./secp256k1\";\nimport { KeylessPublicKey, KeylessSignature } from \"./keyless\";\nimport { Signature } from \"./signature\";\n\n/**\n * Represents any public key supported by Aptos.\n *\n * Since [AIP-55](https://github.com/aptos-foundation/AIPs/pull/263) Aptos supports\n * `Legacy` and `Unified` authentication keys.\n *\n * Any unified authentication key is represented in the SDK as `AnyPublicKey`.\n */\nexport class Any<PERSON><PERSON><PERSON><PERSON><PERSON> extends AccountPublicKey {\n  /**\n   * Reference to the inner public key\n   */\n  public readonly publicKey: PublicKey;\n\n  /**\n   * Index of the underlying enum variant\n   */\n  public readonly variant: AnyPublicKeyVariant;\n\n  // region Constructors\n\n  constructor(publicKey: PublicKey) {\n    super();\n    this.publicKey = publicKey;\n    if (publicKey instanceof Ed25519PublicKey) {\n      this.variant = AnyPublicKeyVariant.Ed25519;\n    } else if (publicKey instanceof Secp256k1PublicKey) {\n      this.variant = AnyPublicKeyVariant.Secp256k1;\n    } else if (publicKey instanceof KeylessPublicKey) {\n      this.variant = AnyPublicKeyVariant.Keyless;\n    } else {\n      throw new Error(\"Unsupported public key type\");\n    }\n  }\n\n  // endregion\n\n  // region AccountPublicKey\n\n  verifySignature(args: VerifySignatureArgs): boolean {\n    const { message, signature } = args;\n    if (!(signature instanceof AnySignature)) {\n      return false;\n    }\n\n    return this.publicKey.verifySignature({\n      message,\n      signature: signature.signature,\n    });\n  }\n\n  authKey(): AuthenticationKey {\n    return AuthenticationKey.fromSchemeAndBytes({\n      scheme: AuthenticationKeyScheme.SingleKey,\n      input: this.toUint8Array(),\n    });\n  }\n\n  toUint8Array() {\n    return this.bcsToBytes();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.variant);\n    this.publicKey.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): AnyPublicKey {\n    const variantIndex = deserializer.deserializeUleb128AsU32();\n    let publicKey: PublicKey;\n    switch (variantIndex) {\n      case AnyPublicKeyVariant.Ed25519:\n        publicKey = Ed25519PublicKey.deserialize(deserializer);\n        break;\n      case AnyPublicKeyVariant.Secp256k1:\n        publicKey = Secp256k1PublicKey.deserialize(deserializer);\n        break;\n      case AnyPublicKeyVariant.Keyless:\n        publicKey = KeylessPublicKey.deserialize(deserializer);\n        break;\n      default:\n        throw new Error(`Unknown variant index for AnyPublicKey: ${variantIndex}`);\n    }\n    return new AnyPublicKey(publicKey);\n  }\n\n  // endregion\n\n  /**\n   * @deprecated use `instanceof AnyPublicKey` instead.\n   */\n  static isPublicKey(publicKey: AccountPublicKey): publicKey is AnyPublicKey {\n    return publicKey instanceof AnyPublicKey;\n  }\n\n  /**\n   * @deprecated use `publicKey instanceof Ed25519PublicKey` instead.\n   */\n  isEd25519(): boolean {\n    return this.publicKey instanceof Ed25519PublicKey;\n  }\n\n  /**\n   * @deprecated use `publicKey instanceof Secp256k1PublicKey` instead.\n   */\n  isSecp256k1PublicKey(): boolean {\n    return this.publicKey instanceof Secp256k1PublicKey;\n  }\n}\n\n/**\n * Instance of signature that uses the SingleKey authentication scheme.\n * This signature can only be generated by a `SingleKeySigner`, since it uses the\n * same authentication scheme.\n */\nexport class AnySignature extends Signature {\n  public readonly signature: Signature;\n\n  /**\n   * Index of the underlying enum variant\n   */\n  private readonly variant: AnySignatureVariant;\n\n  // region Constructors\n\n  constructor(signature: Signature) {\n    super();\n    this.signature = signature;\n\n    if (signature instanceof Ed25519Signature) {\n      this.variant = AnySignatureVariant.Ed25519;\n    } else if (signature instanceof Secp256k1Signature) {\n      this.variant = AnySignatureVariant.Secp256k1;\n    } else if (signature instanceof KeylessSignature) {\n      this.variant = AnySignatureVariant.Keyless;\n    } else {\n      throw new Error(\"Unsupported signature type\");\n    }\n  }\n\n  // endregion\n\n  // region AccountSignature\n\n  toUint8Array() {\n    return this.bcsToBytes();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.variant);\n    this.signature.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): AnySignature {\n    const variantIndex = deserializer.deserializeUleb128AsU32();\n    let signature: Signature;\n    switch (variantIndex) {\n      case AnySignatureVariant.Ed25519:\n        signature = Ed25519Signature.deserialize(deserializer);\n        break;\n      case AnySignatureVariant.Secp256k1:\n        signature = Secp256k1Signature.deserialize(deserializer);\n        break;\n      case AnySignatureVariant.Keyless:\n        signature = KeylessSignature.deserialize(deserializer);\n        break;\n      default:\n        throw new Error(`Unknown variant index for AnySignature: ${variantIndex}`);\n    }\n    return new AnySignature(signature);\n  }\n\n  // endregion\n}\n"], "mappings": ";;;;;;AAiBO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAqBC,CAAiB;IAajDC,YAAYC,CAAA,EAAsB;MAGhC,IAFA,MAAM,GACN,KAAKC,SAAA,GAAYD,CAAA,EACbA,CAAA,YAAqBE,CAAA,EACvB,KAAKC,OAAA,GAAU,WACNH,CAAA,YAAqBI,CAAA,EAC9B,KAAKD,OAAA,GAAU,WACNH,CAAA,YAAqBK,CAAA,EAC9B,KAAKF,OAAA,GAAU,OAEf,MAAM,IAAIG,KAAA,CAAM,6BAA6B,CAEjD;IAAA;IAMAC,gBAAgBP,CAAA,EAAoC;MAClD,IAAM;QAAEQ,OAAA,EAAAC,CAAA;QAASC,SAAA,EAAAC;MAAU,IAAIX,CAAA;MAC/B,OAAMW,CAAA,YAAqBC,CAAA,GAIpB,KAAKX,SAAA,CAAUM,eAAA,CAAgB;QACpCC,OAAA,EAAAC,CAAA;QACAC,SAAA,EAAWC,CAAA,CAAUD;MACvB,CAAC,IANQ,EAOX;IAAA;IAEAG,QAAA,EAA6B;MAC3B,OAAOC,CAAA,CAAkBC,kBAAA,CAAmB;QAC1CC,MAAA;QACAC,KAAA,EAAO,KAAKC,YAAA,CAAa;MAC3B,CAAC,CACH;IAAA;IAEAA,aAAA,EAAe;MACb,OAAO,KAAKC,UAAA,CAAW,CACzB;IAAA;IAMAC,UAAUpB,CAAA,EAA8B;MACtCA,CAAA,CAAWqB,qBAAA,CAAsB,KAAKlB,OAAO,GAC7C,KAAKF,SAAA,CAAUmB,SAAA,CAAUpB,CAAU,CACrC;IAAA;IAEA,OAAOsB,YAAYtB,CAAA,EAA0C;MAC3D,IAAMS,CAAA,GAAeT,CAAA,CAAauB,uBAAA,CAAwB;QACtDZ,CAAA;MACJ,QAAQF,CAAA;QACN;UACEE,CAAA,GAAYT,CAAA,CAAiBoB,WAAA,CAAYtB,CAAY;UACrD;QACF;UACEW,CAAA,GAAYP,CAAA,CAAmBkB,WAAA,CAAYtB,CAAY;UACvD;QACF;UACEW,CAAA,GAAYN,CAAA,CAAiBiB,WAAA,CAAYtB,CAAY;UACrD;QACF;UACE,MAAM,IAAIM,KAAA,CAAM,2CAA2CG,CAAY,EAAE,CAC7E;MAAA;MACA,OAAO,IAAIZ,CAAA,CAAac,CAAS,CACnC;IAAA;IAOA,OAAOa,YAAYxB,CAAA,EAAwD;MACzE,OAAOA,CAAA,YAAqBH,CAC9B;IAAA;IAKA4B,UAAA,EAAqB;MACnB,OAAO,KAAKxB,SAAA,YAAqBC,CACnC;IAAA;IAKAwB,qBAAA,EAAgC;MAC9B,OAAO,KAAKzB,SAAA,YAAqBG,CACnC;IAAA;EACF;EAOaQ,CAAA,GAAN,MAAMf,CAAA,SAAqB8B,CAAU;IAU1C5B,YAAYC,CAAA,EAAsB;MAIhC,IAHA,MAAM,GACN,KAAKU,SAAA,GAAYV,CAAA,EAEbA,CAAA,YAAqB4B,CAAA,EACvB,KAAKzB,OAAA,GAAU,WACNH,CAAA,YAAqB6B,CAAA,EAC9B,KAAK1B,OAAA,GAAU,WACNH,CAAA,YAAqB8B,CAAA,EAC9B,KAAK3B,OAAA,GAAU,OAEf,MAAM,IAAIG,KAAA,CAAM,4BAA4B,CAEhD;IAAA;IAMAY,aAAA,EAAe;MACb,OAAO,KAAKC,UAAA,CAAW,CACzB;IAAA;IAMAC,UAAUpB,CAAA,EAA8B;MACtCA,CAAA,CAAWqB,qBAAA,CAAsB,KAAKlB,OAAO,GAC7C,KAAKO,SAAA,CAAUU,SAAA,CAAUpB,CAAU,CACrC;IAAA;IAEA,OAAOsB,YAAYtB,CAAA,EAA0C;MAC3D,IAAMS,CAAA,GAAeT,CAAA,CAAauB,uBAAA,CAAwB;QACtDZ,CAAA;MACJ,QAAQF,CAAA;QACN;UACEE,CAAA,GAAYiB,CAAA,CAAiBN,WAAA,CAAYtB,CAAY;UACrD;QACF;UACEW,CAAA,GAAYkB,CAAA,CAAmBP,WAAA,CAAYtB,CAAY;UACvD;QACF;UACEW,CAAA,GAAYmB,CAAA,CAAiBR,WAAA,CAAYtB,CAAY;UACrD;QACF;UACE,MAAM,IAAIM,KAAA,CAAM,2CAA2CG,CAAY,EAAE,CAC7E;MAAA;MACA,OAAO,IAAIZ,CAAA,CAAac,CAAS,CACnC;IAAA;EAGF;AAAA,SAAAf,CAAA,IAAAQ,CAAA,EAAAQ,CAAA,IAAAd,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}