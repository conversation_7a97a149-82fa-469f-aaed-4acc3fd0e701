{"ast": null, "code": "/**\n\n<PERSON>A<PERSON> (RFC 3174), MD5 (RFC 1321) and RIPEMD160 (RFC 2286) legacy, weak hash functions.\nDon't use them in a new protocol. What \"weak\" means:\n\n- Collisions can be made with 2^18 effort in MD5, 2^60 in SHA1, 2^80 in RIPEMD160.\n- No practical pre-image attacks (only theoretical, 2^123.4)\n- HMAC seems kinda ok: https://datatracker.ietf.org/doc/html/rfc6151\n * @module\n */\nimport { Chi, HashMD, Maj } from \"./_md.js\";\nimport { clean, createHasher, rotl } from \"./utils.js\";\n/** Initial SHA1 state */\nconst SHA1_IV = /* @__PURE__ */Uint32Array.from([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0]);\n// Reusable temporary buffer\nconst SHA1_W = /* @__PURE__ */new Uint32Array(80);\n/** SHA1 legacy hash class. */\nexport class SHA1 extends HashMD {\n  constructor() {\n    super(64, 20, 8, false);\n    this.A = SHA1_IV[0] | 0;\n    this.B = SHA1_IV[1] | 0;\n    this.C = SHA1_IV[2] | 0;\n    this.D = SHA1_IV[3] | 0;\n    this.E = SHA1_IV[4] | 0;\n  }\n  get() {\n    const {\n      A,\n      B,\n      C,\n      D,\n      E\n    } = this;\n    return [A, B, C, D, E];\n  }\n  set(A, B, C, D, E) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n  }\n  process(view, offset) {\n    for (let i = 0; i < 16; i++, offset += 4) SHA1_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 80; i++) SHA1_W[i] = rotl(SHA1_W[i - 3] ^ SHA1_W[i - 8] ^ SHA1_W[i - 14] ^ SHA1_W[i - 16], 1);\n    // Compression function main loop, 80 rounds\n    let {\n      A,\n      B,\n      C,\n      D,\n      E\n    } = this;\n    for (let i = 0; i < 80; i++) {\n      let F, K;\n      if (i < 20) {\n        F = Chi(B, C, D);\n        K = 0x5a827999;\n      } else if (i < 40) {\n        F = B ^ C ^ D;\n        K = 0x6ed9eba1;\n      } else if (i < 60) {\n        F = Maj(B, C, D);\n        K = 0x8f1bbcdc;\n      } else {\n        F = B ^ C ^ D;\n        K = 0xca62c1d6;\n      }\n      const T = rotl(A, 5) + F + E + K + SHA1_W[i] | 0;\n      E = D;\n      D = C;\n      C = rotl(B, 30);\n      B = A;\n      A = T;\n    }\n    // Add the compressed chunk to the current hash value\n    A = A + this.A | 0;\n    B = B + this.B | 0;\n    C = C + this.C | 0;\n    D = D + this.D | 0;\n    E = E + this.E | 0;\n    this.set(A, B, C, D, E);\n  }\n  roundClean() {\n    clean(SHA1_W);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\n/** SHA1 (RFC 3174) legacy hash function. It was cryptographically broken. */\nexport const sha1 = /* @__PURE__ */createHasher(() => new SHA1());\n/** Per-round constants */\nconst p32 = /* @__PURE__ */Math.pow(2, 32);\nconst K = /* @__PURE__ */Array.from({\n  length: 64\n}, (_, i) => Math.floor(p32 * Math.abs(Math.sin(i + 1))));\n/** md5 initial state: same as sha1, but 4 u32 instead of 5. */\nconst MD5_IV = /* @__PURE__ */SHA1_IV.slice(0, 4);\n// Reusable temporary buffer\nconst MD5_W = /* @__PURE__ */new Uint32Array(16);\n/** MD5 legacy hash class. */\nexport class MD5 extends HashMD {\n  constructor() {\n    super(64, 16, 8, true);\n    this.A = MD5_IV[0] | 0;\n    this.B = MD5_IV[1] | 0;\n    this.C = MD5_IV[2] | 0;\n    this.D = MD5_IV[3] | 0;\n  }\n  get() {\n    const {\n      A,\n      B,\n      C,\n      D\n    } = this;\n    return [A, B, C, D];\n  }\n  set(A, B, C, D) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n  }\n  process(view, offset) {\n    for (let i = 0; i < 16; i++, offset += 4) MD5_W[i] = view.getUint32(offset, true);\n    // Compression function main loop, 64 rounds\n    let {\n      A,\n      B,\n      C,\n      D\n    } = this;\n    for (let i = 0; i < 64; i++) {\n      let F, g, s;\n      if (i < 16) {\n        F = Chi(B, C, D);\n        g = i;\n        s = [7, 12, 17, 22];\n      } else if (i < 32) {\n        F = Chi(D, B, C);\n        g = (5 * i + 1) % 16;\n        s = [5, 9, 14, 20];\n      } else if (i < 48) {\n        F = B ^ C ^ D;\n        g = (3 * i + 5) % 16;\n        s = [4, 11, 16, 23];\n      } else {\n        F = C ^ (B | ~D);\n        g = 7 * i % 16;\n        s = [6, 10, 15, 21];\n      }\n      F = F + A + K[i] + MD5_W[g];\n      A = D;\n      D = C;\n      C = B;\n      B = B + rotl(F, s[i % 4]);\n    }\n    // Add the compressed chunk to the current hash value\n    A = A + this.A | 0;\n    B = B + this.B | 0;\n    C = C + this.C | 0;\n    D = D + this.D | 0;\n    this.set(A, B, C, D);\n  }\n  roundClean() {\n    clean(MD5_W);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\n/**\n * MD5 (RFC 1321) legacy hash function. It was cryptographically broken.\n * MD5 architecture is similar to SHA1, with some differences:\n * - Reduced output length: 16 bytes (128 bit) instead of 20\n * - 64 rounds, instead of 80\n * - Little-endian: could be faster, but will require more code\n * - Non-linear index selection: huge speed-up for unroll\n * - Per round constants: more memory accesses, additional speed-up for unroll\n */\nexport const md5 = /* @__PURE__ */createHasher(() => new MD5());\n// RIPEMD-160\nconst Rho160 = /* @__PURE__ */Uint8Array.from([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id160 = /* @__PURE__ */(() => Uint8Array.from(new Array(16).fill(0).map((_, i) => i)))();\nconst Pi160 = /* @__PURE__ */(() => Id160.map(i => (9 * i + 5) % 16))();\nconst idxLR = /* @__PURE__ */(() => {\n  const L = [Id160];\n  const R = [Pi160];\n  const res = [L, R];\n  for (let i = 0; i < 4; i++) for (let j of res) j.push(j[i].map(k => Rho160[k]));\n  return res;\n})();\nconst idxL = /* @__PURE__ */(() => idxLR[0])();\nconst idxR = /* @__PURE__ */(() => idxLR[1])();\n// const [idxL, idxR] = idxLR;\nconst shifts160 = /* @__PURE__ */[[11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8], [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7], [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9], [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6], [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5]].map(i => Uint8Array.from(i));\nconst shiftsL160 = /* @__PURE__ */idxL.map((idx, i) => idx.map(j => shifts160[i][j]));\nconst shiftsR160 = /* @__PURE__ */idxR.map((idx, i) => idx.map(j => shifts160[i][j]));\nconst Kl160 = /* @__PURE__ */Uint32Array.from([0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e]);\nconst Kr160 = /* @__PURE__ */Uint32Array.from([0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000]);\n// It's called f() in spec.\nfunction ripemd_f(group, x, y, z) {\n  if (group === 0) return x ^ y ^ z;\n  if (group === 1) return x & y | ~x & z;\n  if (group === 2) return (x | ~y) ^ z;\n  if (group === 3) return x & z | y & ~z;\n  return x ^ (y | ~z);\n}\n// Reusable temporary buffer\nconst BUF_160 = /* @__PURE__ */new Uint32Array(16);\nexport class RIPEMD160 extends HashMD {\n  constructor() {\n    super(64, 20, 8, true);\n    this.h0 = 0x67452301 | 0;\n    this.h1 = 0xefcdab89 | 0;\n    this.h2 = 0x98badcfe | 0;\n    this.h3 = 0x10325476 | 0;\n    this.h4 = 0xc3d2e1f0 | 0;\n  }\n  get() {\n    const {\n      h0,\n      h1,\n      h2,\n      h3,\n      h4\n    } = this;\n    return [h0, h1, h2, h3, h4];\n  }\n  set(h0, h1, h2, h3, h4) {\n    this.h0 = h0 | 0;\n    this.h1 = h1 | 0;\n    this.h2 = h2 | 0;\n    this.h3 = h3 | 0;\n    this.h4 = h4 | 0;\n  }\n  process(view, offset) {\n    for (let i = 0; i < 16; i++, offset += 4) BUF_160[i] = view.getUint32(offset, true);\n    // prettier-ignore\n    let al = this.h0 | 0,\n      ar = al,\n      bl = this.h1 | 0,\n      br = bl,\n      cl = this.h2 | 0,\n      cr = cl,\n      dl = this.h3 | 0,\n      dr = dl,\n      el = this.h4 | 0,\n      er = el;\n    // Instead of iterating 0 to 80, we split it into 5 groups\n    // And use the groups in constants, functions, etc. Much simpler\n    for (let group = 0; group < 5; group++) {\n      const rGroup = 4 - group;\n      const hbl = Kl160[group],\n        hbr = Kr160[group]; // prettier-ignore\n      const rl = idxL[group],\n        rr = idxR[group]; // prettier-ignore\n      const sl = shiftsL160[group],\n        sr = shiftsR160[group]; // prettier-ignore\n      for (let i = 0; i < 16; i++) {\n        const tl = rotl(al + ripemd_f(group, bl, cl, dl) + BUF_160[rl[i]] + hbl, sl[i]) + el | 0;\n        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n      }\n      // 2 loops are 10% faster\n      for (let i = 0; i < 16; i++) {\n        const tr = rotl(ar + ripemd_f(rGroup, br, cr, dr) + BUF_160[rr[i]] + hbr, sr[i]) + er | 0;\n        ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n      }\n    }\n    // Add the compressed chunk to the current hash value\n    this.set(this.h1 + cl + dr | 0, this.h2 + dl + er | 0, this.h3 + el + ar | 0, this.h4 + al + br | 0, this.h0 + bl + cr | 0);\n  }\n  roundClean() {\n    clean(BUF_160);\n  }\n  destroy() {\n    this.destroyed = true;\n    clean(this.buffer);\n    this.set(0, 0, 0, 0, 0);\n  }\n}\n/**\n * RIPEMD-160 - a legacy hash function from 1990s.\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n */\nexport const ripemd160 = /* @__PURE__ */createHasher(() => new RIPEMD160());", "map": {"version": 3, "names": ["<PERSON>", "HashMD", "Maj", "clean", "createHasher", "rotl", "SHA1_IV", "Uint32Array", "from", "SHA1_W", "SHA1", "constructor", "A", "B", "C", "D", "E", "get", "set", "process", "view", "offset", "i", "getUint32", "F", "K", "T", "roundClean", "destroy", "buffer", "sha1", "p32", "Math", "pow", "Array", "length", "_", "floor", "abs", "sin", "MD5_IV", "slice", "MD5_W", "MD5", "g", "s", "md5", "Rho160", "Uint8Array", "Id160", "fill", "map", "Pi160", "idxLR", "L", "R", "res", "j", "push", "k", "idxL", "idxR", "shifts160", "shiftsL160", "idx", "shiftsR160", "Kl160", "Kr160", "ripemd_f", "group", "x", "y", "z", "BUF_160", "RIPEMD160", "h0", "h1", "h2", "h3", "h4", "al", "ar", "bl", "br", "cl", "cr", "dl", "dr", "el", "er", "rGroup", "hbl", "hbr", "rl", "rr", "sl", "sr", "tl", "tr", "destroyed", "ripemd160"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\legacy.ts"], "sourcesContent": ["/**\n\n<PERSON>A<PERSON> (RFC 3174), MD5 (RFC 1321) and RIPEMD160 (RFC 2286) legacy, weak hash functions.\nDon't use them in a new protocol. What \"weak\" means:\n\n- Collisions can be made with 2^18 effort in MD5, 2^60 in SHA1, 2^80 in RIPEMD160.\n- No practical pre-image attacks (only theoretical, 2^123.4)\n- HMAC seems kinda ok: https://datatracker.ietf.org/doc/html/rfc6151\n * @module\n */\nimport { Chi, HashMD, Maj } from './_md.ts';\nimport { type CHash, clean, createHasher, rotl } from './utils.ts';\n\n/** Initial SHA1 state */\nconst SHA1_IV = /* @__PURE__ */ Uint32Array.from([\n  0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0,\n]);\n\n// Reusable temporary buffer\nconst SHA1_W = /* @__PURE__ */ new Uint32Array(80);\n\n/** SHA1 legacy hash class. */\nexport class SHA1 extends HashMD<SHA1> {\n  private A = SHA1_IV[0] | 0;\n  private B = SHA1_IV[1] | 0;\n  private C = SHA1_IV[2] | 0;\n  private D = SHA1_IV[3] | 0;\n  private E = SHA1_IV[4] | 0;\n\n  constructor() {\n    super(64, 20, 8, false);\n  }\n  protected get(): [number, number, number, number, number] {\n    const { A, B, C, D, E } = this;\n    return [A, B, C, D, E];\n  }\n  protected set(A: number, B: number, C: number, D: number, E: number): void {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) SHA1_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 80; i++)\n      SHA1_W[i] = rotl(SHA1_W[i - 3] ^ SHA1_W[i - 8] ^ SHA1_W[i - 14] ^ SHA1_W[i - 16], 1);\n    // Compression function main loop, 80 rounds\n    let { A, B, C, D, E } = this;\n    for (let i = 0; i < 80; i++) {\n      let F, K;\n      if (i < 20) {\n        F = Chi(B, C, D);\n        K = 0x5a827999;\n      } else if (i < 40) {\n        F = B ^ C ^ D;\n        K = 0x6ed9eba1;\n      } else if (i < 60) {\n        F = Maj(B, C, D);\n        K = 0x8f1bbcdc;\n      } else {\n        F = B ^ C ^ D;\n        K = 0xca62c1d6;\n      }\n      const T = (rotl(A, 5) + F + E + K + SHA1_W[i]) | 0;\n      E = D;\n      D = C;\n      C = rotl(B, 30);\n      B = A;\n      A = T;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    this.set(A, B, C, D, E);\n  }\n  protected roundClean(): void {\n    clean(SHA1_W);\n  }\n  destroy(): void {\n    this.set(0, 0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\n\n/** SHA1 (RFC 3174) legacy hash function. It was cryptographically broken. */\nexport const sha1: CHash = /* @__PURE__ */ createHasher(() => new SHA1());\n\n/** Per-round constants */\nconst p32 = /* @__PURE__ */ Math.pow(2, 32);\nconst K = /* @__PURE__ */ Array.from({ length: 64 }, (_, i) =>\n  Math.floor(p32 * Math.abs(Math.sin(i + 1)))\n);\n\n/** md5 initial state: same as sha1, but 4 u32 instead of 5. */\nconst MD5_IV = /* @__PURE__ */ SHA1_IV.slice(0, 4);\n\n// Reusable temporary buffer\nconst MD5_W = /* @__PURE__ */ new Uint32Array(16);\n/** MD5 legacy hash class. */\nexport class MD5 extends HashMD<MD5> {\n  private A = MD5_IV[0] | 0;\n  private B = MD5_IV[1] | 0;\n  private C = MD5_IV[2] | 0;\n  private D = MD5_IV[3] | 0;\n\n  constructor() {\n    super(64, 16, 8, true);\n  }\n  protected get(): [number, number, number, number] {\n    const { A, B, C, D } = this;\n    return [A, B, C, D];\n  }\n  protected set(A: number, B: number, C: number, D: number): void {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) MD5_W[i] = view.getUint32(offset, true);\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D } = this;\n    for (let i = 0; i < 64; i++) {\n      let F, g, s;\n      if (i < 16) {\n        F = Chi(B, C, D);\n        g = i;\n        s = [7, 12, 17, 22];\n      } else if (i < 32) {\n        F = Chi(D, B, C);\n        g = (5 * i + 1) % 16;\n        s = [5, 9, 14, 20];\n      } else if (i < 48) {\n        F = B ^ C ^ D;\n        g = (3 * i + 5) % 16;\n        s = [4, 11, 16, 23];\n      } else {\n        F = C ^ (B | ~D);\n        g = (7 * i) % 16;\n        s = [6, 10, 15, 21];\n      }\n      F = F + A + K[i] + MD5_W[g];\n      A = D;\n      D = C;\n      C = B;\n      B = B + rotl(F, s[i % 4]);\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    this.set(A, B, C, D);\n  }\n  protected roundClean(): void {\n    clean(MD5_W);\n  }\n  destroy(): void {\n    this.set(0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\n\n/**\n * MD5 (RFC 1321) legacy hash function. It was cryptographically broken.\n * MD5 architecture is similar to SHA1, with some differences:\n * - Reduced output length: 16 bytes (128 bit) instead of 20\n * - 64 rounds, instead of 80\n * - Little-endian: could be faster, but will require more code\n * - Non-linear index selection: huge speed-up for unroll\n * - Per round constants: more memory accesses, additional speed-up for unroll\n */\nexport const md5: CHash = /* @__PURE__ */ createHasher(() => new MD5());\n\n// RIPEMD-160\n\nconst Rho160 = /* @__PURE__ */ Uint8Array.from([\n  7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8,\n]);\nconst Id160 = /* @__PURE__ */ (() => Uint8Array.from(new Array(16).fill(0).map((_, i) => i)))();\nconst Pi160 = /* @__PURE__ */ (() => Id160.map((i) => (9 * i + 5) % 16))();\nconst idxLR = /* @__PURE__ */ (() => {\n  const L = [Id160];\n  const R = [Pi160];\n  const res = [L, R];\n  for (let i = 0; i < 4; i++) for (let j of res) j.push(j[i].map((k) => Rho160[k]));\n  return res;\n})();\nconst idxL = /* @__PURE__ */ (() => idxLR[0])();\nconst idxR = /* @__PURE__ */ (() => idxLR[1])();\n// const [idxL, idxR] = idxLR;\n\nconst shifts160 = /* @__PURE__ */ [\n  [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n  [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n  [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n  [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n  [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => Uint8Array.from(i));\nconst shiftsL160 = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts160[i][j]));\nconst shiftsR160 = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts160[i][j]));\nconst Kl160 = /* @__PURE__ */ Uint32Array.from([\n  0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr160 = /* @__PURE__ */ Uint32Array.from([\n  0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// It's called f() in spec.\nfunction ripemd_f(group: number, x: number, y: number, z: number): number {\n  if (group === 0) return x ^ y ^ z;\n  if (group === 1) return (x & y) | (~x & z);\n  if (group === 2) return (x | ~y) ^ z;\n  if (group === 3) return (x & z) | (y & ~z);\n  return x ^ (y | ~z);\n}\n// Reusable temporary buffer\nconst BUF_160 = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends HashMD<RIPEMD160> {\n  private h0 = 0x67452301 | 0;\n  private h1 = 0xefcdab89 | 0;\n  private h2 = 0x98badcfe | 0;\n  private h3 = 0x10325476 | 0;\n  private h4 = 0xc3d2e1f0 | 0;\n\n  constructor() {\n    super(64, 20, 8, true);\n  }\n  protected get(): [number, number, number, number, number] {\n    const { h0, h1, h2, h3, h4 } = this;\n    return [h0, h1, h2, h3, h4];\n  }\n  protected set(h0: number, h1: number, h2: number, h3: number, h4: number): void {\n    this.h0 = h0 | 0;\n    this.h1 = h1 | 0;\n    this.h2 = h2 | 0;\n    this.h3 = h3 | 0;\n    this.h4 = h4 | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    for (let i = 0; i < 16; i++, offset += 4) BUF_160[i] = view.getUint32(offset, true);\n    // prettier-ignore\n    let al = this.h0 | 0, ar = al,\n        bl = this.h1 | 0, br = bl,\n        cl = this.h2 | 0, cr = cl,\n        dl = this.h3 | 0, dr = dl,\n        el = this.h4 | 0, er = el;\n\n    // Instead of iterating 0 to 80, we split it into 5 groups\n    // And use the groups in constants, functions, etc. Much simpler\n    for (let group = 0; group < 5; group++) {\n      const rGroup = 4 - group;\n      const hbl = Kl160[group], hbr = Kr160[group]; // prettier-ignore\n      const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n      const sl = shiftsL160[group], sr = shiftsR160[group]; // prettier-ignore\n      for (let i = 0; i < 16; i++) {\n        const tl = (rotl(al + ripemd_f(group, bl, cl, dl) + BUF_160[rl[i]] + hbl, sl[i]) + el) | 0;\n        al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n      }\n      // 2 loops are 10% faster\n      for (let i = 0; i < 16; i++) {\n        const tr = (rotl(ar + ripemd_f(rGroup, br, cr, dr) + BUF_160[rr[i]] + hbr, sr[i]) + er) | 0;\n        ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n      }\n    }\n    // Add the compressed chunk to the current hash value\n    this.set(\n      (this.h1 + cl + dr) | 0,\n      (this.h2 + dl + er) | 0,\n      (this.h3 + el + ar) | 0,\n      (this.h4 + al + br) | 0,\n      (this.h0 + bl + cr) | 0\n    );\n  }\n  protected roundClean(): void {\n    clean(BUF_160);\n  }\n  destroy(): void {\n    this.destroyed = true;\n    clean(this.buffer);\n    this.set(0, 0, 0, 0, 0);\n  }\n}\n\n/**\n * RIPEMD-160 - a legacy hash function from 1990s.\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n * * https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\n */\nexport const ripemd160: CHash = /* @__PURE__ */ createHasher(() => new RIPEMD160());\n"], "mappings": "AAAA;;;;;;;;;;AAUA,SAASA,GAAG,EAAEC,MAAM,EAAEC,GAAG,QAAQ,UAAU;AAC3C,SAAqBC,KAAK,EAAEC,YAAY,EAAEC,IAAI,QAAQ,YAAY;AAElE;AACA,MAAMC,OAAO,GAAG,eAAgBC,WAAW,CAACC,IAAI,CAAC,CAC/C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC3D,CAAC;AAEF;AACA,MAAMC,MAAM,GAAG,eAAgB,IAAIF,WAAW,CAAC,EAAE,CAAC;AAElD;AACA,OAAM,MAAOG,IAAK,SAAQT,MAAY;EAOpCU,YAAA;IACE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC;IAPjB,KAAAC,CAAC,GAAGN,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAClB,KAAAO,CAAC,GAAGP,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAClB,KAAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAClB,KAAAS,CAAC,GAAGT,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAClB,KAAAU,CAAC,GAAGV,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAI1B;EACUW,GAAGA,CAAA;IACX,MAAM;MAAEL,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IAC9B,OAAO,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACxB;EACUE,GAAGA,CAACN,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS;IACjE,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;EAChB;EACUG,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAEZ,MAAM,CAACa,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,EAAE,KAAK,CAAC;IACnF,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAC1Bb,MAAM,CAACa,CAAC,CAAC,GAAGjB,IAAI,CAACI,MAAM,CAACa,CAAC,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACa,CAAC,GAAG,CAAC,CAAC,GAAGb,MAAM,CAACa,CAAC,GAAG,EAAE,CAAC,GAAGb,MAAM,CAACa,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACtF;IACA,IAAI;MAAEV,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IAC5B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAIE,CAAC,EAAEC,CAAC;MACR,IAAIH,CAAC,GAAG,EAAE,EAAE;QACVE,CAAC,GAAGxB,GAAG,CAACa,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;QAChBU,CAAC,GAAG,UAAU;MAChB,CAAC,MAAM,IAAIH,CAAC,GAAG,EAAE,EAAE;QACjBE,CAAC,GAAGX,CAAC,GAAGC,CAAC,GAAGC,CAAC;QACbU,CAAC,GAAG,UAAU;MAChB,CAAC,MAAM,IAAIH,CAAC,GAAG,EAAE,EAAE;QACjBE,CAAC,GAAGtB,GAAG,CAACW,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;QAChBU,CAAC,GAAG,UAAU;MAChB,CAAC,MAAM;QACLD,CAAC,GAAGX,CAAC,GAAGC,CAAC,GAAGC,CAAC;QACbU,CAAC,GAAG,UAAU;MAChB;MACA,MAAMC,CAAC,GAAIrB,IAAI,CAACO,CAAC,EAAE,CAAC,CAAC,GAAGY,CAAC,GAAGR,CAAC,GAAGS,CAAC,GAAGhB,MAAM,CAACa,CAAC,CAAC,GAAI,CAAC;MAClDN,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGT,IAAI,CAACQ,CAAC,EAAE,EAAE,CAAC;MACfA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGc,CAAC;IACP;IACA;IACAd,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpB,IAAI,CAACE,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACzB;EACUW,UAAUA,CAAA;IAClBxB,KAAK,CAACM,MAAM,CAAC;EACf;EACAmB,OAAOA,CAAA;IACL,IAAI,CAACV,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvBf,KAAK,CAAC,IAAI,CAAC0B,MAAM,CAAC;EACpB;;AAGF;AACA,OAAO,MAAMC,IAAI,GAAU,eAAgB1B,YAAY,CAAC,MAAM,IAAIM,IAAI,EAAE,CAAC;AAEzE;AACA,MAAMqB,GAAG,GAAG,eAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AAC3C,MAAMR,CAAC,GAAG,eAAgBS,KAAK,CAAC1B,IAAI,CAAC;EAAE2B,MAAM,EAAE;AAAE,CAAE,EAAE,CAACC,CAAC,EAAEd,CAAC,KACxDU,IAAI,CAACK,KAAK,CAACN,GAAG,GAAGC,IAAI,CAACM,GAAG,CAACN,IAAI,CAACO,GAAG,CAACjB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC5C;AAED;AACA,MAAMkB,MAAM,GAAG,eAAgBlC,OAAO,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAElD;AACA,MAAMC,KAAK,GAAG,eAAgB,IAAInC,WAAW,CAAC,EAAE,CAAC;AACjD;AACA,OAAM,MAAOoC,GAAI,SAAQ1C,MAAW;EAMlCU,YAAA;IACE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;IANhB,KAAAC,CAAC,GAAG4B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACjB,KAAA3B,CAAC,GAAG2B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACjB,KAAA1B,CAAC,GAAG0B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACjB,KAAAzB,CAAC,GAAGyB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;EAIzB;EACUvB,GAAGA,CAAA;IACX,MAAM;MAAEL,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IAC3B,OAAO,CAACH,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACrB;EACUG,GAAGA,CAACN,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS;IACtD,IAAI,CAACH,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;EAChB;EACUI,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAEqB,KAAK,CAACpB,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,EAAE,IAAI,CAAC;IACjF;IACA,IAAI;MAAET,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IACzB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAIE,CAAC,EAAEoB,CAAC,EAAEC,CAAC;MACX,IAAIvB,CAAC,GAAG,EAAE,EAAE;QACVE,CAAC,GAAGxB,GAAG,CAACa,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;QAChB6B,CAAC,GAAGtB,CAAC;QACLuB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACrB,CAAC,MAAM,IAAIvB,CAAC,GAAG,EAAE,EAAE;QACjBE,CAAC,GAAGxB,GAAG,CAACe,CAAC,EAAEF,CAAC,EAAEC,CAAC,CAAC;QAChB8B,CAAC,GAAG,CAAC,CAAC,GAAGtB,CAAC,GAAG,CAAC,IAAI,EAAE;QACpBuB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;MACpB,CAAC,MAAM,IAAIvB,CAAC,GAAG,EAAE,EAAE;QACjBE,CAAC,GAAGX,CAAC,GAAGC,CAAC,GAAGC,CAAC;QACb6B,CAAC,GAAG,CAAC,CAAC,GAAGtB,CAAC,GAAG,CAAC,IAAI,EAAE;QACpBuB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACrB,CAAC,MAAM;QACLrB,CAAC,GAAGV,CAAC,IAAID,CAAC,GAAG,CAACE,CAAC,CAAC;QAChB6B,CAAC,GAAI,CAAC,GAAGtB,CAAC,GAAI,EAAE;QAChBuB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACrB;MACArB,CAAC,GAAGA,CAAC,GAAGZ,CAAC,GAAGa,CAAC,CAACH,CAAC,CAAC,GAAGoB,KAAK,CAACE,CAAC,CAAC;MAC3BhC,CAAC,GAAGG,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGA,CAAC,GAAGR,IAAI,CAACmB,CAAC,EAAEqB,CAAC,CAACvB,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B;IACA;IACAV,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpB,IAAI,CAACG,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACtB;EACUY,UAAUA,CAAA;IAClBxB,KAAK,CAACuC,KAAK,CAAC;EACd;EACAd,OAAOA,CAAA;IACL,IAAI,CAACV,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACpBf,KAAK,CAAC,IAAI,CAAC0B,MAAM,CAAC;EACpB;;AAGF;;;;;;;;;AASA,OAAO,MAAMiB,GAAG,GAAU,eAAgB1C,YAAY,CAAC,MAAM,IAAIuC,GAAG,EAAE,CAAC;AAEvE;AAEA,MAAMI,MAAM,GAAG,eAAgBC,UAAU,CAACxC,IAAI,CAAC,CAC7C,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACrD,CAAC;AACF,MAAMyC,KAAK,GAAG,eAAgB,CAAC,MAAMD,UAAU,CAACxC,IAAI,CAAC,IAAI0B,KAAK,CAAC,EAAE,CAAC,CAACgB,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACf,CAAC,EAAEd,CAAC,KAAKA,CAAC,CAAC,CAAC,EAAC,CAAE;AAC/F,MAAM8B,KAAK,GAAG,eAAgB,CAAC,MAAMH,KAAK,CAACE,GAAG,CAAE7B,CAAC,IAAK,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAC,CAAE;AAC1E,MAAM+B,KAAK,GAAG,eAAgB,CAAC,MAAK;EAClC,MAAMC,CAAC,GAAG,CAACL,KAAK,CAAC;EACjB,MAAMM,CAAC,GAAG,CAACH,KAAK,CAAC;EACjB,MAAMI,GAAG,GAAG,CAACF,CAAC,EAAEC,CAAC,CAAC;EAClB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE,KAAK,IAAImC,CAAC,IAAID,GAAG,EAAEC,CAAC,CAACC,IAAI,CAACD,CAAC,CAACnC,CAAC,CAAC,CAAC6B,GAAG,CAAEQ,CAAC,IAAKZ,MAAM,CAACY,CAAC,CAAC,CAAC,CAAC;EACjF,OAAOH,GAAG;AACZ,CAAC,EAAC,CAAE;AACJ,MAAMI,IAAI,GAAG,eAAgB,CAAC,MAAMP,KAAK,CAAC,CAAC,CAAC,EAAC,CAAE;AAC/C,MAAMQ,IAAI,GAAG,eAAgB,CAAC,MAAMR,KAAK,CAAC,CAAC,CAAC,EAAC,CAAE;AAC/C;AAEA,MAAMS,SAAS,GAAG,eAAgB,CAChC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACxD,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACxD,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACxD,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACxD,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CACzD,CAACX,GAAG,CAAE7B,CAAC,IAAK0B,UAAU,CAACxC,IAAI,CAACc,CAAC,CAAC,CAAC;AAChC,MAAMyC,UAAU,GAAG,eAAgBH,IAAI,CAACT,GAAG,CAAC,CAACa,GAAG,EAAE1C,CAAC,KAAK0C,GAAG,CAACb,GAAG,CAAEM,CAAC,IAAKK,SAAS,CAACxC,CAAC,CAAC,CAACmC,CAAC,CAAC,CAAC,CAAC;AACxF,MAAMQ,UAAU,GAAG,eAAgBJ,IAAI,CAACV,GAAG,CAAC,CAACa,GAAG,EAAE1C,CAAC,KAAK0C,GAAG,CAACb,GAAG,CAAEM,CAAC,IAAKK,SAAS,CAACxC,CAAC,CAAC,CAACmC,CAAC,CAAC,CAAC,CAAC;AACxF,MAAMS,KAAK,GAAG,eAAgB3D,WAAW,CAACC,IAAI,CAAC,CAC7C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC3D,CAAC;AACF,MAAM2D,KAAK,GAAG,eAAgB5D,WAAW,CAACC,IAAI,CAAC,CAC7C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC3D,CAAC;AACF;AACA,SAAS4D,QAAQA,CAACC,KAAa,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS;EAC9D,IAAIH,KAAK,KAAK,CAAC,EAAE,OAAOC,CAAC,GAAGC,CAAC,GAAGC,CAAC;EACjC,IAAIH,KAAK,KAAK,CAAC,EAAE,OAAQC,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE;EAC1C,IAAIH,KAAK,KAAK,CAAC,EAAE,OAAO,CAACC,CAAC,GAAG,CAACC,CAAC,IAAIC,CAAC;EACpC,IAAIH,KAAK,KAAK,CAAC,EAAE,OAAQC,CAAC,GAAGE,CAAC,GAAKD,CAAC,GAAG,CAACC,CAAE;EAC1C,OAAOF,CAAC,IAAIC,CAAC,GAAG,CAACC,CAAC,CAAC;AACrB;AACA;AACA,MAAMC,OAAO,GAAG,eAAgB,IAAIlE,WAAW,CAAC,EAAE,CAAC;AACnD,OAAM,MAAOmE,SAAU,SAAQzE,MAAiB;EAO9CU,YAAA;IACE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;IAPhB,KAAAgE,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;EAI3B;EACU9D,GAAGA,CAAA;IACX,MAAM;MAAE0D,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAE,GAAG,IAAI;IACnC,OAAO,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC7B;EACU7D,GAAGA,CAACyD,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;IACtE,IAAI,CAACJ,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;EAClB;EACU5D,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAEoD,OAAO,CAACnD,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,EAAE,IAAI,CAAC;IACnF;IACA,IAAI2D,EAAE,GAAG,IAAI,CAACL,EAAE,GAAG,CAAC;MAAEM,EAAE,GAAGD,EAAE;MACzBE,EAAE,GAAG,IAAI,CAACN,EAAE,GAAG,CAAC;MAAEO,EAAE,GAAGD,EAAE;MACzBE,EAAE,GAAG,IAAI,CAACP,EAAE,GAAG,CAAC;MAAEQ,EAAE,GAAGD,EAAE;MACzBE,EAAE,GAAG,IAAI,CAACR,EAAE,GAAG,CAAC;MAAES,EAAE,GAAGD,EAAE;MACzBE,EAAE,GAAG,IAAI,CAACT,EAAE,GAAG,CAAC;MAAEU,EAAE,GAAGD,EAAE;IAE7B;IACA;IACA,KAAK,IAAInB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,EAAEA,KAAK,EAAE,EAAE;MACtC,MAAMqB,MAAM,GAAG,CAAC,GAAGrB,KAAK;MACxB,MAAMsB,GAAG,GAAGzB,KAAK,CAACG,KAAK,CAAC;QAAEuB,GAAG,GAAGzB,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAC9C,MAAMwB,EAAE,GAAGjC,IAAI,CAACS,KAAK,CAAC;QAAEyB,EAAE,GAAGjC,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC;MAC1C,MAAM0B,EAAE,GAAGhC,UAAU,CAACM,KAAK,CAAC;QAAE2B,EAAE,GAAG/B,UAAU,CAACI,KAAK,CAAC,CAAC,CAAC;MACtD,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAM2E,EAAE,GAAI5F,IAAI,CAAC2E,EAAE,GAAGZ,QAAQ,CAACC,KAAK,EAAEa,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC,GAAGb,OAAO,CAACoB,EAAE,CAACvE,CAAC,CAAC,CAAC,GAAGqE,GAAG,EAAEI,EAAE,CAACzE,CAAC,CAAC,CAAC,GAAGkE,EAAE,GAAI,CAAC;QAC1FR,EAAE,GAAGQ,EAAE,EAAEA,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGjF,IAAI,CAAC+E,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAEA,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGe,EAAE,CAAC,CAAC;MAC7D;MACA;MACA,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAM4E,EAAE,GAAI7F,IAAI,CAAC4E,EAAE,GAAGb,QAAQ,CAACsB,MAAM,EAAEP,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC,GAAGd,OAAO,CAACqB,EAAE,CAACxE,CAAC,CAAC,CAAC,GAAGsE,GAAG,EAAEI,EAAE,CAAC1E,CAAC,CAAC,CAAC,GAAGmE,EAAE,GAAI,CAAC;QAC3FR,EAAE,GAAGQ,EAAE,EAAEA,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGlF,IAAI,CAACgF,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAEA,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGe,EAAE,CAAC,CAAC;MAC7D;IACF;IACA;IACA,IAAI,CAAChF,GAAG,CACL,IAAI,CAAC0D,EAAE,GAAGQ,EAAE,GAAGG,EAAE,GAAI,CAAC,EACtB,IAAI,CAACV,EAAE,GAAGS,EAAE,GAAGG,EAAE,GAAI,CAAC,EACtB,IAAI,CAACX,EAAE,GAAGU,EAAE,GAAGP,EAAE,GAAI,CAAC,EACtB,IAAI,CAACF,EAAE,GAAGC,EAAE,GAAGG,EAAE,GAAI,CAAC,EACtB,IAAI,CAACR,EAAE,GAAGO,EAAE,GAAGG,EAAE,GAAI,CAAC,CACxB;EACH;EACU1D,UAAUA,CAAA;IAClBxB,KAAK,CAACsE,OAAO,CAAC;EAChB;EACA7C,OAAOA,CAAA;IACL,IAAI,CAACuE,SAAS,GAAG,IAAI;IACrBhG,KAAK,CAAC,IAAI,CAAC0B,MAAM,CAAC;IAClB,IAAI,CAACX,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB;;AAGF;;;;;AAKA,OAAO,MAAMkF,SAAS,GAAU,eAAgBhG,YAAY,CAAC,MAAM,IAAIsE,SAAS,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}