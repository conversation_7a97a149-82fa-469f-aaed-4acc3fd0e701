{"ast": null, "code": "import { b as p } from \"./chunk-362JBD4O.mjs\";\nasync function o(e) {\n  let {\n      type: t,\n      originMethod: s,\n      path: i,\n      body: r,\n      acceptType: R,\n      contentType: f,\n      params: c,\n      aptosConfig: n,\n      overrides: A\n    } = e,\n    d = n.getRequestUrl(t);\n  return p({\n    url: d,\n    method: \"POST\",\n    originMethod: s,\n    path: i,\n    body: r,\n    contentType: f,\n    acceptType: R,\n    params: c,\n    overrides: A\n  }, n, e.type);\n}\nasync function q(e) {\n  let {\n    aptosConfig: t\n  } = e;\n  return o({\n    ...e,\n    type: \"Fullnode\",\n    overrides: {\n      ...t.clientConfig,\n      ...t.fullnodeConfig,\n      ...e.overrides,\n      HEADERS: {\n        ...t.clientConfig?.HEADERS,\n        ...t.fullnodeConfig?.HEADERS\n      }\n    }\n  });\n}\nasync function C(e) {\n  let {\n    aptosConfig: t\n  } = e;\n  return o({\n    ...e,\n    type: \"Indexer\",\n    overrides: {\n      ...t.clientConfig,\n      ...t.indexerConfig,\n      ...e.overrides,\n      HEADERS: {\n        ...t.clientConfig?.HEADERS,\n        ...t.indexerConfig?.HEADERS\n      }\n    }\n  });\n}\nasync function m(e) {\n  let {\n      aptosConfig: t\n    } = e,\n    s = {\n      ...t,\n      clientConfig: {\n        ...t.clientConfig\n      }\n    };\n  return delete s?.clientConfig?.API_KEY, o({\n    ...e,\n    type: \"Faucet\",\n    overrides: {\n      ...s.clientConfig,\n      ...s.faucetConfig,\n      ...e.overrides,\n      HEADERS: {\n        ...s.clientConfig?.HEADERS,\n        ...s.faucetConfig?.HEADERS\n      }\n    }\n  });\n}\nasync function E(e) {\n  return o({\n    ...e,\n    type: \"Pepper\"\n  });\n}\nasync function a(e) {\n  return o({\n    ...e,\n    type: \"Prover\"\n  });\n}\nexport { o as a, q as b, C as c, m as d, E as e, a as f };", "map": {"version": 3, "names": ["o", "e", "type", "t", "originMethod", "s", "path", "i", "body", "r", "acceptType", "R", "contentType", "f", "params", "c", "aptosConfig", "n", "overrides", "A", "d", "getRequestUrl", "p", "url", "method", "q", "clientConfig", "fullnodeConfig", "HEADERS", "C", "indexerConfig", "m", "API_KEY", "faucetConfig", "E", "a", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\client\\post.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { aptosRequest } from \"./core\";\nimport { AptosResponse } from \"./types\";\nimport { AnyNumber, ClientConfig, MimeType } from \"../types\";\nimport { AptosApiType } from \"../utils/const\";\n\nexport type PostRequestOptions = {\n  /**\n   * The config for the API client\n   */\n  aptosConfig: AptosConfig;\n  /**\n   * The type of API endpoint to call e.g. fullnode, indexer, etc\n   */\n  type: AptosApiType;\n  /**\n   * The name of the API method\n   */\n  originMethod: string;\n  /**\n   * The URL path to the API method\n   */\n  path: string;\n  /**\n   * The content type of the request body\n   */\n  contentType?: MimeType;\n  /**\n   * The accepted content type of the response of the API\n   */\n  acceptType?: MimeType;\n  /**\n   * The query parameters for the request\n   */\n  params?: Record<string, string | AnyNumber | boolean | undefined>;\n  /**\n   * The body of the request, should match the content type of the request\n   */\n  body?: any;\n  /**\n   * Specific client overrides for this request to override aptosConfig\n   */\n  overrides?: ClientConfig;\n};\n\nexport type PostAptosRequestOptions = Omit<PostRequestOptions, \"type\">;\n\n/**\n * Main function to do a Post request\n *\n * @param options PostRequestOptions\n * @returns\n */\nexport async function post<Req extends {}, Res extends {}>(\n  options: PostRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { type, originMethod, path, body, acceptType, contentType, params, aptosConfig, overrides } = options;\n  const url = aptosConfig.getRequestUrl(type);\n\n  return aptosRequest<Req, Res>(\n    {\n      url,\n      method: \"POST\",\n      originMethod,\n      path,\n      body,\n      contentType,\n      acceptType,\n      params,\n      overrides,\n    },\n    aptosConfig,\n    options.type,\n  );\n}\n\nexport async function postAptosFullNode<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n\n  return post<Req, Res>({\n    ...options,\n    type: AptosApiType.FULLNODE,\n    overrides: {\n      ...aptosConfig.clientConfig,\n      ...aptosConfig.fullnodeConfig,\n      ...options.overrides,\n      HEADERS: { ...aptosConfig.clientConfig?.HEADERS, ...aptosConfig.fullnodeConfig?.HEADERS },\n    },\n  });\n}\n\nexport async function postAptosIndexer<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n\n  return post<Req, Res>({\n    ...options,\n    type: AptosApiType.INDEXER,\n    overrides: {\n      ...aptosConfig.clientConfig,\n      ...aptosConfig.indexerConfig,\n      ...options.overrides,\n      HEADERS: { ...aptosConfig.clientConfig?.HEADERS, ...aptosConfig.indexerConfig?.HEADERS },\n    },\n  });\n}\n\nexport async function postAptosFaucet<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n  // Faucet does not support API_KEY\n  // Create a new object with the desired modification\n  const modifiedAptosConfig = {\n    ...aptosConfig,\n    clientConfig: { ...aptosConfig.clientConfig },\n  };\n  // Delete API_KEY config\n  delete modifiedAptosConfig?.clientConfig?.API_KEY;\n\n  return post<Req, Res>({\n    ...options,\n    type: AptosApiType.FAUCET,\n    overrides: {\n      ...modifiedAptosConfig.clientConfig,\n      ...modifiedAptosConfig.faucetConfig,\n      ...options.overrides,\n      HEADERS: { ...modifiedAptosConfig.clientConfig?.HEADERS, ...modifiedAptosConfig.faucetConfig?.HEADERS },\n    },\n  });\n}\n\n/**\n * Makes a post request to the pepper service\n *\n * @param options GetAptosRequestOptions\n * @returns AptosResponse\n */\nexport async function postAptosPepperService<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  return post<Req, Res>({ ...options, type: AptosApiType.PEPPER });\n}\n\nexport async function postAptosProvingService<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  return post<Req, Res>({ ...options, type: AptosApiType.PROVER });\n}\n"], "mappings": ";AAwDA,eAAsBA,EACpBC,CAAA,EACkC;EAClC,IAAM;MAAEC,IAAA,EAAAC,CAAA;MAAMC,YAAA,EAAAC,CAAA;MAAcC,IAAA,EAAAC,CAAA;MAAMC,IAAA,EAAAC,CAAA;MAAMC,UAAA,EAAAC,CAAA;MAAYC,WAAA,EAAAC,CAAA;MAAaC,MAAA,EAAAC,CAAA;MAAQC,WAAA,EAAAC,CAAA;MAAaC,SAAA,EAAAC;IAAU,IAAIlB,CAAA;IAC9FmB,CAAA,GAAMH,CAAA,CAAYI,aAAA,CAAclB,CAAI;EAE1C,OAAOmB,CAAA,CACL;IACEC,GAAA,EAAAH,CAAA;IACAI,MAAA,EAAQ;IACRpB,YAAA,EAAAC,CAAA;IACAC,IAAA,EAAAC,CAAA;IACAC,IAAA,EAAAC,CAAA;IACAG,WAAA,EAAAC,CAAA;IACAH,UAAA,EAAAC,CAAA;IACAG,MAAA,EAAAC,CAAA;IACAG,SAAA,EAAAC;EACF,GACAF,CAAA,EACAhB,CAAA,CAAQC,IACV,CACF;AAAA;AAEA,eAAsBuB,EACpBxB,CAAA,EACkC;EAClC,IAAM;IAAEe,WAAA,EAAAb;EAAY,IAAIF,CAAA;EAExB,OAAOD,CAAA,CAAe;IACpB,GAAGC,CAAA;IACHC,IAAA;IACAgB,SAAA,EAAW;MACT,GAAGf,CAAA,CAAYuB,YAAA;MACf,GAAGvB,CAAA,CAAYwB,cAAA;MACf,GAAG1B,CAAA,CAAQiB,SAAA;MACXU,OAAA,EAAS;QAAE,GAAGzB,CAAA,CAAYuB,YAAA,EAAcE,OAAA;QAAS,GAAGzB,CAAA,CAAYwB,cAAA,EAAgBC;MAAQ;IAC1F;EACF,CAAC,CACH;AAAA;AAEA,eAAsBC,EACpB5B,CAAA,EACkC;EAClC,IAAM;IAAEe,WAAA,EAAAb;EAAY,IAAIF,CAAA;EAExB,OAAOD,CAAA,CAAe;IACpB,GAAGC,CAAA;IACHC,IAAA;IACAgB,SAAA,EAAW;MACT,GAAGf,CAAA,CAAYuB,YAAA;MACf,GAAGvB,CAAA,CAAY2B,aAAA;MACf,GAAG7B,CAAA,CAAQiB,SAAA;MACXU,OAAA,EAAS;QAAE,GAAGzB,CAAA,CAAYuB,YAAA,EAAcE,OAAA;QAAS,GAAGzB,CAAA,CAAY2B,aAAA,EAAeF;MAAQ;IACzF;EACF,CAAC,CACH;AAAA;AAEA,eAAsBG,EACpB9B,CAAA,EACkC;EAClC,IAAM;MAAEe,WAAA,EAAAb;IAAY,IAAIF,CAAA;IAGlBI,CAAA,GAAsB;MAC1B,GAAGF,CAAA;MACHuB,YAAA,EAAc;QAAE,GAAGvB,CAAA,CAAYuB;MAAa;IAC9C;EAEA,cAAOrB,CAAA,EAAqBqB,YAAA,EAAcM,OAAA,EAEnChC,CAAA,CAAe;IACpB,GAAGC,CAAA;IACHC,IAAA;IACAgB,SAAA,EAAW;MACT,GAAGb,CAAA,CAAoBqB,YAAA;MACvB,GAAGrB,CAAA,CAAoB4B,YAAA;MACvB,GAAGhC,CAAA,CAAQiB,SAAA;MACXU,OAAA,EAAS;QAAE,GAAGvB,CAAA,CAAoBqB,YAAA,EAAcE,OAAA;QAAS,GAAGvB,CAAA,CAAoB4B,YAAA,EAAcL;MAAQ;IACxG;EACF,CAAC,CACH;AAAA;AAQA,eAAsBM,EACpBjC,CAAA,EACkC;EAClC,OAAOD,CAAA,CAAe;IAAE,GAAGC,CAAA;IAASC,IAAA;EAA0B,CAAC,CACjE;AAAA;AAEA,eAAsBiC,EACpBlC,CAAA,EACkC;EAClC,OAAOD,CAAA,CAAe;IAAE,GAAGC,CAAA;IAASC,IAAA;EAA0B,CAAC,CACjE;AAAA;AAAA,SAAAF,CAAA,IAAAmC,CAAA,EAAAV,CAAA,IAAAW,CAAA,EAAAP,CAAA,IAAAd,CAAA,EAAAgB,CAAA,IAAAX,CAAA,EAAAc,CAAA,IAAAjC,CAAA,EAAAkC,CAAA,IAAAtB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}