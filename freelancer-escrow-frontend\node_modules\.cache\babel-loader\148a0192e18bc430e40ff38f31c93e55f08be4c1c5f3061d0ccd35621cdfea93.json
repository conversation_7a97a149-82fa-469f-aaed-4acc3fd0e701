{"ast": null, "code": "(function (f) {\n  var c = {};\n  function n(e) {\n    if (c[e]) return c[e].exports;\n    var r = c[e] = {\n      i: e,\n      l: !1,\n      exports: {}\n    };\n    return f[e].call(r.exports, r, r.exports, n), r.l = !0, r.exports;\n  }\n  n.m = f, n.c = c, n.d = function (e, r, i) {\n    n.o(e, r) || Object.defineProperty(e, r, {\n      enumerable: !0,\n      get: i\n    });\n  }, n.r = function (e) {\n    typeof Symbol < \"u\" && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {\n      value: \"Module\"\n    }), Object.defineProperty(e, \"__esModule\", {\n      value: !0\n    });\n  }, n.t = function (e, r) {\n    if (1 & r && (e = n(e)), 8 & r || 4 & r && typeof e == \"object\" && e && e.__esModule) return e;\n    var i = /* @__PURE__ */Object.create(null);\n    if (n.r(i), Object.defineProperty(i, \"default\", {\n      enumerable: !0,\n      value: e\n    }), 2 & r && typeof e != \"string\") for (var a in e) n.d(i, a, function (l) {\n      return e[l];\n    }.bind(null, a));\n    return i;\n  }, n.n = function (e) {\n    var r = e && e.__esModule ? function () {\n      return e.default;\n    } : function () {\n      return e;\n    };\n    return n.d(r, \"a\", r), r;\n  }, n.o = function (e, r) {\n    return Object.prototype.hasOwnProperty.call(e, r);\n  }, n.p = \"\", n(n.s = 0);\n})([function (f, c) {\n  const n = [\"eth_requestAccounts\", \"aptos_requestAccounts\"];\n  function e(i) {\n    const a = new URL(\"dapp-redirected\", i);\n    a.searchParams.append(\"from\", encodeURI(window.location.href)), a.searchParams.append(\"path\", \"/wallet/main\"), window.location.href = a.href;\n  }\n  async function r(i, a = {\n    timeout: 3e3\n  }) {\n    return console.log(\"initializeDekeyProvider called\", {\n      walletDomain: i,\n      opts: a\n    }), new Promise(async (l, d) => {\n      try {\n        if (i && window.location === window.parent.location) return window.dekey = {\n          request: async t => {\n            typeof t == \"object\" && n.includes(t.method) && e(i);\n          },\n          send: async (t, o) => {\n            (typeof t == \"string\" && n.includes(t) || typeof t == \"object\" && n.includes(t)) && e(i);\n          },\n          sendAsync: (t, o) => {\n            typeof t == \"object\" && n.includes(t.method) && e(i);\n          },\n          addListener: () => {},\n          on: () => {},\n          once: () => {},\n          enable: () => {},\n          prependListener: () => {},\n          prependOnceListener: () => {}\n        }, l(\"_unable_to_use_wallet\");\n        (function (t) {\n          let o = history.pushState,\n            s = history.replaceState;\n          history.pushState = function (...u) {\n            o.apply(history, u), window.dispatchEvent(new Event(\"pushstate\")), window.dispatchEvent(new Event(\"locationchange\"));\n          }, history.replaceState = function (...u) {\n            s.apply(history, u), window.dispatchEvent(new Event(\"replacestate\")), window.dispatchEvent(new Event(\"locationchange\"));\n          }, window.addEventListener(\"popstate\", function () {\n            window.dispatchEvent(new Event(\"locationchange\"));\n          }), window.addEventListener(\"locationchange\", function () {\n            window.parent.postMessage(JSON.stringify({\n              href: location.href\n            }), t);\n          });\n        })(i), function () {\n          const {\n            doctype: t\n          } = window.document;\n          return !t || t.name === \"html\";\n        }() && function () {\n          const t = [/\\\\.xml$/u, /\\\\.pdf$/u],\n            o = window.location.pathname;\n          for (let s = 0; s < t.length; s++) if (t[s].test(o)) return !1;\n          return !0;\n        }() && function () {\n          const t = document.documentElement.nodeName;\n          return !t || t.toLowerCase() === \"html\";\n        }() && !function () {\n          const t = [\"uscourts.gov\", \"dropbox.com\", \"webbyawards.com\", \"cdn.shopify.com/s/javascripts/tricorder/xtld-read-only-frame.html\", \"adyen.com\", \"gravityforms.com\", \"harbourair.com\", \"ani.gamer.com.tw\", \"blueskybooking.com\", \"sharefile.com\"],\n            o = window.location.href;\n          let s;\n          for (let u = 0; u < t.length; u++) {\n            const h = t[u].replace(\".\", \"\\\\.\");\n            if (s = new RegExp(`(?:https?:\\\\/\\\\/)(?:(?!${h}).)*$`, \"u\"), !s.test(o)) return !0;\n          }\n          return !1;\n        }() && (setTimeout(() => {\n          d(\"DEKEY_PROVIDER_INIT_TIMEOUT\");\n        }, a.timeout), function (t) {\n          try {\n            const o = document.head || document.documentElement,\n              s = document.createElement(\"script\");\n            s.setAttribute(\"async\", !1), s.textContent = t, o.insertBefore(s, o.children[0]), o.removeChild(s);\n          } catch (o) {\n            console.error(\"Dekey script injection failed\", o);\n          }\n        }(`!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&\"object\"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,\"default\",{enumerable:!0,value:e}),2&t&&\"string\"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,\"a\",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p=\"\",r(r.s=36)}([function(e,t,r){(t=e.exports=r(26)).Stream=t,t.Readable=t,t.Writable=r(16),t.Duplex=r(2),t.Transform=r(30),t.PassThrough=r(57)},function(e,t){var r,n,i=e.exports={};function o(){throw new Error(\"setTimeout has not been defined\")}function s(){throw new Error(\"clearTimeout has not been defined\")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r=\"function\"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n=\"function\"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var u,c=[],l=!1,f=-1;function d(){l&&u&&(l=!1,u.length?c=u.concat(c):f=-1,c.length&&h())}function h(){if(!l){var e=a(d);l=!0;for(var t=c.length;t;){for(u=c,c=[];++f<t;)u&&u[f].run();f=-1,t=c.length}u=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function g(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new p(e,t)),1!==c.length||l||a(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title=\"browser\",i.browser=!0,i.env={},i.argv=[],i.version=\"\",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(e){return[]},i.binding=function(e){throw new Error(\"process.binding is not supported\")},i.cwd=function(){return\"/\"},i.chdir=function(e){throw new Error(\"process.chdir is not supported\")},i.umask=function(){return 0}},function(e,t,r){\"use strict\";var n=r(11),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};e.exports=f;var o=Object.create(r(7));o.inherits=r(4);var s=r(26),a=r(16);o.inherits(f,s);for(var u=i(a.prototype),c=0;c<u.length;c++){var l=u[c];f.prototype[l]||(f.prototype[l]=a.prototype[l])}function f(e){if(!(this instanceof f))return new f(e);s.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once(\"end\",d)}function d(){this.allowHalfOpen||this._writableState.ended||n.nextTick(h,this)}function h(e){e.end()}Object.defineProperty(f.prototype,\"writableHighWaterMark\",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,\"destroyed\",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),f.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},function(e,t){var r;r=function(){return this}();try{r=r||new Function(\"return this\")()}catch(e){\"object\"==typeof window&&(r=window)}e.exports=r},function(e,t){\"function\"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.NOOP=t.isValidNetworkVersion=t.isValidChainId=t.getRpcPromiseCallback=t.getDefaultExternalMiddleware=t.EMITTED_NOTIFICATIONS=void 0;const n=r(22),i=r(9);t.EMITTED_NOTIFICATIONS=Object.freeze([\"eth_subscription\"]);t.getDefaultExternalMiddleware=(e=console)=>{return[n.createIdRemapMiddleware(),(t=e,(e,r,n)=>{\"string\"==typeof e.method&&e.method||(r.error=i.ethErrors.rpc.invalidRequest({message:\"The request 'method' must be a non-empty string.\",data:e})),n(e=>{const{error:n}=r;return n?(t.error(\"Dekey - RPC Error: \"+n.message,n),e()):e()})})];var t};t.getRpcPromiseCallback=(e,t,r=!0)=>(n,i)=>{n||i.error?t(n||i.error):!r||Array.isArray(i)?e(i):e(i.result)};t.isValidChainId=e=>Boolean(e)&&\"string\"==typeof e&&e.startsWith(\"0x\");t.isValidNetworkVersion=e=>Boolean(e)&&\"string\"==typeof e;t.NOOP=()=>{}},function(e,t,r){\"use strict\";(function(e){\n/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> Aboukhadijeh <http://feross.org>\n * @license  MIT\n */\nvar n=r(47),i=r(48),o=r(25);function s(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function a(e,t){if(s()<t)throw new RangeError(\"Invalid typed array length\");return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=u.prototype:(null===e&&(e=new u(t)),e.length=t),e}function u(e,t,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(e,t,r);if(\"number\"==typeof e){if(\"string\"==typeof t)throw new Error(\"If encoding is specified then the first argument must be a string\");return f(this,e)}return c(this,e,t,r)}function c(e,t,r,n){if(\"number\"==typeof t)throw new TypeError('\"value\" argument must not be a number');return\"undefined\"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError(\"'offset' is out of bounds\");if(t.byteLength<r+(n||0))throw new RangeError(\"'length' is out of bounds\");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);u.TYPED_ARRAY_SUPPORT?(e=t).__proto__=u.prototype:e=d(e,t);return e}(e,t,r,n):\"string\"==typeof t?function(e,t,r){\"string\"==typeof r&&\"\"!==r||(r=\"utf8\");if(!u.isEncoding(r))throw new TypeError('\"encoding\" must be a valid string encoding');var n=0|p(t,r),i=(e=a(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(u.isBuffer(t)){var r=0|h(t.length);return 0===(e=a(e,r)).length||t.copy(e,0,0,r),e}if(t){if(\"undefined\"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||\"length\"in t)return\"number\"!=typeof t.length||(n=t.length)!=n?a(e,0):d(e,t);if(\"Buffer\"===t.type&&o(t.data))return d(e,t.data)}var n;throw new TypeError(\"First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.\")}(e,t)}function l(e){if(\"number\"!=typeof e)throw new TypeError('\"size\" argument must be a number');if(e<0)throw new RangeError('\"size\" argument must not be negative')}function f(e,t){if(l(t),e=a(e,t<0?0:0|h(t)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function d(e,t){var r=t.length<0?0:0|h(t.length);e=a(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function h(e){if(e>=s())throw new RangeError(\"Attempt to allocate Buffer larger than maximum size: 0x\"+s().toString(16)+\" bytes\");return 0|e}function p(e,t){if(u.isBuffer(e))return e.length;if(\"undefined\"!=typeof ArrayBuffer&&\"function\"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;\"string\"!=typeof e&&(e=\"\"+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case\"ascii\":case\"latin1\":case\"binary\":return r;case\"utf8\":case\"utf-8\":case void 0:return q(e).length;case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return 2*r;case\"hex\":return r>>>1;case\"base64\":return W(e).length;default:if(n)return q(e).length;t=(\"\"+t).toLowerCase(),n=!0}}function g(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return\"\";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return\"\";if((r>>>=0)<=(t>>>=0))return\"\";for(e||(e=\"utf8\");;)switch(e){case\"hex\":return j(this,t,r);case\"utf8\":case\"utf-8\":return M(this,t,r);case\"ascii\":return R(this,t,r);case\"latin1\":case\"binary\":return k(this,t,r);case\"base64\":return O(this,t,r);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return x(this,t,r);default:if(n)throw new TypeError(\"Unknown encoding: \"+e);e=(e+\"\").toLowerCase(),n=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){if(0===e.length)return-1;if(\"string\"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if(\"string\"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:b(e,t,r,n,i);if(\"number\"==typeof t)return t&=255,u.TYPED_ARRAY_SUPPORT&&\"function\"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):b(e,[t],r,n,i);throw new TypeError(\"val must be string, number or Buffer\")}function b(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&(\"ucs2\"===(n=String(n).toLowerCase())||\"ucs-2\"===n||\"utf16le\"===n||\"utf-16le\"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var l=-1;for(o=r;o<a;o++)if(c(e,o)===c(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*s}else-1!==l&&(o-=o-l),l=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var f=!0,d=0;d<u;d++)if(c(e,o+d)!==c(t,d)){f=!1;break}if(f)return o}return-1}function v(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError(\"Invalid hex string\");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[r+s]=a}return s}function w(e,t,r,n){return F(q(t,e.length-r),e,r,n)}function _(e,t,r,n){return F(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function E(e,t,r,n){return _(e,t,r,n)}function S(e,t,r,n){return F(W(t),e,r,n)}function P(e,t,r,n){return F(function(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function O(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function M(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,s,a,u,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&c)<<6|63&o)>127&&(l=u);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var r=\"\",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}t.Buffer=u,t.SlowBuffer=function(e){+e!=e&&(e=0);return u.alloc(+e)},t.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&\"function\"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=s(),u.poolSize=8192,u._augment=function(e){return e.__proto__=u.prototype,e},u.from=function(e,t,r){return c(null,e,t,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,\"undefined\"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(e,t,r){return function(e,t,r,n){return l(t),t<=0?a(e,t):void 0!==r?\"string\"==typeof n?a(e,t).fill(r,n):a(e,t).fill(r):a(e,t)}(null,e,t,r)},u.allocUnsafe=function(e){return f(null,e)},u.allocUnsafeSlow=function(e){return f(null,e)},u.isBuffer=function(e){return!(null==e||!e._isBuffer)},u.compare=function(e,t){if(!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError(\"Arguments must be Buffers\");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case\"hex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return!0;default:return!1}},u.concat=function(e,t){if(!o(e))throw new TypeError('\"list\" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=u.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(!u.isBuffer(s))throw new TypeError('\"list\" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError(\"Buffer size must be a multiple of 16-bits\");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError(\"Buffer size must be a multiple of 32-bits\");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError(\"Buffer size must be a multiple of 64-bits\");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},u.prototype.toString=function(){var e=0|this.length;return 0===e?\"\":0===arguments.length?M(this,0,e):g.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError(\"Argument must be a Buffer\");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e=\"\",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString(\"hex\",0,r).match(/.{2}/g).join(\" \"),this.length>r&&(e+=\" ... \")),\"<Buffer \"+e+\">\"},u.prototype.compare=function(e,t,r,n,i){if(!u.isBuffer(e))throw new TypeError(\"Argument must be a Buffer\");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError(\"out of range index\");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(t>>>=0),a=Math.min(o,s),c=this.slice(n,i),l=e.slice(t,r),f=0;f<a;++f)if(c[f]!==l[f]){o=c[f],s=l[f];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n=\"utf8\",r=this.length,t=0;else if(void 0===r&&\"string\"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error(\"Buffer.write(string, encoding, offset[, length]) is no longer supported\");t|=0,isFinite(r)?(r|=0,void 0===n&&(n=\"utf8\")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError(\"Attempt to write outside buffer bounds\");n||(n=\"utf8\");for(var o=!1;;)switch(n){case\"hex\":return v(this,e,t,r);case\"utf8\":case\"utf-8\":return w(this,e,t,r);case\"ascii\":return _(this,e,t,r);case\"latin1\":case\"binary\":return E(this,e,t,r);case\"base64\":return S(this,e,t,r);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return P(this,e,t,r);default:if(o)throw new TypeError(\"Unknown encoding: \"+n);n=(\"\"+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:\"Buffer\",data:Array.prototype.slice.call(this._arr||this,0)}};function R(e,t,r){var n=\"\";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function k(e,t,r){var n=\"\";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function j(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i=\"\",o=t;o<r;++o)i+=U(e[o]);return i}function x(e,t,r){for(var n=e.slice(t,r),i=\"\",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function A(e,t,r){if(e%1!=0||e<0)throw new RangeError(\"offset is not uint\");if(e+t>r)throw new RangeError(\"Trying to access beyond buffer length\")}function C(e,t,r,n,i,o){if(!u.isBuffer(e))throw new TypeError('\"buffer\" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('\"value\" argument is out of bounds');if(r+n>e.length)throw new RangeError(\"Index out of range\")}function T(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function D(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function I(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError(\"Index out of range\");if(r<0)throw new RangeError(\"Index out of range\")}function L(e,t,r,n,o){return o||I(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function N(e,t,r,n,o){return o||I(e,0,r,8),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=u.prototype;else{var i=t-e;r=new u(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},u.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},u.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUInt8=function(e,t){return t||A(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return t||A(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return t||A(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return t||A(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUInt32BE=function(e,t){return t||A(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},u.prototype.readInt8=function(e,t){return t||A(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){t||A(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){t||A(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return t||A(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return t||A(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return t||A(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return t||A(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return t||A(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return t||A(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||C(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},u.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||C(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},u.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,1,255,0),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):T(this,e,t,!0),t+2},u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):T(this,e,t,!1),t+2},u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):D(this,e,t,!0),t+4},u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):D(this,e,t,!1),t+4},u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);C(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);C(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,1,127,-128),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):T(this,e,t,!0),t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):T(this,e,t,!1),t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):D(this,e,t,!0),t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):D(this,e,t,!1),t+4},u.prototype.writeFloatLE=function(e,t,r){return L(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return L(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return N(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return N(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError(\"targetStart out of bounds\");if(r<0||r>=this.length)throw new RangeError(\"sourceStart out of bounds\");if(n<0)throw new RangeError(\"sourceEnd out of bounds\");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},u.prototype.fill=function(e,t,r,n){if(\"string\"==typeof e){if(\"string\"==typeof t?(n=t,t=0,r=this.length):\"string\"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&\"string\"!=typeof n)throw new TypeError(\"encoding must be a string\");if(\"string\"==typeof n&&!u.isEncoding(n))throw new TypeError(\"Unknown encoding: \"+n)}else\"number\"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError(\"Out of range index\");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),\"number\"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=u.isBuffer(e)?e:q(new u(e,n).toString()),a=s.length;for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var B=/[^+\\\\/0-9A-Za-z-_]/g;function U(e){return e<16?\"0\"+e.toString(16):e.toString(16)}function q(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error(\"Invalid code point\");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function W(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\\\\s+|\\\\s+$/g,\"\")}(e).replace(B,\"\")).length<2)return\"\";for(;e.length%4!=0;)e+=\"=\";return e}(e))}function F(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r(3))},function(e,t,r){function n(e){return Object.prototype.toString.call(e)}t.isArray=function(e){return Array.isArray?Array.isArray(e):\"[object Array]\"===n(e)},t.isBoolean=function(e){return\"boolean\"==typeof e},t.isNull=function(e){return null===e},t.isNullOrUndefined=function(e){return null==e},t.isNumber=function(e){return\"number\"==typeof e},t.isString=function(e){return\"string\"==typeof e},t.isSymbol=function(e){return\"symbol\"==typeof e},t.isUndefined=function(e){return void 0===e},t.isRegExp=function(e){return\"[object RegExp]\"===n(e)},t.isObject=function(e){return\"object\"==typeof e&&null!==e},t.isDate=function(e){return\"[object Date]\"===n(e)},t.isError=function(e){return\"[object Error]\"===n(e)||e instanceof Error},t.isFunction=function(e){return\"function\"==typeof e},t.isPrimitive=function(e){return null===e||\"boolean\"==typeof e||\"number\"==typeof e||\"string\"==typeof e||\"symbol\"==typeof e||void 0===e},t.isBuffer=r(6).Buffer.isBuffer},function(e,t,r){\"use strict\";var n,i=\"object\"==typeof Reflect?Reflect:null,o=i&&\"function\"==typeof i.apply?i.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};n=i&&\"function\"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var s=Number.isNaN||function(e){return e!=e};function a(){a.init.call(this)}e.exports=a,e.exports.once=function(e,t){return new Promise((function(r,n){function i(r){e.removeListener(t,o),n(r)}function o(){\"function\"==typeof e.removeListener&&e.removeListener(\"error\",i),r([].slice.call(arguments))}y(e,t,o,{once:!0}),\"error\"!==t&&function(e,t,r){\"function\"==typeof e.on&&y(e,\"error\",t,r)}(e,i,{once:!0})}))},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var u=10;function c(e){if(\"function\"!=typeof e)throw new TypeError('The \"listener\" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function f(e,t,r,n){var i,o,s,a;if(c(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit(\"newListener\",t,r.listener?r.listener:r),o=e._events),s=o[t]),void 0===s)s=o[t]=r,++e._eventsCount;else if(\"function\"==typeof s?s=o[t]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(i=l(e))>0&&s.length>i&&!s.warned){s.warned=!0;var u=new Error(\"Possible EventEmitter memory leak detected. \"+s.length+\" \"+String(t)+\" listeners added. Use emitter.setMaxListeners() to increase limit\");u.name=\"MaxListenersExceededWarning\",u.emitter=e,u.type=t,u.count=s.length,a=u,console&&console.warn&&console.warn(a)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=d.bind(n);return i.listener=r,n.wrapFn=i,i}function p(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:\"function\"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):m(i,i.length)}function g(e){var t=this._events;if(void 0!==t){var r=t[e];if(\"function\"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function m(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function y(e,t,r,n){if(\"function\"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if(\"function\"!=typeof e.addEventListener)throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){n.once&&e.removeEventListener(t,i),r(o)}))}}Object.defineProperty(a,\"defaultMaxListeners\",{enumerable:!0,get:function(){return u},set:function(e){if(\"number\"!=typeof e||e<0||s(e))throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received '+e+\".\");u=e}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if(\"number\"!=typeof e||e<0||s(e))throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received '+e+\".\");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return l(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n=\"error\"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var a=new Error(\"Unhandled error.\"+(s?\" (\"+s.message+\")\":\"\"));throw a.context=s,a}var u=i[e];if(void 0===u)return!1;if(\"function\"==typeof u)o(u,this,t);else{var c=u.length,l=m(u,c);for(r=0;r<c;++r)o(l[r],this,t)}return!0},a.prototype.addListener=function(e,t){return f(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return f(this,e,t,!0)},a.prototype.once=function(e,t){return c(t),this.on(e,h(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){return c(t),this.prependListener(e,h(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,n,i,o,s;if(c(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit(\"removeListener\",e,r.listener||t));else if(\"function\"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===t||r[o].listener===t){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit(\"removeListener\",e,s||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)\"removeListener\"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners(\"removeListener\"),this._events=Object.create(null),this._eventsCount=0,this}if(\"function\"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},a.prototype.listeners=function(e){return p(this,e,!0)},a.prototype.rawListeners=function(e){return p(this,e,!1)},a.listenerCount=function(e,t){return\"function\"==typeof e.listenerCount?e.listenerCount(t):g.call(e,t)},a.prototype.listenerCount=g,a.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.getMessageFromCode=t.serializeError=t.EthereumProviderError=t.EthereumRpcError=t.ethErrors=t.errorCodes=void 0;const n=r(13);Object.defineProperty(t,\"EthereumRpcError\",{enumerable:!0,get:function(){return n.EthereumRpcError}}),Object.defineProperty(t,\"EthereumProviderError\",{enumerable:!0,get:function(){return n.EthereumProviderError}});const i=r(21);Object.defineProperty(t,\"serializeError\",{enumerable:!0,get:function(){return i.serializeError}}),Object.defineProperty(t,\"getMessageFromCode\",{enumerable:!0,get:function(){return i.getMessageFromCode}});const o=r(39);Object.defineProperty(t,\"ethErrors\",{enumerable:!0,get:function(){return o.ethErrors}});const s=r(14);Object.defineProperty(t,\"errorCodes\",{enumerable:!0,get:function(){return s.errorCodes}})},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});const n={errors:{disconnected:()=>\"Dekey: Disconnected from chain. Attempting to connect.\",permanentlyDisconnected:()=>\"Dekey: Disconnected from Dekey background. Page reload required.\",sendSiteMetadata:()=>\"Dekey: Failed to send site metadata. This is an internal error, please report this bug.\",unsupportedSync:e=>\\`Dekey: The Dekey Ethereum provider does not support synchronous methods like \\${e} without a callback parameter.\\`,invalidDuplexStream:()=>\"Must provide a Node.js-style duplex stream.\",invalidNetworkParams:()=>\"Dekey: Received invalid network parameters. Please report this bug.\",invalidRequestArgs:()=>\"Expected a single, non-array, object argument.\",invalidRequestMethod:()=>\"'args.method' must be a non-empty string.\",invalidRequestParams:()=>\"'args.params' must be an object or array if provided.\",invalidLoggerObject:()=>\"'args.logger' must be an object if provided.\",invalidLoggerMethod:e=>\\`'args.logger' must include required method '\\${e}'.\\`},info:{connected:e=>\\`Dekey: Connected to chain with ID \"\\${e}\".\\`},warnings:{enableDeprecation:\"Dekey: 'ethereum.enable()' is deprecated and may be removed in the future. Please use the 'eth_requestAccounts' RPC method instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1102\",sendDeprecation:\"Dekey: 'ethereum.send(...)' is deprecated and may be removed in the future. Please use 'ethereum.sendAsync(...)' or 'ethereum.request(...)' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193\",events:{close:\"Dekey: The event 'close' is deprecated and may be removed in the future. Please use 'disconnect' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#disconnect\",data:\"Dekey: The event 'data' is deprecated and will be removed in the future. Use 'message' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message\",networkChanged:\"Dekey: The event 'networkChanged' is deprecated and may be removed in the future. Use 'chainChanged' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#chainchanged\",notification:\"Dekey: The event 'notification' is deprecated and may be removed in the future. Use 'message' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message\"},experimentalMethods:\"Dekey: 'ethereum._dekey' exposes non-standard, experimental methods. They may be removed or changed without warning.\"}};t.default=n},function(e,t,r){\"use strict\";(function(t){void 0===t||!t.version||0===t.version.indexOf(\"v0.\")||0===t.version.indexOf(\"v1.\")&&0!==t.version.indexOf(\"v1.8.\")?e.exports={nextTick:function(e,r,n,i){if(\"function\"!=typeof e)throw new TypeError('\"callback\" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return t.nextTick(e);case 2:return t.nextTick((function(){e.call(null,r)}));case 3:return t.nextTick((function(){e.call(null,r,n)}));case 4:return t.nextTick((function(){e.call(null,r,n,i)}));default:for(o=new Array(a-1),s=0;s<o.length;)o[s++]=arguments[s];return t.nextTick((function(){e.apply(null,o)}))}}}:e.exports=t}).call(this,r(1))},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});const n=r(8);function i(e,t,r){try{Reflect.apply(e,t,r)}catch(e){setTimeout(()=>{throw e})}}class o extends n.EventEmitter{emit(e,...t){let r=\"error\"===e;const n=this._events;if(void 0!==n)r=r&&void 0===n.error;else if(!r)return!1;if(r){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const r=new Error(\"Unhandled error.\"+(e?\\` (\\${e.message})\\`:\"\"));throw r.context=e,r}const o=n[e];if(void 0===o)return!1;if(\"function\"==typeof o)i(o,this,t);else{const e=o.length,r=function(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}(o);for(let n=0;n<e;n+=1)i(r[n],this,t)}return!0}}t.default=o},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.EthereumProviderError=t.EthereumRpcError=void 0;const n=r(38);class i extends Error{constructor(e,t,r){if(!Number.isInteger(e))throw new Error('\"code\" must be an integer.');if(!t||\"string\"!=typeof t)throw new Error('\"message\" must be a nonempty string.');super(t),this.code=e,void 0!==r&&(this.data=r)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),this.stack&&(e.stack=this.stack),e}toString(){return n.default(this.serialize(),o,2)}}t.EthereumRpcError=i;function o(e,t){if(\"[Circular]\"!==t)return t}t.EthereumProviderError=class extends i{constructor(e,t,r){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');super(e,t,r)}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.errorValues=t.errorCodes=void 0,t.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},t.errorValues={\"-32700\":{standard:\"JSON RPC 2.0\",message:\"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.\"},\"-32600\":{standard:\"JSON RPC 2.0\",message:\"The JSON sent is not a valid Request object.\"},\"-32601\":{standard:\"JSON RPC 2.0\",message:\"The method does not exist / is not available.\"},\"-32602\":{standard:\"JSON RPC 2.0\",message:\"Invalid method parameter(s).\"},\"-32603\":{standard:\"JSON RPC 2.0\",message:\"Internal JSON-RPC error.\"},\"-32000\":{standard:\"EIP-1474\",message:\"Invalid input.\"},\"-32001\":{standard:\"EIP-1474\",message:\"Resource not found.\"},\"-32002\":{standard:\"EIP-1474\",message:\"Resource unavailable.\"},\"-32003\":{standard:\"EIP-1474\",message:\"Transaction rejected.\"},\"-32004\":{standard:\"EIP-1474\",message:\"Method not supported.\"},\"-32005\":{standard:\"EIP-1474\",message:\"Request limit exceeded.\"},4001:{standard:\"EIP-1193\",message:\"User rejected the request.\"},4100:{standard:\"EIP-1193\",message:\"The requested account and/or method has not been authorized by the user.\"},4200:{standard:\"EIP-1193\",message:\"The requested method is not supported by this Ethereum provider.\"},4900:{standard:\"EIP-1193\",message:\"The provider is disconnected from all chains.\"},4901:{standard:\"EIP-1193\",message:\"The provider is disconnected from the specified chain.\"}}},function(e,t,r){var n=r(6),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(o(n,t),t.Buffer=s),o(i,s),s.from=function(e,t,r){if(\"number\"==typeof e)throw new TypeError(\"Argument must not be a number\");return i(e,t,r)},s.alloc=function(e,t,r){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");var n=i(e);return void 0!==t?\"string\"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return i(e)},s.allocUnsafeSlow=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return n.SlowBuffer(e)}},function(e,t,r){\"use strict\";(function(t,n,i){var o=r(11);function s(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;e.entry=null;for(;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}(t,e)}}e.exports=b;var a,u=!t.browser&&[\"v0.10\",\"v0.9.\"].indexOf(t.version.slice(0,5))>-1?n:o.nextTick;b.WritableState=y;var c=Object.create(r(7));c.inherits=r(4);var l={deprecate:r(55)},f=r(27),d=r(15).Buffer,h=(void 0!==i?i:\"undefined\"!=typeof window?window:\"undefined\"!=typeof self?self:{}).Uint8Array||function(){};var p,g=r(28);function m(){}function y(e,t){a=a||r(2),e=e||{};var n=t instanceof a;this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var i=e.highWaterMark,c=e.writableHighWaterMark,l=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(c||0===c)?c:l,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var f=!1===e.decodeStrings;this.decodeStrings=!f,this.defaultEncoding=e.defaultEncoding||\"utf8\",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,n,i){--t.pendingcb,r?(o.nextTick(i,n),o.nextTick(P,e,t),e._writableState.errorEmitted=!0,e.emit(\"error\",n)):(i(n),e._writableState.errorEmitted=!0,e.emit(\"error\",n),P(e,t))}(e,r,n,t,i);else{var s=E(r);s||r.corked||r.bufferProcessing||!r.bufferedRequest||_(e,r),n?u(w,e,r,s,i):w(e,r,s,i)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new s(this)}function b(e){if(a=a||r(2),!(p.call(b,this)||this instanceof a))return new b(e);this._writableState=new y(e,this),this.writable=!0,e&&(\"function\"==typeof e.write&&(this._write=e.write),\"function\"==typeof e.writev&&(this._writev=e.writev),\"function\"==typeof e.destroy&&(this._destroy=e.destroy),\"function\"==typeof e.final&&(this._final=e.final)),f.call(this)}function v(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function w(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit(\"drain\"))}(e,t),t.pendingcb--,n(),P(e,t)}function _(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var a=0,u=!0;r;)i[a]=r,r.isBuf||(u=!1),r=r.next,a+=1;i.allBuffers=u,v(e,t,!0,t.length,i,\"\",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new s(t),t.bufferedRequestCount=0}else{for(;r;){var c=r.chunk,l=r.encoding,f=r.callback;if(v(e,t,!1,t.objectMode?1:c.length,c,l,f),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function E(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function S(e,t){e._final((function(r){t.pendingcb--,r&&e.emit(\"error\",r),t.prefinished=!0,e.emit(\"prefinish\"),P(e,t)}))}function P(e,t){var r=E(t);return r&&(!function(e,t){t.prefinished||t.finalCalled||(\"function\"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,o.nextTick(S,e,t)):(t.prefinished=!0,e.emit(\"prefinish\")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit(\"finish\"))),r}c.inherits(b,f),y.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(y.prototype,\"buffer\",{get:l.deprecate((function(){return this.getBuffer()}),\"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.\",\"DEP0003\")})}catch(e){}}(),\"function\"==typeof Symbol&&Symbol.hasInstance&&\"function\"==typeof Function.prototype[Symbol.hasInstance]?(p=Function.prototype[Symbol.hasInstance],Object.defineProperty(b,Symbol.hasInstance,{value:function(e){return!!p.call(this,e)||this===b&&(e&&e._writableState instanceof y)}})):p=function(e){return e instanceof this},b.prototype.pipe=function(){this.emit(\"error\",new Error(\"Cannot pipe, not readable\"))},b.prototype.write=function(e,t,r){var n,i=this._writableState,s=!1,a=!i.objectMode&&(n=e,d.isBuffer(n)||n instanceof h);return a&&!d.isBuffer(e)&&(e=function(e){return d.from(e)}(e)),\"function\"==typeof t&&(r=t,t=null),a?t=\"buffer\":t||(t=i.defaultEncoding),\"function\"!=typeof r&&(r=m),i.ended?function(e,t){var r=new Error(\"write after end\");e.emit(\"error\",r),o.nextTick(t,r)}(this,r):(a||function(e,t,r,n){var i=!0,s=!1;return null===r?s=new TypeError(\"May not write null values to stream\"):\"string\"==typeof r||void 0===r||t.objectMode||(s=new TypeError(\"Invalid non-string/buffer chunk\")),s&&(e.emit(\"error\",s),o.nextTick(n,s),i=!1),i}(this,i,e,r))&&(i.pendingcb++,s=function(e,t,r,n,i,o){if(!r){var s=function(e,t,r){e.objectMode||!1===e.decodeStrings||\"string\"!=typeof t||(t=d.from(t,r));return t}(t,n,i);n!==s&&(r=!0,i=\"buffer\",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;u||(t.needDrain=!0);if(t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,a,n,i,o);return u}(this,i,a,e,t,r)),s},b.prototype.cork=function(){this._writableState.corked++},b.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||_(this,e))},b.prototype.setDefaultEncoding=function(e){if(\"string\"==typeof e&&(e=e.toLowerCase()),!([\"hex\",\"utf8\",\"utf-8\",\"ascii\",\"binary\",\"base64\",\"ucs2\",\"ucs-2\",\"utf16le\",\"utf-16le\",\"raw\"].indexOf((e+\"\").toLowerCase())>-1))throw new TypeError(\"Unknown encoding: \"+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(b.prototype,\"writableHighWaterMark\",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),b.prototype._write=function(e,t,r){r(new Error(\"_write() is not implemented\"))},b.prototype._writev=null,b.prototype.end=function(e,t,r){var n=this._writableState;\"function\"==typeof e?(r=e,e=null,t=null):\"function\"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||function(e,t,r){t.ending=!0,P(e,t),r&&(t.finished?o.nextTick(r):e.once(\"finish\",r));t.ended=!0,e.writable=!1}(this,n,r)},Object.defineProperty(b.prototype,\"destroyed\",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),b.prototype.destroy=g.destroy,b.prototype._undestroy=g.undestroy,b.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this,r(1),r(53).setImmediate,r(3))},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.MetaMaskInpageProvider=t.MetaMaskInpageProviderStreamName=void 0;const i=r(9),o=r(63),s=n(r(10)),a=r(5),u=r(18);t.MetaMaskInpageProviderStreamName=\"dekey-provider\";class c extends u.AbstractStreamProvider{constructor(e,{jsonRpcStreamName:r=t.MetaMaskInpageProviderStreamName,logger:n=console,maxEventListeners:i,shouldSendMetadata:s}={}){if(super(e,{jsonRpcStreamName:r,logger:n,maxEventListeners:i,rpcMiddleware:a.getDefaultExternalMiddleware(n)}),this._sentWarnings={enable:!1,experimentalMethods:!1,send:!1,events:{close:!1,data:!1,networkChanged:!1,notification:!1}},this._initializeStateAsync(),this.networkVersion=null,this.isMetaMask=!0,this._sendSync=this._sendSync.bind(this),this.enable=this.enable.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this._warnOfDeprecation=this._warnOfDeprecation.bind(this),this._metamask=this._getExperimentalApi(),this._jsonRpcConnection.events.on(\"notification\",e=>{const{method:t}=e;a.EMITTED_NOTIFICATIONS.includes(t)&&(this.emit(\"data\",e),this.emit(\"notification\",e.params.result))}),s)if(\"complete\"===document.readyState)o.sendSiteMetadata(this._rpcEngine,this._log);else{const e=()=>{o.sendSiteMetadata(this._rpcEngine,this._log),window.removeEventListener(\"DOMContentLoaded\",e)};window.addEventListener(\"DOMContentLoaded\",e)}}sendAsync(e,t){this._rpcRequest(e,t)}addListener(e,t){return this._warnOfDeprecation(e),super.addListener(e,t)}on(e,t){return this._warnOfDeprecation(e),super.on(e,t)}once(e,t){return this._warnOfDeprecation(e),super.once(e,t)}prependListener(e,t){return this._warnOfDeprecation(e),super.prependListener(e,t)}prependOnceListener(e,t){return this._warnOfDeprecation(e),super.prependOnceListener(e,t)}_handleDisconnect(e,t){super._handleDisconnect(e,t),this.networkVersion&&!e&&(this.networkVersion=null)}_warnOfDeprecation(e){var t;!1===(null===(t=this._sentWarnings)||void 0===t?void 0:t.events[e])&&(this._log.warn(s.default.warnings.events[e]),this._sentWarnings.events[e]=!0)}enable(){return this._sentWarnings.enable||(this._log.warn(s.default.warnings.enableDeprecation),this._sentWarnings.enable=!0),new Promise((e,t)=>{try{this._rpcRequest({method:\"eth_requestAccounts\",params:[]},a.getRpcPromiseCallback(e,t))}catch(e){t(e)}})}send(e,t){return this._sentWarnings.send||(this._log.warn(s.default.warnings.sendDeprecation),this._sentWarnings.send=!0),\"string\"!=typeof e||t&&!Array.isArray(t)?e&&\"object\"==typeof e&&\"function\"==typeof t?this._rpcRequest(e,t):this._sendSync(e):new Promise((r,n)=>{try{this._rpcRequest({method:e,params:t},a.getRpcPromiseCallback(r,n,!1))}catch(e){n(e)}})}_sendSync(e){let t;switch(e.method){case\"eth_accounts\":t=this.selectedAddress?[this.selectedAddress]:[];break;case\"eth_coinbase\":t=this.selectedAddress||null;break;case\"eth_uninstallFilter\":this._rpcRequest(e,a.NOOP),t=!0;break;case\"net_version\":t=this.networkVersion||null;break;default:throw new Error(s.default.errors.unsupportedSync(e.method))}return{id:e.id,jsonrpc:e.jsonrpc,result:t}}_getExperimentalApi(){return new Proxy({isUnlocked:async()=>(this._state.initialized||await new Promise(e=>{this.on(\"_initialized\",()=>e())}),this._state.isUnlocked),requestBatch:async e=>{if(!Array.isArray(e))throw i.ethErrors.rpc.invalidRequest({message:\"Batch requests must be made with an array of request objects.\",data:e});return new Promise((t,r)=>{this._rpcRequest(e,a.getRpcPromiseCallback(t,r))})}},{get:(e,t,...r)=>(this._sentWarnings.experimentalMethods||(this._log.warn(s.default.warnings.experimentalMethods),this._sentWarnings.experimentalMethods=!0),Reflect.get(e,t,...r))})}_handleChainChanged({chainId:e,networkVersion:t}={}){super._handleChainChanged({chainId:e,networkVersion:t}),this._state.isConnected&&t!==this.networkVersion&&(this.networkVersion=t,this._state.initialized&&this.emit(\"networkChanged\",this.networkVersion))}}t.MetaMaskInpageProvider=c},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.StreamProvider=t.AbstractStreamProvider=void 0;const i=n(r(31)),o=r(67),s=r(68),a=n(r(33)),u=n(r(10)),c=r(5),l=r(20);class f extends l.BaseProvider{constructor(e,{jsonRpcStreamName:t,logger:r,maxEventListeners:n,rpcMiddleware:l}){if(super({logger:r,maxEventListeners:n,rpcMiddleware:l}),!o.duplex(e))throw new Error(u.default.errors.invalidDuplexStream());this._handleStreamDisconnect=this._handleStreamDisconnect.bind(this);const f=new i.default;a.default(e,f,e,this._handleStreamDisconnect.bind(this,\"Dekey\")),this._jsonRpcConnection=s.createStreamMiddleware(),a.default(this._jsonRpcConnection.stream,f.createStream(t),this._jsonRpcConnection.stream,this._handleStreamDisconnect.bind(this,\"Dekey RpcProvider\")),this._rpcEngine.push(this._jsonRpcConnection.middleware),this._jsonRpcConnection.events.on(\"notification\",t=>{const{method:r,params:n}=t;\"dekey_accountsChanged\"===r?this._handleAccountsChanged(n):\"dekey_unlockStateChanged\"===r?this._handleUnlockStateChanged(n):\"dekey_chainChanged\"===r?this._handleChainChanged(n):c.EMITTED_NOTIFICATIONS.includes(r)?this.emit(\"message\",{type:r,data:n}):\"DEKEY_STREAM_FAILURE\"===r&&e.destroy(new Error(u.default.errors.permanentlyDisconnected()))})}async _initializeStateAsync(){let e;try{e=await this.request({method:\"dekey_getProviderState\"})}catch(e){this._log.error(\"Dekey: Failed to get initial state. Please report this bug.\",e)}this._initializeState(e)}_handleStreamDisconnect(e,t){let r=\\`Dekey: Lost connection to \"\\${e}\".\\`;(null==t?void 0:t.stack)&&(r+=\"\\\\n\"+t.stack),this._log.warn(r),this.listenerCount(\"error\")>0&&this.emit(\"error\",r),this._handleDisconnect(!1,t?t.message:void 0)}_handleChainChanged({chainId:e,networkVersion:t}={}){c.isValidChainId(e)&&c.isValidNetworkVersion(t)?\"loading\"===t?this._handleDisconnect(!0):super._handleChainChanged({chainId:e}):this._log.error(u.default.errors.invalidNetworkParams(),{chainId:e,networkVersion:t})}}t.AbstractStreamProvider=f;t.StreamProvider=class extends f{async initialize(){return this._initializeStateAsync()}}},function(e,t,r){var n=r(65);function i(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function o(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||\"Function wrapped with \\`once\\`\";return t.onceError=r+\" shouldn't be called more than once\",t.called=!1,t}e.exports=n(i),e.exports.strict=n(o),i.proto=i((function(){Object.defineProperty(Function.prototype,\"once\",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,\"onceStrict\",{value:function(){return o(this)},configurable:!0})}))},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.BaseProvider=void 0;const i=n(r(12)),o=r(9),s=n(r(40)),a=r(22),u=n(r(10)),c=r(5);class l extends i.default{constructor({logger:e=console,maxEventListeners:t=100,rpcMiddleware:r=[]}={}){super(),this._log=e,this.setMaxListeners(t),this._state=Object.assign({},l._defaultState),this.selectedAddress=null,this.chainId=null,this._handleAccountsChanged=this._handleAccountsChanged.bind(this),this._handleConnect=this._handleConnect.bind(this),this._handleChainChanged=this._handleChainChanged.bind(this),this._handleDisconnect=this._handleDisconnect.bind(this),this._handleUnlockStateChanged=this._handleUnlockStateChanged.bind(this),this._rpcRequest=this._rpcRequest.bind(this),this.request=this.request.bind(this);const n=new a.JsonRpcEngine;r.forEach(e=>n.push(e)),this._rpcEngine=n}isConnected(){return this._state.isConnected}async request(e){if(!e||\"object\"!=typeof e||Array.isArray(e))throw o.ethErrors.rpc.invalidRequest({message:u.default.errors.invalidRequestArgs(),data:e});const{method:t,params:r}=e;if(\"string\"!=typeof t||0===t.length)throw o.ethErrors.rpc.invalidRequest({message:u.default.errors.invalidRequestMethod(),data:e});if(void 0!==r&&!Array.isArray(r)&&(\"object\"!=typeof r||null===r))throw o.ethErrors.rpc.invalidRequest({message:u.default.errors.invalidRequestParams(),data:e});return new Promise((e,n)=>{this._rpcRequest({method:t,params:r},c.getRpcPromiseCallback(e,n))})}_initializeState(e){if(!0===this._state.initialized)throw new Error(\"Provider already initialized.\");if(e){const{accounts:t,chainId:r,isUnlocked:n,networkVersion:i}=e;this._handleConnect(r),this._handleChainChanged({chainId:r,networkVersion:i}),this._handleUnlockStateChanged({accounts:t,isUnlocked:n}),this._handleAccountsChanged(t)}this._state.initialized=!0,this.emit(\"_initialized\")}_rpcRequest(e,t){let r=t;return Array.isArray(e)||(e.jsonrpc||(e.jsonrpc=\"2.0\"),\"eth_accounts\"!==e.method&&\"eth_requestAccounts\"!==e.method||(r=(r,n)=>{this._handleAccountsChanged(n.result||[],\"eth_accounts\"===e.method),t(r,n)})),this._rpcEngine.handle(e,r)}_handleConnect(e){this._state.isConnected||(this._state.isConnected=!0,this.emit(\"connect\",{chainId:e}),this._log.debug(u.default.info.connected(e)))}_handleDisconnect(e,t){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!e){let r;this._state.isConnected=!1,e?(r=new o.EthereumRpcError(1013,t||u.default.errors.disconnected()),this._log.debug(r)):(r=new o.EthereumRpcError(1011,t||u.default.errors.permanentlyDisconnected()),this._log.error(r),this.chainId=null,this._state.accounts=null,this.selectedAddress=null,this._state.isUnlocked=!1,this._state.isPermanentlyDisconnected=!0),this.emit(\"disconnect\",r)}}_handleChainChanged({chainId:e}={}){c.isValidChainId(e)?(this._handleConnect(e),this.chainId=e,this._state.initialized&&this.emit(\"chainChanged\",this.chainId)):this._log.error(u.default.errors.invalidNetworkParams(),{chainId:e})}_handleAccountsChanged(e,t=!1){let r=e;Array.isArray(e)||(this._log.error(\"Dekey: Received invalid accounts parameter. Please report this bug.\",e),r=[]);for(const t of e)if(\"string\"!=typeof t){this._log.error(\"Dekey: Received non-string account. Please report this bug.\",e),r=[];break}s.default(this._state.accounts,r)||(t&&null!==this._state.accounts&&this._log.error(\"Dekey: 'eth_accounts' unexpectedly updated accounts. Please report this bug.\",r),this._state.accounts=r,this.selectedAddress!==r[0]&&(this.selectedAddress=r[0]||null),this._state.initialized&&this.emit(\"accountsChanged\",r))}_handleUnlockStateChanged({accounts:e,isUnlocked:t}={}){\"boolean\"==typeof t?t!==this._state.isUnlocked&&(this._state.isUnlocked=t,this._handleAccountsChanged(e||[])):this._log.error(\"Dekey: Received invalid isUnlocked parameter. Please report this bug.\")}}t.BaseProvider=l,l._defaultState={accounts:null,isConnected:!1,isUnlocked:!1,initialized:!1,isPermanentlyDisconnected:!1}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.serializeError=t.isValidCode=t.getMessageFromCode=t.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const n=r(14),i=r(13),o=n.errorCodes.rpc.internal,s={code:o,message:a(o)};function a(e,r=\"Unspecified error message. This is a bug, please report it.\"){if(Number.isInteger(e)){const r=e.toString();if(f(n.errorValues,r))return n.errorValues[r].message;if(c(e))return t.JSON_RPC_SERVER_ERROR_MESSAGE}return r}function u(e){if(!Number.isInteger(e))return!1;const t=e.toString();return!!n.errorValues[t]||!!c(e)}function c(e){return e>=-32099&&e<=-32e3}function l(e){return e&&\"object\"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function f(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.JSON_RPC_SERVER_ERROR_MESSAGE=\"Unspecified server error.\",t.getMessageFromCode=a,t.isValidCode=u,t.serializeError=function(e,{fallbackError:t=s,shouldIncludeStack:r=!1}={}){var n,o;if(!t||!Number.isInteger(t.code)||\"string\"!=typeof t.message)throw new Error(\"Must provide fallback error with integer number code and string message.\");if(e instanceof i.EthereumRpcError)return e.serialize();const c={};if(e&&\"object\"==typeof e&&!Array.isArray(e)&&f(e,\"code\")&&u(e.code)){const t=e;c.code=t.code,t.message&&\"string\"==typeof t.message?(c.message=t.message,f(t,\"data\")&&(c.data=t.data)):(c.message=a(c.code),c.data={originalError:l(e)})}else{c.code=t.code;const r=null===(n=e)||void 0===n?void 0:n.message;c.message=r&&\"string\"==typeof r?r:t.message,c.data={originalError:l(e)}}const d=null===(o=e)||void 0===o?void 0:o.stack;return r&&e&&d&&\"string\"==typeof d&&(c.stack=d),c}},function(e,t,r){\"use strict\";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)\"default\"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,\"__esModule\",{value:!0}),i(r(41),t),i(r(42),t),i(r(43),t),i(r(23),t),i(r(24),t),i(r(44),t)},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.getUniqueId=void 0;let n=Math.floor(4294967295*Math.random());t.getUniqueId=function(){return n=(n+1)%4294967295,n}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.JsonRpcEngine=void 0;const i=n(r(12)),o=r(9);class s extends i.default{constructor(){super(),this._middleware=[]}push(e){this._middleware.push(e)}handle(e,t){if(t&&\"function\"!=typeof t)throw new Error('\"callback\" must be a function if provided.');return Array.isArray(e)?t?this._handleBatch(e,t):this._handleBatch(e):t?this._handle(e,t):this._promiseHandle(e)}asMiddleware(){return async(e,t,r,n)=>{try{const[i,o,a]=await s._runAllMiddleware(e,t,this._middleware);return o?(await s._runReturnHandlers(a),n(i)):r(async e=>{try{await s._runReturnHandlers(a)}catch(t){return e(t)}return e()})}catch(e){return n(e)}}}async _handleBatch(e,t){try{const r=await Promise.all(e.map(this._promiseHandle.bind(this)));return t?t(null,r):r}catch(e){if(t)return t(e);throw e}}_promiseHandle(e){return new Promise(t=>{this._handle(e,(e,r)=>{t(r)})})}async _handle(e,t){if(!e||Array.isArray(e)||\"object\"!=typeof e){const r=new o.EthereumRpcError(o.errorCodes.rpc.invalidRequest,\"Requests must be plain objects. Received: \"+typeof e,{request:e});return t(r,{id:void 0,jsonrpc:\"2.0\",error:r})}if(\"string\"!=typeof e.method){const r=new o.EthereumRpcError(o.errorCodes.rpc.invalidRequest,\"Must specify a string method. Received: \"+typeof e.method,{request:e});return t(r,{id:e.id,jsonrpc:\"2.0\",error:r})}const r=Object.assign({},e),n={id:r.id,jsonrpc:r.jsonrpc};let i=null;try{await this._processRequest(r,n)}catch(e){i=e}return i&&(delete n.result,n.error||(n.error=o.serializeError(i))),t(i,n)}async _processRequest(e,t){const[r,n,i]=await s._runAllMiddleware(e,t,this._middleware);if(s._checkForCompletion(e,t,n),await s._runReturnHandlers(i),r)throw r}static async _runAllMiddleware(e,t,r){const n=[];let i=null,o=!1;for(const a of r)if([i,o]=await s._runMiddleware(e,t,a,n),o)break;return[i,o,n.reverse()]}static _runMiddleware(e,t,r,n){return new Promise(i=>{const s=e=>{const r=e||t.error;r&&(t.error=o.serializeError(r)),i([r,!0])},u=r=>{t.error?s(t.error):(r&&(\"function\"!=typeof r&&s(new o.EthereumRpcError(o.errorCodes.rpc.internal,\\`JsonRpcEngine: \"next\" return handlers must be functions. Received \"\\${typeof r}\" for request:\\\\n\\${a(e)}\\`,{request:e})),n.push(r)),i([null,!1]))};try{r(e,t,u,s)}catch(e){s(e)}})}static async _runReturnHandlers(e){for(const t of e)await new Promise((e,r)=>{t(t=>t?r(t):e())})}static _checkForCompletion(e,t,r){if(!(\"result\"in t)&&!(\"error\"in t))throw new o.EthereumRpcError(o.errorCodes.rpc.internal,\"JsonRpcEngine: Response has no error or result for request:\\\\n\"+a(e),{request:e});if(!r)throw new o.EthereumRpcError(o.errorCodes.rpc.internal,\"JsonRpcEngine: Nothing ended request:\\\\n\"+a(e),{request:e})}}function a(e){return JSON.stringify(e,null,2)}t.JsonRpcEngine=s},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return\"[object Array]\"==r.call(e)}},function(e,t,r){\"use strict\";(function(t,n){var i=r(11);e.exports=v;var o,s=r(25);v.ReadableState=b;r(8).EventEmitter;var a=function(e,t){return e.listeners(t).length},u=r(27),c=r(15).Buffer,l=(void 0!==t?t:\"undefined\"!=typeof window?window:\"undefined\"!=typeof self?self:{}).Uint8Array||function(){};var f=Object.create(r(7));f.inherits=r(4);var d=r(50),h=void 0;h=d&&d.debuglog?d.debuglog(\"stream\"):function(){};var p,g=r(51),m=r(28);f.inherits(v,u);var y=[\"error\",\"close\",\"destroy\",\"pause\",\"resume\"];function b(e,t){e=e||{};var n=t instanceof(o=o||r(2));this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var i=e.highWaterMark,s=e.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(s||0===s)?s:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new g,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||\"utf8\",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(p||(p=r(29).StringDecoder),this.decoder=new p(e.encoding),this.encoding=e.encoding)}function v(e){if(o=o||r(2),!(this instanceof v))return new v(e);this._readableState=new b(e,this),this.readable=!0,e&&(\"function\"==typeof e.read&&(this._read=e.read),\"function\"==typeof e.destroy&&(this._destroy=e.destroy)),u.call(this)}function w(e,t,r,n,i){var o,s=e._readableState;null===t?(s.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,S(e)}(e,s)):(i||(o=function(e,t){var r;n=t,c.isBuffer(n)||n instanceof l||\"string\"==typeof t||void 0===t||e.objectMode||(r=new TypeError(\"Invalid non-string/buffer chunk\"));var n;return r}(s,t)),o?e.emit(\"error\",o):s.objectMode||t&&t.length>0?(\"string\"==typeof t||s.objectMode||Object.getPrototypeOf(t)===c.prototype||(t=function(e){return c.from(e)}(t)),n?s.endEmitted?e.emit(\"error\",new Error(\"stream.unshift() after end event\")):_(e,s,t,!0):s.ended?e.emit(\"error\",new Error(\"stream.push() after EOF\")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?_(e,s,t,!1):O(e,s)):_(e,s,t,!1))):n||(s.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(s)}function _(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit(\"data\",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&S(e)),O(e,t)}Object.defineProperty(v.prototype,\"destroyed\",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),v.prototype.destroy=m.destroy,v.prototype._undestroy=m.undestroy,v.prototype._destroy=function(e,t){this.push(null),t(e)},v.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:\"string\"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=c.from(e,t),t=\"\"),r=!0),w(this,e,t,!1,r)},v.prototype.unshift=function(e){return w(this,e,null,!0,!1)},v.prototype.isPaused=function(){return!1===this._readableState.flowing},v.prototype.setEncoding=function(e){return p||(p=r(29).StringDecoder),this._readableState.decoder=new p(e),this._readableState.encoding=e,this};function E(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=8388608?e=8388608:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function S(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(h(\"emitReadable\",t.flowing),t.emittedReadable=!0,t.sync?i.nextTick(P,e):P(e))}function P(e){h(\"emit readable\"),e.emit(\"readable\"),j(e)}function O(e,t){t.readingMore||(t.readingMore=!0,i.nextTick(M,e,t))}function M(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(h(\"maybeReadMore read 0\"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function R(e){h(\"readable nexttick read 0\"),e.read(0)}function k(e,t){t.reading||(h(\"resume read 0\"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit(\"resume\"),j(e),t.flowing&&!t.reading&&e.read(0)}function j(e){var t=e._readableState;for(h(\"flow\",t.flowing);t.flowing&&null!==e.read(););}function x(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(\"\"):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;e-=i.length;for(;r=r.next;){var o=r.data,s=e>o.length?o.length:e;if(s===o.length?i+=o:i+=o.slice(0,e),0===(e-=s)){s===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(s));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=c.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;for(;n=n.next;){var o=n.data,s=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,s),0===(e-=s)){s===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(s));break}++i}return t.length-=i,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function A(e){var t=e._readableState;if(t.length>0)throw new Error('\"endReadable()\" called on non-empty stream');t.endEmitted||(t.ended=!0,i.nextTick(C,t,e))}function C(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit(\"end\"))}function T(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}v.prototype.read=function(e){h(\"read\",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return h(\"read: emitReadable\",t.length,t.ended),0===t.length&&t.ended?A(this):S(this),null;if(0===(e=E(e,t))&&t.ended)return 0===t.length&&A(this),null;var n,i=t.needReadable;return h(\"need readable\",i),(0===t.length||t.length-e<t.highWaterMark)&&h(\"length less than watermark\",i=!0),t.ended||t.reading?h(\"reading or ended\",i=!1):i&&(h(\"do read\"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=E(r,t))),null===(n=e>0?x(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&A(this)),null!==n&&this.emit(\"data\",n),n},v.prototype._read=function(e){this.emit(\"error\",new Error(\"_read() is not implemented\"))},v.prototype.pipe=function(e,t){var r=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=e;break;case 1:o.pipes=[o.pipes,e];break;default:o.pipes.push(e)}o.pipesCount+=1,h(\"pipe count=%d opts=%j\",o.pipesCount,t);var u=(!t||!1!==t.end)&&e!==n.stdout&&e!==n.stderr?l:v;function c(t,n){h(\"onunpipe\"),t===r&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,h(\"cleanup\"),e.removeListener(\"close\",y),e.removeListener(\"finish\",b),e.removeListener(\"drain\",f),e.removeListener(\"error\",m),e.removeListener(\"unpipe\",c),r.removeListener(\"end\",l),r.removeListener(\"end\",v),r.removeListener(\"data\",g),d=!0,!o.awaitDrain||e._writableState&&!e._writableState.needDrain||f())}function l(){h(\"onend\"),e.end()}o.endEmitted?i.nextTick(u):r.once(\"end\",u),e.on(\"unpipe\",c);var f=function(e){return function(){var t=e._readableState;h(\"pipeOnDrain\",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,\"data\")&&(t.flowing=!0,j(e))}}(r);e.on(\"drain\",f);var d=!1;var p=!1;function g(t){h(\"ondata\"),p=!1,!1!==e.write(t)||p||((1===o.pipesCount&&o.pipes===e||o.pipesCount>1&&-1!==T(o.pipes,e))&&!d&&(h(\"false write response, pause\",o.awaitDrain),o.awaitDrain++,p=!0),r.pause())}function m(t){h(\"onerror\",t),v(),e.removeListener(\"error\",m),0===a(e,\"error\")&&e.emit(\"error\",t)}function y(){e.removeListener(\"finish\",b),v()}function b(){h(\"onfinish\"),e.removeListener(\"close\",y),v()}function v(){h(\"unpipe\"),r.unpipe(e)}return r.on(\"data\",g),function(e,t,r){if(\"function\"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?s(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,\"error\",m),e.once(\"close\",y),e.once(\"finish\",b),e.emit(\"pipe\",r),o.flowing||(h(\"pipe resume\"),r.resume()),e},v.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit(\"unpipe\",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit(\"unpipe\",this,{hasUnpiped:!1});return this}var s=T(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit(\"unpipe\",this,r)),this},v.prototype.on=function(e,t){var r=u.prototype.on.call(this,e,t);if(\"data\"===e)!1!==this._readableState.flowing&&this.resume();else if(\"readable\"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&S(this):i.nextTick(R,this))}return r},v.prototype.addListener=v.prototype.on,v.prototype.resume=function(){var e=this._readableState;return e.flowing||(h(\"resume\"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,i.nextTick(k,e,t))}(this,e)),this},v.prototype.pause=function(){return h(\"call pause flowing=%j\",this._readableState.flowing),!1!==this._readableState.flowing&&(h(\"pause\"),this._readableState.flowing=!1,this.emit(\"pause\")),this},v.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on(\"end\",(function(){if(h(\"wrapped end\"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on(\"data\",(function(i){(h(\"wrapped data\"),r.decoder&&(i=r.decoder.write(i)),r.objectMode&&null==i)||(r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause()))})),e)void 0===this[i]&&\"function\"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<y.length;o++)e.on(y[o],this.emit.bind(this,y[o]));return this._read=function(t){h(\"wrapped _read\",t),n&&(n=!1,e.resume())},this},Object.defineProperty(v.prototype,\"readableHighWaterMark\",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),v._fromList=x}).call(this,r(3),r(1))},function(e,t,r){e.exports=r(8).EventEmitter},function(e,t,r){\"use strict\";var n=r(11);function i(e,t){e.emit(\"error\",t)}e.exports={destroy:function(e,t){var r=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,n.nextTick(i,this,e)):n.nextTick(i,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?r._writableState?r._writableState.errorEmitted||(r._writableState.errorEmitted=!0,n.nextTick(i,r,e)):n.nextTick(i,r,e):t&&t(e)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(e,t,r){\"use strict\";var n=r(56).Buffer,i=n.isEncoding||function(e){switch((e=\"\"+e)&&e.toLowerCase()){case\"hex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":case\"raw\":return!0;default:return!1}};function o(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return\"utf8\";for(var t;;)switch(e){case\"utf8\":case\"utf-8\":return\"utf8\";case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return\"utf16le\";case\"latin1\":case\"binary\":return\"latin1\";case\"base64\":case\"ascii\":case\"hex\":return e;default:if(t)return;e=(\"\"+e).toLowerCase(),t=!0}}(e);if(\"string\"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error(\"Unknown encoding: \"+e);return t||e}(e),this.encoding){case\"utf16le\":this.text=u,this.end=c,t=4;break;case\"utf8\":this.fillLast=a,t=4;break;case\"base64\":this.text=l,this.end=f,t=3;break;default:return this.write=d,void(this.end=h)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function a(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,\"�\";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,\"�\";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,\"�\"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function u(e,t){if((e.length-t)%2==0){var r=e.toString(\"utf16le\",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString(\"utf16le\",t,e.length-1)}function c(e){var t=e&&e.length?this.write(e):\"\";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString(\"utf16le\",0,r)}return t}function l(e,t){var r=(e.length-t)%3;return 0===r?e.toString(\"base64\",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString(\"base64\",t,e.length-r))}function f(e){var t=e&&e.length?this.write(e):\"\";return this.lastNeed?t+this.lastChar.toString(\"base64\",0,3-this.lastNeed):t}function d(e){return e.toString(this.encoding)}function h(e){return e&&e.length?this.write(e):\"\"}t.StringDecoder=o,o.prototype.write=function(e){if(0===e.length)return\"\";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return\"\";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||\"\"},o.prototype.end=function(e){var t=e&&e.length?this.write(e):\"\";return this.lastNeed?t+\"�\":t},o.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if((i=s(t[n]))>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if((i=s(t[n]))>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString(\"utf8\",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString(\"utf8\",t,n)},o.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},function(e,t,r){\"use strict\";e.exports=s;var n=r(2),i=Object.create(r(7));function o(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit(\"error\",new Error(\"write callback called multiple times\"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function s(e){if(!(this instanceof s))return new s(e);n.call(this,e),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&(\"function\"==typeof e.transform&&(this._transform=e.transform),\"function\"==typeof e.flush&&(this._flush=e.flush)),this.on(\"prefinish\",a)}function a(){var e=this;\"function\"==typeof this._flush?this._flush((function(t,r){u(e,t,r)})):u(this,null,null)}function u(e,t,r){if(t)return e.emit(\"error\",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error(\"Calling transform done when ws.length != 0\");if(e._transformState.transforming)throw new Error(\"Calling transform done when still transforming\");return e.push(null)}i.inherits=r(4),i.inherits(s,n),s.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},s.prototype._transform=function(e,t,r){throw new Error(\"_transform() is not implemented\")},s.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},s.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},s.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,(function(e){t(e),r.emit(\"close\")}))}},function(e,t,r){\"use strict\";const n=r(64);e.exports=n.ObjectMultiplex},function(e,t,r){(function(t){var n=r(19),i=function(){},o=function(e,r,s){if(\"function\"==typeof r)return o(e,null,r);r||(r={}),s=n(s||i);var a=e._writableState,u=e._readableState,c=r.readable||!1!==r.readable&&e.readable,l=r.writable||!1!==r.writable&&e.writable,f=!1,d=function(){e.writable||h()},h=function(){l=!1,c||s.call(e)},p=function(){c=!1,l||s.call(e)},g=function(t){s.call(e,t?new Error(\"exited with error code: \"+t):null)},m=function(t){s.call(e,t)},y=function(){t.nextTick(b)},b=function(){if(!f)return(!c||u&&u.ended&&!u.destroyed)&&(!l||a&&a.ended&&!a.destroyed)?void 0:s.call(e,new Error(\"premature close\"))},v=function(){e.req.on(\"finish\",h)};return!function(e){return e.setHeader&&\"function\"==typeof e.abort}(e)?l&&!a&&(e.on(\"end\",d),e.on(\"close\",d)):(e.on(\"complete\",h),e.on(\"abort\",y),e.req?v():e.on(\"request\",v)),function(e){return e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length}(e)&&e.on(\"exit\",g),e.on(\"end\",p),e.on(\"finish\",h),!1!==r.error&&e.on(\"error\",m),e.on(\"close\",y),function(){f=!0,e.removeListener(\"complete\",h),e.removeListener(\"abort\",y),e.removeListener(\"request\",v),e.req&&e.req.removeListener(\"finish\",h),e.removeListener(\"end\",d),e.removeListener(\"close\",d),e.removeListener(\"finish\",h),e.removeListener(\"exit\",g),e.removeListener(\"end\",p),e.removeListener(\"error\",m),e.removeListener(\"close\",y)}};e.exports=o}).call(this,r(1))},function(e,t,r){(function(t){var n=r(19),i=r(32),o=r(71),s=function(){},a=/^v?\\\\.0/.test(t.version),u=function(e){return\"function\"==typeof e},c=function(e,t,r,c){c=n(c);var l=!1;e.on(\"close\",(function(){l=!0})),i(e,{readable:t,writable:r},(function(e){if(e)return c(e);l=!0,c()}));var f=!1;return function(t){if(!l&&!f)return f=!0,function(e){return!!a&&(!!o&&((e instanceof(o.ReadStream||s)||e instanceof(o.WriteStream||s))&&u(e.close)))}(e)?e.close(s):function(e){return e.setHeader&&u(e.abort)}(e)?e.abort():u(e.destroy)?e.destroy():void c(t||new Error(\"stream was destroyed\"))}},l=function(e){e()},f=function(e,t){return e.pipe(t)};e.exports=function(){var e,t=Array.prototype.slice.call(arguments),r=u(t[t.length-1]||s)&&t.pop()||s;if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Error(\"pump requires two streams per minimum\");var n=t.map((function(i,o){var s=o<t.length-1;return c(i,s,o>0,(function(t){e||(e=t),t&&n.forEach(l),s||(n.forEach(l),r(e))}))}));return t.reduce(f)}}).call(this,r(1))},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.shimWeb3=void 0,t.shimWeb3=function(e,t=console){let r=!1,n=!1;if(!window.web3){const i=\"__isMetaMaskShim__\";let o={currentProvider:e};Object.defineProperty(o,i,{value:!0,enumerable:!0,configurable:!1,writable:!1}),o=new Proxy(o,{get:(o,s,...a)=>(\"currentProvider\"!==s||r?\"currentProvider\"===s||s===i||n||(n=!0,t.error(\"Dekey no longer injects web3.\"),e.request({method:\"metamask_logWeb3ShimUsage\"}).catch(e=>{t.debug(\"Dekey: Failed to log web3 shim usage.\",e)})):(r=!0,t.warn(\"You are accessing the Dekey window.web3.currentProvider shim. This property is deprecated; use window.ethereum instead.\")),Reflect.get(o,s,...a)),set:(...e)=>(t.warn(\"You are accessing the Dekey window.web3 shim. This object is deprecated; use window.ethereum instead.\"),Reflect.set(...e))}),Object.defineProperty(window,\"web3\",{value:o,enumerable:!1,configurable:!0,writable:!0})}}},function(e,t,r){(function(e){var n=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++)r[t[n]]=Object.getOwnPropertyDescriptor(e,t[n]);return r},i=/%[sdj%]/g;t.format=function(e){if(!y(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(a(arguments[r]));return t.join(\" \")}r=1;for(var n=arguments,o=n.length,s=String(e).replace(i,(function(e){if(\"%%\"===e)return\"%\";if(r>=o)return e;switch(e){case\"%s\":return String(n[r++]);case\"%d\":return Number(n[r++]);case\"%j\":try{return JSON.stringify(n[r++])}catch(e){return\"[Circular]\"}default:return e}})),u=n[r];r<o;u=n[++r])g(u)||!w(u)?s+=\" \"+u:s+=\" \"+a(u);return s},t.deprecate=function(r,n){if(void 0!==e&&!0===e.noDeprecation)return r;if(void 0===e)return function(){return t.deprecate(r,n).apply(this,arguments)};var i=!1;return function(){if(!i){if(e.throwDeprecation)throw new Error(n);e.traceDeprecation?console.trace(n):console.error(n),i=!0}return r.apply(this,arguments)}};var o,s={};function a(e,r){var n={seen:[],stylize:c};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),p(r)?n.showHidden=r:r&&t._extend(n,r),b(n.showHidden)&&(n.showHidden=!1),b(n.depth)&&(n.depth=2),b(n.colors)&&(n.colors=!1),b(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=u),l(n,e,n.depth)}function u(e,t){var r=a.styles[t];return r?\"\\x1B[\"+a.colors[r][0]+\"m\"+e+\"\\x1B[\"+a.colors[r][1]+\"m\":e}function c(e,t){return e}function l(e,r,n){if(e.customInspect&&r&&S(r.inspect)&&r.inspect!==t.inspect&&(!r.constructor||r.constructor.prototype!==r)){var i=r.inspect(n,e);return y(i)||(i=l(e,i,n)),i}var o=function(e,t){if(b(t))return e.stylize(\"undefined\",\"undefined\");if(y(t)){var r=\"'\"+JSON.stringify(t).replace(/^\"|\"$/g,\"\").replace(/'/g,\"\\\\\\\\'\").replace(/\\\\\\\\\"/g,'\"')+\"'\";return e.stylize(r,\"string\")}if(m(t))return e.stylize(\"\"+t,\"number\");if(p(t))return e.stylize(\"\"+t,\"boolean\");if(g(t))return e.stylize(\"null\",\"null\")}(e,r);if(o)return o;var s=Object.keys(r),a=function(e){var t={};return e.forEach((function(e,r){t[e]=!0})),t}(s);if(e.showHidden&&(s=Object.getOwnPropertyNames(r)),E(r)&&(s.indexOf(\"message\")>=0||s.indexOf(\"description\")>=0))return f(r);if(0===s.length){if(S(r)){var u=r.name?\": \"+r.name:\"\";return e.stylize(\"[Function\"+u+\"]\",\"special\")}if(v(r))return e.stylize(RegExp.prototype.toString.call(r),\"regexp\");if(_(r))return e.stylize(Date.prototype.toString.call(r),\"date\");if(E(r))return f(r)}var c,w=\"\",P=!1,O=[\"{\",\"}\"];(h(r)&&(P=!0,O=[\"[\",\"]\"]),S(r))&&(w=\" [Function\"+(r.name?\": \"+r.name:\"\")+\"]\");return v(r)&&(w=\" \"+RegExp.prototype.toString.call(r)),_(r)&&(w=\" \"+Date.prototype.toUTCString.call(r)),E(r)&&(w=\" \"+f(r)),0!==s.length||P&&0!=r.length?n<0?v(r)?e.stylize(RegExp.prototype.toString.call(r),\"regexp\"):e.stylize(\"[Object]\",\"special\"):(e.seen.push(r),c=P?function(e,t,r,n,i){for(var o=[],s=0,a=t.length;s<a;++s)k(t,String(s))?o.push(d(e,t,r,n,String(s),!0)):o.push(\"\");return i.forEach((function(i){i.match(/^\\\\d+$/)||o.push(d(e,t,r,n,i,!0))})),o}(e,r,n,a,s):s.map((function(t){return d(e,r,n,a,t,P)})),e.seen.pop(),function(e,t,r){if(e.reduce((function(e,t){return t.indexOf(\"\\\\n\")>=0&&0,e+t.replace(/\\\\u001b\\\\[\\\\d\\\\d?m/g,\"\").length+1}),0)>60)return r[0]+(\"\"===t?\"\":t+\"\\\\n \")+\" \"+e.join(\",\\\\n  \")+\" \"+r[1];return r[0]+t+\" \"+e.join(\", \")+\" \"+r[1]}(c,w,O)):O[0]+w+O[1]}function f(e){return\"[\"+Error.prototype.toString.call(e)+\"]\"}function d(e,t,r,n,i,o){var s,a,u;if((u=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?a=u.set?e.stylize(\"[Getter/Setter]\",\"special\"):e.stylize(\"[Getter]\",\"special\"):u.set&&(a=e.stylize(\"[Setter]\",\"special\")),k(n,i)||(s=\"[\"+i+\"]\"),a||(e.seen.indexOf(u.value)<0?(a=g(r)?l(e,u.value,null):l(e,u.value,r-1)).indexOf(\"\\\\n\")>-1&&(a=o?a.split(\"\\\\n\").map((function(e){return\"  \"+e})).join(\"\\\\n\").substr(2):\"\\\\n\"+a.split(\"\\\\n\").map((function(e){return\"   \"+e})).join(\"\\\\n\")):a=e.stylize(\"[Circular]\",\"special\")),b(s)){if(o&&i.match(/^\\\\d+$/))return a;(s=JSON.stringify(\"\"+i)).match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,\"name\")):(s=s.replace(/'/g,\"\\\\\\\\'\").replace(/\\\\\\\\\"/g,'\"').replace(/(^\"|\"$)/g,\"'\"),s=e.stylize(s,\"string\"))}return s+\": \"+a}function h(e){return Array.isArray(e)}function p(e){return\"boolean\"==typeof e}function g(e){return null===e}function m(e){return\"number\"==typeof e}function y(e){return\"string\"==typeof e}function b(e){return void 0===e}function v(e){return w(e)&&\"[object RegExp]\"===P(e)}function w(e){return\"object\"==typeof e&&null!==e}function _(e){return w(e)&&\"[object Date]\"===P(e)}function E(e){return w(e)&&(\"[object Error]\"===P(e)||e instanceof Error)}function S(e){return\"function\"==typeof e}function P(e){return Object.prototype.toString.call(e)}function O(e){return e<10?\"0\"+e.toString(10):e.toString(10)}t.debuglog=function(r){if(b(o)&&(o=e.env.NODE_DEBUG||\"\"),r=r.toUpperCase(),!s[r])if(new RegExp(\"\\\\\\\\b\"+r+\"\\\\\\\\b\",\"i\").test(o)){var n=e.pid;s[r]=function(){var e=t.format.apply(t,arguments);console.error(\"%s %d: %s\",r,n,e)}}else s[r]=function(){};return s[r]},t.inspect=a,a.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},a.styles={special:\"cyan\",number:\"yellow\",boolean:\"yellow\",undefined:\"grey\",null:\"bold\",string:\"green\",date:\"magenta\",regexp:\"red\"},t.isArray=h,t.isBoolean=p,t.isNull=g,t.isNullOrUndefined=function(e){return null==e},t.isNumber=m,t.isString=y,t.isSymbol=function(e){return\"symbol\"==typeof e},t.isUndefined=b,t.isRegExp=v,t.isObject=w,t.isDate=_,t.isError=E,t.isFunction=S,t.isPrimitive=function(e){return null===e||\"boolean\"==typeof e||\"number\"==typeof e||\"string\"==typeof e||\"symbol\"==typeof e||void 0===e},t.isBuffer=r(75);var M=[\"Jan\",\"Feb\",\"Mar\",\"Apr\",\"May\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"];function R(){var e=new Date,t=[O(e.getHours()),O(e.getMinutes()),O(e.getSeconds())].join(\":\");return[e.getDate(),M[e.getMonth()],t].join(\" \")}function k(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.log=function(){console.log(\"%s - %s\",R(),t.format.apply(t,arguments))},t.inherits=r(76),t._extend=function(e,t){if(!t||!w(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e};var j=\"undefined\"!=typeof Symbol?Symbol(\"util.promisify.custom\"):void 0;function x(e,t){if(!e){var r=new Error(\"Promise was rejected with a falsy value\");r.reason=e,e=r}return t(e)}t.promisify=function(e){if(\"function\"!=typeof e)throw new TypeError('The \"original\" argument must be of type Function');if(j&&e[j]){var t;if(\"function\"!=typeof(t=e[j]))throw new TypeError('The \"util.promisify.custom\" argument must be of type Function');return Object.defineProperty(t,j,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,r,n=new Promise((function(e,n){t=e,r=n})),i=[],o=0;o<arguments.length;o++)i.push(arguments[o]);i.push((function(e,n){e?r(e):t(n)}));try{e.apply(this,i)}catch(e){r(e)}return n}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),j&&Object.defineProperty(t,j,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,n(e))},t.promisify.custom=j,t.callbackify=function(t){if(\"function\"!=typeof t)throw new TypeError('The \"original\" argument must be of type Function');function r(){for(var r=[],n=0;n<arguments.length;n++)r.push(arguments[n]);var i=r.pop();if(\"function\"!=typeof i)throw new TypeError(\"The last argument must be of type Function\");var o=this,s=function(){return i.apply(o,arguments)};t.apply(this,r).then((function(t){e.nextTick(s,null,t)}),(function(t){e.nextTick(x,t,s)}))}return Object.setPrototypeOf(r,Object.getPrototypeOf(t)),Object.defineProperties(r,n(t)),r}}).call(this,r(1))},function(e,t,r){var n=r(37),i=n.initializeProvider,o=n.shimWeb3,s=r(31),a=r(33),u=r(74),c=r(77),l=\"dekey-mobile-inpage\",f=\"dekey-mobile-provider\";function d(e,t){var r=\"DekeyContentscript - lost connection to \".concat(e);t&&(r+=\"\\\\n\".concat(t.stack)),console.log(r)}i({connectionStream:new c({name:l,target:\"dekey-mobile-contentscript\"}),shouldSendMetadata:!1,jsonRpcStreamName:f}),Object.defineProperty(window,\"_dekeySetupProvider\",{value:function(e){!function(e){var t=new c({name:\"dekey-mobile-contentscript\",target:l}),r=new u({name:\"dekey-mobile-contentscript\"},e),n=new s;n.setMaxListeners(25);var i=new s;i.setMaxListeners(25),a(n,t,n,(function(e){return d(\"Dekey Inpage Multiplex\",e)})),a(i,r,i,(function(e){d(\"Dekey Background Multiplex\",e),window.postMessage({target:l,data:{name:f,data:{jsonrpc:\"2.0\",method:\"DEKEY_STREAM_FAILURE\"}}},window.location.origin)})),h=f,p=n,g=i,m=p.createStream(h),y=g.createStream(h),a(m,y,m,(function(e){return d('Dekey muxed traffic for channel \"'.concat(h,'\" failed.'),e)})),o(window.ethereum);var h,p,g,m,y}(new URL(e).origin),delete window._dekeySetupProvider},configurable:!0,enumerable:!1,writable:!1})},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.StreamProvider=t.shimWeb3=t.setGlobalProvider=t.MetaMaskInpageProvider=t.MetaMaskInpageProviderStreamName=t.initializeProvider=t.createExternalExtensionProvider=t.BaseProvider=void 0;const n=r(20);Object.defineProperty(t,\"BaseProvider\",{enumerable:!0,get:function(){return n.BaseProvider}});const i=r(45);Object.defineProperty(t,\"createExternalExtensionProvider\",{enumerable:!0,get:function(){return i.createExternalExtensionProvider}});const o=r(73);Object.defineProperty(t,\"initializeProvider\",{enumerable:!0,get:function(){return o.initializeProvider}}),Object.defineProperty(t,\"setGlobalProvider\",{enumerable:!0,get:function(){return o.setGlobalProvider}});const s=r(17);Object.defineProperty(t,\"MetaMaskInpageProvider\",{enumerable:!0,get:function(){return s.MetaMaskInpageProvider}}),Object.defineProperty(t,\"MetaMaskInpageProviderStreamName\",{enumerable:!0,get:function(){return s.MetaMaskInpageProviderStreamName}});const a=r(34);Object.defineProperty(t,\"shimWeb3\",{enumerable:!0,get:function(){return a.shimWeb3}});const u=r(18);Object.defineProperty(t,\"StreamProvider\",{enumerable:!0,get:function(){return u.StreamProvider}})},function(e,t){e.exports=o,o.default=o,o.stable=u,o.stableStringify=u;var r=[],n=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(e,t,o,a){var u;void 0===a&&(a=i()),function e(t,r,n,i,o,a,u){var c;if(a+=1,\"object\"==typeof t&&null!==t){for(c=0;c<i.length;c++)if(i[c]===t)return void s(\"[Circular]\",t,r,o);if(void 0!==u.depthLimit&&a>u.depthLimit)return void s(\"[...]\",t,r,o);if(void 0!==u.edgesLimit&&n+1>u.edgesLimit)return void s(\"[...]\",t,r,o);if(i.push(t),Array.isArray(t))for(c=0;c<t.length;c++)e(t[c],c,c,i,t,a,u);else{var l=Object.keys(t);for(c=0;c<l.length;c++){var f=l[c];e(t[f],f,c,i,t,a,u)}}i.pop()}}(e,\"\",0,[],void 0,0,a);try{u=0===n.length?JSON.stringify(e,t,o):JSON.stringify(e,c(t),o)}catch(e){return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\")}finally{for(;0!==r.length;){var l=r.pop();4===l.length?Object.defineProperty(l[0],l[1],l[3]):l[0][l[1]]=l[2]}}return u}function s(e,t,i,o){var s=Object.getOwnPropertyDescriptor(o,i);void 0!==s.get?s.configurable?(Object.defineProperty(o,i,{value:e}),r.push([o,i,t,s])):n.push([t,i,e]):(o[i]=e,r.push([o,i,t]))}function a(e,t){return e<t?-1:e>t?1:0}function u(e,t,o,u){void 0===u&&(u=i());var l,f=function e(t,n,i,o,u,c,l){var f;if(c+=1,\"object\"==typeof t&&null!==t){for(f=0;f<o.length;f++)if(o[f]===t)return void s(\"[Circular]\",t,n,u);try{if(\"function\"==typeof t.toJSON)return}catch(e){return}if(void 0!==l.depthLimit&&c>l.depthLimit)return void s(\"[...]\",t,n,u);if(void 0!==l.edgesLimit&&i+1>l.edgesLimit)return void s(\"[...]\",t,n,u);if(o.push(t),Array.isArray(t))for(f=0;f<t.length;f++)e(t[f],f,f,o,t,c,l);else{var d={},h=Object.keys(t).sort(a);for(f=0;f<h.length;f++){var p=h[f];e(t[p],p,f,o,t,c,l),d[p]=t[p]}if(void 0===u)return d;r.push([u,n,t]),u[n]=d}o.pop()}}(e,\"\",0,[],void 0,0,u)||e;try{l=0===n.length?JSON.stringify(f,t,o):JSON.stringify(f,c(t),o)}catch(e){return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\")}finally{for(;0!==r.length;){var d=r.pop();4===d.length?Object.defineProperty(d[0],d[1],d[3]):d[0][d[1]]=d[2]}}return l}function c(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(n.length>0)for(var i=0;i<n.length;i++){var o=n[i];if(o[1]===t&&o[0]===r){r=o[2],n.splice(i,1);break}}return e.call(this,t,r)}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.ethErrors=void 0;const n=r(13),i=r(21),o=r(14);function s(e,t){const[r,o]=u(t);return new n.EthereumRpcError(e,r||i.getMessageFromCode(e),o)}function a(e,t){const[r,o]=u(t);return new n.EthereumProviderError(e,r||i.getMessageFromCode(e),o)}function u(e){if(e){if(\"string\"==typeof e)return[e];if(\"object\"==typeof e&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&\"string\"!=typeof t)throw new Error(\"Must specify string message.\");return[t||void 0,r]}}return[]}t.ethErrors={rpc:{parse:e=>s(o.errorCodes.rpc.parse,e),invalidRequest:e=>s(o.errorCodes.rpc.invalidRequest,e),invalidParams:e=>s(o.errorCodes.rpc.invalidParams,e),methodNotFound:e=>s(o.errorCodes.rpc.methodNotFound,e),internal:e=>s(o.errorCodes.rpc.internal,e),server:e=>{if(!e||\"object\"!=typeof e||Array.isArray(e))throw new Error(\"Ethereum RPC Server errors must provide single object argument.\");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');return s(t,e)},invalidInput:e=>s(o.errorCodes.rpc.invalidInput,e),resourceNotFound:e=>s(o.errorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>s(o.errorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>s(o.errorCodes.rpc.transactionRejected,e),methodNotSupported:e=>s(o.errorCodes.rpc.methodNotSupported,e),limitExceeded:e=>s(o.errorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>a(o.errorCodes.provider.userRejectedRequest,e),unauthorized:e=>a(o.errorCodes.provider.unauthorized,e),unsupportedMethod:e=>a(o.errorCodes.provider.unsupportedMethod,e),disconnected:e=>a(o.errorCodes.provider.disconnected,e),chainDisconnected:e=>a(o.errorCodes.provider.chainDisconnected,e),custom:e=>{if(!e||\"object\"!=typeof e||Array.isArray(e))throw new Error(\"Ethereum Provider custom errors must provide single object argument.\");const{code:t,message:r,data:i}=e;if(!r||\"string\"!=typeof r)throw new Error('\"message\" must be a nonempty string');return new n.EthereumProviderError(t,r,i)}}}},function(e,t,r){\"use strict\";var n=Array.isArray,i=Object.keys,o=Object.prototype.hasOwnProperty;e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&\"object\"==typeof t&&\"object\"==typeof r){var s,a,u,c=n(t),l=n(r);if(c&&l){if((a=t.length)!=r.length)return!1;for(s=a;0!=s--;)if(!e(t[s],r[s]))return!1;return!0}if(c!=l)return!1;var f=t instanceof Date,d=r instanceof Date;if(f!=d)return!1;if(f&&d)return t.getTime()==r.getTime();var h=t instanceof RegExp,p=r instanceof RegExp;if(h!=p)return!1;if(h&&p)return t.toString()==r.toString();var g=i(t);if((a=g.length)!==i(r).length)return!1;for(s=a;0!=s--;)if(!o.call(r,g[s]))return!1;for(s=a;0!=s--;)if(!e(t[u=g[s]],r[u]))return!1;return!0}return t!=t&&r!=r}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.createIdRemapMiddleware=void 0;const n=r(23);t.createIdRemapMiddleware=function(){return(e,t,r,i)=>{const o=e.id,s=n.getUniqueId();e.id=s,t.id=s,r(r=>{e.id=o,t.id=o,r()})}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.createAsyncMiddleware=void 0,t.createAsyncMiddleware=function(e){return async(t,r,n,i)=>{let o;const s=new Promise(e=>{o=e});let a=null,u=!1;const c=async()=>{u=!0,n(e=>{a=e,o()}),await s};try{await e(t,r,c),u?(await s,a(null)):i(null)}catch(e){a?a(e):i(e)}}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.createScaffoldMiddleware=void 0,t.createScaffoldMiddleware=function(e){return(t,r,n,i)=>{const o=e[t.method];return void 0===o?n():\"function\"==typeof o?o(t,r,n,i):(r.result=o,i())}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.mergeMiddleware=void 0;const n=r(24);t.mergeMiddleware=function(e){const t=new n.JsonRpcEngine;return e.forEach(e=>t.push(e)),t.asMiddleware()}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.createExternalExtensionProvider=void 0;const i=n(r(46)),o=r(62),s=r(17),a=r(18),u=r(5),c=n(r(72)),l=o.detect();t.createExternalExtensionProvider=function(){let e;try{const t=function(){switch(null==l?void 0:l.name){case\"chrome\":return c.default.CHROME_ID;case\"firefox\":return c.default.FIREFOX_ID;default:return c.default.CHROME_ID}}(),r=chrome.runtime.connect(t),n=new i.default(r);e=new a.StreamProvider(n,{jsonRpcStreamName:s.MetaMaskInpageProviderStreamName,logger:console,rpcMiddleware:u.getDefaultExternalMiddleware(console)}),e.initialize()}catch(e){throw console.dir(\"MetaMask connect error.\",e),e}return e}},function(e,t,r){\"use strict\";(function(e){Object.defineProperty(t,\"__esModule\",{value:!0});const n=r(49);class i extends n.Duplex{constructor(e){super({objectMode:!0}),this._port=e,this._port.onMessage.addListener(e=>this._onMessage(e)),this._port.onDisconnect.addListener(()=>this._onDisconnect()),this._log=()=>null}_onMessage(t){if(e.isBuffer(t)){const r=e.from(t);this._log(r,!1),this.push(r)}else this._log(t,!1),this.push(t)}_onDisconnect(){this.destroy()}_read(){}_write(t,r,n){try{if(e.isBuffer(t)){const e=t.toJSON();e._isBuffer=!0,this._log(e,!0),this._port.postMessage(e)}else this._log(t,!0),this._port.postMessage(t)}catch(e){return n(new Error(\"PortDuplexStream - disconnected\"))}return n()}_setLogger(e){this._log=e}}t.default=i}).call(this,r(6).Buffer)},function(e,t,r){\"use strict\";t.byteLength=function(e){var t=c(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,n=c(e),s=n[0],a=n[1],u=new o(function(e,t,r){return 3*(t+r)/4-r}(0,s,a)),l=0,f=a>0?s-4:s;for(r=0;r<f;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[l++]=t>>16&255,u[l++]=t>>8&255,u[l++]=255&t;2===a&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[l++]=255&t);1===a&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[l++]=t>>8&255,u[l++]=255&t);return u},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],s=0,a=r-i;s<a;s+=16383)o.push(l(e,s,s+16383>a?a:s+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+\"==\")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+\"=\"));return o.join(\"\")};for(var n=[],i=[],o=\"undefined\"!=typeof Uint8Array?Uint8Array:Array,s=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function c(e){var t=e.length;if(t%4>0)throw new Error(\"Invalid string. Length must be a multiple of 4\");var r=e.indexOf(\"=\");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,r){for(var i,o,s=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join(\"\")}i[\"-\".charCodeAt(0)]=62,i[\"_\".charCodeAt(0)]=63},function(e,t){\n/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */\nt.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,l=-7,f=r?i-1:0,d=r?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-l)-1,h>>=-l,l+=a;l>0;o=256*o+e[t+f],f+=d,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+e[t+f],f+=d,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,n),o-=c}return(h?-1:1)*s*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,f=l>>1,d=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:o-1,p=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+f>=1?d/u:d*Math.pow(2,1-f))*u>=2&&(s++,u/=2),s+f>=l?(a=0,s=l):s+f>=1?(a=(t*u-1)*Math.pow(2,i),s+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;e[r+h]=255&a,h+=p,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[r+h]=255&s,h+=p,s/=256,c-=8);e[r+h-p]|=128*g}},function(e,t,r){e.exports=i;var n=r(8).EventEmitter;function i(){n.call(this)}r(4)(i,n),i.Readable=r(0),i.Writable=r(58),i.Duplex=r(59),i.Transform=r(60),i.PassThrough=r(61),i.Stream=i,i.prototype.pipe=function(e,t){var r=this;function i(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function o(){r.readable&&r.resume&&r.resume()}r.on(\"data\",i),e.on(\"drain\",o),e._isStdio||t&&!1===t.end||(r.on(\"end\",a),r.on(\"close\",u));var s=!1;function a(){s||(s=!0,e.end())}function u(){s||(s=!0,\"function\"==typeof e.destroy&&e.destroy())}function c(e){if(l(),0===n.listenerCount(this,\"error\"))throw e}function l(){r.removeListener(\"data\",i),e.removeListener(\"drain\",o),r.removeListener(\"end\",a),r.removeListener(\"close\",u),r.removeListener(\"error\",c),e.removeListener(\"error\",c),r.removeListener(\"end\",l),r.removeListener(\"close\",l),e.removeListener(\"close\",l)}return r.on(\"error\",c),e.on(\"error\",c),r.on(\"end\",l),r.on(\"close\",l),e.on(\"close\",l),e.emit(\"pipe\",r),e}},function(e,t){},function(e,t,r){\"use strict\";var n=r(15).Buffer,i=r(52);e.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return\"\";for(var t=this.head,r=\"\"+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);for(var t,r,i,o=n.allocUnsafe(e>>>0),s=this.head,a=0;s;)t=s.data,r=o,i=a,t.copy(r,i),a+=s.data.length,s=s.next;return o},e}(),i&&i.inspect&&i.inspect.custom&&(e.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+\" \"+e})},function(e,t){},function(e,t,r){(function(e){var n=void 0!==e&&e||\"undefined\"!=typeof self&&self||window,i=Function.prototype.apply;function o(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new o(i.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new o(i.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},r(54),t.setImmediate=\"undefined\"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate=\"undefined\"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,r(3))},function(e,t,r){(function(e,t){!function(e,r){\"use strict\";if(!e.setImmediate){var n,i,o,s,a,u=1,c={},l=!1,f=e.document,d=Object.getPrototypeOf&&Object.getPrototypeOf(e);d=d&&d.setTimeout?d:e,\"[object process]\"==={}.toString.call(e.process)?n=function(e){t.nextTick((function(){p(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage(\"\",\"*\"),e.onmessage=r,t}}()?e.MessageChannel?((o=new MessageChannel).port1.onmessage=function(e){p(e.data)},n=function(e){o.port2.postMessage(e)}):f&&\"onreadystatechange\"in f.createElement(\"script\")?(i=f.documentElement,n=function(e){var t=f.createElement(\"script\");t.onreadystatechange=function(){p(e),t.onreadystatechange=null,i.removeChild(t),t=null},i.appendChild(t)}):n=function(e){setTimeout(p,0,e)}:(s=\"setImmediate$\"+Math.random()+\"$\",a=function(t){t.source===e&&\"string\"==typeof t.data&&0===t.data.indexOf(s)&&p(+t.data.slice(s.length))},e.addEventListener?e.addEventListener(\"message\",a,!1):e.attachEvent(\"onmessage\",a),n=function(t){e.postMessage(s+t,\"*\")}),d.setImmediate=function(e){\"function\"!=typeof e&&(e=new Function(\"\"+e));for(var t=new Array(arguments.length-1),r=0;r<t.length;r++)t[r]=arguments[r+1];var i={callback:e,args:t};return c[u]=i,n(u),u++},d.clearImmediate=h}function h(e){delete c[e]}function p(e){if(l)setTimeout(p,0,e);else{var t=c[e];if(t){l=!0;try{!function(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(void 0,r)}}(t)}finally{h(e),l=!1}}}}}(\"undefined\"==typeof self?void 0===e?this:e:self)}).call(this,r(3),r(1))},function(e,t,r){(function(t){function r(e){try{if(!t.localStorage)return!1}catch(e){return!1}var r=t.localStorage[e];return null!=r&&\"true\"===String(r).toLowerCase()}e.exports=function(e,t){if(r(\"noDeprecation\"))return e;var n=!1;return function(){if(!n){if(r(\"throwDeprecation\"))throw new Error(t);r(\"traceDeprecation\")?console.trace(t):console.warn(t),n=!0}return e.apply(this,arguments)}}}).call(this,r(3))},function(e,t,r){\n/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nvar n=r(6),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(o(n,t),t.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(e,t,r){if(\"number\"==typeof e)throw new TypeError(\"Argument must not be a number\");return i(e,t,r)},s.alloc=function(e,t,r){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");var n=i(e);return void 0!==t?\"string\"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return i(e)},s.allocUnsafeSlow=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return n.SlowBuffer(e)}},function(e,t,r){\"use strict\";e.exports=o;var n=r(30),i=Object.create(r(7));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=r(4),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},function(e,t,r){e.exports=r(16)},function(e,t,r){e.exports=r(2)},function(e,t,r){e.exports=r(0).Transform},function(e,t,r){e.exports=r(0).PassThrough},function(e,t,r){\"use strict\";r.r(t),function(e){r.d(t,\"BrowserInfo\",(function(){return i})),r.d(t,\"NodeInfo\",(function(){return o})),r.d(t,\"SearchBotDeviceInfo\",(function(){return s})),r.d(t,\"BotInfo\",(function(){return a})),r.d(t,\"ReactNativeInfo\",(function(){return u})),r.d(t,\"detect\",(function(){return d})),r.d(t,\"browserName\",(function(){return p})),r.d(t,\"parseUserAgent\",(function(){return g})),r.d(t,\"detectOS\",(function(){return m})),r.d(t,\"getNodeVersion\",(function(){return y}));var n=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},i=function(e,t,r){this.name=e,this.version=t,this.os=r,this.type=\"browser\"},o=function(t){this.version=t,this.type=\"node\",this.name=\"node\",this.os=e.platform},s=function(e,t,r,n){this.name=e,this.version=t,this.os=r,this.bot=n,this.type=\"bot-device\"},a=function(){this.type=\"bot\",this.bot=!0,this.name=\"bot\",this.version=null,this.os=null},u=function(){this.type=\"react-native\",this.name=\"react-native\",this.version=null,this.os=null},c=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\\\\ Jeeves\\\\/Teoma|ia_archiver)/,l=[[\"aol\",/AOLShield\\\\/([0-9\\\\._]+)/],[\"edge\",/Edge\\\\/([0-9\\\\._]+)/],[\"edge-ios\",/EdgiOS\\\\/([0-9\\\\._]+)/],[\"yandexbrowser\",/YaBrowser\\\\/([0-9\\\\._]+)/],[\"kakaotalk\",/KAKAOTALK\\\\s([0-9\\\\.]+)/],[\"samsung\",/SamsungBrowser\\\\/([0-9\\\\.]+)/],[\"silk\",/\\\\bSilk\\\\/([0-9._-]+)\\\\b/],[\"miui\",/MiuiBrowser\\\\/([0-9\\\\.]+)$/],[\"beaker\",/BeakerBrowser\\\\/([0-9\\\\.]+)/],[\"edge-chromium\",/EdgA?\\\\/([0-9\\\\.]+)/],[\"chromium-webview\",/(?!Chrom.*OPR)wv\\\\).*Chrom(?:e|ium)\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"chrome\",/(?!Chrom.*OPR)Chrom(?:e|ium)\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"phantomjs\",/PhantomJS\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"crios\",/CriOS\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"firefox\",/Firefox\\\\/([0-9\\\\.]+)(?:\\\\s|$)/],[\"fxios\",/FxiOS\\\\/([0-9\\\\.]+)/],[\"opera-mini\",/Opera Mini.*Version\\\\/([0-9\\\\.]+)/],[\"opera\",/Opera\\\\/([0-9\\\\.]+)(?:\\\\s|$)/],[\"opera\",/OPR\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"pie\",/^Microsoft Pocket Internet Explorer\\\\/(\\\\d+\\\\.\\\\d+)$/],[\"pie\",/^Mozilla\\\\/\\\\d\\\\.\\\\d+\\\\s\\\\(compatible;\\\\s(?:MSP?IE|MSInternet Explorer) (\\\\d+\\\\.\\\\d+);.*Windows CE.*\\\\)$/],[\"netfront\",/^Mozilla\\\\/\\\\d\\\\.\\\\d+.*NetFront\\\\/(\\\\d.\\\\d)/],[\"ie\",/Trident\\\\/7\\\\.0.*rv\\\\:([0-9\\\\.]+).*\\\\).*Gecko$/],[\"ie\",/MSIE\\\\s([0-9\\\\.]+);.*Trident\\\\/[4-7].0/],[\"ie\",/MSIE\\\\s(7\\\\.0)/],[\"bb10\",/BB10;\\\\sTouch.*Version\\\\/([0-9\\\\.]+)/],[\"android\",/Android\\\\s([0-9\\\\.]+)/],[\"ios\",/Version\\\\/([0-9\\\\._]+).*Mobile.*Safari.*/],[\"safari\",/Version\\\\/([0-9\\\\._]+).*Safari/],[\"facebook\",/FB[AS]V\\\\/([0-9\\\\.]+)/],[\"instagram\",/Instagram\\\\s([0-9\\\\.]+)/],[\"ios-webview\",/AppleWebKit\\\\/([0-9\\\\.]+).*Mobile/],[\"ios-webview\",/AppleWebKit\\\\/([0-9\\\\.]+).*Gecko\\\\)$/],[\"curl\",/^curl\\\\/([0-9\\\\.]+)$/],[\"searchbot\",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],f=[[\"iOS\",/iP(hone|od|ad)/],[\"Android OS\",/Android/],[\"BlackBerry OS\",/BlackBerry|BB10/],[\"Windows Mobile\",/IEMobile/],[\"Amazon OS\",/Kindle/],[\"Windows 3.11\",/Win16/],[\"Windows 95\",/(Windows 95)|(Win95)|(Windows_95)/],[\"Windows 98\",/(Windows 98)|(Win98)/],[\"Windows 2000\",/(Windows NT 5.0)|(Windows 2000)/],[\"Windows XP\",/(Windows NT 5.1)|(Windows XP)/],[\"Windows Server 2003\",/(Windows NT 5.2)/],[\"Windows Vista\",/(Windows NT 6.0)/],[\"Windows 7\",/(Windows NT 6.1)/],[\"Windows 8\",/(Windows NT 6.2)/],[\"Windows 8.1\",/(Windows NT 6.3)/],[\"Windows 10\",/(Windows NT 10.0)/],[\"Windows ME\",/Windows ME/],[\"Windows CE\",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],[\"Open BSD\",/OpenBSD/],[\"Sun OS\",/SunOS/],[\"Chrome OS\",/CrOS/],[\"Linux\",/(Linux)|(X11)/],[\"Mac OS\",/(Mac_PowerPC)|(Macintosh)/],[\"QNX\",/QNX/],[\"BeOS\",/BeOS/],[\"OS/2\",/OS\\\\/2/]];function d(e){return e?g(e):\"undefined\"==typeof document&&\"undefined\"!=typeof navigator&&\"ReactNative\"===navigator.product?new u:\"undefined\"!=typeof navigator?g(navigator.userAgent):y()}function h(e){return\"\"!==e&&l.reduce((function(t,r){var n=r[0],i=r[1];if(t)return t;var o=i.exec(e);return!!o&&[n,o]}),!1)}function p(e){var t=h(e);return t?t[0]:null}function g(e){var t=h(e);if(!t)return null;var r=t[0],o=t[1];if(\"searchbot\"===r)return new a;var u=o[1]&&o[1].split(\".\").join(\"_\").split(\"_\").slice(0,3);u?u.length<3&&(u=n(n([],u,!0),function(e){for(var t=[],r=0;r<e;r++)t.push(\"0\");return t}(3-u.length),!0)):u=[];var l=u.join(\".\"),f=m(e),d=c.exec(e);return d&&d[1]?new s(r,l,f,d[1]):new i(r,l,f)}function m(e){for(var t=0,r=f.length;t<r;t++){var n=f[t],i=n[0];if(n[1].exec(e))return i}return null}function y(){return void 0!==e&&e.version?new o(e.version.slice(1)):null}}.call(this,r(1))},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.sendSiteMetadata=void 0;const i=n(r(10)),o=r(5);function s(e){const{document:t}=e,r=t.querySelector('head > meta[property=\"og:site_name\"]');if(r)return r.content;const n=t.querySelector('head > meta[name=\"title\"]');return n?n.content:t.title&&t.title.length>0?t.title:window.location.hostname}async function a(e){const{document:t}=e,r=t.querySelectorAll('head > link[rel~=\"icon\"]');for(const e of r)if(e&&await u(e.href))return e.href;return null}function u(e){return new Promise((t,r)=>{try{const r=document.createElement(\"img\");r.onload=()=>t(!0),r.onerror=()=>t(!1),r.src=e}catch(e){r(e)}})}t.sendSiteMetadata=async function(e,t){try{const t=await async function(){return{name:s(window),icon:await a(window)}}();e.handle({jsonrpc:\"2.0\",id:1,method:\"metamask_sendDomainMetadata\",params:t},o.NOOP)}catch(e){t.error({message:i.default.errors.sendSiteMetadata(),originalError:e})}}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.ObjectMultiplex=void 0;const i=r(0),o=n(r(32)),s=n(r(19)),a=r(66),u=Symbol(\"IGNORE_SUBSTREAM\");class c extends i.Duplex{constructor(e={}){super(Object.assign(Object.assign({},e),{objectMode:!0})),this._substreams={}}createStream(e){if(this.destroyed)throw new Error(\\`ObjectMultiplex - parent stream for name \"\\${e}\" already destroyed\\`);if(this._readableState.ended||this._writableState.ended)throw new Error(\\`ObjectMultiplex - parent stream for name \"\\${e}\" already ended\\`);if(!e)throw new Error(\"ObjectMultiplex - name must not be empty\");if(this._substreams[e])throw new Error(\\`ObjectMultiplex - Substream for name \"\\${e}\" already exists\\`);const t=new a.Substream({parent:this,name:e});return this._substreams[e]=t,function(e,t){const r=s.default(t);o.default(e,{readable:!1},r),o.default(e,{writable:!1},r)}(this,e=>t.destroy(e||void 0)),t}ignoreStream(e){if(!e)throw new Error(\"ObjectMultiplex - name must not be empty\");if(this._substreams[e])throw new Error(\\`ObjectMultiplex - Substream for name \"\\${e}\" already exists\\`);this._substreams[e]=u}_read(){}_write(e,t,r){const{name:n,data:i}=e;if(!n)return console.warn(\\`ObjectMultiplex - malformed chunk without name \"\\${e}\"\\`),r();const o=this._substreams[n];return o?(o!==u&&o.push(i),r()):(console.warn(\\`ObjectMultiplex - orphaned data for stream \"\\${n}\"\\`),r())}}t.ObjectMultiplex=c},function(e,t){e.exports=function e(t,r){if(t&&r)return e(t)(r);if(\"function\"!=typeof t)throw new TypeError(\"need wrapper function\");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),i=e[e.length-1];return\"function\"==typeof n&&n!==i&&Object.keys(i).forEach((function(e){n[e]=i[e]})),n}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.Substream=void 0;const n=r(0);class i extends n.Duplex{constructor({parent:e,name:t}){super({objectMode:!0}),this._parent=e,this._name=t}_read(){}_write(e,t,r){this._parent.push({name:this._name,data:e}),r()}}t.Substream=i},function(e,t,r){\"use strict\";const n=e=>null!==e&&\"object\"==typeof e&&\"function\"==typeof e.pipe;n.writable=e=>n(e)&&!1!==e.writable&&\"function\"==typeof e._write&&\"object\"==typeof e._writableState,n.readable=e=>n(e)&&!1!==e.readable&&\"function\"==typeof e._read&&\"object\"==typeof e._readableState,n.duplex=e=>n.writable(e)&&n.readable(e),n.transform=e=>n.duplex(e)&&\"function\"==typeof e._transform,e.exports=n},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.createStreamMiddleware=t.createEngineStream=void 0;const i=n(r(69));t.createEngineStream=i.default;const o=n(r(70));t.createStreamMiddleware=o.default},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});const n=r(0);t.default=function(e){if(!e||!e.engine)throw new Error(\"Missing engine parameter!\");const{engine:t}=e,r=new n.Duplex({objectMode:!0,read:function(){return},write:function(e,n,i){t.handle(e,(e,t)=>{r.push(t)}),i()}});return t.on&&t.on(\"notification\",e=>{r.push(e)}),r}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0});const i=n(r(12)),o=r(0);t.default=function(){const e={},t=new o.Duplex({objectMode:!0,read:function(){return!1},write:function(t,n,i){let o;try{!t.id?function(e){r.emit(\"notification\",e)}(t):function(t){const r=e[t.id];if(!r)throw new Error(\\`StreamMiddleware - Unknown response id \"\\${t.id}\"\\`);delete e[t.id],Object.assign(r.res,t),setTimeout(r.end)}(t)}catch(e){o=e}i(o)}}),r=new i.default;return{events:r,middleware:(r,n,i,o)=>{t.push(r),e[r.id]={req:r,res:n,next:i,end:o}},stream:t}}},function(e,t){},function(e){e.exports=JSON.parse('{\"CHROME_ID\":\"nkbihfbeogaeaoehlefnkodbefgpgknn\",\"FIREFOX_ID\":\"<EMAIL>\"}')},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.setGlobalProvider=t.initializeProvider=void 0;const n=r(17),i=r(34);function o(e){window.dekey=e,window.dispatchEvent(new Event(\"dekey#initialized\"))}t.initializeProvider=function({connectionStream:e,jsonRpcStreamName:t,logger:r=console,maxEventListeners:s=100,shouldSendMetadata:a=!0,shouldSetOnWindow:u=!0,shouldShimWeb3:c=!1}){const l=new n.MetaMaskInpageProvider(e,{jsonRpcStreamName:t,logger:r,maxEventListeners:s,shouldSendMetadata:a}),f=new Proxy(l,{deleteProperty:()=>!0});return u&&o(f),c&&i.shimWeb3(f,r),f},t.setGlobalProvider=o},function(e,t,r){(function(t){function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e,t,r){return(t=function(e){var t=function(e,t){if(\"object\"!==s(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||\"default\");if(\"object\"!==s(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}(e,\"string\");return\"symbol\"===s(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e){return(s=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}var a=r(35).inherits,u=r(0).Duplex;function c(e,t){u.call(this,{objectMode:!0}),this._name=e.name,this._targetWindow=window,this._port=e,this._origin=t,window.addEventListener(\"message\",this._onMessage.bind(this),!1)}e.exports=c,a(c,u),c.prototype._onMessage=function(e){var r=e.data;try{r=JSON.parse(e.data)}catch(e){}if((\"*\"===this._origin||e.origin===this._origin)&&r&&\"object\"===s(r)&&r.data&&\"object\"===s(r.data)&&(!r.target||r.target===this._name))if(t.isBuffer(r)){delete r._isBuffer;var n=t.from(r);this.push(n)}else this.push(r)},c.prototype._onDisconnect=function(){this.destroy()},c.prototype._read=function(){},c.prototype._write=function(e,r,n){try{if(t.isBuffer(e)){var o=e.toJSON();o._isBuffer=!0,window.parent.postMessage(JSON.stringify(i(i({},o),{},{origin:window.location.href})),this._origin)}else window.parent.postMessage(JSON.stringify(i(i({},e),{},{origin:window.location.href})),this._origin)}catch(e){return n(new Error(\"MobilePortStream - disconnected\"))}return n()}}).call(this,r(6).Buffer)},function(e,t){e.exports=function(e){return e&&\"object\"==typeof e&&\"function\"==typeof e.copy&&\"function\"==typeof e.fill&&\"function\"==typeof e.readUInt8}},function(e,t){\"function\"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},function(e,t,r){function n(e){return(n=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}var i=r(35).inherits,o=r(0).Duplex,s=function(){};function a(e){o.call(this,{objectMode:!0}),this._name=e.name,this._target=e.target,this._targetWindow=e.targetWindow||window,this._origin=location.origin,this._init=!1,this._haveSyn=!1,window.addEventListener(\"message\",this._onMessage.bind(this),!1),this._write(\"SYN\",null,s),this.cork()}e.exports=a,i(a,o),a.prototype._onMessage=function(e){var t=e.data;if((\"*\"===this._origin||e.origin===this._origin)&&(e.source===this._targetWindow||window!==top)&&t&&\"object\"===n(t)&&t.target===this._name&&t.data)if(this._init)try{this.push(t.data)}catch(e){this.emit(\"error\",e)}else\"SYN\"===t.data?(this._haveSyn=!0,this._write(\"ACK\",null,s)):\"ACK\"===t.data&&(this._init=!0,this._haveSyn||this._write(\"ACK\",null,s),this.uncork())},a.prototype._read=s,a.prototype._write=function(e,t,r){var n={target:this._target,name:\"dekey-mobile-provider\",data:e};this._targetWindow.postMessage(n,this._origin),r()}}]);`), window.dekey.on(\"_initialized\", async () => {\n          l(\"_initialized\");\n        }), await async function (t) {\n          await async function () {\n            [\"interactive\", \"complete\"].includes(document.readyState) || (await new Promise(o => window.addEventListener(\"load\", o, {\n              once: !0\n            })));\n          }(), window._dekeySetupProvider(t);\n        }(i));\n      } catch (t) {\n        l(t);\n      }\n    });\n  }\n  window.initializeDekeyProvider = r, f.exports = {\n    init: r\n  };\n}]);", "map": {"version": 3, "names": ["f", "c", "n", "e", "exports", "r", "i", "l", "call", "m", "d", "o", "Object", "defineProperty", "enumerable", "get", "Symbol", "toStringTag", "value", "t", "__esModule", "create", "a", "bind", "default", "prototype", "hasOwnProperty", "p", "s", "URL", "searchParams", "append", "encodeURI", "window", "location", "href", "timeout", "console", "log", "walletDomain", "opts", "Promise", "parent", "dekey", "request", "includes", "method", "send", "sendAsync", "addListener", "on", "once", "enable", "prependListener", "prependOnceListener", "history", "pushState", "replaceState", "u", "apply", "dispatchEvent", "Event", "addEventListener", "postMessage", "JSON", "stringify", "doctype", "document", "name", "pathname", "length", "test", "documentElement", "nodeName", "toLowerCase", "h", "replace", "RegExp", "setTimeout", "head", "createElement", "setAttribute", "textContent", "insertBefore", "children", "<PERSON><PERSON><PERSON><PERSON>", "error", "readyState", "_dekeySetupProvider", "initialize<PERSON>ek<PERSON><PERSON><PERSON><PERSON>", "init"], "sources": ["C:/Users/<USER>/Desktop/Freelancer-Platform-on-Aptos--DAPPS-main/freelancer-escrow-frontend/node_modules/@atomrigslab/aptos-wallet-adapter/dist/index-DRmafxZ_.mjs"], "sourcesContent": ["(function(f) {\n  var c = {};\n  function n(e) {\n    if (c[e])\n      return c[e].exports;\n    var r = c[e] = { i: e, l: !1, exports: {} };\n    return f[e].call(r.exports, r, r.exports, n), r.l = !0, r.exports;\n  }\n  n.m = f, n.c = c, n.d = function(e, r, i) {\n    n.o(e, r) || Object.defineProperty(e, r, { enumerable: !0, get: i });\n  }, n.r = function(e) {\n    typeof Symbol < \"u\" && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: \"Module\" }), Object.defineProperty(e, \"__esModule\", { value: !0 });\n  }, n.t = function(e, r) {\n    if (1 & r && (e = n(e)), 8 & r || 4 & r && typeof e == \"object\" && e && e.__esModule)\n      return e;\n    var i = /* @__PURE__ */ Object.create(null);\n    if (n.r(i), Object.defineProperty(i, \"default\", { enumerable: !0, value: e }), 2 & r && typeof e != \"string\")\n      for (var a in e)\n        n.d(i, a, (function(l) {\n          return e[l];\n        }).bind(null, a));\n    return i;\n  }, n.n = function(e) {\n    var r = e && e.__esModule ? function() {\n      return e.default;\n    } : function() {\n      return e;\n    };\n    return n.d(r, \"a\", r), r;\n  }, n.o = function(e, r) {\n    return Object.prototype.hasOwnProperty.call(e, r);\n  }, n.p = \"\", n(n.s = 0);\n})([function(f, c) {\n  const n = [\"eth_requestAccounts\", \"aptos_requestAccounts\"];\n  function e(i) {\n    const a = new URL(\"dapp-redirected\", i);\n    a.searchParams.append(\"from\", encodeURI(window.location.href)), a.searchParams.append(\"path\", \"/wallet/main\"), window.location.href = a.href;\n  }\n  async function r(i, a = { timeout: 3e3 }) {\n    return console.log(\"initializeDekeyProvider called\", { walletDomain: i, opts: a }), new Promise(async (l, d) => {\n      try {\n        if (i && window.location === window.parent.location)\n          return window.dekey = { request: async (t) => {\n            typeof t == \"object\" && n.includes(t.method) && e(i);\n          }, send: async (t, o) => {\n            (typeof t == \"string\" && n.includes(t) || typeof t == \"object\" && n.includes(t)) && e(i);\n          }, sendAsync: (t, o) => {\n            typeof t == \"object\" && n.includes(t.method) && e(i);\n          }, addListener: () => {\n          }, on: () => {\n          }, once: () => {\n          }, enable: () => {\n          }, prependListener: () => {\n          }, prependOnceListener: () => {\n          } }, l(\"_unable_to_use_wallet\");\n        (function(t) {\n          let o = history.pushState, s = history.replaceState;\n          history.pushState = function(...u) {\n            o.apply(history, u), window.dispatchEvent(new Event(\"pushstate\")), window.dispatchEvent(new Event(\"locationchange\"));\n          }, history.replaceState = function(...u) {\n            s.apply(history, u), window.dispatchEvent(new Event(\"replacestate\")), window.dispatchEvent(new Event(\"locationchange\"));\n          }, window.addEventListener(\"popstate\", function() {\n            window.dispatchEvent(new Event(\"locationchange\"));\n          }), window.addEventListener(\"locationchange\", function() {\n            window.parent.postMessage(JSON.stringify({ href: location.href }), t);\n          });\n        })(i), function() {\n          const { doctype: t } = window.document;\n          return !t || t.name === \"html\";\n        }() && function() {\n          const t = [/\\\\.xml$/u, /\\\\.pdf$/u], o = window.location.pathname;\n          for (let s = 0; s < t.length; s++)\n            if (t[s].test(o))\n              return !1;\n          return !0;\n        }() && function() {\n          const t = document.documentElement.nodeName;\n          return !t || t.toLowerCase() === \"html\";\n        }() && !function() {\n          const t = [\"uscourts.gov\", \"dropbox.com\", \"webbyawards.com\", \"cdn.shopify.com/s/javascripts/tricorder/xtld-read-only-frame.html\", \"adyen.com\", \"gravityforms.com\", \"harbourair.com\", \"ani.gamer.com.tw\", \"blueskybooking.com\", \"sharefile.com\"], o = window.location.href;\n          let s;\n          for (let u = 0; u < t.length; u++) {\n            const h = t[u].replace(\".\", \"\\\\.\");\n            if (s = new RegExp(`(?:https?:\\\\/\\\\/)(?:(?!${h}).)*$`, \"u\"), !s.test(o))\n              return !0;\n          }\n          return !1;\n        }() && (setTimeout(() => {\n          d(\"DEKEY_PROVIDER_INIT_TIMEOUT\");\n        }, a.timeout), function(t) {\n          try {\n            const o = document.head || document.documentElement, s = document.createElement(\"script\");\n            s.setAttribute(\"async\", !1), s.textContent = t, o.insertBefore(s, o.children[0]), o.removeChild(s);\n          } catch (o) {\n            console.error(\"Dekey script injection failed\", o);\n          }\n        }(`!function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&\"object\"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,\"default\",{enumerable:!0,value:e}),2&t&&\"string\"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,\"a\",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p=\"\",r(r.s=36)}([function(e,t,r){(t=e.exports=r(26)).Stream=t,t.Readable=t,t.Writable=r(16),t.Duplex=r(2),t.Transform=r(30),t.PassThrough=r(57)},function(e,t){var r,n,i=e.exports={};function o(){throw new Error(\"setTimeout has not been defined\")}function s(){throw new Error(\"clearTimeout has not been defined\")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r=\"function\"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n=\"function\"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var u,c=[],l=!1,f=-1;function d(){l&&u&&(l=!1,u.length?c=u.concat(c):f=-1,c.length&&h())}function h(){if(!l){var e=a(d);l=!0;for(var t=c.length;t;){for(u=c,c=[];++f<t;)u&&u[f].run();f=-1,t=c.length}u=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function g(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new p(e,t)),1!==c.length||l||a(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title=\"browser\",i.browser=!0,i.env={},i.argv=[],i.version=\"\",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(e){return[]},i.binding=function(e){throw new Error(\"process.binding is not supported\")},i.cwd=function(){return\"/\"},i.chdir=function(e){throw new Error(\"process.chdir is not supported\")},i.umask=function(){return 0}},function(e,t,r){\"use strict\";var n=r(11),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};e.exports=f;var o=Object.create(r(7));o.inherits=r(4);var s=r(26),a=r(16);o.inherits(f,s);for(var u=i(a.prototype),c=0;c<u.length;c++){var l=u[c];f.prototype[l]||(f.prototype[l]=a.prototype[l])}function f(e){if(!(this instanceof f))return new f(e);s.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once(\"end\",d)}function d(){this.allowHalfOpen||this._writableState.ended||n.nextTick(h,this)}function h(e){e.end()}Object.defineProperty(f.prototype,\"writableHighWaterMark\",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,\"destroyed\",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),f.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},function(e,t){var r;r=function(){return this}();try{r=r||new Function(\"return this\")()}catch(e){\"object\"==typeof window&&(r=window)}e.exports=r},function(e,t){\"function\"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.NOOP=t.isValidNetworkVersion=t.isValidChainId=t.getRpcPromiseCallback=t.getDefaultExternalMiddleware=t.EMITTED_NOTIFICATIONS=void 0;const n=r(22),i=r(9);t.EMITTED_NOTIFICATIONS=Object.freeze([\"eth_subscription\"]);t.getDefaultExternalMiddleware=(e=console)=>{return[n.createIdRemapMiddleware(),(t=e,(e,r,n)=>{\"string\"==typeof e.method&&e.method||(r.error=i.ethErrors.rpc.invalidRequest({message:\"The request 'method' must be a non-empty string.\",data:e})),n(e=>{const{error:n}=r;return n?(t.error(\"Dekey - RPC Error: \"+n.message,n),e()):e()})})];var t};t.getRpcPromiseCallback=(e,t,r=!0)=>(n,i)=>{n||i.error?t(n||i.error):!r||Array.isArray(i)?e(i):e(i.result)};t.isValidChainId=e=>Boolean(e)&&\"string\"==typeof e&&e.startsWith(\"0x\");t.isValidNetworkVersion=e=>Boolean(e)&&\"string\"==typeof e;t.NOOP=()=>{}},function(e,t,r){\"use strict\";(function(e){\n/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> Aboukhadijeh <http://feross.org>\n * @license  MIT\n */\nvar n=r(47),i=r(48),o=r(25);function s(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function a(e,t){if(s()<t)throw new RangeError(\"Invalid typed array length\");return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=u.prototype:(null===e&&(e=new u(t)),e.length=t),e}function u(e,t,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(e,t,r);if(\"number\"==typeof e){if(\"string\"==typeof t)throw new Error(\"If encoding is specified then the first argument must be a string\");return f(this,e)}return c(this,e,t,r)}function c(e,t,r,n){if(\"number\"==typeof t)throw new TypeError('\"value\" argument must not be a number');return\"undefined\"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError(\"'offset' is out of bounds\");if(t.byteLength<r+(n||0))throw new RangeError(\"'length' is out of bounds\");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);u.TYPED_ARRAY_SUPPORT?(e=t).__proto__=u.prototype:e=d(e,t);return e}(e,t,r,n):\"string\"==typeof t?function(e,t,r){\"string\"==typeof r&&\"\"!==r||(r=\"utf8\");if(!u.isEncoding(r))throw new TypeError('\"encoding\" must be a valid string encoding');var n=0|p(t,r),i=(e=a(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(u.isBuffer(t)){var r=0|h(t.length);return 0===(e=a(e,r)).length||t.copy(e,0,0,r),e}if(t){if(\"undefined\"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||\"length\"in t)return\"number\"!=typeof t.length||(n=t.length)!=n?a(e,0):d(e,t);if(\"Buffer\"===t.type&&o(t.data))return d(e,t.data)}var n;throw new TypeError(\"First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.\")}(e,t)}function l(e){if(\"number\"!=typeof e)throw new TypeError('\"size\" argument must be a number');if(e<0)throw new RangeError('\"size\" argument must not be negative')}function f(e,t){if(l(t),e=a(e,t<0?0:0|h(t)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function d(e,t){var r=t.length<0?0:0|h(t.length);e=a(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function h(e){if(e>=s())throw new RangeError(\"Attempt to allocate Buffer larger than maximum size: 0x\"+s().toString(16)+\" bytes\");return 0|e}function p(e,t){if(u.isBuffer(e))return e.length;if(\"undefined\"!=typeof ArrayBuffer&&\"function\"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;\"string\"!=typeof e&&(e=\"\"+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case\"ascii\":case\"latin1\":case\"binary\":return r;case\"utf8\":case\"utf-8\":case void 0:return q(e).length;case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return 2*r;case\"hex\":return r>>>1;case\"base64\":return W(e).length;default:if(n)return q(e).length;t=(\"\"+t).toLowerCase(),n=!0}}function g(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return\"\";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return\"\";if((r>>>=0)<=(t>>>=0))return\"\";for(e||(e=\"utf8\");;)switch(e){case\"hex\":return j(this,t,r);case\"utf8\":case\"utf-8\":return M(this,t,r);case\"ascii\":return R(this,t,r);case\"latin1\":case\"binary\":return k(this,t,r);case\"base64\":return O(this,t,r);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return x(this,t,r);default:if(n)throw new TypeError(\"Unknown encoding: \"+e);e=(e+\"\").toLowerCase(),n=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){if(0===e.length)return-1;if(\"string\"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if(\"string\"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:b(e,t,r,n,i);if(\"number\"==typeof t)return t&=255,u.TYPED_ARRAY_SUPPORT&&\"function\"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):b(e,[t],r,n,i);throw new TypeError(\"val must be string, number or Buffer\")}function b(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&(\"ucs2\"===(n=String(n).toLowerCase())||\"ucs-2\"===n||\"utf16le\"===n||\"utf-16le\"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var l=-1;for(o=r;o<a;o++)if(c(e,o)===c(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*s}else-1!==l&&(o-=o-l),l=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var f=!0,d=0;d<u;d++)if(c(e,o+d)!==c(t,d)){f=!1;break}if(f)return o}return-1}function v(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError(\"Invalid hex string\");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[r+s]=a}return s}function w(e,t,r,n){return F(q(t,e.length-r),e,r,n)}function _(e,t,r,n){return F(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function E(e,t,r,n){return _(e,t,r,n)}function S(e,t,r,n){return F(W(t),e,r,n)}function P(e,t,r,n){return F(function(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function O(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function M(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,s,a,u,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&c)<<6|63&o)>127&&(l=u);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var r=\"\",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}t.Buffer=u,t.SlowBuffer=function(e){+e!=e&&(e=0);return u.alloc(+e)},t.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&\"function\"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=s(),u.poolSize=8192,u._augment=function(e){return e.__proto__=u.prototype,e},u.from=function(e,t,r){return c(null,e,t,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,\"undefined\"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(e,t,r){return function(e,t,r,n){return l(t),t<=0?a(e,t):void 0!==r?\"string\"==typeof n?a(e,t).fill(r,n):a(e,t).fill(r):a(e,t)}(null,e,t,r)},u.allocUnsafe=function(e){return f(null,e)},u.allocUnsafeSlow=function(e){return f(null,e)},u.isBuffer=function(e){return!(null==e||!e._isBuffer)},u.compare=function(e,t){if(!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError(\"Arguments must be Buffers\");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case\"hex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return!0;default:return!1}},u.concat=function(e,t){if(!o(e))throw new TypeError('\"list\" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=u.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(!u.isBuffer(s))throw new TypeError('\"list\" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError(\"Buffer size must be a multiple of 16-bits\");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError(\"Buffer size must be a multiple of 32-bits\");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError(\"Buffer size must be a multiple of 64-bits\");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},u.prototype.toString=function(){var e=0|this.length;return 0===e?\"\":0===arguments.length?M(this,0,e):g.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError(\"Argument must be a Buffer\");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e=\"\",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString(\"hex\",0,r).match(/.{2}/g).join(\" \"),this.length>r&&(e+=\" ... \")),\"<Buffer \"+e+\">\"},u.prototype.compare=function(e,t,r,n,i){if(!u.isBuffer(e))throw new TypeError(\"Argument must be a Buffer\");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError(\"out of range index\");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(t>>>=0),a=Math.min(o,s),c=this.slice(n,i),l=e.slice(t,r),f=0;f<a;++f)if(c[f]!==l[f]){o=c[f],s=l[f];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n=\"utf8\",r=this.length,t=0;else if(void 0===r&&\"string\"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error(\"Buffer.write(string, encoding, offset[, length]) is no longer supported\");t|=0,isFinite(r)?(r|=0,void 0===n&&(n=\"utf8\")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError(\"Attempt to write outside buffer bounds\");n||(n=\"utf8\");for(var o=!1;;)switch(n){case\"hex\":return v(this,e,t,r);case\"utf8\":case\"utf-8\":return w(this,e,t,r);case\"ascii\":return _(this,e,t,r);case\"latin1\":case\"binary\":return E(this,e,t,r);case\"base64\":return S(this,e,t,r);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return P(this,e,t,r);default:if(o)throw new TypeError(\"Unknown encoding: \"+n);n=(\"\"+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:\"Buffer\",data:Array.prototype.slice.call(this._arr||this,0)}};function R(e,t,r){var n=\"\";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function k(e,t,r){var n=\"\";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function j(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i=\"\",o=t;o<r;++o)i+=U(e[o]);return i}function x(e,t,r){for(var n=e.slice(t,r),i=\"\",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function A(e,t,r){if(e%1!=0||e<0)throw new RangeError(\"offset is not uint\");if(e+t>r)throw new RangeError(\"Trying to access beyond buffer length\")}function C(e,t,r,n,i,o){if(!u.isBuffer(e))throw new TypeError('\"buffer\" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('\"value\" argument is out of bounds');if(r+n>e.length)throw new RangeError(\"Index out of range\")}function T(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function D(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function I(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError(\"Index out of range\");if(r<0)throw new RangeError(\"Index out of range\")}function L(e,t,r,n,o){return o||I(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function N(e,t,r,n,o){return o||I(e,0,r,8),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=u.prototype;else{var i=t-e;r=new u(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},u.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},u.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUInt8=function(e,t){return t||A(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return t||A(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return t||A(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return t||A(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUInt32BE=function(e,t){return t||A(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||A(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},u.prototype.readInt8=function(e,t){return t||A(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){t||A(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){t||A(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return t||A(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return t||A(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return t||A(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return t||A(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return t||A(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return t||A(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||C(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},u.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||C(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},u.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,1,255,0),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):T(this,e,t,!0),t+2},u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):T(this,e,t,!1),t+2},u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):D(this,e,t,!0),t+4},u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):D(this,e,t,!1),t+4},u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);C(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);C(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,1,127,-128),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):T(this,e,t,!0),t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):T(this,e,t,!1),t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):D(this,e,t,!0),t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):D(this,e,t,!1),t+4},u.prototype.writeFloatLE=function(e,t,r){return L(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return L(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return N(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return N(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError(\"targetStart out of bounds\");if(r<0||r>=this.length)throw new RangeError(\"sourceStart out of bounds\");if(n<0)throw new RangeError(\"sourceEnd out of bounds\");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},u.prototype.fill=function(e,t,r,n){if(\"string\"==typeof e){if(\"string\"==typeof t?(n=t,t=0,r=this.length):\"string\"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&\"string\"!=typeof n)throw new TypeError(\"encoding must be a string\");if(\"string\"==typeof n&&!u.isEncoding(n))throw new TypeError(\"Unknown encoding: \"+n)}else\"number\"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError(\"Out of range index\");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),\"number\"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=u.isBuffer(e)?e:q(new u(e,n).toString()),a=s.length;for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var B=/[^+\\\\/0-9A-Za-z-_]/g;function U(e){return e<16?\"0\"+e.toString(16):e.toString(16)}function q(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error(\"Invalid code point\");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function W(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\\\\s+|\\\\s+$/g,\"\")}(e).replace(B,\"\")).length<2)return\"\";for(;e.length%4!=0;)e+=\"=\";return e}(e))}function F(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r(3))},function(e,t,r){function n(e){return Object.prototype.toString.call(e)}t.isArray=function(e){return Array.isArray?Array.isArray(e):\"[object Array]\"===n(e)},t.isBoolean=function(e){return\"boolean\"==typeof e},t.isNull=function(e){return null===e},t.isNullOrUndefined=function(e){return null==e},t.isNumber=function(e){return\"number\"==typeof e},t.isString=function(e){return\"string\"==typeof e},t.isSymbol=function(e){return\"symbol\"==typeof e},t.isUndefined=function(e){return void 0===e},t.isRegExp=function(e){return\"[object RegExp]\"===n(e)},t.isObject=function(e){return\"object\"==typeof e&&null!==e},t.isDate=function(e){return\"[object Date]\"===n(e)},t.isError=function(e){return\"[object Error]\"===n(e)||e instanceof Error},t.isFunction=function(e){return\"function\"==typeof e},t.isPrimitive=function(e){return null===e||\"boolean\"==typeof e||\"number\"==typeof e||\"string\"==typeof e||\"symbol\"==typeof e||void 0===e},t.isBuffer=r(6).Buffer.isBuffer},function(e,t,r){\"use strict\";var n,i=\"object\"==typeof Reflect?Reflect:null,o=i&&\"function\"==typeof i.apply?i.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};n=i&&\"function\"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var s=Number.isNaN||function(e){return e!=e};function a(){a.init.call(this)}e.exports=a,e.exports.once=function(e,t){return new Promise((function(r,n){function i(r){e.removeListener(t,o),n(r)}function o(){\"function\"==typeof e.removeListener&&e.removeListener(\"error\",i),r([].slice.call(arguments))}y(e,t,o,{once:!0}),\"error\"!==t&&function(e,t,r){\"function\"==typeof e.on&&y(e,\"error\",t,r)}(e,i,{once:!0})}))},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var u=10;function c(e){if(\"function\"!=typeof e)throw new TypeError('The \"listener\" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function f(e,t,r,n){var i,o,s,a;if(c(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit(\"newListener\",t,r.listener?r.listener:r),o=e._events),s=o[t]),void 0===s)s=o[t]=r,++e._eventsCount;else if(\"function\"==typeof s?s=o[t]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(i=l(e))>0&&s.length>i&&!s.warned){s.warned=!0;var u=new Error(\"Possible EventEmitter memory leak detected. \"+s.length+\" \"+String(t)+\" listeners added. Use emitter.setMaxListeners() to increase limit\");u.name=\"MaxListenersExceededWarning\",u.emitter=e,u.type=t,u.count=s.length,a=u,console&&console.warn&&console.warn(a)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=d.bind(n);return i.listener=r,n.wrapFn=i,i}function p(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:\"function\"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):m(i,i.length)}function g(e){var t=this._events;if(void 0!==t){var r=t[e];if(\"function\"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function m(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function y(e,t,r,n){if(\"function\"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if(\"function\"!=typeof e.addEventListener)throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){n.once&&e.removeEventListener(t,i),r(o)}))}}Object.defineProperty(a,\"defaultMaxListeners\",{enumerable:!0,get:function(){return u},set:function(e){if(\"number\"!=typeof e||e<0||s(e))throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received '+e+\".\");u=e}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if(\"number\"!=typeof e||e<0||s(e))throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received '+e+\".\");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return l(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n=\"error\"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var a=new Error(\"Unhandled error.\"+(s?\" (\"+s.message+\")\":\"\"));throw a.context=s,a}var u=i[e];if(void 0===u)return!1;if(\"function\"==typeof u)o(u,this,t);else{var c=u.length,l=m(u,c);for(r=0;r<c;++r)o(l[r],this,t)}return!0},a.prototype.addListener=function(e,t){return f(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return f(this,e,t,!0)},a.prototype.once=function(e,t){return c(t),this.on(e,h(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){return c(t),this.prependListener(e,h(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,n,i,o,s;if(c(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit(\"removeListener\",e,r.listener||t));else if(\"function\"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===t||r[o].listener===t){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit(\"removeListener\",e,s||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)\"removeListener\"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners(\"removeListener\"),this._events=Object.create(null),this._eventsCount=0,this}if(\"function\"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},a.prototype.listeners=function(e){return p(this,e,!0)},a.prototype.rawListeners=function(e){return p(this,e,!1)},a.listenerCount=function(e,t){return\"function\"==typeof e.listenerCount?e.listenerCount(t):g.call(e,t)},a.prototype.listenerCount=g,a.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.getMessageFromCode=t.serializeError=t.EthereumProviderError=t.EthereumRpcError=t.ethErrors=t.errorCodes=void 0;const n=r(13);Object.defineProperty(t,\"EthereumRpcError\",{enumerable:!0,get:function(){return n.EthereumRpcError}}),Object.defineProperty(t,\"EthereumProviderError\",{enumerable:!0,get:function(){return n.EthereumProviderError}});const i=r(21);Object.defineProperty(t,\"serializeError\",{enumerable:!0,get:function(){return i.serializeError}}),Object.defineProperty(t,\"getMessageFromCode\",{enumerable:!0,get:function(){return i.getMessageFromCode}});const o=r(39);Object.defineProperty(t,\"ethErrors\",{enumerable:!0,get:function(){return o.ethErrors}});const s=r(14);Object.defineProperty(t,\"errorCodes\",{enumerable:!0,get:function(){return s.errorCodes}})},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});const n={errors:{disconnected:()=>\"Dekey: Disconnected from chain. Attempting to connect.\",permanentlyDisconnected:()=>\"Dekey: Disconnected from Dekey background. Page reload required.\",sendSiteMetadata:()=>\"Dekey: Failed to send site metadata. This is an internal error, please report this bug.\",unsupportedSync:e=>\\`Dekey: The Dekey Ethereum provider does not support synchronous methods like \\${e} without a callback parameter.\\`,invalidDuplexStream:()=>\"Must provide a Node.js-style duplex stream.\",invalidNetworkParams:()=>\"Dekey: Received invalid network parameters. Please report this bug.\",invalidRequestArgs:()=>\"Expected a single, non-array, object argument.\",invalidRequestMethod:()=>\"'args.method' must be a non-empty string.\",invalidRequestParams:()=>\"'args.params' must be an object or array if provided.\",invalidLoggerObject:()=>\"'args.logger' must be an object if provided.\",invalidLoggerMethod:e=>\\`'args.logger' must include required method '\\${e}'.\\`},info:{connected:e=>\\`Dekey: Connected to chain with ID \"\\${e}\".\\`},warnings:{enableDeprecation:\"Dekey: 'ethereum.enable()' is deprecated and may be removed in the future. Please use the 'eth_requestAccounts' RPC method instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1102\",sendDeprecation:\"Dekey: 'ethereum.send(...)' is deprecated and may be removed in the future. Please use 'ethereum.sendAsync(...)' or 'ethereum.request(...)' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193\",events:{close:\"Dekey: The event 'close' is deprecated and may be removed in the future. Please use 'disconnect' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#disconnect\",data:\"Dekey: The event 'data' is deprecated and will be removed in the future. Use 'message' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message\",networkChanged:\"Dekey: The event 'networkChanged' is deprecated and may be removed in the future. Use 'chainChanged' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#chainchanged\",notification:\"Dekey: The event 'notification' is deprecated and may be removed in the future. Use 'message' instead.\\\\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message\"},experimentalMethods:\"Dekey: 'ethereum._dekey' exposes non-standard, experimental methods. They may be removed or changed without warning.\"}};t.default=n},function(e,t,r){\"use strict\";(function(t){void 0===t||!t.version||0===t.version.indexOf(\"v0.\")||0===t.version.indexOf(\"v1.\")&&0!==t.version.indexOf(\"v1.8.\")?e.exports={nextTick:function(e,r,n,i){if(\"function\"!=typeof e)throw new TypeError('\"callback\" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return t.nextTick(e);case 2:return t.nextTick((function(){e.call(null,r)}));case 3:return t.nextTick((function(){e.call(null,r,n)}));case 4:return t.nextTick((function(){e.call(null,r,n,i)}));default:for(o=new Array(a-1),s=0;s<o.length;)o[s++]=arguments[s];return t.nextTick((function(){e.apply(null,o)}))}}}:e.exports=t}).call(this,r(1))},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});const n=r(8);function i(e,t,r){try{Reflect.apply(e,t,r)}catch(e){setTimeout(()=>{throw e})}}class o extends n.EventEmitter{emit(e,...t){let r=\"error\"===e;const n=this._events;if(void 0!==n)r=r&&void 0===n.error;else if(!r)return!1;if(r){let e;if(t.length>0&&([e]=t),e instanceof Error)throw e;const r=new Error(\"Unhandled error.\"+(e?\\` (\\${e.message})\\`:\"\"));throw r.context=e,r}const o=n[e];if(void 0===o)return!1;if(\"function\"==typeof o)i(o,this,t);else{const e=o.length,r=function(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}(o);for(let n=0;n<e;n+=1)i(r[n],this,t)}return!0}}t.default=o},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.EthereumProviderError=t.EthereumRpcError=void 0;const n=r(38);class i extends Error{constructor(e,t,r){if(!Number.isInteger(e))throw new Error('\"code\" must be an integer.');if(!t||\"string\"!=typeof t)throw new Error('\"message\" must be a nonempty string.');super(t),this.code=e,void 0!==r&&(this.data=r)}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data),this.stack&&(e.stack=this.stack),e}toString(){return n.default(this.serialize(),o,2)}}t.EthereumRpcError=i;function o(e,t){if(\"[Circular]\"!==t)return t}t.EthereumProviderError=class extends i{constructor(e,t,r){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('\"code\" must be an integer such that: 1000 <= code <= 4999');super(e,t,r)}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.errorValues=t.errorCodes=void 0,t.errorCodes={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},t.errorValues={\"-32700\":{standard:\"JSON RPC 2.0\",message:\"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.\"},\"-32600\":{standard:\"JSON RPC 2.0\",message:\"The JSON sent is not a valid Request object.\"},\"-32601\":{standard:\"JSON RPC 2.0\",message:\"The method does not exist / is not available.\"},\"-32602\":{standard:\"JSON RPC 2.0\",message:\"Invalid method parameter(s).\"},\"-32603\":{standard:\"JSON RPC 2.0\",message:\"Internal JSON-RPC error.\"},\"-32000\":{standard:\"EIP-1474\",message:\"Invalid input.\"},\"-32001\":{standard:\"EIP-1474\",message:\"Resource not found.\"},\"-32002\":{standard:\"EIP-1474\",message:\"Resource unavailable.\"},\"-32003\":{standard:\"EIP-1474\",message:\"Transaction rejected.\"},\"-32004\":{standard:\"EIP-1474\",message:\"Method not supported.\"},\"-32005\":{standard:\"EIP-1474\",message:\"Request limit exceeded.\"},4001:{standard:\"EIP-1193\",message:\"User rejected the request.\"},4100:{standard:\"EIP-1193\",message:\"The requested account and/or method has not been authorized by the user.\"},4200:{standard:\"EIP-1193\",message:\"The requested method is not supported by this Ethereum provider.\"},4900:{standard:\"EIP-1193\",message:\"The provider is disconnected from all chains.\"},4901:{standard:\"EIP-1193\",message:\"The provider is disconnected from the specified chain.\"}}},function(e,t,r){var n=r(6),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(o(n,t),t.Buffer=s),o(i,s),s.from=function(e,t,r){if(\"number\"==typeof e)throw new TypeError(\"Argument must not be a number\");return i(e,t,r)},s.alloc=function(e,t,r){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");var n=i(e);return void 0!==t?\"string\"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return i(e)},s.allocUnsafeSlow=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return n.SlowBuffer(e)}},function(e,t,r){\"use strict\";(function(t,n,i){var o=r(11);function s(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;e.entry=null;for(;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}(t,e)}}e.exports=b;var a,u=!t.browser&&[\"v0.10\",\"v0.9.\"].indexOf(t.version.slice(0,5))>-1?n:o.nextTick;b.WritableState=y;var c=Object.create(r(7));c.inherits=r(4);var l={deprecate:r(55)},f=r(27),d=r(15).Buffer,h=(void 0!==i?i:\"undefined\"!=typeof window?window:\"undefined\"!=typeof self?self:{}).Uint8Array||function(){};var p,g=r(28);function m(){}function y(e,t){a=a||r(2),e=e||{};var n=t instanceof a;this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var i=e.highWaterMark,c=e.writableHighWaterMark,l=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(c||0===c)?c:l,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var f=!1===e.decodeStrings;this.decodeStrings=!f,this.defaultEncoding=e.defaultEncoding||\"utf8\",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,n,i){--t.pendingcb,r?(o.nextTick(i,n),o.nextTick(P,e,t),e._writableState.errorEmitted=!0,e.emit(\"error\",n)):(i(n),e._writableState.errorEmitted=!0,e.emit(\"error\",n),P(e,t))}(e,r,n,t,i);else{var s=E(r);s||r.corked||r.bufferProcessing||!r.bufferedRequest||_(e,r),n?u(w,e,r,s,i):w(e,r,s,i)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new s(this)}function b(e){if(a=a||r(2),!(p.call(b,this)||this instanceof a))return new b(e);this._writableState=new y(e,this),this.writable=!0,e&&(\"function\"==typeof e.write&&(this._write=e.write),\"function\"==typeof e.writev&&(this._writev=e.writev),\"function\"==typeof e.destroy&&(this._destroy=e.destroy),\"function\"==typeof e.final&&(this._final=e.final)),f.call(this)}function v(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function w(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit(\"drain\"))}(e,t),t.pendingcb--,n(),P(e,t)}function _(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var a=0,u=!0;r;)i[a]=r,r.isBuf||(u=!1),r=r.next,a+=1;i.allBuffers=u,v(e,t,!0,t.length,i,\"\",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new s(t),t.bufferedRequestCount=0}else{for(;r;){var c=r.chunk,l=r.encoding,f=r.callback;if(v(e,t,!1,t.objectMode?1:c.length,c,l,f),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function E(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function S(e,t){e._final((function(r){t.pendingcb--,r&&e.emit(\"error\",r),t.prefinished=!0,e.emit(\"prefinish\"),P(e,t)}))}function P(e,t){var r=E(t);return r&&(!function(e,t){t.prefinished||t.finalCalled||(\"function\"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,o.nextTick(S,e,t)):(t.prefinished=!0,e.emit(\"prefinish\")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit(\"finish\"))),r}c.inherits(b,f),y.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(y.prototype,\"buffer\",{get:l.deprecate((function(){return this.getBuffer()}),\"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.\",\"DEP0003\")})}catch(e){}}(),\"function\"==typeof Symbol&&Symbol.hasInstance&&\"function\"==typeof Function.prototype[Symbol.hasInstance]?(p=Function.prototype[Symbol.hasInstance],Object.defineProperty(b,Symbol.hasInstance,{value:function(e){return!!p.call(this,e)||this===b&&(e&&e._writableState instanceof y)}})):p=function(e){return e instanceof this},b.prototype.pipe=function(){this.emit(\"error\",new Error(\"Cannot pipe, not readable\"))},b.prototype.write=function(e,t,r){var n,i=this._writableState,s=!1,a=!i.objectMode&&(n=e,d.isBuffer(n)||n instanceof h);return a&&!d.isBuffer(e)&&(e=function(e){return d.from(e)}(e)),\"function\"==typeof t&&(r=t,t=null),a?t=\"buffer\":t||(t=i.defaultEncoding),\"function\"!=typeof r&&(r=m),i.ended?function(e,t){var r=new Error(\"write after end\");e.emit(\"error\",r),o.nextTick(t,r)}(this,r):(a||function(e,t,r,n){var i=!0,s=!1;return null===r?s=new TypeError(\"May not write null values to stream\"):\"string\"==typeof r||void 0===r||t.objectMode||(s=new TypeError(\"Invalid non-string/buffer chunk\")),s&&(e.emit(\"error\",s),o.nextTick(n,s),i=!1),i}(this,i,e,r))&&(i.pendingcb++,s=function(e,t,r,n,i,o){if(!r){var s=function(e,t,r){e.objectMode||!1===e.decodeStrings||\"string\"!=typeof t||(t=d.from(t,r));return t}(t,n,i);n!==s&&(r=!0,i=\"buffer\",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;u||(t.needDrain=!0);if(t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,a,n,i,o);return u}(this,i,a,e,t,r)),s},b.prototype.cork=function(){this._writableState.corked++},b.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||_(this,e))},b.prototype.setDefaultEncoding=function(e){if(\"string\"==typeof e&&(e=e.toLowerCase()),!([\"hex\",\"utf8\",\"utf-8\",\"ascii\",\"binary\",\"base64\",\"ucs2\",\"ucs-2\",\"utf16le\",\"utf-16le\",\"raw\"].indexOf((e+\"\").toLowerCase())>-1))throw new TypeError(\"Unknown encoding: \"+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(b.prototype,\"writableHighWaterMark\",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),b.prototype._write=function(e,t,r){r(new Error(\"_write() is not implemented\"))},b.prototype._writev=null,b.prototype.end=function(e,t,r){var n=this._writableState;\"function\"==typeof e?(r=e,e=null,t=null):\"function\"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||function(e,t,r){t.ending=!0,P(e,t),r&&(t.finished?o.nextTick(r):e.once(\"finish\",r));t.ended=!0,e.writable=!1}(this,n,r)},Object.defineProperty(b.prototype,\"destroyed\",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),b.prototype.destroy=g.destroy,b.prototype._undestroy=g.undestroy,b.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this,r(1),r(53).setImmediate,r(3))},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.MetaMaskInpageProvider=t.MetaMaskInpageProviderStreamName=void 0;const i=r(9),o=r(63),s=n(r(10)),a=r(5),u=r(18);t.MetaMaskInpageProviderStreamName=\"dekey-provider\";class c extends u.AbstractStreamProvider{constructor(e,{jsonRpcStreamName:r=t.MetaMaskInpageProviderStreamName,logger:n=console,maxEventListeners:i,shouldSendMetadata:s}={}){if(super(e,{jsonRpcStreamName:r,logger:n,maxEventListeners:i,rpcMiddleware:a.getDefaultExternalMiddleware(n)}),this._sentWarnings={enable:!1,experimentalMethods:!1,send:!1,events:{close:!1,data:!1,networkChanged:!1,notification:!1}},this._initializeStateAsync(),this.networkVersion=null,this.isMetaMask=!0,this._sendSync=this._sendSync.bind(this),this.enable=this.enable.bind(this),this.send=this.send.bind(this),this.sendAsync=this.sendAsync.bind(this),this._warnOfDeprecation=this._warnOfDeprecation.bind(this),this._metamask=this._getExperimentalApi(),this._jsonRpcConnection.events.on(\"notification\",e=>{const{method:t}=e;a.EMITTED_NOTIFICATIONS.includes(t)&&(this.emit(\"data\",e),this.emit(\"notification\",e.params.result))}),s)if(\"complete\"===document.readyState)o.sendSiteMetadata(this._rpcEngine,this._log);else{const e=()=>{o.sendSiteMetadata(this._rpcEngine,this._log),window.removeEventListener(\"DOMContentLoaded\",e)};window.addEventListener(\"DOMContentLoaded\",e)}}sendAsync(e,t){this._rpcRequest(e,t)}addListener(e,t){return this._warnOfDeprecation(e),super.addListener(e,t)}on(e,t){return this._warnOfDeprecation(e),super.on(e,t)}once(e,t){return this._warnOfDeprecation(e),super.once(e,t)}prependListener(e,t){return this._warnOfDeprecation(e),super.prependListener(e,t)}prependOnceListener(e,t){return this._warnOfDeprecation(e),super.prependOnceListener(e,t)}_handleDisconnect(e,t){super._handleDisconnect(e,t),this.networkVersion&&!e&&(this.networkVersion=null)}_warnOfDeprecation(e){var t;!1===(null===(t=this._sentWarnings)||void 0===t?void 0:t.events[e])&&(this._log.warn(s.default.warnings.events[e]),this._sentWarnings.events[e]=!0)}enable(){return this._sentWarnings.enable||(this._log.warn(s.default.warnings.enableDeprecation),this._sentWarnings.enable=!0),new Promise((e,t)=>{try{this._rpcRequest({method:\"eth_requestAccounts\",params:[]},a.getRpcPromiseCallback(e,t))}catch(e){t(e)}})}send(e,t){return this._sentWarnings.send||(this._log.warn(s.default.warnings.sendDeprecation),this._sentWarnings.send=!0),\"string\"!=typeof e||t&&!Array.isArray(t)?e&&\"object\"==typeof e&&\"function\"==typeof t?this._rpcRequest(e,t):this._sendSync(e):new Promise((r,n)=>{try{this._rpcRequest({method:e,params:t},a.getRpcPromiseCallback(r,n,!1))}catch(e){n(e)}})}_sendSync(e){let t;switch(e.method){case\"eth_accounts\":t=this.selectedAddress?[this.selectedAddress]:[];break;case\"eth_coinbase\":t=this.selectedAddress||null;break;case\"eth_uninstallFilter\":this._rpcRequest(e,a.NOOP),t=!0;break;case\"net_version\":t=this.networkVersion||null;break;default:throw new Error(s.default.errors.unsupportedSync(e.method))}return{id:e.id,jsonrpc:e.jsonrpc,result:t}}_getExperimentalApi(){return new Proxy({isUnlocked:async()=>(this._state.initialized||await new Promise(e=>{this.on(\"_initialized\",()=>e())}),this._state.isUnlocked),requestBatch:async e=>{if(!Array.isArray(e))throw i.ethErrors.rpc.invalidRequest({message:\"Batch requests must be made with an array of request objects.\",data:e});return new Promise((t,r)=>{this._rpcRequest(e,a.getRpcPromiseCallback(t,r))})}},{get:(e,t,...r)=>(this._sentWarnings.experimentalMethods||(this._log.warn(s.default.warnings.experimentalMethods),this._sentWarnings.experimentalMethods=!0),Reflect.get(e,t,...r))})}_handleChainChanged({chainId:e,networkVersion:t}={}){super._handleChainChanged({chainId:e,networkVersion:t}),this._state.isConnected&&t!==this.networkVersion&&(this.networkVersion=t,this._state.initialized&&this.emit(\"networkChanged\",this.networkVersion))}}t.MetaMaskInpageProvider=c},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.StreamProvider=t.AbstractStreamProvider=void 0;const i=n(r(31)),o=r(67),s=r(68),a=n(r(33)),u=n(r(10)),c=r(5),l=r(20);class f extends l.BaseProvider{constructor(e,{jsonRpcStreamName:t,logger:r,maxEventListeners:n,rpcMiddleware:l}){if(super({logger:r,maxEventListeners:n,rpcMiddleware:l}),!o.duplex(e))throw new Error(u.default.errors.invalidDuplexStream());this._handleStreamDisconnect=this._handleStreamDisconnect.bind(this);const f=new i.default;a.default(e,f,e,this._handleStreamDisconnect.bind(this,\"Dekey\")),this._jsonRpcConnection=s.createStreamMiddleware(),a.default(this._jsonRpcConnection.stream,f.createStream(t),this._jsonRpcConnection.stream,this._handleStreamDisconnect.bind(this,\"Dekey RpcProvider\")),this._rpcEngine.push(this._jsonRpcConnection.middleware),this._jsonRpcConnection.events.on(\"notification\",t=>{const{method:r,params:n}=t;\"dekey_accountsChanged\"===r?this._handleAccountsChanged(n):\"dekey_unlockStateChanged\"===r?this._handleUnlockStateChanged(n):\"dekey_chainChanged\"===r?this._handleChainChanged(n):c.EMITTED_NOTIFICATIONS.includes(r)?this.emit(\"message\",{type:r,data:n}):\"DEKEY_STREAM_FAILURE\"===r&&e.destroy(new Error(u.default.errors.permanentlyDisconnected()))})}async _initializeStateAsync(){let e;try{e=await this.request({method:\"dekey_getProviderState\"})}catch(e){this._log.error(\"Dekey: Failed to get initial state. Please report this bug.\",e)}this._initializeState(e)}_handleStreamDisconnect(e,t){let r=\\`Dekey: Lost connection to \"\\${e}\".\\`;(null==t?void 0:t.stack)&&(r+=\"\\\\n\"+t.stack),this._log.warn(r),this.listenerCount(\"error\")>0&&this.emit(\"error\",r),this._handleDisconnect(!1,t?t.message:void 0)}_handleChainChanged({chainId:e,networkVersion:t}={}){c.isValidChainId(e)&&c.isValidNetworkVersion(t)?\"loading\"===t?this._handleDisconnect(!0):super._handleChainChanged({chainId:e}):this._log.error(u.default.errors.invalidNetworkParams(),{chainId:e,networkVersion:t})}}t.AbstractStreamProvider=f;t.StreamProvider=class extends f{async initialize(){return this._initializeStateAsync()}}},function(e,t,r){var n=r(65);function i(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function o(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||\"Function wrapped with \\`once\\`\";return t.onceError=r+\" shouldn't be called more than once\",t.called=!1,t}e.exports=n(i),e.exports.strict=n(o),i.proto=i((function(){Object.defineProperty(Function.prototype,\"once\",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,\"onceStrict\",{value:function(){return o(this)},configurable:!0})}))},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.BaseProvider=void 0;const i=n(r(12)),o=r(9),s=n(r(40)),a=r(22),u=n(r(10)),c=r(5);class l extends i.default{constructor({logger:e=console,maxEventListeners:t=100,rpcMiddleware:r=[]}={}){super(),this._log=e,this.setMaxListeners(t),this._state=Object.assign({},l._defaultState),this.selectedAddress=null,this.chainId=null,this._handleAccountsChanged=this._handleAccountsChanged.bind(this),this._handleConnect=this._handleConnect.bind(this),this._handleChainChanged=this._handleChainChanged.bind(this),this._handleDisconnect=this._handleDisconnect.bind(this),this._handleUnlockStateChanged=this._handleUnlockStateChanged.bind(this),this._rpcRequest=this._rpcRequest.bind(this),this.request=this.request.bind(this);const n=new a.JsonRpcEngine;r.forEach(e=>n.push(e)),this._rpcEngine=n}isConnected(){return this._state.isConnected}async request(e){if(!e||\"object\"!=typeof e||Array.isArray(e))throw o.ethErrors.rpc.invalidRequest({message:u.default.errors.invalidRequestArgs(),data:e});const{method:t,params:r}=e;if(\"string\"!=typeof t||0===t.length)throw o.ethErrors.rpc.invalidRequest({message:u.default.errors.invalidRequestMethod(),data:e});if(void 0!==r&&!Array.isArray(r)&&(\"object\"!=typeof r||null===r))throw o.ethErrors.rpc.invalidRequest({message:u.default.errors.invalidRequestParams(),data:e});return new Promise((e,n)=>{this._rpcRequest({method:t,params:r},c.getRpcPromiseCallback(e,n))})}_initializeState(e){if(!0===this._state.initialized)throw new Error(\"Provider already initialized.\");if(e){const{accounts:t,chainId:r,isUnlocked:n,networkVersion:i}=e;this._handleConnect(r),this._handleChainChanged({chainId:r,networkVersion:i}),this._handleUnlockStateChanged({accounts:t,isUnlocked:n}),this._handleAccountsChanged(t)}this._state.initialized=!0,this.emit(\"_initialized\")}_rpcRequest(e,t){let r=t;return Array.isArray(e)||(e.jsonrpc||(e.jsonrpc=\"2.0\"),\"eth_accounts\"!==e.method&&\"eth_requestAccounts\"!==e.method||(r=(r,n)=>{this._handleAccountsChanged(n.result||[],\"eth_accounts\"===e.method),t(r,n)})),this._rpcEngine.handle(e,r)}_handleConnect(e){this._state.isConnected||(this._state.isConnected=!0,this.emit(\"connect\",{chainId:e}),this._log.debug(u.default.info.connected(e)))}_handleDisconnect(e,t){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!e){let r;this._state.isConnected=!1,e?(r=new o.EthereumRpcError(1013,t||u.default.errors.disconnected()),this._log.debug(r)):(r=new o.EthereumRpcError(1011,t||u.default.errors.permanentlyDisconnected()),this._log.error(r),this.chainId=null,this._state.accounts=null,this.selectedAddress=null,this._state.isUnlocked=!1,this._state.isPermanentlyDisconnected=!0),this.emit(\"disconnect\",r)}}_handleChainChanged({chainId:e}={}){c.isValidChainId(e)?(this._handleConnect(e),this.chainId=e,this._state.initialized&&this.emit(\"chainChanged\",this.chainId)):this._log.error(u.default.errors.invalidNetworkParams(),{chainId:e})}_handleAccountsChanged(e,t=!1){let r=e;Array.isArray(e)||(this._log.error(\"Dekey: Received invalid accounts parameter. Please report this bug.\",e),r=[]);for(const t of e)if(\"string\"!=typeof t){this._log.error(\"Dekey: Received non-string account. Please report this bug.\",e),r=[];break}s.default(this._state.accounts,r)||(t&&null!==this._state.accounts&&this._log.error(\"Dekey: 'eth_accounts' unexpectedly updated accounts. Please report this bug.\",r),this._state.accounts=r,this.selectedAddress!==r[0]&&(this.selectedAddress=r[0]||null),this._state.initialized&&this.emit(\"accountsChanged\",r))}_handleUnlockStateChanged({accounts:e,isUnlocked:t}={}){\"boolean\"==typeof t?t!==this._state.isUnlocked&&(this._state.isUnlocked=t,this._handleAccountsChanged(e||[])):this._log.error(\"Dekey: Received invalid isUnlocked parameter. Please report this bug.\")}}t.BaseProvider=l,l._defaultState={accounts:null,isConnected:!1,isUnlocked:!1,initialized:!1,isPermanentlyDisconnected:!1}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.serializeError=t.isValidCode=t.getMessageFromCode=t.JSON_RPC_SERVER_ERROR_MESSAGE=void 0;const n=r(14),i=r(13),o=n.errorCodes.rpc.internal,s={code:o,message:a(o)};function a(e,r=\"Unspecified error message. This is a bug, please report it.\"){if(Number.isInteger(e)){const r=e.toString();if(f(n.errorValues,r))return n.errorValues[r].message;if(c(e))return t.JSON_RPC_SERVER_ERROR_MESSAGE}return r}function u(e){if(!Number.isInteger(e))return!1;const t=e.toString();return!!n.errorValues[t]||!!c(e)}function c(e){return e>=-32099&&e<=-32e3}function l(e){return e&&\"object\"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function f(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.JSON_RPC_SERVER_ERROR_MESSAGE=\"Unspecified server error.\",t.getMessageFromCode=a,t.isValidCode=u,t.serializeError=function(e,{fallbackError:t=s,shouldIncludeStack:r=!1}={}){var n,o;if(!t||!Number.isInteger(t.code)||\"string\"!=typeof t.message)throw new Error(\"Must provide fallback error with integer number code and string message.\");if(e instanceof i.EthereumRpcError)return e.serialize();const c={};if(e&&\"object\"==typeof e&&!Array.isArray(e)&&f(e,\"code\")&&u(e.code)){const t=e;c.code=t.code,t.message&&\"string\"==typeof t.message?(c.message=t.message,f(t,\"data\")&&(c.data=t.data)):(c.message=a(c.code),c.data={originalError:l(e)})}else{c.code=t.code;const r=null===(n=e)||void 0===n?void 0:n.message;c.message=r&&\"string\"==typeof r?r:t.message,c.data={originalError:l(e)}}const d=null===(o=e)||void 0===o?void 0:o.stack;return r&&e&&d&&\"string\"==typeof d&&(c.stack=d),c}},function(e,t,r){\"use strict\";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)\"default\"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,\"__esModule\",{value:!0}),i(r(41),t),i(r(42),t),i(r(43),t),i(r(23),t),i(r(24),t),i(r(44),t)},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.getUniqueId=void 0;let n=Math.floor(4294967295*Math.random());t.getUniqueId=function(){return n=(n+1)%4294967295,n}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.JsonRpcEngine=void 0;const i=n(r(12)),o=r(9);class s extends i.default{constructor(){super(),this._middleware=[]}push(e){this._middleware.push(e)}handle(e,t){if(t&&\"function\"!=typeof t)throw new Error('\"callback\" must be a function if provided.');return Array.isArray(e)?t?this._handleBatch(e,t):this._handleBatch(e):t?this._handle(e,t):this._promiseHandle(e)}asMiddleware(){return async(e,t,r,n)=>{try{const[i,o,a]=await s._runAllMiddleware(e,t,this._middleware);return o?(await s._runReturnHandlers(a),n(i)):r(async e=>{try{await s._runReturnHandlers(a)}catch(t){return e(t)}return e()})}catch(e){return n(e)}}}async _handleBatch(e,t){try{const r=await Promise.all(e.map(this._promiseHandle.bind(this)));return t?t(null,r):r}catch(e){if(t)return t(e);throw e}}_promiseHandle(e){return new Promise(t=>{this._handle(e,(e,r)=>{t(r)})})}async _handle(e,t){if(!e||Array.isArray(e)||\"object\"!=typeof e){const r=new o.EthereumRpcError(o.errorCodes.rpc.invalidRequest,\"Requests must be plain objects. Received: \"+typeof e,{request:e});return t(r,{id:void 0,jsonrpc:\"2.0\",error:r})}if(\"string\"!=typeof e.method){const r=new o.EthereumRpcError(o.errorCodes.rpc.invalidRequest,\"Must specify a string method. Received: \"+typeof e.method,{request:e});return t(r,{id:e.id,jsonrpc:\"2.0\",error:r})}const r=Object.assign({},e),n={id:r.id,jsonrpc:r.jsonrpc};let i=null;try{await this._processRequest(r,n)}catch(e){i=e}return i&&(delete n.result,n.error||(n.error=o.serializeError(i))),t(i,n)}async _processRequest(e,t){const[r,n,i]=await s._runAllMiddleware(e,t,this._middleware);if(s._checkForCompletion(e,t,n),await s._runReturnHandlers(i),r)throw r}static async _runAllMiddleware(e,t,r){const n=[];let i=null,o=!1;for(const a of r)if([i,o]=await s._runMiddleware(e,t,a,n),o)break;return[i,o,n.reverse()]}static _runMiddleware(e,t,r,n){return new Promise(i=>{const s=e=>{const r=e||t.error;r&&(t.error=o.serializeError(r)),i([r,!0])},u=r=>{t.error?s(t.error):(r&&(\"function\"!=typeof r&&s(new o.EthereumRpcError(o.errorCodes.rpc.internal,\\`JsonRpcEngine: \"next\" return handlers must be functions. Received \"\\${typeof r}\" for request:\\\\n\\${a(e)}\\`,{request:e})),n.push(r)),i([null,!1]))};try{r(e,t,u,s)}catch(e){s(e)}})}static async _runReturnHandlers(e){for(const t of e)await new Promise((e,r)=>{t(t=>t?r(t):e())})}static _checkForCompletion(e,t,r){if(!(\"result\"in t)&&!(\"error\"in t))throw new o.EthereumRpcError(o.errorCodes.rpc.internal,\"JsonRpcEngine: Response has no error or result for request:\\\\n\"+a(e),{request:e});if(!r)throw new o.EthereumRpcError(o.errorCodes.rpc.internal,\"JsonRpcEngine: Nothing ended request:\\\\n\"+a(e),{request:e})}}function a(e){return JSON.stringify(e,null,2)}t.JsonRpcEngine=s},function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return\"[object Array]\"==r.call(e)}},function(e,t,r){\"use strict\";(function(t,n){var i=r(11);e.exports=v;var o,s=r(25);v.ReadableState=b;r(8).EventEmitter;var a=function(e,t){return e.listeners(t).length},u=r(27),c=r(15).Buffer,l=(void 0!==t?t:\"undefined\"!=typeof window?window:\"undefined\"!=typeof self?self:{}).Uint8Array||function(){};var f=Object.create(r(7));f.inherits=r(4);var d=r(50),h=void 0;h=d&&d.debuglog?d.debuglog(\"stream\"):function(){};var p,g=r(51),m=r(28);f.inherits(v,u);var y=[\"error\",\"close\",\"destroy\",\"pause\",\"resume\"];function b(e,t){e=e||{};var n=t instanceof(o=o||r(2));this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var i=e.highWaterMark,s=e.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(s||0===s)?s:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new g,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||\"utf8\",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(p||(p=r(29).StringDecoder),this.decoder=new p(e.encoding),this.encoding=e.encoding)}function v(e){if(o=o||r(2),!(this instanceof v))return new v(e);this._readableState=new b(e,this),this.readable=!0,e&&(\"function\"==typeof e.read&&(this._read=e.read),\"function\"==typeof e.destroy&&(this._destroy=e.destroy)),u.call(this)}function w(e,t,r,n,i){var o,s=e._readableState;null===t?(s.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,S(e)}(e,s)):(i||(o=function(e,t){var r;n=t,c.isBuffer(n)||n instanceof l||\"string\"==typeof t||void 0===t||e.objectMode||(r=new TypeError(\"Invalid non-string/buffer chunk\"));var n;return r}(s,t)),o?e.emit(\"error\",o):s.objectMode||t&&t.length>0?(\"string\"==typeof t||s.objectMode||Object.getPrototypeOf(t)===c.prototype||(t=function(e){return c.from(e)}(t)),n?s.endEmitted?e.emit(\"error\",new Error(\"stream.unshift() after end event\")):_(e,s,t,!0):s.ended?e.emit(\"error\",new Error(\"stream.push() after EOF\")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?_(e,s,t,!1):O(e,s)):_(e,s,t,!1))):n||(s.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(s)}function _(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit(\"data\",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&S(e)),O(e,t)}Object.defineProperty(v.prototype,\"destroyed\",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),v.prototype.destroy=m.destroy,v.prototype._undestroy=m.undestroy,v.prototype._destroy=function(e,t){this.push(null),t(e)},v.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:\"string\"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=c.from(e,t),t=\"\"),r=!0),w(this,e,t,!1,r)},v.prototype.unshift=function(e){return w(this,e,null,!0,!1)},v.prototype.isPaused=function(){return!1===this._readableState.flowing},v.prototype.setEncoding=function(e){return p||(p=r(29).StringDecoder),this._readableState.decoder=new p(e),this._readableState.encoding=e,this};function E(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=8388608?e=8388608:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function S(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(h(\"emitReadable\",t.flowing),t.emittedReadable=!0,t.sync?i.nextTick(P,e):P(e))}function P(e){h(\"emit readable\"),e.emit(\"readable\"),j(e)}function O(e,t){t.readingMore||(t.readingMore=!0,i.nextTick(M,e,t))}function M(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(h(\"maybeReadMore read 0\"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function R(e){h(\"readable nexttick read 0\"),e.read(0)}function k(e,t){t.reading||(h(\"resume read 0\"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit(\"resume\"),j(e),t.flowing&&!t.reading&&e.read(0)}function j(e){var t=e._readableState;for(h(\"flow\",t.flowing);t.flowing&&null!==e.read(););}function x(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(\"\"):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;e-=i.length;for(;r=r.next;){var o=r.data,s=e>o.length?o.length:e;if(s===o.length?i+=o:i+=o.slice(0,e),0===(e-=s)){s===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(s));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=c.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;for(;n=n.next;){var o=n.data,s=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,s),0===(e-=s)){s===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(s));break}++i}return t.length-=i,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function A(e){var t=e._readableState;if(t.length>0)throw new Error('\"endReadable()\" called on non-empty stream');t.endEmitted||(t.ended=!0,i.nextTick(C,t,e))}function C(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit(\"end\"))}function T(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}v.prototype.read=function(e){h(\"read\",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return h(\"read: emitReadable\",t.length,t.ended),0===t.length&&t.ended?A(this):S(this),null;if(0===(e=E(e,t))&&t.ended)return 0===t.length&&A(this),null;var n,i=t.needReadable;return h(\"need readable\",i),(0===t.length||t.length-e<t.highWaterMark)&&h(\"length less than watermark\",i=!0),t.ended||t.reading?h(\"reading or ended\",i=!1):i&&(h(\"do read\"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=E(r,t))),null===(n=e>0?x(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&A(this)),null!==n&&this.emit(\"data\",n),n},v.prototype._read=function(e){this.emit(\"error\",new Error(\"_read() is not implemented\"))},v.prototype.pipe=function(e,t){var r=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=e;break;case 1:o.pipes=[o.pipes,e];break;default:o.pipes.push(e)}o.pipesCount+=1,h(\"pipe count=%d opts=%j\",o.pipesCount,t);var u=(!t||!1!==t.end)&&e!==n.stdout&&e!==n.stderr?l:v;function c(t,n){h(\"onunpipe\"),t===r&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,h(\"cleanup\"),e.removeListener(\"close\",y),e.removeListener(\"finish\",b),e.removeListener(\"drain\",f),e.removeListener(\"error\",m),e.removeListener(\"unpipe\",c),r.removeListener(\"end\",l),r.removeListener(\"end\",v),r.removeListener(\"data\",g),d=!0,!o.awaitDrain||e._writableState&&!e._writableState.needDrain||f())}function l(){h(\"onend\"),e.end()}o.endEmitted?i.nextTick(u):r.once(\"end\",u),e.on(\"unpipe\",c);var f=function(e){return function(){var t=e._readableState;h(\"pipeOnDrain\",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,\"data\")&&(t.flowing=!0,j(e))}}(r);e.on(\"drain\",f);var d=!1;var p=!1;function g(t){h(\"ondata\"),p=!1,!1!==e.write(t)||p||((1===o.pipesCount&&o.pipes===e||o.pipesCount>1&&-1!==T(o.pipes,e))&&!d&&(h(\"false write response, pause\",o.awaitDrain),o.awaitDrain++,p=!0),r.pause())}function m(t){h(\"onerror\",t),v(),e.removeListener(\"error\",m),0===a(e,\"error\")&&e.emit(\"error\",t)}function y(){e.removeListener(\"finish\",b),v()}function b(){h(\"onfinish\"),e.removeListener(\"close\",y),v()}function v(){h(\"unpipe\"),r.unpipe(e)}return r.on(\"data\",g),function(e,t,r){if(\"function\"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?s(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,\"error\",m),e.once(\"close\",y),e.once(\"finish\",b),e.emit(\"pipe\",r),o.flowing||(h(\"pipe resume\"),r.resume()),e},v.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit(\"unpipe\",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit(\"unpipe\",this,{hasUnpiped:!1});return this}var s=T(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit(\"unpipe\",this,r)),this},v.prototype.on=function(e,t){var r=u.prototype.on.call(this,e,t);if(\"data\"===e)!1!==this._readableState.flowing&&this.resume();else if(\"readable\"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&S(this):i.nextTick(R,this))}return r},v.prototype.addListener=v.prototype.on,v.prototype.resume=function(){var e=this._readableState;return e.flowing||(h(\"resume\"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,i.nextTick(k,e,t))}(this,e)),this},v.prototype.pause=function(){return h(\"call pause flowing=%j\",this._readableState.flowing),!1!==this._readableState.flowing&&(h(\"pause\"),this._readableState.flowing=!1,this.emit(\"pause\")),this},v.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on(\"end\",(function(){if(h(\"wrapped end\"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on(\"data\",(function(i){(h(\"wrapped data\"),r.decoder&&(i=r.decoder.write(i)),r.objectMode&&null==i)||(r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause()))})),e)void 0===this[i]&&\"function\"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<y.length;o++)e.on(y[o],this.emit.bind(this,y[o]));return this._read=function(t){h(\"wrapped _read\",t),n&&(n=!1,e.resume())},this},Object.defineProperty(v.prototype,\"readableHighWaterMark\",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),v._fromList=x}).call(this,r(3),r(1))},function(e,t,r){e.exports=r(8).EventEmitter},function(e,t,r){\"use strict\";var n=r(11);function i(e,t){e.emit(\"error\",t)}e.exports={destroy:function(e,t){var r=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,n.nextTick(i,this,e)):n.nextTick(i,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?r._writableState?r._writableState.errorEmitted||(r._writableState.errorEmitted=!0,n.nextTick(i,r,e)):n.nextTick(i,r,e):t&&t(e)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(e,t,r){\"use strict\";var n=r(56).Buffer,i=n.isEncoding||function(e){switch((e=\"\"+e)&&e.toLowerCase()){case\"hex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":case\"raw\":return!0;default:return!1}};function o(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return\"utf8\";for(var t;;)switch(e){case\"utf8\":case\"utf-8\":return\"utf8\";case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return\"utf16le\";case\"latin1\":case\"binary\":return\"latin1\";case\"base64\":case\"ascii\":case\"hex\":return e;default:if(t)return;e=(\"\"+e).toLowerCase(),t=!0}}(e);if(\"string\"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error(\"Unknown encoding: \"+e);return t||e}(e),this.encoding){case\"utf16le\":this.text=u,this.end=c,t=4;break;case\"utf8\":this.fillLast=a,t=4;break;case\"base64\":this.text=l,this.end=f,t=3;break;default:return this.write=d,void(this.end=h)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function a(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,\"�\";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,\"�\";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,\"�\"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function u(e,t){if((e.length-t)%2==0){var r=e.toString(\"utf16le\",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString(\"utf16le\",t,e.length-1)}function c(e){var t=e&&e.length?this.write(e):\"\";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString(\"utf16le\",0,r)}return t}function l(e,t){var r=(e.length-t)%3;return 0===r?e.toString(\"base64\",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString(\"base64\",t,e.length-r))}function f(e){var t=e&&e.length?this.write(e):\"\";return this.lastNeed?t+this.lastChar.toString(\"base64\",0,3-this.lastNeed):t}function d(e){return e.toString(this.encoding)}function h(e){return e&&e.length?this.write(e):\"\"}t.StringDecoder=o,o.prototype.write=function(e){if(0===e.length)return\"\";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return\"\";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||\"\"},o.prototype.end=function(e){var t=e&&e.length?this.write(e):\"\";return this.lastNeed?t+\"�\":t},o.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if((i=s(t[n]))>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if((i=s(t[n]))>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString(\"utf8\",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString(\"utf8\",t,n)},o.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},function(e,t,r){\"use strict\";e.exports=s;var n=r(2),i=Object.create(r(7));function o(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit(\"error\",new Error(\"write callback called multiple times\"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function s(e){if(!(this instanceof s))return new s(e);n.call(this,e),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&(\"function\"==typeof e.transform&&(this._transform=e.transform),\"function\"==typeof e.flush&&(this._flush=e.flush)),this.on(\"prefinish\",a)}function a(){var e=this;\"function\"==typeof this._flush?this._flush((function(t,r){u(e,t,r)})):u(this,null,null)}function u(e,t,r){if(t)return e.emit(\"error\",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error(\"Calling transform done when ws.length != 0\");if(e._transformState.transforming)throw new Error(\"Calling transform done when still transforming\");return e.push(null)}i.inherits=r(4),i.inherits(s,n),s.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},s.prototype._transform=function(e,t,r){throw new Error(\"_transform() is not implemented\")},s.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},s.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},s.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,(function(e){t(e),r.emit(\"close\")}))}},function(e,t,r){\"use strict\";const n=r(64);e.exports=n.ObjectMultiplex},function(e,t,r){(function(t){var n=r(19),i=function(){},o=function(e,r,s){if(\"function\"==typeof r)return o(e,null,r);r||(r={}),s=n(s||i);var a=e._writableState,u=e._readableState,c=r.readable||!1!==r.readable&&e.readable,l=r.writable||!1!==r.writable&&e.writable,f=!1,d=function(){e.writable||h()},h=function(){l=!1,c||s.call(e)},p=function(){c=!1,l||s.call(e)},g=function(t){s.call(e,t?new Error(\"exited with error code: \"+t):null)},m=function(t){s.call(e,t)},y=function(){t.nextTick(b)},b=function(){if(!f)return(!c||u&&u.ended&&!u.destroyed)&&(!l||a&&a.ended&&!a.destroyed)?void 0:s.call(e,new Error(\"premature close\"))},v=function(){e.req.on(\"finish\",h)};return!function(e){return e.setHeader&&\"function\"==typeof e.abort}(e)?l&&!a&&(e.on(\"end\",d),e.on(\"close\",d)):(e.on(\"complete\",h),e.on(\"abort\",y),e.req?v():e.on(\"request\",v)),function(e){return e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length}(e)&&e.on(\"exit\",g),e.on(\"end\",p),e.on(\"finish\",h),!1!==r.error&&e.on(\"error\",m),e.on(\"close\",y),function(){f=!0,e.removeListener(\"complete\",h),e.removeListener(\"abort\",y),e.removeListener(\"request\",v),e.req&&e.req.removeListener(\"finish\",h),e.removeListener(\"end\",d),e.removeListener(\"close\",d),e.removeListener(\"finish\",h),e.removeListener(\"exit\",g),e.removeListener(\"end\",p),e.removeListener(\"error\",m),e.removeListener(\"close\",y)}};e.exports=o}).call(this,r(1))},function(e,t,r){(function(t){var n=r(19),i=r(32),o=r(71),s=function(){},a=/^v?\\\\.0/.test(t.version),u=function(e){return\"function\"==typeof e},c=function(e,t,r,c){c=n(c);var l=!1;e.on(\"close\",(function(){l=!0})),i(e,{readable:t,writable:r},(function(e){if(e)return c(e);l=!0,c()}));var f=!1;return function(t){if(!l&&!f)return f=!0,function(e){return!!a&&(!!o&&((e instanceof(o.ReadStream||s)||e instanceof(o.WriteStream||s))&&u(e.close)))}(e)?e.close(s):function(e){return e.setHeader&&u(e.abort)}(e)?e.abort():u(e.destroy)?e.destroy():void c(t||new Error(\"stream was destroyed\"))}},l=function(e){e()},f=function(e,t){return e.pipe(t)};e.exports=function(){var e,t=Array.prototype.slice.call(arguments),r=u(t[t.length-1]||s)&&t.pop()||s;if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Error(\"pump requires two streams per minimum\");var n=t.map((function(i,o){var s=o<t.length-1;return c(i,s,o>0,(function(t){e||(e=t),t&&n.forEach(l),s||(n.forEach(l),r(e))}))}));return t.reduce(f)}}).call(this,r(1))},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.shimWeb3=void 0,t.shimWeb3=function(e,t=console){let r=!1,n=!1;if(!window.web3){const i=\"__isMetaMaskShim__\";let o={currentProvider:e};Object.defineProperty(o,i,{value:!0,enumerable:!0,configurable:!1,writable:!1}),o=new Proxy(o,{get:(o,s,...a)=>(\"currentProvider\"!==s||r?\"currentProvider\"===s||s===i||n||(n=!0,t.error(\"Dekey no longer injects web3.\"),e.request({method:\"metamask_logWeb3ShimUsage\"}).catch(e=>{t.debug(\"Dekey: Failed to log web3 shim usage.\",e)})):(r=!0,t.warn(\"You are accessing the Dekey window.web3.currentProvider shim. This property is deprecated; use window.ethereum instead.\")),Reflect.get(o,s,...a)),set:(...e)=>(t.warn(\"You are accessing the Dekey window.web3 shim. This object is deprecated; use window.ethereum instead.\"),Reflect.set(...e))}),Object.defineProperty(window,\"web3\",{value:o,enumerable:!1,configurable:!0,writable:!0})}}},function(e,t,r){(function(e){var n=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++)r[t[n]]=Object.getOwnPropertyDescriptor(e,t[n]);return r},i=/%[sdj%]/g;t.format=function(e){if(!y(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(a(arguments[r]));return t.join(\" \")}r=1;for(var n=arguments,o=n.length,s=String(e).replace(i,(function(e){if(\"%%\"===e)return\"%\";if(r>=o)return e;switch(e){case\"%s\":return String(n[r++]);case\"%d\":return Number(n[r++]);case\"%j\":try{return JSON.stringify(n[r++])}catch(e){return\"[Circular]\"}default:return e}})),u=n[r];r<o;u=n[++r])g(u)||!w(u)?s+=\" \"+u:s+=\" \"+a(u);return s},t.deprecate=function(r,n){if(void 0!==e&&!0===e.noDeprecation)return r;if(void 0===e)return function(){return t.deprecate(r,n).apply(this,arguments)};var i=!1;return function(){if(!i){if(e.throwDeprecation)throw new Error(n);e.traceDeprecation?console.trace(n):console.error(n),i=!0}return r.apply(this,arguments)}};var o,s={};function a(e,r){var n={seen:[],stylize:c};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),p(r)?n.showHidden=r:r&&t._extend(n,r),b(n.showHidden)&&(n.showHidden=!1),b(n.depth)&&(n.depth=2),b(n.colors)&&(n.colors=!1),b(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=u),l(n,e,n.depth)}function u(e,t){var r=a.styles[t];return r?\"\\x1B[\"+a.colors[r][0]+\"m\"+e+\"\\x1B[\"+a.colors[r][1]+\"m\":e}function c(e,t){return e}function l(e,r,n){if(e.customInspect&&r&&S(r.inspect)&&r.inspect!==t.inspect&&(!r.constructor||r.constructor.prototype!==r)){var i=r.inspect(n,e);return y(i)||(i=l(e,i,n)),i}var o=function(e,t){if(b(t))return e.stylize(\"undefined\",\"undefined\");if(y(t)){var r=\"'\"+JSON.stringify(t).replace(/^\"|\"$/g,\"\").replace(/'/g,\"\\\\\\\\'\").replace(/\\\\\\\\\"/g,'\"')+\"'\";return e.stylize(r,\"string\")}if(m(t))return e.stylize(\"\"+t,\"number\");if(p(t))return e.stylize(\"\"+t,\"boolean\");if(g(t))return e.stylize(\"null\",\"null\")}(e,r);if(o)return o;var s=Object.keys(r),a=function(e){var t={};return e.forEach((function(e,r){t[e]=!0})),t}(s);if(e.showHidden&&(s=Object.getOwnPropertyNames(r)),E(r)&&(s.indexOf(\"message\")>=0||s.indexOf(\"description\")>=0))return f(r);if(0===s.length){if(S(r)){var u=r.name?\": \"+r.name:\"\";return e.stylize(\"[Function\"+u+\"]\",\"special\")}if(v(r))return e.stylize(RegExp.prototype.toString.call(r),\"regexp\");if(_(r))return e.stylize(Date.prototype.toString.call(r),\"date\");if(E(r))return f(r)}var c,w=\"\",P=!1,O=[\"{\",\"}\"];(h(r)&&(P=!0,O=[\"[\",\"]\"]),S(r))&&(w=\" [Function\"+(r.name?\": \"+r.name:\"\")+\"]\");return v(r)&&(w=\" \"+RegExp.prototype.toString.call(r)),_(r)&&(w=\" \"+Date.prototype.toUTCString.call(r)),E(r)&&(w=\" \"+f(r)),0!==s.length||P&&0!=r.length?n<0?v(r)?e.stylize(RegExp.prototype.toString.call(r),\"regexp\"):e.stylize(\"[Object]\",\"special\"):(e.seen.push(r),c=P?function(e,t,r,n,i){for(var o=[],s=0,a=t.length;s<a;++s)k(t,String(s))?o.push(d(e,t,r,n,String(s),!0)):o.push(\"\");return i.forEach((function(i){i.match(/^\\\\d+$/)||o.push(d(e,t,r,n,i,!0))})),o}(e,r,n,a,s):s.map((function(t){return d(e,r,n,a,t,P)})),e.seen.pop(),function(e,t,r){if(e.reduce((function(e,t){return t.indexOf(\"\\\\n\")>=0&&0,e+t.replace(/\\\\u001b\\\\[\\\\d\\\\d?m/g,\"\").length+1}),0)>60)return r[0]+(\"\"===t?\"\":t+\"\\\\n \")+\" \"+e.join(\",\\\\n  \")+\" \"+r[1];return r[0]+t+\" \"+e.join(\", \")+\" \"+r[1]}(c,w,O)):O[0]+w+O[1]}function f(e){return\"[\"+Error.prototype.toString.call(e)+\"]\"}function d(e,t,r,n,i,o){var s,a,u;if((u=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?a=u.set?e.stylize(\"[Getter/Setter]\",\"special\"):e.stylize(\"[Getter]\",\"special\"):u.set&&(a=e.stylize(\"[Setter]\",\"special\")),k(n,i)||(s=\"[\"+i+\"]\"),a||(e.seen.indexOf(u.value)<0?(a=g(r)?l(e,u.value,null):l(e,u.value,r-1)).indexOf(\"\\\\n\")>-1&&(a=o?a.split(\"\\\\n\").map((function(e){return\"  \"+e})).join(\"\\\\n\").substr(2):\"\\\\n\"+a.split(\"\\\\n\").map((function(e){return\"   \"+e})).join(\"\\\\n\")):a=e.stylize(\"[Circular]\",\"special\")),b(s)){if(o&&i.match(/^\\\\d+$/))return a;(s=JSON.stringify(\"\"+i)).match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,\"name\")):(s=s.replace(/'/g,\"\\\\\\\\'\").replace(/\\\\\\\\\"/g,'\"').replace(/(^\"|\"$)/g,\"'\"),s=e.stylize(s,\"string\"))}return s+\": \"+a}function h(e){return Array.isArray(e)}function p(e){return\"boolean\"==typeof e}function g(e){return null===e}function m(e){return\"number\"==typeof e}function y(e){return\"string\"==typeof e}function b(e){return void 0===e}function v(e){return w(e)&&\"[object RegExp]\"===P(e)}function w(e){return\"object\"==typeof e&&null!==e}function _(e){return w(e)&&\"[object Date]\"===P(e)}function E(e){return w(e)&&(\"[object Error]\"===P(e)||e instanceof Error)}function S(e){return\"function\"==typeof e}function P(e){return Object.prototype.toString.call(e)}function O(e){return e<10?\"0\"+e.toString(10):e.toString(10)}t.debuglog=function(r){if(b(o)&&(o=e.env.NODE_DEBUG||\"\"),r=r.toUpperCase(),!s[r])if(new RegExp(\"\\\\\\\\b\"+r+\"\\\\\\\\b\",\"i\").test(o)){var n=e.pid;s[r]=function(){var e=t.format.apply(t,arguments);console.error(\"%s %d: %s\",r,n,e)}}else s[r]=function(){};return s[r]},t.inspect=a,a.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},a.styles={special:\"cyan\",number:\"yellow\",boolean:\"yellow\",undefined:\"grey\",null:\"bold\",string:\"green\",date:\"magenta\",regexp:\"red\"},t.isArray=h,t.isBoolean=p,t.isNull=g,t.isNullOrUndefined=function(e){return null==e},t.isNumber=m,t.isString=y,t.isSymbol=function(e){return\"symbol\"==typeof e},t.isUndefined=b,t.isRegExp=v,t.isObject=w,t.isDate=_,t.isError=E,t.isFunction=S,t.isPrimitive=function(e){return null===e||\"boolean\"==typeof e||\"number\"==typeof e||\"string\"==typeof e||\"symbol\"==typeof e||void 0===e},t.isBuffer=r(75);var M=[\"Jan\",\"Feb\",\"Mar\",\"Apr\",\"May\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"];function R(){var e=new Date,t=[O(e.getHours()),O(e.getMinutes()),O(e.getSeconds())].join(\":\");return[e.getDate(),M[e.getMonth()],t].join(\" \")}function k(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.log=function(){console.log(\"%s - %s\",R(),t.format.apply(t,arguments))},t.inherits=r(76),t._extend=function(e,t){if(!t||!w(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e};var j=\"undefined\"!=typeof Symbol?Symbol(\"util.promisify.custom\"):void 0;function x(e,t){if(!e){var r=new Error(\"Promise was rejected with a falsy value\");r.reason=e,e=r}return t(e)}t.promisify=function(e){if(\"function\"!=typeof e)throw new TypeError('The \"original\" argument must be of type Function');if(j&&e[j]){var t;if(\"function\"!=typeof(t=e[j]))throw new TypeError('The \"util.promisify.custom\" argument must be of type Function');return Object.defineProperty(t,j,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,r,n=new Promise((function(e,n){t=e,r=n})),i=[],o=0;o<arguments.length;o++)i.push(arguments[o]);i.push((function(e,n){e?r(e):t(n)}));try{e.apply(this,i)}catch(e){r(e)}return n}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),j&&Object.defineProperty(t,j,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,n(e))},t.promisify.custom=j,t.callbackify=function(t){if(\"function\"!=typeof t)throw new TypeError('The \"original\" argument must be of type Function');function r(){for(var r=[],n=0;n<arguments.length;n++)r.push(arguments[n]);var i=r.pop();if(\"function\"!=typeof i)throw new TypeError(\"The last argument must be of type Function\");var o=this,s=function(){return i.apply(o,arguments)};t.apply(this,r).then((function(t){e.nextTick(s,null,t)}),(function(t){e.nextTick(x,t,s)}))}return Object.setPrototypeOf(r,Object.getPrototypeOf(t)),Object.defineProperties(r,n(t)),r}}).call(this,r(1))},function(e,t,r){var n=r(37),i=n.initializeProvider,o=n.shimWeb3,s=r(31),a=r(33),u=r(74),c=r(77),l=\"dekey-mobile-inpage\",f=\"dekey-mobile-provider\";function d(e,t){var r=\"DekeyContentscript - lost connection to \".concat(e);t&&(r+=\"\\\\n\".concat(t.stack)),console.log(r)}i({connectionStream:new c({name:l,target:\"dekey-mobile-contentscript\"}),shouldSendMetadata:!1,jsonRpcStreamName:f}),Object.defineProperty(window,\"_dekeySetupProvider\",{value:function(e){!function(e){var t=new c({name:\"dekey-mobile-contentscript\",target:l}),r=new u({name:\"dekey-mobile-contentscript\"},e),n=new s;n.setMaxListeners(25);var i=new s;i.setMaxListeners(25),a(n,t,n,(function(e){return d(\"Dekey Inpage Multiplex\",e)})),a(i,r,i,(function(e){d(\"Dekey Background Multiplex\",e),window.postMessage({target:l,data:{name:f,data:{jsonrpc:\"2.0\",method:\"DEKEY_STREAM_FAILURE\"}}},window.location.origin)})),h=f,p=n,g=i,m=p.createStream(h),y=g.createStream(h),a(m,y,m,(function(e){return d('Dekey muxed traffic for channel \"'.concat(h,'\" failed.'),e)})),o(window.ethereum);var h,p,g,m,y}(new URL(e).origin),delete window._dekeySetupProvider},configurable:!0,enumerable:!1,writable:!1})},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.StreamProvider=t.shimWeb3=t.setGlobalProvider=t.MetaMaskInpageProvider=t.MetaMaskInpageProviderStreamName=t.initializeProvider=t.createExternalExtensionProvider=t.BaseProvider=void 0;const n=r(20);Object.defineProperty(t,\"BaseProvider\",{enumerable:!0,get:function(){return n.BaseProvider}});const i=r(45);Object.defineProperty(t,\"createExternalExtensionProvider\",{enumerable:!0,get:function(){return i.createExternalExtensionProvider}});const o=r(73);Object.defineProperty(t,\"initializeProvider\",{enumerable:!0,get:function(){return o.initializeProvider}}),Object.defineProperty(t,\"setGlobalProvider\",{enumerable:!0,get:function(){return o.setGlobalProvider}});const s=r(17);Object.defineProperty(t,\"MetaMaskInpageProvider\",{enumerable:!0,get:function(){return s.MetaMaskInpageProvider}}),Object.defineProperty(t,\"MetaMaskInpageProviderStreamName\",{enumerable:!0,get:function(){return s.MetaMaskInpageProviderStreamName}});const a=r(34);Object.defineProperty(t,\"shimWeb3\",{enumerable:!0,get:function(){return a.shimWeb3}});const u=r(18);Object.defineProperty(t,\"StreamProvider\",{enumerable:!0,get:function(){return u.StreamProvider}})},function(e,t){e.exports=o,o.default=o,o.stable=u,o.stableStringify=u;var r=[],n=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(e,t,o,a){var u;void 0===a&&(a=i()),function e(t,r,n,i,o,a,u){var c;if(a+=1,\"object\"==typeof t&&null!==t){for(c=0;c<i.length;c++)if(i[c]===t)return void s(\"[Circular]\",t,r,o);if(void 0!==u.depthLimit&&a>u.depthLimit)return void s(\"[...]\",t,r,o);if(void 0!==u.edgesLimit&&n+1>u.edgesLimit)return void s(\"[...]\",t,r,o);if(i.push(t),Array.isArray(t))for(c=0;c<t.length;c++)e(t[c],c,c,i,t,a,u);else{var l=Object.keys(t);for(c=0;c<l.length;c++){var f=l[c];e(t[f],f,c,i,t,a,u)}}i.pop()}}(e,\"\",0,[],void 0,0,a);try{u=0===n.length?JSON.stringify(e,t,o):JSON.stringify(e,c(t),o)}catch(e){return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\")}finally{for(;0!==r.length;){var l=r.pop();4===l.length?Object.defineProperty(l[0],l[1],l[3]):l[0][l[1]]=l[2]}}return u}function s(e,t,i,o){var s=Object.getOwnPropertyDescriptor(o,i);void 0!==s.get?s.configurable?(Object.defineProperty(o,i,{value:e}),r.push([o,i,t,s])):n.push([t,i,e]):(o[i]=e,r.push([o,i,t]))}function a(e,t){return e<t?-1:e>t?1:0}function u(e,t,o,u){void 0===u&&(u=i());var l,f=function e(t,n,i,o,u,c,l){var f;if(c+=1,\"object\"==typeof t&&null!==t){for(f=0;f<o.length;f++)if(o[f]===t)return void s(\"[Circular]\",t,n,u);try{if(\"function\"==typeof t.toJSON)return}catch(e){return}if(void 0!==l.depthLimit&&c>l.depthLimit)return void s(\"[...]\",t,n,u);if(void 0!==l.edgesLimit&&i+1>l.edgesLimit)return void s(\"[...]\",t,n,u);if(o.push(t),Array.isArray(t))for(f=0;f<t.length;f++)e(t[f],f,f,o,t,c,l);else{var d={},h=Object.keys(t).sort(a);for(f=0;f<h.length;f++){var p=h[f];e(t[p],p,f,o,t,c,l),d[p]=t[p]}if(void 0===u)return d;r.push([u,n,t]),u[n]=d}o.pop()}}(e,\"\",0,[],void 0,0,u)||e;try{l=0===n.length?JSON.stringify(f,t,o):JSON.stringify(f,c(t),o)}catch(e){return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\")}finally{for(;0!==r.length;){var d=r.pop();4===d.length?Object.defineProperty(d[0],d[1],d[3]):d[0][d[1]]=d[2]}}return l}function c(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(n.length>0)for(var i=0;i<n.length;i++){var o=n[i];if(o[1]===t&&o[0]===r){r=o[2],n.splice(i,1);break}}return e.call(this,t,r)}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.ethErrors=void 0;const n=r(13),i=r(21),o=r(14);function s(e,t){const[r,o]=u(t);return new n.EthereumRpcError(e,r||i.getMessageFromCode(e),o)}function a(e,t){const[r,o]=u(t);return new n.EthereumProviderError(e,r||i.getMessageFromCode(e),o)}function u(e){if(e){if(\"string\"==typeof e)return[e];if(\"object\"==typeof e&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&\"string\"!=typeof t)throw new Error(\"Must specify string message.\");return[t||void 0,r]}}return[]}t.ethErrors={rpc:{parse:e=>s(o.errorCodes.rpc.parse,e),invalidRequest:e=>s(o.errorCodes.rpc.invalidRequest,e),invalidParams:e=>s(o.errorCodes.rpc.invalidParams,e),methodNotFound:e=>s(o.errorCodes.rpc.methodNotFound,e),internal:e=>s(o.errorCodes.rpc.internal,e),server:e=>{if(!e||\"object\"!=typeof e||Array.isArray(e))throw new Error(\"Ethereum RPC Server errors must provide single object argument.\");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('\"code\" must be an integer such that: -32099 <= code <= -32005');return s(t,e)},invalidInput:e=>s(o.errorCodes.rpc.invalidInput,e),resourceNotFound:e=>s(o.errorCodes.rpc.resourceNotFound,e),resourceUnavailable:e=>s(o.errorCodes.rpc.resourceUnavailable,e),transactionRejected:e=>s(o.errorCodes.rpc.transactionRejected,e),methodNotSupported:e=>s(o.errorCodes.rpc.methodNotSupported,e),limitExceeded:e=>s(o.errorCodes.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>a(o.errorCodes.provider.userRejectedRequest,e),unauthorized:e=>a(o.errorCodes.provider.unauthorized,e),unsupportedMethod:e=>a(o.errorCodes.provider.unsupportedMethod,e),disconnected:e=>a(o.errorCodes.provider.disconnected,e),chainDisconnected:e=>a(o.errorCodes.provider.chainDisconnected,e),custom:e=>{if(!e||\"object\"!=typeof e||Array.isArray(e))throw new Error(\"Ethereum Provider custom errors must provide single object argument.\");const{code:t,message:r,data:i}=e;if(!r||\"string\"!=typeof r)throw new Error('\"message\" must be a nonempty string');return new n.EthereumProviderError(t,r,i)}}}},function(e,t,r){\"use strict\";var n=Array.isArray,i=Object.keys,o=Object.prototype.hasOwnProperty;e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&\"object\"==typeof t&&\"object\"==typeof r){var s,a,u,c=n(t),l=n(r);if(c&&l){if((a=t.length)!=r.length)return!1;for(s=a;0!=s--;)if(!e(t[s],r[s]))return!1;return!0}if(c!=l)return!1;var f=t instanceof Date,d=r instanceof Date;if(f!=d)return!1;if(f&&d)return t.getTime()==r.getTime();var h=t instanceof RegExp,p=r instanceof RegExp;if(h!=p)return!1;if(h&&p)return t.toString()==r.toString();var g=i(t);if((a=g.length)!==i(r).length)return!1;for(s=a;0!=s--;)if(!o.call(r,g[s]))return!1;for(s=a;0!=s--;)if(!e(t[u=g[s]],r[u]))return!1;return!0}return t!=t&&r!=r}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.createIdRemapMiddleware=void 0;const n=r(23);t.createIdRemapMiddleware=function(){return(e,t,r,i)=>{const o=e.id,s=n.getUniqueId();e.id=s,t.id=s,r(r=>{e.id=o,t.id=o,r()})}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.createAsyncMiddleware=void 0,t.createAsyncMiddleware=function(e){return async(t,r,n,i)=>{let o;const s=new Promise(e=>{o=e});let a=null,u=!1;const c=async()=>{u=!0,n(e=>{a=e,o()}),await s};try{await e(t,r,c),u?(await s,a(null)):i(null)}catch(e){a?a(e):i(e)}}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.createScaffoldMiddleware=void 0,t.createScaffoldMiddleware=function(e){return(t,r,n,i)=>{const o=e[t.method];return void 0===o?n():\"function\"==typeof o?o(t,r,n,i):(r.result=o,i())}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.mergeMiddleware=void 0;const n=r(24);t.mergeMiddleware=function(e){const t=new n.JsonRpcEngine;return e.forEach(e=>t.push(e)),t.asMiddleware()}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.createExternalExtensionProvider=void 0;const i=n(r(46)),o=r(62),s=r(17),a=r(18),u=r(5),c=n(r(72)),l=o.detect();t.createExternalExtensionProvider=function(){let e;try{const t=function(){switch(null==l?void 0:l.name){case\"chrome\":return c.default.CHROME_ID;case\"firefox\":return c.default.FIREFOX_ID;default:return c.default.CHROME_ID}}(),r=chrome.runtime.connect(t),n=new i.default(r);e=new a.StreamProvider(n,{jsonRpcStreamName:s.MetaMaskInpageProviderStreamName,logger:console,rpcMiddleware:u.getDefaultExternalMiddleware(console)}),e.initialize()}catch(e){throw console.dir(\"MetaMask connect error.\",e),e}return e}},function(e,t,r){\"use strict\";(function(e){Object.defineProperty(t,\"__esModule\",{value:!0});const n=r(49);class i extends n.Duplex{constructor(e){super({objectMode:!0}),this._port=e,this._port.onMessage.addListener(e=>this._onMessage(e)),this._port.onDisconnect.addListener(()=>this._onDisconnect()),this._log=()=>null}_onMessage(t){if(e.isBuffer(t)){const r=e.from(t);this._log(r,!1),this.push(r)}else this._log(t,!1),this.push(t)}_onDisconnect(){this.destroy()}_read(){}_write(t,r,n){try{if(e.isBuffer(t)){const e=t.toJSON();e._isBuffer=!0,this._log(e,!0),this._port.postMessage(e)}else this._log(t,!0),this._port.postMessage(t)}catch(e){return n(new Error(\"PortDuplexStream - disconnected\"))}return n()}_setLogger(e){this._log=e}}t.default=i}).call(this,r(6).Buffer)},function(e,t,r){\"use strict\";t.byteLength=function(e){var t=c(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,n=c(e),s=n[0],a=n[1],u=new o(function(e,t,r){return 3*(t+r)/4-r}(0,s,a)),l=0,f=a>0?s-4:s;for(r=0;r<f;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[l++]=t>>16&255,u[l++]=t>>8&255,u[l++]=255&t;2===a&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[l++]=255&t);1===a&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[l++]=t>>8&255,u[l++]=255&t);return u},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],s=0,a=r-i;s<a;s+=16383)o.push(l(e,s,s+16383>a?a:s+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+\"==\")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+\"=\"));return o.join(\"\")};for(var n=[],i=[],o=\"undefined\"!=typeof Uint8Array?Uint8Array:Array,s=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function c(e){var t=e.length;if(t%4>0)throw new Error(\"Invalid string. Length must be a multiple of 4\");var r=e.indexOf(\"=\");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,r){for(var i,o,s=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join(\"\")}i[\"-\".charCodeAt(0)]=62,i[\"_\".charCodeAt(0)]=63},function(e,t){\n/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */\nt.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,l=-7,f=r?i-1:0,d=r?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-l)-1,h>>=-l,l+=a;l>0;o=256*o+e[t+f],f+=d,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+e[t+f],f+=d,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,n),o-=c}return(h?-1:1)*s*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,f=l>>1,d=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:o-1,p=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+f>=1?d/u:d*Math.pow(2,1-f))*u>=2&&(s++,u/=2),s+f>=l?(a=0,s=l):s+f>=1?(a=(t*u-1)*Math.pow(2,i),s+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;e[r+h]=255&a,h+=p,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[r+h]=255&s,h+=p,s/=256,c-=8);e[r+h-p]|=128*g}},function(e,t,r){e.exports=i;var n=r(8).EventEmitter;function i(){n.call(this)}r(4)(i,n),i.Readable=r(0),i.Writable=r(58),i.Duplex=r(59),i.Transform=r(60),i.PassThrough=r(61),i.Stream=i,i.prototype.pipe=function(e,t){var r=this;function i(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function o(){r.readable&&r.resume&&r.resume()}r.on(\"data\",i),e.on(\"drain\",o),e._isStdio||t&&!1===t.end||(r.on(\"end\",a),r.on(\"close\",u));var s=!1;function a(){s||(s=!0,e.end())}function u(){s||(s=!0,\"function\"==typeof e.destroy&&e.destroy())}function c(e){if(l(),0===n.listenerCount(this,\"error\"))throw e}function l(){r.removeListener(\"data\",i),e.removeListener(\"drain\",o),r.removeListener(\"end\",a),r.removeListener(\"close\",u),r.removeListener(\"error\",c),e.removeListener(\"error\",c),r.removeListener(\"end\",l),r.removeListener(\"close\",l),e.removeListener(\"close\",l)}return r.on(\"error\",c),e.on(\"error\",c),r.on(\"end\",l),r.on(\"close\",l),e.on(\"close\",l),e.emit(\"pipe\",r),e}},function(e,t){},function(e,t,r){\"use strict\";var n=r(15).Buffer,i=r(52);e.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return\"\";for(var t=this.head,r=\"\"+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);for(var t,r,i,o=n.allocUnsafe(e>>>0),s=this.head,a=0;s;)t=s.data,r=o,i=a,t.copy(r,i),a+=s.data.length,s=s.next;return o},e}(),i&&i.inspect&&i.inspect.custom&&(e.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+\" \"+e})},function(e,t){},function(e,t,r){(function(e){var n=void 0!==e&&e||\"undefined\"!=typeof self&&self||window,i=Function.prototype.apply;function o(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new o(i.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new o(i.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},r(54),t.setImmediate=\"undefined\"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate=\"undefined\"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,r(3))},function(e,t,r){(function(e,t){!function(e,r){\"use strict\";if(!e.setImmediate){var n,i,o,s,a,u=1,c={},l=!1,f=e.document,d=Object.getPrototypeOf&&Object.getPrototypeOf(e);d=d&&d.setTimeout?d:e,\"[object process]\"==={}.toString.call(e.process)?n=function(e){t.nextTick((function(){p(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage(\"\",\"*\"),e.onmessage=r,t}}()?e.MessageChannel?((o=new MessageChannel).port1.onmessage=function(e){p(e.data)},n=function(e){o.port2.postMessage(e)}):f&&\"onreadystatechange\"in f.createElement(\"script\")?(i=f.documentElement,n=function(e){var t=f.createElement(\"script\");t.onreadystatechange=function(){p(e),t.onreadystatechange=null,i.removeChild(t),t=null},i.appendChild(t)}):n=function(e){setTimeout(p,0,e)}:(s=\"setImmediate$\"+Math.random()+\"$\",a=function(t){t.source===e&&\"string\"==typeof t.data&&0===t.data.indexOf(s)&&p(+t.data.slice(s.length))},e.addEventListener?e.addEventListener(\"message\",a,!1):e.attachEvent(\"onmessage\",a),n=function(t){e.postMessage(s+t,\"*\")}),d.setImmediate=function(e){\"function\"!=typeof e&&(e=new Function(\"\"+e));for(var t=new Array(arguments.length-1),r=0;r<t.length;r++)t[r]=arguments[r+1];var i={callback:e,args:t};return c[u]=i,n(u),u++},d.clearImmediate=h}function h(e){delete c[e]}function p(e){if(l)setTimeout(p,0,e);else{var t=c[e];if(t){l=!0;try{!function(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(void 0,r)}}(t)}finally{h(e),l=!1}}}}}(\"undefined\"==typeof self?void 0===e?this:e:self)}).call(this,r(3),r(1))},function(e,t,r){(function(t){function r(e){try{if(!t.localStorage)return!1}catch(e){return!1}var r=t.localStorage[e];return null!=r&&\"true\"===String(r).toLowerCase()}e.exports=function(e,t){if(r(\"noDeprecation\"))return e;var n=!1;return function(){if(!n){if(r(\"throwDeprecation\"))throw new Error(t);r(\"traceDeprecation\")?console.trace(t):console.warn(t),n=!0}return e.apply(this,arguments)}}}).call(this,r(3))},function(e,t,r){\n/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\nvar n=r(6),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(o(n,t),t.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(e,t,r){if(\"number\"==typeof e)throw new TypeError(\"Argument must not be a number\");return i(e,t,r)},s.alloc=function(e,t,r){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");var n=i(e);return void 0!==t?\"string\"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return i(e)},s.allocUnsafeSlow=function(e){if(\"number\"!=typeof e)throw new TypeError(\"Argument must be a number\");return n.SlowBuffer(e)}},function(e,t,r){\"use strict\";e.exports=o;var n=r(30),i=Object.create(r(7));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=r(4),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},function(e,t,r){e.exports=r(16)},function(e,t,r){e.exports=r(2)},function(e,t,r){e.exports=r(0).Transform},function(e,t,r){e.exports=r(0).PassThrough},function(e,t,r){\"use strict\";r.r(t),function(e){r.d(t,\"BrowserInfo\",(function(){return i})),r.d(t,\"NodeInfo\",(function(){return o})),r.d(t,\"SearchBotDeviceInfo\",(function(){return s})),r.d(t,\"BotInfo\",(function(){return a})),r.d(t,\"ReactNativeInfo\",(function(){return u})),r.d(t,\"detect\",(function(){return d})),r.d(t,\"browserName\",(function(){return p})),r.d(t,\"parseUserAgent\",(function(){return g})),r.d(t,\"detectOS\",(function(){return m})),r.d(t,\"getNodeVersion\",(function(){return y}));var n=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},i=function(e,t,r){this.name=e,this.version=t,this.os=r,this.type=\"browser\"},o=function(t){this.version=t,this.type=\"node\",this.name=\"node\",this.os=e.platform},s=function(e,t,r,n){this.name=e,this.version=t,this.os=r,this.bot=n,this.type=\"bot-device\"},a=function(){this.type=\"bot\",this.bot=!0,this.name=\"bot\",this.version=null,this.os=null},u=function(){this.type=\"react-native\",this.name=\"react-native\",this.version=null,this.os=null},c=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\\\\ Jeeves\\\\/Teoma|ia_archiver)/,l=[[\"aol\",/AOLShield\\\\/([0-9\\\\._]+)/],[\"edge\",/Edge\\\\/([0-9\\\\._]+)/],[\"edge-ios\",/EdgiOS\\\\/([0-9\\\\._]+)/],[\"yandexbrowser\",/YaBrowser\\\\/([0-9\\\\._]+)/],[\"kakaotalk\",/KAKAOTALK\\\\s([0-9\\\\.]+)/],[\"samsung\",/SamsungBrowser\\\\/([0-9\\\\.]+)/],[\"silk\",/\\\\bSilk\\\\/([0-9._-]+)\\\\b/],[\"miui\",/MiuiBrowser\\\\/([0-9\\\\.]+)$/],[\"beaker\",/BeakerBrowser\\\\/([0-9\\\\.]+)/],[\"edge-chromium\",/EdgA?\\\\/([0-9\\\\.]+)/],[\"chromium-webview\",/(?!Chrom.*OPR)wv\\\\).*Chrom(?:e|ium)\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"chrome\",/(?!Chrom.*OPR)Chrom(?:e|ium)\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"phantomjs\",/PhantomJS\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"crios\",/CriOS\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"firefox\",/Firefox\\\\/([0-9\\\\.]+)(?:\\\\s|$)/],[\"fxios\",/FxiOS\\\\/([0-9\\\\.]+)/],[\"opera-mini\",/Opera Mini.*Version\\\\/([0-9\\\\.]+)/],[\"opera\",/Opera\\\\/([0-9\\\\.]+)(?:\\\\s|$)/],[\"opera\",/OPR\\\\/([0-9\\\\.]+)(:?\\\\s|$)/],[\"pie\",/^Microsoft Pocket Internet Explorer\\\\/(\\\\d+\\\\.\\\\d+)$/],[\"pie\",/^Mozilla\\\\/\\\\d\\\\.\\\\d+\\\\s\\\\(compatible;\\\\s(?:MSP?IE|MSInternet Explorer) (\\\\d+\\\\.\\\\d+);.*Windows CE.*\\\\)$/],[\"netfront\",/^Mozilla\\\\/\\\\d\\\\.\\\\d+.*NetFront\\\\/(\\\\d.\\\\d)/],[\"ie\",/Trident\\\\/7\\\\.0.*rv\\\\:([0-9\\\\.]+).*\\\\).*Gecko$/],[\"ie\",/MSIE\\\\s([0-9\\\\.]+);.*Trident\\\\/[4-7].0/],[\"ie\",/MSIE\\\\s(7\\\\.0)/],[\"bb10\",/BB10;\\\\sTouch.*Version\\\\/([0-9\\\\.]+)/],[\"android\",/Android\\\\s([0-9\\\\.]+)/],[\"ios\",/Version\\\\/([0-9\\\\._]+).*Mobile.*Safari.*/],[\"safari\",/Version\\\\/([0-9\\\\._]+).*Safari/],[\"facebook\",/FB[AS]V\\\\/([0-9\\\\.]+)/],[\"instagram\",/Instagram\\\\s([0-9\\\\.]+)/],[\"ios-webview\",/AppleWebKit\\\\/([0-9\\\\.]+).*Mobile/],[\"ios-webview\",/AppleWebKit\\\\/([0-9\\\\.]+).*Gecko\\\\)$/],[\"curl\",/^curl\\\\/([0-9\\\\.]+)$/],[\"searchbot\",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],f=[[\"iOS\",/iP(hone|od|ad)/],[\"Android OS\",/Android/],[\"BlackBerry OS\",/BlackBerry|BB10/],[\"Windows Mobile\",/IEMobile/],[\"Amazon OS\",/Kindle/],[\"Windows 3.11\",/Win16/],[\"Windows 95\",/(Windows 95)|(Win95)|(Windows_95)/],[\"Windows 98\",/(Windows 98)|(Win98)/],[\"Windows 2000\",/(Windows NT 5.0)|(Windows 2000)/],[\"Windows XP\",/(Windows NT 5.1)|(Windows XP)/],[\"Windows Server 2003\",/(Windows NT 5.2)/],[\"Windows Vista\",/(Windows NT 6.0)/],[\"Windows 7\",/(Windows NT 6.1)/],[\"Windows 8\",/(Windows NT 6.2)/],[\"Windows 8.1\",/(Windows NT 6.3)/],[\"Windows 10\",/(Windows NT 10.0)/],[\"Windows ME\",/Windows ME/],[\"Windows CE\",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],[\"Open BSD\",/OpenBSD/],[\"Sun OS\",/SunOS/],[\"Chrome OS\",/CrOS/],[\"Linux\",/(Linux)|(X11)/],[\"Mac OS\",/(Mac_PowerPC)|(Macintosh)/],[\"QNX\",/QNX/],[\"BeOS\",/BeOS/],[\"OS/2\",/OS\\\\/2/]];function d(e){return e?g(e):\"undefined\"==typeof document&&\"undefined\"!=typeof navigator&&\"ReactNative\"===navigator.product?new u:\"undefined\"!=typeof navigator?g(navigator.userAgent):y()}function h(e){return\"\"!==e&&l.reduce((function(t,r){var n=r[0],i=r[1];if(t)return t;var o=i.exec(e);return!!o&&[n,o]}),!1)}function p(e){var t=h(e);return t?t[0]:null}function g(e){var t=h(e);if(!t)return null;var r=t[0],o=t[1];if(\"searchbot\"===r)return new a;var u=o[1]&&o[1].split(\".\").join(\"_\").split(\"_\").slice(0,3);u?u.length<3&&(u=n(n([],u,!0),function(e){for(var t=[],r=0;r<e;r++)t.push(\"0\");return t}(3-u.length),!0)):u=[];var l=u.join(\".\"),f=m(e),d=c.exec(e);return d&&d[1]?new s(r,l,f,d[1]):new i(r,l,f)}function m(e){for(var t=0,r=f.length;t<r;t++){var n=f[t],i=n[0];if(n[1].exec(e))return i}return null}function y(){return void 0!==e&&e.version?new o(e.version.slice(1)):null}}.call(this,r(1))},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.sendSiteMetadata=void 0;const i=n(r(10)),o=r(5);function s(e){const{document:t}=e,r=t.querySelector('head > meta[property=\"og:site_name\"]');if(r)return r.content;const n=t.querySelector('head > meta[name=\"title\"]');return n?n.content:t.title&&t.title.length>0?t.title:window.location.hostname}async function a(e){const{document:t}=e,r=t.querySelectorAll('head > link[rel~=\"icon\"]');for(const e of r)if(e&&await u(e.href))return e.href;return null}function u(e){return new Promise((t,r)=>{try{const r=document.createElement(\"img\");r.onload=()=>t(!0),r.onerror=()=>t(!1),r.src=e}catch(e){r(e)}})}t.sendSiteMetadata=async function(e,t){try{const t=await async function(){return{name:s(window),icon:await a(window)}}();e.handle({jsonrpc:\"2.0\",id:1,method:\"metamask_sendDomainMetadata\",params:t},o.NOOP)}catch(e){t.error({message:i.default.errors.sendSiteMetadata(),originalError:e})}}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.ObjectMultiplex=void 0;const i=r(0),o=n(r(32)),s=n(r(19)),a=r(66),u=Symbol(\"IGNORE_SUBSTREAM\");class c extends i.Duplex{constructor(e={}){super(Object.assign(Object.assign({},e),{objectMode:!0})),this._substreams={}}createStream(e){if(this.destroyed)throw new Error(\\`ObjectMultiplex - parent stream for name \"\\${e}\" already destroyed\\`);if(this._readableState.ended||this._writableState.ended)throw new Error(\\`ObjectMultiplex - parent stream for name \"\\${e}\" already ended\\`);if(!e)throw new Error(\"ObjectMultiplex - name must not be empty\");if(this._substreams[e])throw new Error(\\`ObjectMultiplex - Substream for name \"\\${e}\" already exists\\`);const t=new a.Substream({parent:this,name:e});return this._substreams[e]=t,function(e,t){const r=s.default(t);o.default(e,{readable:!1},r),o.default(e,{writable:!1},r)}(this,e=>t.destroy(e||void 0)),t}ignoreStream(e){if(!e)throw new Error(\"ObjectMultiplex - name must not be empty\");if(this._substreams[e])throw new Error(\\`ObjectMultiplex - Substream for name \"\\${e}\" already exists\\`);this._substreams[e]=u}_read(){}_write(e,t,r){const{name:n,data:i}=e;if(!n)return console.warn(\\`ObjectMultiplex - malformed chunk without name \"\\${e}\"\\`),r();const o=this._substreams[n];return o?(o!==u&&o.push(i),r()):(console.warn(\\`ObjectMultiplex - orphaned data for stream \"\\${n}\"\\`),r())}}t.ObjectMultiplex=c},function(e,t){e.exports=function e(t,r){if(t&&r)return e(t)(r);if(\"function\"!=typeof t)throw new TypeError(\"need wrapper function\");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),i=e[e.length-1];return\"function\"==typeof n&&n!==i&&Object.keys(i).forEach((function(e){n[e]=i[e]})),n}}},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.Substream=void 0;const n=r(0);class i extends n.Duplex{constructor({parent:e,name:t}){super({objectMode:!0}),this._parent=e,this._name=t}_read(){}_write(e,t,r){this._parent.push({name:this._name,data:e}),r()}}t.Substream=i},function(e,t,r){\"use strict\";const n=e=>null!==e&&\"object\"==typeof e&&\"function\"==typeof e.pipe;n.writable=e=>n(e)&&!1!==e.writable&&\"function\"==typeof e._write&&\"object\"==typeof e._writableState,n.readable=e=>n(e)&&!1!==e.readable&&\"function\"==typeof e._read&&\"object\"==typeof e._readableState,n.duplex=e=>n.writable(e)&&n.readable(e),n.transform=e=>n.duplex(e)&&\"function\"==typeof e._transform,e.exports=n},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.createStreamMiddleware=t.createEngineStream=void 0;const i=n(r(69));t.createEngineStream=i.default;const o=n(r(70));t.createStreamMiddleware=o.default},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0});const n=r(0);t.default=function(e){if(!e||!e.engine)throw new Error(\"Missing engine parameter!\");const{engine:t}=e,r=new n.Duplex({objectMode:!0,read:function(){return},write:function(e,n,i){t.handle(e,(e,t)=>{r.push(t)}),i()}});return t.on&&t.on(\"notification\",e=>{r.push(e)}),r}},function(e,t,r){\"use strict\";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,\"__esModule\",{value:!0});const i=n(r(12)),o=r(0);t.default=function(){const e={},t=new o.Duplex({objectMode:!0,read:function(){return!1},write:function(t,n,i){let o;try{!t.id?function(e){r.emit(\"notification\",e)}(t):function(t){const r=e[t.id];if(!r)throw new Error(\\`StreamMiddleware - Unknown response id \"\\${t.id}\"\\`);delete e[t.id],Object.assign(r.res,t),setTimeout(r.end)}(t)}catch(e){o=e}i(o)}}),r=new i.default;return{events:r,middleware:(r,n,i,o)=>{t.push(r),e[r.id]={req:r,res:n,next:i,end:o}},stream:t}}},function(e,t){},function(e){e.exports=JSON.parse('{\"CHROME_ID\":\"nkbihfbeogaeaoehlefnkodbefgpgknn\",\"FIREFOX_ID\":\"<EMAIL>\"}')},function(e,t,r){\"use strict\";Object.defineProperty(t,\"__esModule\",{value:!0}),t.setGlobalProvider=t.initializeProvider=void 0;const n=r(17),i=r(34);function o(e){window.dekey=e,window.dispatchEvent(new Event(\"dekey#initialized\"))}t.initializeProvider=function({connectionStream:e,jsonRpcStreamName:t,logger:r=console,maxEventListeners:s=100,shouldSendMetadata:a=!0,shouldSetOnWindow:u=!0,shouldShimWeb3:c=!1}){const l=new n.MetaMaskInpageProvider(e,{jsonRpcStreamName:t,logger:r,maxEventListeners:s,shouldSendMetadata:a}),f=new Proxy(l,{deleteProperty:()=>!0});return u&&o(f),c&&i.shimWeb3(f,r),f},t.setGlobalProvider=o},function(e,t,r){(function(t){function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e,t,r){return(t=function(e){var t=function(e,t){if(\"object\"!==s(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||\"default\");if(\"object\"!==s(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}(e,\"string\");return\"symbol\"===s(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e){return(s=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}var a=r(35).inherits,u=r(0).Duplex;function c(e,t){u.call(this,{objectMode:!0}),this._name=e.name,this._targetWindow=window,this._port=e,this._origin=t,window.addEventListener(\"message\",this._onMessage.bind(this),!1)}e.exports=c,a(c,u),c.prototype._onMessage=function(e){var r=e.data;try{r=JSON.parse(e.data)}catch(e){}if((\"*\"===this._origin||e.origin===this._origin)&&r&&\"object\"===s(r)&&r.data&&\"object\"===s(r.data)&&(!r.target||r.target===this._name))if(t.isBuffer(r)){delete r._isBuffer;var n=t.from(r);this.push(n)}else this.push(r)},c.prototype._onDisconnect=function(){this.destroy()},c.prototype._read=function(){},c.prototype._write=function(e,r,n){try{if(t.isBuffer(e)){var o=e.toJSON();o._isBuffer=!0,window.parent.postMessage(JSON.stringify(i(i({},o),{},{origin:window.location.href})),this._origin)}else window.parent.postMessage(JSON.stringify(i(i({},e),{},{origin:window.location.href})),this._origin)}catch(e){return n(new Error(\"MobilePortStream - disconnected\"))}return n()}}).call(this,r(6).Buffer)},function(e,t){e.exports=function(e){return e&&\"object\"==typeof e&&\"function\"==typeof e.copy&&\"function\"==typeof e.fill&&\"function\"==typeof e.readUInt8}},function(e,t){\"function\"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},function(e,t,r){function n(e){return(n=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}var i=r(35).inherits,o=r(0).Duplex,s=function(){};function a(e){o.call(this,{objectMode:!0}),this._name=e.name,this._target=e.target,this._targetWindow=e.targetWindow||window,this._origin=location.origin,this._init=!1,this._haveSyn=!1,window.addEventListener(\"message\",this._onMessage.bind(this),!1),this._write(\"SYN\",null,s),this.cork()}e.exports=a,i(a,o),a.prototype._onMessage=function(e){var t=e.data;if((\"*\"===this._origin||e.origin===this._origin)&&(e.source===this._targetWindow||window!==top)&&t&&\"object\"===n(t)&&t.target===this._name&&t.data)if(this._init)try{this.push(t.data)}catch(e){this.emit(\"error\",e)}else\"SYN\"===t.data?(this._haveSyn=!0,this._write(\"ACK\",null,s)):\"ACK\"===t.data&&(this._init=!0,this._haveSyn||this._write(\"ACK\",null,s),this.uncork())},a.prototype._read=s,a.prototype._write=function(e,t,r){var n={target:this._target,name:\"dekey-mobile-provider\",data:e};this._targetWindow.postMessage(n,this._origin),r()}}]);`), window.dekey.on(\"_initialized\", async () => {\n          l(\"_initialized\");\n        }), await async function(t) {\n          await async function() {\n            [\"interactive\", \"complete\"].includes(document.readyState) || await new Promise((o) => window.addEventListener(\"load\", o, { once: !0 }));\n          }(), window._dekeySetupProvider(t);\n        }(i));\n      } catch (t) {\n        l(t);\n      }\n    });\n  }\n  window.initializeDekeyProvider = r, f.exports = { init: r };\n}]);\n"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAAE;EACX,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,SAASC,CAACA,CAACC,CAAC,EAAE;IACZ,IAAIF,CAAC,CAACE,CAAC,CAAC,EACN,OAAOF,CAAC,CAACE,CAAC,CAAC,CAACC,OAAO;IACrB,IAAIC,CAAC,GAAGJ,CAAC,CAACE,CAAC,CAAC,GAAG;MAAEG,CAAC,EAAEH,CAAC;MAAEI,CAAC,EAAE,CAAC,CAAC;MAAEH,OAAO,EAAE,CAAC;IAAE,CAAC;IAC3C,OAAOJ,CAAC,CAACG,CAAC,CAAC,CAACK,IAAI,CAACH,CAAC,CAACD,OAAO,EAAEC,CAAC,EAAEA,CAAC,CAACD,OAAO,EAAEF,CAAC,CAAC,EAAEG,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,EAAEF,CAAC,CAACD,OAAO;EACnE;EACAF,CAAC,CAACO,CAAC,GAAGT,CAAC,EAAEE,CAAC,CAACD,CAAC,GAAGA,CAAC,EAAEC,CAAC,CAACQ,CAAC,GAAG,UAASP,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;IACxCJ,CAAC,CAACS,CAAC,CAACR,CAAC,EAAEE,CAAC,CAAC,IAAIO,MAAM,CAACC,cAAc,CAACV,CAAC,EAAEE,CAAC,EAAE;MAAES,UAAU,EAAE,CAAC,CAAC;MAAEC,GAAG,EAAET;IAAE,CAAC,CAAC;EACtE,CAAC,EAAEJ,CAAC,CAACG,CAAC,GAAG,UAASF,CAAC,EAAE;IACnB,OAAOa,MAAM,GAAG,GAAG,IAAIA,MAAM,CAACC,WAAW,IAAIL,MAAM,CAACC,cAAc,CAACV,CAAC,EAAEa,MAAM,CAACC,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC,EAAEN,MAAM,CAACC,cAAc,CAACV,CAAC,EAAE,YAAY,EAAE;MAAEe,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC;EACvK,CAAC,EAAEhB,CAAC,CAACiB,CAAC,GAAG,UAAShB,CAAC,EAAEE,CAAC,EAAE;IACtB,IAAI,CAAC,GAAGA,CAAC,KAAKF,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGE,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAI,OAAOF,CAAC,IAAI,QAAQ,IAAIA,CAAC,IAAIA,CAAC,CAACiB,UAAU,EAClF,OAAOjB,CAAC;IACV,IAAIG,CAAC,GAAG,eAAgBM,MAAM,CAACS,MAAM,CAAC,IAAI,CAAC;IAC3C,IAAInB,CAAC,CAACG,CAAC,CAACC,CAAC,CAAC,EAAEM,MAAM,CAACC,cAAc,CAACP,CAAC,EAAE,SAAS,EAAE;MAAEQ,UAAU,EAAE,CAAC,CAAC;MAAEI,KAAK,EAAEf;IAAE,CAAC,CAAC,EAAE,CAAC,GAAGE,CAAC,IAAI,OAAOF,CAAC,IAAI,QAAQ,EAC1G,KAAK,IAAImB,CAAC,IAAInB,CAAC,EACbD,CAAC,CAACQ,CAAC,CAACJ,CAAC,EAAEgB,CAAC,EAAG,UAASf,CAAC,EAAE;MACrB,OAAOJ,CAAC,CAACI,CAAC,CAAC;IACb,CAAC,CAAEgB,IAAI,CAAC,IAAI,EAAED,CAAC,CAAC,CAAC;IACrB,OAAOhB,CAAC;EACV,CAAC,EAAEJ,CAAC,CAACA,CAAC,GAAG,UAASC,CAAC,EAAE;IACnB,IAAIE,CAAC,GAAGF,CAAC,IAAIA,CAAC,CAACiB,UAAU,GAAG,YAAW;MACrC,OAAOjB,CAAC,CAACqB,OAAO;IAClB,CAAC,GAAG,YAAW;MACb,OAAOrB,CAAC;IACV,CAAC;IACD,OAAOD,CAAC,CAACQ,CAAC,CAACL,CAAC,EAAE,GAAG,EAAEA,CAAC,CAAC,EAAEA,CAAC;EAC1B,CAAC,EAAEH,CAAC,CAACS,CAAC,GAAG,UAASR,CAAC,EAAEE,CAAC,EAAE;IACtB,OAAOO,MAAM,CAACa,SAAS,CAACC,cAAc,CAAClB,IAAI,CAACL,CAAC,EAAEE,CAAC,CAAC;EACnD,CAAC,EAAEH,CAAC,CAACyB,CAAC,GAAG,EAAE,EAAEzB,CAAC,CAACA,CAAC,CAAC0B,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC,EAAE,CAAC,UAAS5B,CAAC,EAAEC,CAAC,EAAE;EACjB,MAAMC,CAAC,GAAG,CAAC,qBAAqB,EAAE,uBAAuB,CAAC;EAC1D,SAASC,CAACA,CAACG,CAAC,EAAE;IACZ,MAAMgB,CAAC,GAAG,IAAIO,GAAG,CAAC,iBAAiB,EAAEvB,CAAC,CAAC;IACvCgB,CAAC,CAACQ,YAAY,CAACC,MAAM,CAAC,MAAM,EAAEC,SAAS,CAACC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAEb,CAAC,CAACQ,YAAY,CAACC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,EAAEE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGb,CAAC,CAACa,IAAI;EAC9I;EACA,eAAe9B,CAACA,CAACC,CAAC,EAAEgB,CAAC,GAAG;IAAEc,OAAO,EAAE;EAAI,CAAC,EAAE;IACxC,OAAOC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAAEC,YAAY,EAAEjC,CAAC;MAAEkC,IAAI,EAAElB;IAAE,CAAC,CAAC,EAAE,IAAImB,OAAO,CAAC,OAAOlC,CAAC,EAAEG,CAAC,KAAK;MAC9G,IAAI;QACF,IAAIJ,CAAC,IAAI2B,MAAM,CAACC,QAAQ,KAAKD,MAAM,CAACS,MAAM,CAACR,QAAQ,EACjD,OAAOD,MAAM,CAACU,KAAK,GAAG;UAAEC,OAAO,EAAE,MAAOzB,CAAC,IAAK;YAC5C,OAAOA,CAAC,IAAI,QAAQ,IAAIjB,CAAC,CAAC2C,QAAQ,CAAC1B,CAAC,CAAC2B,MAAM,CAAC,IAAI3C,CAAC,CAACG,CAAC,CAAC;UACtD,CAAC;UAAEyC,IAAI,EAAE,MAAAA,CAAO5B,CAAC,EAAER,CAAC,KAAK;YACvB,CAAC,OAAOQ,CAAC,IAAI,QAAQ,IAAIjB,CAAC,CAAC2C,QAAQ,CAAC1B,CAAC,CAAC,IAAI,OAAOA,CAAC,IAAI,QAAQ,IAAIjB,CAAC,CAAC2C,QAAQ,CAAC1B,CAAC,CAAC,KAAKhB,CAAC,CAACG,CAAC,CAAC;UAC1F,CAAC;UAAE0C,SAAS,EAAEA,CAAC7B,CAAC,EAAER,CAAC,KAAK;YACtB,OAAOQ,CAAC,IAAI,QAAQ,IAAIjB,CAAC,CAAC2C,QAAQ,CAAC1B,CAAC,CAAC2B,MAAM,CAAC,IAAI3C,CAAC,CAACG,CAAC,CAAC;UACtD,CAAC;UAAE2C,WAAW,EAAEA,CAAA,KAAM,CACtB,CAAC;UAAEC,EAAE,EAAEA,CAAA,KAAM,CACb,CAAC;UAAEC,IAAI,EAAEA,CAAA,KAAM,CACf,CAAC;UAAEC,MAAM,EAAEA,CAAA,KAAM,CACjB,CAAC;UAAEC,eAAe,EAAEA,CAAA,KAAM,CAC1B,CAAC;UAAEC,mBAAmB,EAAEA,CAAA,KAAM,CAC9B;QAAE,CAAC,EAAE/C,CAAC,CAAC,uBAAuB,CAAC;QACjC,CAAC,UAASY,CAAC,EAAE;UACX,IAAIR,CAAC,GAAG4C,OAAO,CAACC,SAAS;YAAE5B,CAAC,GAAG2B,OAAO,CAACE,YAAY;UACnDF,OAAO,CAACC,SAAS,GAAG,UAAS,GAAGE,CAAC,EAAE;YACjC/C,CAAC,CAACgD,KAAK,CAACJ,OAAO,EAAEG,CAAC,CAAC,EAAEzB,MAAM,CAAC2B,aAAa,CAAC,IAAIC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE5B,MAAM,CAAC2B,aAAa,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;UACtH,CAAC,EAAEN,OAAO,CAACE,YAAY,GAAG,UAAS,GAAGC,CAAC,EAAE;YACvC9B,CAAC,CAAC+B,KAAK,CAACJ,OAAO,EAAEG,CAAC,CAAC,EAAEzB,MAAM,CAAC2B,aAAa,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE5B,MAAM,CAAC2B,aAAa,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;UACzH,CAAC,EAAE5B,MAAM,CAAC6B,gBAAgB,CAAC,UAAU,EAAE,YAAW;YAChD7B,MAAM,CAAC2B,aAAa,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;UACnD,CAAC,CAAC,EAAE5B,MAAM,CAAC6B,gBAAgB,CAAC,gBAAgB,EAAE,YAAW;YACvD7B,MAAM,CAACS,MAAM,CAACqB,WAAW,CAACC,IAAI,CAACC,SAAS,CAAC;cAAE9B,IAAI,EAAED,QAAQ,CAACC;YAAK,CAAC,CAAC,EAAEhB,CAAC,CAAC;UACvE,CAAC,CAAC;QACJ,CAAC,EAAEb,CAAC,CAAC,EAAE,YAAW;UAChB,MAAM;YAAE4D,OAAO,EAAE/C;UAAE,CAAC,GAAGc,MAAM,CAACkC,QAAQ;UACtC,OAAO,CAAChD,CAAC,IAAIA,CAAC,CAACiD,IAAI,KAAK,MAAM;QAChC,CAAC,CAAC,CAAC,IAAI,YAAW;UAChB,MAAMjD,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;YAAER,CAAC,GAAGsB,MAAM,CAACC,QAAQ,CAACmC,QAAQ;UAChE,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,CAAC,CAACmD,MAAM,EAAE1C,CAAC,EAAE,EAC/B,IAAIT,CAAC,CAACS,CAAC,CAAC,CAAC2C,IAAI,CAAC5D,CAAC,CAAC,EACd,OAAO,CAAC,CAAC;UACb,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,IAAI,YAAW;UAChB,MAAMQ,CAAC,GAAGgD,QAAQ,CAACK,eAAe,CAACC,QAAQ;UAC3C,OAAO,CAACtD,CAAC,IAAIA,CAAC,CAACuD,WAAW,CAAC,CAAC,KAAK,MAAM;QACzC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAW;UACjB,MAAMvD,CAAC,GAAG,CAAC,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,mEAAmE,EAAE,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,eAAe,CAAC;YAAER,CAAC,GAAGsB,MAAM,CAACC,QAAQ,CAACC,IAAI;UACzQ,IAAIP,CAAC;UACL,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,CAAC,CAACmD,MAAM,EAAEZ,CAAC,EAAE,EAAE;YACjC,MAAMiB,CAAC,GAAGxD,CAAC,CAACuC,CAAC,CAAC,CAACkB,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;YAClC,IAAIhD,CAAC,GAAG,IAAIiD,MAAM,CAAC,0BAA0BF,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC/C,CAAC,CAAC2C,IAAI,CAAC5D,CAAC,CAAC,EACrE,OAAO,CAAC,CAAC;UACb;UACA,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,KAAKmE,UAAU,CAAC,MAAM;UACvBpE,CAAC,CAAC,6BAA6B,CAAC;QAClC,CAAC,EAAEY,CAAC,CAACc,OAAO,CAAC,EAAE,UAASjB,CAAC,EAAE;UACzB,IAAI;YACF,MAAMR,CAAC,GAAGwD,QAAQ,CAACY,IAAI,IAAIZ,QAAQ,CAACK,eAAe;cAAE5C,CAAC,GAAGuC,QAAQ,CAACa,aAAa,CAAC,QAAQ,CAAC;YACzFpD,CAAC,CAACqD,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAErD,CAAC,CAACsD,WAAW,GAAG/D,CAAC,EAAER,CAAC,CAACwE,YAAY,CAACvD,CAAC,EAAEjB,CAAC,CAACyE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEzE,CAAC,CAAC0E,WAAW,CAACzD,CAAC,CAAC;UACpG,CAAC,CAAC,OAAOjB,CAAC,EAAE;YACV0B,OAAO,CAACiD,KAAK,CAAC,+BAA+B,EAAE3E,CAAC,CAAC;UACnD;QACF,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qreAAqre,CAAC,EAAEsB,MAAM,CAACU,KAAK,CAACO,EAAE,CAAC,cAAc,EAAE,YAAY;UAC1te3C,CAAC,CAAC,cAAc,CAAC;QACnB,CAAC,CAAC,EAAE,MAAM,gBAAeY,CAAC,EAAE;UAC1B,MAAM,kBAAiB;YACrB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC0B,QAAQ,CAACsB,QAAQ,CAACoB,UAAU,CAAC,KAAI,MAAM,IAAI9C,OAAO,CAAE9B,CAAC,IAAKsB,MAAM,CAAC6B,gBAAgB,CAAC,MAAM,EAAEnD,CAAC,EAAE;cAAEwC,IAAI,EAAE,CAAC;YAAE,CAAC,CAAC,CAAC;UACzI,CAAC,CAAC,CAAC,EAAElB,MAAM,CAACuD,mBAAmB,CAACrE,CAAC,CAAC;QACpC,CAAC,CAACb,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,OAAOa,CAAC,EAAE;QACVZ,CAAC,CAACY,CAAC,CAAC;MACN;IACF,CAAC,CAAC;EACJ;EACAc,MAAM,CAACwD,uBAAuB,GAAGpF,CAAC,EAAEL,CAAC,CAACI,OAAO,GAAG;IAAEsF,IAAI,EAAErF;EAAE,CAAC;AAC7D,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}