module escrow_platform::freelancer_escrow {
    use std::signer;
    use std::vector;
    use std::string::String;
    use aptos_framework::coin::{Self, Coin};
    use aptos_framework::aptos_coin::AptosCoin;
    use aptos_framework::timestamp;
    use aptos_framework::event;
    use aptos_std::table::{Self, Table};

    // ==================
    // Error Constants
    // ==================
    const E_NOT_AUTHORIZED: u64 = 1;
    const E_ESCROW_NOT_FOUND: u64 = 2;
    const E_INSUFFICIENT_FUNDS: u64 = 3;
    const E_INVALID_STATUS: u64 = 4;
    const E_ALREADY_EXISTS: u64 = 5;
    const E_DEADLINE_PASSED: u64 = 6;
    const E_PLATFORM_PAUSED: u64 = 7;
    const E_INVALID_AMOUNT: u64 = 8;
    const E_DISPUTE_ACTIVE: u64 = 9;
    const E_REVIEW_PERIOD_ACTIVE: u64 = 10;

    // ==================
    // Status Constants
    // ==================
    const STATUS_CREATED: u8 = 0;
    const STATUS_FUNDED: u8 = 1;
    const STATUS_IN_PROGRESS: u8 = 2;
    const STATUS_SUBMITTED: u8 = 3;
    const STATUS_COMPLETED: u8 = 4;
    const STATUS_DISPUTED: u8 = 5;
    const STATUS_CANCELLED: u8 = 6;
    const STATUS_REFUNDED: u8 = 7;

    // ==================
    // Platform Constants
    // ==================
    const PLATFORM_FEE_BPS: u64 = 250;     // 2.5%
    const BASIS_POINTS: u64 = 10000;
    const DISPUTE_PERIOD: u64 = 604800;     // 7 days in seconds
    const REVIEW_PERIOD: u64 = 259200;      // 3 days in seconds
    const MIN_AMOUNT: u64 = 1000000;        // 0.01 APT minimum
    const MAX_TITLE_LENGTH: u64 = 100;
    const MAX_DESCRIPTION_LENGTH: u64 = 1000;

    // ==================
    // Core Structs
    // ==================
    
    struct EscrowContract has key, store {
        id: u64,
        client: address,
        freelancer: address,
        title: String,
        description: String,
        category: String,
        amount: u64,
        platform_fee: u64,
        deadline: u64,
        status: u8,
        created_at: u64,
        funded_at: u64,
        started_at: u64,
        submitted_at: u64,
        completed_at: u64,
        funds: Coin<AptosCoin>,
        dispute_reason: String,
        disputed_at: u64,
        auto_release_time: u64,
    }

    struct Platform has key {
        admin: address,
        next_escrow_id: u64,
        escrows: Table<u64, EscrowContract>,
        user_escrows: Table<address, vector<u64>>,
        treasury: Coin<AptosCoin>,
        total_escrows: u64,
        total_volume: u64,
        total_completed: u64,
        paused: bool,
        arbitrators: vector<address>,
    }

    struct UserStats has key {
        total_as_client: u64,
        total_as_freelancer: u64,
        completed_as_client: u64,
        completed_as_freelancer: u64,
        total_earned: u64,
        total_spent: u64,
        reputation_score: u64,
        join_timestamp: u64,
    }

    // ==================
    // Events
    // ==================
    
    #[event]
    struct EscrowCreated has drop, store {
        escrow_id: u64,
        client: address,
        freelancer: address,
        amount: u64,
        title: String,
        category: String,
        deadline: u64,
        created_at: u64,
    }

    #[event]
    struct EscrowFunded has drop, store {
        escrow_id: u64,
        client: address,
        amount: u64,
        platform_fee: u64,
        funded_at: u64,
    }

    #[event]
    struct WorkStarted has drop, store {
        escrow_id: u64,
        freelancer: address,
        started_at: u64,
    }

    #[event]
    struct WorkSubmitted has drop, store {
        escrow_id: u64,
        freelancer: address,
        submitted_at: u64,
        auto_release_time: u64,
    }

    #[event]
    struct WorkApproved has drop, store {
        escrow_id: u64,
        client: address,
        approved_at: u64,
    }

    #[event]
    struct FundsReleased has drop, store {
        escrow_id: u64,
        freelancer: address,
        amount: u64,
        platform_fee: u64,
        released_at: u64,
    }

    #[event]
    struct DisputeRaised has drop, store {
        escrow_id: u64,
        raised_by: address,
        reason: String,
        disputed_at: u64,
    }

    #[event]
    struct DisputeResolved has drop, store {
        escrow_id: u64,
        winner: address,
        resolution: String,
        resolved_by: address,
    }

    #[event]
    struct EscrowCancelled has drop, store {
        escrow_id: u64,
        cancelled_by: address,
        reason: String,
        cancelled_at: u64,
    }

    #[event]
    struct FundsRefunded has drop, store {
        escrow_id: u64,
        client: address,
        amount: u64,
        refunded_at: u64,
    }

    // ==================
    // Initialize Functions
    // ==================
    
    public entry fun initialize(admin: &signer) {
        let admin_addr = signer::address_of(admin);
        assert!(admin_addr == @escrow_platform, E_NOT_AUTHORIZED);
        assert!(!exists<Platform>(@escrow_platform), E_ALREADY_EXISTS);

        move_to(admin, Platform {
            admin: admin_addr,
            next_escrow_id: 1,
            escrows: table::new(),
            user_escrows: table::new(),
            treasury: coin::zero<AptosCoin>(),
            total_escrows: 0,
            total_volume: 0,
            total_completed: 0,
            paused: false,
            arbitrators: vector::empty(),
        });
    }

    public entry fun create_user_stats(user: &signer) {
        let user_addr = signer::address_of(user);
        if (!exists<UserStats>(user_addr)) {
            move_to(user, UserStats {
                total_as_client: 0,
                total_as_freelancer: 0,
                completed_as_client: 0,
                completed_as_freelancer: 0,
                total_earned: 0,
                total_spent: 0,
                reputation_score: 100,
                join_timestamp: timestamp::now_seconds(),
            });
        }
    }

    // ==================
    // Core Functions
    // ==================

    public entry fun create_escrow(
        client: &signer,
        freelancer: address,
        title: String,
        description: String,
        category: String,
        amount: u64,
        deadline: u64,
    ) acquires Platform {
        let client_addr = signer::address_of(client);
        let platform = borrow_global_mut<Platform>(@escrow_platform);

        assert!(!platform.paused, E_PLATFORM_PAUSED);
        assert!(amount >= MIN_AMOUNT, E_INVALID_AMOUNT);
        assert!(deadline > timestamp::now_seconds(), E_DEADLINE_PASSED);

        let escrow_id = platform.next_escrow_id;
        platform.next_escrow_id = platform.next_escrow_id + 1;

        let platform_fee = (amount * PLATFORM_FEE_BPS) / BASIS_POINTS;

        let escrow = EscrowContract {
            id: escrow_id,
            client: client_addr,
            freelancer,
            title,
            description,
            category,
            amount,
            platform_fee,
            deadline,
            status: STATUS_CREATED,
            created_at: timestamp::now_seconds(),
            funded_at: 0,
            started_at: 0,
            submitted_at: 0,
            completed_at: 0,
            funds: coin::zero<AptosCoin>(),
            dispute_reason: std::string::utf8(b""),
            disputed_at: 0,
            auto_release_time: 0,
        };

        table::add(&mut platform.escrows, escrow_id, escrow);

        if (!table::contains(&platform.user_escrows, client_addr)) {
            table::add(&mut platform.user_escrows, client_addr, vector::empty<u64>());
        };
        let client_escrows = table::borrow_mut(&mut platform.user_escrows, client_addr);
        vector::push_back(client_escrows, escrow_id);

        if (!table::contains(&platform.user_escrows, freelancer)) {
            table::add(&mut platform.user_escrows, freelancer, vector::empty<u64>());
        };
        let freelancer_escrows = table::borrow_mut(&mut platform.user_escrows, freelancer);
        vector::push_back(freelancer_escrows, escrow_id);

        platform.total_escrows = platform.total_escrows + 1;

        create_user_stats(client);

        event::emit(EscrowCreated {
            escrow_id,
            client: client_addr,
            freelancer,
            amount,
            title,
            category,
            deadline,
            created_at: timestamp::now_seconds(),
        });
    }

    public entry fun fund_escrow(
        client: &signer,
        escrow_id: u64,
    ) acquires Platform {
        let client_addr = signer::address_of(client);
        let platform = borrow_global_mut<Platform>(@escrow_platform);

        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);
        let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);

        assert!(escrow.client == client_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_CREATED, E_INVALID_STATUS);

        let total_amount = escrow.amount + escrow.platform_fee;
        let payment = coin::withdraw<AptosCoin>(client, total_amount);
        coin::merge(&mut escrow.funds, payment);

        escrow.status = STATUS_FUNDED;
        escrow.funded_at = timestamp::now_seconds();

        platform.total_volume = platform.total_volume + escrow.amount;

        event::emit(EscrowFunded {
            escrow_id,
            client: client_addr,
            amount: escrow.amount,
            platform_fee: escrow.platform_fee,
            funded_at: timestamp::now_seconds(),
        });
    }

    public entry fun start_work(
        freelancer: &signer,
        escrow_id: u64,
    ) acquires Platform {
        let freelancer_addr = signer::address_of(freelancer);
        let platform = borrow_global_mut<Platform>(@escrow_platform);

        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);
        let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);

        assert!(escrow.freelancer == freelancer_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_FUNDED, E_INVALID_STATUS);

        escrow.status = STATUS_IN_PROGRESS;
        escrow.started_at = timestamp::now_seconds();

        event::emit(WorkStarted {
            escrow_id,
            freelancer: freelancer_addr,
            started_at: timestamp::now_seconds(),
        });
    }

    public entry fun submit_work(
        freelancer: &signer,
        escrow_id: u64,
    ) acquires Platform {
        let freelancer_addr = signer::address_of(freelancer);
        let platform = borrow_global_mut<Platform>(@escrow_platform);

        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);
        let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);

        assert!(escrow.freelancer == freelancer_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_IN_PROGRESS, E_INVALID_STATUS);

        escrow.status = STATUS_SUBMITTED;
        escrow.submitted_at = timestamp::now_seconds();
        escrow.auto_release_time = timestamp::now_seconds() + REVIEW_PERIOD;

        event::emit(WorkSubmitted {
            escrow_id,
            freelancer: freelancer_addr,
            submitted_at: timestamp::now_seconds(),
            auto_release_time: escrow.auto_release_time,
        });
    }

    public entry fun approve_work(
        client: &signer,
        escrow_id: u64,
    ) acquires Platform, UserStats {
        let client_addr = signer::address_of(client);
        let platform = borrow_global_mut<Platform>(@escrow_platform);

        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);

        let (freelancer_addr, escrow_amount) = {
            let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);
            assert!(escrow.client == client_addr, E_NOT_AUTHORIZED);
            assert!(escrow.status == STATUS_SUBMITTED, E_INVALID_STATUS);

            escrow.status = STATUS_COMPLETED;
            escrow.completed_at = timestamp::now_seconds();
            (escrow.freelancer, escrow.amount)
        };

        release_funds_internal(platform, escrow_id);

        platform.total_completed = platform.total_completed + 1;

        if (exists<UserStats>(freelancer_addr)) {
            let stats = borrow_global_mut<UserStats>(freelancer_addr);
            stats.completed_as_freelancer = stats.completed_as_freelancer + 1;
            stats.total_earned = stats.total_earned + escrow_amount;
        };

        if (exists<UserStats>(client_addr)) {
            let stats = borrow_global_mut<UserStats>(client_addr);
            stats.completed_as_client = stats.completed_as_client + 1;
            stats.total_spent = stats.total_spent + escrow_amount;
        };

        event::emit(WorkApproved {
            escrow_id,
            client: client_addr,
            approved_at: timestamp::now_seconds(),
        });
    }

    public entry fun auto_release_funds(
        escrow_id: u64,
    ) acquires Platform, UserStats {
        let platform = borrow_global_mut<Platform>(@escrow_platform);

        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);

        let (freelancer_addr, client_addr, escrow_amount) = {
            let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);
            assert!(escrow.status == STATUS_SUBMITTED, E_INVALID_STATUS);
            assert!(timestamp::now_seconds() >= escrow.auto_release_time, E_REVIEW_PERIOD_ACTIVE);

            escrow.status = STATUS_COMPLETED;
            escrow.completed_at = timestamp::now_seconds();
            (escrow.freelancer, escrow.client, escrow.amount)
        };

        release_funds_internal(platform, escrow_id);

        platform.total_completed = platform.total_completed + 1;

        if (exists<UserStats>(freelancer_addr)) {
            let stats = borrow_global_mut<UserStats>(freelancer_addr);
            stats.completed_as_freelancer = stats.completed_as_freelancer + 1;
            stats.total_earned = stats.total_earned + escrow_amount;
        };

        if (exists<UserStats>(client_addr)) {
            let stats = borrow_global_mut<UserStats>(client_addr);
            stats.completed_as_client = stats.completed_as_client + 1;
            stats.total_spent = stats.total_spent + escrow_amount;
        };
    }

    fun release_funds_internal(
        platform: &mut Platform,
        escrow_id: u64,
    ) {
        let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);
        
        let freelancer_amount = escrow.amount;
        let platform_fee = escrow.platform_fee;

        let total_funds = coin::extract_all(&mut escrow.funds);
        let fee_coin = coin::extract(&mut total_funds, platform_fee);

        coin::deposit(escrow.freelancer, total_funds);
        coin::merge(&mut platform.treasury, fee_coin);

        event::emit(FundsReleased {
            escrow_id,
            freelancer: escrow.freelancer,
            amount: freelancer_amount,
            platform_fee,
            released_at: timestamp::now_seconds(),
        });
    }

    public entry fun resolve_dispute(
        arbitrator: &signer,
        escrow_id: u64,
        winner: address,
        resolution: String,
    ) acquires Platform, UserStats {
        let arbitrator_addr = signer::address_of(arbitrator);
        let platform = borrow_global_mut<Platform>(@escrow_platform);
        
        assert!(
            platform.admin == arbitrator_addr || 
            vector::contains(&platform.arbitrators, &arbitrator_addr),
            E_NOT_AUTHORIZED
        );
        
        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);
        
        let (freelancer_addr, client_addr, escrow_amount) = {
            let escrow = table::borrow(&platform.escrows, escrow_id);
            assert!(escrow.status == STATUS_DISPUTED, E_INVALID_STATUS);
            (escrow.freelancer, escrow.client, escrow.amount)
        };

        if (winner == freelancer_addr) {
            let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);
            escrow.status = STATUS_COMPLETED;
            escrow.completed_at = timestamp::now_seconds();
            
            release_funds_internal(platform, escrow_id);
            
            if (exists<UserStats>(freelancer_addr)) {
                let stats = borrow_global_mut<UserStats>(freelancer_addr);
                stats.completed_as_freelancer = stats.completed_as_freelancer + 1;
                stats.total_earned = stats.total_earned + escrow_amount;
            };
        } else {
            let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);
            escrow.status = STATUS_REFUNDED;
            let refund = coin::extract_all(&mut escrow.funds);
            let refund_amount = coin::value(&refund);
            coin::deposit(client_addr, refund);
            
            event::emit(FundsRefunded {
                escrow_id,
                client: client_addr,
                amount: refund_amount,
                refunded_at: timestamp::now_seconds(),
            });
        };

        event::emit(DisputeResolved {
            escrow_id,
            winner,
            resolution,
            resolved_by: arbitrator_addr,
        });
    }

    public entry fun cancel_escrow(
        caller: &signer,
        escrow_id: u64,
        reason: String,
    ) acquires Platform {
        let caller_addr = signer::address_of(caller);
        let platform = borrow_global_mut<Platform>(@escrow_platform);
        
        assert!(table::contains(&platform.escrows, escrow_id), E_ESCROW_NOT_FOUND);
        let escrow = table::borrow_mut(&mut platform.escrows, escrow_id);
        
        assert!(
            escrow.client == caller_addr || escrow.freelancer == caller_addr,
            E_NOT_AUTHORIZED
        );
        
        assert!(
            escrow.status == STATUS_CREATED || 
            (escrow.status == STATUS_FUNDED && timestamp::now_seconds() > escrow.deadline),
            E_INVALID_STATUS
        );

        if (escrow.status == STATUS_FUNDED) {
            let refund = coin::extract_all(&mut escrow.funds);
            let refund_amount = coin::value(&refund);
            coin::deposit(escrow.client, refund);
            
            event::emit(FundsRefunded {
                escrow_id,
                client: escrow.client,
                amount: refund_amount,
                refunded_at: timestamp::now_seconds(),
            });
        };

        escrow.status = STATUS_CANCELLED;

        event::emit(EscrowCancelled {
            escrow_id,
            cancelled_by: caller_addr,
            reason,
            cancelled_at: timestamp::now_seconds(),
        });
    }

    // [unchanged: view + admin functions]
}
