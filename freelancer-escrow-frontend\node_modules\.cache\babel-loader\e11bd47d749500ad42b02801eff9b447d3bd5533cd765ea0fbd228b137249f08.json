{"ast": null, "code": "/**\n * SHA2-512 a.k.a. sha512 and sha384. It is slower than sha256 in js because u64 operations are slow.\n *\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [the paper on truncated SHA512/256](https://eprint.iacr.org/2010/548.pdf).\n * @module\n * @deprecated\n */\nimport { SHA384 as SHA384n, sha384 as sha384n, sha512_224 as sha512_224n, SHA512_224 as SHA512_224n, sha512_256 as sha512_256n, SHA512_256 as SHA512_256n, SHA512 as SHA512n, sha512 as sha512n } from \"./sha2.js\";\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA512 = SHA512n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha512 = sha512n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA384 = SHA384n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha384 = sha384n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA512_224 = SHA512_224n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha512_224 = sha512_224n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA512_256 = SHA512_256n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha512_256 = sha512_256n;", "map": {"version": 3, "names": ["SHA384", "SHA384n", "sha384", "sha384n", "sha512_224", "sha512_224n", "SHA512_224", "SHA512_224n", "sha512_256", "sha512_256n", "SHA512_256", "SHA512_256n", "SHA512", "SHA512n", "sha512", "sha512n"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\sha512.ts"], "sourcesContent": ["/**\n * SHA2-512 a.k.a. sha512 and sha384. It is slower than sha256 in js because u64 operations are slow.\n *\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [the paper on truncated SHA512/256](https://eprint.iacr.org/2010/548.pdf).\n * @module\n * @deprecated\n */\nimport {\n  SHA384 as SHA384n,\n  sha384 as sha384n,\n  sha512_224 as sha512_224n,\n  SHA512_224 as SHA512_224n,\n  sha512_256 as sha512_256n,\n  SHA512_256 as SHA512_256n,\n  SHA512 as SHA512n,\n  sha512 as sha512n,\n} from './sha2.ts';\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA512: typeof SHA512n = SHA512n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha512: typeof sha512n = sha512n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA384: typeof SHA384n = SHA384n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha384: typeof sha384n = sha384n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA512_224: typeof SHA512_224n = SHA512_224n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha512_224: typeof sha512_224n = sha512_224n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA512_256: typeof SHA512_256n = SHA512_256n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha512_256: typeof sha512_256n = sha512_256n;\n"], "mappings": "AAAA;;;;;;;;AAQA,SACEA,MAAM,IAAIC,OAAO,EACjBC,MAAM,IAAIC,OAAO,EACjBC,UAAU,IAAIC,WAAW,EACzBC,UAAU,IAAIC,WAAW,EACzBC,UAAU,IAAIC,WAAW,EACzBC,UAAU,IAAIC,WAAW,EACzBC,MAAM,IAAIC,OAAO,EACjBC,MAAM,IAAIC,OAAO,QACZ,WAAW;AAClB;AACA,OAAO,MAAMH,MAAM,GAAmBC,OAAO;AAC7C;AACA,OAAO,MAAMC,MAAM,GAAmBC,OAAO;AAC7C;AACA,OAAO,MAAMf,MAAM,GAAmBC,OAAO;AAC7C;AACA,OAAO,MAAMC,MAAM,GAAmBC,OAAO;AAC7C;AACA,OAAO,MAAMG,UAAU,GAAuBC,WAAW;AACzD;AACA,OAAO,MAAMH,UAAU,GAAuBC,WAAW;AACzD;AACA,OAAO,MAAMK,UAAU,GAAuBC,WAAW;AACzD;AACA,OAAO,MAAMH,UAAU,GAAuBC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}