[package]
name = "escrow_platform"
version = "1.0.0"
authors = ["<PERSON><PERSON>"]

[addresses]
# Use a placeholder for development
escrow_platform = "0x6b70a18147b3a8130c96197760056e1648fe0c8e6371a4c8b32eb6af09a5d563"
# Standard library
std = "0x1"

[dependencies]
AptosFramework = { git = "https://github.com/aptos-labs/aptos-core.git", rev = "mainnet", subdir = "aptos-move/framework/aptos-framework" }

#[dev-addresses]
# Real address for local testing or deployment
#escrow_platform = "0x1488281fca119a985dea35435e53bb4bef835aa0ae7e02fde2ee022eeb86b751
[dev-dependencies]
