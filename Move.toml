[package]
name = "escrow_platform"
version = "1.0.0"
authors = ["<PERSON><PERSON>"]

[addresses]
# Use your actual account address for deployment
escrow_platform = "0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22"
# Standard library
std = "0x1"

[dependencies]
AptosFramework = { git = "https://github.com/aptos-labs/aptos-core.git", rev = "mainnet", subdir = "aptos-move/framework/aptos-framework" }

#[dev-addresses]
# Real address for local testing or deployment
#escrow_platform = "0x1488281fca119a985dea35435e53bb4bef835aa0ae7e02fde2ee022eeb86b751
[dev-dependencies]
