{"ast": null, "code": "import { a as e } from \"./chunk-DZXM2MQY.mjs\";\nfunction t(i, r) {\n  let l = r.bcsToBytes(),\n    a = new e(l);\n  return i.deserialize(a);\n}\nexport { t as a };", "map": {"version": 3, "names": ["t", "i", "r", "l", "bcsToBytes", "a", "e", "deserialize"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\utils\\normalizeBundle.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Deserializer, Serializable } from \"../bcs\";\n\nexport type DeserializableClass<T extends Serializable> = {\n  deserialize(deserializer: Deserializer): T;\n};\n\n/**\n * Utility function that serializes and deserialize an object back into the same bundle as the sdk.\n * This is a workaround to have the `instanceof` operator work when input objects come from a different\n * bundle.\n * @param cls The class of the object to normalize\n * @param value the instance to normalize\n */\nexport function normalizeBundle<T extends Serializable>(cls: DeserializableClass<T>, value: T) {\n  const serializedBytes = value.bcsToBytes();\n  const deserializer = new Deserializer(serializedBytes);\n  return cls.deserialize(deserializer);\n}\n"], "mappings": ";AAgBO,SAASA,EAAwCC,CAAA,EAA6BC,CAAA,EAAU;EAC7F,IAAMC,CAAA,GAAkBD,CAAA,CAAME,UAAA,CAAW;IACnCC,CAAA,GAAe,IAAIC,CAAA,CAAaH,CAAe;EACrD,OAAOF,CAAA,CAAIM,WAAA,CAAYF,CAAY,CACrC;AAAA;AAAA,SAAAL,CAAA,IAAAK,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}