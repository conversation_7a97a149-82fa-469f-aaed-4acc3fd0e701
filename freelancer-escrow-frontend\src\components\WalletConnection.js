import React, { useState } from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';

const WalletConnection = () => {
  const { 
    connect, 
    disconnect, 
    account, 
    connected, 
    connecting, 
    wallets 
  } = useWallet();

  const [showDropdown, setShowDropdown] = useState(false);

  const handleConnect = async (walletName) => {
    try {
      await connect(walletName);
      setShowDropdown(false);
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
      setShowDropdown(false);
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };

  const truncateAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  if (connected && account) {
    return (
      <div className="relative">
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className="flex items-center space-x-3 bg-green-100 hover:bg-green-200 text-green-800 font-medium px-4 py-2 rounded-lg transition-colors"
        >
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span>{truncateAddress(account.address)}</span>
          <span className="text-xs">▼</span>
        </button>

        {showDropdown && (
          <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-2">Wallet Connected</h3>
              <p className="text-sm text-gray-600 font-mono break-all">
                {account.address}
              </p>
            </div>
            <div className="p-4">
              <button
                onClick={handleDisconnect}
                className="w-full bg-red-100 hover:bg-red-200 text-red-800 font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Disconnect Wallet
              </button>
            </div>
          </div>
        )}

        {showDropdown && (
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setShowDropdown(false)}
          />
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        disabled={connecting}
        className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {connecting ? 'Connecting...' : 'Connect Wallet'}
      </button>

      {showDropdown && !connected && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-1">Connect Wallet</h3>
            <p className="text-sm text-gray-600">Choose your Aptos wallet</p>
          </div>
          
          <div className="p-2">
            {wallets.length > 0 ? (
              wallets.map((wallet) => (
                <button
                  key={wallet.name}
                  onClick={() => handleConnect(wallet.name)}
                  disabled={connecting}
                  className="w-full flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-sm">🔗</span>
                  </div>
                  <div className="flex-1 text-left">
                    <p className="font-medium text-gray-900">{wallet.name}</p>
                    <p className="text-xs text-gray-600">Aptos Wallet</p>
                  </div>
                </button>
              ))
            ) : (
              <div className="p-4 text-center">
                <p className="text-sm text-gray-600 mb-2">No wallets detected</p>
                <p className="text-xs text-gray-500">
                  Please install an Aptos wallet extension
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {showDropdown && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default WalletConnection;
