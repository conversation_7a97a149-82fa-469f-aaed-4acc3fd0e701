{"ast": null, "code": "import { a as e } from \"./chunk-A63SMUOU.mjs\";\nvar r = class t extends e {\n  constructor(i) {\n    super(), this.identifier = i;\n  }\n  serialize(i) {\n    i.serializeStr(this.identifier);\n  }\n  static deserialize(i) {\n    let s = i.deserializeStr();\n    return new t(s);\n  }\n};\nexport { r as a };", "map": {"version": 3, "names": ["r", "t", "e", "constructor", "i", "identifier", "serialize", "serializeStr", "deserialize", "s", "deserializeStr", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\identifier.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\n\n/**\n * Representation of an Identifier that can serialized and deserialized.\n * We use Identifier to represent the module \"name\" in \"ModuleId\" and\n * the \"function name\" in \"EntryFunction\"\n */\nexport class Identifier extends Serializable {\n  public identifier: string;\n\n  constructor(identifier: string) {\n    super();\n    this.identifier = identifier;\n  }\n\n  public serialize(serializer: Serializer): void {\n    serializer.serializeStr(this.identifier);\n  }\n\n  static deserialize(deserializer: Deserializer): Identifier {\n    const identifier = deserializer.deserializeStr();\n    return new Identifier(identifier);\n  }\n}\n"], "mappings": ";AAWO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAmBC,CAAa;EAG3CC,YAAYC,CAAA,EAAoB;IAC9B,MAAM,GACN,KAAKC,UAAA,GAAaD,CACpB;EAAA;EAEOE,UAAUF,CAAA,EAA8B;IAC7CA,CAAA,CAAWG,YAAA,CAAa,KAAKF,UAAU,CACzC;EAAA;EAEA,OAAOG,YAAYJ,CAAA,EAAwC;IACzD,IAAMK,CAAA,GAAaL,CAAA,CAAaM,cAAA,CAAe;IAC/C,OAAO,IAAIT,CAAA,CAAWQ,CAAU,CAClC;EAAA;AACF;AAAA,SAAAT,CAAA,IAAAW,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}