{"ast": null, "code": "import { a as r } from \"./chunk-A63SMUOU.mjs\";\nimport { b as i } from \"./chunk-BCUSI3N6.mjs\";\nvar t = class a extends r {\n  constructor(e) {\n    super(), this.value = i.fromHexInput(e).toUint8Array();\n  }\n  serialize(e) {\n    e.serializeFixedBytes(this.value);\n  }\n  serializeForEntryFunction(e) {\n    e.serialize(this);\n  }\n  serializeForScriptFunction(e) {\n    e.serialize(this);\n  }\n  static deserialize(e, s) {\n    let o = e.deserializeFixedBytes(s);\n    return new a(o);\n  }\n};\nexport { t as a };", "map": {"version": 3, "names": ["t", "a", "r", "constructor", "e", "value", "i", "fromHexInput", "toUint8Array", "serialize", "serializeFixedBytes", "serializeForEntryFunction", "serializeForScriptFunction", "deserialize", "s", "o", "deserializeFixedBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\serializable\\fixedBytes.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../serializer\";\nimport { Deserializer } from \"../deserializer\";\nimport { HexInput } from \"../../types\";\nimport { Hex } from \"../../core/hex\";\nimport { TransactionArgument } from \"../../transactions/instances/transactionArgument\";\n\n/**\n *  This class exists to represent a contiguous sequence of already serialized BCS-bytes.\n *\n *  It differs from most other Serializable classes in that its internal byte buffer is serialized to BCS\n *  bytes exactly as-is, without prepending the length of the bytes.\n *\n *  If you want to write your own serialization function and pass the bytes as a transaction argument,\n *  you should use this class.\n *\n *  This class is also more generally used to represent type-agnostic BCS bytes as a vector<u8>.\n *\n *  An example of this is the bytes resulting from entry function arguments that have been serialized\n *  for an entry function.\n *\n *  @example\n *  const yourCustomSerializedBytes = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);\n *  const fixedBytes = new FixedBytes(yourCustomSerializedBytes);\n *  const payload = await generateTransactionPayload({\n *    function: \"0xbeefcafe::your_module::your_function_that_requires_custom_serialization\",\n *    functionArguments: [yourCustomBytes],\n *  });\n *\n *  For example, if you store each of the 32 bytes for an address as a U8 in a MoveVector<U8>, when you\n *  serialize that MoveVector<U8>, it will be serialized to 33 bytes. If you solely want to pass around\n *  the 32 bytes as a Serializable class that *does not* prepend the length to the BCS-serialized representation,\n *  use this class.\n *\n * @params value: HexInput representing a sequence of Uint8 bytes\n * @returns a Serializable FixedBytes instance, which when serialized, does not prepend the length of the bytes\n * @see EntryFunctionBytes\n */\nexport class FixedBytes extends Serializable implements TransactionArgument {\n  public value: Uint8Array;\n\n  constructor(value: HexInput) {\n    super();\n    this.value = Hex.fromHexInput(value).toUint8Array();\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    serializer.serialize(this);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer, length: number): FixedBytes {\n    const bytes = deserializer.deserializeFixedBytes(length);\n    return new FixedBytes(bytes);\n  }\n}\n"], "mappings": ";;AAwCO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAmBC,CAA4C;EAG1EC,YAAYC,CAAA,EAAiB;IAC3B,MAAM,GACN,KAAKC,KAAA,GAAQC,CAAA,CAAIC,YAAA,CAAaH,CAAK,EAAEI,YAAA,CAAa,CACpD;EAAA;EAEAC,UAAUL,CAAA,EAA8B;IACtCA,CAAA,CAAWM,mBAAA,CAAoB,KAAKL,KAAK,CAC3C;EAAA;EAEAM,0BAA0BP,CAAA,EAA8B;IACtDA,CAAA,CAAWK,SAAA,CAAU,IAAI,CAC3B;EAAA;EAEAG,2BAA2BR,CAAA,EAA8B;IACvDA,CAAA,CAAWK,SAAA,CAAU,IAAI,CAC3B;EAAA;EAEA,OAAOI,YAAYT,CAAA,EAA4BU,CAAA,EAA4B;IACzE,IAAMC,CAAA,GAAQX,CAAA,CAAaY,qBAAA,CAAsBF,CAAM;IACvD,OAAO,IAAIb,CAAA,CAAWc,CAAK,CAC7B;EAAA;AACF;AAAA,SAAAf,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}