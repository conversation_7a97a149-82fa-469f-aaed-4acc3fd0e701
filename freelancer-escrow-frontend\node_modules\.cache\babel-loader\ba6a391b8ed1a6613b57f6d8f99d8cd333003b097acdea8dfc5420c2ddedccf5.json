{"ast": null, "code": "var s = class extends Error {\n  constructor(e, t, a) {\n    super(a), this.name = \"AptosApiError\", this.url = t.url, this.status = t.status, this.statusText = t.statusText, this.data = t.data, this.request = e;\n  }\n};\nexport { s as a };", "map": {"version": 3, "names": ["s", "Error", "constructor", "e", "t", "a", "name", "url", "status", "statusText", "data", "request"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\client\\types.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AptosRequest } from \"../types\";\n\n/**\n * The API response type\n *\n * @param status - the response status. i.e. 200\n * @param statusText - the response message\n * @param data the response data\n * @param url the url the request was made to\n * @param headers the response headers\n * @param config (optional) - the request object\n * @param request (optional) - the request object\n */\nexport interface AptosResponse<Req, Res> {\n  status: number;\n  statusText: string;\n  data: Res;\n  url: string;\n  headers: any;\n  config?: any;\n  request?: Req;\n}\n\n/**\n * The type returned from an API error\n *\n * @param name - the error name \"AptosApiError\"\n * @param url the url the request was made to\n * @param status - the response status. i.e. 400\n * @param statusText - the response message\n * @param data the response data\n * @param request - the AptosRequest\n */\nexport class AptosApiError extends Error {\n  readonly url: string;\n\n  readonly status: number;\n\n  readonly statusText: string;\n\n  readonly data: any;\n\n  readonly request: AptosRequest;\n\n  constructor(request: AptosRequest, response: AptosResponse<any, any>, message: string) {\n    super(message);\n\n    this.name = \"AptosApiError\";\n    this.url = response.url;\n    this.status = response.status;\n    this.statusText = response.statusText;\n    this.data = response.data;\n    this.request = request;\n  }\n}\n"], "mappings": "AAoCO,IAAMA,CAAA,GAAN,cAA4BC,KAAM;EAWvCC,YAAYC,CAAA,EAAuBC,CAAA,EAAmCC,CAAA,EAAiB;IACrF,MAAMA,CAAO,GAEb,KAAKC,IAAA,GAAO,iBACZ,KAAKC,GAAA,GAAMH,CAAA,CAASG,GAAA,EACpB,KAAKC,MAAA,GAASJ,CAAA,CAASI,MAAA,EACvB,KAAKC,UAAA,GAAaL,CAAA,CAASK,UAAA,EAC3B,KAAKC,IAAA,GAAON,CAAA,CAASM,IAAA,EACrB,KAAKC,OAAA,GAAUR,CACjB;EAAA;AACF;AAAA,SAAAH,CAAA,IAAAK,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}