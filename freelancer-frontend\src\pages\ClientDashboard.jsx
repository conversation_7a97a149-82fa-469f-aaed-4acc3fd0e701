import React, { useState } from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import { useEscrow } from '../contexts/EscrowContext';
import { Plus, Clock, DollarSign, CheckCircle, AlertCircle, Eye } from 'lucide-react';
import aptosService from '../services/aptosService';
import CreateEscrowModal from './CreateEscrowModal';
import EscrowCard from './EscrowCard';

const ClientDashboard = () => {
  const { account } = useWallet();
  const { escrows, loading, balance, fundEscrow, approveWork } = useEscrow();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedEscrow, setSelectedEscrow] = useState(null);

  // Filter escrows where user is the client
  const clientEscrows = escrows.filter(escrow => 
    escrow.client === account?.address || escrow.client.includes('1234') // Mock data filter
  );

  const stats = {
    total: clientEscrows.length,
    active: clientEscrows.filter(e => 
      [aptosService.ESCROW_STATUS.FUNDED, aptosService.ESCROW_STATUS.IN_PROGRESS].includes(e.status)
    ).length,
    completed: clientEscrows.filter(e => e.status === aptosService.ESCROW_STATUS.COMPLETED).length,
    totalSpent: clientEscrows
      .filter(e => e.status === aptosService.ESCROW_STATUS.COMPLETED)
      .reduce((sum, e) => sum + e.amount, 0)
  };

  const handleFundEscrow = async (escrowId) => {
    try {
      await fundEscrow(escrowId);
      alert('Escrow funded successfully!');
    } catch (error) {
      alert('Failed to fund escrow: ' + error.message);
    }
  };

  const handleApproveWork = async (escrowId) => {
    try {
      await approveWork(escrowId);
      alert('Work approved and payment released!');
    } catch (error) {
      alert('Failed to approve work: ' + error.message);
    }
  };

  if (!account) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-secondary-900 mb-2">
            Connect Your Wallet
          </h3>
          <p className="text-secondary-600">
            Please connect your wallet to access the client dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Client Dashboard</h1>
          <p className="text-secondary-600">Manage your projects and payments</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <p className="text-sm text-secondary-600">Balance</p>
            <p className="text-lg font-semibold text-secondary-900">{balance} APT</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Create Project</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Projects</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.total}</p>
            </div>
            <Eye className="w-8 h-8 text-primary-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Active Projects</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.active}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Completed</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.completed}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600">Total Spent</p>
              <p className="text-2xl font-bold text-secondary-900">{stats.totalSpent.toFixed(2)} APT</p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-600" />
          </div>
        </div>
      </div>

      {/* Projects List */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-secondary-900">Your Projects</h2>
        
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : clientEscrows.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-secondary-600">No projects found. Create your first project!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {clientEscrows.map((escrow) => (
              <EscrowCard
                key={escrow.id}
                escrow={escrow}
                userRole="client"
                onFund={() => handleFundEscrow(escrow.id)}
                onApprove={() => handleApproveWork(escrow.id)}
                onViewDetails={() => setSelectedEscrow(escrow)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Create Escrow Modal */}
      {showCreateModal && (
        <CreateEscrowModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            // Escrows will be refreshed automatically by the context
          }}
        />
      )}
    </div>
  );
};

export default ClientDashboard;
