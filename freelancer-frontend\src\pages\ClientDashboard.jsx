import { useWallet } from '@aptos-labs/wallet-adapter-react';
import {
    AlertCircle,
    CheckCircle,
    Clock,
    DollarSign,
    Eye,
    Plus
} from 'lucide-react';
import { useState } from 'react';
import { useEscrow } from '../contexts/EscrowContext';

const ClientDashboard = () => {
  const { account, connected } = useWallet();
  const { escrows, loading, balance, createEscrow, fundEscrow, approveWork } = useEscrow();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedEscrow, setSelectedEscrow] = useState(null);
  const [isCreating, setIsCreating] = useState(false);

  // Mock data for demonstration
  const mockEscrows = [
    {
      id: 1,
      client: account?.address || '0x1234...5678',
      freelancer: '0x8765...4321',
      title: 'Modern React Website Development',
      description: 'Build a responsive, modern website using React.js with Tailwind CSS. Include user authentication, dashboard, and payment integration.',
      category: 'Web Development',
      amount: 5.0,
      platform_fee: 0.125,
      deadline: Date.now() + 7 * 24 * 60 * 60 * 1000,
      status: ESCROW_STATUS.FUNDED,
      created_at: Date.now() - 2 * 24 * 60 * 60 * 1000,
      funded_at: Date.now() - 1 * 24 * 60 * 60 * 1000,
      started_at: 0,
      submitted_at: 0,
      completed_at: 0
    },
    {
      id: 2,
      client: account?.address || '0x2345...6789',
      freelancer: '0x9876...5432',
      title: 'Mobile App UI/UX Design',
      description: 'Design a complete mobile application interface with modern UI/UX principles.',
      category: 'Design',
      amount: 3.5,
      platform_fee: 0.0875,
      deadline: Date.now() + 5 * 24 * 60 * 60 * 1000,
      status: ESCROW_STATUS.IN_PROGRESS,
      created_at: Date.now() - 3 * 24 * 60 * 60 * 1000,
      funded_at: Date.now() - 2 * 24 * 60 * 60 * 1000,
      started_at: Date.now() - 1 * 24 * 60 * 60 * 1000,
      submitted_at: 0,
      completed_at: 0
    }
  ];

  const clientEscrows = connected ? mockEscrows : [];

  const stats = {
    total: clientEscrows.length,
    active: clientEscrows.filter(e =>
      [ESCROW_STATUS.FUNDED, ESCROW_STATUS.IN_PROGRESS].includes(e.status)
    ).length,
    completed: clientEscrows.filter(e => e.status === ESCROW_STATUS.COMPLETED).length,
    totalSpent: clientEscrows
      .filter(e => e.status === ESCROW_STATUS.COMPLETED)
      .reduce((sum, e) => sum + e.amount, 0),
    pendingReview: clientEscrows.filter(e => e.status === ESCROW_STATUS.SUBMITTED).length
  };

  const handleCreateProject = () => {
    setShowCreateModal(true);
  };

  const handleFundEscrow = async (escrowId) => {
    try {
      setIsCreating(true);
      await fundEscrow(escrowId);
      // Show success notification
    } catch (error) {
      console.error('Failed to fund escrow:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleApproveWork = async (escrowId) => {
    try {
      setIsCreating(true);
      await approveWork(escrowId);
      // Show success notification
    } catch (error) {
      console.error('Failed to approve work:', error);
    } finally {
      setIsCreating(false);
    }
  };

  if (!connected) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Card className="max-w-md w-full">
          <Card.Body className="text-center py-12">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="w-8 h-8 text-primary-600" />
            </div>
            <h3 className="text-xl font-semibold text-secondary-900 mb-3">
              Connect Your Wallet
            </h3>
            <p className="text-secondary-600 mb-6">
              Please connect your Aptos wallet to access the client dashboard and start creating projects.
            </p>
            <Button variant="primary" size="lg">
              Connect Wallet
            </Button>
          </Card.Body>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">
            Client Dashboard
          </h1>
          <p className="text-secondary-600">
            Manage your projects, track progress, and handle payments
          </p>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <div className="text-right">
            <p className="text-sm text-secondary-600">Available Balance</p>
            <p className="text-2xl font-bold text-secondary-900">
              {formatCurrency(balance || '12.5')}
            </p>
          </div>
          <Button
            variant="primary"
            size="lg"
            icon={<Plus className="w-5 h-5" />}
            onClick={handleCreateProject}
            className="whitespace-nowrap"
          >
            Create Project
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card p-6 hover:shadow-lg transition-shadow group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600 mb-1">Total Projects</p>
              <p className="text-3xl font-bold text-secondary-900">{stats.total}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
              <Eye className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="card p-6 hover:shadow-lg transition-shadow group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600 mb-1">Active Projects</p>
              <p className="text-3xl font-bold text-secondary-900">{stats.active}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="card p-6 hover:shadow-lg transition-shadow group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600 mb-1">Completed</p>
              <p className="text-3xl font-bold text-secondary-900">{stats.completed}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="card p-6 hover:shadow-lg transition-shadow group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-secondary-600 mb-1">Total Spent</p>
              <p className="text-3xl font-bold text-secondary-900">
                {formatCurrency(stats.totalSpent)}
              </p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
              <DollarSign className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Projects List */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-secondary-900">Your Projects</h2>
          <div className="px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
            {clientEscrows.length} Total
          </div>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="card p-6 animate-pulse">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-10 h-10 bg-secondary-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-secondary-200 rounded w-3/4"></div>
                    <div className="h-3 bg-secondary-200 rounded w-1/2"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-secondary-200 rounded"></div>
                  <div className="h-3 bg-secondary-200 rounded"></div>
                  <div className="h-3 bg-secondary-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : clientEscrows.length === 0 ? (
          <div className="card">
            <div className="text-center py-16 px-6">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Plus className="w-8 h-8 text-secondary-400" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                No projects yet
              </h3>
              <p className="text-secondary-600 mb-6">
                Create your first project to get started with FreelanceChain
              </p>
              <button
                onClick={handleCreateProject}
                className="btn-primary flex items-center space-x-2 mx-auto"
              >
                <Plus className="w-4 h-4" />
                <span>Create Your First Project</span>
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {clientEscrows.map((escrow, index) => (
              <ProjectCard
                key={escrow.id}
                escrow={escrow}
                onFund={() => handleFundEscrow(escrow.id)}
                onApprove={() => handleApproveWork(escrow.id)}
                onViewDetails={() => setSelectedEscrow(escrow)}
                isLoading={isCreating}
                style={{ animationDelay: `${index * 100}ms` }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Project Card Component
const ProjectCard = ({ escrow, onFund, onApprove, onViewDetails, isLoading, ...props }) => {
  const timeLeft = getTimeUntilDeadline(escrow.deadline);
  const statusColor = STATUS_COLORS[escrow.status] || 'secondary';

  return (
    <div className="card hover:shadow-lg transition-all duration-200 animate-fade-in" {...props}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-secondary-900 mb-2 line-clamp-1">
              {escrow.title}
            </h3>
            <div className="flex items-center space-x-3">
              <div className={`px-2 py-1 rounded-full text-xs font-medium badge-${statusColor}`}>
                {STATUS_LABELS[escrow.status]}
              </div>
              <span className="text-xs text-secondary-500">#{escrow.id}</span>
            </div>
          </div>
          <div className="text-right ml-4">
            <p className="text-xl font-bold text-secondary-900">
              {formatCurrency(escrow.amount)}
            </p>
            <p className="text-xs text-secondary-500">
              +{formatCurrency(escrow.platform_fee)} fee
            </p>
          </div>
        </div>

        {/* Description */}
        <p className="text-secondary-600 text-sm mb-4 line-clamp-2">
          {escrow.description}
        </p>

        {/* Details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-secondary-600">Category:</span>
            <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
              {escrow.category}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-secondary-600">Deadline:</span>
            <span className={`font-medium ${timeLeft.expired ? 'text-red-600' : timeLeft.days <= 3 ? 'text-yellow-600' : 'text-secondary-900'}`}>
              {timeLeft.expired ? 'Overdue' : `${timeLeft.days}d ${timeLeft.hours}h left`}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-secondary-600">Created:</span>
            <span className="text-secondary-900">{formatDate(escrow.created_at)}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-secondary-200">
          <button
            onClick={onViewDetails}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View Details
          </button>

          {escrow.status === ESCROW_STATUS.CREATED && (
            <button
              onClick={onFund}
              disabled={isLoading}
              className="btn-primary btn-sm flex items-center space-x-2"
            >
              <DollarSign className="w-4 h-4" />
              <span>Fund Project</span>
            </button>
          )}

          {escrow.status === ESCROW_STATUS.SUBMITTED && (
            <button
              onClick={onApprove}
              disabled={isLoading}
              className="btn-success btn-sm flex items-center space-x-2"
            >
              <CheckCircle className="w-4 h-4" />
              <span>Approve & Pay</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientDashboard;
