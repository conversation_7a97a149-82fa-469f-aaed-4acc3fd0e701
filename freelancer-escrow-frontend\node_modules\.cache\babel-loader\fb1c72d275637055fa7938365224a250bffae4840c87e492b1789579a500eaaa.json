{"ast": null, "code": "export * from './connect.js';\nexport * from './disconnect.js';\nexport * from './events.js';", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\features\\src\\index.ts"], "sourcesContent": ["import type { WalletWithFeatures } from '@wallet-standard/base';\nimport type { StandardConnectFeature } from './connect.js';\nimport type { StandardDisconnectFeature } from './disconnect.js';\nimport type { StandardEventsFeature } from './events.js';\n\n/**\n * Type alias for some or all {@link \"@wallet-standard/base\".Wallet.features} implemented within the reserved `standard`\n * namespace.\n *\n * @group Features\n */\nexport type StandardFeatures = StandardConnectFeature | StandardDisconnectFeature | StandardEventsFeature;\n\n/**\n * Type alias for a {@link \"@wallet-standard/base\".Wallet} that implements some or all {@link StandardFeatures}.\n *\n * @group Features\n */\nexport type WalletWithStandardFeatures = WalletWithFeatures<StandardFeatures>;\n\nexport * from './connect.js';\nexport * from './disconnect.js';\nexport * from './events.js';\n"], "mappings": "AAoBA,cAAc,cAAc;AAC5B,cAAc,iBAAiB;AAC/B,cAAc,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}