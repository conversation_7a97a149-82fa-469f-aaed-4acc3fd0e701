import React, { useState, useEffect } from 'react';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import { 
  Wallet, 
  LogOut, 
  Copy, 
  Check, 
  ChevronDown, 
  ExternalLink, 
  Zap, 
  DollarSign,
  Activity,
  Shield,
  AlertCircle
} from 'lucide-react';
import { Aptos, AptosConfig, Network } from '@aptos-labs/ts-sdk';

const WalletConnectionEnhanced = () => {
  const { 
    connect, 
    disconnect, 
    account, 
    connected, 
    connecting, 
    wallets 
  } = useWallet();

  const [copied, setCopied] = useState(false);
  const [balance, setBalance] = useState('0');
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [networkStatus, setNetworkStatus] = useState('connected');

  // Initialize Aptos client
  const aptos = new Aptos(new AptosConfig({ network: Network.DEVNET }));

  // Fetch balance when account changes
  useEffect(() => {
    const fetchBalance = async () => {
      if (account?.address) {
        try {
          setIsLoading(true);
          const resources = await aptos.getAccountResources({
            accountAddress: account.address
          });
          
          const coinResource = resources.find(
            (resource) => resource.type === '0x1::coin::CoinStore<0x1::aptos_coin::AptosCoin>'
          );
          
          if (coinResource) {
            const balanceValue = parseInt(coinResource.data.coin.value) / *********;
            setBalance(balanceValue.toFixed(4));
          }
          setNetworkStatus('connected');
        } catch (error) {
          console.error('Error fetching balance:', error);
          setNetworkStatus('error');
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchBalance();
    const interval = setInterval(fetchBalance, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, [account?.address]);

  const handleConnect = async (walletName) => {
    try {
      setIsLoading(true);
      await connect(walletName);
      setShowDropdown(false);
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
      setBalance('0');
      setShowDropdown(false);
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };

  const copyAddress = async () => {
    if (account?.address) {
      try {
        await navigator.clipboard.writeText(account.address);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy address:', error);
      }
    }
  };

  const truncateAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const getNetworkStatusIcon = () => {
    switch (networkStatus) {
      case 'connected':
        return <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>;
      case 'error':
        return <div className="w-2 h-2 bg-red-500 rounded-full"></div>;
      default:
        return <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>;
    }
  };

  if (connected && account) {
    return (
      <div className="relative">
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className="flex items-center space-x-3 bg-white hover:bg-secondary-50 border border-secondary-200 rounded-xl px-4 py-2.5 transition-all duration-200 shadow-sm hover:shadow-md"
        >
          {/* Network Status */}
          <div className="flex items-center space-x-2">
            {getNetworkStatusIcon()}
            <span className="text-xs text-secondary-600 font-medium">Devnet</span>
          </div>

          {/* Balance */}
          <div className="flex items-center space-x-2 px-3 py-1 bg-green-50 rounded-lg">
            <DollarSign className="w-4 h-4 text-green-600" />
            <span className="text-sm font-semibold text-green-700">
              {isLoading ? '...' : `${balance} APT`}
            </span>
          </div>

          {/* Address */}
          <div className="flex items-center space-x-2">
            <Wallet className="w-4 h-4 text-primary-600" />
            <span className="text-sm font-medium text-secondary-700">
              {truncateAddress(account.address)}
            </span>
          </div>

          <ChevronDown className={`w-4 h-4 text-secondary-500 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
        </button>

        {/* Dropdown Menu */}
        {showDropdown && (
          <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-xl border border-secondary-200 z-50 animate-fade-in">
            <div className="p-4 border-b border-secondary-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-secondary-900">Wallet Details</h3>
                <div className="flex items-center space-x-1">
                  {getNetworkStatusIcon()}
                  <span className="text-xs text-secondary-600">Aptos Devnet</span>
                </div>
              </div>
              
              {/* Balance Card */}
              <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-3 mb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-secondary-600 mb-1">Available Balance</p>
                    <p className="text-xl font-bold text-secondary-900">
                      {isLoading ? (
                        <div className="animate-pulse bg-secondary-200 h-6 w-20 rounded"></div>
                      ) : (
                        `${balance} APT`
                      )}
                    </p>
                  </div>
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <Zap className="w-5 h-5 text-primary-600" />
                  </div>
                </div>
              </div>

              {/* Address */}
              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div className="flex-1">
                  <p className="text-xs text-secondary-600 mb-1">Address</p>
                  <p className="text-sm font-mono text-secondary-900">
                    {truncateAddress(account.address)}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={copyAddress}
                    className="p-2 hover:bg-secondary-200 rounded-lg transition-colors"
                    title="Copy address"
                  >
                    {copied ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-secondary-500" />
                    )}
                  </button>
                  <a
                    href={`https://explorer.aptoslabs.com/account/${account.address}?network=devnet`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 hover:bg-secondary-200 rounded-lg transition-colors"
                    title="View on Aptos Explorer"
                  >
                    <ExternalLink className="w-4 h-4 text-secondary-500" />
                  </a>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="p-4">
              <button
                onClick={handleDisconnect}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2.5 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg transition-colors font-medium"
              >
                <LogOut className="w-4 h-4" />
                <span>Disconnect Wallet</span>
              </button>
            </div>
          </div>
        )}

        {/* Click outside to close */}
        {showDropdown && (
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setShowDropdown(false)}
          />
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        disabled={connecting}
        className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white font-medium px-6 py-2.5 rounded-xl transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <Wallet className="w-4 h-4" />
        <span>{connecting ? 'Connecting...' : 'Connect Wallet'}</span>
        {!connecting && <ChevronDown className="w-4 h-4" />}
      </button>

      {/* Wallet Selection Dropdown */}
      {showDropdown && !connected && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-xl border border-secondary-200 z-50 animate-fade-in">
          <div className="p-4 border-b border-secondary-200">
            <h3 className="font-semibold text-secondary-900 mb-1">Connect Wallet</h3>
            <p className="text-xs text-secondary-600">Choose your preferred Aptos wallet</p>
          </div>
          
          <div className="p-2">
            {wallets.length > 0 ? (
              wallets.map((wallet) => (
                <button
                  key={wallet.name}
                  onClick={() => handleConnect(wallet.name)}
                  disabled={connecting || isLoading}
                  className="w-full flex items-center space-x-3 p-3 hover:bg-secondary-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <img 
                    src={wallet.icon} 
                    alt={wallet.name} 
                    className="w-8 h-8 rounded-lg"
                  />
                  <div className="flex-1 text-left">
                    <p className="font-medium text-secondary-900">{wallet.name}</p>
                    <p className="text-xs text-secondary-600">
                      {wallet.name === 'Petra' ? 'Most popular Aptos wallet' : 
                       wallet.name === 'Martian' ? 'Multi-chain wallet' : 
                       'Aptos wallet'}
                    </p>
                  </div>
                  {connecting && <div className="animate-spin w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full"></div>}
                </button>
              ))
            ) : (
              <div className="p-4 text-center">
                <AlertCircle className="w-8 h-8 text-secondary-400 mx-auto mb-2" />
                <p className="text-sm text-secondary-600 mb-2">No wallets detected</p>
                <p className="text-xs text-secondary-500">
                  Please install Petra, Martian, or another Aptos wallet extension
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {showDropdown && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default WalletConnectionEnhanced;
