{"ast": null, "code": "import { getWallets as o } from \"@wallet-standard/core\";\nvar n = [\"aptos:account\", \"aptos:connect\", \"aptos:disconnect\", \"aptos:network\", \"aptos:onAccountChange\", \"aptos:onNetworkChange\", \"aptos:signMessage\", \"aptos:signTransaction\"];\nfunction i(t, e = []) {\n  return [...n, ...e].every(s => s in t.features);\n}\nfunction d() {\n  let {\n      get: t,\n      on: e\n    } = o(),\n    s = t(),\n    a = [];\n  return s.map(l => {\n    i(l) && a.push(l);\n  }), {\n    aptosWallets: a,\n    on: e\n  };\n}\nexport { i as a, d as b };", "map": {"version": 3, "names": ["getWallets", "o", "n", "i", "t", "e", "every", "s", "features", "d", "get", "on", "a", "map", "l", "push", "aptosWallets", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\detect.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport {\n  Wallet,\n  WalletWithFeatures,\n  WalletsEventsListeners,\n  getWallets\n} from '@wallet-standard/core'\n\nimport { MinimallyRequiredFeatures } from './features'\nimport { AptosWallet } from './wallet'\n\n// These features are absolutely required for wallets to function in the Aptos ecosystem.\n// Eventually, as wallets have more consistent support of features, we may want to extend this list.\nconst REQUIRED_FEATURES: (keyof MinimallyRequiredFeatures)[] = [\n  'aptos:account',\n  'aptos:connect',\n  'aptos:disconnect',\n  'aptos:network',\n  'aptos:onAccountChange',\n  'aptos:onNetworkChange',\n  'aptos:signMessage',\n  'aptos:signTransaction'\n]\n\nexport function isWalletWithRequiredFeatureSet<AdditionalFeatures extends Wallet['features']>(\n  wallet: Wallet,\n  additionalFeatures: (keyof AdditionalFeatures)[] = []\n): wallet is WalletWithFeatures<MinimallyRequiredFeatures & AdditionalFeatures> {\n  return [...REQUIRED_FEATURES, ...additionalFeatures].every(\n    (feature) => feature in wallet.features\n  )\n}\n\n/**\n * Helper function to get only Aptos wallets\n * @returns Aptos compatible wallets and `on` event to listen to wallets register event\n */\nexport function getAptosWallets(): {\n  aptosWallets: AptosWallet[]\n  on: <E extends keyof WalletsEventsListeners>(\n    event: E,\n    listener: WalletsEventsListeners[E]\n  ) => () => void\n} {\n  const { get, on } = getWallets()\n\n  const wallets = get()\n\n  const aptosWallets: Wallet[] = []\n\n  wallets.map((wallet: Wallet) => {\n    const isAptos = isWalletWithRequiredFeatureSet(wallet)\n\n    if (isAptos) {\n      aptosWallets.push(wallet)\n    }\n  })\n\n  return { aptosWallets: aptosWallets as AptosWallet[], on }\n}\n"], "mappings": "AAGA,SAIEA,UAAA,IAAAC,CAAA,QACK;AAOP,IAAMC,CAAA,GAAyD,CAC7D,iBACA,iBACA,oBACA,iBACA,yBACA,yBACA,qBACA,uBACF;AAEO,SAASC,EACdC,CAAA,EACAC,CAAA,GAAmD,EAAC,EAC0B;EAC9E,OAAO,CAAC,GAAGH,CAAA,EAAmB,GAAGG,CAAkB,EAAEC,KAAA,CAClDC,CAAA,IAAYA,CAAA,IAAWH,CAAA,CAAOI,QACjC,CACF;AAAA;AAMO,SAASC,EAAA,EAMd;EACA,IAAM;MAAEC,GAAA,EAAAN,CAAA;MAAKO,EAAA,EAAAN;IAAG,IAAIJ,CAAA,CAAW;IAEzBM,CAAA,GAAUH,CAAA,CAAI;IAEdQ,CAAA,GAAyB,EAAC;EAEhC,OAAAL,CAAA,CAAQM,GAAA,CAAKC,CAAA,IAAmB;IACdX,CAAA,CAA+BW,CAAM,KAGnDF,CAAA,CAAaG,IAAA,CAAKD,CAAM,CAE5B;EAAA,CAAC,GAEM;IAAEE,YAAA,EAAcJ,CAAA;IAA+BD,EAAA,EAAAN;EAAG,CAC3D;AAAA;AAAA,SAAAF,CAAA,IAAAS,CAAA,EAAAH,CAAA,IAAAQ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}