{"ast": null, "code": "import { a as i } from \"./chunk-JPDT6E3B.mjs\";\nimport { a as e } from \"./chunk-K7DBDI2I.mjs\";\nimport { a as r } from \"./chunk-A63SMUOU.mjs\";\nvar n = class o extends r {\n  constructor(t, a) {\n    super(), this.raw_txn = t, this.authenticator = a;\n  }\n  serialize(t) {\n    this.raw_txn.serialize(t), this.authenticator.serialize(t);\n  }\n  static deserialize(t) {\n    let a = i.deserialize(t),\n      s = e.deserialize(t);\n    return new o(a, s);\n  }\n};\nexport { n as a };", "map": {"version": 3, "names": ["n", "o", "r", "constructor", "t", "a", "raw_txn", "authenticator", "serialize", "deserialize", "i", "s", "e"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\signedTransaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\nimport { TransactionAuthenticator } from \"../authenticator/transaction\";\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { RawTransaction } from \"./rawTransaction\";\n\nexport class SignedTransaction extends Serializable {\n  public readonly raw_txn: RawTransaction;\n\n  public readonly authenticator: TransactionAuthenticator;\n\n  /**\n   * A SignedTransaction consists of a raw transaction and an authenticator. The authenticator\n   * contains a client's public key and the signature of the raw transaction.\n   *\n   * @see {@link https://aptos.dev/integration/creating-a-signed-transaction | Creating a Signed Transaction}\n   *\n   * @param raw_txn\n   * @param authenticator Contains a client's public key and the signature of the raw transaction.\n   * Authenticator has 3 flavors: single signature, multi-signature and multi-agent.\n   * @see {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/authenticator.rs} for details.\n   */\n  constructor(raw_txn: RawTransaction, authenticator: TransactionAuthenticator) {\n    super();\n    this.raw_txn = raw_txn;\n    this.authenticator = authenticator;\n  }\n\n  serialize(serializer: Serializer): void {\n    this.raw_txn.serialize(serializer);\n    this.authenticator.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): SignedTransaction {\n    const raw_txn = RawTransaction.deserialize(deserializer);\n    const authenticator = TransactionAuthenticator.deserialize(deserializer);\n    return new SignedTransaction(raw_txn, authenticator);\n  }\n}\n"], "mappings": ";;;AAUO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAA0BC,CAAa;EAgBlDC,YAAYC,CAAA,EAAyBC,CAAA,EAAyC;IAC5E,MAAM,GACN,KAAKC,OAAA,GAAUF,CAAA,EACf,KAAKG,aAAA,GAAgBF,CACvB;EAAA;EAEAG,UAAUJ,CAAA,EAA8B;IACtC,KAAKE,OAAA,CAAQE,SAAA,CAAUJ,CAAU,GACjC,KAAKG,aAAA,CAAcC,SAAA,CAAUJ,CAAU,CACzC;EAAA;EAEA,OAAOK,YAAYL,CAAA,EAA+C;IAChE,IAAMC,CAAA,GAAUK,CAAA,CAAeD,WAAA,CAAYL,CAAY;MACjDO,CAAA,GAAgBC,CAAA,CAAyBH,WAAA,CAAYL,CAAY;IACvE,OAAO,IAAIH,CAAA,CAAkBI,CAAA,EAASM,CAAa,CACrD;EAAA;AACF;AAAA,SAAAX,CAAA,IAAAK,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}