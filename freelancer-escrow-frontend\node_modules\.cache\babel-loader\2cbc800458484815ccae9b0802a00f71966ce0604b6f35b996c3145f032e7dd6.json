{"ast": null, "code": "/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from \"./_u64.js\";\n// prettier-ignore\nimport { abytes, aexists, anumber, aoutput, clean, createHasher, createXOFer, Hash, swap32IfBE, toBytes, u32 } from \"./utils.js\";\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((round + 1) * (round + 2) / 2 % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = (R << _1n ^ (R >> _7n) * _0x71n) % _256n;\n    if (R & _2n) t ^= _1n << (_1n << /* @__PURE__ */BigInt(j)) - _1n;\n  }\n  _SHA3_IOTA.push(t);\n}\nconst IOTAS = split(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s);\nconst rotlL = (h, l, s) => s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s);\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s, rounds = 24) {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  clean(B);\n}\n/** Keccak sponge function. */\nexport class Keccak extends Hash {\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n    super();\n    this.pos = 0;\n    this.posOut = 0;\n    this.finished = false;\n    this.destroyed = false;\n    this.enableXOF = false;\n    this.blockLen = blockLen;\n    this.suffix = suffix;\n    this.outputLen = outputLen;\n    this.enableXOF = enableXOF;\n    this.rounds = rounds;\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (!(0 < blockLen && blockLen < 200)) throw new Error('only keccak-f1600 function is supported');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  clone() {\n    return this._cloneInto();\n  }\n  keccak() {\n    swap32IfBE(this.state32);\n    keccakP(this.state32, this.rounds);\n    swap32IfBE(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data) {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    const {\n      blockLen,\n      state\n    } = this;\n    const len = data.length;\n    for (let pos = 0; pos < len;) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  finish() {\n    if (this.finished) return;\n    this.finished = true;\n    const {\n      state,\n      suffix,\n      pos,\n      blockLen\n    } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  writeInto(out) {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const {\n      blockLen\n    } = this;\n    for (let pos = 0, len = out.length; pos < len;) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out) {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes) {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out) {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest() {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy() {\n    this.destroyed = true;\n    clean(this.state);\n  }\n  _cloneInto(to) {\n    const {\n      blockLen,\n      suffix,\n      outputLen,\n      rounds,\n      enableXOF\n    } = this;\n    to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\nconst gen = (suffix, blockLen, outputLen) => createHasher(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nexport const sha3_224 = /* @__PURE__ */(() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256 = /* @__PURE__ */(() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nexport const sha3_384 = /* @__PURE__ */(() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nexport const sha3_512 = /* @__PURE__ */(() => gen(0x06, 72, 512 / 8))();\n/** keccak-224 hash function. */\nexport const keccak_224 = /* @__PURE__ */(() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256 = /* @__PURE__ */(() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nexport const keccak_384 = /* @__PURE__ */(() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nexport const keccak_512 = /* @__PURE__ */(() => gen(0x01, 72, 512 / 8))();\nconst genShake = (suffix, blockLen, outputLen) => createXOFer((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128 = /* @__PURE__ */(() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256 = /* @__PURE__ */(() => genShake(0x1f, 136, 256 / 8))();", "map": {"version": 3, "names": ["rotlBH", "rotlBL", "rotlSH", "rotlSL", "split", "abytes", "aexists", "anumber", "aoutput", "clean", "createHasher", "createXOFer", "Hash", "swap32IfBE", "toBytes", "u32", "_0n", "BigInt", "_1n", "_2n", "_7n", "_256n", "_0x71n", "SHA3_PI", "SHA3_ROTL", "_SHA3_IOTA", "round", "R", "x", "y", "push", "t", "j", "IOTAS", "SHA3_IOTA_H", "SHA3_IOTA_L", "rotlH", "h", "l", "s", "rotlL", "keccakP", "rounds", "B", "Uint32Array", "idx1", "idx0", "B0", "B1", "Th", "Tl", "curH", "curL", "shift", "PI", "Keccak", "constructor", "blockLen", "suffix", "outputLen", "enableXOF", "pos", "posOut", "finished", "destroyed", "Error", "state", "Uint8Array", "state32", "clone", "_cloneInto", "keccak", "update", "data", "len", "length", "take", "Math", "min", "i", "finish", "writeInto", "out", "bufferOut", "set", "subarray", "xofInto", "xof", "bytes", "digestInto", "destroy", "digest", "to", "gen", "sha3_224", "sha3_256", "sha3_384", "sha3_512", "keccak_224", "keccak_256", "keccak_384", "keccak_512", "gen<PERSON>hake", "opts", "dkLen", "undefined", "shake128", "shake256"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\sha3.ts"], "sourcesContent": ["/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.ts';\n// prettier-ignore\nimport {\n  abytes, aexists, anumber, aoutput,\n  clean, createHasher, createXOFer, Hash,\n  swap32IfBE,\n  toBytes, u32,\n  type CHash, type CHashXO, type HashXOF, type Input\n} from './utils.ts';\n\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI: number[] = [];\nconst SHA3_ROTL: number[] = [];\nconst _SHA3_IOTA: bigint[] = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst IOTAS = split(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s: Uint32Array, rounds: number = 24): void {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  clean(B);\n}\n\n/** Keccak sponge function. */\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n\n  public blockLen: number;\n  public suffix: number;\n  public outputLen: number;\n  protected enableXOF = false;\n  protected rounds: number;\n\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    blockLen: number,\n    suffix: number,\n    outputLen: number,\n    enableXOF = false,\n    rounds: number = 24\n  ) {\n    super();\n    this.blockLen = blockLen;\n    this.suffix = suffix;\n    this.outputLen = outputLen;\n    this.enableXOF = enableXOF;\n    this.rounds = rounds;\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (!(0 < blockLen && blockLen < 200))\n      throw new Error('only keccak-f1600 function is supported');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  clone(): Keccak {\n    return this._cloneInto();\n  }\n  protected keccak(): void {\n    swap32IfBE(this.state32);\n    keccakP(this.state32, this.rounds);\n    swap32IfBE(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input): this {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    const { blockLen, state } = this;\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish(): void {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array): Uint8Array {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest(): Uint8Array {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy(): void {\n    this.destroyed = true;\n    clean(this.state);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  createHasher(() => new Keccak(blockLen, suffix, outputLen));\n\n/** SHA3-224 hash function. */\nexport const sha3_224: CHash = /* @__PURE__ */ (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256: CHash = /* @__PURE__ */ (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nexport const sha3_384: CHash = /* @__PURE__ */ (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nexport const sha3_512: CHash = /* @__PURE__ */ (() => gen(0x06, 72, 512 / 8))();\n\n/** keccak-224 hash function. */\nexport const keccak_224: CHash = /* @__PURE__ */ (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256: CHash = /* @__PURE__ */ (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nexport const keccak_384: CHash = /* @__PURE__ */ (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nexport const keccak_512: CHash = /* @__PURE__ */ (() => gen(0x01, 72, 512 / 8))();\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  createXOFer<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128: CHashXO = /* @__PURE__ */ (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256: CHashXO = /* @__PURE__ */ (() => genShake(0x1f, 136, 256 / 8))();\n"], "mappings": "AAAA;;;;;;;;;;;AAWA,SAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,WAAW;AACjE;AACA,SACEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EACjCC,KAAK,EAAEC,YAAY,EAAEC,WAAW,EAAEC,IAAI,EACtCC,UAAU,EACVC,OAAO,EAAEC,GAAG,QAEP,YAAY;AAEnB;AACA;AACA;AACA,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;AACrB,MAAME,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMG,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMI,KAAK,GAAGJ,MAAM,CAAC,GAAG,CAAC;AACzB,MAAMK,MAAM,GAAGL,MAAM,CAAC,IAAI,CAAC;AAC3B,MAAMM,OAAO,GAAa,EAAE;AAC5B,MAAMC,SAAS,GAAa,EAAE;AAC9B,MAAMC,UAAU,GAAa,EAAE;AAC/B,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEC,CAAC,GAAGT,GAAG,EAAEU,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEH,KAAK,GAAG,EAAE,EAAEA,KAAK,EAAE,EAAE;EAC9D;EACA,CAACE,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACA,CAAC,EAAE,CAAC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAGC,CAAC,IAAI,CAAC,CAAC;EACjCN,OAAO,CAACO,IAAI,CAAC,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAGD,CAAC,CAAC,CAAC;EAC7B;EACAJ,SAAS,CAACM,IAAI,CAAG,CAACJ,KAAK,GAAG,CAAC,KAAKA,KAAK,GAAG,CAAC,CAAC,GAAI,CAAC,GAAI,EAAE,CAAC;EACtD;EACA,IAAIK,CAAC,GAAGf,GAAG;EACX,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BL,CAAC,GAAG,CAAEA,CAAC,IAAIT,GAAG,GAAK,CAACS,CAAC,IAAIP,GAAG,IAAIE,MAAO,IAAID,KAAK;IAChD,IAAIM,CAAC,GAAGR,GAAG,EAAEY,CAAC,IAAIb,GAAG,IAAK,CAACA,GAAG,IAAI,eAAgBD,MAAM,CAACe,CAAC,CAAC,IAAId,GAAI;EACrE;EACAO,UAAU,CAACK,IAAI,CAACC,CAAC,CAAC;AACpB;AACA,MAAME,KAAK,GAAG7B,KAAK,CAACqB,UAAU,EAAE,IAAI,CAAC;AACrC,MAAMS,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC;AAC5B,MAAME,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;AAE5B;AACA,MAAMG,KAAK,GAAGA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS,KAAMA,CAAC,GAAG,EAAE,GAAGvC,MAAM,CAACqC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGrC,MAAM,CAACmC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAE;AAC/F,MAAMC,KAAK,GAAGA,CAACH,CAAS,EAAEC,CAAS,EAAEC,CAAS,KAAMA,CAAC,GAAG,EAAE,GAAGtC,MAAM,CAACoC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGpC,MAAM,CAACkC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAE;AAE/F;AACA,OAAM,SAAUE,OAAOA,CAACF,CAAc,EAAEG,MAAA,GAAiB,EAAE;EACzD,MAAMC,CAAC,GAAG,IAAIC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;EAChC;EACA,KAAK,IAAIlB,KAAK,GAAG,EAAE,GAAGgB,MAAM,EAAEhB,KAAK,GAAG,EAAE,EAAEA,KAAK,EAAE,EAAE;IACjD;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEe,CAAC,CAACf,CAAC,CAAC,GAAGW,CAAC,CAACX,CAAC,CAAC,GAAGW,CAAC,CAACX,CAAC,GAAG,EAAE,CAAC,GAAGW,CAAC,CAACX,CAAC,GAAG,EAAE,CAAC,GAAGW,CAAC,CAACX,CAAC,GAAG,EAAE,CAAC,GAAGW,CAAC,CAACX,CAAC,GAAG,EAAE,CAAC;IACxF,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC9B,MAAMiB,IAAI,GAAG,CAACjB,CAAC,GAAG,CAAC,IAAI,EAAE;MACzB,MAAMkB,IAAI,GAAG,CAAClB,CAAC,GAAG,CAAC,IAAI,EAAE;MACzB,MAAMmB,EAAE,GAAGJ,CAAC,CAACG,IAAI,CAAC;MAClB,MAAME,EAAE,GAAGL,CAAC,CAACG,IAAI,GAAG,CAAC,CAAC;MACtB,MAAMG,EAAE,GAAGb,KAAK,CAACW,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC,GAAGL,CAAC,CAACE,IAAI,CAAC;MACrC,MAAMK,EAAE,GAAGV,KAAK,CAACO,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC,GAAGL,CAAC,CAACE,IAAI,GAAG,CAAC,CAAC;MACzC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE;QAC/BU,CAAC,CAACX,CAAC,GAAGC,CAAC,CAAC,IAAIoB,EAAE;QACdV,CAAC,CAACX,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,IAAIqB,EAAE;MACpB;IACF;IACA;IACA,IAAIC,IAAI,GAAGZ,CAAC,CAAC,CAAC,CAAC;IACf,IAAIa,IAAI,GAAGb,CAAC,CAAC,CAAC,CAAC;IACf,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMsB,KAAK,GAAG7B,SAAS,CAACO,CAAC,CAAC;MAC1B,MAAMkB,EAAE,GAAGb,KAAK,CAACe,IAAI,EAAEC,IAAI,EAAEC,KAAK,CAAC;MACnC,MAAMH,EAAE,GAAGV,KAAK,CAACW,IAAI,EAAEC,IAAI,EAAEC,KAAK,CAAC;MACnC,MAAMC,EAAE,GAAG/B,OAAO,CAACQ,CAAC,CAAC;MACrBoB,IAAI,GAAGZ,CAAC,CAACe,EAAE,CAAC;MACZF,IAAI,GAAGb,CAAC,CAACe,EAAE,GAAG,CAAC,CAAC;MAChBf,CAAC,CAACe,EAAE,CAAC,GAAGL,EAAE;MACVV,CAAC,CAACe,EAAE,GAAG,CAAC,CAAC,GAAGJ,EAAE;IAChB;IACA;IACA,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE;MAC/B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEe,CAAC,CAACf,CAAC,CAAC,GAAGW,CAAC,CAACV,CAAC,GAAGD,CAAC,CAAC;MAC5C,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEW,CAAC,CAACV,CAAC,GAAGD,CAAC,CAAC,IAAI,CAACe,CAAC,CAAC,CAACf,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAGe,CAAC,CAAC,CAACf,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;IAC7E;IACA;IACAW,CAAC,CAAC,CAAC,CAAC,IAAIL,WAAW,CAACR,KAAK,CAAC;IAC1Ba,CAAC,CAAC,CAAC,CAAC,IAAIJ,WAAW,CAACT,KAAK,CAAC;EAC5B;EACAjB,KAAK,CAACkC,CAAC,CAAC;AACV;AAEA;AACA,OAAM,MAAOY,MAAO,SAAQ3C,IAAY;EActC;EACA4C,YACEC,QAAgB,EAChBC,MAAc,EACdC,SAAiB,EACjBC,SAAS,GAAG,KAAK,EACjBlB,MAAA,GAAiB,EAAE;IAEnB,KAAK,EAAE;IApBC,KAAAmB,GAAG,GAAG,CAAC;IACP,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,SAAS,GAAG,KAAK;IAKjB,KAAAJ,SAAS,GAAG,KAAK;IAYzB,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAClB,MAAM,GAAGA,MAAM;IACpB;IACAnC,OAAO,CAACoD,SAAS,CAAC;IAClB;IACA;IACA,IAAI,EAAE,CAAC,GAAGF,QAAQ,IAAIA,QAAQ,GAAG,GAAG,CAAC,EACnC,MAAM,IAAIQ,KAAK,CAAC,yCAAyC,CAAC;IAC5D,IAAI,CAACC,KAAK,GAAG,IAAIC,UAAU,CAAC,GAAG,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGrD,GAAG,CAAC,IAAI,CAACmD,KAAK,CAAC;EAChC;EACAG,KAAKA,CAAA;IACH,OAAO,IAAI,CAACC,UAAU,EAAE;EAC1B;EACUC,MAAMA,CAAA;IACd1D,UAAU,CAAC,IAAI,CAACuD,OAAO,CAAC;IACxB3B,OAAO,CAAC,IAAI,CAAC2B,OAAO,EAAE,IAAI,CAAC1B,MAAM,CAAC;IAClC7B,UAAU,CAAC,IAAI,CAACuD,OAAO,CAAC;IACxB,IAAI,CAACN,MAAM,GAAG,CAAC;IACf,IAAI,CAACD,GAAG,GAAG,CAAC;EACd;EACAW,MAAMA,CAACC,IAAW;IAChBnE,OAAO,CAAC,IAAI,CAAC;IACbmE,IAAI,GAAG3D,OAAO,CAAC2D,IAAI,CAAC;IACpBpE,MAAM,CAACoE,IAAI,CAAC;IACZ,MAAM;MAAEhB,QAAQ;MAAES;IAAK,CAAE,GAAG,IAAI;IAChC,MAAMQ,GAAG,GAAGD,IAAI,CAACE,MAAM;IACvB,KAAK,IAAId,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGa,GAAG,GAAI;MAC7B,MAAME,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACrB,QAAQ,GAAG,IAAI,CAACI,GAAG,EAAEa,GAAG,GAAGb,GAAG,CAAC;MACrD,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAEb,KAAK,CAAC,IAAI,CAACL,GAAG,EAAE,CAAC,IAAIY,IAAI,CAACZ,GAAG,EAAE,CAAC;MAC/D,IAAI,IAAI,CAACA,GAAG,KAAKJ,QAAQ,EAAE,IAAI,CAACc,MAAM,EAAE;IAC1C;IACA,OAAO,IAAI;EACb;EACUS,MAAMA,CAAA;IACd,IAAI,IAAI,CAACjB,QAAQ,EAAE;IACnB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,MAAM;MAAEG,KAAK;MAAER,MAAM;MAAEG,GAAG;MAAEJ;IAAQ,CAAE,GAAG,IAAI;IAC7C;IACAS,KAAK,CAACL,GAAG,CAAC,IAAIH,MAAM;IACpB,IAAI,CAACA,MAAM,GAAG,IAAI,MAAM,CAAC,IAAIG,GAAG,KAAKJ,QAAQ,GAAG,CAAC,EAAE,IAAI,CAACc,MAAM,EAAE;IAChEL,KAAK,CAACT,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI;IAC3B,IAAI,CAACc,MAAM,EAAE;EACf;EACUU,SAASA,CAACC,GAAe;IACjC5E,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;IACpBD,MAAM,CAAC6E,GAAG,CAAC;IACX,IAAI,CAACF,MAAM,EAAE;IACb,MAAMG,SAAS,GAAG,IAAI,CAACjB,KAAK;IAC5B,MAAM;MAAET;IAAQ,CAAE,GAAG,IAAI;IACzB,KAAK,IAAII,GAAG,GAAG,CAAC,EAAEa,GAAG,GAAGQ,GAAG,CAACP,MAAM,EAAEd,GAAG,GAAGa,GAAG,GAAI;MAC/C,IAAI,IAAI,CAACZ,MAAM,IAAIL,QAAQ,EAAE,IAAI,CAACc,MAAM,EAAE;MAC1C,MAAMK,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACrB,QAAQ,GAAG,IAAI,CAACK,MAAM,EAAEY,GAAG,GAAGb,GAAG,CAAC;MACxDqB,GAAG,CAACE,GAAG,CAACD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACvB,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGc,IAAI,CAAC,EAAEf,GAAG,CAAC;MACjE,IAAI,CAACC,MAAM,IAAIc,IAAI;MACnBf,GAAG,IAAIe,IAAI;IACb;IACA,OAAOM,GAAG;EACZ;EACAI,OAAOA,CAACJ,GAAe;IACrB;IACA,IAAI,CAAC,IAAI,CAACtB,SAAS,EAAE,MAAM,IAAIK,KAAK,CAAC,uCAAuC,CAAC;IAC7E,OAAO,IAAI,CAACgB,SAAS,CAACC,GAAG,CAAC;EAC5B;EACAK,GAAGA,CAACC,KAAa;IACfjF,OAAO,CAACiF,KAAK,CAAC;IACd,OAAO,IAAI,CAACF,OAAO,CAAC,IAAInB,UAAU,CAACqB,KAAK,CAAC,CAAC;EAC5C;EACAC,UAAUA,CAACP,GAAe;IACxB1E,OAAO,CAAC0E,GAAG,EAAE,IAAI,CAAC;IAClB,IAAI,IAAI,CAACnB,QAAQ,EAAE,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;IACjE,IAAI,CAACgB,SAAS,CAACC,GAAG,CAAC;IACnB,IAAI,CAACQ,OAAO,EAAE;IACd,OAAOR,GAAG;EACZ;EACAS,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACF,UAAU,CAAC,IAAItB,UAAU,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC;EACxD;EACA+B,OAAOA,CAAA;IACL,IAAI,CAAC1B,SAAS,GAAG,IAAI;IACrBvD,KAAK,CAAC,IAAI,CAACyD,KAAK,CAAC;EACnB;EACAI,UAAUA,CAACsB,EAAW;IACpB,MAAM;MAAEnC,QAAQ;MAAEC,MAAM;MAAEC,SAAS;MAAEjB,MAAM;MAAEkB;IAAS,CAAE,GAAG,IAAI;IAC/DgC,EAAE,KAAFA,EAAE,GAAK,IAAIrC,MAAM,CAACE,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAElB,MAAM,CAAC;IACjEkD,EAAE,CAACxB,OAAO,CAACgB,GAAG,CAAC,IAAI,CAAChB,OAAO,CAAC;IAC5BwB,EAAE,CAAC/B,GAAG,GAAG,IAAI,CAACA,GAAG;IACjB+B,EAAE,CAAC9B,MAAM,GAAG,IAAI,CAACA,MAAM;IACvB8B,EAAE,CAAC7B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC3B6B,EAAE,CAAClD,MAAM,GAAGA,MAAM;IAClB;IACAkD,EAAE,CAAClC,MAAM,GAAGA,MAAM;IAClBkC,EAAE,CAACjC,SAAS,GAAGA,SAAS;IACxBiC,EAAE,CAAChC,SAAS,GAAGA,SAAS;IACxBgC,EAAE,CAAC5B,SAAS,GAAG,IAAI,CAACA,SAAS;IAC7B,OAAO4B,EAAE;EACX;;AAGF,MAAMC,GAAG,GAAGA,CAACnC,MAAc,EAAED,QAAgB,EAAEE,SAAiB,KAC9DjD,YAAY,CAAC,MAAM,IAAI6C,MAAM,CAACE,QAAQ,EAAEC,MAAM,EAAEC,SAAS,CAAC,CAAC;AAE7D;AACA,OAAO,MAAMmC,QAAQ,GAAU,eAAgB,CAAC,MAAMD,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAChF;AACA,OAAO,MAAME,QAAQ,GAAU,eAAgB,CAAC,MAAMF,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAChF;AACA,OAAO,MAAMG,QAAQ,GAAU,eAAgB,CAAC,MAAMH,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAChF;AACA,OAAO,MAAMI,QAAQ,GAAU,eAAgB,CAAC,MAAMJ,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAE/E;AACA,OAAO,MAAMK,UAAU,GAAU,eAAgB,CAAC,MAAML,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAClF;AACA,OAAO,MAAMM,UAAU,GAAU,eAAgB,CAAC,MAAMN,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAClF;AACA,OAAO,MAAMO,UAAU,GAAU,eAAgB,CAAC,MAAMP,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAClF;AACA,OAAO,MAAMQ,UAAU,GAAU,eAAgB,CAAC,MAAMR,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AAIjF,MAAMS,QAAQ,GAAGA,CAAC5C,MAAc,EAAED,QAAgB,EAAEE,SAAiB,KACnEhD,WAAW,CACT,CAAC4F,IAAA,GAAkB,EAAE,KACnB,IAAIhD,MAAM,CAACE,QAAQ,EAAEC,MAAM,EAAE6C,IAAI,CAACC,KAAK,KAAKC,SAAS,GAAG9C,SAAS,GAAG4C,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC,CACxF;AAEH;AACA,OAAO,MAAME,QAAQ,GAAY,eAAgB,CAAC,MAAMJ,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE;AACvF;AACA,OAAO,MAAMK,QAAQ,GAAY,eAAgB,CAAC,MAAML,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}