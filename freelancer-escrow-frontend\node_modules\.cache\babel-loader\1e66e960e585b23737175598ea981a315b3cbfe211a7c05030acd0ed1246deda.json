{"ast": null, "code": "/**\n * Audited & minimal JS implementation of\n * [BIP39 mnemonic phrases](https://github.com/bitcoin/bips/blob/master/bip-0039.mediawiki).\n * @module\n * @example\n```js\nimport * as bip39 from '@scure/bip39';\nimport { wordlist } from '@scure/bip39/wordlists/english';\nconst mn = bip39.generateMnemonic(wordlist);\nconsole.log(mn);\nconst ent = bip39.mnemonicToEntropy(mn, wordlist)\nbip39.entropyToMnemonic(ent, wordlist);\nbip39.validateMnemonic(mn, wordlist);\nawait bip39.mnemonicToSeed(mn, 'password');\nbip39.mnemonicToSeedSync(mn, 'password');\n\n// Wordlists\nimport { wordlist as czech } from '@scure/bip39/wordlists/czech';\nimport { wordlist as english } from '@scure/bip39/wordlists/english';\nimport { wordlist as french } from '@scure/bip39/wordlists/french';\nimport { wordlist as italian } from '@scure/bip39/wordlists/italian';\nimport { wordlist as japanese } from '@scure/bip39/wordlists/japanese';\nimport { wordlist as korean } from '@scure/bip39/wordlists/korean';\nimport { wordlist as portuguese } from '@scure/bip39/wordlists/portuguese';\nimport { wordlist as simplifiedChinese } from '@scure/bip39/wordlists/simplified-chinese';\nimport { wordlist as spanish } from '@scure/bip39/wordlists/spanish';\nimport { wordlist as traditionalChinese } from '@scure/bip39/wordlists/traditional-chinese';\n```\n */\n/*! scure-bip39 - MIT License (c) 2022 Patricio Palladino, Paul Miller (paulmillr.com) */\nimport { pbkdf2, pbkdf2Async } from '@noble/hashes/pbkdf2';\nimport { sha256, sha512 } from '@noble/hashes/sha2';\nimport { abytes, anumber, randomBytes } from '@noble/hashes/utils';\nimport { utils as baseUtils } from '@scure/base';\n// Japanese wordlist\nconst isJapanese = wordlist => wordlist[0] === '\\u3042\\u3044\\u3053\\u304f\\u3057\\u3093';\n// Normalization replaces equivalent sequences of characters\n// so that any two texts that are equivalent will be reduced\n// to the same sequence of code points, called the normal form of the original text.\n// https://tonsky.me/blog/unicode/#why-is-a----\nfunction nfkd(str) {\n  if (typeof str !== 'string') throw new TypeError('invalid mnemonic type: ' + typeof str);\n  return str.normalize('NFKD');\n}\nfunction normalize(str) {\n  const norm = nfkd(str);\n  const words = norm.split(' ');\n  if (![12, 15, 18, 21, 24].includes(words.length)) throw new Error('Invalid mnemonic');\n  return {\n    nfkd: norm,\n    words\n  };\n}\nfunction aentropy(ent) {\n  abytes(ent, 16, 20, 24, 28, 32);\n}\n/**\n * Generate x random words. Uses Cryptographically-Secure Random Number Generator.\n * @param wordlist imported wordlist for specific language\n * @param strength mnemonic strength 128-256 bits\n * @example\n * generateMnemonic(wordlist, 128)\n * // 'legal winner thank year wave sausage worth useful legal winner thank yellow'\n */\nexport function generateMnemonic(wordlist, strength = 128) {\n  anumber(strength);\n  if (strength % 32 !== 0 || strength > 256) throw new TypeError('Invalid entropy');\n  return entropyToMnemonic(randomBytes(strength / 8), wordlist);\n}\nconst calcChecksum = entropy => {\n  // Checksum is ent.length/4 bits long\n  const bitsLeft = 8 - entropy.length / 4;\n  // Zero rightmost \"bitsLeft\" bits in byte\n  // For example: bitsLeft=4 val=10111101 -> 10110000\n  return new Uint8Array([sha256(entropy)[0] >> bitsLeft << bitsLeft]);\n};\nfunction getCoder(wordlist) {\n  if (!Array.isArray(wordlist) || wordlist.length !== 2048 || typeof wordlist[0] !== 'string') throw new Error('Wordlist: expected array of 2048 strings');\n  wordlist.forEach(i => {\n    if (typeof i !== 'string') throw new Error('wordlist: non-string element: ' + i);\n  });\n  return baseUtils.chain(baseUtils.checksum(1, calcChecksum), baseUtils.radix2(11, true), baseUtils.alphabet(wordlist));\n}\n/**\n * Reversible: Converts mnemonic string to raw entropy in form of byte array.\n * @param mnemonic 12-24 words\n * @param wordlist imported wordlist for specific language\n * @example\n * const mnem = 'legal winner thank year wave sausage worth useful legal winner thank yellow';\n * mnemonicToEntropy(mnem, wordlist)\n * // Produces\n * new Uint8Array([\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f\n * ])\n */\nexport function mnemonicToEntropy(mnemonic, wordlist) {\n  const {\n    words\n  } = normalize(mnemonic);\n  const entropy = getCoder(wordlist).decode(words);\n  aentropy(entropy);\n  return entropy;\n}\n/**\n * Reversible: Converts raw entropy in form of byte array to mnemonic string.\n * @param entropy byte array\n * @param wordlist imported wordlist for specific language\n * @returns 12-24 words\n * @example\n * const ent = new Uint8Array([\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f\n * ]);\n * entropyToMnemonic(ent, wordlist);\n * // 'legal winner thank year wave sausage worth useful legal winner thank yellow'\n */\nexport function entropyToMnemonic(entropy, wordlist) {\n  aentropy(entropy);\n  const words = getCoder(wordlist).encode(entropy);\n  return words.join(isJapanese(wordlist) ? '\\u3000' : ' ');\n}\n/**\n * Validates mnemonic for being 12-24 words contained in `wordlist`.\n */\nexport function validateMnemonic(mnemonic, wordlist) {\n  try {\n    mnemonicToEntropy(mnemonic, wordlist);\n  } catch (e) {\n    return false;\n  }\n  return true;\n}\nconst psalt = passphrase => nfkd('mnemonic' + passphrase);\n/**\n * Irreversible: Uses KDF to derive 64 bytes of key data from mnemonic + optional password.\n * @param mnemonic 12-24 words\n * @param passphrase string that will additionally protect the key\n * @returns 64 bytes of key data\n * @example\n * const mnem = 'legal winner thank year wave sausage worth useful legal winner thank yellow';\n * await mnemonicToSeed(mnem, 'password');\n * // new Uint8Array([...64 bytes])\n */\nexport function mnemonicToSeed(mnemonic, passphrase = '') {\n  return pbkdf2Async(sha512, normalize(mnemonic).nfkd, psalt(passphrase), {\n    c: 2048,\n    dkLen: 64\n  });\n}\n/**\n * Irreversible: Uses KDF to derive 64 bytes of key data from mnemonic + optional password.\n * @param mnemonic 12-24 words\n * @param passphrase string that will additionally protect the key\n * @returns 64 bytes of key data\n * @example\n * const mnem = 'legal winner thank year wave sausage worth useful legal winner thank yellow';\n * mnemonicToSeedSync(mnem, 'password');\n * // new Uint8Array([...64 bytes])\n */\nexport function mnemonicToSeedSync(mnemonic, passphrase = '') {\n  return pbkdf2(sha512, normalize(mnemonic).nfkd, psalt(passphrase), {\n    c: 2048,\n    dkLen: 64\n  });\n}", "map": {"version": 3, "names": ["pbkdf2", "pbkdf2Async", "sha256", "sha512", "abytes", "anumber", "randomBytes", "utils", "baseUtils", "isJapanese", "wordlist", "nfkd", "str", "TypeError", "normalize", "norm", "words", "split", "includes", "length", "Error", "aentropy", "ent", "generateMnemonic", "strength", "entropyToMnemonic", "calcChecksum", "entropy", "bitsLeft", "Uint8Array", "getCoder", "Array", "isArray", "for<PERSON>ach", "i", "chain", "checksum", "radix2", "alphabet", "mnemonicToEntropy", "mnemonic", "decode", "encode", "join", "validateMnemonic", "e", "psalt", "passphrase", "mnemonicToSeed", "c", "dkLen", "mnemonicToSeedSync"], "sources": ["C:/Users/<USER>/Desktop/Freelancer-Platform-on-Aptos--DAPPS-main/freelancer-escrow-frontend/node_modules/@scure/bip39/esm/index.js"], "sourcesContent": ["/**\n * Audited & minimal JS implementation of\n * [BIP39 mnemonic phrases](https://github.com/bitcoin/bips/blob/master/bip-0039.mediawiki).\n * @module\n * @example\n```js\nimport * as bip39 from '@scure/bip39';\nimport { wordlist } from '@scure/bip39/wordlists/english';\nconst mn = bip39.generateMnemonic(wordlist);\nconsole.log(mn);\nconst ent = bip39.mnemonicToEntropy(mn, wordlist)\nbip39.entropyToMnemonic(ent, wordlist);\nbip39.validateMnemonic(mn, wordlist);\nawait bip39.mnemonicToSeed(mn, 'password');\nbip39.mnemonicToSeedSync(mn, 'password');\n\n// Wordlists\nimport { wordlist as czech } from '@scure/bip39/wordlists/czech';\nimport { wordlist as english } from '@scure/bip39/wordlists/english';\nimport { wordlist as french } from '@scure/bip39/wordlists/french';\nimport { wordlist as italian } from '@scure/bip39/wordlists/italian';\nimport { wordlist as japanese } from '@scure/bip39/wordlists/japanese';\nimport { wordlist as korean } from '@scure/bip39/wordlists/korean';\nimport { wordlist as portuguese } from '@scure/bip39/wordlists/portuguese';\nimport { wordlist as simplifiedChinese } from '@scure/bip39/wordlists/simplified-chinese';\nimport { wordlist as spanish } from '@scure/bip39/wordlists/spanish';\nimport { wordlist as traditionalChinese } from '@scure/bip39/wordlists/traditional-chinese';\n```\n */\n/*! scure-bip39 - MIT License (c) 2022 Patricio Palladino, Paul Miller (paulmillr.com) */\nimport { pbkdf2, pbkdf2Async } from '@noble/hashes/pbkdf2';\nimport { sha256, sha512 } from '@noble/hashes/sha2';\nimport { abytes, anumber, randomBytes } from '@noble/hashes/utils';\nimport { utils as baseUtils } from '@scure/base';\n// Japanese wordlist\nconst isJapanese = (wordlist) => wordlist[0] === '\\u3042\\u3044\\u3053\\u304f\\u3057\\u3093';\n// Normalization replaces equivalent sequences of characters\n// so that any two texts that are equivalent will be reduced\n// to the same sequence of code points, called the normal form of the original text.\n// https://tonsky.me/blog/unicode/#why-is-a----\nfunction nfkd(str) {\n    if (typeof str !== 'string')\n        throw new TypeError('invalid mnemonic type: ' + typeof str);\n    return str.normalize('NFKD');\n}\nfunction normalize(str) {\n    const norm = nfkd(str);\n    const words = norm.split(' ');\n    if (![12, 15, 18, 21, 24].includes(words.length))\n        throw new Error('Invalid mnemonic');\n    return { nfkd: norm, words };\n}\nfunction aentropy(ent) {\n    abytes(ent, 16, 20, 24, 28, 32);\n}\n/**\n * Generate x random words. Uses Cryptographically-Secure Random Number Generator.\n * @param wordlist imported wordlist for specific language\n * @param strength mnemonic strength 128-256 bits\n * @example\n * generateMnemonic(wordlist, 128)\n * // 'legal winner thank year wave sausage worth useful legal winner thank yellow'\n */\nexport function generateMnemonic(wordlist, strength = 128) {\n    anumber(strength);\n    if (strength % 32 !== 0 || strength > 256)\n        throw new TypeError('Invalid entropy');\n    return entropyToMnemonic(randomBytes(strength / 8), wordlist);\n}\nconst calcChecksum = (entropy) => {\n    // Checksum is ent.length/4 bits long\n    const bitsLeft = 8 - entropy.length / 4;\n    // Zero rightmost \"bitsLeft\" bits in byte\n    // For example: bitsLeft=4 val=10111101 -> 10110000\n    return new Uint8Array([(sha256(entropy)[0] >> bitsLeft) << bitsLeft]);\n};\nfunction getCoder(wordlist) {\n    if (!Array.isArray(wordlist) || wordlist.length !== 2048 || typeof wordlist[0] !== 'string')\n        throw new Error('Wordlist: expected array of 2048 strings');\n    wordlist.forEach((i) => {\n        if (typeof i !== 'string')\n            throw new Error('wordlist: non-string element: ' + i);\n    });\n    return baseUtils.chain(baseUtils.checksum(1, calcChecksum), baseUtils.radix2(11, true), baseUtils.alphabet(wordlist));\n}\n/**\n * Reversible: Converts mnemonic string to raw entropy in form of byte array.\n * @param mnemonic 12-24 words\n * @param wordlist imported wordlist for specific language\n * @example\n * const mnem = 'legal winner thank year wave sausage worth useful legal winner thank yellow';\n * mnemonicToEntropy(mnem, wordlist)\n * // Produces\n * new Uint8Array([\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f\n * ])\n */\nexport function mnemonicToEntropy(mnemonic, wordlist) {\n    const { words } = normalize(mnemonic);\n    const entropy = getCoder(wordlist).decode(words);\n    aentropy(entropy);\n    return entropy;\n}\n/**\n * Reversible: Converts raw entropy in form of byte array to mnemonic string.\n * @param entropy byte array\n * @param wordlist imported wordlist for specific language\n * @returns 12-24 words\n * @example\n * const ent = new Uint8Array([\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,\n *   0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f\n * ]);\n * entropyToMnemonic(ent, wordlist);\n * // 'legal winner thank year wave sausage worth useful legal winner thank yellow'\n */\nexport function entropyToMnemonic(entropy, wordlist) {\n    aentropy(entropy);\n    const words = getCoder(wordlist).encode(entropy);\n    return words.join(isJapanese(wordlist) ? '\\u3000' : ' ');\n}\n/**\n * Validates mnemonic for being 12-24 words contained in `wordlist`.\n */\nexport function validateMnemonic(mnemonic, wordlist) {\n    try {\n        mnemonicToEntropy(mnemonic, wordlist);\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}\nconst psalt = (passphrase) => nfkd('mnemonic' + passphrase);\n/**\n * Irreversible: Uses KDF to derive 64 bytes of key data from mnemonic + optional password.\n * @param mnemonic 12-24 words\n * @param passphrase string that will additionally protect the key\n * @returns 64 bytes of key data\n * @example\n * const mnem = 'legal winner thank year wave sausage worth useful legal winner thank yellow';\n * await mnemonicToSeed(mnem, 'password');\n * // new Uint8Array([...64 bytes])\n */\nexport function mnemonicToSeed(mnemonic, passphrase = '') {\n    return pbkdf2Async(sha512, normalize(mnemonic).nfkd, psalt(passphrase), { c: 2048, dkLen: 64 });\n}\n/**\n * Irreversible: Uses KDF to derive 64 bytes of key data from mnemonic + optional password.\n * @param mnemonic 12-24 words\n * @param passphrase string that will additionally protect the key\n * @returns 64 bytes of key data\n * @example\n * const mnem = 'legal winner thank year wave sausage worth useful legal winner thank yellow';\n * mnemonicToSeedSync(mnem, 'password');\n * // new Uint8Array([...64 bytes])\n */\nexport function mnemonicToSeedSync(mnemonic, passphrase = '') {\n    return pbkdf2(sha512, normalize(mnemonic).nfkd, psalt(passphrase), { c: 2048, dkLen: 64 });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,WAAW,QAAQ,sBAAsB;AAC1D,SAASC,MAAM,EAAEC,MAAM,QAAQ,oBAAoB;AACnD,SAASC,MAAM,EAAEC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAClE,SAASC,KAAK,IAAIC,SAAS,QAAQ,aAAa;AAChD;AACA,MAAMC,UAAU,GAAIC,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC,KAAK,sCAAsC;AACvF;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,GAAG,EAAE;EACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,EACvB,MAAM,IAAIC,SAAS,CAAC,yBAAyB,GAAG,OAAOD,GAAG,CAAC;EAC/D,OAAOA,GAAG,CAACE,SAAS,CAAC,MAAM,CAAC;AAChC;AACA,SAASA,SAASA,CAACF,GAAG,EAAE;EACpB,MAAMG,IAAI,GAAGJ,IAAI,CAACC,GAAG,CAAC;EACtB,MAAMI,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAC7B,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAC5C,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;EACvC,OAAO;IAAET,IAAI,EAAEI,IAAI;IAAEC;EAAM,CAAC;AAChC;AACA,SAASK,QAAQA,CAACC,GAAG,EAAE;EACnBlB,MAAM,CAACkB,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACb,QAAQ,EAAEc,QAAQ,GAAG,GAAG,EAAE;EACvDnB,OAAO,CAACmB,QAAQ,CAAC;EACjB,IAAIA,QAAQ,GAAG,EAAE,KAAK,CAAC,IAAIA,QAAQ,GAAG,GAAG,EACrC,MAAM,IAAIX,SAAS,CAAC,iBAAiB,CAAC;EAC1C,OAAOY,iBAAiB,CAACnB,WAAW,CAACkB,QAAQ,GAAG,CAAC,CAAC,EAAEd,QAAQ,CAAC;AACjE;AACA,MAAMgB,YAAY,GAAIC,OAAO,IAAK;EAC9B;EACA,MAAMC,QAAQ,GAAG,CAAC,GAAGD,OAAO,CAACR,MAAM,GAAG,CAAC;EACvC;EACA;EACA,OAAO,IAAIU,UAAU,CAAC,CAAE3B,MAAM,CAACyB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIC,QAAQ,IAAKA,QAAQ,CAAC,CAAC;AACzE,CAAC;AACD,SAASE,QAAQA,CAACpB,QAAQ,EAAE;EACxB,IAAI,CAACqB,KAAK,CAACC,OAAO,CAACtB,QAAQ,CAAC,IAAIA,QAAQ,CAACS,MAAM,KAAK,IAAI,IAAI,OAAOT,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,EACvF,MAAM,IAAIU,KAAK,CAAC,0CAA0C,CAAC;EAC/DV,QAAQ,CAACuB,OAAO,CAAEC,CAAC,IAAK;IACpB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EACrB,MAAM,IAAId,KAAK,CAAC,gCAAgC,GAAGc,CAAC,CAAC;EAC7D,CAAC,CAAC;EACF,OAAO1B,SAAS,CAAC2B,KAAK,CAAC3B,SAAS,CAAC4B,QAAQ,CAAC,CAAC,EAAEV,YAAY,CAAC,EAAElB,SAAS,CAAC6B,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE7B,SAAS,CAAC8B,QAAQ,CAAC5B,QAAQ,CAAC,CAAC;AACzH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,iBAAiBA,CAACC,QAAQ,EAAE9B,QAAQ,EAAE;EAClD,MAAM;IAAEM;EAAM,CAAC,GAAGF,SAAS,CAAC0B,QAAQ,CAAC;EACrC,MAAMb,OAAO,GAAGG,QAAQ,CAACpB,QAAQ,CAAC,CAAC+B,MAAM,CAACzB,KAAK,CAAC;EAChDK,QAAQ,CAACM,OAAO,CAAC;EACjB,OAAOA,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASF,iBAAiBA,CAACE,OAAO,EAAEjB,QAAQ,EAAE;EACjDW,QAAQ,CAACM,OAAO,CAAC;EACjB,MAAMX,KAAK,GAAGc,QAAQ,CAACpB,QAAQ,CAAC,CAACgC,MAAM,CAACf,OAAO,CAAC;EAChD,OAAOX,KAAK,CAAC2B,IAAI,CAAClC,UAAU,CAACC,QAAQ,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;AAC5D;AACA;AACA;AACA;AACA,OAAO,SAASkC,gBAAgBA,CAACJ,QAAQ,EAAE9B,QAAQ,EAAE;EACjD,IAAI;IACA6B,iBAAiB,CAACC,QAAQ,EAAE9B,QAAQ,CAAC;EACzC,CAAC,CACD,OAAOmC,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,MAAMC,KAAK,GAAIC,UAAU,IAAKpC,IAAI,CAAC,UAAU,GAAGoC,UAAU,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACR,QAAQ,EAAEO,UAAU,GAAG,EAAE,EAAE;EACtD,OAAO9C,WAAW,CAACE,MAAM,EAAEW,SAAS,CAAC0B,QAAQ,CAAC,CAAC7B,IAAI,EAAEmC,KAAK,CAACC,UAAU,CAAC,EAAE;IAAEE,CAAC,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,CAAC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACX,QAAQ,EAAEO,UAAU,GAAG,EAAE,EAAE;EAC1D,OAAO/C,MAAM,CAACG,MAAM,EAAEW,SAAS,CAAC0B,QAAQ,CAAC,CAAC7B,IAAI,EAAEmC,KAAK,CAACC,UAAU,CAAC,EAAE;IAAEE,CAAC,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}