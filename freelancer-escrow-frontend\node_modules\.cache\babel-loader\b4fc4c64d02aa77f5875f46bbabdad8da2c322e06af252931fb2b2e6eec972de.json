{"ast": null, "code": "var o = \"aptos:connect\";\nexport { o as a };", "map": {"version": 3, "names": ["o", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosConnect.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AccountInfo } from '../AccountInfo'\nimport { NetworkInfo, UserResponse } from '../misc'\n\n/** Version of the feature. */\nexport type AptosConnectVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosConnectNamespace = 'aptos:connect'\n/** TODO: docs */\nexport type AptosConnectFeature = {\n  /** Namespace for the feature. */\n  [AptosConnectNamespace]: {\n    /** Version of the feature API. */\n    version: AptosConnectVersion\n    connect: AptosConnectMethod\n  }\n}\n/** TODO: docs */\nexport type AptosConnectMethod = (\n  ...args: AptosConnectInput\n) => Promise<UserResponse<AptosConnectOutput>>\n\n/** TODO: docs */\nexport type AptosConnectInput = [silent?: boolean, networkInfo?: NetworkInfo]\n/** TODO: docs */\nexport type AptosConnectOutput = AccountInfo\n"], "mappings": "AASO,IAAMA,CAAA,GAAwB;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}