import React from 'react';

const BlockchainSection = () => {
  const blockchainStats = [
    { label: 'Network', value: 'Aptos Devnet', icon: '🌐' },
    { label: 'TPS', value: '160,000+', icon: '⚡' },
    { label: 'Finality', value: '<1 second', icon: '⏱️' },
    { label: 'Gas Cost', value: '$0.0001', icon: '💸' },
    { label: 'Uptime', value: '99.9%', icon: '🔄' },
    { label: 'Validators', value: '100+', icon: '🛡️' }
  ];

  const contractInfo = {
    address: '0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22',
    functions: [
      'initialize',
      'create_escrow',
      'fund_escrow',
      'start_work',
      'submit_work',
      'approve_work',
      'cancel_escrow',
      'resolve_dispute'
    ]
  };

  const recentTransactions = [
    {
      id: 1,
      hash: '0xcf5ec5fc356a0d5c079d2cd696e6efb01971dbf828791b41c99a45913c14a659',
      type: 'Create Escrow',
      amount: '5.0 APT',
      timestamp: '2 minutes ago',
      status: 'Success'
    },
    {
      id: 2,
      hash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
      type: 'Fund Escrow',
      amount: '2.5 APT',
      timestamp: '5 minutes ago',
      status: 'Success'
    },
    {
      id: 3,
      hash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
      type: 'Approve Work',
      amount: '1.8 APT',
      timestamp: '10 minutes ago',
      status: 'Success'
    }
  ];

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          ⛓️ Aptos Blockchain Infrastructure
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Built on Aptos, the most advanced blockchain for decentralized applications. 
          Experience lightning-fast transactions, rock-solid security, and complete transparency.
        </p>
      </div>

      {/* Blockchain Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-16">
        {blockchainStats.map((stat, index) => (
          <div key={index} className="card p-6 text-center">
            <div className="text-3xl mb-3">{stat.icon}</div>
            <h4 className="font-semibold text-gray-900 mb-1">{stat.label}</h4>
            <p className="text-lg font-bold text-blue-600">{stat.value}</p>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Smart Contract Details */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">📋 Smart Contract</h3>
          <div className="card p-6">
            <div className="mb-6">
              <h4 className="font-semibold text-gray-900 mb-3">Contract Address</h4>
              <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm break-all">
                {contractInfo.address}
              </div>
              <div className="flex space-x-2 mt-3">
                <button className="btn-primary text-sm py-2 px-4">
                  Copy Address
                </button>
                <button className="btn-secondary text-sm py-2 px-4">
                  View on Explorer
                </button>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Available Functions</h4>
              <div className="grid grid-cols-2 gap-2">
                {contractInfo.functions.map((func, index) => (
                  <div key={index} className="bg-blue-50 text-blue-800 px-3 py-2 rounded-lg text-sm font-medium">
                    {func}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6">📊 Recent Transactions</h3>
          <div className="space-y-4">
            {recentTransactions.map((tx) => (
              <div key={tx.id} className="card p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900">{tx.type}</h4>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    {tx.status}
                  </span>
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  <p>Amount: {tx.amount}</p>
                  <p>Time: {tx.timestamp}</p>
                </div>
                <div className="bg-gray-100 p-2 rounded font-mono text-xs break-all">
                  {tx.hash}
                </div>
                <button className="btn-secondary text-xs py-1 px-3 mt-2">
                  View Details
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Blockchain Benefits */}
      <div className="mt-16 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Why Aptos Blockchain?
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-4xl mb-3">⚡</div>
            <h4 className="font-semibold text-gray-900 mb-2">Lightning Fast</h4>
            <p className="text-gray-600 text-sm">
              160,000+ TPS with sub-second finality
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">🔒</div>
            <h4 className="font-semibold text-gray-900 mb-2">Ultra Secure</h4>
            <p className="text-gray-600 text-sm">
              Move language prevents common smart contract vulnerabilities
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">💰</div>
            <h4 className="font-semibold text-gray-900 mb-2">Low Cost</h4>
            <p className="text-gray-600 text-sm">
              Minimal gas fees, maximum value for users
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-3">🌍</div>
            <h4 className="font-semibold text-gray-900 mb-2">Global Scale</h4>
            <p className="text-gray-600 text-sm">
              Built to serve billions of users worldwide
            </p>
          </div>
        </div>
      </div>

      {/* Network Status */}
      <div className="mt-12 bg-white rounded-2xl shadow-lg p-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          🔄 Network Status
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 bg-green-500 rounded-full animate-pulse"></div>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Network Health</h4>
            <p className="text-green-600 font-medium">Excellent</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📈</span>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Transaction Volume</h4>
            <p className="text-blue-600 font-medium">2.5M+ Daily</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🏆</span>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">Reliability</h4>
            <p className="text-purple-600 font-medium">99.9% Uptime</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlockchainSection;
