{"ast": null, "code": "import { b as s } from \"./chunk-BF46IXHH.mjs\";\nimport { a as o } from \"./chunk-A63SMUOU.mjs\";\nimport { b as n } from \"./chunk-BCUSI3N6.mjs\";\nimport { sha3_256 as d } from \"@noble/hashes/sha3\";\nvar r = class r extends o {\n  constructor(t) {\n    super();\n    let {\n        data: e\n      } = t,\n      i = n.fromHexInput(e);\n    if (i.toUint8Array().length !== r.LENGTH) throw new Error(`Authentication Key length should be ${r.LENGTH}`);\n    this.data = i;\n  }\n  serialize(t) {\n    t.serializeFixedBytes(this.data.toUint8Array());\n  }\n  static deserialize(t) {\n    let e = t.deserializeFixedBytes(r.LENGTH);\n    return new r({\n      data: e\n    });\n  }\n  toString() {\n    return this.data.toString();\n  }\n  toUint8Array() {\n    return this.data.toUint8Array();\n  }\n  static fromSchemeAndBytes(t) {\n    let {\n        scheme: e,\n        input: i\n      } = t,\n      u = n.fromHexInput(i).toUint8Array(),\n      h = new Uint8Array([...u, e]),\n      a = d.create();\n    a.update(h);\n    let y = a.digest();\n    return new r({\n      data: y\n    });\n  }\n  static fromPublicKeyAndScheme(t) {\n    let {\n      publicKey: e\n    } = t;\n    return e.authKey();\n  }\n  static fromPublicKey(t) {\n    let {\n      publicKey: e\n    } = t;\n    return e.authKey();\n  }\n  derivedAddress() {\n    return new s(this.data.toUint8Array());\n  }\n};\nr.LENGTH = 32;\nvar c = r;\nexport { c as a };", "map": {"version": 3, "names": ["sha3_256", "d", "r", "o", "constructor", "t", "data", "e", "i", "n", "fromHexInput", "toUint8Array", "length", "LENGTH", "Error", "serialize", "serializeFixedBytes", "deserialize", "deserializeFixedBytes", "toString", "fromSchemeAndBytes", "scheme", "input", "u", "h", "Uint8Array", "a", "create", "update", "y", "digest", "fromPublicKeyAndScheme", "public<PERSON>ey", "auth<PERSON><PERSON>", "fromPublicKey", "derivedAddress", "s", "c"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\authenticationKey.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { sha3_256 as sha3Hash } from \"@noble/hashes/sha3\";\nimport { AccountAddress } from \"./accountAddress\";\nimport type { AccountPub<PERSON><PERSON>ey } from \"./crypto\";\nimport { Hex } from \"./hex\";\nimport { AuthenticationKeyScheme, HexInput } from \"../types\";\nimport { Serializable, Serializer } from \"../bcs/serializer\";\nimport { Deserializer } from \"../bcs/deserializer\";\n\n/**\n * Each account stores an authentication key. Authentication key enables account owners to rotate\n * their private key(s) associated with the account without changing the address that hosts their account.\n * @see {@link https://aptos.dev/concepts/accounts | Account Basics}\n *\n * Account addresses can be derived from AuthenticationKey\n */\nexport class AuthenticationKey extends Serializable {\n  /**\n   * An authentication key is always a SHA3-256 hash of data, and is always 32 bytes.\n   *\n   * The data to hash depends on the underlying public key type and the derivation scheme.\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The raw bytes of the authentication key.\n   */\n  public readonly data: Hex;\n\n  constructor(args: { data: HexInput }) {\n    super();\n    const { data } = args;\n    const hex = Hex.fromHexInput(data);\n    if (hex.toUint8Array().length !== AuthenticationKey.LENGTH) {\n      throw new Error(`Authentication Key length should be ${AuthenticationKey.LENGTH}`);\n    }\n    this.data = hex;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.data.toUint8Array());\n  }\n\n  /**\n   * Deserialize an AuthenticationKey from the byte buffer in a Deserializer instance.\n   * @param deserializer The deserializer to deserialize the AuthenticationKey from.\n   * @returns An instance of AuthenticationKey.\n   */\n  static deserialize(deserializer: Deserializer): AuthenticationKey {\n    const bytes = deserializer.deserializeFixedBytes(AuthenticationKey.LENGTH);\n    return new AuthenticationKey({ data: bytes });\n  }\n\n  toString(): string {\n    return this.data.toString();\n  }\n\n  toUint8Array(): Uint8Array {\n    return this.data.toUint8Array();\n  }\n\n  static fromSchemeAndBytes(args: { scheme: AuthenticationKeyScheme; input: HexInput }): AuthenticationKey {\n    const { scheme, input } = args;\n    const inputBytes = Hex.fromHexInput(input).toUint8Array();\n    const hashInput = new Uint8Array([...inputBytes, scheme]);\n    const hash = sha3Hash.create();\n    hash.update(hashInput);\n    const hashDigest = hash.digest();\n    return new AuthenticationKey({ data: hashDigest });\n  }\n\n  /**\n   * @deprecated Use `fromPublicKey` instead\n   * Derives an AuthenticationKey from the public key seed bytes and an explicit derivation scheme.\n   *\n   * This facilitates targeting a specific scheme for deriving an authentication key from a public key.\n   *\n   * @param args - the public key and scheme to use for the derivation\n   */\n  public static fromPublicKeyAndScheme(args: { publicKey: AccountPublicKey; scheme: AuthenticationKeyScheme }) {\n    const { publicKey } = args;\n    return publicKey.authKey();\n  }\n\n  /**\n   * Converts a PublicKey(s) to an AuthenticationKey, using the derivation scheme inferred from the\n   * instance of the PublicKey type passed in.\n   *\n   * @param args.publicKey\n   * @returns AuthenticationKey\n   */\n  static fromPublicKey(args: { publicKey: AccountPublicKey }): AuthenticationKey {\n    const { publicKey } = args;\n    return publicKey.authKey();\n  }\n\n  /**\n   * Derives an account address from an AuthenticationKey. Since an AccountAddress is also 32 bytes,\n   * the AuthenticationKey bytes are directly translated to an AccountAddress.\n   *\n   * @returns AccountAddress\n   */\n  derivedAddress(): AccountAddress {\n    return new AccountAddress(this.data.toUint8Array());\n  }\n}\n"], "mappings": ";;;AAGA,SAASA,QAAA,IAAYC,CAAA,QAAgB;AAe9B,IAAMC,CAAA,GAAN,MAAMA,CAAA,SAA0BC,CAAa;EAalDC,YAAYC,CAAA,EAA0B;IACpC,MAAM;IACN,IAAM;QAAEC,IAAA,EAAAC;MAAK,IAAIF,CAAA;MACXG,CAAA,GAAMC,CAAA,CAAIC,YAAA,CAAaH,CAAI;IACjC,IAAIC,CAAA,CAAIG,YAAA,CAAa,EAAEC,MAAA,KAAWV,CAAA,CAAkBW,MAAA,EAClD,MAAM,IAAIC,KAAA,CAAM,uCAAuCZ,CAAA,CAAkBW,MAAM,EAAE;IAEnF,KAAKP,IAAA,GAAOE,CACd;EAAA;EAEAO,UAAUV,CAAA,EAA8B;IACtCA,CAAA,CAAWW,mBAAA,CAAoB,KAAKV,IAAA,CAAKK,YAAA,CAAa,CAAC,CACzD;EAAA;EAOA,OAAOM,YAAYZ,CAAA,EAA+C;IAChE,IAAME,CAAA,GAAQF,CAAA,CAAaa,qBAAA,CAAsBhB,CAAA,CAAkBW,MAAM;IACzE,OAAO,IAAIX,CAAA,CAAkB;MAAEI,IAAA,EAAMC;IAAM,CAAC,CAC9C;EAAA;EAEAY,SAAA,EAAmB;IACjB,OAAO,KAAKb,IAAA,CAAKa,QAAA,CAAS,CAC5B;EAAA;EAEAR,aAAA,EAA2B;IACzB,OAAO,KAAKL,IAAA,CAAKK,YAAA,CAAa,CAChC;EAAA;EAEA,OAAOS,mBAAmBf,CAAA,EAA+E;IACvG,IAAM;QAAEgB,MAAA,EAAAd,CAAA;QAAQe,KAAA,EAAAd;MAAM,IAAIH,CAAA;MACpBkB,CAAA,GAAad,CAAA,CAAIC,YAAA,CAAaF,CAAK,EAAEG,YAAA,CAAa;MAClDa,CAAA,GAAY,IAAIC,UAAA,CAAW,CAAC,GAAGF,CAAA,EAAYhB,CAAM,CAAC;MAClDmB,CAAA,GAAOzB,CAAA,CAAS0B,MAAA,CAAO;IAC7BD,CAAA,CAAKE,MAAA,CAAOJ,CAAS;IACrB,IAAMK,CAAA,GAAaH,CAAA,CAAKI,MAAA,CAAO;IAC/B,OAAO,IAAI5B,CAAA,CAAkB;MAAEI,IAAA,EAAMuB;IAAW,CAAC,CACnD;EAAA;EAUA,OAAcE,uBAAuB1B,CAAA,EAAwE;IAC3G,IAAM;MAAE2B,SAAA,EAAAzB;IAAU,IAAIF,CAAA;IACtB,OAAOE,CAAA,CAAU0B,OAAA,CAAQ,CAC3B;EAAA;EASA,OAAOC,cAAc7B,CAAA,EAA0D;IAC7E,IAAM;MAAE2B,SAAA,EAAAzB;IAAU,IAAIF,CAAA;IACtB,OAAOE,CAAA,CAAU0B,OAAA,CAAQ,CAC3B;EAAA;EAQAE,eAAA,EAAiC;IAC/B,OAAO,IAAIC,CAAA,CAAe,KAAK9B,IAAA,CAAKK,YAAA,CAAa,CAAC,CACpD;EAAA;AACF;AAzFaT,CAAA,CAMKW,MAAA,GAAiB;AAN5B,IAAMwB,CAAA,GAANnC,CAAA;AAAA,SAAAmC,CAAA,IAAAX,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}