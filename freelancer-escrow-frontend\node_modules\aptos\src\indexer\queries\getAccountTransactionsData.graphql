#import "./TokenActivitiesFieldsFragment";
query getAccountTransactionsData(
  $where_condition: account_transactions_bool_exp!
  $offset: Int
  $limit: Int
  $order_by: [account_transactions_order_by!]
) {
  account_transactions(where: $where_condition, order_by: $order_by, limit: $limit, offset: $offset) {
    token_activities_v2 {
      ...TokenActivitiesFields
    }
    transaction_version
    account_address
  }
}
