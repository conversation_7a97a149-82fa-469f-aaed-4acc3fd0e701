{"ast": null, "code": "var s = class extends Error {\n  constructor(e, i) {\n    super(e), this.invalidReason = i;\n  }\n};\nexport { s as a };", "map": {"version": 3, "names": ["s", "Error", "constructor", "e", "i", "invalidReason", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\common.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This error is used to explain why parsing failed.\n */\nexport class ParsingError<T> extends Error {\n  /**\n   * This provides a programmatic way to access why parsing failed. Downstream devs\n   * might want to use this to build their own error messages if the default error\n   * messages are not suitable for their use case. This should be an enum.\n   */\n  public invalidReason: T;\n\n  constructor(message: string, invalidReason: T) {\n    super(message);\n    this.invalidReason = invalidReason;\n  }\n}\n\n/**\n * Whereas ParsingError is thrown when parsing fails, e.g. in a fromString function,\n * this type is returned from \"defensive\" functions like isValid.\n */\nexport type ParsingResult<T> = {\n  /**\n   * True if valid, false otherwise.\n   */\n  valid: boolean;\n\n  /**\n   * If valid is false, this will be a code explaining why parsing failed.\n   */\n  invalidReason?: T;\n\n  /**\n   * If valid is false, this will be a string explaining why parsing failed.\n   */\n  invalidReasonMessage?: string;\n};\n"], "mappings": "AAMO,IAAMA,CAAA,GAAN,cAA8BC,KAAM;EAQzCC,YAAYC,CAAA,EAAiBC,CAAA,EAAkB;IAC7C,MAAMD,CAAO,GACb,KAAKE,aAAA,GAAgBD,CACvB;EAAA;AACF;AAAA,SAAAJ,CAAA,IAAAM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}