{"ast": null, "code": "import { A<PERSON>unt<PERSON><PERSON><PERSON> as l, <PERSON>P<PERSON>lic<PERSON><PERSON> as a, Ed25519P<PERSON>lic<PERSON><PERSON> as c, MultiEd25519Public<PERSON>ey as r, <PERSON><PERSON>ey as u, Serializable as y, SigningScheme as i } from \"@aptos-labs/ts-sdk\";\nvar d = class o extends y {\n  constructor({\n    address: e,\n    publicKey: n,\n    ansName: t\n  }) {\n    super();\n    this.address = l.from(e), this.publicKey = n, this.ansName = t;\n  }\n  serialize(e) {\n    if (this.address.serialize(e), this.publicKey instanceof c) e.serializeU32AsUleb128(i.Ed25519);else if (this.publicKey instanceof r) e.serializeU32AsUleb128(i.MultiEd25519);else if (this.publicKey instanceof a) e.serializeU32AsUleb128(i.SingleKey);else if (this.publicKey instanceof u) e.serializeU32AsUleb128(i.MultiKey);else throw new Error(\"Unsupported public key\");\n    this.publicKey.serialize(e), e.serializeStr(this.ansName ?? \"\");\n  }\n  static deserialize(e) {\n    let n = l.deserialize(e),\n      t = e.deserializeUleb128AsU32(),\n      s;\n    switch (t) {\n      case i.Ed25519:\n        s = c.deserialize(e);\n        break;\n      case i.MultiEd25519:\n        s = r.deserialize(e);\n        break;\n      case i.SingleKey:\n        s = a.deserialize(e);\n        break;\n      case i.MultiKey:\n        s = u.deserialize(e);\n        break;\n      default:\n        throw new Error(`Unknown variant index for WrappedPublicKey: ${t}`);\n    }\n    let b = e.deserializeStr() || void 0;\n    return new o({\n      address: n,\n      publicKey: s,\n      ansName: b\n    });\n  }\n};\nexport { d as a };", "map": {"version": 3, "names": ["Account<PERSON><PERSON><PERSON>", "l", "AnyPublicKey", "a", "Ed25519PublicKey", "c", "MultiEd25519PublicKey", "r", "MultiKey", "u", "Serializable", "y", "SigningScheme", "i", "d", "o", "constructor", "address", "e", "public<PERSON>ey", "n", "ans<PERSON>ame", "t", "from", "serialize", "serializeU32AsUleb128", "Ed25519", "MultiEd25519", "<PERSON><PERSON>ey", "Error", "serializeStr", "deserialize", "deserializeUleb128AsU32", "s", "b", "deserializeStr"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\AccountInfo.ts"], "sourcesContent": ["import {\n  AccountAddress, Account<PERSON>dd<PERSON>In<PERSON>, AnyP<PERSON><PERSON><PERSON><PERSON>,\n  Deserializer,\n  Ed25519<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  MultiEd25519<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ey,\n  Public<PERSON><PERSON>,\n  Serializable,\n  Serializer, SigningScheme\n} from '@aptos-labs/ts-sdk'\n\nexport interface AccountInfoInput {\n  address: AccountAddressInput\n  publicKey: PublicKey\n  ansName?: string\n}\n\nexport class AccountInfo extends Serializable {\n  readonly address: AccountAddress\n  readonly publicKey: PublicKey\n  readonly ansName?: string\n\n  constructor({ address, publicKey, ansName }: AccountInfoInput) {\n    super()\n    this.address = AccountAddress.from(address)\n    this.publicKey = publicKey\n    this.ansName = ansName\n  }\n\n  serialize(serializer: Serializer) {\n    this.address.serialize(serializer)\n    if (this.publicKey instanceof Ed25519PublicKey) {\n      serializer.serializeU32AsUleb128(SigningScheme.Ed25519)\n    } else if (this.publicKey instanceof MultiEd25519PublicK<PERSON>) {\n      serializer.serializeU32AsUleb128(SigningScheme.MultiEd25519)\n    } else if (this.publicKey instanceof AnyPublicKey) {\n      serializer.serializeU32AsUleb128(SigningScheme.SingleKey)\n    } else if (this.publicKey instanceof MultiKey) {\n      serializer.serializeU32AsUleb128(SigningScheme.MultiKey)\n    } else {\n      throw new Error('Unsupported public key')\n    }\n    this.publicKey.serialize(serializer)\n    serializer.serializeStr(this.ansName ?? '')\n  }\n\n  static deserialize(deserializer: Deserializer) {\n    const address = AccountAddress.deserialize(deserializer)\n    const variant = deserializer.deserializeUleb128AsU32()\n    let publicKey: PublicKey\n    switch (variant) {\n      case SigningScheme.Ed25519:\n        publicKey = Ed25519PublicKey.deserialize(deserializer)\n        break\n      case SigningScheme.MultiEd25519:\n        publicKey = MultiEd25519PublicKey.deserialize(deserializer)\n        break\n      case SigningScheme.SingleKey:\n        publicKey = AnyPublicKey.deserialize(deserializer)\n        break\n      case SigningScheme.MultiKey:\n        publicKey = MultiKey.deserialize(deserializer)\n        break\n      default:\n        throw new Error(`Unknown variant index for WrappedPublicKey: ${variant}`)\n    }\n    const ansName = deserializer.deserializeStr() || undefined\n    return new AccountInfo({ address, publicKey, ansName })\n  }\n}\n"], "mappings": "AAAA,SACEA,cAAA,IAAAC,CAAA,EAAqCC,YAAA,IAAAC,CAAA,EAErCC,gBAAA,IAAAC,CAAA,EACAC,qBAAA,IAAAC,CAAA,EAAuBC,QAAA,IAAAC,CAAA,EAEvBC,YAAA,IAAAC,CAAA,EACYC,aAAA,IAAAC,CAAA,QACP;AAQA,IAAMC,CAAA,GAAN,MAAMC,CAAA,SAAoBJ,CAAa;EAK5CK,YAAY;IAAEC,OAAA,EAAAC,CAAA;IAASC,SAAA,EAAAC,CAAA;IAAWC,OAAA,EAAAC;EAAQ,GAAqB;IAC7D,MAAM;IACN,KAAKL,OAAA,GAAUhB,CAAA,CAAesB,IAAA,CAAKL,CAAO,GAC1C,KAAKC,SAAA,GAAYC,CAAA,EACjB,KAAKC,OAAA,GAAUC,CACjB;EAAA;EAEAE,UAAUN,CAAA,EAAwB;IAEhC,IADA,KAAKD,OAAA,CAAQO,SAAA,CAAUN,CAAU,GAC7B,KAAKC,SAAA,YAAqBd,CAAA,EAC5Ba,CAAA,CAAWO,qBAAA,CAAsBZ,CAAA,CAAca,OAAO,WAC7C,KAAKP,SAAA,YAAqBZ,CAAA,EACnCW,CAAA,CAAWO,qBAAA,CAAsBZ,CAAA,CAAcc,YAAY,WAClD,KAAKR,SAAA,YAAqBhB,CAAA,EACnCe,CAAA,CAAWO,qBAAA,CAAsBZ,CAAA,CAAce,SAAS,WAC/C,KAAKT,SAAA,YAAqBV,CAAA,EACnCS,CAAA,CAAWO,qBAAA,CAAsBZ,CAAA,CAAcL,QAAQ,OAEvD,MAAM,IAAIqB,KAAA,CAAM,wBAAwB;IAE1C,KAAKV,SAAA,CAAUK,SAAA,CAAUN,CAAU,GACnCA,CAAA,CAAWY,YAAA,CAAa,KAAKT,OAAA,IAAW,EAAE,CAC5C;EAAA;EAEA,OAAOU,YAAYb,CAAA,EAA4B;IAC7C,IAAME,CAAA,GAAUnB,CAAA,CAAe8B,WAAA,CAAYb,CAAY;MACjDI,CAAA,GAAUJ,CAAA,CAAac,uBAAA,CAAwB;MACjDC,CAAA;IACJ,QAAQX,CAAA;MACN,KAAKT,CAAA,CAAca,OAAA;QACjBO,CAAA,GAAY5B,CAAA,CAAiB0B,WAAA,CAAYb,CAAY;QACrD;MACF,KAAKL,CAAA,CAAcc,YAAA;QACjBM,CAAA,GAAY1B,CAAA,CAAsBwB,WAAA,CAAYb,CAAY;QAC1D;MACF,KAAKL,CAAA,CAAce,SAAA;QACjBK,CAAA,GAAY9B,CAAA,CAAa4B,WAAA,CAAYb,CAAY;QACjD;MACF,KAAKL,CAAA,CAAcL,QAAA;QACjByB,CAAA,GAAYxB,CAAA,CAASsB,WAAA,CAAYb,CAAY;QAC7C;MACF;QACE,MAAM,IAAIW,KAAA,CAAM,+CAA+CP,CAAO,EAAE,CAC5E;IAAA;IACA,IAAMY,CAAA,GAAUhB,CAAA,CAAaiB,cAAA,CAAe,KAAK;IACjD,OAAO,IAAIpB,CAAA,CAAY;MAAEE,OAAA,EAAAG,CAAA;MAASD,SAAA,EAAAc,CAAA;MAAWZ,OAAA,EAAAa;IAAQ,CAAC,CACxD;EAAA;AACF;AAAA,SAAApB,CAAA,IAAAX,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}