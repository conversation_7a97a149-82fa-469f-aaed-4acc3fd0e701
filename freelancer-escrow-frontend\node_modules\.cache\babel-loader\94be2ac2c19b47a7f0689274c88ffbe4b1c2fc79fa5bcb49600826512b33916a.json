{"ast": null, "code": "var t = 255,\n  n = 65535,\n  U = **********,\n  _ = 18446744073709551615n,\n  o = 340282366920938463463374607431768211455n,\n  i = 115792089237316195423570985008687907853269984665640564039457584007913129639935n;\nexport { t as a, n as b, U as c, _ as d, o as e, i as f };", "map": {"version": 3, "names": ["t", "n", "U", "_", "o", "i", "a", "b", "c", "d", "e", "f"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\consts.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Uint8, Uint16, Uint32, Uint64, Uint128, Uint256 } from \"../types\";\n\n// Upper bound values for uint8, uint16, uint64 etc.  These are all derived as\n// 2^N - 1, where N is the number of bits in the type.\nexport const MAX_U8_NUMBER: Uint8 = 255;\nexport const MAX_U16_NUMBER: Uint16 = 65535;\nexport const MAX_U32_NUMBER: Uint32 = **********;\nexport const MAX_U64_BIG_INT: Uint64 = 18446744073709551615n;\nexport const MAX_U128_BIG_INT: Uint128 = 340282366920938463463374607431768211455n;\nexport const MAX_U256_BIG_INT: Uint256 =\n  115792089237316195423570985008687907853269984665640564039457584007913129639935n;\n"], "mappings": "AAOO,IAAMA,CAAA,GAAuB;EACvBC,CAAA,GAAyB;EACzBC,CAAA,GAAyB;EACzBC,CAAA,GAA0B;EAC1BC,CAAA,GAA4B;EAC5BC,CAAA,GACX;AAAA,SAAAL,CAAA,IAAAM,CAAA,EAAAL,CAAA,IAAAM,CAAA,EAAAL,CAAA,IAAAM,CAAA,EAAAL,CAAA,IAAAM,CAAA,EAAAL,CAAA,IAAAM,CAAA,EAAAL,CAAA,IAAAM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}