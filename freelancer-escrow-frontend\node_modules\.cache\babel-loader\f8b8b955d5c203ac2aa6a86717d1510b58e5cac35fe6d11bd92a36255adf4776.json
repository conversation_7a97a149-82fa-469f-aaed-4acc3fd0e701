{"ast": null, "code": "import { a as f } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as d } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as u } from \"./chunk-F7EMGK4M.mjs\";\nimport { e as g, j as h } from \"./chunk-LR65XHSF.mjs\";\nimport { a as p } from \"./chunk-A63SMUOU.mjs\";\nimport { b as s } from \"./chunk-BCUSI3N6.mjs\";\nimport { sha3_256 as H } from \"@noble/hashes/sha3\";\nimport { secp256k1 as o } from \"@noble/curves/secp256k1\";\nimport { HDKey as S } from \"@scure/bip32\";\nvar n = class n extends f {\n  constructor(e) {\n    super();\n    let r = s.fromHexInput(e);\n    if (r.toUint8Array().length !== n.LENGTH) throw new Error(`PublicKey length should be ${n.LENGTH}`);\n    this.key = r;\n  }\n  verifySignature(e) {\n    let {\n      message: r,\n      signature: i\n    } = e;\n    if (!(i instanceof y)) return !1;\n    let l = u(r),\n      c = s.fromHexInput(l).toUint8Array(),\n      A = H(c),\n      x = i.toUint8Array();\n    return o.verify(x, A, this.key.toUint8Array(), {\n      lowS: !0\n    });\n  }\n  toUint8Array() {\n    return this.key.toUint8Array();\n  }\n  serialize(e) {\n    e.serializeBytes(this.key.toUint8Array());\n  }\n  static deserialize(e) {\n    let r = e.deserializeBytes();\n    return new n(r);\n  }\n  static isPublicKey(e) {\n    return e instanceof n;\n  }\n};\nn.LENGTH = 65;\nvar m = n,\n  t = class t extends p {\n    constructor(e) {\n      super();\n      let r = s.fromHexInput(e);\n      if (r.toUint8Array().length !== t.LENGTH) throw new Error(`PrivateKey length should be ${t.LENGTH}`);\n      this.key = r;\n    }\n    static generate() {\n      let e = o.utils.randomPrivateKey();\n      return new t(e);\n    }\n    static fromDerivationPath(e, r) {\n      if (!g(e)) throw new Error(`Invalid derivation path ${e}`);\n      return t.fromDerivationPathInner(e, h(r));\n    }\n    static fromDerivationPathInner(e, r) {\n      let {\n        privateKey: i\n      } = S.fromMasterSeed(r).derive(e);\n      if (i === null) throw new Error(\"Invalid key\");\n      return new t(i);\n    }\n    sign(e) {\n      let r = u(e),\n        i = s.fromHexInput(r),\n        l = H(i.toUint8Array()),\n        c = o.sign(l, this.key.toUint8Array(), {\n          lowS: !0\n        });\n      return new y(c.toCompactRawBytes());\n    }\n    publicKey() {\n      let e = o.getPublicKey(this.key.toUint8Array(), !1);\n      return new m(e);\n    }\n    toUint8Array() {\n      return this.key.toUint8Array();\n    }\n    toString() {\n      return this.key.toString();\n    }\n    serialize(e) {\n      e.serializeBytes(this.toUint8Array());\n    }\n    static deserialize(e) {\n      let r = e.deserializeBytes();\n      return new t(r);\n    }\n    static isPrivateKey(e) {\n      return e instanceof t;\n    }\n  };\nt.LENGTH = 32;\nvar v = t,\n  a = class a extends d {\n    constructor(e) {\n      super();\n      let r = s.fromHexInput(e);\n      if (r.toUint8Array().length !== a.LENGTH) throw new Error(`Signature length should be ${a.LENGTH}, received ${r.toUint8Array().length}`);\n      this.data = r;\n    }\n    toUint8Array() {\n      return this.data.toUint8Array();\n    }\n    serialize(e) {\n      e.serializeBytes(this.data.toUint8Array());\n    }\n    static deserialize(e) {\n      let r = e.deserializeBytes();\n      return new a(r);\n    }\n  };\na.LENGTH = 64;\nvar y = a;\nexport { m as a, v as b, y as c };", "map": {"version": 3, "names": ["sha3_256", "H", "secp256k1", "o", "HDKey", "S", "n", "f", "constructor", "e", "r", "s", "fromHexInput", "toUint8Array", "length", "LENGTH", "Error", "key", "verifySignature", "message", "signature", "i", "y", "l", "u", "c", "A", "x", "verify", "lowS", "serialize", "serializeBytes", "deserialize", "deserializeBytes", "isPublicKey", "m", "t", "p", "generate", "utils", "randomPrivateKey", "fromDerivationPath", "g", "fromDerivationPathInner", "h", "privateKey", "fromMasterSeed", "derive", "sign", "toCompactRawBytes", "public<PERSON>ey", "getPublicKey", "toString", "isPrivateKey", "v", "a", "d", "data", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\secp256k1.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { sha3_256 } from \"@noble/hashes/sha3\";\nimport { secp256k1 } from \"@noble/curves/secp256k1\";\nimport { HDKey } from \"@scure/bip32\";\nimport { Serializable, Deserializer, Serializer } from \"../../bcs\";\nimport { Hex } from \"../hex\";\nimport { HexInput } from \"../../types\";\nimport { isValidBIP44Path, mnemonicToSeed } from \"./hdKey\";\nimport { PrivateKey } from \"./privateKey\";\nimport { PublicKey, VerifySignatureArgs } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { convertSigningMessage } from \"./utils\";\n\n/**\n * Represents the Secp256k1 ecdsa public key\n *\n * Secp256k1 authentication key is represented in the SDK as `AnyPublicKey`.\n */\nexport class Secp256k1PublicKey extends PublicKey {\n  // Secp256k1 ecdsa public keys contain a prefix indicating compression and two 32-byte coordinates.\n  static readonly LENGTH: number = 65;\n\n  // Hex value of the public key\n  private readonly key: Hex;\n\n  /**\n   * Create a new PublicKey instance from a Uint8Array or String.\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   */\n  constructor(hexInput: HexInput) {\n    super();\n\n    const hex = Hex.fromHexInput(hexInput);\n    if (hex.toUint8Array().length !== Secp256k1PublicKey.LENGTH) {\n      throw new Error(`PublicKey length should be ${Secp256k1PublicKey.LENGTH}`);\n    }\n    this.key = hex;\n  }\n\n  // region PublicKey\n  /**\n   * Verifies a Secp256k1 signature against the public key\n   *\n   * Note signatures are validated to be canonical as a malleability check\n   */\n  verifySignature(args: VerifySignatureArgs): boolean {\n    const { message, signature } = args;\n    if (!(signature instanceof Secp256k1Signature)) {\n      return false;\n    }\n    const messageToVerify = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToVerify).toUint8Array();\n    const messageSha3Bytes = sha3_256(messageBytes);\n    const signatureBytes = signature.toUint8Array();\n    return secp256k1.verify(signatureBytes, messageSha3Bytes, this.key.toUint8Array(), { lowS: true });\n  }\n\n  toUint8Array(): Uint8Array {\n    return this.key.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.key.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Secp256k1PublicKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Secp256k1PublicKey(bytes);\n  }\n\n  // endregion\n\n  /**\n   * @deprecated use `instanceof Secp256k1PublicKey` instead\n   * @param publicKey\n   */\n  static isPublicKey(publicKey: PublicKey): publicKey is Secp256k1PublicKey {\n    return publicKey instanceof Secp256k1PublicKey;\n  }\n}\n\n/**\n * A Secp256k1 ecdsa private key\n */\nexport class Secp256k1PrivateKey extends Serializable implements PrivateKey {\n  /**\n   * Length of Secp256k1 ecdsa private key\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The private key bytes\n   * @private\n   */\n  private readonly key: Hex;\n\n  // region Constructors\n\n  /**\n   * Create a new PrivateKey instance from a Uint8Array or String.\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   */\n  constructor(hexInput: HexInput) {\n    super();\n\n    const privateKeyHex = Hex.fromHexInput(hexInput);\n    if (privateKeyHex.toUint8Array().length !== Secp256k1PrivateKey.LENGTH) {\n      throw new Error(`PrivateKey length should be ${Secp256k1PrivateKey.LENGTH}`);\n    }\n\n    this.key = privateKeyHex;\n  }\n\n  /**\n   * Generate a new random private key.\n   *\n   * @returns Secp256k1PrivateKey\n   */\n  static generate(): Secp256k1PrivateKey {\n    const hexInput = secp256k1.utils.randomPrivateKey();\n    return new Secp256k1PrivateKey(hexInput);\n  }\n\n  /**\n   * Derives a private key from a mnemonic seed phrase.\n   *\n   * @param path the BIP44 path\n   * @param mnemonics the mnemonic seed phrase\n   *\n   * @returns The generated key\n   */\n  static fromDerivationPath(path: string, mnemonics: string): Secp256k1PrivateKey {\n    if (!isValidBIP44Path(path)) {\n      throw new Error(`Invalid derivation path ${path}`);\n    }\n    return Secp256k1PrivateKey.fromDerivationPathInner(path, mnemonicToSeed(mnemonics));\n  }\n\n  /**\n   * A private inner function so we can separate from the main fromDerivationPath() method\n   * to add tests to verify we create the keys correctly.\n   *\n   * @param path the BIP44 path\n   * @param seed the seed phrase created by the mnemonics\n   *\n   * @returns The generated key\n   */\n  private static fromDerivationPathInner(path: string, seed: Uint8Array): Secp256k1PrivateKey {\n    const { privateKey } = HDKey.fromMasterSeed(seed).derive(path);\n    // library returns privateKey as Uint8Array | null\n    if (privateKey === null) {\n      throw new Error(\"Invalid key\");\n    }\n\n    return new Secp256k1PrivateKey(privateKey);\n  }\n\n  // endregion\n\n  // region PrivateKey\n\n  /**\n   * Sign the given message with the private key.\n   *\n   * Note: signatures are canonical, and non-malleable\n   *\n   * @param message a message as a string or Uint8Array\n   * @returns Signature\n   */\n  sign(message: HexInput): Secp256k1Signature {\n    const messageToSign = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToSign);\n    const messageHashBytes = sha3_256(messageBytes.toUint8Array());\n    const signature = secp256k1.sign(messageHashBytes, this.key.toUint8Array(), { lowS: true });\n    return new Secp256k1Signature(signature.toCompactRawBytes());\n  }\n\n  /**\n   * Derive the Secp256k1PublicKey from this private key.\n   *\n   * @returns Secp256k1PublicKey\n   */\n  publicKey(): Secp256k1PublicKey {\n    const bytes = secp256k1.getPublicKey(this.key.toUint8Array(), false);\n    return new Secp256k1PublicKey(bytes);\n  }\n\n  /**\n   * Get the private key in bytes (Uint8Array).\n   *\n   * @returns\n   */\n  toUint8Array(): Uint8Array {\n    return this.key.toUint8Array();\n  }\n\n  /**\n   * Get the private key as a hex string with the 0x prefix.\n   *\n   * @returns string representation of the private key\n   */\n  toString(): string {\n    return this.key.toString();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Secp256k1PrivateKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Secp256k1PrivateKey(bytes);\n  }\n\n  // endregion\n\n  /**\n   * @deprecated use `instanceof Secp256k1PrivateKey` instead\n   */\n  static isPrivateKey(privateKey: PrivateKey): privateKey is Secp256k1PrivateKey {\n    return privateKey instanceof Secp256k1PrivateKey;\n  }\n}\n\n/**\n * A signature of a message signed using a Secp256k1 ecdsa private key\n */\nexport class Secp256k1Signature extends Signature {\n  /**\n   * Secp256k1 ecdsa signatures are 256-bit.\n   */\n  static readonly LENGTH = 64;\n\n  /**\n   * The signature bytes\n   * @private\n   */\n  private readonly data: Hex;\n\n  // region Constructors\n\n  /**\n   * Create a new Signature instance from a Uint8Array or String.\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   */\n  constructor(hexInput: HexInput) {\n    super();\n    const data = Hex.fromHexInput(hexInput);\n    if (data.toUint8Array().length !== Secp256k1Signature.LENGTH) {\n      throw new Error(\n        `Signature length should be ${Secp256k1Signature.LENGTH}, received ${data.toUint8Array().length}`,\n      );\n    }\n    this.data = data;\n  }\n\n  // endregion\n\n  // region Signature\n\n  toUint8Array(): Uint8Array {\n    return this.data.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.data.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Secp256k1Signature {\n    const hex = deserializer.deserializeBytes();\n    return new Secp256k1Signature(hex);\n  }\n\n  // endregion\n}\n"], "mappings": ";;;;;;AAGA,SAASA,QAAA,IAAAC,CAAA,QAAgB;AACzB,SAASC,SAAA,IAAAC,CAAA,QAAiB;AAC1B,SAASC,KAAA,IAAAC,CAAA,QAAa;AAef,IAAMC,CAAA,GAAN,MAAMA,CAAA,SAA2BC,CAAU;EAYhDC,YAAYC,CAAA,EAAoB;IAC9B,MAAM;IAEN,IAAMC,CAAA,GAAMC,CAAA,CAAIC,YAAA,CAAaH,CAAQ;IACrC,IAAIC,CAAA,CAAIG,YAAA,CAAa,EAAEC,MAAA,KAAWR,CAAA,CAAmBS,MAAA,EACnD,MAAM,IAAIC,KAAA,CAAM,8BAA8BV,CAAA,CAAmBS,MAAM,EAAE;IAE3E,KAAKE,GAAA,GAAMP,CACb;EAAA;EAQAQ,gBAAgBT,CAAA,EAAoC;IAClD,IAAM;MAAEU,OAAA,EAAAT,CAAA;MAASU,SAAA,EAAAC;IAAU,IAAIZ,CAAA;IAC/B,IAAI,EAAEY,CAAA,YAAqBC,CAAA,GACzB,OAAO;IAET,IAAMC,CAAA,GAAkBC,CAAA,CAAsBd,CAAO;MAC/Ce,CAAA,GAAed,CAAA,CAAIC,YAAA,CAAaW,CAAe,EAAEV,YAAA,CAAa;MAC9Da,CAAA,GAAmBzB,CAAA,CAASwB,CAAY;MACxCE,CAAA,GAAiBN,CAAA,CAAUR,YAAA,CAAa;IAC9C,OAAOV,CAAA,CAAUyB,MAAA,CAAOD,CAAA,EAAgBD,CAAA,EAAkB,KAAKT,GAAA,CAAIJ,YAAA,CAAa,GAAG;MAAEgB,IAAA,EAAM;IAAK,CAAC,CACnG;EAAA;EAEAhB,aAAA,EAA2B;IACzB,OAAO,KAAKI,GAAA,CAAIJ,YAAA,CAAa,CAC/B;EAAA;EAMAiB,UAAUrB,CAAA,EAA8B;IACtCA,CAAA,CAAWsB,cAAA,CAAe,KAAKd,GAAA,CAAIJ,YAAA,CAAa,CAAC,CACnD;EAAA;EAEA,OAAOmB,YAAYvB,CAAA,EAAgD;IACjE,IAAMC,CAAA,GAAQD,CAAA,CAAawB,gBAAA,CAAiB;IAC5C,OAAO,IAAI3B,CAAA,CAAmBI,CAAK,CACrC;EAAA;EAQA,OAAOwB,YAAYzB,CAAA,EAAuD;IACxE,OAAOA,CAAA,YAAqBH,CAC9B;EAAA;AACF;AAlEaA,CAAA,CAEKS,MAAA,GAAiB;AAF5B,IAAMoB,CAAA,GAAN7B,CAAA;EAuEM8B,CAAA,GAAN,MAAMA,CAAA,SAA4BC,CAAmC;IAmB1E7B,YAAYC,CAAA,EAAoB;MAC9B,MAAM;MAEN,IAAMC,CAAA,GAAgBC,CAAA,CAAIC,YAAA,CAAaH,CAAQ;MAC/C,IAAIC,CAAA,CAAcG,YAAA,CAAa,EAAEC,MAAA,KAAWsB,CAAA,CAAoBrB,MAAA,EAC9D,MAAM,IAAIC,KAAA,CAAM,+BAA+BoB,CAAA,CAAoBrB,MAAM,EAAE;MAG7E,KAAKE,GAAA,GAAMP,CACb;IAAA;IAOA,OAAO4B,SAAA,EAAgC;MACrC,IAAM7B,CAAA,GAAWN,CAAA,CAAUoC,KAAA,CAAMC,gBAAA,CAAiB;MAClD,OAAO,IAAIJ,CAAA,CAAoB3B,CAAQ,CACzC;IAAA;IAUA,OAAOgC,mBAAmBhC,CAAA,EAAcC,CAAA,EAAwC;MAC9E,IAAI,CAACgC,CAAA,CAAiBjC,CAAI,GACxB,MAAM,IAAIO,KAAA,CAAM,2BAA2BP,CAAI,EAAE;MAEnD,OAAO2B,CAAA,CAAoBO,uBAAA,CAAwBlC,CAAA,EAAMmC,CAAA,CAAelC,CAAS,CAAC,CACpF;IAAA;IAWA,OAAeiC,wBAAwBlC,CAAA,EAAcC,CAAA,EAAuC;MAC1F,IAAM;QAAEmC,UAAA,EAAAxB;MAAW,IAAIhB,CAAA,CAAMyC,cAAA,CAAepC,CAAI,EAAEqC,MAAA,CAAOtC,CAAI;MAE7D,IAAIY,CAAA,KAAe,MACjB,MAAM,IAAIL,KAAA,CAAM,aAAa;MAG/B,OAAO,IAAIoB,CAAA,CAAoBf,CAAU,CAC3C;IAAA;IAcA2B,KAAKvC,CAAA,EAAuC;MAC1C,IAAMC,CAAA,GAAgBc,CAAA,CAAsBf,CAAO;QAC7CY,CAAA,GAAeV,CAAA,CAAIC,YAAA,CAAaF,CAAa;QAC7Ca,CAAA,GAAmBtB,CAAA,CAASoB,CAAA,CAAaR,YAAA,CAAa,CAAC;QACvDY,CAAA,GAAYtB,CAAA,CAAU6C,IAAA,CAAKzB,CAAA,EAAkB,KAAKN,GAAA,CAAIJ,YAAA,CAAa,GAAG;UAAEgB,IAAA,EAAM;QAAK,CAAC;MAC1F,OAAO,IAAIP,CAAA,CAAmBG,CAAA,CAAUwB,iBAAA,CAAkB,CAAC,CAC7D;IAAA;IAOAC,UAAA,EAAgC;MAC9B,IAAMzC,CAAA,GAAQN,CAAA,CAAUgD,YAAA,CAAa,KAAKlC,GAAA,CAAIJ,YAAA,CAAa,GAAG,EAAK;MACnE,OAAO,IAAIsB,CAAA,CAAmB1B,CAAK,CACrC;IAAA;IAOAI,aAAA,EAA2B;MACzB,OAAO,KAAKI,GAAA,CAAIJ,YAAA,CAAa,CAC/B;IAAA;IAOAuC,SAAA,EAAmB;MACjB,OAAO,KAAKnC,GAAA,CAAImC,QAAA,CAAS,CAC3B;IAAA;IAMAtB,UAAUrB,CAAA,EAA8B;MACtCA,CAAA,CAAWsB,cAAA,CAAe,KAAKlB,YAAA,CAAa,CAAC,CAC/C;IAAA;IAEA,OAAOmB,YAAYvB,CAAA,EAAiD;MAClE,IAAMC,CAAA,GAAQD,CAAA,CAAawB,gBAAA,CAAiB;MAC5C,OAAO,IAAIG,CAAA,CAAoB1B,CAAK,CACtC;IAAA;IAOA,OAAO2C,aAAa5C,CAAA,EAA2D;MAC7E,OAAOA,CAAA,YAAsB2B,CAC/B;IAAA;EACF;AA/IaA,CAAA,CAIKrB,MAAA,GAAiB;AAJ5B,IAAMuC,CAAA,GAANlB,CAAA;EAoJMmB,CAAA,GAAN,MAAMA,CAAA,SAA2BC,CAAU;IAmBhDhD,YAAYC,CAAA,EAAoB;MAC9B,MAAM;MACN,IAAMC,CAAA,GAAOC,CAAA,CAAIC,YAAA,CAAaH,CAAQ;MACtC,IAAIC,CAAA,CAAKG,YAAA,CAAa,EAAEC,MAAA,KAAWyC,CAAA,CAAmBxC,MAAA,EACpD,MAAM,IAAIC,KAAA,CACR,8BAA8BuC,CAAA,CAAmBxC,MAAM,cAAcL,CAAA,CAAKG,YAAA,CAAa,EAAEC,MAAM,EACjG;MAEF,KAAK2C,IAAA,GAAO/C,CACd;IAAA;IAMAG,aAAA,EAA2B;MACzB,OAAO,KAAK4C,IAAA,CAAK5C,YAAA,CAAa,CAChC;IAAA;IAMAiB,UAAUrB,CAAA,EAA8B;MACtCA,CAAA,CAAWsB,cAAA,CAAe,KAAK0B,IAAA,CAAK5C,YAAA,CAAa,CAAC,CACpD;IAAA;IAEA,OAAOmB,YAAYvB,CAAA,EAAgD;MACjE,IAAMC,CAAA,GAAMD,CAAA,CAAawB,gBAAA,CAAiB;MAC1C,OAAO,IAAIsB,CAAA,CAAmB7C,CAAG,CACnC;IAAA;EAGF;AApDa6C,CAAA,CAIKxC,MAAA,GAAS;AAJpB,IAAMO,CAAA,GAANiC,CAAA;AAAA,SAAApB,CAAA,IAAAoB,CAAA,EAAAD,CAAA,IAAAI,CAAA,EAAApC,CAAA,IAAAG,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}