{"ast": null, "code": "import { b as c } from \"./chunk-BCUSI3N6.mjs\";\nimport { a as U, b, c as f, d as l, e as h, f as y } from \"./chunk-56CNRT2K.mjs\";\nimport { a as s } from \"./chunk-FVA2OPG4.mjs\";\nvar B = class {\n    bcsToBytes() {\n      let e = new n();\n      return this.serialize(e), e.toUint8Array();\n    }\n    bcsToHex() {\n      let e = this.bcsToBytes();\n      return c.fromHexInput(e);\n    }\n  },\n  n = class {\n    constructor(e = 64) {\n      if (e <= 0) throw new Error(\"Length needs to be greater than 0\");\n      this.buffer = new ArrayBuffer(e), this.offset = 0;\n    }\n    ensureBufferWillHandleSize(e) {\n      for (; this.buffer.byteLength < this.offset + e;) {\n        let t = new ArrayBuffer(this.buffer.byteLength * 2);\n        new Uint8Array(t).set(new Uint8Array(this.buffer)), this.buffer = t;\n      }\n    }\n    appendToBuffer(e) {\n      this.ensureBufferWillHandleSize(e.length), new Uint8Array(this.buffer, this.offset).set(e), this.offset += e.length;\n    }\n    serializeWithFunction(e, t, i) {\n      this.ensureBufferWillHandleSize(t);\n      let a = new DataView(this.buffer, this.offset);\n      e.apply(a, [0, i, !0]), this.offset += t;\n    }\n    serializeStr(e) {\n      let t = new TextEncoder();\n      this.serializeBytes(t.encode(e));\n    }\n    serializeBytes(e) {\n      this.serializeU32AsUleb128(e.length), this.appendToBuffer(e);\n    }\n    serializeFixedBytes(e) {\n      this.appendToBuffer(e);\n    }\n    serializeBool(e) {\n      A(e);\n      let t = e ? 1 : 0;\n      this.appendToBuffer(new Uint8Array([t]));\n    }\n    serializeU8(e) {\n      this.appendToBuffer(new Uint8Array([e]));\n    }\n    serializeU16(e) {\n      this.serializeWithFunction(DataView.prototype.setUint16, 2, e);\n    }\n    serializeU32(e) {\n      this.serializeWithFunction(DataView.prototype.setUint32, 4, e);\n    }\n    serializeU64(e) {\n      let t = BigInt(e) & BigInt(f),\n        i = BigInt(e) >> BigInt(32);\n      this.serializeU32(Number(t)), this.serializeU32(Number(i));\n    }\n    serializeU128(e) {\n      let t = BigInt(e) & l,\n        i = BigInt(e) >> BigInt(64);\n      this.serializeU64(t), this.serializeU64(i);\n    }\n    serializeU256(e) {\n      let t = BigInt(e) & h,\n        i = BigInt(e) >> BigInt(128);\n      this.serializeU128(t), this.serializeU128(i);\n    }\n    serializeU32AsUleb128(e) {\n      let t = e,\n        i = [];\n      for (; t >>> 7;) i.push(t & 127 | 128), t >>>= 7;\n      i.push(t), this.appendToBuffer(new Uint8Array(i));\n    }\n    toUint8Array() {\n      return new Uint8Array(this.buffer).slice(0, this.offset);\n    }\n    serialize(e) {\n      e.serialize(this);\n    }\n    serializeVector(e) {\n      this.serializeU32AsUleb128(e.length), e.forEach(t => {\n        t.serialize(this);\n      });\n    }\n    serializeOption(e) {\n      let t = e !== void 0;\n      this.serializeBool(t), t && e.serialize(this);\n    }\n    serializeOptionStr(e) {\n      e === void 0 ? this.serializeU32AsUleb128(0) : (this.serializeU32AsUleb128(1), this.serializeStr(e));\n    }\n  };\ns([o(0, U)], n.prototype, \"serializeU8\", 1), s([o(0, b)], n.prototype, \"serializeU16\", 1), s([o(0, f)], n.prototype, \"serializeU32\", 1), s([o(BigInt(0), l)], n.prototype, \"serializeU64\", 1), s([o(BigInt(0), h)], n.prototype, \"serializeU128\", 1), s([o(BigInt(0), y)], n.prototype, \"serializeU256\", 1), s([o(0, f)], n.prototype, \"serializeU32AsUleb128\", 1);\nfunction A(r) {\n  if (typeof r != \"boolean\") throw new Error(`${r} is not a boolean value`);\n}\nvar z = (r, e, t) => `${r} is out of range: [${e}, ${t}]`;\nfunction g(r, e, t) {\n  let i = BigInt(r);\n  if (i > BigInt(t) || i < BigInt(e)) throw new Error(z(r, e, t));\n}\nfunction o(r, e) {\n  return (t, i, a) => {\n    let p = a.value;\n    return a.value = function (u) {\n      return g(u, r, e), p.apply(this, [u]);\n    }, a;\n  };\n}\nexport { B as a, n as b, A as c, z as d, g as e };", "map": {"version": 3, "names": ["B", "bcsToBytes", "e", "n", "serialize", "toUint8Array", "bcsToHex", "c", "fromHexInput", "constructor", "Error", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offset", "ensureBufferWillHandleSize", "byteLength", "t", "Uint8Array", "set", "append<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "serializeWithFunction", "i", "a", "DataView", "apply", "serializeStr", "TextEncoder", "serializeBytes", "encode", "serializeU32AsUleb128", "serializeFixedBytes", "serializeBool", "A", "serializeU8", "serializeU16", "prototype", "setUint16", "serializeU32", "setUint32", "serializeU64", "BigInt", "f", "Number", "serializeU128", "l", "serializeU256", "h", "push", "slice", "serializeVector", "for<PERSON>ach", "serializeOption", "serializeOptionStr", "s", "o", "U", "b", "y", "r", "z", "outOfRangeErrorMessage", "g", "p", "value", "u", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\serializer.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable no-bitwise */\nimport {\n  MAX_U128_BIG_INT,\n  MAX_U16_NUMBER,\n  MAX_U32_NUMBER,\n  MAX_U64_BIG_INT,\n  MAX_U8_NUMBER,\n  MAX_U256_BIG_INT,\n} from \"./consts\";\nimport { Hex } from \"../core/hex\";\nimport { AnyNumber, Uint16, Uint32, Uint8 } from \"../types\";\n\n// This class is intended to be used as a base class for all serializable types.\n// It can be used to facilitate composable serialization of a complex type and\n// in general to serialize a type to its BCS representation.\nexport abstract class Serializable {\n  abstract serialize(serializer: Serializer): void;\n\n  /**\n   * Serializes a `Serializable` value to its BCS representation.\n   * This function is the Typescript SDK equivalent of `bcs::to_bytes` in Move.\n   * @returns the BCS representation of the Serializable instance as a byte buffer\n   */\n  bcsToBytes(): Uint8Array {\n    const serializer = new Serializer();\n    this.serialize(serializer);\n    return serializer.toUint8Array();\n  }\n\n  /**\n   * Helper function to get a value's BCS-serialized bytes as a Hex instance.\n   * @returns a Hex instance with the BCS-serialized bytes loaded into its underlying Uint8Array\n   */\n  bcsToHex(): Hex {\n    const bcsBytes = this.bcsToBytes();\n    return Hex.fromHexInput(bcsBytes);\n  }\n}\n\nexport class Serializer {\n  private buffer: ArrayBuffer;\n\n  private offset: number;\n\n  // Constructs a serializer with a buffer of size `length` bytes, 64 bytes by default.\n  // `length` must be greater than 0.\n  constructor(length: number = 64) {\n    if (length <= 0) {\n      throw new Error(\"Length needs to be greater than 0\");\n    }\n    this.buffer = new ArrayBuffer(length);\n    this.offset = 0;\n  }\n\n  private ensureBufferWillHandleSize(bytes: number) {\n    while (this.buffer.byteLength < this.offset + bytes) {\n      const newBuffer = new ArrayBuffer(this.buffer.byteLength * 2);\n      new Uint8Array(newBuffer).set(new Uint8Array(this.buffer));\n      this.buffer = newBuffer;\n    }\n  }\n\n  protected appendToBuffer(values: Uint8Array) {\n    this.ensureBufferWillHandleSize(values.length);\n    new Uint8Array(this.buffer, this.offset).set(values);\n    this.offset += values.length;\n  }\n\n  private serializeWithFunction(\n    fn: (byteOffset: number, value: number, littleEndian?: boolean) => void,\n    bytesLength: number,\n    value: number,\n  ) {\n    this.ensureBufferWillHandleSize(bytesLength);\n    const dv = new DataView(this.buffer, this.offset);\n    fn.apply(dv, [0, value, true]);\n    this.offset += bytesLength;\n  }\n\n  /**\n   * Serializes a string. UTF8 string is supported.\n   *\n   * The number of bytes in the string content is serialized first, as a uleb128-encoded u32 integer.\n   * Then the string content is serialized as UTF8 encoded bytes.\n   *\n   * BCS layout for \"string\": string_length | string_content\n   * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.\n   *\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeStr(\"1234abcd\");\n   * assert(serializer.toUint8Array() === new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   * ```\n   */\n  serializeStr(value: string) {\n    const textEncoder = new TextEncoder();\n    this.serializeBytes(textEncoder.encode(value));\n  }\n\n  /**\n   * Serializes an array of bytes.\n   *\n   * BCS layout for \"bytes\": bytes_length | bytes\n   * where bytes_length is a u32 integer encoded as a uleb128 integer, equal to the length of the bytes array.\n   */\n  serializeBytes(value: Uint8Array) {\n    this.serializeU32AsUleb128(value.length);\n    this.appendToBuffer(value);\n  }\n\n  /**\n   * Serializes an array of bytes with known length. Therefore, length doesn't need to be\n   * serialized to help deserialization.\n   *\n   * When deserializing, the number of bytes to deserialize needs to be passed in.\n   */\n  serializeFixedBytes(value: Uint8Array) {\n    this.appendToBuffer(value);\n  }\n\n  /**\n   * Serializes a boolean value.\n   *\n   * BCS layout for \"boolean\": One byte. \"0x01\" for true and \"0x00\" for false.\n   */\n  serializeBool(value: boolean) {\n    ensureBoolean(value);\n    const byteValue = value ? 1 : 0;\n    this.appendToBuffer(new Uint8Array([byteValue]));\n  }\n\n  /**\n   * Serializes a uint8 number.\n   *\n   * BCS layout for \"uint8\": One byte. Binary format in little-endian representation.\n   */\n  @checkNumberRange(0, MAX_U8_NUMBER)\n  serializeU8(value: Uint8) {\n    this.appendToBuffer(new Uint8Array([value]));\n  }\n\n  /**\n   * Serializes a uint16 number.\n   *\n   * BCS layout for \"uint16\": Two bytes. Binary format in little-endian representation.\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeU16(4660);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x34, 0x12]));\n   * ```\n   */\n  @checkNumberRange(0, MAX_U16_NUMBER)\n  serializeU16(value: Uint16) {\n    this.serializeWithFunction(DataView.prototype.setUint16, 2, value);\n  }\n\n  /**\n   * Serializes a uint32 number.\n   *\n   * BCS layout for \"uint32\": Four bytes. Binary format in little-endian representation.\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeU32(305419896);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x78, 0x56, 0x34, 0x12]));\n   * ```\n   */\n  @checkNumberRange(0, MAX_U32_NUMBER)\n  serializeU32(value: Uint32) {\n    this.serializeWithFunction(DataView.prototype.setUint32, 4, value);\n  }\n\n  /**\n   * Serializes a uint64 number.\n   *\n   * BCS layout for \"uint64\": Eight bytes. Binary format in little-endian representation.\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeU64(1311768467750121216);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));\n   * ```\n   */\n  @checkNumberRange(BigInt(0), MAX_U64_BIG_INT)\n  serializeU64(value: AnyNumber) {\n    const low = BigInt(value) & BigInt(MAX_U32_NUMBER);\n    const high = BigInt(value) >> BigInt(32);\n\n    // write little endian number\n    this.serializeU32(Number(low));\n    this.serializeU32(Number(high));\n  }\n\n  /**\n   * Serializes a uint128 number.\n   *\n   * BCS layout for \"uint128\": Sixteen bytes. Binary format in little-endian representation.\n   */\n  @checkNumberRange(BigInt(0), MAX_U128_BIG_INT)\n  serializeU128(value: AnyNumber) {\n    const low = BigInt(value) & MAX_U64_BIG_INT;\n    const high = BigInt(value) >> BigInt(64);\n\n    // write little endian number\n    this.serializeU64(low);\n    this.serializeU64(high);\n  }\n\n  /**\n   * Serializes a uint256 number.\n   *\n   * BCS layout for \"uint256\": Sixteen bytes. Binary format in little-endian representation.\n   */\n  @checkNumberRange(BigInt(0), MAX_U256_BIG_INT)\n  serializeU256(value: AnyNumber) {\n    const low = BigInt(value) & MAX_U128_BIG_INT;\n    const high = BigInt(value) >> BigInt(128);\n\n    // write little endian number\n    this.serializeU128(low);\n    this.serializeU128(high);\n  }\n\n  /**\n   * Serializes a uint32 number with uleb128.\n   *\n   * BCS uses uleb128 encoding in two cases: (1) lengths of variable-length sequences and (2) tags of enum values\n   */\n  @checkNumberRange(0, MAX_U32_NUMBER)\n  serializeU32AsUleb128(val: Uint32) {\n    let value = val;\n    const valueArray = [];\n    while (value >>> 7 !== 0) {\n      valueArray.push((value & 0x7f) | 0x80);\n      value >>>= 7;\n    }\n    valueArray.push(value);\n    this.appendToBuffer(new Uint8Array(valueArray));\n  }\n\n  /**\n   * Returns the buffered bytes\n   */\n  toUint8Array(): Uint8Array {\n    return new Uint8Array(this.buffer).slice(0, this.offset);\n  }\n\n  /**\n   * Serializes a `Serializable` value, facilitating composable serialization.\n   *\n   * @param value The Serializable value to serialize\n   *\n   * @example\n   * // Define the MoveStruct class that implements the Serializable interface\n   * class MoveStruct extends Serializable {\n   *     constructor(\n   *         public creatorAddress: AccountAddress, // where AccountAddress extends Serializable\n   *         public collectionName: string,\n   *         public tokenName: string\n   *     ) {}\n   *\n   *     serialize(serializer: Serializer): void {\n   *         serializer.serialize(this.creatorAddress);  // Composable serialization of another Serializable object\n   *         serializer.serializeStr(this.collectionName);\n   *         serializer.serializeStr(this.tokenName);\n   *     }\n   * }\n   *\n   * // Construct a MoveStruct\n   * const moveStruct = new MoveStruct(new AccountAddress(...), \"MyCollection\", \"TokenA\");\n   *\n   * // Serialize a string, a u64 number, and a MoveStruct instance.\n   * const serializer = new Serializer();\n   * serializer.serializeStr(\"ExampleString\");\n   * serializer.serializeU64(********);\n   * serializer.serialize(moveStruct);\n   *\n   * // Get the bytes from the Serializer instance\n   * const serializedBytes = serializer.toUint8Array();\n   *\n   * @returns the serializer instance\n   */\n  serialize<T extends Serializable>(value: T): void {\n    // NOTE: The `serialize` method called by `value` is defined in `value`'s\n    // Serializable interface, not the one defined in this class.\n    value.serialize(this);\n  }\n\n  /**\n   * Serializes an array of BCS Serializable values to a serializer instance.\n   * Note that this does not return anything. The bytes are added to the serializer instance's byte buffer.\n   *\n   * @param values The array of BCS Serializable values\n   * @example\n   * const addresses = new Array<AccountAddress>(\n   *   AccountAddress.from(\"0x1\"),\n   *   AccountAddress.from(\"0x2\"),\n   *   AccountAddress.from(\"0xa\"),\n   *   AccountAddress.from(\"0xb\"),\n   * );\n   * const serializer = new Serializer();\n   * serializer.serializeVector(addresses);\n   * const serializedBytes = serializer.toUint8Array();\n   * // serializedBytes is now the BCS-serialized bytes\n   * // The equivalent value in Move would be:\n   * // `bcs::to_bytes(&vector<address> [@0x1, @0x2, @0xa, @0xb])`;\n   */\n  serializeVector<T extends Serializable>(values: Array<T>): void {\n    this.serializeU32AsUleb128(values.length);\n    values.forEach((item) => {\n      item.serialize(this);\n    });\n  }\n\n  /**\n   * Serializes a BCS Serializable values into a serializer instance or undefined.\n   * Note that this does not return anything. The bytes are added to the serializer instance's byte buffer.\n   *\n   * @param values The array of BCS Serializable values\n   *\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeOption(new AccountAddress(...));\n   * const serializedBytes = serializer.toUint8Array();\n   * // serializedBytes is now the BCS-serialized byte representation of AccountAddress\n   *\n   * const serializer = new Serializer();\n   * serializer.serializeOption(undefined);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x00]));\n   * ```\n   */\n  serializeOption<T extends Serializable>(value?: T): void {\n    const hasValue = value !== undefined;\n    this.serializeBool(hasValue);\n    if (hasValue) {\n      value.serialize(this);\n    }\n  }\n\n  /**\n   * Serializes an optional string. UTF8 string is supported.\n   *\n   * The existence of the string is encoded first, 0 if undefined and 1 if it exists.\n   * Them the number of bytes in the string content is serialized, as a uleb128-encoded u32 integer.\n   * Then the string content is serialized as UTF8 encoded bytes.\n   *\n   * BCS layout for optional \"string\": 1 | string_length | string_content\n   * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.\n   *\n   * BCS layout for undefined: 0\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeOptionStr(\"1234abcd\");\n   * assert(serializer.toUint8Array() === new Uint8Array([1, 8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   *\n   * const serializer = new Serializer();\n   * serializer.serializeOptionStr(undefined);\n   * assert(serializer.toUint8Array() === new Uint8Array([0]));\n   * ```\n   */\n  serializeOptionStr(value?: string): void {\n    if (value === undefined) {\n      this.serializeU32AsUleb128(0);\n    } else {\n      this.serializeU32AsUleb128(1);\n      this.serializeStr(value);\n    }\n  }\n}\n\nexport function ensureBoolean(value: unknown): asserts value is boolean {\n  if (typeof value !== \"boolean\") {\n    throw new Error(`${value} is not a boolean value`);\n  }\n}\n\nexport const outOfRangeErrorMessage = (value: AnyNumber, min: AnyNumber, max: AnyNumber) =>\n  `${value} is out of range: [${min}, ${max}]`;\n\nexport function validateNumberInRange<T extends AnyNumber>(value: T, minValue: T, maxValue: T) {\n  const valueBigInt = BigInt(value);\n  if (valueBigInt > BigInt(maxValue) || valueBigInt < BigInt(minValue)) {\n    throw new Error(outOfRangeErrorMessage(value, minValue, maxValue));\n  }\n}\n\n/**\n * A decorator to ensure the input argument for a function is within a range.\n * @param minValue The input argument must be >= minValue\n * @param maxValue The input argument must be <= maxValue\n */\nfunction checkNumberRange<T extends AnyNumber>(minValue: T, maxValue: T) {\n  return (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) => {\n    const childFunction = descriptor.value;\n    // eslint-disable-next-line no-param-reassign\n    descriptor.value = function deco(value: AnyNumber) {\n      validateNumberInRange(value, minValue, maxValue);\n      return childFunction.apply(this, [value]);\n    };\n\n    return descriptor;\n  };\n}\n"], "mappings": ";;;AAkBO,IAAeA,CAAA,GAAf,MAA4B;IAQjCC,WAAA,EAAyB;MACvB,IAAMC,CAAA,GAAa,IAAIC,CAAA;MACvB,YAAKC,SAAA,CAAUF,CAAU,GAClBA,CAAA,CAAWG,YAAA,CAAa,CACjC;IAAA;IAMAC,SAAA,EAAgB;MACd,IAAMJ,CAAA,GAAW,KAAKD,UAAA,CAAW;MACjC,OAAOM,CAAA,CAAIC,YAAA,CAAaN,CAAQ,CAClC;IAAA;EACF;EAEaC,CAAA,GAAN,MAAiB;IAOtBM,YAAYP,CAAA,GAAiB,IAAI;MAC/B,IAAIA,CAAA,IAAU,GACZ,MAAM,IAAIQ,KAAA,CAAM,mCAAmC;MAErD,KAAKC,MAAA,GAAS,IAAIC,WAAA,CAAYV,CAAM,GACpC,KAAKW,MAAA,GAAS,CAChB;IAAA;IAEQC,2BAA2BZ,CAAA,EAAe;MAChD,OAAO,KAAKS,MAAA,CAAOI,UAAA,GAAa,KAAKF,MAAA,GAASX,CAAA,GAAO;QACnD,IAAMc,CAAA,GAAY,IAAIJ,WAAA,CAAY,KAAKD,MAAA,CAAOI,UAAA,GAAa,CAAC;QAC5D,IAAIE,UAAA,CAAWD,CAAS,EAAEE,GAAA,CAAI,IAAID,UAAA,CAAW,KAAKN,MAAM,CAAC,GACzD,KAAKA,MAAA,GAASK,CAChB;MAAA;IACF;IAEUG,eAAejB,CAAA,EAAoB;MAC3C,KAAKY,0BAAA,CAA2BZ,CAAA,CAAOkB,MAAM,GAC7C,IAAIH,UAAA,CAAW,KAAKN,MAAA,EAAQ,KAAKE,MAAM,EAAEK,GAAA,CAAIhB,CAAM,GACnD,KAAKW,MAAA,IAAUX,CAAA,CAAOkB,MACxB;IAAA;IAEQC,sBACNnB,CAAA,EACAc,CAAA,EACAM,CAAA,EACA;MACA,KAAKR,0BAAA,CAA2BE,CAAW;MAC3C,IAAMO,CAAA,GAAK,IAAIC,QAAA,CAAS,KAAKb,MAAA,EAAQ,KAAKE,MAAM;MAChDX,CAAA,CAAGuB,KAAA,CAAMF,CAAA,EAAI,CAAC,GAAGD,CAAA,EAAO,EAAI,CAAC,GAC7B,KAAKT,MAAA,IAAUG,CACjB;IAAA;IAkBAU,aAAaxB,CAAA,EAAe;MAC1B,IAAMc,CAAA,GAAc,IAAIW,WAAA;MACxB,KAAKC,cAAA,CAAeZ,CAAA,CAAYa,MAAA,CAAO3B,CAAK,CAAC,CAC/C;IAAA;IAQA0B,eAAe1B,CAAA,EAAmB;MAChC,KAAK4B,qBAAA,CAAsB5B,CAAA,CAAMkB,MAAM,GACvC,KAAKD,cAAA,CAAejB,CAAK,CAC3B;IAAA;IAQA6B,oBAAoB7B,CAAA,EAAmB;MACrC,KAAKiB,cAAA,CAAejB,CAAK,CAC3B;IAAA;IAOA8B,cAAc9B,CAAA,EAAgB;MAC5B+B,CAAA,CAAc/B,CAAK;MACnB,IAAMc,CAAA,GAAYd,CAAA,GAAQ,IAAI;MAC9B,KAAKiB,cAAA,CAAe,IAAIF,UAAA,CAAW,CAACD,CAAS,CAAC,CAAC,CACjD;IAAA;IAQAkB,YAAYhC,CAAA,EAAc;MACxB,KAAKiB,cAAA,CAAe,IAAIF,UAAA,CAAW,CAACf,CAAK,CAAC,CAAC,CAC7C;IAAA;IAcAiC,aAAajC,CAAA,EAAe;MAC1B,KAAKmB,qBAAA,CAAsBG,QAAA,CAASY,SAAA,CAAUC,SAAA,EAAW,GAAGnC,CAAK,CACnE;IAAA;IAcAoC,aAAapC,CAAA,EAAe;MAC1B,KAAKmB,qBAAA,CAAsBG,QAAA,CAASY,SAAA,CAAUG,SAAA,EAAW,GAAGrC,CAAK,CACnE;IAAA;IAcAsC,aAAatC,CAAA,EAAkB;MAC7B,IAAMc,CAAA,GAAMyB,MAAA,CAAOvC,CAAK,IAAIuC,MAAA,CAAOC,CAAc;QAC3CpB,CAAA,GAAOmB,MAAA,CAAOvC,CAAK,KAAKuC,MAAA,CAAO,EAAE;MAGvC,KAAKH,YAAA,CAAaK,MAAA,CAAO3B,CAAG,CAAC,GAC7B,KAAKsB,YAAA,CAAaK,MAAA,CAAOrB,CAAI,CAAC,CAChC;IAAA;IAQAsB,cAAc1C,CAAA,EAAkB;MAC9B,IAAMc,CAAA,GAAMyB,MAAA,CAAOvC,CAAK,IAAI2C,CAAA;QACtBvB,CAAA,GAAOmB,MAAA,CAAOvC,CAAK,KAAKuC,MAAA,CAAO,EAAE;MAGvC,KAAKD,YAAA,CAAaxB,CAAG,GACrB,KAAKwB,YAAA,CAAalB,CAAI,CACxB;IAAA;IAQAwB,cAAc5C,CAAA,EAAkB;MAC9B,IAAMc,CAAA,GAAMyB,MAAA,CAAOvC,CAAK,IAAI6C,CAAA;QACtBzB,CAAA,GAAOmB,MAAA,CAAOvC,CAAK,KAAKuC,MAAA,CAAO,GAAG;MAGxC,KAAKG,aAAA,CAAc5B,CAAG,GACtB,KAAK4B,aAAA,CAActB,CAAI,CACzB;IAAA;IAQAQ,sBAAsB5B,CAAA,EAAa;MACjC,IAAIc,CAAA,GAAQd,CAAA;QACNoB,CAAA,GAAa,EAAC;MACpB,OAAON,CAAA,KAAU,IACfM,CAAA,CAAW0B,IAAA,CAAMhC,CAAA,GAAQ,MAAQ,GAAI,GACrCA,CAAA,MAAW;MAEbM,CAAA,CAAW0B,IAAA,CAAKhC,CAAK,GACrB,KAAKG,cAAA,CAAe,IAAIF,UAAA,CAAWK,CAAU,CAAC,CAChD;IAAA;IAKAjB,aAAA,EAA2B;MACzB,OAAO,IAAIY,UAAA,CAAW,KAAKN,MAAM,EAAEsC,KAAA,CAAM,GAAG,KAAKpC,MAAM,CACzD;IAAA;IAqCAT,UAAkCF,CAAA,EAAgB;MAGhDA,CAAA,CAAME,SAAA,CAAU,IAAI,CACtB;IAAA;IAqBA8C,gBAAwChD,CAAA,EAAwB;MAC9D,KAAK4B,qBAAA,CAAsB5B,CAAA,CAAOkB,MAAM,GACxClB,CAAA,CAAOiD,OAAA,CAASnC,CAAA,IAAS;QACvBA,CAAA,CAAKZ,SAAA,CAAU,IAAI,CACrB;MAAA,CAAC,CACH;IAAA;IAoBAgD,gBAAwClD,CAAA,EAAiB;MACvD,IAAMc,CAAA,GAAWd,CAAA,KAAU;MAC3B,KAAK8B,aAAA,CAAchB,CAAQ,GACvBA,CAAA,IACFd,CAAA,CAAME,SAAA,CAAU,IAAI,CAExB;IAAA;IAwBAiD,mBAAmBnD,CAAA,EAAsB;MACnCA,CAAA,KAAU,SACZ,KAAK4B,qBAAA,CAAsB,CAAC,KAE5B,KAAKA,qBAAA,CAAsB,CAAC,GAC5B,KAAKJ,YAAA,CAAaxB,CAAK,EAE3B;IAAA;EACF;AA1OEoD,CAAA,EADCC,CAAA,CAAiB,GAAGC,CAAa,IAlGvBrD,CAAA,CAmGXiC,SAAA,qBAgBAkB,CAAA,EADCC,CAAA,CAAiB,GAAGE,CAAc,IAlHxBtD,CAAA,CAmHXiC,SAAA,sBAgBAkB,CAAA,EADCC,CAAA,CAAiB,GAAGb,CAAc,IAlIxBvC,CAAA,CAmIXiC,SAAA,sBAgBAkB,CAAA,EADCC,CAAA,CAAiBd,MAAA,CAAO,CAAC,GAAGI,CAAe,IAlJjC1C,CAAA,CAmJXiC,SAAA,sBAeAkB,CAAA,EADCC,CAAA,CAAiBd,MAAA,CAAO,CAAC,GAAGM,CAAgB,IAjKlC5C,CAAA,CAkKXiC,SAAA,uBAeAkB,CAAA,EADCC,CAAA,CAAiBd,MAAA,CAAO,CAAC,GAAGiB,CAAgB,IAhLlCvD,CAAA,CAiLXiC,SAAA,uBAeAkB,CAAA,EADCC,CAAA,CAAiB,GAAGb,CAAc,IA/LxBvC,CAAA,CAgMXiC,SAAA;AA+IK,SAASH,EAAc0B,CAAA,EAA0C;EACtE,IAAI,OAAOA,CAAA,IAAU,WACnB,MAAM,IAAIjD,KAAA,CAAM,GAAGiD,CAAK,yBAAyB,CAErD;AAAA;AAEO,IAAMC,CAAA,GAAyBC,CAACF,CAAA,EAAkBzD,CAAA,EAAgBc,CAAA,KACvE,GAAG2C,CAAK,sBAAsBzD,CAAG,KAAKc,CAAG;AAEpC,SAAS8C,EAA2CH,CAAA,EAAUzD,CAAA,EAAac,CAAA,EAAa;EAC7F,IAAMM,CAAA,GAAcmB,MAAA,CAAOkB,CAAK;EAChC,IAAIrC,CAAA,GAAcmB,MAAA,CAAOzB,CAAQ,KAAKM,CAAA,GAAcmB,MAAA,CAAOvC,CAAQ,GACjE,MAAM,IAAIQ,KAAA,CAAMkD,CAAA,CAAuBD,CAAA,EAAOzD,CAAA,EAAUc,CAAQ,CAAC,CAErE;AAAA;AAOA,SAASuC,EAAsCI,CAAA,EAAazD,CAAA,EAAa;EACvE,OAAO,CAACc,CAAA,EAAiBM,CAAA,EAAqBC,CAAA,KAAmC;IAC/E,IAAMwC,CAAA,GAAgBxC,CAAA,CAAWyC,KAAA;IAEjC,OAAAzC,CAAA,CAAWyC,KAAA,GAAQ,UAAcC,CAAA,EAAkB;MACjD,OAAAH,CAAA,CAAsBG,CAAA,EAAON,CAAA,EAAUzD,CAAQ,GACxC6D,CAAA,CAActC,KAAA,CAAM,MAAM,CAACwC,CAAK,CAAC,CAC1C;IAAA,GAEO1C,CACT;EAAA,CACF;AAAA;AAAA,SAAAvB,CAAA,IAAAuB,CAAA,EAAApB,CAAA,IAAAsD,CAAA,EAAAxB,CAAA,IAAA1B,CAAA,EAAAqD,CAAA,IAAAM,CAAA,EAAAJ,CAAA,IAAA5D,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}