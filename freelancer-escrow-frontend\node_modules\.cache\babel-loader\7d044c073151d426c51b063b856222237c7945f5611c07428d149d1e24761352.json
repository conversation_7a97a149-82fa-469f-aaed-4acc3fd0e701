{"ast": null, "code": "import { a as P } from \"./chunk-NSQLZBCA.mjs\";\nimport { c as d } from \"./chunk-K4CTCBLY.mjs\";\nimport { d as w, e as h, f as k, g as T, h as b, j as O, k as q, l as v } from \"./chunk-HIHKTLLM.mjs\";\nimport { a as f } from \"./chunk-5YHI7WOB.mjs\";\nimport { a as C } from \"./chunk-QVWBJJRF.mjs\";\nimport { b as y } from \"./chunk-6ZQWPHLV.mjs\";\nimport { b as l } from \"./chunk-A2Z7I2EY.mjs\";\nimport { a as g } from \"./chunk-77NXCSLY.mjs\";\nimport { b as c } from \"./chunk-BF46IXHH.mjs\";\nimport { a as _ } from \"./chunk-AH44UPM4.mjs\";\nimport { b as A, d as p } from \"./chunk-HHE63GFW.mjs\";\nimport { a as m } from \"./chunk-GHYE26Q5.mjs\";\nasync function S(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e\n    } = t,\n    {\n      data: o\n    } = await A({\n      aptosConfig: n,\n      originMethod: \"getInfo\",\n      path: `accounts/${c.from(e).toString()}`\n    });\n  return o;\n}\nasync function H(t) {\n  let {\n    aptosConfig: n,\n    accountAddress: e,\n    options: o\n  } = t;\n  return p({\n    aptosConfig: n,\n    originMethod: \"getModules\",\n    path: `accounts/${c.from(e).toString()}/modules`,\n    params: {\n      ledger_version: o?.ledgerVersion,\n      start: o?.offset,\n      limit: o?.limit ?? 1e3\n    }\n  });\n}\nasync function J(t) {\n  return t.options?.ledgerVersion !== void 0 ? G(t) : _(async () => G(t), `module-${t.accountAddress}-${t.moduleName}`, 1e3 * 60 * 5)();\n}\nasync function G(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      moduleName: o,\n      options: s\n    } = t,\n    {\n      data: r\n    } = await A({\n      aptosConfig: n,\n      originMethod: \"getModule\",\n      path: `accounts/${c.from(e).toString()}/module/${o}`,\n      params: {\n        ledger_version: s?.ledgerVersion\n      }\n    });\n  return r;\n}\nasync function U(t) {\n  let {\n    aptosConfig: n,\n    accountAddress: e,\n    options: o\n  } = t;\n  return p({\n    aptosConfig: n,\n    originMethod: \"getTransactions\",\n    path: `accounts/${c.from(e).toString()}/transactions`,\n    params: {\n      start: o?.offset,\n      limit: o?.limit\n    }\n  });\n}\nasync function X(t) {\n  let {\n    aptosConfig: n,\n    accountAddress: e,\n    options: o\n  } = t;\n  return p({\n    aptosConfig: n,\n    originMethod: \"getResources\",\n    path: `accounts/${c.from(e).toString()}/resources`,\n    params: {\n      ledger_version: o?.ledgerVersion,\n      start: o?.offset,\n      limit: o?.limit ?? 999\n    }\n  });\n}\nasync function K(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      resourceType: o,\n      options: s\n    } = t,\n    {\n      data: r\n    } = await A({\n      aptosConfig: n,\n      originMethod: \"getResource\",\n      path: `accounts/${c.from(e).toString()}/resource/${o}`,\n      params: {\n        ledger_version: s?.ledgerVersion\n      }\n    });\n  return r.data;\n}\nasync function Q(t) {\n  let {\n      aptosConfig: n,\n      authenticationKey: e,\n      options: o\n    } = t,\n    s = await K({\n      aptosConfig: n,\n      accountAddress: \"0x1\",\n      resourceType: \"0x1::account::OriginatingAddress\",\n      options: o\n    }),\n    {\n      address_map: {\n        handle: r\n      }\n    } = s,\n    a = c.from(e);\n  try {\n    let i = await P({\n      aptosConfig: n,\n      handle: r,\n      data: {\n        key: a.toString(),\n        key_type: \"address\",\n        value_type: \"address\"\n      },\n      options: o\n    });\n    return c.from(i);\n  } catch (i) {\n    if (i instanceof m && i.data.error_code === \"table_item_not_found\") return a;\n    throw i;\n  }\n}\nasync function Y(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e\n    } = t,\n    s = {\n      owner_address: {\n        _eq: c.from(e).toStringLong()\n      },\n      amount: {\n        _gt: 0\n      }\n    },\n    a = await d({\n      aptosConfig: n,\n      query: {\n        query: q,\n        variables: {\n          where_condition: s\n        }\n      },\n      originMethod: \"getAccountTokensCount\"\n    });\n  return a.current_token_ownerships_v2_aggregate.aggregate ? a.current_token_ownerships_v2_aggregate.aggregate.count : 0;\n}\nasync function Z(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      options: o\n    } = t,\n    r = {\n      owner_address: {\n        _eq: c.from(e).toStringLong()\n      },\n      amount: {\n        _gt: 0\n      }\n    };\n  o?.tokenStandard && (r.token_standard = {\n    _eq: o?.tokenStandard\n  });\n  let a = {\n    query: b,\n    variables: {\n      where_condition: r,\n      offset: o?.offset,\n      limit: o?.limit,\n      order_by: o?.orderBy\n    }\n  };\n  return (await d({\n    aptosConfig: n,\n    query: a,\n    originMethod: \"getAccountOwnedTokens\"\n  })).current_token_ownerships_v2;\n}\nasync function oo(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      collectionAddress: o,\n      options: s\n    } = t,\n    r = c.from(e).toStringLong(),\n    a = c.from(o).toStringLong(),\n    i = {\n      owner_address: {\n        _eq: r\n      },\n      current_token_data: {\n        collection_id: {\n          _eq: a\n        }\n      },\n      amount: {\n        _gt: 0\n      }\n    };\n  s?.tokenStandard && (i.token_standard = {\n    _eq: s?.tokenStandard\n  });\n  let u = {\n    query: O,\n    variables: {\n      where_condition: i,\n      offset: s?.offset,\n      limit: s?.limit,\n      order_by: s?.orderBy\n    }\n  };\n  return (await d({\n    aptosConfig: n,\n    query: u,\n    originMethod: \"getAccountOwnedTokensFromCollectionAddress\"\n  })).current_token_ownerships_v2;\n}\nasync function to(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      options: o\n    } = t,\n    r = {\n      owner_address: {\n        _eq: c.from(e).toStringLong()\n      },\n      amount: {\n        _gt: 0\n      }\n    };\n  o?.tokenStandard && (r.current_collection = {\n    token_standard: {\n      _eq: o?.tokenStandard\n    }\n  });\n  let a = {\n    query: k,\n    variables: {\n      where_condition: r,\n      offset: o?.offset,\n      limit: o?.limit,\n      order_by: o?.orderBy\n    }\n  };\n  return (await d({\n    aptosConfig: n,\n    query: a,\n    originMethod: \"getAccountCollectionsWithOwnedTokens\"\n  })).current_collection_ownership_v2_view;\n}\nasync function eo(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e\n    } = t,\n    o = c.from(e).toStringLong(),\n    r = await d({\n      aptosConfig: n,\n      query: {\n        query: v,\n        variables: {\n          address: o\n        }\n      },\n      originMethod: \"getAccountTransactionsCount\"\n    });\n  return r.account_transactions_aggregate.aggregate ? r.account_transactions_aggregate.aggregate.count : 0;\n}\nasync function no(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      coinType: o\n    } = t,\n    s = c.from(e).toStringLong(),\n    r = await I({\n      aptosConfig: n,\n      accountAddress: s,\n      options: {\n        where: {\n          asset_type: {\n            _eq: o\n          }\n        }\n      }\n    });\n  return r[0] ? r[0].amount : 0;\n}\nasync function I(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      options: o\n    } = t,\n    s = c.from(e).toStringLong(),\n    r = {\n      ...o?.where,\n      owner_address: {\n        _eq: s\n      }\n    },\n    a = {\n      query: h,\n      variables: {\n        where_condition: r,\n        offset: o?.offset,\n        limit: o?.limit,\n        order_by: o?.orderBy\n      }\n    };\n  return (await d({\n    aptosConfig: n,\n    query: a,\n    originMethod: \"getAccountCoinsData\"\n  })).current_fungible_asset_balances;\n}\nasync function ro(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e\n    } = t,\n    o = c.from(e).toStringLong(),\n    r = await d({\n      aptosConfig: n,\n      query: {\n        query: w,\n        variables: {\n          address: o\n        }\n      },\n      originMethod: \"getAccountCoinsCount\"\n    });\n  if (!r.current_fungible_asset_balances_aggregate.aggregate) throw Error(\"Failed to get the count of account coins\");\n  return r.current_fungible_asset_balances_aggregate.aggregate.count;\n}\nasync function so(t) {\n  let {\n      aptosConfig: n,\n      accountAddress: e,\n      options: o\n    } = t,\n    r = {\n      owner_address: {\n        _eq: c.from(e).toStringLong()\n      }\n    },\n    a = {\n      query: T,\n      variables: {\n        where_condition: r,\n        offset: o?.offset,\n        limit: o?.limit,\n        order_by: o?.orderBy\n      }\n    };\n  return (await d({\n    aptosConfig: n,\n    query: a,\n    originMethod: \"getAccountOwnedObjects\"\n  })).current_objects;\n}\nasync function co(t) {\n  let {\n      aptosConfig: n,\n      privateKey: e\n    } = t,\n    o = new C(e.publicKey());\n  if (e instanceof y) {\n    let r = g.fromPublicKey({\n      publicKey: o\n    }).derivedAddress();\n    return f.fromPrivateKey({\n      privateKey: e,\n      address: r\n    });\n  }\n  if (e instanceof l) {\n    let s = g.fromPublicKey({\n      publicKey: o\n    });\n    if (await M({\n      authKey: s,\n      aptosConfig: n\n    })) {\n      let u = s.derivedAddress();\n      return f.fromPrivateKey({\n        privateKey: e,\n        address: u,\n        legacy: !1\n      });\n    }\n    let a = g.fromPublicKey({\n      publicKey: o.publicKey\n    });\n    if (await M({\n      authKey: a,\n      aptosConfig: n\n    })) {\n      let u = a.derivedAddress();\n      return f.fromPrivateKey({\n        privateKey: e,\n        address: u,\n        legacy: !0\n      });\n    }\n  }\n  throw new Error(`Can't derive account from private key ${e}`);\n}\nasync function M(t) {\n  let {\n      aptosConfig: n,\n      authKey: e\n    } = t,\n    o = await Q({\n      aptosConfig: n,\n      authenticationKey: e.derivedAddress()\n    });\n  try {\n    return await S({\n      aptosConfig: n,\n      accountAddress: o\n    }), !0;\n  } catch (s) {\n    if (s.status === 404) return !1;\n    throw new Error(`Error while looking for an account info ${o.toString()}`);\n  }\n}\nexport { S as a, H as b, J as c, U as d, X as e, K as f, Q as g, Y as h, Z as i, oo as j, to as k, eo as l, no as m, I as n, ro as o, so as p, co as q, M as r };", "map": {"version": 3, "names": ["S", "t", "aptosConfig", "n", "accountAddress", "e", "data", "o", "A", "originMethod", "path", "c", "from", "toString", "H", "options", "p", "params", "ledger_version", "ledgerVersion", "start", "offset", "limit", "J", "G", "_", "moduleName", "s", "r", "U", "X", "K", "resourceType", "Q", "authenticationKey", "address_map", "handle", "a", "i", "P", "key", "key_type", "value_type", "m", "error_code", "Y", "owner_address", "_eq", "toStringLong", "amount", "_gt", "d", "query", "q", "variables", "where_condition", "current_token_ownerships_v2_aggregate", "aggregate", "count", "Z", "tokenStandard", "token_standard", "b", "order_by", "orderBy", "current_token_ownerships_v2", "oo", "collection<PERSON>dd<PERSON>", "current_token_data", "collection_id", "u", "O", "to", "current_collection", "k", "current_collection_ownership_v2_view", "eo", "v", "address", "account_transactions_aggregate", "no", "coinType", "I", "where", "asset_type", "h", "current_fungible_asset_balances", "ro", "w", "current_fungible_asset_balances_aggregate", "Error", "so", "T", "current_objects", "co", "privateKey", "C", "public<PERSON>ey", "y", "g", "fromPublicKey", "derivedAddress", "f", "fromPrivateKey", "l", "M", "auth<PERSON><PERSON>", "legacy", "status", "j"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\internal\\account.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This file contains the underlying implementations for exposed API surface in\n * the {@link api/account}. By moving the methods out into a separate file,\n * other namespaces and processes can access these methods without depending on the entire\n * account namespace and without having a dependency cycle error.\n */\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { AptosApiError, getAptosFullNode, paginateWithCursor } from \"../client\";\nimport { AccountAddress, AccountAddressInput } from \"../core/accountAddress\";\nimport { Account } from \"../account\";\nimport { AnyPub<PERSON><PERSON><PERSON>, Ed25519PublicKey, PrivateKey } from \"../core/crypto\";\nimport { queryIndexer } from \"./general\";\nimport {\n  AccountData,\n  GetAccountCoinsDataResponse,\n  GetAccountCollectionsWithOwnedTokenResponse,\n  GetAccountOwnedObjectsResponse,\n  GetAccountOwnedTokensFromCollectionResponse,\n  GetAccountOwnedTokensQueryResponse,\n  LedgerVersionArg,\n  MoveModuleBytecode,\n  MoveResource,\n  MoveStructId,\n  OrderByArg,\n  PaginationArgs,\n  TokenStandardArg,\n  TransactionResponse,\n  WhereArg,\n} from \"../types\";\nimport {\n  GetAccountCoinsCountQuery,\n  GetAccountCoinsDataQuery,\n  GetAccountCollectionsWithOwnedTokensQuery,\n  GetAccountOwnedObjectsQuery,\n  GetAccountOwnedTokensFromCollectionQuery,\n  GetAccountOwnedTokensQuery,\n  GetAccountTokensCountQuery,\n  GetAccountTransactionsCountQuery,\n} from \"../types/generated/operations\";\nimport {\n  GetAccountCoinsCount,\n  GetAccountCoinsData,\n  GetAccountCollectionsWithOwnedTokens,\n  GetAccountOwnedObjects,\n  GetAccountOwnedTokens,\n  GetAccountOwnedTokensFromCollection,\n  GetAccountTokensCount,\n  GetAccountTransactionsCount,\n} from \"../types/generated/queries\";\nimport { memoizeAsync } from \"../utils/memoize\";\nimport { Secp256k1PrivateKey, AuthenticationKey, Ed25519PrivateKey } from \"../core\";\nimport { CurrentFungibleAssetBalancesBoolExp } from \"../types/generated/types\";\nimport { getTableItem } from \"./table\";\n\nexport async function getInfo(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n}): Promise<AccountData> {\n  const { aptosConfig, accountAddress } = args;\n  const { data } = await getAptosFullNode<{}, AccountData>({\n    aptosConfig,\n    originMethod: \"getInfo\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}`,\n  });\n  return data;\n}\n\nexport async function getModules(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: PaginationArgs & LedgerVersionArg;\n}): Promise<MoveModuleBytecode[]> {\n  const { aptosConfig, accountAddress, options } = args;\n  return paginateWithCursor<{}, MoveModuleBytecode[]>({\n    aptosConfig,\n    originMethod: \"getModules\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}/modules`,\n    params: {\n      ledger_version: options?.ledgerVersion,\n      start: options?.offset,\n      limit: options?.limit ?? 1000,\n    },\n  });\n}\n\n/**\n * Queries for a move module given account address and module name\n *\n * @param args.accountAddress Hex-encoded 32 byte Aptos account address\n * @param args.moduleName The name of the module\n * @param args.query.ledgerVersion Specifies ledger version of transactions. By default, latest version will be used\n * @returns The move module.\n */\nexport async function getModule(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  moduleName: string;\n  options?: LedgerVersionArg;\n}): Promise<MoveModuleBytecode> {\n  // We don't memoize the account module by ledger version, as it's not a common use case, this would be handled\n  // by the developer directly\n  if (args.options?.ledgerVersion !== undefined) {\n    return getModuleInner(args);\n  }\n\n  return memoizeAsync(\n    async () => getModuleInner(args),\n    `module-${args.accountAddress}-${args.moduleName}`,\n    1000 * 60 * 5, // 5 minutes\n  )();\n}\n\nasync function getModuleInner(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  moduleName: string;\n  options?: LedgerVersionArg;\n}): Promise<MoveModuleBytecode> {\n  const { aptosConfig, accountAddress, moduleName, options } = args;\n\n  const { data } = await getAptosFullNode<{}, MoveModuleBytecode>({\n    aptosConfig,\n    originMethod: \"getModule\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}/module/${moduleName}`,\n    params: { ledger_version: options?.ledgerVersion },\n  });\n  return data;\n}\n\nexport async function getTransactions(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: PaginationArgs;\n}): Promise<TransactionResponse[]> {\n  const { aptosConfig, accountAddress, options } = args;\n  return paginateWithCursor<{}, TransactionResponse[]>({\n    aptosConfig,\n    originMethod: \"getTransactions\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}/transactions`,\n    params: { start: options?.offset, limit: options?.limit },\n  });\n}\n\nexport async function getResources(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: PaginationArgs & LedgerVersionArg;\n}): Promise<MoveResource[]> {\n  const { aptosConfig, accountAddress, options } = args;\n  return paginateWithCursor<{}, MoveResource[]>({\n    aptosConfig,\n    originMethod: \"getResources\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}/resources`,\n    params: {\n      ledger_version: options?.ledgerVersion,\n      start: options?.offset,\n      limit: options?.limit ?? 999,\n    },\n  });\n}\n\nexport async function getResource<T extends {}>(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  resourceType: MoveStructId;\n  options?: LedgerVersionArg;\n}): Promise<T> {\n  const { aptosConfig, accountAddress, resourceType, options } = args;\n  const { data } = await getAptosFullNode<{}, MoveResource>({\n    aptosConfig,\n    originMethod: \"getResource\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}/resource/${resourceType}`,\n    params: { ledger_version: options?.ledgerVersion },\n  });\n  return data.data as T;\n}\n\nexport async function lookupOriginalAccountAddress(args: {\n  aptosConfig: AptosConfig;\n  authenticationKey: AccountAddressInput;\n  options?: LedgerVersionArg;\n}): Promise<AccountAddress> {\n  const { aptosConfig, authenticationKey, options } = args;\n  type OriginatingAddress = {\n    address_map: { handle: string };\n  };\n  const resource = await getResource<OriginatingAddress>({\n    aptosConfig,\n    accountAddress: \"0x1\",\n    resourceType: \"0x1::account::OriginatingAddress\",\n    options,\n  });\n\n  const {\n    address_map: { handle },\n  } = resource;\n\n  const authKeyAddress = AccountAddress.from(authenticationKey);\n\n  // If the address is not found in the address map, which means its not rotated\n  // then return the address as is\n  try {\n    const originalAddress = await getTableItem<string>({\n      aptosConfig,\n      handle,\n      data: {\n        key: authKeyAddress.toString(),\n        key_type: \"address\",\n        value_type: \"address\",\n      },\n      options,\n    });\n\n    return AccountAddress.from(originalAddress);\n  } catch (err) {\n    if (err instanceof AptosApiError && err.data.error_code === \"table_item_not_found\") {\n      return authKeyAddress;\n    }\n\n    throw err;\n  }\n}\n\nexport async function getAccountTokensCount(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n}): Promise<number> {\n  const { aptosConfig, accountAddress } = args;\n\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const whereCondition: { owner_address: { _eq: string }; amount: { _gt: number } } = {\n    owner_address: { _eq: address },\n    amount: { _gt: 0 },\n  };\n\n  const graphqlQuery = {\n    query: GetAccountTokensCount,\n    variables: { where_condition: whereCondition },\n  };\n\n  const data = await queryIndexer<GetAccountTokensCountQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountTokensCount\",\n  });\n\n  // commonjs (aka cjs) doesnt handle Nullish Coalescing for some reason\n  // might be because of how ts infer the graphql generated scheme type\n  return data.current_token_ownerships_v2_aggregate.aggregate\n    ? data.current_token_ownerships_v2_aggregate.aggregate.count\n    : 0;\n}\n\nexport async function getAccountOwnedTokens(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountOwnedTokensQueryResponse[0]>;\n}): Promise<GetAccountOwnedTokensQueryResponse> {\n  const { aptosConfig, accountAddress, options } = args;\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const whereCondition: { owner_address: { _eq: string }; amount: { _gt: number }; token_standard?: { _eq: string } } =\n    {\n      owner_address: { _eq: address },\n      amount: { _gt: 0 },\n    };\n\n  if (options?.tokenStandard) {\n    whereCondition.token_standard = { _eq: options?.tokenStandard };\n  }\n\n  const graphqlQuery = {\n    query: GetAccountOwnedTokens,\n    variables: {\n      where_condition: whereCondition,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n\n  const data = await queryIndexer<GetAccountOwnedTokensQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountOwnedTokens\",\n  });\n\n  return data.current_token_ownerships_v2;\n}\n\nexport async function getAccountOwnedTokensFromCollectionAddress(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  collectionAddress: AccountAddressInput;\n  options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountOwnedTokensFromCollectionResponse[0]>;\n}): Promise<GetAccountOwnedTokensFromCollectionResponse> {\n  const { aptosConfig, accountAddress, collectionAddress, options } = args;\n  const ownerAddress = AccountAddress.from(accountAddress).toStringLong();\n  const collAddress = AccountAddress.from(collectionAddress).toStringLong();\n\n  const whereCondition: {\n    owner_address: { _eq: string };\n    current_token_data: { collection_id: { _eq: string } };\n    amount: { _gt: number };\n    token_standard?: { _eq: string };\n  } = {\n    owner_address: { _eq: ownerAddress },\n    current_token_data: { collection_id: { _eq: collAddress } },\n    amount: { _gt: 0 },\n  };\n\n  if (options?.tokenStandard) {\n    whereCondition.token_standard = { _eq: options?.tokenStandard };\n  }\n\n  const graphqlQuery = {\n    query: GetAccountOwnedTokensFromCollection,\n    variables: {\n      where_condition: whereCondition,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n\n  const data = await queryIndexer<GetAccountOwnedTokensFromCollectionQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountOwnedTokensFromCollectionAddress\",\n  });\n\n  return data.current_token_ownerships_v2;\n}\n\nexport async function getAccountCollectionsWithOwnedTokens(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountCollectionsWithOwnedTokenResponse[0]>;\n}): Promise<GetAccountCollectionsWithOwnedTokenResponse> {\n  const { aptosConfig, accountAddress, options } = args;\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const whereCondition: {\n    owner_address: { _eq: string };\n    amount: { _gt: number };\n    current_collection?: { token_standard: { _eq: string } };\n  } = {\n    owner_address: { _eq: address },\n    amount: { _gt: 0 },\n  };\n\n  if (options?.tokenStandard) {\n    whereCondition.current_collection = {\n      token_standard: { _eq: options?.tokenStandard },\n    };\n  }\n\n  const graphqlQuery = {\n    query: GetAccountCollectionsWithOwnedTokens,\n    variables: {\n      where_condition: whereCondition,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n\n  const data = await queryIndexer<GetAccountCollectionsWithOwnedTokensQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountCollectionsWithOwnedTokens\",\n  });\n\n  return data.current_collection_ownership_v2_view;\n}\n\nexport async function getAccountTransactionsCount(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n}): Promise<number> {\n  const { aptosConfig, accountAddress } = args;\n\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const graphqlQuery = {\n    query: GetAccountTransactionsCount,\n    variables: { address },\n  };\n\n  const data = await queryIndexer<GetAccountTransactionsCountQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountTransactionsCount\",\n  });\n\n  // commonjs (aka cjs) doesnt handle Nullish Coalescing for some reason\n  // might be because of how ts infer the graphql generated scheme type\n  return data.account_transactions_aggregate.aggregate ? data.account_transactions_aggregate.aggregate.count : 0;\n}\n\nexport async function getAccountCoinAmount(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  coinType: MoveStructId;\n}): Promise<number> {\n  const { aptosConfig, accountAddress, coinType } = args;\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const data = await getAccountCoinsData({\n    aptosConfig,\n    accountAddress: address,\n    options: {\n      where: { asset_type: { _eq: coinType } },\n    },\n  });\n\n  // commonjs (aka cjs) doesnt handle Nullish Coalescing for some reason\n  // might be because of how ts infer the graphql generated scheme type\n  return data[0] ? data[0].amount : 0;\n}\n\nexport async function getAccountCoinsData(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: PaginationArgs & OrderByArg<GetAccountCoinsDataResponse[0]> & WhereArg<CurrentFungibleAssetBalancesBoolExp>;\n}): Promise<GetAccountCoinsDataResponse> {\n  const { aptosConfig, accountAddress, options } = args;\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const whereCondition: { owner_address: { _eq: string } } = {\n    ...options?.where,\n    owner_address: { _eq: address },\n  };\n\n  const graphqlQuery = {\n    query: GetAccountCoinsData,\n    variables: {\n      where_condition: whereCondition,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n\n  const data = await queryIndexer<GetAccountCoinsDataQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountCoinsData\",\n  });\n\n  return data.current_fungible_asset_balances;\n}\n\nexport async function getAccountCoinsCount(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n}): Promise<number> {\n  const { aptosConfig, accountAddress } = args;\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const graphqlQuery = {\n    query: GetAccountCoinsCount,\n    variables: { address },\n  };\n\n  const data = await queryIndexer<GetAccountCoinsCountQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountCoinsCount\",\n  });\n\n  if (!data.current_fungible_asset_balances_aggregate.aggregate) {\n    throw Error(\"Failed to get the count of account coins\");\n  }\n\n  return data.current_fungible_asset_balances_aggregate.aggregate.count;\n}\n\nexport async function getAccountOwnedObjects(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  options?: PaginationArgs & OrderByArg<GetAccountOwnedObjectsResponse[0]>;\n}): Promise<GetAccountOwnedObjectsResponse> {\n  const { aptosConfig, accountAddress, options } = args;\n  const address = AccountAddress.from(accountAddress).toStringLong();\n\n  const whereCondition: { owner_address: { _eq: string } } = {\n    owner_address: { _eq: address },\n  };\n  const graphqlQuery = {\n    query: GetAccountOwnedObjects,\n    variables: {\n      where_condition: whereCondition,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n  const data = await queryIndexer<GetAccountOwnedObjectsQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getAccountOwnedObjects\",\n  });\n\n  return data.current_objects;\n}\n\n/**\n * NOTE: There is a potential issue once unified single signer scheme will be adopted\n * by the community.\n *\n * Becuase on could create 2 accounts with the same private key with this new authenticator type,\n * we’ll need to determine the order in which we lookup the accounts. First unified\n * scheme and then legacy scheme vs first legacy scheme and then unified scheme.\n *\n */\nexport async function deriveAccountFromPrivateKey(args: {\n  aptosConfig: AptosConfig;\n  privateKey: PrivateKey;\n}): Promise<Account> {\n  const { aptosConfig, privateKey } = args;\n  const publicKey = new AnyPublicKey(privateKey.publicKey());\n\n  if (privateKey instanceof Secp256k1PrivateKey) {\n    // private key is secp256k1, therefore we know it for sure uses a single signer key\n    const authKey = AuthenticationKey.fromPublicKey({ publicKey });\n    const address = authKey.derivedAddress();\n    return Account.fromPrivateKey({ privateKey, address });\n  }\n\n  if (privateKey instanceof Ed25519PrivateKey) {\n    // lookup single sender ed25519\n    const singleSenderTransactionAuthenticatorAuthKey = AuthenticationKey.fromPublicKey({\n      publicKey,\n    });\n    const isSingleSenderTransactionAuthenticator = await isAccountExist({\n      authKey: singleSenderTransactionAuthenticatorAuthKey,\n      aptosConfig,\n    });\n    if (isSingleSenderTransactionAuthenticator) {\n      const address = singleSenderTransactionAuthenticatorAuthKey.derivedAddress();\n      return Account.fromPrivateKey({ privateKey, address, legacy: false });\n    }\n    // lookup legacy ed25519\n    const legacyAuthKey = AuthenticationKey.fromPublicKey({\n      publicKey: publicKey.publicKey as Ed25519PublicKey,\n    });\n    const isLegacyEd25519 = await isAccountExist({ authKey: legacyAuthKey, aptosConfig });\n    if (isLegacyEd25519) {\n      const address = legacyAuthKey.derivedAddress();\n      return Account.fromPrivateKey({ privateKey, address, legacy: true });\n    }\n  }\n  // if we are here, it means we couldn't find an address with an\n  // auth key that matches the provided private key\n  throw new Error(`Can't derive account from private key ${privateKey}`);\n}\n\nexport async function isAccountExist(args: { aptosConfig: AptosConfig; authKey: AuthenticationKey }): Promise<boolean> {\n  const { aptosConfig, authKey } = args;\n  const accountAddress = await lookupOriginalAccountAddress({\n    aptosConfig,\n    authenticationKey: authKey.derivedAddress(),\n  });\n\n  try {\n    await getInfo({\n      aptosConfig,\n      accountAddress,\n    });\n    return true;\n  } catch (error: any) {\n    // account not found\n    if (error.status === 404) {\n      return false;\n    }\n    throw new Error(`Error while looking for an account info ${accountAddress.toString()}`);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AA0DA,eAAsBA,EAAQC,CAAA,EAGL;EACvB,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC;IAAe,IAAIJ,CAAA;IAClC;MAAEK,IAAA,EAAAC;IAAK,IAAI,MAAMC,CAAA,CAAkC;MACvDN,WAAA,EAAAC,CAAA;MACAM,YAAA,EAAc;MACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAEQ,QAAA,CAAS,CAAC;IAClE,CAAC;EACD,OAAON,CACT;AAAA;AAEA,eAAsBO,EAAWb,CAAA,EAIC;EAChC,IAAM;IAAEC,WAAA,EAAAC,CAAA;IAAaC,cAAA,EAAAC,CAAA;IAAgBU,OAAA,EAAAR;EAAQ,IAAIN,CAAA;EACjD,OAAOe,CAAA,CAA6C;IAClDd,WAAA,EAAAC,CAAA;IACAM,YAAA,EAAc;IACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAEQ,QAAA,CAAS,CAAC;IAChEI,MAAA,EAAQ;MACNC,cAAA,EAAgBX,CAAA,EAASY,aAAA;MACzBC,KAAA,EAAOb,CAAA,EAASc,MAAA;MAChBC,KAAA,EAAOf,CAAA,EAASe,KAAA,IAAS;IAC3B;EACF,CAAC,CACH;AAAA;AAUA,eAAsBC,EAAUtB,CAAA,EAKA;EAG9B,OAAIA,CAAA,CAAKc,OAAA,EAASI,aAAA,KAAkB,SAC3BK,CAAA,CAAevB,CAAI,IAGrBwB,CAAA,CACL,YAAYD,CAAA,CAAevB,CAAI,GAC/B,UAAUA,CAAA,CAAKG,cAAc,IAAIH,CAAA,CAAKyB,UAAU,IAChD,MAAO,KAAK,CACd,EAAE,CACJ;AAAA;AAEA,eAAeF,EAAevB,CAAA,EAKE;EAC9B,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgBqB,UAAA,EAAAnB,CAAA;MAAYQ,OAAA,EAAAY;IAAQ,IAAI1B,CAAA;IAEvD;MAAEK,IAAA,EAAAsB;IAAK,IAAI,MAAMpB,CAAA,CAAyC;MAC9DN,WAAA,EAAAC,CAAA;MACAM,YAAA,EAAc;MACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAEQ,QAAA,CAAS,CAAC,WAAWN,CAAU;MACrFU,MAAA,EAAQ;QAAEC,cAAA,EAAgBS,CAAA,EAASR;MAAc;IACnD,CAAC;EACD,OAAOS,CACT;AAAA;AAEA,eAAsBC,EAAgB5B,CAAA,EAIH;EACjC,IAAM;IAAEC,WAAA,EAAAC,CAAA;IAAaC,cAAA,EAAAC,CAAA;IAAgBU,OAAA,EAAAR;EAAQ,IAAIN,CAAA;EACjD,OAAOe,CAAA,CAA8C;IACnDd,WAAA,EAAAC,CAAA;IACAM,YAAA,EAAc;IACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAEQ,QAAA,CAAS,CAAC;IAChEI,MAAA,EAAQ;MAAEG,KAAA,EAAOb,CAAA,EAASc,MAAA;MAAQC,KAAA,EAAOf,CAAA,EAASe;IAAM;EAC1D,CAAC,CACH;AAAA;AAEA,eAAsBQ,EAAa7B,CAAA,EAIP;EAC1B,IAAM;IAAEC,WAAA,EAAAC,CAAA;IAAaC,cAAA,EAAAC,CAAA;IAAgBU,OAAA,EAAAR;EAAQ,IAAIN,CAAA;EACjD,OAAOe,CAAA,CAAuC;IAC5Cd,WAAA,EAAAC,CAAA;IACAM,YAAA,EAAc;IACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAEQ,QAAA,CAAS,CAAC;IAChEI,MAAA,EAAQ;MACNC,cAAA,EAAgBX,CAAA,EAASY,aAAA;MACzBC,KAAA,EAAOb,CAAA,EAASc,MAAA;MAChBC,KAAA,EAAOf,CAAA,EAASe,KAAA,IAAS;IAC3B;EACF,CAAC,CACH;AAAA;AAEA,eAAsBS,EAA0B9B,CAAA,EAKjC;EACb,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgB2B,YAAA,EAAAzB,CAAA;MAAcQ,OAAA,EAAAY;IAAQ,IAAI1B,CAAA;IACzD;MAAEK,IAAA,EAAAsB;IAAK,IAAI,MAAMpB,CAAA,CAAmC;MACxDN,WAAA,EAAAC,CAAA;MACAM,YAAA,EAAc;MACdC,IAAA,EAAM,YAAYC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAEQ,QAAA,CAAS,CAAC,aAAaN,CAAY;MACzFU,MAAA,EAAQ;QAAEC,cAAA,EAAgBS,CAAA,EAASR;MAAc;IACnD,CAAC;EACD,OAAOS,CAAA,CAAKtB,IACd;AAAA;AAEA,eAAsB2B,EAA6BhC,CAAA,EAIvB;EAC1B,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAa+B,iBAAA,EAAA7B,CAAA;MAAmBU,OAAA,EAAAR;IAAQ,IAAIN,CAAA;IAI9C0B,CAAA,GAAW,MAAMI,CAAA,CAAgC;MACrD7B,WAAA,EAAAC,CAAA;MACAC,cAAA,EAAgB;MAChB4B,YAAA,EAAc;MACdjB,OAAA,EAAAR;IACF,CAAC;IAEK;MACJ4B,WAAA,EAAa;QAAEC,MAAA,EAAAR;MAAO;IACxB,IAAID,CAAA;IAEEU,CAAA,GAAiB1B,CAAA,CAAeC,IAAA,CAAKP,CAAiB;EAI5D,IAAI;IACF,IAAMiC,CAAA,GAAkB,MAAMC,CAAA,CAAqB;MACjDrC,WAAA,EAAAC,CAAA;MACAiC,MAAA,EAAAR,CAAA;MACAtB,IAAA,EAAM;QACJkC,GAAA,EAAKH,CAAA,CAAexB,QAAA,CAAS;QAC7B4B,QAAA,EAAU;QACVC,UAAA,EAAY;MACd;MACA3B,OAAA,EAAAR;IACF,CAAC;IAED,OAAOI,CAAA,CAAeC,IAAA,CAAK0B,CAAe,CAC5C;EAAA,SAASA,CAAA,EAAK;IACZ,IAAIA,CAAA,YAAeK,CAAA,IAAiBL,CAAA,CAAIhC,IAAA,CAAKsC,UAAA,KAAe,wBAC1D,OAAOP,CAAA;IAGT,MAAMC,CACR;EAAA;AACF;AAEA,eAAsBO,EAAsB5C,CAAA,EAGxB;EAClB,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC;IAAe,IAAIJ,CAAA;IAIlC0B,CAAA,GAA8E;MAClFmB,aAAA,EAAe;QAAEC,GAAA,EAHHpC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;MAGjC;MAC9BC,MAAA,EAAQ;QAAEC,GAAA,EAAK;MAAE;IACnB;IAOMb,CAAA,GAAO,MAAMc,CAAA,CAAyC;MAC1DjD,WAAA,EAAAC,CAAA;MACAiD,KAAA,EAPmB;QACnBA,KAAA,EAAOC,CAAA;QACPC,SAAA,EAAW;UAAEC,eAAA,EAAiB5B;QAAe;MAC/C;MAKElB,YAAA,EAAc;IAChB,CAAC;EAID,OAAO4B,CAAA,CAAKmB,qCAAA,CAAsCC,SAAA,GAC9CpB,CAAA,CAAKmB,qCAAA,CAAsCC,SAAA,CAAUC,KAAA,GACrD,CACN;AAAA;AAEA,eAAsBC,EAAsB1D,CAAA,EAII;EAC9C,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgBU,OAAA,EAAAR;IAAQ,IAAIN,CAAA;IAG3C2B,CAAA,GACJ;MACEkB,aAAA,EAAe;QAAEC,GAAA,EAJLpC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;MAI/B;MAC9BC,MAAA,EAAQ;QAAEC,GAAA,EAAK;MAAE;IACnB;EAEE3C,CAAA,EAASqD,aAAA,KACXhC,CAAA,CAAeiC,cAAA,GAAiB;IAAEd,GAAA,EAAKxC,CAAA,EAASqD;EAAc;EAGhE,IAAMvB,CAAA,GAAe;IACnBe,KAAA,EAAOU,CAAA;IACPR,SAAA,EAAW;MACTC,eAAA,EAAiB3B,CAAA;MACjBP,MAAA,EAAQd,CAAA,EAASc,MAAA;MACjBC,KAAA,EAAOf,CAAA,EAASe,KAAA;MAChByC,QAAA,EAAUxD,CAAA,EAASyD;IACrB;EACF;EAQA,QANa,MAAMb,CAAA,CAAyC;IAC1DjD,WAAA,EAAAC,CAAA;IACAiD,KAAA,EAAOf,CAAA;IACP5B,YAAA,EAAc;EAChB,CAAC,GAEWwD,2BACd;AAAA;AAEA,eAAsBC,GAA2CjE,CAAA,EAKR;EACvD,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgB8D,iBAAA,EAAA5D,CAAA;MAAmBQ,OAAA,EAAAY;IAAQ,IAAI1B,CAAA;IAC9D2B,CAAA,GAAejB,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;IAChEX,CAAA,GAAc1B,CAAA,CAAeC,IAAA,CAAKL,CAAiB,EAAEyC,YAAA,CAAa;IAElEV,CAAA,GAKF;MACFQ,aAAA,EAAe;QAAEC,GAAA,EAAKnB;MAAa;MACnCwC,kBAAA,EAAoB;QAAEC,aAAA,EAAe;UAAEtB,GAAA,EAAKV;QAAY;MAAE;MAC1DY,MAAA,EAAQ;QAAEC,GAAA,EAAK;MAAE;IACnB;EAEIvB,CAAA,EAASiC,aAAA,KACXtB,CAAA,CAAeuB,cAAA,GAAiB;IAAEd,GAAA,EAAKpB,CAAA,EAASiC;EAAc;EAGhE,IAAMU,CAAA,GAAe;IACnBlB,KAAA,EAAOmB,CAAA;IACPjB,SAAA,EAAW;MACTC,eAAA,EAAiBjB,CAAA;MACjBjB,MAAA,EAAQM,CAAA,EAASN,MAAA;MACjBC,KAAA,EAAOK,CAAA,EAASL,KAAA;MAChByC,QAAA,EAAUpC,CAAA,EAASqC;IACrB;EACF;EAQA,QANa,MAAMb,CAAA,CAAuD;IACxEjD,WAAA,EAAAC,CAAA;IACAiD,KAAA,EAAOkB,CAAA;IACP7D,YAAA,EAAc;EAChB,CAAC,GAEWwD,2BACd;AAAA;AAEA,eAAsBO,GAAqCvE,CAAA,EAIF;EACvD,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgBU,OAAA,EAAAR;IAAQ,IAAIN,CAAA;IAG3C2B,CAAA,GAIF;MACFkB,aAAA,EAAe;QAAEC,GAAA,EAPHpC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;MAOjC;MAC9BC,MAAA,EAAQ;QAAEC,GAAA,EAAK;MAAE;IACnB;EAEI3C,CAAA,EAASqD,aAAA,KACXhC,CAAA,CAAe6C,kBAAA,GAAqB;IAClCZ,cAAA,EAAgB;MAAEd,GAAA,EAAKxC,CAAA,EAASqD;IAAc;EAChD;EAGF,IAAMvB,CAAA,GAAe;IACnBe,KAAA,EAAOsB,CAAA;IACPpB,SAAA,EAAW;MACTC,eAAA,EAAiB3B,CAAA;MACjBP,MAAA,EAAQd,CAAA,EAASc,MAAA;MACjBC,KAAA,EAAOf,CAAA,EAASe,KAAA;MAChByC,QAAA,EAAUxD,CAAA,EAASyD;IACrB;EACF;EAQA,QANa,MAAMb,CAAA,CAAwD;IACzEjD,WAAA,EAAAC,CAAA;IACAiD,KAAA,EAAOf,CAAA;IACP5B,YAAA,EAAc;EAChB,CAAC,GAEWkE,oCACd;AAAA;AAEA,eAAsBC,GAA4B3E,CAAA,EAG9B;EAClB,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC;IAAe,IAAIJ,CAAA;IAElCM,CAAA,GAAUI,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;IAO3DpB,CAAA,GAAO,MAAMuB,CAAA,CAA+C;MAChEjD,WAAA,EAAAC,CAAA;MACAiD,KAAA,EAPmB;QACnBA,KAAA,EAAOyB,CAAA;QACPvB,SAAA,EAAW;UAAEwB,OAAA,EAAAvE;QAAQ;MACvB;MAKEE,YAAA,EAAc;IAChB,CAAC;EAID,OAAOmB,CAAA,CAAKmD,8BAAA,CAA+BtB,SAAA,GAAY7B,CAAA,CAAKmD,8BAAA,CAA+BtB,SAAA,CAAUC,KAAA,GAAQ,CAC/G;AAAA;AAEA,eAAsBsB,GAAqB/E,CAAA,EAIvB;EAClB,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgB4E,QAAA,EAAA1E;IAAS,IAAIN,CAAA;IAC5C0B,CAAA,GAAUhB,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;IAE3DpB,CAAA,GAAO,MAAMsD,CAAA,CAAoB;MACrChF,WAAA,EAAAC,CAAA;MACAC,cAAA,EAAgBuB,CAAA;MAChBZ,OAAA,EAAS;QACPoE,KAAA,EAAO;UAAEC,UAAA,EAAY;YAAErC,GAAA,EAAKxC;UAAS;QAAE;MACzC;IACF,CAAC;EAID,OAAOqB,CAAA,CAAK,CAAC,IAAIA,CAAA,CAAK,CAAC,EAAEqB,MAAA,GAAS,CACpC;AAAA;AAEA,eAAsBiC,EAAoBjF,CAAA,EAID;EACvC,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgBU,OAAA,EAAAR;IAAQ,IAAIN,CAAA;IAC3C0B,CAAA,GAAUhB,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;IAE3DpB,CAAA,GAAqD;MACzD,GAAGrB,CAAA,EAAS4E,KAAA;MACZrC,aAAA,EAAe;QAAEC,GAAA,EAAKpB;MAAQ;IAChC;IAEMU,CAAA,GAAe;MACnBe,KAAA,EAAOiC,CAAA;MACP/B,SAAA,EAAW;QACTC,eAAA,EAAiB3B,CAAA;QACjBP,MAAA,EAAQd,CAAA,EAASc,MAAA;QACjBC,KAAA,EAAOf,CAAA,EAASe,KAAA;QAChByC,QAAA,EAAUxD,CAAA,EAASyD;MACrB;IACF;EAQA,QANa,MAAMb,CAAA,CAAuC;IACxDjD,WAAA,EAAAC,CAAA;IACAiD,KAAA,EAAOf,CAAA;IACP5B,YAAA,EAAc;EAChB,CAAC,GAEW6E,+BACd;AAAA;AAEA,eAAsBC,GAAqBtF,CAAA,EAGvB;EAClB,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC;IAAe,IAAIJ,CAAA;IAClCM,CAAA,GAAUI,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;IAO3DpB,CAAA,GAAO,MAAMuB,CAAA,CAAwC;MACzDjD,WAAA,EAAAC,CAAA;MACAiD,KAAA,EAPmB;QACnBA,KAAA,EAAOoC,CAAA;QACPlC,SAAA,EAAW;UAAEwB,OAAA,EAAAvE;QAAQ;MACvB;MAKEE,YAAA,EAAc;IAChB,CAAC;EAED,IAAI,CAACmB,CAAA,CAAK6D,yCAAA,CAA0ChC,SAAA,EAClD,MAAMiC,KAAA,CAAM,0CAA0C;EAGxD,OAAO9D,CAAA,CAAK6D,yCAAA,CAA0ChC,SAAA,CAAUC,KAClE;AAAA;AAEA,eAAsBiC,GAAuB1F,CAAA,EAID;EAC1C,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,cAAA,EAAAC,CAAA;MAAgBU,OAAA,EAAAR;IAAQ,IAAIN,CAAA;IAG3C2B,CAAA,GAAqD;MACzDkB,aAAA,EAAe;QAAEC,GAAA,EAHHpC,CAAA,CAAeC,IAAA,CAAKP,CAAc,EAAE2C,YAAA,CAAa;MAGjC;IAChC;IACMX,CAAA,GAAe;MACnBe,KAAA,EAAOwC,CAAA;MACPtC,SAAA,EAAW;QACTC,eAAA,EAAiB3B,CAAA;QACjBP,MAAA,EAAQd,CAAA,EAASc,MAAA;QACjBC,KAAA,EAAOf,CAAA,EAASe,KAAA;QAChByC,QAAA,EAAUxD,CAAA,EAASyD;MACrB;IACF;EAOA,QANa,MAAMb,CAAA,CAA0C;IAC3DjD,WAAA,EAAAC,CAAA;IACAiD,KAAA,EAAOf,CAAA;IACP5B,YAAA,EAAc;EAChB,CAAC,GAEWoF,eACd;AAAA;AAWA,eAAsBC,GAA4B7F,CAAA,EAG7B;EACnB,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAa4F,UAAA,EAAA1F;IAAW,IAAIJ,CAAA;IAC9BM,CAAA,GAAY,IAAIyF,CAAA,CAAa3F,CAAA,CAAW4F,SAAA,CAAU,CAAC;EAEzD,IAAI5F,CAAA,YAAsB6F,CAAA,EAAqB;IAG7C,IAAMtE,CAAA,GADUuE,CAAA,CAAkBC,aAAA,CAAc;MAAEH,SAAA,EAAA1F;IAAU,CAAC,EACrC8F,cAAA,CAAe;IACvC,OAAOC,CAAA,CAAQC,cAAA,CAAe;MAAER,UAAA,EAAA1F,CAAA;MAAYyE,OAAA,EAAAlD;IAAQ,CAAC,CACvD;EAAA;EAEA,IAAIvB,CAAA,YAAsBmG,CAAA,EAAmB;IAE3C,IAAM7E,CAAA,GAA8CwE,CAAA,CAAkBC,aAAA,CAAc;MAClFH,SAAA,EAAA1F;IACF,CAAC;IAKD,IAJ+C,MAAMkG,CAAA,CAAe;MAClEC,OAAA,EAAS/E,CAAA;MACTzB,WAAA,EAAAC;IACF,CAAC,GAC2C;MAC1C,IAAMmE,CAAA,GAAU3C,CAAA,CAA4C0E,cAAA,CAAe;MAC3E,OAAOC,CAAA,CAAQC,cAAA,CAAe;QAAER,UAAA,EAAA1F,CAAA;QAAYyE,OAAA,EAAAR,CAAA;QAASqC,MAAA,EAAQ;MAAM,CAAC,CACtE;IAAA;IAEA,IAAMtE,CAAA,GAAgB8D,CAAA,CAAkBC,aAAA,CAAc;MACpDH,SAAA,EAAW1F,CAAA,CAAU0F;IACvB,CAAC;IAED,IADwB,MAAMQ,CAAA,CAAe;MAAEC,OAAA,EAASrE,CAAA;MAAenC,WAAA,EAAAC;IAAY,CAAC,GAC/D;MACnB,IAAMmE,CAAA,GAAUjC,CAAA,CAAcgE,cAAA,CAAe;MAC7C,OAAOC,CAAA,CAAQC,cAAA,CAAe;QAAER,UAAA,EAAA1F,CAAA;QAAYyE,OAAA,EAAAR,CAAA;QAASqC,MAAA,EAAQ;MAAK,CAAC,CACrE;IAAA;EACF;EAGA,MAAM,IAAIjB,KAAA,CAAM,yCAAyCrF,CAAU,EAAE,CACvE;AAAA;AAEA,eAAsBoG,EAAexG,CAAA,EAAkF;EACrH,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAauG,OAAA,EAAArG;IAAQ,IAAIJ,CAAA;IAC3BM,CAAA,GAAiB,MAAM0B,CAAA,CAA6B;MACxD/B,WAAA,EAAAC,CAAA;MACA+B,iBAAA,EAAmB7B,CAAA,CAAQgG,cAAA,CAAe;IAC5C,CAAC;EAED,IAAI;IACF,aAAMrG,CAAA,CAAQ;MACZE,WAAA,EAAAC,CAAA;MACAC,cAAA,EAAAG;IACF,CAAC,GACM,EACT;EAAA,SAASoB,CAAA,EAAY;IAEnB,IAAIA,CAAA,CAAMiF,MAAA,KAAW,KACnB,OAAO;IAET,MAAM,IAAIlB,KAAA,CAAM,2CAA2CnF,CAAA,CAAeM,QAAA,CAAS,CAAC,EAAE,CACxF;EAAA;AACF;AAAA,SAAAb,CAAA,IAAAqC,CAAA,EAAAvB,CAAA,IAAAgD,CAAA,EAAAvC,CAAA,IAAAZ,CAAA,EAAAkB,CAAA,IAAAsB,CAAA,EAAArB,CAAA,IAAAzB,CAAA,EAAA0B,CAAA,IAAAuE,CAAA,EAAArE,CAAA,IAAAkE,CAAA,EAAAtD,CAAA,IAAAwC,CAAA,EAAA1B,CAAA,IAAArB,CAAA,EAAA4B,EAAA,IAAA2C,CAAA,EAAArC,EAAA,IAAAE,CAAA,EAAAE,EAAA,IAAA4B,CAAA,EAAAxB,EAAA,IAAArC,CAAA,EAAAuC,CAAA,IAAA/E,CAAA,EAAAoF,EAAA,IAAAhF,CAAA,EAAAoF,EAAA,IAAA3E,CAAA,EAAA8E,EAAA,IAAAzC,CAAA,EAAAoD,CAAA,IAAA7E,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}