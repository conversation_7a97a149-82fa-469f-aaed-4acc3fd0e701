{"ast": null, "code": "export const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;", "map": {"version": 3, "names": ["crypto", "globalThis", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\crypto.ts"], "sourcesContent": ["/**\n * Internal webcrypto alias.\n * We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n * See utils.ts for details.\n * @module\n */\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto: any =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n"], "mappings": "AAOA,OAAO,MAAMA,MAAM,GACjB,OAAOC,UAAU,KAAK,QAAQ,IAAI,QAAQ,IAAIA,UAAU,GAAGA,UAAU,CAACD,MAAM,GAAGE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}