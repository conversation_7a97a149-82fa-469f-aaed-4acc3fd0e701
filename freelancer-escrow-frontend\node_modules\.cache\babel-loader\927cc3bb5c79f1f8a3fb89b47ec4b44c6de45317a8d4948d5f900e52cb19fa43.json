{"ast": null, "code": "var e = \"aptos:signMessage\";\nexport { e as a };", "map": {"version": 3, "names": ["e", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosSignMessage.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Signature } from '@aptos-labs/ts-sdk'\nimport { UserResponse } from '../misc'\n\n/** Version of the feature. */\nexport type AptosSignMessageVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosSignMessageNamespace = 'aptos:signMessage'\n\n/** TODO: docs */\nexport type AptosSignMessageFeature = {\n  /** Namespace for the feature. */\n  [AptosSignMessageNamespace]: {\n    /** Version of the feature API. */\n    version: AptosSignMessageVersion\n    signMessage: AptosSignMessageMethod\n  }\n}\n/** TODO: docs */\nexport type AptosSignMessageMethod = (\n  input: AptosSignMessageInput\n) => Promise<UserResponse<AptosSignMessageOutput>>\n\n/** TODO: docs */\nexport type AptosSignMessageInput = {\n  address?: boolean\n  application?: boolean\n  chainId?: boolean\n  message: string\n  nonce: string\n}\n\n/** TODO: docs */\nexport type AptosSignMessageOutput = {\n  address?: string\n  application?: string\n  chainId?: number\n  fullMessage: string\n  message: string\n  nonce: string\n  prefix: 'APTOS'\n  signature: Signature\n}\n"], "mappings": "AASO,IAAMA,CAAA,GAA4B;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}