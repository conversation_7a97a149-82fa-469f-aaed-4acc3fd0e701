import { useWallet } from '@aptos-labs/wallet-adapter-react';
import {
    Activity,
    ArrowRight,
    Briefcase,
    CheckCircle,
    DollarSign,
    Shield,
    Users,
    Zap
} from 'lucide-react';
import { Link } from 'react-router-dom';

const Home = () => {
  const { connected, account } = useWallet();
  const { escrows, balance } = useEscrow();
  const [stats, setStats] = useState({
    totalProjects: 1234,
    activeUsers: 567,
    successRate: 89.5,
    totalVolume: 2.1,
    completedProjects: 892,
    avgProjectValue: 3.2
  });

  // Real-time stats animation
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalProjects: prev.totalProjects + Math.floor(Math.random() * 3),
        activeUsers: prev.activeUsers + Math.floor(Math.random() * 2),
        totalVolume: prev.totalVolume + (Math.random() * 0.1)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: Shield,
      title: 'Secure Escrow',
      description: 'Smart contracts ensure funds are safely held until work is completed and approved.',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Built on Aptos blockchain for sub-second finality and ultra-low gas fees.',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      icon: Globe,
      title: 'Global Access',
      description: 'Work with anyone, anywhere in the world without geographical restrictions.',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      icon: Lock,
      title: 'Trustless System',
      description: 'No intermediaries needed. Smart contracts handle everything automatically.',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      icon: DollarSign,
      title: 'Low Fees',
      description: 'Only 2.5% platform fee, significantly lower than traditional platforms.',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      icon: TrendingUp,
      title: 'Transparent',
      description: 'All transactions are recorded on-chain for complete transparency.',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    }
  ];

  const howItWorks = [
    {
      step: 1,
      title: 'Client Creates Project',
      description: 'Client posts a project with requirements, budget, and deadline.',
      icon: Users
    },
    {
      step: 2,
      title: 'Funds are Escrowed',
      description: 'Client funds the project, and money is held securely in smart contract.',
      icon: Shield
    },
    {
      step: 3,
      title: 'Freelancer Delivers',
      description: 'Freelancer completes the work and submits deliverables for review.',
      icon: Briefcase
    },
    {
      step: 4,
      title: 'Payment Released',
      description: 'Once approved, smart contract automatically releases payment to freelancer.',
      icon: CheckCircle
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <div className="text-center space-y-8 animate-fade-in">
        <div className="space-y-6">
          <Badge variant="primary" className="mb-4">
            🚀 Now Live on Aptos Devnet
          </Badge>

          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-secondary-900 leading-tight">
            The Future of
            <span className="text-gradient block mt-2">Freelancing</span>
          </h1>

          <p className="text-xl md:text-2xl text-secondary-600 max-w-4xl mx-auto leading-relaxed">
            Experience trustless freelancing powered by Aptos blockchain.
            <span className="block mt-2">Secure escrow • Instant payments • Zero disputes</span>
          </p>

          {connected && account && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-4 max-w-md mx-auto">
              <p className="text-green-800 font-medium">
                ✅ Wallet Connected • Balance: {formatAmount(balance)} APT
              </p>
            </div>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            as={Link}
            to="/client"
            variant="primary"
            size="xl"
            icon={<Users className="w-5 h-5" />}
            iconPosition="left"
            className="group"
          >
            <span>I'm a Client</span>
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>

          <Button
            as={Link}
            to="/freelancer"
            variant="secondary"
            size="xl"
            icon={<Briefcase className="w-5 h-5" />}
            iconPosition="left"
            className="group"
          >
            <span>I'm a Freelancer</span>
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>

        {!connected && (
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6 max-w-lg mx-auto">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <p className="text-yellow-800 font-medium">Connect your wallet to get started</p>
            </div>
            <p className="text-yellow-700 text-sm">
              Use Petra, Martian, or any Aptos-compatible wallet
            </p>
          </div>
        )}
      </div>

      {/* Features Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-secondary-900 mb-4">
            Why Choose FreelanceChain?
          </h2>
          <p className="text-secondary-600 max-w-2xl mx-auto">
            Experience the future of freelancing with blockchain technology
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card
                key={index}
                hover
                className="text-center group animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <Card.Body className="p-8">
                  <div className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200`}>
                    <Icon className={`w-8 h-8 ${feature.color}`} />
                  </div>
                  <h3 className="text-xl font-bold text-secondary-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-secondary-600 leading-relaxed">
                    {feature.description}
                  </p>
                </Card.Body>
              </Card>
            );
          })}
        </div>
      </div>

      {/* How It Works Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-secondary-900 mb-4">
            How It Works
          </h2>
          <p className="text-secondary-600 max-w-2xl mx-auto">
            Simple, secure, and transparent process from start to finish
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {howItWorks.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="relative">
                <div className="card p-6 text-center">
                  <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                    {step.step}
                  </div>
                  <div className="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-4 h-4 text-secondary-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-secondary-600 text-sm">
                    {step.description}
                  </p>
                </div>
                {index < howItWorks.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                    <ArrowRight className="w-6 h-6 text-secondary-300" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 rounded-3xl p-8 md:p-12 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-40 h-40 bg-white rounded-full -translate-x-20 -translate-y-20"></div>
          <div className="absolute bottom-0 right-0 w-60 h-60 bg-white rounded-full translate-x-30 translate-y-30"></div>
        </div>

        <div className="relative z-10">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4 bg-white/20 text-white border-white/30">
              📊 Live Statistics
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Platform Performance
            </h2>
            <p className="text-primary-100 text-lg">
              Join thousands of users building the future of work
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold mb-2 animate-pulse">
                {stats.totalProjects.toLocaleString()}
              </div>
              <div className="text-primary-100 text-sm">Total Projects</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold mb-2 animate-pulse">
                {stats.activeUsers.toLocaleString()}
              </div>
              <div className="text-primary-100 text-sm">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold mb-2">
                {stats.successRate}%
              </div>
              <div className="text-primary-100 text-sm">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold mb-2 animate-pulse">
                {formatCurrency(stats.totalVolume, 'M APT')}
              </div>
              <div className="text-primary-100 text-sm">Total Volume</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold mb-2">
                {stats.completedProjects.toLocaleString()}
              </div>
              <div className="text-primary-100 text-sm">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold mb-2">
                {formatCurrency(stats.avgProjectValue)}
              </div>
              <div className="text-primary-100 text-sm">Avg Project</div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center space-y-6">
        <h2 className="text-3xl font-bold text-secondary-900">
          Ready to Get Started?
        </h2>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Join the future of freelancing today. Connect your wallet and start your first project.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/blockchain"
            className="btn-secondary flex items-center justify-center space-x-2"
          >
            <Activity className="w-5 h-5" />
            <span>View Blockchain Status</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
