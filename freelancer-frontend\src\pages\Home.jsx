import React from 'react';
import { Link } from 'react-router-dom';
import { useWallet } from '@aptos-labs/wallet-adapter-react';
import { 
  Shield, 
  Zap, 
  Users, 
  Briefcase, 
  Activity, 
  ArrowRight,
  CheckCircle,
  DollarSign
} from 'lucide-react';

const Home = () => {
  const { connected } = useWallet();

  const features = [
    {
      icon: Shield,
      title: 'Secure Escrow',
      description: 'Smart contracts ensure funds are safely held until work is completed and approved.'
    },
    {
      icon: Zap,
      title: 'Fast Transactions',
      description: 'Built on Aptos blockchain for lightning-fast and low-cost transactions.'
    },
    {
      icon: Users,
      title: 'Trustless System',
      description: 'No need to trust intermediaries. The blockchain handles everything automatically.'
    },
    {
      icon: DollarSign,
      title: 'Low Fees',
      description: 'Only 2.5% platform fee, much lower than traditional freelancing platforms.'
    }
  ];

  const howItWorks = [
    {
      step: 1,
      title: 'Client Creates Project',
      description: 'Client posts a project with requirements, budget, and deadline.',
      icon: Users
    },
    {
      step: 2,
      title: 'Funds are Escrowed',
      description: 'Client funds the project, and money is held securely in smart contract.',
      icon: Shield
    },
    {
      step: 3,
      title: 'Freelancer Delivers',
      description: 'Freelancer completes the work and submits deliverables for review.',
      icon: Briefcase
    },
    {
      step: 4,
      title: 'Payment Released',
      description: 'Once approved, smart contract automatically releases payment to freelancer.',
      icon: CheckCircle
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <div className="text-center space-y-8">
        <div className="space-y-4">
          <h1 className="text-4xl md:text-6xl font-bold text-secondary-900">
            Decentralized
            <span className="text-primary-600"> Freelancing</span>
          </h1>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            A trustless freelancing platform built on Aptos blockchain. 
            Secure escrow, instant payments, and transparent transactions.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/client"
            className="btn-primary flex items-center justify-center space-x-2 text-lg px-8 py-3"
          >
            <Users className="w-5 h-5" />
            <span>I'm a Client</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
          <Link
            to="/freelancer"
            className="btn-secondary flex items-center justify-center space-x-2 text-lg px-8 py-3"
          >
            <Briefcase className="w-5 h-5" />
            <span>I'm a Freelancer</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>

        {!connected && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-yellow-800 text-sm">
              💡 Connect your wallet to get started with the platform
            </p>
          </div>
        )}
      </div>

      {/* Features Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-secondary-900 mb-4">
            Why Choose FreelanceChain?
          </h2>
          <p className="text-secondary-600 max-w-2xl mx-auto">
            Experience the future of freelancing with blockchain technology
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="card p-6 text-center hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Icon className="w-6 h-6 text-primary-600" />
                </div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-secondary-600 text-sm">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>

      {/* How It Works Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-secondary-900 mb-4">
            How It Works
          </h2>
          <p className="text-secondary-600 max-w-2xl mx-auto">
            Simple, secure, and transparent process from start to finish
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {howItWorks.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="relative">
                <div className="card p-6 text-center">
                  <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                    {step.step}
                  </div>
                  <div className="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-4 h-4 text-secondary-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-secondary-600 text-sm">
                    {step.description}
                  </p>
                </div>
                {index < howItWorks.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                    <ArrowRight className="w-6 h-6 text-secondary-300" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-primary-600 rounded-2xl p-8 text-white">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">Platform Statistics</h2>
          <p className="text-primary-100">
            Join thousands of users already using FreelanceChain
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">1,234</div>
            <div className="text-primary-100">Total Projects</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">567</div>
            <div className="text-primary-100">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">89.5%</div>
            <div className="text-primary-100">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">$2.1M</div>
            <div className="text-primary-100">Total Volume</div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center space-y-6">
        <h2 className="text-3xl font-bold text-secondary-900">
          Ready to Get Started?
        </h2>
        <p className="text-secondary-600 max-w-2xl mx-auto">
          Join the future of freelancing today. Connect your wallet and start your first project.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/blockchain"
            className="btn-secondary flex items-center justify-center space-x-2"
          >
            <Activity className="w-5 h-5" />
            <span>View Blockchain Status</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
