{"ast": null, "code": "import { _validateObject, abytes, bytesToNumberBE, concatBytes, isBytes, isHash, utf8ToBytes } from \"../utils.js\";\nimport { FpInvertBatch, mod } from \"./modular.js\";\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n  anum(value);\n  anum(length);\n  if (value < 0 || value >= 1 << 8 * length) throw new Error('invalid I2OSP input: ' + value);\n  const res = Array.from({\n    length\n  }).fill(0);\n  for (let i = length - 1; i >= 0; i--) {\n    res[i] = value & 0xff;\n    value >>>= 8;\n  }\n  return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n  const arr = new Uint8Array(a.length);\n  for (let i = 0; i < a.length; i++) {\n    arr[i] = a[i] ^ b[i];\n  }\n  return arr;\n}\nfunction anum(item) {\n  if (!Number.isSafeInteger(item)) throw new Error('number expected');\n}\nfunction normDST(DST) {\n  if (!isBytes(DST) && typeof DST !== 'string') throw new Error('DST must be Uint8Array or string');\n  return typeof DST === 'string' ? utf8ToBytes(DST) : DST;\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nexport function expand_message_xmd(msg, DST, lenInBytes, H) {\n  abytes(msg);\n  anum(lenInBytes);\n  DST = normDST(DST);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  if (DST.length > 255) DST = H(concatBytes(utf8ToBytes('H2C-OVERSIZE-DST-'), DST));\n  const {\n    outputLen: b_in_bytes,\n    blockLen: r_in_bytes\n  } = H;\n  const ell = Math.ceil(lenInBytes / b_in_bytes);\n  if (lenInBytes > 65535 || ell > 255) throw new Error('expand_message_xmd: invalid lenInBytes');\n  const DST_prime = concatBytes(DST, i2osp(DST.length, 1));\n  const Z_pad = i2osp(0, r_in_bytes);\n  const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n  const b = new Array(ell);\n  const b_0 = H(concatBytes(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n  b[0] = H(concatBytes(b_0, i2osp(1, 1), DST_prime));\n  for (let i = 1; i <= ell; i++) {\n    const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n    b[i] = H(concatBytes(...args));\n  }\n  const pseudo_random_bytes = concatBytes(...b);\n  return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nexport function expand_message_xof(msg, DST, lenInBytes, k, H) {\n  abytes(msg);\n  anum(lenInBytes);\n  DST = normDST(DST);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n  if (DST.length > 255) {\n    const dkLen = Math.ceil(2 * k / 8);\n    DST = H.create({\n      dkLen\n    }).update(utf8ToBytes('H2C-OVERSIZE-DST-')).update(DST).digest();\n  }\n  if (lenInBytes > 65535 || DST.length > 255) throw new Error('expand_message_xof: invalid lenInBytes');\n  return H.create({\n    dkLen: lenInBytes\n  }).update(msg).update(i2osp(lenInBytes, 2))\n  // 2. DST_prime = DST || I2OSP(len(DST), 1)\n  .update(DST).update(i2osp(DST.length, 1)).digest();\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nexport function hash_to_field(msg, count, options) {\n  _validateObject(options, {\n    p: 'bigint',\n    m: 'number',\n    k: 'number',\n    hash: 'function'\n  });\n  const {\n    p,\n    k,\n    m,\n    hash,\n    expand,\n    DST\n  } = options;\n  if (!isHash(options.hash)) throw new Error('expected valid hash');\n  abytes(msg);\n  anum(count);\n  const log2p = p.toString(2).length;\n  const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n  const len_in_bytes = count * m * L;\n  let prb; // pseudo_random_bytes\n  if (expand === 'xmd') {\n    prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n  } else if (expand === 'xof') {\n    prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n  } else if (expand === '_internal_pass') {\n    // for internal tests only\n    prb = msg;\n  } else {\n    throw new Error('expand must be \"xmd\" or \"xof\"');\n  }\n  const u = new Array(count);\n  for (let i = 0; i < count; i++) {\n    const e = new Array(m);\n    for (let j = 0; j < m; j++) {\n      const elm_offset = L * (j + i * m);\n      const tv = prb.subarray(elm_offset, elm_offset + L);\n      e[j] = mod(os2ip(tv), p);\n    }\n    u[i] = e;\n  }\n  return u;\n}\nexport function isogenyMap(field, map) {\n  // Make same order as in spec\n  const coeff = map.map(i => Array.from(i).reverse());\n  return (x, y) => {\n    const [xn, xd, yn, yd] = coeff.map(val => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n    // 6.6.3\n    // Exceptional cases of iso_map are inputs that cause the denominator of\n    // either rational function to evaluate to zero; such cases MUST return\n    // the identity point on E.\n    const [xd_inv, yd_inv] = FpInvertBatch(field, [xd, yd], true);\n    x = field.mul(xn, xd_inv); // xNum / xDen\n    y = field.mul(y, field.mul(yn, yd_inv)); // y * (yNum / yDev)\n    return {\n      x,\n      y\n    };\n  };\n}\nexport const _DST_scalar = utf8ToBytes('HashToScalar-');\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. See {@link H2CHasher}. */\nexport function createHasher(Point, mapToCurve, defaults) {\n  if (typeof mapToCurve !== 'function') throw new Error('mapToCurve() must be defined');\n  function map(num) {\n    return Point.fromAffine(mapToCurve(num));\n  }\n  function clear(initial) {\n    const P = initial.clearCofactor();\n    if (P.equals(Point.ZERO)) return Point.ZERO; // zero will throw in assert\n    P.assertValidity();\n    return P;\n  }\n  return {\n    defaults,\n    hashToCurve(msg, options) {\n      const opts = Object.assign({}, defaults, options);\n      const u = hash_to_field(msg, 2, opts);\n      const u0 = map(u[0]);\n      const u1 = map(u[1]);\n      return clear(u0.add(u1));\n    },\n    encodeToCurve(msg, options) {\n      const optsDst = defaults.encodeDST ? {\n        DST: defaults.encodeDST\n      } : {};\n      const opts = Object.assign({}, defaults, optsDst, options);\n      const u = hash_to_field(msg, 1, opts);\n      const u0 = map(u[0]);\n      return clear(u0);\n    },\n    /** See {@link H2CHasher} */\n    mapToCurve(scalars) {\n      if (!Array.isArray(scalars)) throw new Error('expected array of bigints');\n      for (const i of scalars) if (typeof i !== 'bigint') throw new Error('expected array of bigints');\n      return clear(map(scalars));\n    },\n    // hash_to_scalar can produce 0: https://www.rfc-editor.org/errata/eid8393\n    // RFC 9380, draft-irtf-cfrg-bbs-signatures-08\n    hashToScalar(msg, options) {\n      // @ts-ignore\n      const N = Point.Fn.ORDER;\n      const opts = Object.assign({}, defaults, {\n        p: N,\n        m: 1,\n        DST: _DST_scalar\n      }, options);\n      return hash_to_field(msg, 1, opts)[0][0];\n    }\n  };\n}", "map": {"version": 3, "names": ["_validateObject", "abytes", "bytesToNumberBE", "concatBytes", "isBytes", "isHash", "utf8ToBytes", "FpInvertBatch", "mod", "os2ip", "i2osp", "value", "length", "anum", "Error", "res", "Array", "from", "fill", "i", "Uint8Array", "strxor", "a", "b", "arr", "item", "Number", "isSafeInteger", "normDST", "DST", "expand_message_xmd", "msg", "lenInBytes", "H", "outputLen", "b_in_bytes", "blockLen", "r_in_bytes", "ell", "Math", "ceil", "DST_prime", "Z_pad", "l_i_b_str", "b_0", "args", "pseudo_random_bytes", "slice", "expand_message_xof", "k", "dkLen", "create", "update", "digest", "hash_to_field", "count", "options", "p", "m", "hash", "expand", "log2p", "toString", "L", "len_in_bytes", "prb", "u", "e", "j", "elm_offset", "tv", "subarray", "isogenyMap", "field", "map", "coeff", "reverse", "x", "y", "xn", "xd", "yn", "yd", "val", "reduce", "acc", "add", "mul", "xd_inv", "yd_inv", "_DST_scalar", "createHasher", "Point", "mapToCurve", "defaults", "num", "fromAffine", "clear", "initial", "P", "clearCofactor", "equals", "ZERO", "assertValidity", "hashToCurve", "opts", "Object", "assign", "u0", "u1", "encodeToCurve", "optsDst", "encodeDST", "scalars", "isArray", "hashToScalar", "N", "Fn", "ORDER"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\abstract\\hash-to-curve.ts"], "sourcesContent": ["/**\n * hash-to-curve from RFC 9380.\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * https://www.rfc-editor.org/rfc/rfc9380\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport type { CHash } from '../utils.ts';\nimport {\n  _validateObject,\n  abytes,\n  bytesToNumberBE,\n  concatBytes,\n  isBytes,\n  isHash,\n  utf8ToBytes,\n} from '../utils.ts';\nimport type { AffinePoint, Group, GroupConstructor } from './curve.ts';\nimport { FpInvertBatch, mod, type IField } from './modular.ts';\n\nexport type UnicodeOrBytes = string | Uint8Array;\n\n/**\n * * `DST` is a domain separation tag, defined in section 2.2.5\n * * `p` characteristic of F, where F is a finite field of characteristic p and order q = p^m\n * * `m` is extension degree (1 for prime fields)\n * * `k` is the target security target in bits (e.g. 128), from section 5.1\n * * `expand` is `xmd` (SHA2, SHA3, BLAKE) or `xof` (SHAKE, BLAKE-XOF)\n * * `hash` conforming to `utils.CHash` interface, with `outputLen` / `blockLen` props\n */\nexport type H2COpts = {\n  DST: UnicodeOrBytes;\n  expand: 'xmd' | 'xof';\n  hash: CHash;\n  p: bigint;\n  m: number;\n  k: number;\n};\nexport type H2CHashOpts = {\n  expand: 'xmd' | 'xof';\n  hash: CHash;\n};\n// todo: remove\nexport type Opts = H2COpts;\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = bytesToNumberBE;\n\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value: number, length: number): Uint8Array {\n  anum(value);\n  anum(length);\n  if (value < 0 || value >= 1 << (8 * length)) throw new Error('invalid I2OSP input: ' + value);\n  const res = Array.from({ length }).fill(0) as number[];\n  for (let i = length - 1; i >= 0; i--) {\n    res[i] = value & 0xff;\n    value >>>= 8;\n  }\n  return new Uint8Array(res);\n}\n\nfunction strxor(a: Uint8Array, b: Uint8Array): Uint8Array {\n  const arr = new Uint8Array(a.length);\n  for (let i = 0; i < a.length; i++) {\n    arr[i] = a[i] ^ b[i];\n  }\n  return arr;\n}\n\nfunction anum(item: unknown): void {\n  if (!Number.isSafeInteger(item)) throw new Error('number expected');\n}\n\nfunction normDST(DST: UnicodeOrBytes): Uint8Array {\n  if (!isBytes(DST) && typeof DST !== 'string') throw new Error('DST must be Uint8Array or string');\n  return typeof DST === 'string' ? utf8ToBytes(DST) : DST;\n}\n\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nexport function expand_message_xmd(\n  msg: Uint8Array,\n  DST: UnicodeOrBytes,\n  lenInBytes: number,\n  H: CHash\n): Uint8Array {\n  abytes(msg);\n  anum(lenInBytes);\n  DST = normDST(DST);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  if (DST.length > 255) DST = H(concatBytes(utf8ToBytes('H2C-OVERSIZE-DST-'), DST));\n  const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n  const ell = Math.ceil(lenInBytes / b_in_bytes);\n  if (lenInBytes > 65535 || ell > 255) throw new Error('expand_message_xmd: invalid lenInBytes');\n  const DST_prime = concatBytes(DST, i2osp(DST.length, 1));\n  const Z_pad = i2osp(0, r_in_bytes);\n  const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n  const b = new Array<Uint8Array>(ell);\n  const b_0 = H(concatBytes(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n  b[0] = H(concatBytes(b_0, i2osp(1, 1), DST_prime));\n  for (let i = 1; i <= ell; i++) {\n    const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n    b[i] = H(concatBytes(...args));\n  }\n  const pseudo_random_bytes = concatBytes(...b);\n  return pseudo_random_bytes.slice(0, lenInBytes);\n}\n\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nexport function expand_message_xof(\n  msg: Uint8Array,\n  DST: UnicodeOrBytes,\n  lenInBytes: number,\n  k: number,\n  H: CHash\n): Uint8Array {\n  abytes(msg);\n  anum(lenInBytes);\n  DST = normDST(DST);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n  if (DST.length > 255) {\n    const dkLen = Math.ceil((2 * k) / 8);\n    DST = H.create({ dkLen }).update(utf8ToBytes('H2C-OVERSIZE-DST-')).update(DST).digest();\n  }\n  if (lenInBytes > 65535 || DST.length > 255)\n    throw new Error('expand_message_xof: invalid lenInBytes');\n  return (\n    H.create({ dkLen: lenInBytes })\n      .update(msg)\n      .update(i2osp(lenInBytes, 2))\n      // 2. DST_prime = DST || I2OSP(len(DST), 1)\n      .update(DST)\n      .update(i2osp(DST.length, 1))\n      .digest()\n  );\n}\n\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nexport function hash_to_field(msg: Uint8Array, count: number, options: H2COpts): bigint[][] {\n  _validateObject(options, {\n    p: 'bigint',\n    m: 'number',\n    k: 'number',\n    hash: 'function',\n  });\n  const { p, k, m, hash, expand, DST } = options;\n  if (!isHash(options.hash)) throw new Error('expected valid hash');\n  abytes(msg);\n  anum(count);\n  const log2p = p.toString(2).length;\n  const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n  const len_in_bytes = count * m * L;\n  let prb; // pseudo_random_bytes\n  if (expand === 'xmd') {\n    prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n  } else if (expand === 'xof') {\n    prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n  } else if (expand === '_internal_pass') {\n    // for internal tests only\n    prb = msg;\n  } else {\n    throw new Error('expand must be \"xmd\" or \"xof\"');\n  }\n  const u = new Array(count);\n  for (let i = 0; i < count; i++) {\n    const e = new Array(m);\n    for (let j = 0; j < m; j++) {\n      const elm_offset = L * (j + i * m);\n      const tv = prb.subarray(elm_offset, elm_offset + L);\n      e[j] = mod(os2ip(tv), p);\n    }\n    u[i] = e;\n  }\n  return u;\n}\n\nexport type XY<T> = (x: T, y: T) => { x: T; y: T };\nexport type XYRatio<T> = [T[], T[], T[], T[]]; // xn/xd, yn/yd\nexport function isogenyMap<T, F extends IField<T>>(field: F, map: XYRatio<T>): XY<T> {\n  // Make same order as in spec\n  const coeff = map.map((i) => Array.from(i).reverse());\n  return (x: T, y: T) => {\n    const [xn, xd, yn, yd] = coeff.map((val) =>\n      val.reduce((acc, i) => field.add(field.mul(acc, x), i))\n    );\n    // 6.6.3\n    // Exceptional cases of iso_map are inputs that cause the denominator of\n    // either rational function to evaluate to zero; such cases MUST return\n    // the identity point on E.\n    const [xd_inv, yd_inv] = FpInvertBatch(field, [xd, yd], true);\n    x = field.mul(xn, xd_inv); // xNum / xDen\n    y = field.mul(y, field.mul(yn, yd_inv)); // y * (yNum / yDev)\n    return { x, y };\n  };\n}\n\n/** Point interface, which curves must implement to work correctly with the module. */\nexport interface H2CPoint<T> extends Group<H2CPoint<T>> {\n  add(rhs: H2CPoint<T>): H2CPoint<T>;\n  toAffine(iz?: bigint): AffinePoint<T>;\n  clearCofactor(): H2CPoint<T>;\n  assertValidity(): void;\n}\n\nexport interface H2CPointConstructor<T> extends GroupConstructor<H2CPoint<T>> {\n  fromAffine(ap: AffinePoint<T>): H2CPoint<T>;\n}\n\nexport type MapToCurve<T> = (scalar: bigint[]) => AffinePoint<T>;\n\n// Separated from initialization opts, so users won't accidentally change per-curve parameters\n// (changing DST is ok!)\nexport type htfBasicOpts = { DST: UnicodeOrBytes };\nexport type H2CMethod<T> = (msg: Uint8Array, options?: htfBasicOpts) => H2CPoint<T>;\n// TODO: remove\nexport type HTFMethod<T> = H2CMethod<T>;\nexport type MapMethod<T> = (scalars: bigint[]) => H2CPoint<T>;\nexport type H2CHasherBase<T> = {\n  hashToCurve: H2CMethod<T>;\n  hashToScalar: (msg: Uint8Array, options: htfBasicOpts) => bigint;\n};\n/**\n * RFC 9380 methods, with cofactor clearing. See https://www.rfc-editor.org/rfc/rfc9380#section-3.\n *\n * * hashToCurve: `map(hash(input))`, encodes RANDOM bytes to curve (WITH hashing)\n * * encodeToCurve: `map(hash(input))`, encodes NON-UNIFORM bytes to curve (WITH hashing)\n * * mapToCurve: `map(scalars)`, encodes NON-UNIFORM scalars to curve (NO hashing)\n */\nexport type H2CHasher<T> = H2CHasherBase<T> & {\n  encodeToCurve: H2CMethod<T>;\n  mapToCurve: MapMethod<T>;\n  defaults: H2COpts & { encodeDST?: UnicodeOrBytes };\n};\n// TODO: remove\nexport type Hasher<T> = H2CHasher<T>;\n\nexport const _DST_scalar: Uint8Array = utf8ToBytes('HashToScalar-');\n\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. See {@link H2CHasher}. */\nexport function createHasher<T>(\n  Point: H2CPointConstructor<T>,\n  mapToCurve: MapToCurve<T>,\n  defaults: H2COpts & { encodeDST?: UnicodeOrBytes }\n): H2CHasher<T> {\n  if (typeof mapToCurve !== 'function') throw new Error('mapToCurve() must be defined');\n  function map(num: bigint[]) {\n    return Point.fromAffine(mapToCurve(num));\n  }\n  function clear(initial: H2CPoint<T>) {\n    const P = initial.clearCofactor();\n    if (P.equals(Point.ZERO)) return Point.ZERO; // zero will throw in assert\n    P.assertValidity();\n    return P;\n  }\n\n  return {\n    defaults,\n\n    hashToCurve(msg: Uint8Array, options?: htfBasicOpts): H2CPoint<T> {\n      const opts = Object.assign({}, defaults, options);\n      const u = hash_to_field(msg, 2, opts);\n      const u0 = map(u[0]);\n      const u1 = map(u[1]);\n      return clear(u0.add(u1));\n    },\n    encodeToCurve(msg: Uint8Array, options?: htfBasicOpts): H2CPoint<T> {\n      const optsDst = defaults.encodeDST ? { DST: defaults.encodeDST } : {};\n      const opts = Object.assign({}, defaults, optsDst, options);\n      const u = hash_to_field(msg, 1, opts);\n      const u0 = map(u[0]);\n      return clear(u0);\n    },\n    /** See {@link H2CHasher} */\n    mapToCurve(scalars: bigint[]): H2CPoint<T> {\n      if (!Array.isArray(scalars)) throw new Error('expected array of bigints');\n      for (const i of scalars)\n        if (typeof i !== 'bigint') throw new Error('expected array of bigints');\n      return clear(map(scalars));\n    },\n\n    // hash_to_scalar can produce 0: https://www.rfc-editor.org/errata/eid8393\n    // RFC 9380, draft-irtf-cfrg-bbs-signatures-08\n    hashToScalar(msg: Uint8Array, options?: htfBasicOpts): bigint {\n      // @ts-ignore\n      const N = Point.Fn.ORDER;\n      const opts = Object.assign({}, defaults, { p: N, m: 1, DST: _DST_scalar }, options);\n      return hash_to_field(msg, 1, opts)[0][0];\n    },\n  };\n}\n"], "mappings": "AAQA,SACEA,eAAe,EACfC,MAAM,EACNC,eAAe,EACfC,WAAW,EACXC,OAAO,EACPC,MAAM,EACNC,WAAW,QACN,aAAa;AAEpB,SAASC,aAAa,EAAEC,GAAG,QAAqB,cAAc;AA2B9D;AACA,MAAMC,KAAK,GAAGP,eAAe;AAE7B;AACA,SAASQ,KAAKA,CAACC,KAAa,EAAEC,MAAc;EAC1CC,IAAI,CAACF,KAAK,CAAC;EACXE,IAAI,CAACD,MAAM,CAAC;EACZ,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAK,CAAC,GAAGC,MAAO,EAAE,MAAM,IAAIE,KAAK,CAAC,uBAAuB,GAAGH,KAAK,CAAC;EAC7F,MAAMI,GAAG,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEL;EAAM,CAAE,CAAC,CAACM,IAAI,CAAC,CAAC,CAAa;EACtD,KAAK,IAAIC,CAAC,GAAGP,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACpCJ,GAAG,CAACI,CAAC,CAAC,GAAGR,KAAK,GAAG,IAAI;IACrBA,KAAK,MAAM,CAAC;EACd;EACA,OAAO,IAAIS,UAAU,CAACL,GAAG,CAAC;AAC5B;AAEA,SAASM,MAAMA,CAACC,CAAa,EAAEC,CAAa;EAC1C,MAAMC,GAAG,GAAG,IAAIJ,UAAU,CAACE,CAAC,CAACV,MAAM,CAAC;EACpC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,CAAC,CAACV,MAAM,EAAEO,CAAC,EAAE,EAAE;IACjCK,GAAG,CAACL,CAAC,CAAC,GAAGG,CAAC,CAACH,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EACtB;EACA,OAAOK,GAAG;AACZ;AAEA,SAASX,IAAIA,CAACY,IAAa;EACzB,IAAI,CAACC,MAAM,CAACC,aAAa,CAACF,IAAI,CAAC,EAAE,MAAM,IAAIX,KAAK,CAAC,iBAAiB,CAAC;AACrE;AAEA,SAASc,OAAOA,CAACC,GAAmB;EAClC,IAAI,CAACzB,OAAO,CAACyB,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIf,KAAK,CAAC,kCAAkC,CAAC;EACjG,OAAO,OAAOe,GAAG,KAAK,QAAQ,GAAGvB,WAAW,CAACuB,GAAG,CAAC,GAAGA,GAAG;AACzD;AAEA;;;;AAIA,OAAM,SAAUC,kBAAkBA,CAChCC,GAAe,EACfF,GAAmB,EACnBG,UAAkB,EAClBC,CAAQ;EAERhC,MAAM,CAAC8B,GAAG,CAAC;EACXlB,IAAI,CAACmB,UAAU,CAAC;EAChBH,GAAG,GAAGD,OAAO,CAACC,GAAG,CAAC;EAClB;EACA,IAAIA,GAAG,CAACjB,MAAM,GAAG,GAAG,EAAEiB,GAAG,GAAGI,CAAC,CAAC9B,WAAW,CAACG,WAAW,CAAC,mBAAmB,CAAC,EAAEuB,GAAG,CAAC,CAAC;EACjF,MAAM;IAAEK,SAAS,EAAEC,UAAU;IAAEC,QAAQ,EAAEC;EAAU,CAAE,GAAGJ,CAAC;EACzD,MAAMK,GAAG,GAAGC,IAAI,CAACC,IAAI,CAACR,UAAU,GAAGG,UAAU,CAAC;EAC9C,IAAIH,UAAU,GAAG,KAAK,IAAIM,GAAG,GAAG,GAAG,EAAE,MAAM,IAAIxB,KAAK,CAAC,wCAAwC,CAAC;EAC9F,MAAM2B,SAAS,GAAGtC,WAAW,CAAC0B,GAAG,EAAEnB,KAAK,CAACmB,GAAG,CAACjB,MAAM,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM8B,KAAK,GAAGhC,KAAK,CAAC,CAAC,EAAE2B,UAAU,CAAC;EAClC,MAAMM,SAAS,GAAGjC,KAAK,CAACsB,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMT,CAAC,GAAG,IAAIP,KAAK,CAAasB,GAAG,CAAC;EACpC,MAAMM,GAAG,GAAGX,CAAC,CAAC9B,WAAW,CAACuC,KAAK,EAAEX,GAAG,EAAEY,SAAS,EAAEjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE+B,SAAS,CAAC,CAAC;EACzElB,CAAC,CAAC,CAAC,CAAC,GAAGU,CAAC,CAAC9B,WAAW,CAACyC,GAAG,EAAElC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE+B,SAAS,CAAC,CAAC;EAClD,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImB,GAAG,EAAEnB,CAAC,EAAE,EAAE;IAC7B,MAAM0B,IAAI,GAAG,CAACxB,MAAM,CAACuB,GAAG,EAAErB,CAAC,CAACJ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAET,KAAK,CAACS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEsB,SAAS,CAAC;IAChElB,CAAC,CAACJ,CAAC,CAAC,GAAGc,CAAC,CAAC9B,WAAW,CAAC,GAAG0C,IAAI,CAAC,CAAC;EAChC;EACA,MAAMC,mBAAmB,GAAG3C,WAAW,CAAC,GAAGoB,CAAC,CAAC;EAC7C,OAAOuB,mBAAmB,CAACC,KAAK,CAAC,CAAC,EAAEf,UAAU,CAAC;AACjD;AAEA;;;;;;;AAOA,OAAM,SAAUgB,kBAAkBA,CAChCjB,GAAe,EACfF,GAAmB,EACnBG,UAAkB,EAClBiB,CAAS,EACThB,CAAQ;EAERhC,MAAM,CAAC8B,GAAG,CAAC;EACXlB,IAAI,CAACmB,UAAU,CAAC;EAChBH,GAAG,GAAGD,OAAO,CAACC,GAAG,CAAC;EAClB;EACA;EACA,IAAIA,GAAG,CAACjB,MAAM,GAAG,GAAG,EAAE;IACpB,MAAMsC,KAAK,GAAGX,IAAI,CAACC,IAAI,CAAE,CAAC,GAAGS,CAAC,GAAI,CAAC,CAAC;IACpCpB,GAAG,GAAGI,CAAC,CAACkB,MAAM,CAAC;MAAED;IAAK,CAAE,CAAC,CAACE,MAAM,CAAC9C,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC8C,MAAM,CAACvB,GAAG,CAAC,CAACwB,MAAM,EAAE;EACzF;EACA,IAAIrB,UAAU,GAAG,KAAK,IAAIH,GAAG,CAACjB,MAAM,GAAG,GAAG,EACxC,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;EAC3D,OACEmB,CAAC,CAACkB,MAAM,CAAC;IAAED,KAAK,EAAElB;EAAU,CAAE,CAAC,CAC5BoB,MAAM,CAACrB,GAAG,CAAC,CACXqB,MAAM,CAAC1C,KAAK,CAACsB,UAAU,EAAE,CAAC,CAAC;EAC5B;EAAA,CACCoB,MAAM,CAACvB,GAAG,CAAC,CACXuB,MAAM,CAAC1C,KAAK,CAACmB,GAAG,CAACjB,MAAM,EAAE,CAAC,CAAC,CAAC,CAC5ByC,MAAM,EAAE;AAEf;AAEA;;;;;;;;AAQA,OAAM,SAAUC,aAAaA,CAACvB,GAAe,EAAEwB,KAAa,EAAEC,OAAgB;EAC5ExD,eAAe,CAACwD,OAAO,EAAE;IACvBC,CAAC,EAAE,QAAQ;IACXC,CAAC,EAAE,QAAQ;IACXT,CAAC,EAAE,QAAQ;IACXU,IAAI,EAAE;GACP,CAAC;EACF,MAAM;IAAEF,CAAC;IAAER,CAAC;IAAES,CAAC;IAAEC,IAAI;IAAEC,MAAM;IAAE/B;EAAG,CAAE,GAAG2B,OAAO;EAC9C,IAAI,CAACnD,MAAM,CAACmD,OAAO,CAACG,IAAI,CAAC,EAAE,MAAM,IAAI7C,KAAK,CAAC,qBAAqB,CAAC;EACjEb,MAAM,CAAC8B,GAAG,CAAC;EACXlB,IAAI,CAAC0C,KAAK,CAAC;EACX,MAAMM,KAAK,GAAGJ,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAClD,MAAM;EAClC,MAAMmD,CAAC,GAAGxB,IAAI,CAACC,IAAI,CAAC,CAACqB,KAAK,GAAGZ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EACtC,MAAMe,YAAY,GAAGT,KAAK,GAAGG,CAAC,GAAGK,CAAC;EAClC,IAAIE,GAAG,CAAC,CAAC;EACT,IAAIL,MAAM,KAAK,KAAK,EAAE;IACpBK,GAAG,GAAGnC,kBAAkB,CAACC,GAAG,EAAEF,GAAG,EAAEmC,YAAY,EAAEL,IAAI,CAAC;EACxD,CAAC,MAAM,IAAIC,MAAM,KAAK,KAAK,EAAE;IAC3BK,GAAG,GAAGjB,kBAAkB,CAACjB,GAAG,EAAEF,GAAG,EAAEmC,YAAY,EAAEf,CAAC,EAAEU,IAAI,CAAC;EAC3D,CAAC,MAAM,IAAIC,MAAM,KAAK,gBAAgB,EAAE;IACtC;IACAK,GAAG,GAAGlC,GAAG;EACX,CAAC,MAAM;IACL,MAAM,IAAIjB,KAAK,CAAC,+BAA+B,CAAC;EAClD;EACA,MAAMoD,CAAC,GAAG,IAAIlD,KAAK,CAACuC,KAAK,CAAC;EAC1B,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,EAAEpC,CAAC,EAAE,EAAE;IAC9B,MAAMgD,CAAC,GAAG,IAAInD,KAAK,CAAC0C,CAAC,CAAC;IACtB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,CAAC,EAAEU,CAAC,EAAE,EAAE;MAC1B,MAAMC,UAAU,GAAGN,CAAC,IAAIK,CAAC,GAAGjD,CAAC,GAAGuC,CAAC,CAAC;MAClC,MAAMY,EAAE,GAAGL,GAAG,CAACM,QAAQ,CAACF,UAAU,EAAEA,UAAU,GAAGN,CAAC,CAAC;MACnDI,CAAC,CAACC,CAAC,CAAC,GAAG5D,GAAG,CAACC,KAAK,CAAC6D,EAAE,CAAC,EAAEb,CAAC,CAAC;IAC1B;IACAS,CAAC,CAAC/C,CAAC,CAAC,GAAGgD,CAAC;EACV;EACA,OAAOD,CAAC;AACV;AAIA,OAAM,SAAUM,UAAUA,CAAyBC,KAAQ,EAAEC,GAAe;EAC1E;EACA,MAAMC,KAAK,GAAGD,GAAG,CAACA,GAAG,CAAEvD,CAAC,IAAKH,KAAK,CAACC,IAAI,CAACE,CAAC,CAAC,CAACyD,OAAO,EAAE,CAAC;EACrD,OAAO,CAACC,CAAI,EAAEC,CAAI,KAAI;IACpB,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGP,KAAK,CAACD,GAAG,CAAES,GAAG,IACrCA,GAAG,CAACC,MAAM,CAAC,CAACC,GAAG,EAAElE,CAAC,KAAKsD,KAAK,CAACa,GAAG,CAACb,KAAK,CAACc,GAAG,CAACF,GAAG,EAAER,CAAC,CAAC,EAAE1D,CAAC,CAAC,CAAC,CACxD;IACD;IACA;IACA;IACA;IACA,MAAM,CAACqE,MAAM,EAAEC,MAAM,CAAC,GAAGlF,aAAa,CAACkE,KAAK,EAAE,CAACO,EAAE,EAAEE,EAAE,CAAC,EAAE,IAAI,CAAC;IAC7DL,CAAC,GAAGJ,KAAK,CAACc,GAAG,CAACR,EAAE,EAAES,MAAM,CAAC,CAAC,CAAC;IAC3BV,CAAC,GAAGL,KAAK,CAACc,GAAG,CAACT,CAAC,EAAEL,KAAK,CAACc,GAAG,CAACN,EAAE,EAAEQ,MAAM,CAAC,CAAC,CAAC,CAAC;IACzC,OAAO;MAAEZ,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC;AACH;AA0CA,OAAO,MAAMY,WAAW,GAAepF,WAAW,CAAC,eAAe,CAAC;AAEnE;AACA,OAAM,SAAUqF,YAAYA,CAC1BC,KAA6B,EAC7BC,UAAyB,EACzBC,QAAkD;EAElD,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE,MAAM,IAAI/E,KAAK,CAAC,8BAA8B,CAAC;EACrF,SAAS4D,GAAGA,CAACqB,GAAa;IACxB,OAAOH,KAAK,CAACI,UAAU,CAACH,UAAU,CAACE,GAAG,CAAC,CAAC;EAC1C;EACA,SAASE,KAAKA,CAACC,OAAoB;IACjC,MAAMC,CAAC,GAAGD,OAAO,CAACE,aAAa,EAAE;IACjC,IAAID,CAAC,CAACE,MAAM,CAACT,KAAK,CAACU,IAAI,CAAC,EAAE,OAAOV,KAAK,CAACU,IAAI,CAAC,CAAC;IAC7CH,CAAC,CAACI,cAAc,EAAE;IAClB,OAAOJ,CAAC;EACV;EAEA,OAAO;IACLL,QAAQ;IAERU,WAAWA,CAACzE,GAAe,EAAEyB,OAAsB;MACjD,MAAMiD,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEb,QAAQ,EAAEtC,OAAO,CAAC;MACjD,MAAMU,CAAC,GAAGZ,aAAa,CAACvB,GAAG,EAAE,CAAC,EAAE0E,IAAI,CAAC;MACrC,MAAMG,EAAE,GAAGlC,GAAG,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,MAAM2C,EAAE,GAAGnC,GAAG,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,OAAO+B,KAAK,CAACW,EAAE,CAACtB,GAAG,CAACuB,EAAE,CAAC,CAAC;IAC1B,CAAC;IACDC,aAAaA,CAAC/E,GAAe,EAAEyB,OAAsB;MACnD,MAAMuD,OAAO,GAAGjB,QAAQ,CAACkB,SAAS,GAAG;QAAEnF,GAAG,EAAEiE,QAAQ,CAACkB;MAAS,CAAE,GAAG,EAAE;MACrE,MAAMP,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEb,QAAQ,EAAEiB,OAAO,EAAEvD,OAAO,CAAC;MAC1D,MAAMU,CAAC,GAAGZ,aAAa,CAACvB,GAAG,EAAE,CAAC,EAAE0E,IAAI,CAAC;MACrC,MAAMG,EAAE,GAAGlC,GAAG,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,OAAO+B,KAAK,CAACW,EAAE,CAAC;IAClB,CAAC;IACD;IACAf,UAAUA,CAACoB,OAAiB;MAC1B,IAAI,CAACjG,KAAK,CAACkG,OAAO,CAACD,OAAO,CAAC,EAAE,MAAM,IAAInG,KAAK,CAAC,2BAA2B,CAAC;MACzE,KAAK,MAAMK,CAAC,IAAI8F,OAAO,EACrB,IAAI,OAAO9F,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIL,KAAK,CAAC,2BAA2B,CAAC;MACzE,OAAOmF,KAAK,CAACvB,GAAG,CAACuC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;IACA;IACAE,YAAYA,CAACpF,GAAe,EAAEyB,OAAsB;MAClD;MACA,MAAM4D,CAAC,GAAGxB,KAAK,CAACyB,EAAE,CAACC,KAAK;MACxB,MAAMb,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEb,QAAQ,EAAE;QAAErC,CAAC,EAAE2D,CAAC;QAAE1D,CAAC,EAAE,CAAC;QAAE7B,GAAG,EAAE6D;MAAW,CAAE,EAAElC,OAAO,CAAC;MACnF,OAAOF,aAAa,CAACvB,GAAG,EAAE,CAAC,EAAE0E,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}