{"ast": null, "code": "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n// Cast array to different type\nexport const u8 = arr => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = arr => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\nfunction isBytes(a) {\n  return a instanceof Uint8Array || a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array';\n}\n// Cast array to view\nexport const createView = arr => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => word << 32 - shift | word >>> shift;\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\n// Other libraries would silently corrupt the data instead of throwing an error,\n// when they don't support it.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE) throw new Error('Non little-endian hardware is not supported');\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */Array.from({\n  length: 256\n}, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n  if (!isBytes(bytes)) throw new Error('Uint8Array expected');\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = {\n  _0: 48,\n  _9: 57,\n  _A: 65,\n  _F: 70,\n  _a: 97,\n  _f: 102\n};\nfunction asciiToBase16(char) {\n  if (char >= asciis._0 && char <= asciis._9) return char - asciis._0;\n  if (char >= asciis._A && char <= asciis._F) return char - (asciis._A - 10);\n  if (char >= asciis._a && char <= asciis._f) return char - (asciis._a - 10);\n  return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2;\n  }\n  return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => {};\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  if (!isBytes(data)) throw new Error(`expected Uint8Array, got ${typeof data}`);\n  return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    if (!isBytes(a)) throw new Error('Uint8Array expected');\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n// For runtime check if class implements interface\nexport class Hash {\n  // Safe version that clones internal state\n  clone() {\n    return this._cloneInto();\n  }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n  if (opts !== undefined && toStr.call(opts) !== '[object Object]') throw new Error('Options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged;\n}\nexport function wrapConstructor(hashCons) {\n  const hashC = msg => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n  const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({});\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = opts => hashCons(opts);\n  return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n  const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({});\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = opts => hashCons(opts);\n  return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}", "map": {"version": 3, "names": ["crypto", "u8", "arr", "Uint8Array", "buffer", "byteOffset", "byteLength", "u32", "Uint32Array", "Math", "floor", "isBytes", "a", "constructor", "name", "createView", "DataView", "rotr", "word", "shift", "isLE", "Error", "hexes", "Array", "from", "length", "_", "i", "toString", "padStart", "bytesToHex", "bytes", "hex", "asciis", "_0", "_9", "_A", "_F", "_a", "_f", "asciiToBase16", "char", "hexToBytes", "hl", "al", "array", "ai", "hi", "n1", "charCodeAt", "n2", "undefined", "nextTick", "asyncLoop", "iters", "tick", "cb", "ts", "Date", "now", "diff", "utf8ToBytes", "str", "TextEncoder", "encode", "toBytes", "data", "concatBytes", "arrays", "sum", "res", "pad", "set", "Hash", "clone", "_cloneInto", "toStr", "checkOpts", "defaults", "opts", "call", "merged", "Object", "assign", "wrapConstructor", "hashCons", "hashC", "msg", "update", "digest", "tmp", "outputLen", "blockLen", "create", "wrapConstructorWithOpts", "wrapXOFConstructorWithOpts", "randomBytes", "bytesLength", "getRandomValues"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\aptos\\node_modules\\@noble\\hashes\\src\\utils.ts"], "sourcesContent": ["/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n// Cast array to different type\nexport const u8 = (arr: TypedArray) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr: TypedArray) =>\n  new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n\nfunction isBytes(a: unknown): a is Uint8Array {\n  return (\n    a instanceof Uint8Array ||\n    (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array')\n  );\n}\n\n// Cast array to view\nexport const createView = (arr: TypedArray) =>\n  new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word: number, shift: number) => (word << (32 - shift)) | (word >>> shift);\n\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\n// Other libraries would silently corrupt the data instead of throwing an error,\n// when they don't support it.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE) throw new Error('Non little-endian hardware is not supported');\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  if (!isBytes(bytes)) throw new Error('Uint8Array expected');\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, _A: 65, _F: 70, _a: 97, _f: 102 } as const;\nfunction asciiToBase16(char: number): number | undefined {\n  if (char >= asciis._0 && char <= asciis._9) return char - asciis._0;\n  if (char >= asciis._A && char <= asciis._F) return char - (asciis._A - 10);\n  if (char >= asciis._a && char <= asciis._f) return char - (asciis._a - 10);\n  return;\n}\n\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2;\n  }\n  return array;\n}\n\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => {};\n\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters: number, tick: number, cb: (i: number) => void) {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols in both browsers and Node.js since v11\n// See https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\nexport type Input = Uint8Array | string;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  if (!isBytes(data)) throw new Error(`expected Uint8Array, got ${typeof data}`);\n  return data;\n}\n\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    if (!isBytes(a)) throw new Error('Uint8Array expected');\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\n// For runtime check if class implements interface\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\nconst toStr = {}.toString;\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n    throw new Error('Options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\nexport type CHash = ReturnType<typeof wrapConstructor>;\n\nexport function wrapConstructor<T extends Hash<T>>(hashCons: () => Hash<T>) {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function wrapConstructorWithOpts<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n) {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function wrapXOFConstructorWithOpts<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n) {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n"], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,sBAAsB;AAM7C;AACA,OAAO,MAAMC,EAAE,GAAIC,GAAe,IAAK,IAAIC,UAAU,CAACD,GAAG,CAACE,MAAM,EAAEF,GAAG,CAACG,UAAU,EAAEH,GAAG,CAACI,UAAU,CAAC;AACjG,OAAO,MAAMC,GAAG,GAAIL,GAAe,IACjC,IAAIM,WAAW,CAACN,GAAG,CAACE,MAAM,EAAEF,GAAG,CAACG,UAAU,EAAEI,IAAI,CAACC,KAAK,CAACR,GAAG,CAACI,UAAU,GAAG,CAAC,CAAC,CAAC;AAE7E,SAASK,OAAOA,CAACC,CAAU;EACzB,OACEA,CAAC,YAAYT,UAAU,IACtBS,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,WAAW,CAACC,IAAI,KAAK,YAAa;AAE/E;AAEA;AACA,OAAO,MAAMC,UAAU,GAAIb,GAAe,IACxC,IAAIc,QAAQ,CAACd,GAAG,CAACE,MAAM,EAAEF,GAAG,CAACG,UAAU,EAAEH,GAAG,CAACI,UAAU,CAAC;AAE1D;AACA,OAAO,MAAMW,IAAI,GAAGA,CAACC,IAAY,EAAEC,KAAa,KAAMD,IAAI,IAAK,EAAE,GAAGC,KAAM,GAAKD,IAAI,KAAKC,KAAM;AAE9F;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG,IAAIjB,UAAU,CAAC,IAAIK,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;AACpF,IAAI,CAACgB,IAAI,EAAE,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;AAEzE;AACA,MAAMC,KAAK,GAAG,eAAgBC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAC7DA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAChC;AACD;;;AAGA,OAAM,SAAUC,UAAUA,CAACC,KAAiB;EAC1C,IAAI,CAACpB,OAAO,CAACoB,KAAK,CAAC,EAAE,MAAM,IAAIV,KAAK,CAAC,qBAAqB,CAAC;EAC3D;EACA,IAAIW,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAACN,MAAM,EAAEE,CAAC,EAAE,EAAE;IACrCK,GAAG,IAAIV,KAAK,CAACS,KAAK,CAACJ,CAAC,CAAC,CAAC;EACxB;EACA,OAAOK,GAAG;AACZ;AAEA;AACA,MAAMC,MAAM,GAAG;EAAEC,EAAE,EAAE,EAAE;EAAEC,EAAE,EAAE,EAAE;EAAEC,EAAE,EAAE,EAAE;EAAEC,EAAE,EAAE,EAAE;EAAEC,EAAE,EAAE,EAAE;EAAEC,EAAE,EAAE;AAAG,CAAW;AAC3E,SAASC,aAAaA,CAACC,IAAY;EACjC,IAAIA,IAAI,IAAIR,MAAM,CAACC,EAAE,IAAIO,IAAI,IAAIR,MAAM,CAACE,EAAE,EAAE,OAAOM,IAAI,GAAGR,MAAM,CAACC,EAAE;EACnE,IAAIO,IAAI,IAAIR,MAAM,CAACG,EAAE,IAAIK,IAAI,IAAIR,MAAM,CAACI,EAAE,EAAE,OAAOI,IAAI,IAAIR,MAAM,CAACG,EAAE,GAAG,EAAE,CAAC;EAC1E,IAAIK,IAAI,IAAIR,MAAM,CAACK,EAAE,IAAIG,IAAI,IAAIR,MAAM,CAACM,EAAE,EAAE,OAAOE,IAAI,IAAIR,MAAM,CAACK,EAAE,GAAG,EAAE,CAAC;EAC1E;AACF;AAEA;;;AAGA,OAAM,SAAUI,UAAUA,CAACV,GAAW;EACpC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIX,KAAK,CAAC,2BAA2B,GAAG,OAAOW,GAAG,CAAC;EACtF,MAAMW,EAAE,GAAGX,GAAG,CAACP,MAAM;EACrB,MAAMmB,EAAE,GAAGD,EAAE,GAAG,CAAC;EACjB,IAAIA,EAAE,GAAG,CAAC,EAAE,MAAM,IAAItB,KAAK,CAAC,yDAAyD,GAAGsB,EAAE,CAAC;EAC3F,MAAME,KAAK,GAAG,IAAI1C,UAAU,CAACyC,EAAE,CAAC;EAChC,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAGF,EAAE,EAAEE,EAAE,EAAE,EAAEC,EAAE,IAAI,CAAC,EAAE;IAC/C,MAAMC,EAAE,GAAGR,aAAa,CAACR,GAAG,CAACiB,UAAU,CAACF,EAAE,CAAC,CAAC;IAC5C,MAAMG,EAAE,GAAGV,aAAa,CAACR,GAAG,CAACiB,UAAU,CAACF,EAAE,GAAG,CAAC,CAAC,CAAC;IAChD,IAAIC,EAAE,KAAKG,SAAS,IAAID,EAAE,KAAKC,SAAS,EAAE;MACxC,MAAMV,IAAI,GAAGT,GAAG,CAACe,EAAE,CAAC,GAAGf,GAAG,CAACe,EAAE,GAAG,CAAC,CAAC;MAClC,MAAM,IAAI1B,KAAK,CAAC,8CAA8C,GAAGoB,IAAI,GAAG,aAAa,GAAGM,EAAE,CAAC;IAC7F;IACAF,KAAK,CAACC,EAAE,CAAC,GAAGE,EAAE,GAAG,EAAE,GAAGE,EAAE;EAC1B;EACA,OAAOL,KAAK;AACd;AAEA;AACA;AACA;AACA,OAAO,MAAMO,QAAQ,GAAG,MAAAA,CAAA,KAAW,CAAE,CAAC;AAEtC;AACA,OAAO,eAAeC,SAASA,CAACC,KAAa,EAAEC,IAAY,EAAEC,EAAuB;EAClF,IAAIC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;EACnB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,KAAK,EAAE3B,CAAC,EAAE,EAAE;IAC9B6B,EAAE,CAAC7B,CAAC,CAAC;IACL;IACA,MAAMiC,IAAI,GAAGF,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;IAC5B,IAAIG,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGL,IAAI,EAAE;IAC9B,MAAMH,QAAQ,EAAE;IAChBK,EAAE,IAAIG,IAAI;EACZ;AACF;AAMA;;;AAGA,OAAM,SAAUC,WAAWA,CAACC,GAAW;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIzC,KAAK,CAAC,oCAAoC,OAAOyC,GAAG,EAAE,CAAC;EAC9F,OAAO,IAAI3D,UAAU,CAAC,IAAI4D,WAAW,EAAE,CAACC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD;AAGA;;;;;AAKA,OAAM,SAAUG,OAAOA,CAACC,IAAW;EACjC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAGL,WAAW,CAACK,IAAI,CAAC;EACtD,IAAI,CAACvD,OAAO,CAACuD,IAAI,CAAC,EAAE,MAAM,IAAI7C,KAAK,CAAC,4BAA4B,OAAO6C,IAAI,EAAE,CAAC;EAC9E,OAAOA,IAAI;AACb;AAEA;;;AAGA,OAAM,SAAUC,WAAWA,CAAC,GAAGC,MAAoB;EACjD,IAAIC,GAAG,GAAG,CAAC;EACX,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,MAAM,CAAC3C,MAAM,EAAEE,CAAC,EAAE,EAAE;IACtC,MAAMf,CAAC,GAAGwD,MAAM,CAACzC,CAAC,CAAC;IACnB,IAAI,CAAChB,OAAO,CAACC,CAAC,CAAC,EAAE,MAAM,IAAIS,KAAK,CAAC,qBAAqB,CAAC;IACvDgD,GAAG,IAAIzD,CAAC,CAACa,MAAM;EACjB;EACA,MAAM6C,GAAG,GAAG,IAAInE,UAAU,CAACkE,GAAG,CAAC;EAC/B,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAE4C,GAAG,GAAG,CAAC,EAAE5C,CAAC,GAAGyC,MAAM,CAAC3C,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC/C,MAAMf,CAAC,GAAGwD,MAAM,CAACzC,CAAC,CAAC;IACnB2C,GAAG,CAACE,GAAG,CAAC5D,CAAC,EAAE2D,GAAG,CAAC;IACfA,GAAG,IAAI3D,CAAC,CAACa,MAAM;EACjB;EACA,OAAO6C,GAAG;AACZ;AAEA;AACA,OAAM,MAAgBG,IAAI;EAqBxB;EACAC,KAAKA,CAAA;IACH,OAAO,IAAI,CAACC,UAAU,EAAE;EAC1B;;AAcF,MAAMC,KAAK,GAAG,EAAE,CAAChD,QAAQ;AAEzB,OAAM,SAAUiD,SAASA,CACvBC,QAAY,EACZC,IAAS;EAET,IAAIA,IAAI,KAAK5B,SAAS,IAAIyB,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC,KAAK,iBAAiB,EAC9D,MAAM,IAAI1D,KAAK,CAAC,uCAAuC,CAAC;EAC1D,MAAM4D,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACL,QAAQ,EAAEC,IAAI,CAAC;EAC5C,OAAOE,MAAiB;AAC1B;AAIA,OAAM,SAAUG,eAAeA,CAAoBC,QAAuB;EACxE,MAAMC,KAAK,GAAIC,GAAU,IAAiBF,QAAQ,EAAE,CAACG,MAAM,CAACvB,OAAO,CAACsB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAClF,MAAMC,GAAG,GAAGL,QAAQ,EAAE;EACtBC,KAAK,CAACK,SAAS,GAAGD,GAAG,CAACC,SAAS;EAC/BL,KAAK,CAACM,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7BN,KAAK,CAACO,MAAM,GAAG,MAAMR,QAAQ,EAAE;EAC/B,OAAOC,KAAK;AACd;AAEA,OAAM,SAAUQ,uBAAuBA,CACrCT,QAA+B;EAE/B,MAAMC,KAAK,GAAGA,CAACC,GAAU,EAAER,IAAQ,KAAiBM,QAAQ,CAACN,IAAI,CAAC,CAACS,MAAM,CAACvB,OAAO,CAACsB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAChG,MAAMC,GAAG,GAAGL,QAAQ,CAAC,EAAO,CAAC;EAC7BC,KAAK,CAACK,SAAS,GAAGD,GAAG,CAACC,SAAS;EAC/BL,KAAK,CAACM,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7BN,KAAK,CAACO,MAAM,GAAId,IAAO,IAAKM,QAAQ,CAACN,IAAI,CAAC;EAC1C,OAAOO,KAAK;AACd;AAEA,OAAM,SAAUS,0BAA0BA,CACxCV,QAAkC;EAElC,MAAMC,KAAK,GAAGA,CAACC,GAAU,EAAER,IAAQ,KAAiBM,QAAQ,CAACN,IAAI,CAAC,CAACS,MAAM,CAACvB,OAAO,CAACsB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAChG,MAAMC,GAAG,GAAGL,QAAQ,CAAC,EAAO,CAAC;EAC7BC,KAAK,CAACK,SAAS,GAAGD,GAAG,CAACC,SAAS;EAC/BL,KAAK,CAACM,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7BN,KAAK,CAACO,MAAM,GAAId,IAAO,IAAKM,QAAQ,CAACN,IAAI,CAAC;EAC1C,OAAOO,KAAK;AACd;AAEA;;;AAGA,OAAM,SAAUU,WAAWA,CAACC,WAAW,GAAG,EAAE;EAC1C,IAAIjG,MAAM,IAAI,OAAOA,MAAM,CAACkG,eAAe,KAAK,UAAU,EAAE;IAC1D,OAAOlG,MAAM,CAACkG,eAAe,CAAC,IAAI/F,UAAU,CAAC8F,WAAW,CAAC,CAAC;EAC5D;EACA,MAAM,IAAI5E,KAAK,CAAC,wCAAwC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}