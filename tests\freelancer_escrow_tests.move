#[test_only]
module escrow_platform::freelancer_escrow_tests {
    use std::signer;
    use std::debug;
    use std::string;
    use aptos_framework::account;
    use aptos_framework::aptos_coin::AptosCoin;
    use aptos_framework::coin;
    use aptos_framework::aptos_coin;
    use aptos_framework::timestamp;
    use escrow_platform::freelancer_escrow;

    #[test_only]
    fun setup() : (signer, signer, signer) {
        let client = account::create_account_for_test(@0x100);
        let freelancer = account::create_account_for_test(@0x200);
        let admin = account::create_account_for_test(@escrow_platform);

        // Initialize AptosCoin for testing with the framework account
        let framework = account::create_account_for_test(@0x1);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(&framework);

        // Initialize timestamp for testing
        timestamp::set_time_has_started_for_testing(&framework);

        // Register AptosCoin
        coin::register<AptosCoin>(&client);
        coin::register<AptosCoin>(&freelancer);
        coin::register<AptosCoin>(&admin);

        // Give client test balance
        let coins = coin::mint<AptosCoin>(10_000_000, &mint_cap);
        coin::deposit(signer::address_of(&client), coins);

        // Clean up capabilities
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);

        // Initialize escrow platform (once, by admin)
        freelancer_escrow::initialize(&admin);

        (client, freelancer, admin)
    }

    #[test]
    fun test_create_and_fund_escrow() {
        let (client, freelancer, admin) = setup();

        // Client creates escrow
        freelancer_escrow::create_escrow(
            &client,
            signer::address_of(&freelancer),
            string::utf8(b"Website Development"),
            string::utf8(b"Build a responsive website"),
            string::utf8(b"Web Dev"),
            5_000_000,  // 5 APT
            9999999999  // dummy deadline
        );

        // Fund escrow (escrow_id = 1 since it's the first one)
        freelancer_escrow::fund_escrow(
            &client,
            1
        );

        debug::print(&b"Escrow created and funded successfully");
    }

    #[test]
    fun test_approve_and_release() {
        let (client, freelancer, admin) = setup();

        freelancer_escrow::create_escrow(
            &client,
            signer::address_of(&freelancer),
            string::utf8(b"Logo Design"),
            string::utf8(b"Create a new logo"),
            string::utf8(b"Design"),
            3_000_000,
            9999999999
        );

        freelancer_escrow::fund_escrow(&client, 1);

        // Freelancer starts work
        freelancer_escrow::start_work(&freelancer, 1);

        // Freelancer submits work
        freelancer_escrow::submit_work(&freelancer, 1);

        // Client approves work → funds released
        freelancer_escrow::approve_work(&client, 1);

        debug::print(&b"Escrow approved and funds released");
    }

    #[test]
    fun test_cancel_escrow() {
        let (client, freelancer, admin) = setup();

        freelancer_escrow::create_escrow(
            &client,
            signer::address_of(&freelancer),
            string::utf8(b"Mobile App"),
            string::utf8(b"Develop a mobile app"),
            string::utf8(b"App Dev"),
            4_000_000,
            9999999999
        );

        // Cancel escrow before funding
        freelancer_escrow::cancel_escrow(
            &client,
            1,
            string::utf8(b"Client changed requirements")
        );

        debug::print(&b"Escrow cancelled successfully");
    }
}
