import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import Navigation from './components/layout/Navigation';
import WalletProvider from './components/layout/WalletProvider';
import { EscrowProvider } from './contexts/EscrowContext';
import BlockchainStatus from './pages/BlockchainStatus';
import ClientDashboard from './pages/ClientDashboard';
import FreelancerDashboard from './pages/FreelancerDashboard';
import Home from './pages/Home';

function App() {
  return (
    <WalletProvider>
      <EscrowProvider>
        <Router>
          <div className="min-h-screen bg-secondary-50">
            <Navigation />
            <main className="container mx-auto px-4 py-8">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/client" element={<ClientDashboard />} />
                <Route path="/freelancer" element={<FreelancerDashboard />} />
                <Route path="/blockchain" element={<BlockchainStatus />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </main>
          </div>
        </Router>
      </EscrowProvider>
    </WalletProvider>
  );
}

export default App;
