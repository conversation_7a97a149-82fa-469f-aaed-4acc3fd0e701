import React, { Suspense } from 'react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Navigation from './components/layout/Navigation';
import WalletProvider from './components/layout/WalletProvider';
import Loading from './components/ui/Loading';
import NotificationContainer from './components/ui/Notification';
import { EscrowProvider } from './contexts/EscrowContext';
import { NotificationProvider } from './contexts/NotificationContext';

// Lazy load pages for better performance
const Home = React.lazy(() => import('./pages/Home'));
const ClientDashboard = React.lazy(() => import('./pages/ClientDashboard'));
const FreelancerDashboard = React.lazy(() => import('./pages/FreelancerDashboard'));
const BlockchainStatus = React.lazy(() => import('./pages/BlockchainStatus'));

function App() {
  return (
    <ErrorBoundary>
      <NotificationProvider>
        <WalletProvider>
          <EscrowProvider>
            <Router>
              <div className="min-h-screen bg-gradient-to-br from-secondary-50 to-primary-50">
                <Navigation />
                <main className="container py-8">
                  <Suspense
                    fallback={
                      <Loading.Page text="Loading page..." className="min-h-96" />
                    }
                  >
                    <Routes>
                      <Route path="/" element={<Home />} />
                      <Route path="/client" element={<ClientDashboard />} />
                      <Route path="/freelancer" element={<FreelancerDashboard />} />
                      <Route path="/blockchain" element={<BlockchainStatus />} />
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </Suspense>
                </main>
                <NotificationContainer />
              </div>
            </Router>
          </EscrowProvider>
        </WalletProvider>
      </NotificationProvider>
    </ErrorBoundary>
  );
}

export default App;
