import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import BlockchainStatus from './components/BlockchainStatus';
import ClientDashboard from './components/ClientDashboard';
import FreelancerDashboard from './components/FreelancerDashboard';
import Home from './components/Home';
import Navigation from './components/Navigation';
import WalletProvider from './components/WalletProvider';
import { EscrowProvider } from './contexts/EscrowContext';

function App() {
  return (
    <WalletProvider>
      <EscrowProvider>
        <Router>
          <div className="min-h-screen bg-secondary-50">
            <Navigation />
            <main className="container mx-auto px-4 py-8">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/client" element={<ClientDashboard />} />
                <Route path="/freelancer" element={<FreelancerDashboard />} />
                <Route path="/blockchain" element={<BlockchainStatus />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </main>
          </div>
        </Router>
      </EscrowProvider>
    </WalletProvider>
  );
}

export default App;
