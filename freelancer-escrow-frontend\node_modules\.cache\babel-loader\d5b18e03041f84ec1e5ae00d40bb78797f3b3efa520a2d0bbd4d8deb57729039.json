{"ast": null, "code": "import { b as t } from \"./chunk-BCUSI3N6.mjs\";\nvar o = r => typeof r == \"string\" ? t.isValid(r).valid ? r : Buffer.from(r, \"utf8\") : r;\nexport { o as a };", "map": {"version": 3, "names": ["o", "r", "t", "<PERSON><PERSON><PERSON><PERSON>", "valid", "<PERSON><PERSON><PERSON>", "from", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\utils.ts"], "sourcesContent": ["import { HexInput } from \"../../types\";\nimport { Hex } from \"../hex\";\n\n/**\n * Helper function to convert a message to sign or to verify to a valid message input\n *\n * @param message a message as a string or Uint8Array\n *\n * @returns a valid HexInput - string or Uint8Array\n */\nexport const convertSigningMessage = (message: HexInput): HexInput => {\n  // if message is of type string, verify it is a valid Hex string\n  if (typeof message === \"string\") {\n    const isValid = Hex.isValid(message);\n    // If message is not a valid Hex string, convert it into a Buffer\n    if (!isValid.valid) {\n      return Buffer.from(message, \"utf8\");\n    }\n    // If message is a valid Hex string, return it\n    return message;\n  }\n  // message is a Uint8Array\n  return message;\n};\n"], "mappings": ";AAUO,IAAMA,CAAA,GAAyBC,CAAA,IAEhC,OAAOA,CAAA,IAAY,WACLC,CAAA,CAAIC,OAAA,CAAQF,CAAO,EAEtBG,KAAA,GAINH,CAAA,GAHEI,MAAA,CAAOC,IAAA,CAAKL,CAAA,EAAS,MAAM,IAM/BA,CAAA;AAAA,SAAAD,CAAA,IAAAO,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}