{"ast": null, "code": "import { a as r } from \"./chunk-A63SMUOU.mjs\";\nimport { b as t } from \"./chunk-BCUSI3N6.mjs\";\nvar e = class extends r {\n  toString() {\n    let s = this.bcsToBytes();\n    return t.fromHexInput(s).toString();\n  }\n};\nexport { e as a };", "map": {"version": 3, "names": ["e", "r", "toString", "s", "bcsToBytes", "t", "fromHexInput", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\proof.ts"], "sourcesContent": ["import { Serializable } from \"../../bcs\";\nimport { Hex } from \"../hex\";\n\n/**\n * An abstract representation of a crypto proof.\n * associated to a specific zero knowledge proof schemes e.g. Groth16, PLONK\n */\nexport abstract class Proof extends Serializable {\n  /**\n   * Get the proof as a hex string with a 0x prefix e.g. 0x123456...\n   */\n  toString(): string {\n    const bytes = this.bcsToBytes();\n    return Hex.fromHexInput(bytes).toString();\n  }\n}\n"], "mappings": ";;AAOO,IAAeA,CAAA,GAAf,cAA6BC,CAAa;EAI/CC,SAAA,EAAmB;IACjB,IAAMC,CAAA,GAAQ,KAAKC,UAAA,CAAW;IAC9B,OAAOC,CAAA,CAAIC,YAAA,CAAaH,CAAK,EAAED,QAAA,CAAS,CAC1C;EAAA;AACF;AAAA,SAAAF,CAAA,IAAAO,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}