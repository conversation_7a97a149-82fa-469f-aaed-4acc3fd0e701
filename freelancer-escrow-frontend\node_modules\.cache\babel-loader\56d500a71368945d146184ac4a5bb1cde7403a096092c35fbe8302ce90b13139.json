{"ast": null, "code": "import { hash as assertHash, number as assertNumber } from './_assert.js';\nimport { hmac } from './hmac.js';\nimport { createView, toBytes, checkOpts, asyncLoop } from './utils.js';\n// Common prologue and epilogue for sync/async functions\nfunction pbkdf2Init(hash, _password, _salt, _opts) {\n  assertHash(hash);\n  const opts = checkOpts({\n    dkLen: 32,\n    asyncTick: 10\n  }, _opts);\n  const {\n    c,\n    dkLen,\n    asyncTick\n  } = opts;\n  assertNumber(c);\n  assertNumber(dkLen);\n  assertNumber(asyncTick);\n  if (c < 1) throw new Error('PBKDF2: iterations (c) should be >= 1');\n  const password = toBytes(_password);\n  const salt = toBytes(_salt);\n  // DK = PBKDF2(PRF, Password, Salt, c, dkLen);\n  const DK = new Uint8Array(dkLen);\n  // U1 = PRF(Password, Salt + INT_32_BE(i))\n  const PRF = hmac.create(hash, password);\n  const PRFSalt = PRF._cloneInto().update(salt);\n  return {\n    c,\n    dkLen,\n    asyncTick,\n    DK,\n    PRF,\n    PRFSalt\n  };\n}\nfunction pbkdf2Output(PRF, PRFSalt, DK, prfW, u) {\n  PRF.destroy();\n  PRFSalt.destroy();\n  if (prfW) prfW.destroy();\n  u.fill(0);\n  return DK;\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function\n * @param hash - hash function that would be used e.g. sha256\n * @param password - password from which a derived key is generated\n * @param salt - cryptographic salt\n * @param opts - {c, dkLen} where c is work factor and dkLen is output message size\n */\nexport function pbkdf2(hash, password, salt, opts) {\n  const {\n    c,\n    dkLen,\n    DK,\n    PRF,\n    PRFSalt\n  } = pbkdf2Init(hash, password, salt, opts);\n  let prfW; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    for (let ui = 1; ui < c; ui++) {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    }\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\nexport async function pbkdf2Async(hash, password, salt, opts) {\n  const {\n    c,\n    dkLen,\n    asyncTick,\n    DK,\n    PRF,\n    PRFSalt\n  } = pbkdf2Init(hash, password, salt, opts);\n  let prfW; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    await asyncLoop(c - 1, asyncTick, () => {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    });\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}", "map": {"version": 3, "names": ["hash", "assertHash", "number", "assertNumber", "hmac", "createView", "toBytes", "checkOpts", "asyncLoop", "pbkdf2Init", "_password", "_salt", "_opts", "opts", "dkLen", "asyncTick", "c", "Error", "password", "salt", "DK", "Uint8Array", "PRF", "create", "PRFSalt", "_cloneInto", "update", "pbkdf2Output", "prfW", "u", "destroy", "fill", "pbkdf2", "arr", "view", "outputLen", "ti", "pos", "Ti", "subarray", "setInt32", "digestInto", "set", "length", "ui", "i", "pbkdf2Async"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\aptos\\node_modules\\@noble\\hashes\\src\\pbkdf2.ts"], "sourcesContent": ["import { hash as assertHash, number as assertNumber } from './_assert.js';\nimport { hmac } from './hmac.js';\nimport { Hash, CHash, Input, createView, toBytes, checkOpts, asyncLoop } from './utils.js';\n\n// PBKDF (RFC 2898)\nexport type Pbkdf2Opt = {\n  c: number; // Iterations\n  dkLen?: number; // Desired key length in bytes (Intended output length in octets of the derived key\n  asyncTick?: number; // Maximum time in ms for which async function can block execution\n};\n// Common prologue and epilogue for sync/async functions\nfunction pbkdf2Init(hash: CHash, _password: Input, _salt: Input, _opts: Pbkdf2Opt) {\n  assertHash(hash);\n  const opts = checkOpts({ dkLen: 32, asyncTick: 10 }, _opts);\n  const { c, dkLen, asyncTick } = opts;\n  assertNumber(c);\n  assertNumber(dkLen);\n  assertNumber(asyncTick);\n  if (c < 1) throw new Error('PBKDF2: iterations (c) should be >= 1');\n  const password = toBytes(_password);\n  const salt = toBytes(_salt);\n  // DK = PBKDF2(PRF, Password, Salt, c, dkLen);\n  const DK = new Uint8Array(dkLen);\n  // U1 = PRF(Password, Salt + INT_32_BE(i))\n  const PRF = hmac.create(hash, password);\n  const PRFSalt = PRF._cloneInto().update(salt);\n  return { c, dkLen, asyncTick, DK, PRF, PRFSalt };\n}\n\nfunction pbkdf2Output<T extends Hash<T>>(\n  PRF: Hash<T>,\n  PRFSalt: Hash<T>,\n  DK: Uint8Array,\n  prfW: Hash<T>,\n  u: Uint8Array\n) {\n  PRF.destroy();\n  PRFSalt.destroy();\n  if (prfW) prfW.destroy();\n  u.fill(0);\n  return DK;\n}\n\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function\n * @param hash - hash function that would be used e.g. sha256\n * @param password - password from which a derived key is generated\n * @param salt - cryptographic salt\n * @param opts - {c, dkLen} where c is work factor and dkLen is output message size\n */\nexport function pbkdf2(hash: CHash, password: Input, salt: Input, opts: Pbkdf2Opt) {\n  const { c, dkLen, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n  let prfW: any; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    for (let ui = 1; ui < c; ui++) {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    }\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n\nexport async function pbkdf2Async(hash: CHash, password: Input, salt: Input, opts: Pbkdf2Opt) {\n  const { c, dkLen, asyncTick, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n  let prfW: any; // Working copy\n  const arr = new Uint8Array(4);\n  const view = createView(arr);\n  const u = new Uint8Array(PRF.outputLen);\n  // DK = T1 + T2 + ⋯ + Tdklen/hlen\n  for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n    // Ti = F(Password, Salt, c, i)\n    const Ti = DK.subarray(pos, pos + PRF.outputLen);\n    view.setInt32(0, ti, false);\n    // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n    Ti.set(u.subarray(0, Ti.length));\n    await asyncLoop(c - 1, asyncTick, () => {\n      // Uc = PRF(Password, Uc−1)\n      PRF._cloneInto(prfW).update(u).digestInto(u);\n      for (let i = 0; i < Ti.length; i++) Ti[i] ^= u[i];\n    });\n  }\n  return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n"], "mappings": "AAAA,SAASA,IAAI,IAAIC,UAAU,EAAEC,MAAM,IAAIC,YAAY,QAAQ,cAAc;AACzE,SAASC,IAAI,QAAQ,WAAW;AAChC,SAA6BC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,QAAQ,YAAY;AAQ1F;AACA,SAASC,UAAUA,CAACT,IAAW,EAAEU,SAAgB,EAAEC,KAAY,EAAEC,KAAgB;EAC/EX,UAAU,CAACD,IAAI,CAAC;EAChB,MAAMa,IAAI,GAAGN,SAAS,CAAC;IAAEO,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAE,CAAE,EAAEH,KAAK,CAAC;EAC3D,MAAM;IAAEI,CAAC;IAAEF,KAAK;IAAEC;EAAS,CAAE,GAAGF,IAAI;EACpCV,YAAY,CAACa,CAAC,CAAC;EACfb,YAAY,CAACW,KAAK,CAAC;EACnBX,YAAY,CAACY,SAAS,CAAC;EACvB,IAAIC,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EACnE,MAAMC,QAAQ,GAAGZ,OAAO,CAACI,SAAS,CAAC;EACnC,MAAMS,IAAI,GAAGb,OAAO,CAACK,KAAK,CAAC;EAC3B;EACA,MAAMS,EAAE,GAAG,IAAIC,UAAU,CAACP,KAAK,CAAC;EAChC;EACA,MAAMQ,GAAG,GAAGlB,IAAI,CAACmB,MAAM,CAACvB,IAAI,EAAEkB,QAAQ,CAAC;EACvC,MAAMM,OAAO,GAAGF,GAAG,CAACG,UAAU,EAAE,CAACC,MAAM,CAACP,IAAI,CAAC;EAC7C,OAAO;IAAEH,CAAC;IAAEF,KAAK;IAAEC,SAAS;IAAEK,EAAE;IAAEE,GAAG;IAAEE;EAAO,CAAE;AAClD;AAEA,SAASG,YAAYA,CACnBL,GAAY,EACZE,OAAgB,EAChBJ,EAAc,EACdQ,IAAa,EACbC,CAAa;EAEbP,GAAG,CAACQ,OAAO,EAAE;EACbN,OAAO,CAACM,OAAO,EAAE;EACjB,IAAIF,IAAI,EAAEA,IAAI,CAACE,OAAO,EAAE;EACxBD,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACT,OAAOX,EAAE;AACX;AAEA;;;;;;;AAOA,OAAM,SAAUY,MAAMA,CAAChC,IAAW,EAAEkB,QAAe,EAAEC,IAAW,EAAEN,IAAe;EAC/E,MAAM;IAAEG,CAAC;IAAEF,KAAK;IAAEM,EAAE;IAAEE,GAAG;IAAEE;EAAO,CAAE,GAAGf,UAAU,CAACT,IAAI,EAAEkB,QAAQ,EAAEC,IAAI,EAAEN,IAAI,CAAC;EAC7E,IAAIe,IAAS,CAAC,CAAC;EACf,MAAMK,GAAG,GAAG,IAAIZ,UAAU,CAAC,CAAC,CAAC;EAC7B,MAAMa,IAAI,GAAG7B,UAAU,CAAC4B,GAAG,CAAC;EAC5B,MAAMJ,CAAC,GAAG,IAAIR,UAAU,CAACC,GAAG,CAACa,SAAS,CAAC;EACvC;EACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGvB,KAAK,EAAEsB,EAAE,EAAE,EAAEC,GAAG,IAAIf,GAAG,CAACa,SAAS,EAAE;IACjE;IACA,MAAMG,EAAE,GAAGlB,EAAE,CAACmB,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGf,GAAG,CAACa,SAAS,CAAC;IAChDD,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAEJ,EAAE,EAAE,KAAK,CAAC;IAC3B;IACA;IACA,CAACR,IAAI,GAAGJ,OAAO,CAACC,UAAU,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACO,GAAG,CAAC,CAACQ,UAAU,CAACZ,CAAC,CAAC;IAC3DS,EAAE,CAACI,GAAG,CAACb,CAAC,CAACU,QAAQ,CAAC,CAAC,EAAED,EAAE,CAACK,MAAM,CAAC,CAAC;IAChC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG5B,CAAC,EAAE4B,EAAE,EAAE,EAAE;MAC7B;MACAtB,GAAG,CAACG,UAAU,CAACG,IAAI,CAAC,CAACF,MAAM,CAACG,CAAC,CAAC,CAACY,UAAU,CAACZ,CAAC,CAAC;MAC5C,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACK,MAAM,EAAEE,CAAC,EAAE,EAAEP,EAAE,CAACO,CAAC,CAAC,IAAIhB,CAAC,CAACgB,CAAC,CAAC;IACnD;EACF;EACA,OAAOlB,YAAY,CAACL,GAAG,EAAEE,OAAO,EAAEJ,EAAE,EAAEQ,IAAI,EAAEC,CAAC,CAAC;AAChD;AAEA,OAAO,eAAeiB,WAAWA,CAAC9C,IAAW,EAAEkB,QAAe,EAAEC,IAAW,EAAEN,IAAe;EAC1F,MAAM;IAAEG,CAAC;IAAEF,KAAK;IAAEC,SAAS;IAAEK,EAAE;IAAEE,GAAG;IAAEE;EAAO,CAAE,GAAGf,UAAU,CAACT,IAAI,EAAEkB,QAAQ,EAAEC,IAAI,EAAEN,IAAI,CAAC;EACxF,IAAIe,IAAS,CAAC,CAAC;EACf,MAAMK,GAAG,GAAG,IAAIZ,UAAU,CAAC,CAAC,CAAC;EAC7B,MAAMa,IAAI,GAAG7B,UAAU,CAAC4B,GAAG,CAAC;EAC5B,MAAMJ,CAAC,GAAG,IAAIR,UAAU,CAACC,GAAG,CAACa,SAAS,CAAC;EACvC;EACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGvB,KAAK,EAAEsB,EAAE,EAAE,EAAEC,GAAG,IAAIf,GAAG,CAACa,SAAS,EAAE;IACjE;IACA,MAAMG,EAAE,GAAGlB,EAAE,CAACmB,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGf,GAAG,CAACa,SAAS,CAAC;IAChDD,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAEJ,EAAE,EAAE,KAAK,CAAC;IAC3B;IACA;IACA,CAACR,IAAI,GAAGJ,OAAO,CAACC,UAAU,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACO,GAAG,CAAC,CAACQ,UAAU,CAACZ,CAAC,CAAC;IAC3DS,EAAE,CAACI,GAAG,CAACb,CAAC,CAACU,QAAQ,CAAC,CAAC,EAAED,EAAE,CAACK,MAAM,CAAC,CAAC;IAChC,MAAMnC,SAAS,CAACQ,CAAC,GAAG,CAAC,EAAED,SAAS,EAAE,MAAK;MACrC;MACAO,GAAG,CAACG,UAAU,CAACG,IAAI,CAAC,CAACF,MAAM,CAACG,CAAC,CAAC,CAACY,UAAU,CAACZ,CAAC,CAAC;MAC5C,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACK,MAAM,EAAEE,CAAC,EAAE,EAAEP,EAAE,CAACO,CAAC,CAAC,IAAIhB,CAAC,CAACgB,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;EACA,OAAOlB,YAAY,CAACL,GAAG,EAAEE,OAAO,EAAEJ,EAAE,EAAEQ,IAAI,EAAEC,CAAC,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}