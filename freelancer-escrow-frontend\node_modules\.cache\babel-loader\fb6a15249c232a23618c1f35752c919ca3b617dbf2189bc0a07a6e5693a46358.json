{"ast": null, "code": "import { c as T, d as c } from \"./chunk-JPDT6E3B.mjs\";\nimport { f as A, g as a } from \"./chunk-YE5B2S5L.mjs\";\nimport { sha3_256 as y } from \"@noble/hashes/sha3\";\nfunction d(e) {\n  return e.feePayerAddress ? new c(e.rawTransaction, e.secondarySignerAddresses ?? [], e.feePayerAddress) : e.secondarySignerAddresses ? new T(e.rawTransaction, e.secondarySignerAddresses) : e.rawTransaction;\n}\nfunction n(e, r) {\n  let i = y.create();\n  if (!r.startsWith(\"APTOS::\")) throw new Error(`Domain separator needs to start with 'APTOS::'.  Provided - ${r}`);\n  i.update(r);\n  let s = i.digest(),\n    o = e,\n    t = new Uint8Array(s.length + o.length);\n  return t.set(s), t.set(o, s.length), t;\n}\nfunction u(e) {\n  return n(e.bcsToBytes(), `APTOS::${e.constructor.name}`);\n}\nfunction S(e) {\n  let r = d(e);\n  return e.feePayerAddress ? n(r.bcsToBytes(), a) : e.secondarySignerAddresses ? n(r.bcsToBytes(), a) : n(r.bcsToBytes(), A);\n}\nexport { d as a, n as b, u as c, S as d };", "map": {"version": 3, "names": ["sha3_256", "y", "d", "e", "feePayer<PERSON>dd<PERSON>", "c", "rawTransaction", "secondarySignerAddresses", "T", "n", "r", "i", "create", "startsWith", "Error", "update", "s", "digest", "o", "t", "Uint8Array", "length", "set", "u", "bcsToBytes", "constructor", "name", "S", "a", "A", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\transactionBuilder\\signingMessage.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This file handles the generation of the signing message.\n */\nimport { sha3_256 as sha3Hash } from \"@noble/hashes/sha3\";\nimport { RAW_TRANSACTION_SALT, RAW_TRANSACTION_WITH_DATA_SALT } from \"../../utils/const\";\nimport { FeePayerRawTransaction, MultiAgentRawTransaction } from \"../instances\";\nimport { AnyRawTransaction, AnyRawTransactionInstance } from \"../types\";\nimport { Serializable } from \"../../bcs\";\n\n/**\n * Derive the raw transaction type - FeePayerRawTransaction or MultiAgentRawTransaction or RawTransaction\n *\n * @param transaction A aptos transaction type\n *\n * @returns FeePayerRawTransaction | MultiAgentRawTransaction | RawTransaction\n */\nexport function deriveTransactionType(transaction: AnyRawTransaction): AnyRawTransactionInstance {\n  if (transaction.feePayerAddress) {\n    return new FeePayerRawTransaction(\n      transaction.rawTransaction,\n      transaction.secondarySignerAddresses ?? [],\n      transaction.feePayerAddress,\n    );\n  }\n  if (transaction.secondarySignerAddresses) {\n    return new MultiAgentRawTransaction(transaction.rawTransaction, transaction.secondarySignerAddresses);\n  }\n\n  return transaction.rawTransaction;\n}\n\n/**\n * Generates the 'signing message' form of a message to be signed.\n *\n * @param bytes The byte representation of the message to be signed and sent to the chain\n * @param domainSeparator A domain separator that starts with 'APTOS::'\n *\n * @returns The Uint8Array of the signing message\n */\nexport function generateSigningMessage(bytes: Uint8Array, domainSeparator: string): Uint8Array {\n  const hash = sha3Hash.create();\n\n  if (!domainSeparator.startsWith(\"APTOS::\")) {\n    throw new Error(`Domain separator needs to start with 'APTOS::'.  Provided - ${domainSeparator}`);\n  }\n\n  hash.update(domainSeparator);\n\n  const prefix = hash.digest();\n\n  const body = bytes;\n\n  const mergedArray = new Uint8Array(prefix.length + body.length);\n  mergedArray.set(prefix);\n  mergedArray.set(body, prefix.length);\n\n  return mergedArray;\n}\n\n/**\n * @deprecated\n * Use CryptoHashable instead by having your class implement it and call hash() to get the signing message.\n *\n * Generates the 'signing message' form of a serilizable value. It bcs serializes the value and uses the name of\n * its constructor as the domain separator.\n *\n * @param serializable An object that has a bcs serialized form\n *\n * @returns The Uint8Array of the signing message\n */\nexport function generateSigningMessageForSerializable(serializable: Serializable): Uint8Array {\n  return generateSigningMessage(serializable.bcsToBytes(), `APTOS::${serializable.constructor.name}`);\n}\n\n/**\n * Generates the 'signing message' form of a transaction. It derives the type of transaction and\n * applies the appropriate domain separator based on if there is extra data such as a fee payer or\n * secondary signers.\n *\n * @param transaction A transaction that is to be signed\n *\n * @returns The Uint8Array of the signing message\n */\nexport function generateSigningMessageForTransaction(transaction: AnyRawTransaction): Uint8Array {\n  const rawTxn = deriveTransactionType(transaction);\n  if (transaction.feePayerAddress) {\n    return generateSigningMessage(rawTxn.bcsToBytes(), RAW_TRANSACTION_WITH_DATA_SALT);\n  }\n  if (transaction.secondarySignerAddresses) {\n    return generateSigningMessage(rawTxn.bcsToBytes(), RAW_TRANSACTION_WITH_DATA_SALT);\n  }\n  return generateSigningMessage(rawTxn.bcsToBytes(), RAW_TRANSACTION_SALT);\n}\n"], "mappings": ";;AAMA,SAASA,QAAA,IAAYC,CAAA,QAAgB;AAa9B,SAASC,EAAsBC,CAAA,EAA2D;EAC/F,OAAIA,CAAA,CAAYC,eAAA,GACP,IAAIC,CAAA,CACTF,CAAA,CAAYG,cAAA,EACZH,CAAA,CAAYI,wBAAA,IAA4B,EAAC,EACzCJ,CAAA,CAAYC,eACd,IAEED,CAAA,CAAYI,wBAAA,GACP,IAAIC,CAAA,CAAyBL,CAAA,CAAYG,cAAA,EAAgBH,CAAA,CAAYI,wBAAwB,IAG/FJ,CAAA,CAAYG,cACrB;AAAA;AAUO,SAASG,EAAuBN,CAAA,EAAmBO,CAAA,EAAqC;EAC7F,IAAMC,CAAA,GAAOV,CAAA,CAASW,MAAA,CAAO;EAE7B,IAAI,CAACF,CAAA,CAAgBG,UAAA,CAAW,SAAS,GACvC,MAAM,IAAIC,KAAA,CAAM,+DAA+DJ,CAAe,EAAE;EAGlGC,CAAA,CAAKI,MAAA,CAAOL,CAAe;EAE3B,IAAMM,CAAA,GAASL,CAAA,CAAKM,MAAA,CAAO;IAErBC,CAAA,GAAOf,CAAA;IAEPgB,CAAA,GAAc,IAAIC,UAAA,CAAWJ,CAAA,CAAOK,MAAA,GAASH,CAAA,CAAKG,MAAM;EAC9D,OAAAF,CAAA,CAAYG,GAAA,CAAIN,CAAM,GACtBG,CAAA,CAAYG,GAAA,CAAIJ,CAAA,EAAMF,CAAA,CAAOK,MAAM,GAE5BF,CACT;AAAA;AAaO,SAASI,EAAsCpB,CAAA,EAAwC;EAC5F,OAAOM,CAAA,CAAuBN,CAAA,CAAaqB,UAAA,CAAW,GAAG,UAAUrB,CAAA,CAAasB,WAAA,CAAYC,IAAI,EAAE,CACpG;AAAA;AAWO,SAASC,EAAqCxB,CAAA,EAA4C;EAC/F,IAAMO,CAAA,GAASR,CAAA,CAAsBC,CAAW;EAChD,OAAIA,CAAA,CAAYC,eAAA,GACPK,CAAA,CAAuBC,CAAA,CAAOc,UAAA,CAAW,GAAGI,CAA8B,IAE/EzB,CAAA,CAAYI,wBAAA,GACPE,CAAA,CAAuBC,CAAA,CAAOc,UAAA,CAAW,GAAGI,CAA8B,IAE5EnB,CAAA,CAAuBC,CAAA,CAAOc,UAAA,CAAW,GAAGK,CAAoB,CACzE;AAAA;AAAA,SAAA3B,CAAA,IAAA0B,CAAA,EAAAnB,CAAA,IAAAqB,CAAA,EAAAP,CAAA,IAAAlB,CAAA,EAAAsB,CAAA,IAAAzB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}