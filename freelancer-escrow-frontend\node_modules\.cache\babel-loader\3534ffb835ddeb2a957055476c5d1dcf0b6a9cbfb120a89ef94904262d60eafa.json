{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\base\\src\\identifier.ts"], "sourcesContent": ["/**\n * A namespaced identifier in the format `${namespace}:${reference}`.\n *\n * Used by {@link IdentifierArray} and {@link IdentifierRecord}.\n *\n * @group Identifier\n */\nexport type IdentifierString = `${string}:${string}`;\n\n/**\n * A read-only array of namespaced identifiers in the format `${namespace}:${reference}`.\n *\n * Used by {@link Wallet.chains | Wallet::chains}, {@link WalletAccount.chains | WalletAccount::chains}, and\n * {@link WalletAccount.features | WalletAccount::features}.\n *\n * @group Identifier\n */\nexport type IdentifierArray = readonly IdentifierString[];\n\n/**\n * A read-only object with keys of namespaced identifiers in the format `${namespace}:${reference}`.\n *\n * Used by {@link Wallet.features | Wallet::features}.\n *\n * @group Identifier\n */\nexport type IdentifierRecord<T> = Readonly<Record<IdentifierString, T>>;\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}