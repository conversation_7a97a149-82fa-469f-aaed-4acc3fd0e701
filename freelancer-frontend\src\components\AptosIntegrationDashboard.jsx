import { useWallet } from '@aptos-labs/wallet-adapter-react';
import {
    Activity,
    Briefcase,
    Check,
    CheckCircle,
    Clock,
    Copy,
    DollarSign,
    ExternalLink,
    Shield,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';

const AptosIntegrationDashboard = () => {
  const { account, connected, signAndSubmitTransaction } = useWallet();
  const [balance, setBalance] = useState('0');
  const [isLoading, setIsLoading] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [contractStats, setContractStats] = useState({
    totalEscrows: 0,
    totalVolume: 0,
    activeEscrows: 0,
    completedEscrows: 0
  });
  const [copied, setCopied] = useState('');

  // Contract address
  const CONTRACT_ADDRESS = '0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22';

  // Mock data for demo - in production this would fetch from Aptos SDK
  useEffect(() => {
    if (account?.address) {
      setBalance('12.5432'); // Mock balance
      setTransactions([
        {
          hash: '0xcf5ec5fc356a0d5c079d2cd696e6efb01971dbf828791b41c99a45913c14a659',
          type: 'Create Escrow',
          timestamp: Date.now() - 1000000,
        },
        {
          hash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          type: 'Fund Escrow',
          timestamp: Date.now() - 2000000,
        }
      ]);
    } else {
      setBalance('0');
      setTransactions([]);
    }
  }, [account?.address]);

  const copyToClipboard = async (text, type) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(type);
      setTimeout(() => setCopied(''), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };

  const formatAmount = (amount) => {
    return (parseInt(amount) / *********).toFixed(4);
  };

  const createTestEscrow = async () => {
    if (!signAndSubmitTransaction) {
      alert('Please connect your wallet first');
      return;
    }

    setIsLoading(true);
    try {
      const transaction = {
        data: {
          function: `${CONTRACT_ADDRESS}::freelancer_escrow::create_escrow`,
          functionArguments: [
            account.address, // freelancer (self for demo)
            "Test Project from UI",
            "This is a test project created from the React UI",
            "Web Development",
            "*********", // 5 APT in octas
            Math.floor(Date.now() / 1000 + 7 * 24 * 60 * 60).toString() // 7 days from now
          ]
        }
      };

      const response = await signAndSubmitTransaction(transaction);
      alert(`Escrow created successfully! Transaction: ${response.hash}`);
    } catch (error) {
      console.error('Error creating escrow:', error);
      alert('Failed to create escrow: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (!connected) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Shield className="w-8 h-8 text-primary-600" />
          </div>
          <h2 className="text-2xl font-bold text-secondary-900 mb-4">
            Connect to Aptos Network
          </h2>
          <p className="text-secondary-600 mb-6">
            Connect your Aptos wallet to interact with the blockchain and view your dashboard
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">
          Aptos Integration Dashboard
        </h1>
        <p className="text-secondary-600">
          Real-time blockchain interaction and smart contract management
        </p>
      </div>

      {/* Account Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-xl p-6 border border-primary-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-secondary-900">Account Balance</h3>
            <DollarSign className="w-6 h-6 text-primary-600" />
          </div>
          <div className="space-y-2">
            <p className="text-3xl font-bold text-secondary-900">
              {isLoading ? '...' : `${balance} APT`}
            </p>
            <p className="text-sm text-secondary-600">
              ≈ ${(parseFloat(balance) * 8.5).toFixed(2)} USD
            </p>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-secondary-900">Network Status</h3>
            <Activity className="w-6 h-6 text-green-600" />
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-lg font-semibold text-secondary-900">Connected</span>
            </div>
            <p className="text-sm text-secondary-600">Aptos Devnet</p>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-secondary-900">Smart Contract</h3>
            <Shield className="w-6 h-6 text-purple-600" />
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-lg font-semibold text-secondary-900">Active</span>
            </div>
            <p className="text-sm text-secondary-600">Escrow Platform</p>
          </div>
        </div>
      </div>

      {/* Smart Contract Info */}
      <div className="bg-white rounded-xl shadow-lg border border-secondary-200 p-6">
        <h3 className="text-xl font-semibold text-secondary-900 mb-6">Smart Contract Details</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-secondary-700">Contract Address</p>
                <p className="text-xs font-mono text-secondary-600 mt-1">
                  {formatAddress(CONTRACT_ADDRESS)}
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => copyToClipboard(CONTRACT_ADDRESS, 'contract')}
                  className="p-2 hover:bg-secondary-200 rounded-lg transition-colors"
                >
                  {copied === 'contract' ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4 text-secondary-500" />
                  )}
                </button>
                <a
                  href={`https://explorer.aptoslabs.com/account/${CONTRACT_ADDRESS}?network=devnet`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 hover:bg-secondary-200 rounded-lg transition-colors"
                >
                  <ExternalLink className="w-4 h-4 text-secondary-500" />
                </a>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg text-center">
                <p className="text-sm font-medium text-blue-800">Network</p>
                <p className="text-lg font-bold text-blue-900">Devnet</p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg text-center">
                <p className="text-sm font-medium text-green-800">Status</p>
                <p className="text-lg font-bold text-green-900">Live</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="font-medium text-secondary-900">Available Functions</h4>
            <div className="space-y-2">
              {[
                'create_escrow',
                'fund_escrow', 
                'start_work',
                'submit_work',
                'approve_work',
                'cancel_escrow'
              ].map((func) => (
                <div key={func} className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="font-mono text-secondary-700">{func}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg border border-secondary-200 p-6">
        <h3 className="text-xl font-semibold text-secondary-900 mb-6">Quick Actions</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={createTestEscrow}
            disabled={isLoading}
            className="flex items-center justify-center space-x-2 p-4 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <Briefcase className="w-5 h-5" />
            <span>Create Test Escrow</span>
          </button>

          <a
            href={`https://explorer.aptoslabs.com/account/${account.address}?network=devnet`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center space-x-2 p-4 bg-secondary-600 hover:bg-secondary-700 text-white rounded-lg transition-colors"
          >
            <ExternalLink className="w-5 h-5" />
            <span>View on Explorer</span>
          </a>

          <button
            onClick={() => window.open('https://aptoslabs.com/developers', '_blank')}
            className="flex items-center justify-center space-x-2 p-4 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <Zap className="w-5 h-5" />
            <span>Aptos Docs</span>
          </button>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-xl shadow-lg border border-secondary-200 p-6">
        <h3 className="text-xl font-semibold text-secondary-900 mb-6">Recent Transactions</h3>
        
        {transactions.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
            <p className="text-secondary-600">No recent transactions</p>
          </div>
        ) : (
          <div className="space-y-3">
            {transactions.map((tx, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <Activity className="w-4 h-4 text-primary-600" />
                  </div>
                  <div>
                    <p className="font-medium text-secondary-900">
                      {tx.type || 'Transaction'}
                    </p>
                    <p className="text-sm text-secondary-600">
                      {new Date(parseInt(tx.timestamp) / 1000).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    Success
                  </span>
                  <a
                    href={`https://explorer.aptoslabs.com/txn/${tx.hash}?network=devnet`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-1 hover:bg-secondary-200 rounded transition-colors"
                  >
                    <ExternalLink className="w-4 h-4 text-secondary-500" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AptosIntegrationDashboard;
