/* Main App Styles */
.App {
  text-align: center;
}

/* Gradient backgrounds */
.gradient-bg-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Enhanced card styles */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button enhancements */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Tab navigation styles */
.tab-active {
  border-bottom: 2px solid #3b82f6;
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.05);
}

.tab-inactive {
  border-bottom: 2px solid transparent;
  color: #6b7280;
}

.tab-inactive:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
  background-color: rgba(107, 114, 128, 0.05);
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-success {
  background-color: #dcfce7;
  color: #166534;
}

.status-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-info {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-error {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Progress bars */
.progress-bar {
  width: 100%;
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 9999px;
  transition: width 0.3s ease;
}

/* Skill tags */
.skill-tag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #eff6ff;
  color: #1e40af;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
  margin: 0.125rem;
}

.skill-tag-green {
  background-color: #f0fdf4;
  color: #166534;
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dropdown animations */
.dropdown-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.dropdown-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.dropdown-exit {
  opacity: 1;
  transform: translateY(0);
}

.dropdown-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Responsive text */
@media (max-width: 640px) {
  .text-responsive-lg {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-responsive-xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-responsive-2xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 641px) {
  .text-responsive-lg {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-responsive-xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .text-responsive-2xl {
    font-size: 3rem;
    line-height: 1;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus states */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Utility classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
