{"ast": null, "code": "/** Name of the feature. */\nexport const StandardDisconnect = 'standard:disconnect';\n/**\n * @deprecated Use {@link StandardDisconnect} instead.\n *\n * @group Deprecated\n */\nexport const Disconnect = StandardDisconnect;", "map": {"version": 3, "names": ["StandardDisconnect", "Disconnect"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\features\\src\\disconnect.ts"], "sourcesContent": ["/** Name of the feature. */\nexport const StandardDisconnect = 'standard:disconnect';\n/**\n * @deprecated Use {@link StandardDisconnect} instead.\n *\n * @group Deprecated\n */\nexport const Disconnect = StandardDisconnect;\n\n/**\n * `standard:disconnect` is a {@link \"@wallet-standard/base\".Wallet.features | feature} that may be implemented by a\n * {@link \"@wallet-standard/base\".Wallet} to allow the app to perform any cleanup work.\n *\n * This feature may or may not be used by the app and the Wallet should not depend on it being used.\n * If this feature is used by the app, the Wallet should perform any cleanup work, but should not revoke authorization\n * to use accounts previously granted through the {@link ConnectFeature}.\n *\n * @group Disconnect\n */\nexport type StandardDisconnectFeature = {\n    /** Name of the feature. */\n    readonly [StandardDisconnect]: {\n        /** Version of the feature implemented by the Wallet. */\n        readonly version: StandardDisconnectVersion;\n        /** Method to call to use the feature. */\n        readonly disconnect: StandardDisconnectMethod;\n    };\n};\n/**\n * @deprecated Use {@link StandardDisconnectFeature} instead.\n *\n * @group Deprecated\n */\nexport type DisconnectFeature = StandardDisconnectFeature;\n\n/**\n * Version of the {@link StandardDisconnectFeature} implemented by a Wallet.\n *\n * @group Disconnect\n */\nexport type StandardDisconnectVersion = '1.0.0';\n/**\n * @deprecated Use {@link StandardDisconnectVersion} instead.\n *\n * @group Deprecated\n */\nexport type DisconnectVersion = StandardDisconnectVersion;\n\n/**\n * Method to call to use the {@link StandardDisconnectFeature}.\n *\n * @group Disconnect\n */\nexport type StandardDisconnectMethod = () => Promise<void>;\n/**\n * @deprecated Use {@link StandardDisconnectMethod} instead.\n *\n * @group Deprecated\n */\nexport type DisconnectMethod = StandardDisconnectMethod;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,kBAAkB,GAAG,qBAAqB;AACvD;;;;;AAKA,OAAO,MAAMC,UAAU,GAAGD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}