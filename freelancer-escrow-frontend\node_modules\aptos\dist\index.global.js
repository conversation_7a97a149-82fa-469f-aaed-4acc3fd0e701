"use strict";var aptosSDK=(()=>{var Xp=Object.create;var vn=Object.defineProperty;var Vs=Object.getOwnPropertyDescriptor;var Yp=Object.getOwnPropertyNames;var Zp=Object.getPrototypeOf,eu=Object.prototype.hasOwnProperty;var Rs=(r=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(r,{get:(e,t)=>(typeof require!="undefined"?require:e)[t]}):r)(function(r){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+r+'" is not supported')});var mo=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),Tr=(r,e)=>{for(var t in e)vn(r,t,{get:e[t],enumerable:!0})},Ds=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of Yp(e))!eu.call(r,a)&&a!==t&&vn(r,a,{get:()=>e[a],enumerable:!(n=Vs(e,a))||n.enumerable});return r};var go=(r,e,t)=>(t=r!=null?Xp(Zp(r)):{},Ds(e||!r||!r.__esModule?vn(t,"default",{value:r,enumerable:!0}):t,r)),tu=r=>Ds(vn({},"__esModule",{value:!0}),r),de=(r,e,t,n)=>{for(var a=n>1?void 0:n?Vs(e,t):e,i=r.length-1,s;i>=0;i--)(s=r[i])&&(a=(n?s(e,t,a):s(a))||a);return n&&a&&vn(e,t,a),a};var Ns=mo(()=>{"use strict"});var fo=mo((wy,ta)=>{"use strict";(function(r){"use strict";var e=function(_){var c,u=new Float64Array(16);if(_)for(c=0;c<_.length;c++)u[c]=_[c];return u},t=function(){throw new Error("no PRNG")},n=new Uint8Array(16),a=new Uint8Array(32);a[0]=9;var i=e(),s=e([1]),p=e([56129,1]),l=e([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),d=e([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),g=e([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),b=e([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),I=e([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function B(_,c,u,o){_[c]=u>>24&255,_[c+1]=u>>16&255,_[c+2]=u>>8&255,_[c+3]=u&255,_[c+4]=o>>24&255,_[c+5]=o>>16&255,_[c+6]=o>>8&255,_[c+7]=o&255}function x(_,c,u,o,y){var f,M=0;for(f=0;f<y;f++)M|=_[c+f]^u[o+f];return(1&M-1>>>8)-1}function S(_,c,u,o){return x(_,c,u,o,16)}function V(_,c,u,o){return x(_,c,u,o,32)}function Z(_,c,u,o){for(var y=o[0]&255|(o[1]&255)<<8|(o[2]&255)<<16|(o[3]&255)<<24,f=u[0]&255|(u[1]&255)<<8|(u[2]&255)<<16|(u[3]&255)<<24,M=u[4]&255|(u[5]&255)<<8|(u[6]&255)<<16|(u[7]&255)<<24,T=u[8]&255|(u[9]&255)<<8|(u[10]&255)<<16|(u[11]&255)<<24,P=u[12]&255|(u[13]&255)<<8|(u[14]&255)<<16|(u[15]&255)<<24,G=o[4]&255|(o[5]&255)<<8|(o[6]&255)<<16|(o[7]&255)<<24,D=c[0]&255|(c[1]&255)<<8|(c[2]&255)<<16|(c[3]&255)<<24,be=c[4]&255|(c[5]&255)<<8|(c[6]&255)<<16|(c[7]&255)<<24,U=c[8]&255|(c[9]&255)<<8|(c[10]&255)<<16|(c[11]&255)<<24,W=c[12]&255|(c[13]&255)<<8|(c[14]&255)<<16|(c[15]&255)<<24,J=o[8]&255|(o[9]&255)<<8|(o[10]&255)<<16|(o[11]&255)<<24,ne=u[16]&255|(u[17]&255)<<8|(u[18]&255)<<16|(u[19]&255)<<24,te=u[20]&255|(u[21]&255)<<8|(u[22]&255)<<16|(u[23]&255)<<24,X=u[24]&255|(u[25]&255)<<8|(u[26]&255)<<16|(u[27]&255)<<24,ee=u[28]&255|(u[29]&255)<<8|(u[30]&255)<<16|(u[31]&255)<<24,Y=o[12]&255|(o[13]&255)<<8|(o[14]&255)<<16|(o[15]&255)<<24,z=y,Q=f,N=M,L=T,q=P,R=G,h=D,v=be,O=U,k=W,C=J,E=ne,j=te,ae=X,se=ee,oe=Y,m,pe=0;pe<20;pe+=2)m=z+j|0,q^=m<<7|m>>>25,m=q+z|0,O^=m<<9|m>>>23,m=O+q|0,j^=m<<13|m>>>19,m=j+O|0,z^=m<<18|m>>>14,m=R+Q|0,k^=m<<7|m>>>25,m=k+R|0,ae^=m<<9|m>>>23,m=ae+k|0,Q^=m<<13|m>>>19,m=Q+ae|0,R^=m<<18|m>>>14,m=C+h|0,se^=m<<7|m>>>25,m=se+C|0,N^=m<<9|m>>>23,m=N+se|0,h^=m<<13|m>>>19,m=h+N|0,C^=m<<18|m>>>14,m=oe+E|0,L^=m<<7|m>>>25,m=L+oe|0,v^=m<<9|m>>>23,m=v+L|0,E^=m<<13|m>>>19,m=E+v|0,oe^=m<<18|m>>>14,m=z+L|0,Q^=m<<7|m>>>25,m=Q+z|0,N^=m<<9|m>>>23,m=N+Q|0,L^=m<<13|m>>>19,m=L+N|0,z^=m<<18|m>>>14,m=R+q|0,h^=m<<7|m>>>25,m=h+R|0,v^=m<<9|m>>>23,m=v+h|0,q^=m<<13|m>>>19,m=q+v|0,R^=m<<18|m>>>14,m=C+k|0,E^=m<<7|m>>>25,m=E+C|0,O^=m<<9|m>>>23,m=O+E|0,k^=m<<13|m>>>19,m=k+O|0,C^=m<<18|m>>>14,m=oe+se|0,j^=m<<7|m>>>25,m=j+oe|0,ae^=m<<9|m>>>23,m=ae+j|0,se^=m<<13|m>>>19,m=se+ae|0,oe^=m<<18|m>>>14;z=z+y|0,Q=Q+f|0,N=N+M|0,L=L+T|0,q=q+P|0,R=R+G|0,h=h+D|0,v=v+be|0,O=O+U|0,k=k+W|0,C=C+J|0,E=E+ne|0,j=j+te|0,ae=ae+X|0,se=se+ee|0,oe=oe+Y|0,_[0]=z>>>0&255,_[1]=z>>>8&255,_[2]=z>>>16&255,_[3]=z>>>24&255,_[4]=Q>>>0&255,_[5]=Q>>>8&255,_[6]=Q>>>16&255,_[7]=Q>>>24&255,_[8]=N>>>0&255,_[9]=N>>>8&255,_[10]=N>>>16&255,_[11]=N>>>24&255,_[12]=L>>>0&255,_[13]=L>>>8&255,_[14]=L>>>16&255,_[15]=L>>>24&255,_[16]=q>>>0&255,_[17]=q>>>8&255,_[18]=q>>>16&255,_[19]=q>>>24&255,_[20]=R>>>0&255,_[21]=R>>>8&255,_[22]=R>>>16&255,_[23]=R>>>24&255,_[24]=h>>>0&255,_[25]=h>>>8&255,_[26]=h>>>16&255,_[27]=h>>>24&255,_[28]=v>>>0&255,_[29]=v>>>8&255,_[30]=v>>>16&255,_[31]=v>>>24&255,_[32]=O>>>0&255,_[33]=O>>>8&255,_[34]=O>>>16&255,_[35]=O>>>24&255,_[36]=k>>>0&255,_[37]=k>>>8&255,_[38]=k>>>16&255,_[39]=k>>>24&255,_[40]=C>>>0&255,_[41]=C>>>8&255,_[42]=C>>>16&255,_[43]=C>>>24&255,_[44]=E>>>0&255,_[45]=E>>>8&255,_[46]=E>>>16&255,_[47]=E>>>24&255,_[48]=j>>>0&255,_[49]=j>>>8&255,_[50]=j>>>16&255,_[51]=j>>>24&255,_[52]=ae>>>0&255,_[53]=ae>>>8&255,_[54]=ae>>>16&255,_[55]=ae>>>24&255,_[56]=se>>>0&255,_[57]=se>>>8&255,_[58]=se>>>16&255,_[59]=se>>>24&255,_[60]=oe>>>0&255,_[61]=oe>>>8&255,_[62]=oe>>>16&255,_[63]=oe>>>24&255}function K(_,c,u,o){for(var y=o[0]&255|(o[1]&255)<<8|(o[2]&255)<<16|(o[3]&255)<<24,f=u[0]&255|(u[1]&255)<<8|(u[2]&255)<<16|(u[3]&255)<<24,M=u[4]&255|(u[5]&255)<<8|(u[6]&255)<<16|(u[7]&255)<<24,T=u[8]&255|(u[9]&255)<<8|(u[10]&255)<<16|(u[11]&255)<<24,P=u[12]&255|(u[13]&255)<<8|(u[14]&255)<<16|(u[15]&255)<<24,G=o[4]&255|(o[5]&255)<<8|(o[6]&255)<<16|(o[7]&255)<<24,D=c[0]&255|(c[1]&255)<<8|(c[2]&255)<<16|(c[3]&255)<<24,be=c[4]&255|(c[5]&255)<<8|(c[6]&255)<<16|(c[7]&255)<<24,U=c[8]&255|(c[9]&255)<<8|(c[10]&255)<<16|(c[11]&255)<<24,W=c[12]&255|(c[13]&255)<<8|(c[14]&255)<<16|(c[15]&255)<<24,J=o[8]&255|(o[9]&255)<<8|(o[10]&255)<<16|(o[11]&255)<<24,ne=u[16]&255|(u[17]&255)<<8|(u[18]&255)<<16|(u[19]&255)<<24,te=u[20]&255|(u[21]&255)<<8|(u[22]&255)<<16|(u[23]&255)<<24,X=u[24]&255|(u[25]&255)<<8|(u[26]&255)<<16|(u[27]&255)<<24,ee=u[28]&255|(u[29]&255)<<8|(u[30]&255)<<16|(u[31]&255)<<24,Y=o[12]&255|(o[13]&255)<<8|(o[14]&255)<<16|(o[15]&255)<<24,z=y,Q=f,N=M,L=T,q=P,R=G,h=D,v=be,O=U,k=W,C=J,E=ne,j=te,ae=X,se=ee,oe=Y,m,pe=0;pe<20;pe+=2)m=z+j|0,q^=m<<7|m>>>25,m=q+z|0,O^=m<<9|m>>>23,m=O+q|0,j^=m<<13|m>>>19,m=j+O|0,z^=m<<18|m>>>14,m=R+Q|0,k^=m<<7|m>>>25,m=k+R|0,ae^=m<<9|m>>>23,m=ae+k|0,Q^=m<<13|m>>>19,m=Q+ae|0,R^=m<<18|m>>>14,m=C+h|0,se^=m<<7|m>>>25,m=se+C|0,N^=m<<9|m>>>23,m=N+se|0,h^=m<<13|m>>>19,m=h+N|0,C^=m<<18|m>>>14,m=oe+E|0,L^=m<<7|m>>>25,m=L+oe|0,v^=m<<9|m>>>23,m=v+L|0,E^=m<<13|m>>>19,m=E+v|0,oe^=m<<18|m>>>14,m=z+L|0,Q^=m<<7|m>>>25,m=Q+z|0,N^=m<<9|m>>>23,m=N+Q|0,L^=m<<13|m>>>19,m=L+N|0,z^=m<<18|m>>>14,m=R+q|0,h^=m<<7|m>>>25,m=h+R|0,v^=m<<9|m>>>23,m=v+h|0,q^=m<<13|m>>>19,m=q+v|0,R^=m<<18|m>>>14,m=C+k|0,E^=m<<7|m>>>25,m=E+C|0,O^=m<<9|m>>>23,m=O+E|0,k^=m<<13|m>>>19,m=k+O|0,C^=m<<18|m>>>14,m=oe+se|0,j^=m<<7|m>>>25,m=j+oe|0,ae^=m<<9|m>>>23,m=ae+j|0,se^=m<<13|m>>>19,m=se+ae|0,oe^=m<<18|m>>>14;_[0]=z>>>0&255,_[1]=z>>>8&255,_[2]=z>>>16&255,_[3]=z>>>24&255,_[4]=R>>>0&255,_[5]=R>>>8&255,_[6]=R>>>16&255,_[7]=R>>>24&255,_[8]=C>>>0&255,_[9]=C>>>8&255,_[10]=C>>>16&255,_[11]=C>>>24&255,_[12]=oe>>>0&255,_[13]=oe>>>8&255,_[14]=oe>>>16&255,_[15]=oe>>>24&255,_[16]=h>>>0&255,_[17]=h>>>8&255,_[18]=h>>>16&255,_[19]=h>>>24&255,_[20]=v>>>0&255,_[21]=v>>>8&255,_[22]=v>>>16&255,_[23]=v>>>24&255,_[24]=O>>>0&255,_[25]=O>>>8&255,_[26]=O>>>16&255,_[27]=O>>>24&255,_[28]=k>>>0&255,_[29]=k>>>8&255,_[30]=k>>>16&255,_[31]=k>>>24&255}function H(_,c,u,o){Z(_,c,u,o)}function he(_,c,u,o){K(_,c,u,o)}var w=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function xr(_,c,u,o,y,f,M){var T=new Uint8Array(16),P=new Uint8Array(64),G,D;for(D=0;D<16;D++)T[D]=0;for(D=0;D<8;D++)T[D]=f[D];for(;y>=64;){for(H(P,T,M,w),D=0;D<64;D++)_[c+D]=u[o+D]^P[D];for(G=1,D=8;D<16;D++)G=G+(T[D]&255)|0,T[D]=G&255,G>>>=8;y-=64,c+=64,o+=64}if(y>0)for(H(P,T,M,w),D=0;D<y;D++)_[c+D]=u[o+D]^P[D];return 0}function hr(_,c,u,o,y){var f=new Uint8Array(16),M=new Uint8Array(64),T,P;for(P=0;P<16;P++)f[P]=0;for(P=0;P<8;P++)f[P]=o[P];for(;u>=64;){for(H(M,f,y,w),P=0;P<64;P++)_[c+P]=M[P];for(T=1,P=8;P<16;P++)T=T+(f[P]&255)|0,f[P]=T&255,T>>>=8;u-=64,c+=64}if(u>0)for(H(M,f,y,w),P=0;P<u;P++)_[c+P]=M[P];return 0}function Ye(_,c,u,o,y){var f=new Uint8Array(32);he(f,o,y,w);for(var M=new Uint8Array(8),T=0;T<8;T++)M[T]=o[T+16];return hr(_,c,u,M,f)}function Ke(_,c,u,o,y,f,M){var T=new Uint8Array(32);he(T,f,M,w);for(var P=new Uint8Array(8),G=0;G<8;G++)P[G]=f[G+16];return xr(_,c,u,o,y,P,T)}var ft=function(_){this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0;var c,u,o,y,f,M,T,P;c=_[0]&255|(_[1]&255)<<8,this.r[0]=c&8191,u=_[2]&255|(_[3]&255)<<8,this.r[1]=(c>>>13|u<<3)&8191,o=_[4]&255|(_[5]&255)<<8,this.r[2]=(u>>>10|o<<6)&7939,y=_[6]&255|(_[7]&255)<<8,this.r[3]=(o>>>7|y<<9)&8191,f=_[8]&255|(_[9]&255)<<8,this.r[4]=(y>>>4|f<<12)&255,this.r[5]=f>>>1&8190,M=_[10]&255|(_[11]&255)<<8,this.r[6]=(f>>>14|M<<2)&8191,T=_[12]&255|(_[13]&255)<<8,this.r[7]=(M>>>11|T<<5)&8065,P=_[14]&255|(_[15]&255)<<8,this.r[8]=(T>>>8|P<<8)&8191,this.r[9]=P>>>5&127,this.pad[0]=_[16]&255|(_[17]&255)<<8,this.pad[1]=_[18]&255|(_[19]&255)<<8,this.pad[2]=_[20]&255|(_[21]&255)<<8,this.pad[3]=_[22]&255|(_[23]&255)<<8,this.pad[4]=_[24]&255|(_[25]&255)<<8,this.pad[5]=_[26]&255|(_[27]&255)<<8,this.pad[6]=_[28]&255|(_[29]&255)<<8,this.pad[7]=_[30]&255|(_[31]&255)<<8};ft.prototype.blocks=function(_,c,u){for(var o=this.fin?0:2048,y,f,M,T,P,G,D,be,U,W,J,ne,te,X,ee,Y,z,Q,N,L=this.h[0],q=this.h[1],R=this.h[2],h=this.h[3],v=this.h[4],O=this.h[5],k=this.h[6],C=this.h[7],E=this.h[8],j=this.h[9],ae=this.r[0],se=this.r[1],oe=this.r[2],m=this.r[3],pe=this.r[4],me=this.r[5],ge=this.r[6],_e=this.r[7],ye=this.r[8],le=this.r[9];u>=16;)y=_[c+0]&255|(_[c+1]&255)<<8,L+=y&8191,f=_[c+2]&255|(_[c+3]&255)<<8,q+=(y>>>13|f<<3)&8191,M=_[c+4]&255|(_[c+5]&255)<<8,R+=(f>>>10|M<<6)&8191,T=_[c+6]&255|(_[c+7]&255)<<8,h+=(M>>>7|T<<9)&8191,P=_[c+8]&255|(_[c+9]&255)<<8,v+=(T>>>4|P<<12)&8191,O+=P>>>1&8191,G=_[c+10]&255|(_[c+11]&255)<<8,k+=(P>>>14|G<<2)&8191,D=_[c+12]&255|(_[c+13]&255)<<8,C+=(G>>>11|D<<5)&8191,be=_[c+14]&255|(_[c+15]&255)<<8,E+=(D>>>8|be<<8)&8191,j+=be>>>5|o,U=0,W=U,W+=L*ae,W+=q*(5*le),W+=R*(5*ye),W+=h*(5*_e),W+=v*(5*ge),U=W>>>13,W&=8191,W+=O*(5*me),W+=k*(5*pe),W+=C*(5*m),W+=E*(5*oe),W+=j*(5*se),U+=W>>>13,W&=8191,J=U,J+=L*se,J+=q*ae,J+=R*(5*le),J+=h*(5*ye),J+=v*(5*_e),U=J>>>13,J&=8191,J+=O*(5*ge),J+=k*(5*me),J+=C*(5*pe),J+=E*(5*m),J+=j*(5*oe),U+=J>>>13,J&=8191,ne=U,ne+=L*oe,ne+=q*se,ne+=R*ae,ne+=h*(5*le),ne+=v*(5*ye),U=ne>>>13,ne&=8191,ne+=O*(5*_e),ne+=k*(5*ge),ne+=C*(5*me),ne+=E*(5*pe),ne+=j*(5*m),U+=ne>>>13,ne&=8191,te=U,te+=L*m,te+=q*oe,te+=R*se,te+=h*ae,te+=v*(5*le),U=te>>>13,te&=8191,te+=O*(5*ye),te+=k*(5*_e),te+=C*(5*ge),te+=E*(5*me),te+=j*(5*pe),U+=te>>>13,te&=8191,X=U,X+=L*pe,X+=q*m,X+=R*oe,X+=h*se,X+=v*ae,U=X>>>13,X&=8191,X+=O*(5*le),X+=k*(5*ye),X+=C*(5*_e),X+=E*(5*ge),X+=j*(5*me),U+=X>>>13,X&=8191,ee=U,ee+=L*me,ee+=q*pe,ee+=R*m,ee+=h*oe,ee+=v*se,U=ee>>>13,ee&=8191,ee+=O*ae,ee+=k*(5*le),ee+=C*(5*ye),ee+=E*(5*_e),ee+=j*(5*ge),U+=ee>>>13,ee&=8191,Y=U,Y+=L*ge,Y+=q*me,Y+=R*pe,Y+=h*m,Y+=v*oe,U=Y>>>13,Y&=8191,Y+=O*se,Y+=k*ae,Y+=C*(5*le),Y+=E*(5*ye),Y+=j*(5*_e),U+=Y>>>13,Y&=8191,z=U,z+=L*_e,z+=q*ge,z+=R*me,z+=h*pe,z+=v*m,U=z>>>13,z&=8191,z+=O*oe,z+=k*se,z+=C*ae,z+=E*(5*le),z+=j*(5*ye),U+=z>>>13,z&=8191,Q=U,Q+=L*ye,Q+=q*_e,Q+=R*ge,Q+=h*me,Q+=v*pe,U=Q>>>13,Q&=8191,Q+=O*m,Q+=k*oe,Q+=C*se,Q+=E*ae,Q+=j*(5*le),U+=Q>>>13,Q&=8191,N=U,N+=L*le,N+=q*ye,N+=R*_e,N+=h*ge,N+=v*me,U=N>>>13,N&=8191,N+=O*pe,N+=k*m,N+=C*oe,N+=E*se,N+=j*ae,U+=N>>>13,N&=8191,U=(U<<2)+U|0,U=U+W|0,W=U&8191,U=U>>>13,J+=U,L=W,q=J,R=ne,h=te,v=X,O=ee,k=Y,C=z,E=Q,j=N,c+=16,u-=16;this.h[0]=L,this.h[1]=q,this.h[2]=R,this.h[3]=h,this.h[4]=v,this.h[5]=O,this.h[6]=k,this.h[7]=C,this.h[8]=E,this.h[9]=j},ft.prototype.finish=function(_,c){var u=new Uint16Array(10),o,y,f,M;if(this.leftover){for(M=this.leftover,this.buffer[M++]=1;M<16;M++)this.buffer[M]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(o=this.h[1]>>>13,this.h[1]&=8191,M=2;M<10;M++)this.h[M]+=o,o=this.h[M]>>>13,this.h[M]&=8191;for(this.h[0]+=o*5,o=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=o,o=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=o,u[0]=this.h[0]+5,o=u[0]>>>13,u[0]&=8191,M=1;M<10;M++)u[M]=this.h[M]+o,o=u[M]>>>13,u[M]&=8191;for(u[9]-=8192,y=(o^1)-1,M=0;M<10;M++)u[M]&=y;for(y=~y,M=0;M<10;M++)this.h[M]=this.h[M]&y|u[M];for(this.h[0]=(this.h[0]|this.h[1]<<13)&65535,this.h[1]=(this.h[1]>>>3|this.h[2]<<10)&65535,this.h[2]=(this.h[2]>>>6|this.h[3]<<7)&65535,this.h[3]=(this.h[3]>>>9|this.h[4]<<4)&65535,this.h[4]=(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14)&65535,this.h[5]=(this.h[6]>>>2|this.h[7]<<11)&65535,this.h[6]=(this.h[7]>>>5|this.h[8]<<8)&65535,this.h[7]=(this.h[8]>>>8|this.h[9]<<5)&65535,f=this.h[0]+this.pad[0],this.h[0]=f&65535,M=1;M<8;M++)f=(this.h[M]+this.pad[M]|0)+(f>>>16)|0,this.h[M]=f&65535;_[c+0]=this.h[0]>>>0&255,_[c+1]=this.h[0]>>>8&255,_[c+2]=this.h[1]>>>0&255,_[c+3]=this.h[1]>>>8&255,_[c+4]=this.h[2]>>>0&255,_[c+5]=this.h[2]>>>8&255,_[c+6]=this.h[3]>>>0&255,_[c+7]=this.h[3]>>>8&255,_[c+8]=this.h[4]>>>0&255,_[c+9]=this.h[4]>>>8&255,_[c+10]=this.h[5]>>>0&255,_[c+11]=this.h[5]>>>8&255,_[c+12]=this.h[6]>>>0&255,_[c+13]=this.h[6]>>>8&255,_[c+14]=this.h[7]>>>0&255,_[c+15]=this.h[7]>>>8&255},ft.prototype.update=function(_,c,u){var o,y;if(this.leftover){for(y=16-this.leftover,y>u&&(y=u),o=0;o<y;o++)this.buffer[this.leftover+o]=_[c+o];if(u-=y,c+=y,this.leftover+=y,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(u>=16&&(y=u-u%16,this.blocks(_,c,y),c+=y,u-=y),u){for(o=0;o<u;o++)this.buffer[this.leftover+o]=_[c+o];this.leftover+=u}};function er(_,c,u,o,y,f){var M=new ft(f);return M.update(u,o,y),M.finish(_,c),0}function tr(_,c,u,o,y,f){var M=new Uint8Array(16);return er(M,0,u,o,y,f),S(_,c,M,0)}function rr(_,c,u,o,y){var f;if(u<32)return-1;for(Ke(_,0,c,0,u,o,y),er(_,16,_,32,u-32,_),f=0;f<16;f++)_[f]=0;return 0}function Mn(_,c,u,o,y){var f,M=new Uint8Array(32);if(u<32||(Ye(M,0,32,o,y),tr(c,16,c,32,u-32,M)!==0))return-1;for(Ke(_,0,c,0,u,o,y),f=0;f<32;f++)_[f]=0;return 0}function Ze(_,c){var u;for(u=0;u<16;u++)_[u]=c[u]|0}function no(_){var c,u,o=1;for(c=0;c<16;c++)u=_[c]+o+65535,o=Math.floor(u/65536),_[c]=u-o*65536;_[0]+=o-1+37*(o-1)}function vr(_,c,u){for(var o,y=~(u-1),f=0;f<16;f++)o=y&(_[f]^c[f]),_[f]^=o,c[f]^=o}function Ar(_,c){var u,o,y,f=e(),M=e();for(u=0;u<16;u++)M[u]=c[u];for(no(M),no(M),no(M),o=0;o<2;o++){for(f[0]=M[0]-65517,u=1;u<15;u++)f[u]=M[u]-65535-(f[u-1]>>16&1),f[u-1]&=65535;f[15]=M[15]-32767-(f[14]>>16&1),y=f[15]>>16&1,f[14]&=65535,vr(M,f,1-y)}for(u=0;u<16;u++)_[2*u]=M[u]&255,_[2*u+1]=M[u]>>8}function xs(_,c){var u=new Uint8Array(32),o=new Uint8Array(32);return Ar(u,_),Ar(o,c),V(u,0,o,0)}function hs(_){var c=new Uint8Array(32);return Ar(c,_),c[0]&1}function ao(_,c){var u;for(u=0;u<16;u++)_[u]=c[2*u]+(c[2*u+1]<<8);_[15]&=32767}function nt(_,c,u){for(var o=0;o<16;o++)_[o]=c[o]+u[o]}function at(_,c,u){for(var o=0;o<16;o++)_[o]=c[o]-u[o]}function ce(_,c,u){var o,y,f=0,M=0,T=0,P=0,G=0,D=0,be=0,U=0,W=0,J=0,ne=0,te=0,X=0,ee=0,Y=0,z=0,Q=0,N=0,L=0,q=0,R=0,h=0,v=0,O=0,k=0,C=0,E=0,j=0,ae=0,se=0,oe=0,m=u[0],pe=u[1],me=u[2],ge=u[3],_e=u[4],ye=u[5],le=u[6],Te=u[7],Ie=u[8],ve=u[9],Ae=u[10],Be=u[11],Fe=u[12],Ne=u[13],Ue=u[14],ze=u[15];o=c[0],f+=o*m,M+=o*pe,T+=o*me,P+=o*ge,G+=o*_e,D+=o*ye,be+=o*le,U+=o*Te,W+=o*Ie,J+=o*ve,ne+=o*Ae,te+=o*Be,X+=o*Fe,ee+=o*Ne,Y+=o*Ue,z+=o*ze,o=c[1],M+=o*m,T+=o*pe,P+=o*me,G+=o*ge,D+=o*_e,be+=o*ye,U+=o*le,W+=o*Te,J+=o*Ie,ne+=o*ve,te+=o*Ae,X+=o*Be,ee+=o*Fe,Y+=o*Ne,z+=o*Ue,Q+=o*ze,o=c[2],T+=o*m,P+=o*pe,G+=o*me,D+=o*ge,be+=o*_e,U+=o*ye,W+=o*le,J+=o*Te,ne+=o*Ie,te+=o*ve,X+=o*Ae,ee+=o*Be,Y+=o*Fe,z+=o*Ne,Q+=o*Ue,N+=o*ze,o=c[3],P+=o*m,G+=o*pe,D+=o*me,be+=o*ge,U+=o*_e,W+=o*ye,J+=o*le,ne+=o*Te,te+=o*Ie,X+=o*ve,ee+=o*Ae,Y+=o*Be,z+=o*Fe,Q+=o*Ne,N+=o*Ue,L+=o*ze,o=c[4],G+=o*m,D+=o*pe,be+=o*me,U+=o*ge,W+=o*_e,J+=o*ye,ne+=o*le,te+=o*Te,X+=o*Ie,ee+=o*ve,Y+=o*Ae,z+=o*Be,Q+=o*Fe,N+=o*Ne,L+=o*Ue,q+=o*ze,o=c[5],D+=o*m,be+=o*pe,U+=o*me,W+=o*ge,J+=o*_e,ne+=o*ye,te+=o*le,X+=o*Te,ee+=o*Ie,Y+=o*ve,z+=o*Ae,Q+=o*Be,N+=o*Fe,L+=o*Ne,q+=o*Ue,R+=o*ze,o=c[6],be+=o*m,U+=o*pe,W+=o*me,J+=o*ge,ne+=o*_e,te+=o*ye,X+=o*le,ee+=o*Te,Y+=o*Ie,z+=o*ve,Q+=o*Ae,N+=o*Be,L+=o*Fe,q+=o*Ne,R+=o*Ue,h+=o*ze,o=c[7],U+=o*m,W+=o*pe,J+=o*me,ne+=o*ge,te+=o*_e,X+=o*ye,ee+=o*le,Y+=o*Te,z+=o*Ie,Q+=o*ve,N+=o*Ae,L+=o*Be,q+=o*Fe,R+=o*Ne,h+=o*Ue,v+=o*ze,o=c[8],W+=o*m,J+=o*pe,ne+=o*me,te+=o*ge,X+=o*_e,ee+=o*ye,Y+=o*le,z+=o*Te,Q+=o*Ie,N+=o*ve,L+=o*Ae,q+=o*Be,R+=o*Fe,h+=o*Ne,v+=o*Ue,O+=o*ze,o=c[9],J+=o*m,ne+=o*pe,te+=o*me,X+=o*ge,ee+=o*_e,Y+=o*ye,z+=o*le,Q+=o*Te,N+=o*Ie,L+=o*ve,q+=o*Ae,R+=o*Be,h+=o*Fe,v+=o*Ne,O+=o*Ue,k+=o*ze,o=c[10],ne+=o*m,te+=o*pe,X+=o*me,ee+=o*ge,Y+=o*_e,z+=o*ye,Q+=o*le,N+=o*Te,L+=o*Ie,q+=o*ve,R+=o*Ae,h+=o*Be,v+=o*Fe,O+=o*Ne,k+=o*Ue,C+=o*ze,o=c[11],te+=o*m,X+=o*pe,ee+=o*me,Y+=o*ge,z+=o*_e,Q+=o*ye,N+=o*le,L+=o*Te,q+=o*Ie,R+=o*ve,h+=o*Ae,v+=o*Be,O+=o*Fe,k+=o*Ne,C+=o*Ue,E+=o*ze,o=c[12],X+=o*m,ee+=o*pe,Y+=o*me,z+=o*ge,Q+=o*_e,N+=o*ye,L+=o*le,q+=o*Te,R+=o*Ie,h+=o*ve,v+=o*Ae,O+=o*Be,k+=o*Fe,C+=o*Ne,E+=o*Ue,j+=o*ze,o=c[13],ee+=o*m,Y+=o*pe,z+=o*me,Q+=o*ge,N+=o*_e,L+=o*ye,q+=o*le,R+=o*Te,h+=o*Ie,v+=o*ve,O+=o*Ae,k+=o*Be,C+=o*Fe,E+=o*Ne,j+=o*Ue,ae+=o*ze,o=c[14],Y+=o*m,z+=o*pe,Q+=o*me,N+=o*ge,L+=o*_e,q+=o*ye,R+=o*le,h+=o*Te,v+=o*Ie,O+=o*ve,k+=o*Ae,C+=o*Be,E+=o*Fe,j+=o*Ne,ae+=o*Ue,se+=o*ze,o=c[15],z+=o*m,Q+=o*pe,N+=o*me,L+=o*ge,q+=o*_e,R+=o*ye,h+=o*le,v+=o*Te,O+=o*Ie,k+=o*ve,C+=o*Ae,E+=o*Be,j+=o*Fe,ae+=o*Ne,se+=o*Ue,oe+=o*ze,f+=38*Q,M+=38*N,T+=38*L,P+=38*q,G+=38*R,D+=38*h,be+=38*v,U+=38*O,W+=38*k,J+=38*C,ne+=38*E,te+=38*j,X+=38*ae,ee+=38*se,Y+=38*oe,y=1,o=f+y+65535,y=Math.floor(o/65536),f=o-y*65536,o=M+y+65535,y=Math.floor(o/65536),M=o-y*65536,o=T+y+65535,y=Math.floor(o/65536),T=o-y*65536,o=P+y+65535,y=Math.floor(o/65536),P=o-y*65536,o=G+y+65535,y=Math.floor(o/65536),G=o-y*65536,o=D+y+65535,y=Math.floor(o/65536),D=o-y*65536,o=be+y+65535,y=Math.floor(o/65536),be=o-y*65536,o=U+y+65535,y=Math.floor(o/65536),U=o-y*65536,o=W+y+65535,y=Math.floor(o/65536),W=o-y*65536,o=J+y+65535,y=Math.floor(o/65536),J=o-y*65536,o=ne+y+65535,y=Math.floor(o/65536),ne=o-y*65536,o=te+y+65535,y=Math.floor(o/65536),te=o-y*65536,o=X+y+65535,y=Math.floor(o/65536),X=o-y*65536,o=ee+y+65535,y=Math.floor(o/65536),ee=o-y*65536,o=Y+y+65535,y=Math.floor(o/65536),Y=o-y*65536,o=z+y+65535,y=Math.floor(o/65536),z=o-y*65536,f+=y-1+37*(y-1),y=1,o=f+y+65535,y=Math.floor(o/65536),f=o-y*65536,o=M+y+65535,y=Math.floor(o/65536),M=o-y*65536,o=T+y+65535,y=Math.floor(o/65536),T=o-y*65536,o=P+y+65535,y=Math.floor(o/65536),P=o-y*65536,o=G+y+65535,y=Math.floor(o/65536),G=o-y*65536,o=D+y+65535,y=Math.floor(o/65536),D=o-y*65536,o=be+y+65535,y=Math.floor(o/65536),be=o-y*65536,o=U+y+65535,y=Math.floor(o/65536),U=o-y*65536,o=W+y+65535,y=Math.floor(o/65536),W=o-y*65536,o=J+y+65535,y=Math.floor(o/65536),J=o-y*65536,o=ne+y+65535,y=Math.floor(o/65536),ne=o-y*65536,o=te+y+65535,y=Math.floor(o/65536),te=o-y*65536,o=X+y+65535,y=Math.floor(o/65536),X=o-y*65536,o=ee+y+65535,y=Math.floor(o/65536),ee=o-y*65536,o=Y+y+65535,y=Math.floor(o/65536),Y=o-y*65536,o=z+y+65535,y=Math.floor(o/65536),z=o-y*65536,f+=y-1+37*(y-1),_[0]=f,_[1]=M,_[2]=T,_[3]=P,_[4]=G,_[5]=D,_[6]=be,_[7]=U,_[8]=W,_[9]=J,_[10]=ne,_[11]=te,_[12]=X,_[13]=ee,_[14]=Y,_[15]=z}function et(_,c){ce(_,c,c)}function vs(_,c){var u=e(),o;for(o=0;o<16;o++)u[o]=c[o];for(o=253;o>=0;o--)et(u,u),o!==2&&o!==4&&ce(u,u,c);for(o=0;o<16;o++)_[o]=u[o]}function As(_,c){var u=e(),o;for(o=0;o<16;o++)u[o]=c[o];for(o=250;o>=0;o--)et(u,u),o!==1&&ce(u,u,c);for(o=0;o<16;o++)_[o]=u[o]}function jn(_,c,u){var o=new Uint8Array(32),y=new Float64Array(80),f,M,T=e(),P=e(),G=e(),D=e(),be=e(),U=e();for(M=0;M<31;M++)o[M]=c[M];for(o[31]=c[31]&127|64,o[0]&=248,ao(y,u),M=0;M<16;M++)P[M]=y[M],D[M]=T[M]=G[M]=0;for(T[0]=D[0]=1,M=254;M>=0;--M)f=o[M>>>3]>>>(M&7)&1,vr(T,P,f),vr(G,D,f),nt(be,T,G),at(T,T,G),nt(G,P,D),at(P,P,D),et(D,be),et(U,T),ce(T,G,T),ce(G,P,be),nt(be,T,G),at(T,T,G),et(P,T),at(G,D,U),ce(T,G,p),nt(T,T,D),ce(G,G,T),ce(T,D,U),ce(D,P,y),et(P,be),vr(T,P,f),vr(G,D,f);for(M=0;M<16;M++)y[M+16]=T[M],y[M+32]=G[M],y[M+48]=P[M],y[M+64]=D[M];var W=y.subarray(32),J=y.subarray(16);return vs(W,W),ce(J,J,W),Ar(_,J),0}function Kn(_,c){return jn(_,c,a)}function Bs(_,c){return t(c,32),Kn(_,c)}function Wn(_,c,u){var o=new Uint8Array(32);return jn(o,u,c),he(_,n,o,w)}var ks=rr,Gp=Mn;function $p(_,c,u,o,y,f){var M=new Uint8Array(32);return Wn(M,y,f),ks(_,c,u,o,M)}function Qp(_,c,u,o,y,f){var M=new Uint8Array(32);return Wn(M,y,f),Gp(_,c,u,o,M)}var Ts=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function Cs(_,c,u,o){for(var y=new Int32Array(16),f=new Int32Array(16),M,T,P,G,D,be,U,W,J,ne,te,X,ee,Y,z,Q,N,L,q,R,h,v,O,k,C,E,j=_[0],ae=_[1],se=_[2],oe=_[3],m=_[4],pe=_[5],me=_[6],ge=_[7],_e=c[0],ye=c[1],le=c[2],Te=c[3],Ie=c[4],ve=c[5],Ae=c[6],Be=c[7],Fe=0;o>=128;){for(q=0;q<16;q++)R=8*q+Fe,y[q]=u[R+0]<<24|u[R+1]<<16|u[R+2]<<8|u[R+3],f[q]=u[R+4]<<24|u[R+5]<<16|u[R+6]<<8|u[R+7];for(q=0;q<80;q++)if(M=j,T=ae,P=se,G=oe,D=m,be=pe,U=me,W=ge,J=_e,ne=ye,te=le,X=Te,ee=Ie,Y=ve,z=Ae,Q=Be,h=ge,v=Be,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=(m>>>14|Ie<<18)^(m>>>18|Ie<<14)^(Ie>>>9|m<<23),v=(Ie>>>14|m<<18)^(Ie>>>18|m<<14)^(m>>>9|Ie<<23),O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,h=m&pe^~m&me,v=Ie&ve^~Ie&Ae,O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,h=Ts[q*2],v=Ts[q*2+1],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,h=y[q%16],v=f[q%16],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,N=C&65535|E<<16,L=O&65535|k<<16,h=N,v=L,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=(j>>>28|_e<<4)^(_e>>>2|j<<30)^(_e>>>7|j<<25),v=(_e>>>28|j<<4)^(j>>>2|_e<<30)^(j>>>7|_e<<25),O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,h=j&ae^j&se^ae&se,v=_e&ye^_e&le^ye&le,O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,W=C&65535|E<<16,Q=O&65535|k<<16,h=G,v=X,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=N,v=L,O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,G=C&65535|E<<16,X=O&65535|k<<16,ae=M,se=T,oe=P,m=G,pe=D,me=be,ge=U,j=W,ye=J,le=ne,Te=te,Ie=X,ve=ee,Ae=Y,Be=z,_e=Q,q%16===15)for(R=0;R<16;R++)h=y[R],v=f[R],O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=y[(R+9)%16],v=f[(R+9)%16],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,N=y[(R+1)%16],L=f[(R+1)%16],h=(N>>>1|L<<31)^(N>>>8|L<<24)^N>>>7,v=(L>>>1|N<<31)^(L>>>8|N<<24)^(L>>>7|N<<25),O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,N=y[(R+14)%16],L=f[(R+14)%16],h=(N>>>19|L<<13)^(L>>>29|N<<3)^N>>>6,v=(L>>>19|N<<13)^(N>>>29|L<<3)^(L>>>6|N<<26),O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,y[R]=C&65535|E<<16,f[R]=O&65535|k<<16;h=j,v=_e,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[0],v=c[0],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[0]=j=C&65535|E<<16,c[0]=_e=O&65535|k<<16,h=ae,v=ye,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[1],v=c[1],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[1]=ae=C&65535|E<<16,c[1]=ye=O&65535|k<<16,h=se,v=le,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[2],v=c[2],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[2]=se=C&65535|E<<16,c[2]=le=O&65535|k<<16,h=oe,v=Te,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[3],v=c[3],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[3]=oe=C&65535|E<<16,c[3]=Te=O&65535|k<<16,h=m,v=Ie,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[4],v=c[4],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[4]=m=C&65535|E<<16,c[4]=Ie=O&65535|k<<16,h=pe,v=ve,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[5],v=c[5],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[5]=pe=C&65535|E<<16,c[5]=ve=O&65535|k<<16,h=me,v=Ae,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[6],v=c[6],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[6]=me=C&65535|E<<16,c[6]=Ae=O&65535|k<<16,h=ge,v=Be,O=v&65535,k=v>>>16,C=h&65535,E=h>>>16,h=_[7],v=c[7],O+=v&65535,k+=v>>>16,C+=h&65535,E+=h>>>16,k+=O>>>16,C+=k>>>16,E+=C>>>16,_[7]=ge=C&65535|E<<16,c[7]=Be=O&65535|k<<16,Fe+=128,o-=128}return o}function nr(_,c,u){var o=new Int32Array(8),y=new Int32Array(8),f=new Uint8Array(256),M,T=u;for(o[0]=1779033703,o[1]=3144134277,o[2]=1013904242,o[3]=2773480762,o[4]=1359893119,o[5]=2600822924,o[6]=528734635,o[7]=1541459225,y[0]=4089235720,y[1]=2227873595,y[2]=4271175723,y[3]=1595750129,y[4]=2917565137,y[5]=725511199,y[6]=4215389547,y[7]=327033209,Cs(o,y,c,u),u%=128,M=0;M<u;M++)f[M]=c[T-u+M];for(f[u]=128,u=256-128*(u<112?1:0),f[u-9]=0,B(f,u-8,T/536870912|0,T<<3),Cs(o,y,f,u),M=0;M<8;M++)B(_,8*M,o[M],y[M]);return 0}function Jn(_,c){var u=e(),o=e(),y=e(),f=e(),M=e(),T=e(),P=e(),G=e(),D=e();at(u,_[1],_[0]),at(D,c[1],c[0]),ce(u,u,D),nt(o,_[0],_[1]),nt(D,c[0],c[1]),ce(o,o,D),ce(y,_[3],c[3]),ce(y,y,d),ce(f,_[2],c[2]),nt(f,f,f),at(M,o,u),at(T,f,y),nt(P,f,y),nt(G,o,u),ce(_[0],M,T),ce(_[1],G,P),ce(_[2],P,T),ce(_[3],M,G)}function ws(_,c,u){var o;for(o=0;o<4;o++)vr(_[o],c[o],u)}function oo(_,c){var u=e(),o=e(),y=e();vs(y,c[2]),ce(u,c[0],y),ce(o,c[1],y),Ar(_,o),_[31]^=hs(u)<<7}function so(_,c,u){var o,y;for(Ze(_[0],i),Ze(_[1],s),Ze(_[2],s),Ze(_[3],i),y=255;y>=0;--y)o=u[y/8|0]>>(y&7)&1,ws(_,c,o),Jn(c,_),Jn(_,_),ws(_,c,o)}function Xn(_,c){var u=[e(),e(),e(),e()];Ze(u[0],g),Ze(u[1],b),Ze(u[2],s),ce(u[3],g,b),so(_,u,c)}function io(_,c,u){var o=new Uint8Array(64),y=[e(),e(),e(),e()],f;for(u||t(c,32),nr(o,c,32),o[0]&=248,o[31]&=127,o[31]|=64,Xn(y,o),oo(_,y),f=0;f<32;f++)c[f+32]=_[f];return 0}var Yn=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function _o(_,c){var u,o,y,f;for(o=63;o>=32;--o){for(u=0,y=o-32,f=o-12;y<f;++y)c[y]+=u-16*c[o]*Yn[y-(o-32)],u=Math.floor((c[y]+128)/256),c[y]-=u*256;c[y]+=u,c[o]=0}for(u=0,y=0;y<32;y++)c[y]+=u-(c[31]>>4)*Yn[y],u=c[y]>>8,c[y]&=255;for(y=0;y<32;y++)c[y]-=u*Yn[y];for(o=0;o<32;o++)c[o+1]+=c[o]>>8,_[o]=c[o]&255}function po(_){var c=new Float64Array(64),u;for(u=0;u<64;u++)c[u]=_[u];for(u=0;u<64;u++)_[u]=0;_o(_,c)}function Os(_,c,u,o){var y=new Uint8Array(64),f=new Uint8Array(64),M=new Uint8Array(64),T,P,G=new Float64Array(64),D=[e(),e(),e(),e()];nr(y,o,32),y[0]&=248,y[31]&=127,y[31]|=64;var be=u+64;for(T=0;T<u;T++)_[64+T]=c[T];for(T=0;T<32;T++)_[32+T]=y[32+T];for(nr(M,_.subarray(32),u+32),po(M),Xn(D,M),oo(_,D),T=32;T<64;T++)_[T]=o[T];for(nr(f,_,u+64),po(f),T=0;T<64;T++)G[T]=0;for(T=0;T<32;T++)G[T]=M[T];for(T=0;T<32;T++)for(P=0;P<32;P++)G[T+P]+=f[T]*y[P];return _o(_.subarray(32),G),be}function jp(_,c){var u=e(),o=e(),y=e(),f=e(),M=e(),T=e(),P=e();return Ze(_[2],s),ao(_[1],c),et(y,_[1]),ce(f,y,l),at(y,y,_[2]),nt(f,_[2],f),et(M,f),et(T,M),ce(P,T,M),ce(u,P,y),ce(u,u,f),As(u,u),ce(u,u,y),ce(u,u,f),ce(u,u,f),ce(_[0],u,f),et(o,_[0]),ce(o,o,f),xs(o,y)&&ce(_[0],_[0],I),et(o,_[0]),ce(o,o,f),xs(o,y)?-1:(hs(_[0])===c[31]>>7&&at(_[0],i,_[0]),ce(_[3],_[0],_[1]),0)}function uo(_,c,u,o){var y,f=new Uint8Array(32),M=new Uint8Array(64),T=[e(),e(),e(),e()],P=[e(),e(),e(),e()];if(u<64||jp(P,o))return-1;for(y=0;y<u;y++)_[y]=c[y];for(y=0;y<32;y++)_[y+32]=o[y];if(nr(M,_,u),po(M),so(T,P,M),Xn(P,c.subarray(32)),Jn(T,P),oo(f,T),u-=64,V(c,0,f,0)){for(y=0;y<u;y++)_[y]=0;return-1}for(y=0;y<u;y++)_[y]=c[y+64];return u}var co=32,Zn=24,Sn=32,Br=16,In=32,ea=32,xn=32,hn=32,yo=32,Es=Zn,Kp=Sn,Wp=Br,Mt=64,ar=32,kr=64,lo=32,bo=64;r.lowlevel={crypto_core_hsalsa20:he,crypto_stream_xor:Ke,crypto_stream:Ye,crypto_stream_salsa20_xor:xr,crypto_stream_salsa20:hr,crypto_onetimeauth:er,crypto_onetimeauth_verify:tr,crypto_verify_16:S,crypto_verify_32:V,crypto_secretbox:rr,crypto_secretbox_open:Mn,crypto_scalarmult:jn,crypto_scalarmult_base:Kn,crypto_box_beforenm:Wn,crypto_box_afternm:ks,crypto_box:$p,crypto_box_open:Qp,crypto_box_keypair:Bs,crypto_hash:nr,crypto_sign:Os,crypto_sign_keypair:io,crypto_sign_open:uo,crypto_secretbox_KEYBYTES:co,crypto_secretbox_NONCEBYTES:Zn,crypto_secretbox_ZEROBYTES:Sn,crypto_secretbox_BOXZEROBYTES:Br,crypto_scalarmult_BYTES:In,crypto_scalarmult_SCALARBYTES:ea,crypto_box_PUBLICKEYBYTES:xn,crypto_box_SECRETKEYBYTES:hn,crypto_box_BEFORENMBYTES:yo,crypto_box_NONCEBYTES:Es,crypto_box_ZEROBYTES:Kp,crypto_box_BOXZEROBYTES:Wp,crypto_sign_BYTES:Mt,crypto_sign_PUBLICKEYBYTES:ar,crypto_sign_SECRETKEYBYTES:kr,crypto_sign_SEEDBYTES:lo,crypto_hash_BYTES:bo,gf:e,D:l,L:Yn,pack25519:Ar,unpack25519:ao,M:ce,A:nt,S:et,Z:at,pow2523:As,add:Jn,set25519:Ze,modL:_o,scalarmult:so,scalarbase:Xn};function Fs(_,c){if(_.length!==co)throw new Error("bad key size");if(c.length!==Zn)throw new Error("bad nonce size")}function Jp(_,c){if(_.length!==xn)throw new Error("bad public key size");if(c.length!==hn)throw new Error("bad secret key size")}function We(){for(var _=0;_<arguments.length;_++)if(!(arguments[_]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function Ps(_){for(var c=0;c<_.length;c++)_[c]=0}r.randomBytes=function(_){var c=new Uint8Array(_);return t(c,_),c},r.secretbox=function(_,c,u){We(_,c,u),Fs(u,c);for(var o=new Uint8Array(Sn+_.length),y=new Uint8Array(o.length),f=0;f<_.length;f++)o[f+Sn]=_[f];return rr(y,o,o.length,c,u),y.subarray(Br)},r.secretbox.open=function(_,c,u){We(_,c,u),Fs(u,c);for(var o=new Uint8Array(Br+_.length),y=new Uint8Array(o.length),f=0;f<_.length;f++)o[f+Br]=_[f];return o.length<32||Mn(y,o,o.length,c,u)!==0?null:y.subarray(Sn)},r.secretbox.keyLength=co,r.secretbox.nonceLength=Zn,r.secretbox.overheadLength=Br,r.scalarMult=function(_,c){if(We(_,c),_.length!==ea)throw new Error("bad n size");if(c.length!==In)throw new Error("bad p size");var u=new Uint8Array(In);return jn(u,_,c),u},r.scalarMult.base=function(_){if(We(_),_.length!==ea)throw new Error("bad n size");var c=new Uint8Array(In);return Kn(c,_),c},r.scalarMult.scalarLength=ea,r.scalarMult.groupElementLength=In,r.box=function(_,c,u,o){var y=r.box.before(u,o);return r.secretbox(_,c,y)},r.box.before=function(_,c){We(_,c),Jp(_,c);var u=new Uint8Array(yo);return Wn(u,_,c),u},r.box.after=r.secretbox,r.box.open=function(_,c,u,o){var y=r.box.before(u,o);return r.secretbox.open(_,c,y)},r.box.open.after=r.secretbox.open,r.box.keyPair=function(){var _=new Uint8Array(xn),c=new Uint8Array(hn);return Bs(_,c),{publicKey:_,secretKey:c}},r.box.keyPair.fromSecretKey=function(_){if(We(_),_.length!==hn)throw new Error("bad secret key size");var c=new Uint8Array(xn);return Kn(c,_),{publicKey:c,secretKey:new Uint8Array(_)}},r.box.publicKeyLength=xn,r.box.secretKeyLength=hn,r.box.sharedKeyLength=yo,r.box.nonceLength=Es,r.box.overheadLength=r.secretbox.overheadLength,r.sign=function(_,c){if(We(_,c),c.length!==kr)throw new Error("bad secret key size");var u=new Uint8Array(Mt+_.length);return Os(u,_,_.length,c),u},r.sign.open=function(_,c){if(We(_,c),c.length!==ar)throw new Error("bad public key size");var u=new Uint8Array(_.length),o=uo(u,_,_.length,c);if(o<0)return null;for(var y=new Uint8Array(o),f=0;f<y.length;f++)y[f]=u[f];return y},r.sign.detached=function(_,c){for(var u=r.sign(_,c),o=new Uint8Array(Mt),y=0;y<o.length;y++)o[y]=u[y];return o},r.sign.detached.verify=function(_,c,u){if(We(_,c,u),c.length!==Mt)throw new Error("bad signature size");if(u.length!==ar)throw new Error("bad public key size");var o=new Uint8Array(Mt+_.length),y=new Uint8Array(Mt+_.length),f;for(f=0;f<Mt;f++)o[f]=c[f];for(f=0;f<_.length;f++)o[f+Mt]=_[f];return uo(y,o,o.length,u)>=0},r.sign.keyPair=function(){var _=new Uint8Array(ar),c=new Uint8Array(kr);return io(_,c),{publicKey:_,secretKey:c}},r.sign.keyPair.fromSecretKey=function(_){if(We(_),_.length!==kr)throw new Error("bad secret key size");for(var c=new Uint8Array(ar),u=0;u<c.length;u++)c[u]=_[32+u];return{publicKey:c,secretKey:new Uint8Array(_)}},r.sign.keyPair.fromSeed=function(_){if(We(_),_.length!==lo)throw new Error("bad seed size");for(var c=new Uint8Array(ar),u=new Uint8Array(kr),o=0;o<32;o++)u[o]=_[o];return io(c,u,!0),{publicKey:c,secretKey:u}},r.sign.publicKeyLength=ar,r.sign.secretKeyLength=kr,r.sign.seedLength=lo,r.sign.signatureLength=Mt,r.hash=function(_){We(_);var c=new Uint8Array(bo);return nr(c,_,_.length),c},r.hash.hashLength=bo,r.verify=function(_,c){return We(_,c),_.length===0||c.length===0||_.length!==c.length?!1:x(_,0,c,0,_.length)===0},r.setPRNG=function(_){t=_},function(){var _=typeof self!="undefined"?self.crypto||self.msCrypto:null;if(_&&_.getRandomValues){var c=65536;r.setPRNG(function(u,o){var y,f=new Uint8Array(o);for(y=0;y<o;y+=c)_.getRandomValues(f.subarray(y,y+Math.min(o-y,c)));for(y=0;y<o;y++)u[y]=f[y];Ps(f)})}else typeof Rs!="undefined"&&(_=Ns(),_&&_.randomBytes&&r.setPRNG(function(u,o){var y,f=_.randomBytes(o);for(y=0;y<o;y++)u[y]=f[y];Ps(f)}))}()})(typeof ta!="undefined"&&ta.exports?ta.exports:self.nacl=self.nacl||{})});var R_=mo((aI,gs)=>{"use strict";var vy=Object.prototype.hasOwnProperty,je="~";function Qn(){}Object.create&&(Qn.prototype=Object.create(null),new Qn().__proto__||(je=!1));function Ay(r,e,t){this.fn=r,this.context=e,this.once=t||!1}function V_(r,e,t,n,a){if(typeof t!="function")throw new TypeError("The listener must be a function");var i=new Ay(t,n||r,a),s=je?je+e:e;return r._events[s]?r._events[s].fn?r._events[s]=[r._events[s],i]:r._events[s].push(i):(r._events[s]=i,r._eventsCount++),r}function to(r,e){--r._eventsCount===0?r._events=new Qn:delete r._events[e]}function He(){this._events=new Qn,this._eventsCount=0}He.prototype.eventNames=function(){var e=[],t,n;if(this._eventsCount===0)return e;for(n in t=this._events)vy.call(t,n)&&e.push(je?n.slice(1):n);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e};He.prototype.listeners=function(e){var t=je?je+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var a=0,i=n.length,s=new Array(i);a<i;a++)s[a]=n[a].fn;return s};He.prototype.listenerCount=function(e){var t=je?je+e:e,n=this._events[t];return n?n.fn?1:n.length:0};He.prototype.emit=function(e,t,n,a,i,s){var p=je?je+e:e;if(!this._events[p])return!1;var l=this._events[p],d=arguments.length,g,b;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,a),!0;case 5:return l.fn.call(l.context,t,n,a,i),!0;case 6:return l.fn.call(l.context,t,n,a,i,s),!0}for(b=1,g=new Array(d-1);b<d;b++)g[b-1]=arguments[b];l.fn.apply(l.context,g)}else{var I=l.length,B;for(b=0;b<I;b++)switch(l[b].once&&this.removeListener(e,l[b].fn,void 0,!0),d){case 1:l[b].fn.call(l[b].context);break;case 2:l[b].fn.call(l[b].context,t);break;case 3:l[b].fn.call(l[b].context,t,n);break;case 4:l[b].fn.call(l[b].context,t,n,a);break;default:if(!g)for(B=1,g=new Array(d-1);B<d;B++)g[B-1]=arguments[B];l[b].fn.apply(l[b].context,g)}}return!0};He.prototype.on=function(e,t,n){return V_(this,e,t,n,!1)};He.prototype.once=function(e,t,n){return V_(this,e,t,n,!0)};He.prototype.removeListener=function(e,t,n,a){var i=je?je+e:e;if(!this._events[i])return this;if(!t)return to(this,i),this;var s=this._events[i];if(s.fn)s.fn===t&&(!a||s.once)&&(!n||s.context===n)&&to(this,i);else{for(var p=0,l=[],d=s.length;p<d;p++)(s[p].fn!==t||a&&!s[p].once||n&&s[p].context!==n)&&l.push(s[p]);l.length?this._events[i]=l.length===1?l[0]:l:to(this,i)}return this};He.prototype.removeAllListeners=function(e){var t;return e?(t=je?je+e:e,this._events[t]&&to(this,t)):(this._events=new Qn,this._eventsCount=0),this};He.prototype.off=He.prototype.removeListener;He.prototype.addListener=He.prototype.on;He.prefixed=je;He.EventEmitter=He;typeof gs!="undefined"&&(gs.exports=He)});var By={};Tr(By,{APTOS_COIN:()=>sr,AccountSequenceNumber:()=>$n,Account_Transactions_Select_Column:()=>L_,Address_Events_Summary_Select_Column:()=>H_,Address_Version_From_Events_Select_Column:()=>q_,Address_Version_From_Move_Resources_Select_Column:()=>G_,AnsClient:()=>ms,ApiError:()=>Ve,AptosAccount:()=>fr,AptosApiError:()=>ir,AptosClient:()=>Me,AptosToken:()=>cs,BCS:()=>Fa,Block_Metadata_Transactions_Select_Column:()=>$_,CKDPriv:()=>yi,COIN_TRANSFER:()=>F_,CoinClient:()=>ls,Coin_Activities_Select_Column:()=>Q_,Coin_Balances_Select_Column:()=>j_,Coin_Infos_Select_Column:()=>K_,Coin_Supply_Select_Column:()=>W_,Collection_Datas_Select_Column:()=>J_,Current_Ans_Lookup_Select_Column:()=>X_,Current_Ans_Lookup_V2_Select_Column:()=>Y_,Current_Aptos_Names_Select_Column:()=>Z_,Current_Coin_Balances_Select_Column:()=>ep,Current_Collection_Datas_Select_Column:()=>tp,Current_Collection_Ownership_V2_View_Select_Column:()=>rp,Current_Collections_V2_Select_Column:()=>np,Current_Delegated_Staking_Pool_Balances_Select_Column:()=>ap,Current_Delegated_Voter_Select_Column:()=>op,Current_Delegator_Balances_Select_Column:()=>sp,Current_Fungible_Asset_Balances_Select_Column:()=>ip,Current_Objects_Select_Column:()=>_p,Current_Staking_Pool_Voter_Select_Column:()=>pp,Current_Table_Items_Select_Column:()=>up,Current_Token_Datas_Select_Column:()=>cp,Current_Token_Datas_V2_Select_Column:()=>yp,Current_Token_Ownerships_Select_Column:()=>lp,Current_Token_Ownerships_V2_Select_Column:()=>dp,Current_Token_Pending_Claims_Select_Column:()=>bp,Cursor_Ordering:()=>mp,Delegated_Staking_Activities_Select_Column:()=>gp,Delegated_Staking_Pools_Select_Column:()=>fp,Delegator_Distinct_Pool_Select_Column:()=>Mp,Events_Select_Column:()=>Sp,FailedTransactionError:()=>Wa,FaucetClient:()=>ds,FungibleAssetClient:()=>Zt,Fungible_Asset_Activities_Select_Column:()=>Ip,Fungible_Asset_Metadata_Select_Column:()=>xp,HexString:()=>F,IndexerClient:()=>ln,Indexer_Status_Select_Column:()=>hp,Ledger_Infos_Select_Column:()=>vp,Move_Resources_Select_Column:()=>Ap,Network:()=>e_,NetworkToIndexerAPI:()=>zr,NetworkToNodeAPI:()=>es,Nft_Marketplace_V2_Current_Nft_Marketplace_Auctions_Select_Column:()=>Bp,Nft_Marketplace_V2_Current_Nft_Marketplace_Collection_Offers_Select_Column:()=>kp,Nft_Marketplace_V2_Current_Nft_Marketplace_Listings_Select_Column:()=>Tp,Nft_Marketplace_V2_Current_Nft_Marketplace_Token_Offers_Select_Column:()=>Cp,Nft_Marketplace_V2_Nft_Marketplace_Activities_Select_Column:()=>wp,Nft_Metadata_Crawler_Parsed_Asset_Uris_Select_Column:()=>Op,NodeAPIToNetwork:()=>Oa,Num_Active_Delegator_Per_Pool_Select_Column:()=>Ep,Order_By:()=>Fp,Processor_Status_Select_Column:()=>Pp,PropertyMap:()=>Yt,PropertyValue:()=>dn,Proposal_Votes_Select_Column:()=>Vp,Provider:()=>Xt,TRANSFER_COINS:()=>ys,Table_Items_Select_Column:()=>Rp,Table_Metadatas_Select_Column:()=>Dp,TokenClient:()=>us,TokenTypes:()=>Za,Token_Activities_Select_Column:()=>Np,Token_Activities_V2_Select_Column:()=>Up,Token_Datas_Select_Column:()=>zp,Token_Ownerships_Select_Column:()=>Lp,Tokens_Select_Column:()=>Hp,TransactionBuilder:()=>Qe,TransactionBuilderABI:()=>ja,TransactionBuilderEd25519:()=>Ir,TransactionBuilderMultiEd25519:()=>Hn,TransactionBuilderRemoteABI:()=>fe,TransactionWorker:()=>fs,TransactionWorkerEvents:()=>z_,TxnBuilderTypes:()=>$,TypeTagParser:()=>pt,Types:()=>ro,User_Transactions_Select_Column:()=>qp,WaitForTransactionError:()=>Ka,ansContractsMap:()=>bs,aptosRequest:()=>Nn,argToTransactionArgument:()=>ps,derivePath:()=>Oo,deserializePropertyMap:()=>Ya,deserializeValueBasedOnTypeTag:()=>E_,ensureBigInt:()=>Sr,ensureBoolean:()=>is,ensureNumber:()=>Mr,get:()=>Re,getAddressFromAccountOrAddress:()=>cn,getMasterKeyFromSeed:()=>ci,getPropertyType:()=>Ja,getPropertyValueRaw:()=>bn,getPublicKey:()=>tc,getSinglePropertyValueRaw:()=>Xa,isValidPath:()=>li,nameComponentPattern:()=>Et,namePattern:()=>P_,post:()=>lt,serializeArg:()=>yn});var zn=go(fo());function St(r){if(!Number.isSafeInteger(r)||r<0)throw new Error(`Wrong positive integer: ${r}`)}function ru(r){return r instanceof Uint8Array||r!=null&&typeof r=="object"&&r.constructor.name==="Uint8Array"}function An(r,...e){if(!ru(r))throw new Error("Expected Uint8Array");if(e.length>0&&!e.includes(r.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${r.length}`)}function ra(r){if(typeof r!="function"||typeof r.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");St(r.outputLen),St(r.blockLen)}function It(r,e=!0){if(r.destroyed)throw new Error("Hash instance has been destroyed");if(e&&r.finished)throw new Error("Hash#digest() has already been called")}function na(r,e){An(r);let t=e.outputLen;if(r.length<t)throw new Error(`digestInto() expects output buffer of length at least ${t}`)}var zs=r=>new Uint32Array(r.buffer,r.byteOffset,Math.floor(r.byteLength/4));function Ls(r){return r instanceof Uint8Array||r!=null&&typeof r=="object"&&r.constructor.name==="Uint8Array"}var Cr=r=>new DataView(r.buffer,r.byteOffset,r.byteLength),ot=(r,e)=>r<<32-e|r>>>e,nu=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!nu)throw new Error("Non little-endian hardware is not supported");var au=Array.from({length:256},(r,e)=>e.toString(16).padStart(2,"0"));function aa(r){if(!Ls(r))throw new Error("Uint8Array expected");let e="";for(let t=0;t<r.length;t++)e+=au[r[t]];return e}var xt={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function Us(r){if(r>=xt._0&&r<=xt._9)return r-xt._0;if(r>=xt._A&&r<=xt._F)return r-(xt._A-10);if(r>=xt._a&&r<=xt._f)return r-(xt._a-10)}function oa(r){if(typeof r!="string")throw new Error("hex string expected, got "+typeof r);let e=r.length,t=e/2;if(e%2)throw new Error("padded hex string expected, got unpadded hex of length "+e);let n=new Uint8Array(t);for(let a=0,i=0;a<t;a++,i+=2){let s=Us(r.charCodeAt(i)),p=Us(r.charCodeAt(i+1));if(s===void 0||p===void 0){let l=r[i]+r[i+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+i)}n[a]=s*16+p}return n}function ou(r){if(typeof r!="string")throw new Error(`utf8ToBytes expected string, got ${typeof r}`);return new Uint8Array(new TextEncoder().encode(r))}function st(r){if(typeof r=="string"&&(r=ou(r)),!Ls(r))throw new Error(`expected Uint8Array, got ${typeof r}`);return r}var Ft=class{clone(){return this._cloneInto()}},su={}.toString;function Hs(r,e){if(e!==void 0&&su.call(e)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(r,e)}function wr(r){let e=n=>r().update(st(n)).digest(),t=r();return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=()=>r(),e}function qs(r){let e=(n,a)=>r(a).update(st(n)).digest(),t=r({});return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=n=>r(n),e}var sa=class extends Ft{constructor(e,t){super(),this.finished=!1,this.destroyed=!1,ra(e);let n=st(t);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;let a=this.blockLen,i=new Uint8Array(a);i.set(n.length>a?e.create().update(n).digest():n);for(let s=0;s<i.length;s++)i[s]^=54;this.iHash.update(i),this.oHash=e.create();for(let s=0;s<i.length;s++)i[s]^=106;this.oHash.update(i),i.fill(0)}update(e){return It(this),this.iHash.update(e),this}digestInto(e){It(this),An(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){let e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));let{oHash:t,iHash:n,finished:a,destroyed:i,blockLen:s,outputLen:p}=this;return e=e,e.finished=a,e.destroyed=i,e.blockLen=s,e.outputLen=p,e.oHash=t._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}},Or=(r,e,t)=>new sa(r,e).update(t).digest();Or.create=(r,e)=>new sa(r,e);function iu(r,e,t,n){ra(r);let a=Hs({dkLen:32,asyncTick:10},n),{c:i,dkLen:s,asyncTick:p}=a;if(St(i),St(s),St(p),i<1)throw new Error("PBKDF2: iterations (c) should be >= 1");let l=st(e),d=st(t),g=new Uint8Array(s),b=Or.create(r,l),I=b._cloneInto().update(d);return{c:i,dkLen:s,asyncTick:p,DK:g,PRF:b,PRFSalt:I}}function _u(r,e,t,n,a){return r.destroy(),e.destroy(),n&&n.destroy(),a.fill(0),t}function Gs(r,e,t,n){let{c:a,dkLen:i,DK:s,PRF:p,PRFSalt:l}=iu(r,e,t,n),d,g=new Uint8Array(4),b=Cr(g),I=new Uint8Array(p.outputLen);for(let B=1,x=0;x<i;B++,x+=p.outputLen){let S=s.subarray(x,x+p.outputLen);b.setInt32(0,B,!1),(d=l._cloneInto(d)).update(g).digestInto(I),S.set(I.subarray(0,S.length));for(let V=1;V<a;V++){p._cloneInto(d).update(I).digestInto(I);for(let Z=0;Z<S.length;Z++)S[Z]^=I[Z]}}return _u(p,l,s,d,I)}function pu(r,e,t,n){if(typeof r.setBigUint64=="function")return r.setBigUint64(e,t,n);let a=BigInt(32),i=BigInt(4294967295),s=Number(t>>a&i),p=Number(t&i),l=n?4:0,d=n?0:4;r.setUint32(e+l,s,n),r.setUint32(e+d,p,n)}var Er=class extends Ft{constructor(e,t,n,a){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=a,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Cr(this.buffer)}update(e){It(this);let{view:t,buffer:n,blockLen:a}=this;e=st(e);let i=e.length;for(let s=0;s<i;){let p=Math.min(a-this.pos,i-s);if(p===a){let l=Cr(e);for(;a<=i-s;s+=a)this.process(l,s);continue}n.set(e.subarray(s,s+p),this.pos),this.pos+=p,s+=p,this.pos===a&&(this.process(t,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){It(this),na(e,this),this.finished=!0;let{buffer:t,view:n,blockLen:a,isLE:i}=this,{pos:s}=this;t[s++]=128,this.buffer.subarray(s).fill(0),this.padOffset>a-s&&(this.process(n,0),s=0);for(let b=s;b<a;b++)t[b]=0;pu(n,a-8,BigInt(this.length*8),i),this.process(n,0);let p=Cr(e),l=this.outputLen;if(l%4)throw new Error("_sha2: outputLen should be aligned to 32bit");let d=l/4,g=this.get();if(d>g.length)throw new Error("_sha2: outputLen bigger than state");for(let b=0;b<d;b++)p.setUint32(4*b,g[b],i)}digest(){let{buffer:e,outputLen:t}=this;this.digestInto(e);let n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());let{blockLen:t,buffer:n,length:a,finished:i,destroyed:s,pos:p}=this;return e.length=a,e.pos=p,e.finished=i,e.destroyed=s,a%t&&e.buffer.set(n),e}};var uu=(r,e,t)=>r&e^~r&t,cu=(r,e,t)=>r&e^r&t^e&t,yu=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Pt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Vt=new Uint32Array(64),Mo=class extends Er{constructor(){super(64,32,8,!1),this.A=Pt[0]|0,this.B=Pt[1]|0,this.C=Pt[2]|0,this.D=Pt[3]|0,this.E=Pt[4]|0,this.F=Pt[5]|0,this.G=Pt[6]|0,this.H=Pt[7]|0}get(){let{A:e,B:t,C:n,D:a,E:i,F:s,G:p,H:l}=this;return[e,t,n,a,i,s,p,l]}set(e,t,n,a,i,s,p,l){this.A=e|0,this.B=t|0,this.C=n|0,this.D=a|0,this.E=i|0,this.F=s|0,this.G=p|0,this.H=l|0}process(e,t){for(let b=0;b<16;b++,t+=4)Vt[b]=e.getUint32(t,!1);for(let b=16;b<64;b++){let I=Vt[b-15],B=Vt[b-2],x=ot(I,7)^ot(I,18)^I>>>3,S=ot(B,17)^ot(B,19)^B>>>10;Vt[b]=S+Vt[b-7]+x+Vt[b-16]|0}let{A:n,B:a,C:i,D:s,E:p,F:l,G:d,H:g}=this;for(let b=0;b<64;b++){let I=ot(p,6)^ot(p,11)^ot(p,25),B=g+I+uu(p,l,d)+yu[b]+Vt[b]|0,S=(ot(n,2)^ot(n,13)^ot(n,22))+cu(n,a,i)|0;g=d,d=l,l=p,p=s+B|0,s=i,i=a,a=n,n=B+S|0}n=n+this.A|0,a=a+this.B|0,i=i+this.C|0,s=s+this.D|0,p=p+this.E|0,l=l+this.F|0,d=d+this.G|0,g=g+this.H|0,this.set(n,a,i,s,p,l,d,g)}roundClean(){Vt.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}};var $s=wr(()=>new Mo);var ia=BigInt(4294967295),So=BigInt(32);function Qs(r,e=!1){return e?{h:Number(r&ia),l:Number(r>>So&ia)}:{h:Number(r>>So&ia)|0,l:Number(r&ia)|0}}function Io(r,e=!1){let t=new Uint32Array(r.length),n=new Uint32Array(r.length);for(let a=0;a<r.length;a++){let{h:i,l:s}=Qs(r[a],e);[t[a],n[a]]=[i,s]}return[t,n]}var lu=(r,e)=>BigInt(r>>>0)<<So|BigInt(e>>>0),du=(r,e,t)=>r>>>t,bu=(r,e,t)=>r<<32-t|e>>>t,mu=(r,e,t)=>r>>>t|e<<32-t,gu=(r,e,t)=>r<<32-t|e>>>t,fu=(r,e,t)=>r<<64-t|e>>>t-32,Mu=(r,e,t)=>r>>>t-32|e<<64-t,Su=(r,e)=>e,Iu=(r,e)=>r,xo=(r,e,t)=>r<<t|e>>>32-t,ho=(r,e,t)=>e<<t|r>>>32-t,vo=(r,e,t)=>e<<t-32|r>>>64-t,Ao=(r,e,t)=>r<<t-32|e>>>64-t;function xu(r,e,t,n){let a=(e>>>0)+(n>>>0);return{h:r+t+(a/2**32|0)|0,l:a|0}}var hu=(r,e,t)=>(r>>>0)+(e>>>0)+(t>>>0),vu=(r,e,t,n)=>e+t+n+(r/2**32|0)|0,Au=(r,e,t,n)=>(r>>>0)+(e>>>0)+(t>>>0)+(n>>>0),Bu=(r,e,t,n,a)=>e+t+n+a+(r/2**32|0)|0,ku=(r,e,t,n,a)=>(r>>>0)+(e>>>0)+(t>>>0)+(n>>>0)+(a>>>0),Tu=(r,e,t,n,a,i)=>e+t+n+a+i+(r/2**32|0)|0;var Cu={fromBig:Qs,split:Io,toBig:lu,shrSH:du,shrSL:bu,rotrSH:mu,rotrSL:gu,rotrBH:fu,rotrBL:Mu,rotr32H:Su,rotr32L:Iu,rotlSH:xo,rotlSL:ho,rotlBH:vo,rotlBL:Ao,add:xu,add3L:hu,add3H:vu,add4L:Au,add4H:Bu,add5H:Tu,add5L:ku},ie=Cu;var[wu,Ou]=ie.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(r=>BigInt(r))),Rt=new Uint32Array(80),Dt=new Uint32Array(80),Bo=class extends Er{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){let{Ah:e,Al:t,Bh:n,Bl:a,Ch:i,Cl:s,Dh:p,Dl:l,Eh:d,El:g,Fh:b,Fl:I,Gh:B,Gl:x,Hh:S,Hl:V}=this;return[e,t,n,a,i,s,p,l,d,g,b,I,B,x,S,V]}set(e,t,n,a,i,s,p,l,d,g,b,I,B,x,S,V){this.Ah=e|0,this.Al=t|0,this.Bh=n|0,this.Bl=a|0,this.Ch=i|0,this.Cl=s|0,this.Dh=p|0,this.Dl=l|0,this.Eh=d|0,this.El=g|0,this.Fh=b|0,this.Fl=I|0,this.Gh=B|0,this.Gl=x|0,this.Hh=S|0,this.Hl=V|0}process(e,t){for(let H=0;H<16;H++,t+=4)Rt[H]=e.getUint32(t),Dt[H]=e.getUint32(t+=4);for(let H=16;H<80;H++){let he=Rt[H-15]|0,w=Dt[H-15]|0,xr=ie.rotrSH(he,w,1)^ie.rotrSH(he,w,8)^ie.shrSH(he,w,7),hr=ie.rotrSL(he,w,1)^ie.rotrSL(he,w,8)^ie.shrSL(he,w,7),Ye=Rt[H-2]|0,Ke=Dt[H-2]|0,ft=ie.rotrSH(Ye,Ke,19)^ie.rotrBH(Ye,Ke,61)^ie.shrSH(Ye,Ke,6),er=ie.rotrSL(Ye,Ke,19)^ie.rotrBL(Ye,Ke,61)^ie.shrSL(Ye,Ke,6),tr=ie.add4L(hr,er,Dt[H-7],Dt[H-16]),rr=ie.add4H(tr,xr,ft,Rt[H-7],Rt[H-16]);Rt[H]=rr|0,Dt[H]=tr|0}let{Ah:n,Al:a,Bh:i,Bl:s,Ch:p,Cl:l,Dh:d,Dl:g,Eh:b,El:I,Fh:B,Fl:x,Gh:S,Gl:V,Hh:Z,Hl:K}=this;for(let H=0;H<80;H++){let he=ie.rotrSH(b,I,14)^ie.rotrSH(b,I,18)^ie.rotrBH(b,I,41),w=ie.rotrSL(b,I,14)^ie.rotrSL(b,I,18)^ie.rotrBL(b,I,41),xr=b&B^~b&S,hr=I&x^~I&V,Ye=ie.add5L(K,w,hr,Ou[H],Dt[H]),Ke=ie.add5H(Ye,Z,he,xr,wu[H],Rt[H]),ft=Ye|0,er=ie.rotrSH(n,a,28)^ie.rotrBH(n,a,34)^ie.rotrBH(n,a,39),tr=ie.rotrSL(n,a,28)^ie.rotrBL(n,a,34)^ie.rotrBL(n,a,39),rr=n&i^n&p^i&p,Mn=a&s^a&l^s&l;Z=S|0,K=V|0,S=B|0,V=x|0,B=b|0,x=I|0,{h:b,l:I}=ie.add(d|0,g|0,Ke|0,ft|0),d=p|0,g=l|0,p=i|0,l=s|0,i=n|0,s=a|0;let Ze=ie.add3L(ft,tr,Mn);n=ie.add3H(Ze,Ke,er,rr),a=Ze|0}({h:n,l:a}=ie.add(this.Ah|0,this.Al|0,n|0,a|0)),{h:i,l:s}=ie.add(this.Bh|0,this.Bl|0,i|0,s|0),{h:p,l}=ie.add(this.Ch|0,this.Cl|0,p|0,l|0),{h:d,l:g}=ie.add(this.Dh|0,this.Dl|0,d|0,g|0),{h:b,l:I}=ie.add(this.Eh|0,this.El|0,b|0,I|0),{h:B,l:x}=ie.add(this.Fh|0,this.Fl|0,B|0,x|0),{h:S,l:V}=ie.add(this.Gh|0,this.Gl|0,S|0,V|0),{h:Z,l:K}=ie.add(this.Hh|0,this.Hl|0,Z|0,K|0),this.set(n,a,i,s,p,l,d,g,b,I,B,x,S,V,Z,K)}roundClean(){Rt.fill(0),Dt.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}};var Bn=wr(()=>new Bo);function Fr(r){if(!Number.isSafeInteger(r))throw new Error(`Wrong integer: ${r}`)}function ht(...r){let e=(a,i)=>s=>a(i(s)),t=Array.from(r).reverse().reduce((a,i)=>a?e(a,i.encode):i.encode,void 0),n=r.reduce((a,i)=>a?e(a,i.decode):i.decode,void 0);return{encode:t,decode:n}}function vt(r){return{encode:e=>{if(!Array.isArray(e)||e.length&&typeof e[0]!="number")throw new Error("alphabet.encode input should be an array of numbers");return e.map(t=>{if(Fr(t),t<0||t>=r.length)throw new Error(`Digit index outside alphabet: ${t} (alphabet: ${r.length})`);return r[t]})},decode:e=>{if(!Array.isArray(e)||e.length&&typeof e[0]!="string")throw new Error("alphabet.decode input should be array of strings");return e.map(t=>{if(typeof t!="string")throw new Error(`alphabet.decode: not string element=${t}`);let n=r.indexOf(t);if(n===-1)throw new Error(`Unknown letter: "${t}". Allowed: ${r}`);return n})}}}function At(r=""){if(typeof r!="string")throw new Error("join separator should be string");return{encode:e=>{if(!Array.isArray(e)||e.length&&typeof e[0]!="string")throw new Error("join.encode input should be array of strings");for(let t of e)if(typeof t!="string")throw new Error(`join.encode: non-string input=${t}`);return e.join(r)},decode:e=>{if(typeof e!="string")throw new Error("join.decode input should be string");return e.split(r)}}}function pa(r,e="="){if(Fr(r),typeof e!="string")throw new Error("padding chr should be string");return{encode(t){if(!Array.isArray(t)||t.length&&typeof t[0]!="string")throw new Error("padding.encode input should be array of strings");for(let n of t)if(typeof n!="string")throw new Error(`padding.encode: non-string input=${n}`);for(;t.length*r%8;)t.push(e);return t},decode(t){if(!Array.isArray(t)||t.length&&typeof t[0]!="string")throw new Error("padding.encode input should be array of strings");for(let a of t)if(typeof a!="string")throw new Error(`padding.decode: non-string input=${a}`);let n=t.length;if(n*r%8)throw new Error("Invalid padding: string should have whole number of bytes");for(;n>0&&t[n-1]===e;n--)if(!((n-1)*r%8))throw new Error("Invalid padding: string has too much padding");return t.slice(0,n)}}}function Ys(r){if(typeof r!="function")throw new Error("normalize fn should be function");return{encode:e=>e,decode:e=>r(e)}}function js(r,e,t){if(e<2)throw new Error(`convertRadix: wrong from=${e}, base cannot be less than 2`);if(t<2)throw new Error(`convertRadix: wrong to=${t}, base cannot be less than 2`);if(!Array.isArray(r))throw new Error("convertRadix: data should be array");if(!r.length)return[];let n=0,a=[],i=Array.from(r);for(i.forEach(s=>{if(Fr(s),s<0||s>=e)throw new Error(`Wrong integer: ${s}`)});;){let s=0,p=!0;for(let l=n;l<i.length;l++){let d=i[l],g=e*s+d;if(!Number.isSafeInteger(g)||e*s/e!==s||g-d!==e*s)throw new Error("convertRadix: carry overflow");if(s=g%t,i[l]=Math.floor(g/t),!Number.isSafeInteger(i[l])||i[l]*t+s!==g)throw new Error("convertRadix: carry overflow");if(p)i[l]?p=!1:n=l;else continue}if(a.push(s),p)break}for(let s=0;s<r.length-1&&r[s]===0;s++)a.push(0);return a.reverse()}var Zs=(r,e)=>e?Zs(e,r%e):r,_a=(r,e)=>r+(e-Zs(r,e));function ko(r,e,t,n){if(!Array.isArray(r))throw new Error("convertRadix2: data should be array");if(e<=0||e>32)throw new Error(`convertRadix2: wrong from=${e}`);if(t<=0||t>32)throw new Error(`convertRadix2: wrong to=${t}`);if(_a(e,t)>32)throw new Error(`convertRadix2: carry overflow from=${e} to=${t} carryBits=${_a(e,t)}`);let a=0,i=0,s=2**t-1,p=[];for(let l of r){if(Fr(l),l>=2**e)throw new Error(`convertRadix2: invalid data word=${l} from=${e}`);if(a=a<<e|l,i+e>32)throw new Error(`convertRadix2: carry overflow pos=${i} from=${e}`);for(i+=e;i>=t;i-=t)p.push((a>>i-t&s)>>>0);a&=2**i-1}if(a=a<<t-i&s,!n&&i>=e)throw new Error("Excess padding");if(!n&&a)throw new Error(`Non-zero padding: ${a}`);return n&&i>0&&p.push(a>>>0),p}function Eu(r){return Fr(r),{encode:e=>{if(!(e instanceof Uint8Array))throw new Error("radix.encode input should be Uint8Array");return js(Array.from(e),2**8,r)},decode:e=>{if(!Array.isArray(e)||e.length&&typeof e[0]!="number")throw new Error("radix.decode input should be array of strings");return Uint8Array.from(js(e,r,2**8))}}}function Nt(r,e=!1){if(Fr(r),r<=0||r>32)throw new Error("radix2: bits should be in (0..32]");if(_a(8,r)>32||_a(r,8)>32)throw new Error("radix2: carry overflow");return{encode:t=>{if(!(t instanceof Uint8Array))throw new Error("radix2.encode input should be Uint8Array");return ko(Array.from(t),8,r,!e)},decode:t=>{if(!Array.isArray(t)||t.length&&typeof t[0]!="number")throw new Error("radix2.decode input should be array of strings");return Uint8Array.from(ko(t,r,8,e))}}}function Ks(r){if(typeof r!="function")throw new Error("unsafeWrapper fn should be function");return function(...e){try{return r.apply(null,e)}catch(t){}}}var Fu=ht(Nt(4),vt("0123456789ABCDEF"),At("")),Pu=ht(Nt(5),vt("ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"),pa(5),At("")),Zy=ht(Nt(5),vt("0123456789ABCDEFGHIJKLMNOPQRSTUV"),pa(5),At("")),el=ht(Nt(5),vt("0123456789ABCDEFGHJKMNPQRSTVWXYZ"),At(""),Ys(r=>r.toUpperCase().replace(/O/g,"0").replace(/[IL]/g,"1"))),Vu=ht(Nt(6),vt("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),pa(6),At("")),Ru=ht(Nt(6),vt("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"),pa(6),At("")),wo=r=>ht(Eu(58),vt(r),At("")),To=wo("123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"),tl=wo("123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"),rl=wo("rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz"),Ws=[0,2,3,5,6,7,9,10,11],Du={encode(r){let e="";for(let t=0;t<r.length;t+=8){let n=r.subarray(t,t+8);e+=To.encode(n).padStart(Ws[n.length],"1")}return e},decode(r){let e=[];for(let t=0;t<r.length;t+=11){let n=r.slice(t,t+11),a=Ws.indexOf(n.length),i=To.decode(n);for(let s=0;s<i.length-a;s++)if(i[s]!==0)throw new Error("base58xmr: wrong padding");e=e.concat(Array.from(i.slice(i.length-a)))}return Uint8Array.from(e)}};var Co=ht(vt("qpzry9x8gf2tvdw0s3jn54khce6mua7l"),At("")),Js=[996825010,642813549,513874426,1027748829,705979059];function kn(r){let e=r>>25,t=(r&33554431)<<5;for(let n=0;n<Js.length;n++)(e>>n&1)===1&&(t^=Js[n]);return t}function Xs(r,e,t=1){let n=r.length,a=1;for(let i=0;i<n;i++){let s=r.charCodeAt(i);if(s<33||s>126)throw new Error(`Invalid prefix (${r})`);a=kn(a)^s>>5}a=kn(a);for(let i=0;i<n;i++)a=kn(a)^r.charCodeAt(i)&31;for(let i of e)a=kn(a)^i;for(let i=0;i<6;i++)a=kn(a);return a^=t,Co.encode(ko([a%2**30],30,5,!1))}function ei(r){let e=r==="bech32"?1:734539939,t=Nt(5),n=t.decode,a=t.encode,i=Ks(n);function s(g,b,I=90){if(typeof g!="string")throw new Error(`bech32.encode prefix should be string, not ${typeof g}`);if(!Array.isArray(b)||b.length&&typeof b[0]!="number")throw new Error(`bech32.encode words should be array of numbers, not ${typeof b}`);let B=g.length+7+b.length;if(I!==!1&&B>I)throw new TypeError(`Length ${B} exceeds limit ${I}`);return g=g.toLowerCase(),`${g}1${Co.encode(b)}${Xs(g,b,e)}`}function p(g,b=90){if(typeof g!="string")throw new Error(`bech32.decode input should be string, not ${typeof g}`);if(g.length<8||b!==!1&&g.length>b)throw new TypeError(`Wrong string length: ${g.length} (${g}). Expected (8..${b})`);let I=g.toLowerCase();if(g!==I&&g!==g.toUpperCase())throw new Error("String must be lowercase or uppercase");g=I;let B=g.lastIndexOf("1");if(B===0||B===-1)throw new Error('Letter "1" must be present between prefix and data only');let x=g.slice(0,B),S=g.slice(B+1);if(S.length<6)throw new Error("Data must be at least 6 characters long");let V=Co.decode(S).slice(0,-6),Z=Xs(x,V,e);if(!S.endsWith(Z))throw new Error(`Invalid checksum in ${g}: expected "${Z}"`);return{prefix:x,words:V}}let l=Ks(p);function d(g){let{prefix:b,words:I}=p(g,!1);return{prefix:b,words:I,bytes:n(I)}}return{encode:s,decode:p,decodeToBytes:d,decodeUnsafe:l,fromWords:n,fromWordsUnsafe:i,toWords:a}}var nl=ei("bech32"),al=ei("bech32m"),Nu={encode:r=>new TextDecoder().decode(r),decode:r=>new TextEncoder().encode(r)},Uu=ht(Nt(4),vt("0123456789abcdef"),At(""),Ys(r=>{if(typeof r!="string"||r.length%2)throw new TypeError(`hex.decode: expected string, got ${typeof r} with length ${r.length}`);return r.toLowerCase()})),zu={utf8:Nu,hex:Uu,base16:Fu,base32:Pu,base64:Vu,base64url:Ru,base58:To,base58xmr:Du},ol=`Invalid encoding type. Available types: ${Object.keys(zu).join(", ")}`;function ti(r){if(typeof r!="string")throw new TypeError(`Invalid mnemonic type: ${typeof r}`);return r.normalize("NFKD")}function Lu(r){let e=ti(r),t=e.split(" ");if(![12,15,18,21,24].includes(t.length))throw new Error("Invalid mnemonic");return{nfkd:e,words:t}}var Hu=r=>ti(`mnemonic${r}`);function ri(r,e=""){return Gs(Bn,Lu(r).nfkd,Hu(e),{c:2048,dkLen:64})}var[oi,si,ii]=[[],[],[]],Gu=BigInt(0),Tn=BigInt(1),$u=BigInt(2),Qu=BigInt(7),ju=BigInt(256),Ku=BigInt(113);for(let r=0,e=Tn,t=1,n=0;r<24;r++){[t,n]=[n,(2*t+3*n)%5],oi.push(2*(5*n+t)),si.push((r+1)*(r+2)/2%64);let a=Gu;for(let i=0;i<7;i++)e=(e<<Tn^(e>>Qu)*Ku)%ju,e&$u&&(a^=Tn<<(Tn<<BigInt(i))-Tn);ii.push(a)}var[Wu,Ju]=Io(ii,!0),ni=(r,e,t)=>t>32?vo(r,e,t):xo(r,e,t),ai=(r,e,t)=>t>32?Ao(r,e,t):ho(r,e,t);function Xu(r,e=24){let t=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let s=0;s<10;s++)t[s]=r[s]^r[s+10]^r[s+20]^r[s+30]^r[s+40];for(let s=0;s<10;s+=2){let p=(s+8)%10,l=(s+2)%10,d=t[l],g=t[l+1],b=ni(d,g,1)^t[p],I=ai(d,g,1)^t[p+1];for(let B=0;B<50;B+=10)r[s+B]^=b,r[s+B+1]^=I}let a=r[2],i=r[3];for(let s=0;s<24;s++){let p=si[s],l=ni(a,i,p),d=ai(a,i,p),g=oi[s];a=r[g],i=r[g+1],r[g]=l,r[g+1]=d}for(let s=0;s<50;s+=10){for(let p=0;p<10;p++)t[p]=r[s+p];for(let p=0;p<10;p++)r[s+p]^=~t[(p+2)%10]&t[(p+4)%10]}r[0]^=Wu[n],r[1]^=Ju[n]}t.fill(0)}var ua=class r extends Ft{constructor(e,t,n,a=!1,i=24){if(super(),this.blockLen=e,this.suffix=t,this.outputLen=n,this.enableXOF=a,this.rounds=i,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,St(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=zs(this.state)}keccak(){Xu(this.state32,this.rounds),this.posOut=0,this.pos=0}update(e){It(this);let{blockLen:t,state:n}=this;e=st(e);let a=e.length;for(let i=0;i<a;){let s=Math.min(t-this.pos,a-i);for(let p=0;p<s;p++)n[this.pos++]^=e[i++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:n,blockLen:a}=this;e[n]^=t,t&128&&n===a-1&&this.keccak(),e[a-1]^=128,this.keccak()}writeInto(e){It(this,!1),An(e),this.finish();let t=this.state,{blockLen:n}=this;for(let a=0,i=e.length;a<i;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,i-a);e.set(t.subarray(this.posOut,this.posOut+s),a),this.posOut+=s,a+=s}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return St(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(na(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){let{blockLen:t,suffix:n,outputLen:a,rounds:i,enableXOF:s}=this;return e||(e=new r(t,n,a,s,i)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=i,e.suffix=n,e.outputLen=a,e.enableXOF=s,e.destroyed=this.destroyed,e}},Ut=(r,e,t)=>wr(()=>new ua(e,r,t)),bl=Ut(6,144,224/8),ct=Ut(6,136,256/8),ml=Ut(6,104,384/8),gl=Ut(6,72,512/8),fl=Ut(1,144,224/8),Ml=Ut(1,136,256/8),Sl=Ut(1,104,384/8),Il=Ut(1,72,512/8),_i=(r,e,t)=>qs((n={})=>new ua(e,r,n.dkLen===void 0?t:n.dkLen,!0)),xl=_i(31,168,128/8),hl=_i(31,136,256/8);var pi=go(fo());var Yu=/^m(\/[0-9]+')+$/,ui=r=>r.replace("'",""),Zu="ed25519 seed",ec=2147483648,ci=r=>{let t=Or.create(Bn,Zu).update(oa(r)).digest(),n=t.slice(0,32),a=t.slice(32);return{key:n,chainCode:a}},yi=({key:r,chainCode:e},t)=>{let n=new ArrayBuffer(4);new DataView(n).setUint32(0,t);let a=new Uint8Array(n),i=new Uint8Array([0]),s=new Uint8Array([...i,...r,...a]),p=Or.create(Bn,e).update(s).digest(),l=p.slice(0,32),d=p.slice(32);return{key:l,chainCode:d}},tc=(r,e=!0)=>{let n=pi.default.sign.keyPair.fromSeed(r).secretKey.subarray(32),a=new Uint8Array([0]);return e?new Uint8Array([...a,...n]):n},li=r=>Yu.test(r)?!r.split("/").slice(1).map(ui).some(Number.isNaN):!1,Oo=(r,e,t=ec)=>{if(!li(r))throw new Error("Invalid derivation path");let{key:n,chainCode:a}=ci(e);return r.split("/").slice(1).map(ui).map(s=>parseInt(s,10)).reduce((s,p)=>yi(s,p+t),{key:n,chainCode:a})};var ca="1.21.0";async function or(r){return new Promise(e=>{setTimeout(e,r)})}var di="/v1";function bi(r){let e=`${r}`;return e.endsWith("/")&&(e=e.substring(0,e.length-1)),e.endsWith(di)||(e=`${e}${di}`),e}var ya=2e5,la=20,da=20,sr="0x1::aptos_coin::AptosCoin",Ol={"x-aptos-client":`aptos-ts-sdk/${ca}`};function Pr(r){let e,t,n;return typeof r=="object"?(e=r.hashFunction,t=r.ttlMs,n=r.tags):e=r,(a,i,s)=>{if(s.value!=null)s.value=mi(s.value,e,t,n);else if(s.get!=null)s.get=mi(s.get,e,t,n);else throw new Error("Only put a Memoize() decorator on a method or get accessor.")}}function ma(r,e){return Pr({ttlMs:r,hashFunction:e})}var ba=new Map;function gi(r){let e=new Set;for(let t of r){let n=ba.get(t);if(n)for(let a of n)e.has(a)||(a.clear(),e.add(a))}return e.size}function mi(r,e,t=0,n){let a=Symbol("__memoized_map__");return function(...i){let s,p=this;p.hasOwnProperty(a)||Object.defineProperty(p,a,{configurable:!1,enumerable:!1,writable:!1,value:new Map});let l=p[a];if(Array.isArray(n))for(let d of n)ba.has(d)?ba.get(d).push(l):ba.set(d,[l]);if(e||i.length>0||t>0){let d;e===!0?d=i.map(I=>I.toString()).join("!"):e?d=e.apply(p,i):d=i[0];let g=`${d}__timestamp`,b=!1;if(t>0)if(!l.has(g))b=!0;else{let I=l.get(g);b=Date.now()-I>t}l.has(d)&&!b?s=l.get(d):(s=r.apply(p,i),l.set(d,s),t>0&&l.set(g,Date.now()))}else{let d=p;l.has(d)?s=l.get(d):(s=r.apply(p,i),l.set(d,s))}return s}}function Cn(r,e){return function(){return r.apply(e,arguments)}}var{toString:rc}=Object.prototype,{getPrototypeOf:Po}=Object,fa=(r=>e=>{let t=rc.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),yt=r=>(r=r.toLowerCase(),e=>fa(e)===r),Ma=r=>e=>typeof e===r,{isArray:Vr}=Array,wn=Ma("undefined");function nc(r){return r!==null&&!wn(r)&&r.constructor!==null&&!wn(r.constructor)&&tt(r.constructor.isBuffer)&&r.constructor.isBuffer(r)}var Si=yt("ArrayBuffer");function ac(r){let e;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?e=ArrayBuffer.isView(r):e=r&&r.buffer&&Si(r.buffer),e}var oc=Ma("string"),tt=Ma("function"),Ii=Ma("number"),Sa=r=>r!==null&&typeof r=="object",sc=r=>r===!0||r===!1,ga=r=>{if(fa(r)!=="object")return!1;let e=Po(r);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in r)&&!(Symbol.iterator in r)},ic=yt("Date"),_c=yt("File"),pc=yt("Blob"),uc=yt("FileList"),cc=r=>Sa(r)&&tt(r.pipe),yc=r=>{let e;return r&&(typeof FormData=="function"&&r instanceof FormData||tt(r.append)&&((e=fa(r))==="formdata"||e==="object"&&tt(r.toString)&&r.toString()==="[object FormData]"))},lc=yt("URLSearchParams"),dc=r=>r.trim?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function On(r,e,{allOwnKeys:t=!1}={}){if(r===null||typeof r=="undefined")return;let n,a;if(typeof r!="object"&&(r=[r]),Vr(r))for(n=0,a=r.length;n<a;n++)e.call(null,r[n],n,r);else{let i=t?Object.getOwnPropertyNames(r):Object.keys(r),s=i.length,p;for(n=0;n<s;n++)p=i[n],e.call(null,r[p],p,r)}}function xi(r,e){e=e.toLowerCase();let t=Object.keys(r),n=t.length,a;for(;n-- >0;)if(a=t[n],e===a.toLowerCase())return a;return null}var hi=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global,vi=r=>!wn(r)&&r!==hi;function Fo(){let{caseless:r}=vi(this)&&this||{},e={},t=(n,a)=>{let i=r&&xi(e,a)||a;ga(e[i])&&ga(n)?e[i]=Fo(e[i],n):ga(n)?e[i]=Fo({},n):Vr(n)?e[i]=n.slice():e[i]=n};for(let n=0,a=arguments.length;n<a;n++)arguments[n]&&On(arguments[n],t);return e}var bc=(r,e,t,{allOwnKeys:n}={})=>(On(e,(a,i)=>{t&&tt(a)?r[i]=Cn(a,t):r[i]=a},{allOwnKeys:n}),r),mc=r=>(r.charCodeAt(0)===65279&&(r=r.slice(1)),r),gc=(r,e,t,n)=>{r.prototype=Object.create(e.prototype,n),r.prototype.constructor=r,Object.defineProperty(r,"super",{value:e.prototype}),t&&Object.assign(r.prototype,t)},fc=(r,e,t,n)=>{let a,i,s,p={};if(e=e||{},r==null)return e;do{for(a=Object.getOwnPropertyNames(r),i=a.length;i-- >0;)s=a[i],(!n||n(s,r,e))&&!p[s]&&(e[s]=r[s],p[s]=!0);r=t!==!1&&Po(r)}while(r&&(!t||t(r,e))&&r!==Object.prototype);return e},Mc=(r,e,t)=>{r=String(r),(t===void 0||t>r.length)&&(t=r.length),t-=e.length;let n=r.indexOf(e,t);return n!==-1&&n===t},Sc=r=>{if(!r)return null;if(Vr(r))return r;let e=r.length;if(!Ii(e))return null;let t=new Array(e);for(;e-- >0;)t[e]=r[e];return t},Ic=(r=>e=>r&&e instanceof r)(typeof Uint8Array!="undefined"&&Po(Uint8Array)),xc=(r,e)=>{let n=(r&&r[Symbol.iterator]).call(r),a;for(;(a=n.next())&&!a.done;){let i=a.value;e.call(r,i[0],i[1])}},hc=(r,e)=>{let t,n=[];for(;(t=r.exec(e))!==null;)n.push(t);return n},vc=yt("HTMLFormElement"),Ac=r=>r.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,n,a){return n.toUpperCase()+a}),fi=(({hasOwnProperty:r})=>(e,t)=>r.call(e,t))(Object.prototype),Bc=yt("RegExp"),Ai=(r,e)=>{let t=Object.getOwnPropertyDescriptors(r),n={};On(t,(a,i)=>{let s;(s=e(a,i,r))!==!1&&(n[i]=s||a)}),Object.defineProperties(r,n)},kc=r=>{Ai(r,(e,t)=>{if(tt(r)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;let n=r[t];if(tt(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},Tc=(r,e)=>{let t={},n=a=>{a.forEach(i=>{t[i]=!0})};return Vr(r)?n(r):n(String(r).split(e)),t},Cc=()=>{},wc=(r,e)=>(r=+r,Number.isFinite(r)?r:e),Eo="abcdefghijklmnopqrstuvwxyz",Mi="0123456789",Bi={DIGIT:Mi,ALPHA:Eo,ALPHA_DIGIT:Eo+Eo.toUpperCase()+Mi},Oc=(r=16,e=Bi.ALPHA_DIGIT)=>{let t="",{length:n}=e;for(;r--;)t+=e[Math.random()*n|0];return t};function Ec(r){return!!(r&&tt(r.append)&&r[Symbol.toStringTag]==="FormData"&&r[Symbol.iterator])}var Fc=r=>{let e=new Array(10),t=(n,a)=>{if(Sa(n)){if(e.indexOf(n)>=0)return;if(!("toJSON"in n)){e[a]=n;let i=Vr(n)?[]:{};return On(n,(s,p)=>{let l=t(s,a+1);!wn(l)&&(i[p]=l)}),e[a]=void 0,i}}return n};return t(r,0)},Pc=yt("AsyncFunction"),Vc=r=>r&&(Sa(r)||tt(r))&&tt(r.then)&&tt(r.catch),A={isArray:Vr,isArrayBuffer:Si,isBuffer:nc,isFormData:yc,isArrayBufferView:ac,isString:oc,isNumber:Ii,isBoolean:sc,isObject:Sa,isPlainObject:ga,isUndefined:wn,isDate:ic,isFile:_c,isBlob:pc,isRegExp:Bc,isFunction:tt,isStream:cc,isURLSearchParams:lc,isTypedArray:Ic,isFileList:uc,forEach:On,merge:Fo,extend:bc,trim:dc,stripBOM:mc,inherits:gc,toFlatObject:fc,kindOf:fa,kindOfTest:yt,endsWith:Mc,toArray:Sc,forEachEntry:xc,matchAll:hc,isHTMLForm:vc,hasOwnProperty:fi,hasOwnProp:fi,reduceDescriptors:Ai,freezeMethods:kc,toObjectSet:Tc,toCamelCase:Ac,noop:Cc,toFiniteNumber:wc,findKey:xi,global:hi,isContextDefined:vi,ALPHABET:Bi,generateString:Oc,isSpecCompliantForm:Ec,toJSONObject:Fc,isAsyncFn:Pc,isThenable:Vc};function Rr(r,e,t,n,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=r,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),n&&(this.request=n),a&&(this.response=a)}A.inherits(Rr,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:A.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var ki=Rr.prototype,Ti={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(r=>{Ti[r]={value:r}});Object.defineProperties(Rr,Ti);Object.defineProperty(ki,"isAxiosError",{value:!0});Rr.from=(r,e,t,n,a,i)=>{let s=Object.create(ki);return A.toFlatObject(r,s,function(l){return l!==Error.prototype},p=>p!=="isAxiosError"),Rr.call(s,r.message,e,t,n,a),s.cause=r,s.name=r.name,i&&Object.assign(s,i),s};var ue=Rr;var Ia=null;function Vo(r){return A.isPlainObject(r)||A.isArray(r)}function wi(r){return A.endsWith(r,"[]")?r.slice(0,-2):r}function Ci(r,e,t){return r?r.concat(e).map(function(a,i){return a=wi(a),!t&&i?"["+a+"]":a}).join(t?".":""):e}function Rc(r){return A.isArray(r)&&!r.some(Vo)}var Dc=A.toFlatObject(A,{},null,function(e){return/^is[A-Z]/.test(e)});function Nc(r,e,t){if(!A.isObject(r))throw new TypeError("target must be an object");e=e||new(Ia||FormData),t=A.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,V){return!A.isUndefined(V[S])});let n=t.metaTokens,a=t.visitor||g,i=t.dots,s=t.indexes,l=(t.Blob||typeof Blob!="undefined"&&Blob)&&A.isSpecCompliantForm(e);if(!A.isFunction(a))throw new TypeError("visitor must be a function");function d(x){if(x===null)return"";if(A.isDate(x))return x.toISOString();if(!l&&A.isBlob(x))throw new ue("Blob is not supported. Use a Buffer instead.");return A.isArrayBuffer(x)||A.isTypedArray(x)?l&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function g(x,S,V){let Z=x;if(x&&!V&&typeof x=="object"){if(A.endsWith(S,"{}"))S=n?S:S.slice(0,-2),x=JSON.stringify(x);else if(A.isArray(x)&&Rc(x)||(A.isFileList(x)||A.endsWith(S,"[]"))&&(Z=A.toArray(x)))return S=wi(S),Z.forEach(function(H,he){!(A.isUndefined(H)||H===null)&&e.append(s===!0?Ci([S],he,i):s===null?S:S+"[]",d(H))}),!1}return Vo(x)?!0:(e.append(Ci(V,S,i),d(x)),!1)}let b=[],I=Object.assign(Dc,{defaultVisitor:g,convertValue:d,isVisitable:Vo});function B(x,S){if(!A.isUndefined(x)){if(b.indexOf(x)!==-1)throw Error("Circular reference detected in "+S.join("."));b.push(x),A.forEach(x,function(Z,K){(!(A.isUndefined(Z)||Z===null)&&a.call(e,Z,A.isString(K)?K.trim():K,S,I))===!0&&B(Z,S?S.concat(K):[K])}),b.pop()}}if(!A.isObject(r))throw new TypeError("data must be an object");return B(r),e}var zt=Nc;function Oi(r){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function Ei(r,e){this._pairs=[],r&&zt(r,this,e)}var Fi=Ei.prototype;Fi.append=function(e,t){this._pairs.push([e,t])};Fi.toString=function(e){let t=e?function(n){return e.call(this,n,Oi)}:Oi;return this._pairs.map(function(a){return t(a[0])+"="+t(a[1])},"").join("&")};var xa=Ei;function Uc(r){return encodeURIComponent(r).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function En(r,e,t){if(!e)return r;let n=t&&t.encode||Uc,a=t&&t.serialize,i;if(a?i=a(e,t):i=A.isURLSearchParams(e)?e.toString():new xa(e,t).toString(n),i){let s=r.indexOf("#");s!==-1&&(r=r.slice(0,s)),r+=(r.indexOf("?")===-1?"?":"&")+i}return r}var Ro=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){A.forEach(this.handlers,function(n){n!==null&&e(n)})}},Do=Ro;var ha={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var Pi=typeof URLSearchParams!="undefined"?URLSearchParams:xa;var Vi=typeof FormData!="undefined"?FormData:null;var Ri=typeof Blob!="undefined"?Blob:null;var Di={isBrowser:!0,classes:{URLSearchParams:Pi,FormData:Vi,Blob:Ri},protocols:["http","https","file","blob","url","data"]};var No={};Tr(No,{hasBrowserEnv:()=>Ni,hasStandardBrowserEnv:()=>zc,hasStandardBrowserWebWorkerEnv:()=>Lc});var Ni=typeof window!="undefined"&&typeof document!="undefined",zc=(r=>Ni&&["ReactNative","NativeScript","NS"].indexOf(r)<0)(typeof navigator!="undefined"&&navigator.product),Lc=typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function";var qe={...No,...Di};function Uo(r,e){return zt(r,new qe.classes.URLSearchParams,Object.assign({visitor:function(t,n,a,i){return qe.isNode&&A.isBuffer(t)?(this.append(n,t.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},e))}function Hc(r){return A.matchAll(/\w+|\[(\w*)]/g,r).map(e=>e[0]==="[]"?"":e[1]||e[0])}function qc(r){let e={},t=Object.keys(r),n,a=t.length,i;for(n=0;n<a;n++)i=t[n],e[i]=r[i];return e}function Gc(r){function e(t,n,a,i){let s=t[i++],p=Number.isFinite(+s),l=i>=t.length;return s=!s&&A.isArray(a)?a.length:s,l?(A.hasOwnProp(a,s)?a[s]=[a[s],n]:a[s]=n,!p):((!a[s]||!A.isObject(a[s]))&&(a[s]=[]),e(t,n,a[s],i)&&A.isArray(a[s])&&(a[s]=qc(a[s])),!p)}if(A.isFormData(r)&&A.isFunction(r.entries)){let t={};return A.forEachEntry(r,(n,a)=>{e(Hc(n),a,t,0)}),t}return null}var va=Gc;function $c(r,e,t){if(A.isString(r))try{return(e||JSON.parse)(r),A.trim(r)}catch(n){if(n.name!=="SyntaxError")throw n}return(t||JSON.stringify)(r)}var zo={transitional:ha,adapter:["xhr","http"],transformRequest:[function(e,t){let n=t.getContentType()||"",a=n.indexOf("application/json")>-1,i=A.isObject(e);if(i&&A.isHTMLForm(e)&&(e=new FormData(e)),A.isFormData(e))return a&&a?JSON.stringify(va(e)):e;if(A.isArrayBuffer(e)||A.isBuffer(e)||A.isStream(e)||A.isFile(e)||A.isBlob(e))return e;if(A.isArrayBufferView(e))return e.buffer;if(A.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let p;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Uo(e,this.formSerializer).toString();if((p=A.isFileList(e))||n.indexOf("multipart/form-data")>-1){let l=this.env&&this.env.FormData;return zt(p?{"files[]":e}:e,l&&new l,this.formSerializer)}}return i||a?(t.setContentType("application/json",!1),$c(e)):e}],transformResponse:[function(e){let t=this.transitional||zo.transitional,n=t&&t.forcedJSONParsing,a=this.responseType==="json";if(e&&A.isString(e)&&(n&&!this.responseType||a)){let s=!(t&&t.silentJSONParsing)&&a;try{return JSON.parse(e)}catch(p){if(s)throw p.name==="SyntaxError"?ue.from(p,ue.ERR_BAD_RESPONSE,this,null,this.response):p}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:qe.classes.FormData,Blob:qe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};A.forEach(["delete","get","head","post","put","patch"],r=>{zo.headers[r]={}});var Dr=zo;var Qc=A.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ui=r=>{let e={},t,n,a;return r&&r.split(`
`).forEach(function(s){a=s.indexOf(":"),t=s.substring(0,a).trim().toLowerCase(),n=s.substring(a+1).trim(),!(!t||e[t]&&Qc[t])&&(t==="set-cookie"?e[t]?e[t].push(n):e[t]=[n]:e[t]=e[t]?e[t]+", "+n:n)}),e};var zi=Symbol("internals");function Fn(r){return r&&String(r).trim().toLowerCase()}function Aa(r){return r===!1||r==null?r:A.isArray(r)?r.map(Aa):String(r)}function jc(r){let e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,n;for(;n=t.exec(r);)e[n[1]]=n[2];return e}var Kc=r=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());function Lo(r,e,t,n,a){if(A.isFunction(n))return n.call(this,e,t);if(a&&(e=t),!!A.isString(e)){if(A.isString(n))return e.indexOf(n)!==-1;if(A.isRegExp(n))return n.test(e)}}function Wc(r){return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}function Jc(r,e){let t=A.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(r,n+t,{value:function(a,i,s){return this[n].call(this,e,a,i,s)},configurable:!0})})}var Nr=class{constructor(e){e&&this.set(e)}set(e,t,n){let a=this;function i(p,l,d){let g=Fn(l);if(!g)throw new Error("header name must be a non-empty string");let b=A.findKey(a,g);(!b||a[b]===void 0||d===!0||d===void 0&&a[b]!==!1)&&(a[b||l]=Aa(p))}let s=(p,l)=>A.forEach(p,(d,g)=>i(d,g,l));return A.isPlainObject(e)||e instanceof this.constructor?s(e,t):A.isString(e)&&(e=e.trim())&&!Kc(e)?s(Ui(e),t):e!=null&&i(t,e,n),this}get(e,t){if(e=Fn(e),e){let n=A.findKey(this,e);if(n){let a=this[n];if(!t)return a;if(t===!0)return jc(a);if(A.isFunction(t))return t.call(this,a,n);if(A.isRegExp(t))return t.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Fn(e),e){let n=A.findKey(this,e);return!!(n&&this[n]!==void 0&&(!t||Lo(this,this[n],n,t)))}return!1}delete(e,t){let n=this,a=!1;function i(s){if(s=Fn(s),s){let p=A.findKey(n,s);p&&(!t||Lo(n,n[p],p,t))&&(delete n[p],a=!0)}}return A.isArray(e)?e.forEach(i):i(e),a}clear(e){let t=Object.keys(this),n=t.length,a=!1;for(;n--;){let i=t[n];(!e||Lo(this,this[i],i,e,!0))&&(delete this[i],a=!0)}return a}normalize(e){let t=this,n={};return A.forEach(this,(a,i)=>{let s=A.findKey(n,i);if(s){t[s]=Aa(a),delete t[i];return}let p=e?Wc(i):String(i).trim();p!==i&&delete t[i],t[p]=Aa(a),n[p]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return A.forEach(this,(n,a)=>{n!=null&&n!==!1&&(t[a]=e&&A.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let n=new this(e);return t.forEach(a=>n.set(a)),n}static accessor(e){let n=(this[zi]=this[zi]={accessors:{}}).accessors,a=this.prototype;function i(s){let p=Fn(s);n[p]||(Jc(a,s),n[p]=!0)}return A.isArray(e)?e.forEach(i):i(e),this}};Nr.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);A.reduceDescriptors(Nr.prototype,({value:r},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>r,set(n){this[t]=n}}});A.freezeMethods(Nr);var Ge=Nr;function Pn(r,e){let t=this||Dr,n=e||t,a=Ge.from(n.headers),i=n.data;return A.forEach(r,function(p){i=p.call(t,i,a.normalize(),e?e.status:void 0)}),a.normalize(),i}function Vn(r){return!!(r&&r.__CANCEL__)}function Li(r,e,t){ue.call(this,r==null?"canceled":r,ue.ERR_CANCELED,e,t),this.name="CanceledError"}A.inherits(Li,ue,{__CANCEL__:!0});var Lt=Li;function Ho(r,e,t){let n=t.config.validateStatus;!t.status||!n||n(t.status)?r(t):e(new ue("Request failed with status code "+t.status,[ue.ERR_BAD_REQUEST,ue.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}var Hi=qe.hasStandardBrowserEnv?{write(r,e,t,n,a,i){let s=[r+"="+encodeURIComponent(e)];A.isNumber(t)&&s.push("expires="+new Date(t).toGMTString()),A.isString(n)&&s.push("path="+n),A.isString(a)&&s.push("domain="+a),i===!0&&s.push("secure"),document.cookie=s.join("; ")},read(r){let e=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(r){this.write(r,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function qo(r){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)}function Go(r,e){return e?r.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):r}function Rn(r,e){return r&&!qo(e)?Go(r,e):e}var qi=qe.hasStandardBrowserEnv?function(){let e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a"),n;function a(i){let s=i;return e&&(t.setAttribute("href",s),s=t.href),t.setAttribute("href",s),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:t.pathname.charAt(0)==="/"?t.pathname:"/"+t.pathname}}return n=a(window.location.href),function(s){let p=A.isString(s)?a(s):s;return p.protocol===n.protocol&&p.host===n.host}}():function(){return function(){return!0}}();function $o(r){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(r);return e&&e[1]||""}function Xc(r,e){r=r||10;let t=new Array(r),n=new Array(r),a=0,i=0,s;return e=e!==void 0?e:1e3,function(l){let d=Date.now(),g=n[i];s||(s=d),t[a]=l,n[a]=d;let b=i,I=0;for(;b!==a;)I+=t[b++],b=b%r;if(a=(a+1)%r,a===i&&(i=(i+1)%r),d-s<e)return;let B=g&&d-g;return B?Math.round(I*1e3/B):void 0}}var Gi=Xc;function $i(r,e){let t=0,n=Gi(50,250);return a=>{let i=a.loaded,s=a.lengthComputable?a.total:void 0,p=i-t,l=n(p),d=i<=s;t=i;let g={loaded:i,total:s,progress:s?i/s:void 0,bytes:p,rate:l||void 0,estimated:l&&s&&d?(s-i)/l:void 0,event:a};g[e?"download":"upload"]=!0,r(g)}}var Yc=typeof XMLHttpRequest!="undefined",Qi=Yc&&function(r){return new Promise(function(t,n){let a=r.data,i=Ge.from(r.headers).normalize(),{responseType:s,withXSRFToken:p}=r,l;function d(){r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let g;if(A.isFormData(a)){if(qe.hasStandardBrowserEnv||qe.hasStandardBrowserWebWorkerEnv)i.setContentType(!1);else if((g=i.getContentType())!==!1){let[S,...V]=g?g.split(";").map(Z=>Z.trim()).filter(Boolean):[];i.setContentType([S||"multipart/form-data",...V].join("; "))}}let b=new XMLHttpRequest;if(r.auth){let S=r.auth.username||"",V=r.auth.password?unescape(encodeURIComponent(r.auth.password)):"";i.set("Authorization","Basic "+btoa(S+":"+V))}let I=Rn(r.baseURL,r.url);b.open(r.method.toUpperCase(),En(I,r.params,r.paramsSerializer),!0),b.timeout=r.timeout;function B(){if(!b)return;let S=Ge.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),Z={data:!s||s==="text"||s==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:S,config:r,request:b};Ho(function(H){t(H),d()},function(H){n(H),d()},Z),b=null}if("onloadend"in b?b.onloadend=B:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(B)},b.onabort=function(){b&&(n(new ue("Request aborted",ue.ECONNABORTED,r,b)),b=null)},b.onerror=function(){n(new ue("Network Error",ue.ERR_NETWORK,r,b)),b=null},b.ontimeout=function(){let V=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded",Z=r.transitional||ha;r.timeoutErrorMessage&&(V=r.timeoutErrorMessage),n(new ue(V,Z.clarifyTimeoutError?ue.ETIMEDOUT:ue.ECONNABORTED,r,b)),b=null},qe.hasStandardBrowserEnv&&(p&&A.isFunction(p)&&(p=p(r)),p||p!==!1&&qi(I))){let S=r.xsrfHeaderName&&r.xsrfCookieName&&Hi.read(r.xsrfCookieName);S&&i.set(r.xsrfHeaderName,S)}a===void 0&&i.setContentType(null),"setRequestHeader"in b&&A.forEach(i.toJSON(),function(V,Z){b.setRequestHeader(Z,V)}),A.isUndefined(r.withCredentials)||(b.withCredentials=!!r.withCredentials),s&&s!=="json"&&(b.responseType=r.responseType),typeof r.onDownloadProgress=="function"&&b.addEventListener("progress",$i(r.onDownloadProgress,!0)),typeof r.onUploadProgress=="function"&&b.upload&&b.upload.addEventListener("progress",$i(r.onUploadProgress)),(r.cancelToken||r.signal)&&(l=S=>{b&&(n(!S||S.type?new Lt(null,r,b):S),b.abort(),b=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));let x=$o(I);if(x&&qe.protocols.indexOf(x)===-1){n(new ue("Unsupported protocol "+x+":",ue.ERR_BAD_REQUEST,r));return}b.send(a||null)})};var Qo={http:Ia,xhr:Qi};A.forEach(Qo,(r,e)=>{if(r){try{Object.defineProperty(r,"name",{value:e})}catch(t){}Object.defineProperty(r,"adapterName",{value:e})}});var ji=r=>`- ${r}`,Zc=r=>A.isFunction(r)||r===null||r===!1,Ba={getAdapter:r=>{r=A.isArray(r)?r:[r];let{length:e}=r,t,n,a={};for(let i=0;i<e;i++){t=r[i];let s;if(n=t,!Zc(t)&&(n=Qo[(s=String(t)).toLowerCase()],n===void 0))throw new ue(`Unknown adapter '${s}'`);if(n)break;a[s||"#"+i]=n}if(!n){let i=Object.entries(a).map(([p,l])=>`adapter ${p} `+(l===!1?"is not supported by the environment":"is not available in the build")),s=e?i.length>1?`since :
`+i.map(ji).join(`
`):" "+ji(i[0]):"as no adapter specified";throw new ue("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return n},adapters:Qo};function jo(r){if(r.cancelToken&&r.cancelToken.throwIfRequested(),r.signal&&r.signal.aborted)throw new Lt(null,r)}function ka(r){return jo(r),r.headers=Ge.from(r.headers),r.data=Pn.call(r,r.transformRequest),["post","put","patch"].indexOf(r.method)!==-1&&r.headers.setContentType("application/x-www-form-urlencoded",!1),Ba.getAdapter(r.adapter||Dr.adapter)(r).then(function(n){return jo(r),n.data=Pn.call(r,r.transformResponse,n),n.headers=Ge.from(n.headers),n},function(n){return Vn(n)||(jo(r),n&&n.response&&(n.response.data=Pn.call(r,r.transformResponse,n.response),n.response.headers=Ge.from(n.response.headers))),Promise.reject(n)})}var Ki=r=>r instanceof Ge?r.toJSON():r;function Bt(r,e){e=e||{};let t={};function n(d,g,b){return A.isPlainObject(d)&&A.isPlainObject(g)?A.merge.call({caseless:b},d,g):A.isPlainObject(g)?A.merge({},g):A.isArray(g)?g.slice():g}function a(d,g,b){if(A.isUndefined(g)){if(!A.isUndefined(d))return n(void 0,d,b)}else return n(d,g,b)}function i(d,g){if(!A.isUndefined(g))return n(void 0,g)}function s(d,g){if(A.isUndefined(g)){if(!A.isUndefined(d))return n(void 0,d)}else return n(void 0,g)}function p(d,g,b){if(b in e)return n(d,g);if(b in r)return n(void 0,d)}let l={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:p,headers:(d,g)=>a(Ki(d),Ki(g),!0)};return A.forEach(Object.keys(Object.assign({},r,e)),function(g){let b=l[g]||a,I=b(r[g],e[g],g);A.isUndefined(I)&&b!==p||(t[g]=I)}),t}var Ta="1.6.2";var Ko={};["object","boolean","number","function","string","symbol"].forEach((r,e)=>{Ko[r]=function(n){return typeof n===r||"a"+(e<1?"n ":" ")+r}});var Wi={};Ko.transitional=function(e,t,n){function a(i,s){return"[Axios v"+Ta+"] Transitional option '"+i+"'"+s+(n?". "+n:"")}return(i,s,p)=>{if(e===!1)throw new ue(a(s," has been removed"+(t?" in "+t:"")),ue.ERR_DEPRECATED);return t&&!Wi[s]&&(Wi[s]=!0,console.warn(a(s," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(i,s,p):!0}};function ey(r,e,t){if(typeof r!="object")throw new ue("options must be an object",ue.ERR_BAD_OPTION_VALUE);let n=Object.keys(r),a=n.length;for(;a-- >0;){let i=n[a],s=e[i];if(s){let p=r[i],l=p===void 0||s(p,i,r);if(l!==!0)throw new ue("option "+i+" must be "+l,ue.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new ue("Unknown option "+i,ue.ERR_BAD_OPTION)}}var Ca={assertOptions:ey,validators:Ko};var Ht=Ca.validators,Ur=class{constructor(e){this.defaults=e,this.interceptors={request:new Do,response:new Do}}request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=Bt(this.defaults,t);let{transitional:n,paramsSerializer:a,headers:i}=t;n!==void 0&&Ca.assertOptions(n,{silentJSONParsing:Ht.transitional(Ht.boolean),forcedJSONParsing:Ht.transitional(Ht.boolean),clarifyTimeoutError:Ht.transitional(Ht.boolean)},!1),a!=null&&(A.isFunction(a)?t.paramsSerializer={serialize:a}:Ca.assertOptions(a,{encode:Ht.function,serialize:Ht.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=i&&A.merge(i.common,i[t.method]);i&&A.forEach(["delete","get","head","post","put","patch","common"],x=>{delete i[x]}),t.headers=Ge.concat(s,i);let p=[],l=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(t)===!1||(l=l&&S.synchronous,p.unshift(S.fulfilled,S.rejected))});let d=[];this.interceptors.response.forEach(function(S){d.push(S.fulfilled,S.rejected)});let g,b=0,I;if(!l){let x=[ka.bind(this),void 0];for(x.unshift.apply(x,p),x.push.apply(x,d),I=x.length,g=Promise.resolve(t);b<I;)g=g.then(x[b++],x[b++]);return g}I=p.length;let B=t;for(b=0;b<I;){let x=p[b++],S=p[b++];try{B=x(B)}catch(V){S.call(this,V);break}}try{g=ka.call(this,B)}catch(x){return Promise.reject(x)}for(b=0,I=d.length;b<I;)g=g.then(d[b++],d[b++]);return g}getUri(e){e=Bt(this.defaults,e);let t=Rn(e.baseURL,e.url);return En(t,e.params,e.paramsSerializer)}};A.forEach(["delete","get","head","options"],function(e){Ur.prototype[e]=function(t,n){return this.request(Bt(n||{},{method:e,url:t,data:(n||{}).data}))}});A.forEach(["post","put","patch"],function(e){function t(n){return function(i,s,p){return this.request(Bt(p||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:i,data:s}))}}Ur.prototype[e]=t(),Ur.prototype[e+"Form"]=t(!0)});var Dn=Ur;var Wo=class r{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(i){t=i});let n=this;this.promise.then(a=>{if(!n._listeners)return;let i=n._listeners.length;for(;i-- >0;)n._listeners[i](a);n._listeners=null}),this.promise.then=a=>{let i,s=new Promise(p=>{n.subscribe(p),i=p}).then(a);return s.cancel=function(){n.unsubscribe(i)},s},e(function(i,s,p){n.reason||(n.reason=new Lt(i,s,p),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}static source(){let e;return{token:new r(function(a){e=a}),cancel:e}}},Ji=Wo;function Jo(r){return function(t){return r.apply(null,t)}}function Xo(r){return A.isObject(r)&&r.isAxiosError===!0}var Yo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Yo).forEach(([r,e])=>{Yo[e]=r});var Xi=Yo;function Yi(r){let e=new Dn(r),t=Cn(Dn.prototype.request,e);return A.extend(t,Dn.prototype,e,{allOwnKeys:!0}),A.extend(t,e,null,{allOwnKeys:!0}),t.create=function(a){return Yi(Bt(r,a))},t}var Oe=Yi(Dr);Oe.Axios=Dn;Oe.CanceledError=Lt;Oe.CancelToken=Ji;Oe.isCancel=Vn;Oe.VERSION=Ta;Oe.toFormData=zt;Oe.AxiosError=ue;Oe.Cancel=Oe.CanceledError;Oe.all=function(e){return Promise.all(e)};Oe.spread=Jo;Oe.isAxiosError=Xo;Oe.mergeConfig=Bt;Oe.AxiosHeaders=Ge;Oe.formToJSON=r=>va(A.isHTMLForm(r)?new FormData(r):r);Oe.getAdapter=Ba.getAdapter;Oe.HttpStatusCode=Xi;Oe.default=Oe;var wa=Oe;var{Axios:pm,AxiosError:um,CanceledError:cm,isCancel:ym,CancelToken:lm,VERSION:dm,all:bm,Cancel:mm,isAxiosError:gm,spread:fm,toFormData:Mm,AxiosHeaders:Sm,HttpStatusCode:Im,formToJSON:xm,getAdapter:hm,mergeConfig:vm}=wa;async function Zi(r){var e;let{params:t,method:n,url:a,headers:i,body:s,overrides:p}=r,l={headers:i,method:n,url:a,params:t,data:s,withCredentials:(e=p==null?void 0:p.WITH_CREDENTIALS)!=null?e:!0};try{let d=await wa(l);return{status:d.status,statusText:d.statusText,data:d.data,headers:d.headers,config:d.config}}catch(d){let g=d;if(g.response)return g.response;throw d}}var ir=class extends Error{constructor(t,n,a){super(a);this.name="AptosApiError",this.url=n.url,this.status=n.status,this.statusText=n.statusText,this.data=n.data,this.request=t}};var ty={400:"Bad Request",401:"Unauthorized",403:"Forbidden",404:"Not Found",429:"Too Many Requests",500:"Internal Server Error",502:"Bad Gateway",503:"Service Unavailable"};async function ry(r,e,t,n,a,i){let s={...i==null?void 0:i.HEADERS,"x-aptos-client":`aptos-ts-sdk/${ca}`,"content-type":n!=null?n:"application/json"};return i!=null&&i.TOKEN&&(s.Authorization=`Bearer ${i==null?void 0:i.TOKEN}`),await Zi({url:r,method:e,body:t,params:a,headers:s,overrides:i})}async function Nn(r){let{url:e,endpoint:t,method:n,body:a,contentType:i,params:s,overrides:p}=r,l=`${e}/${t!=null?t:""}`,d=await ry(l,n,a,i,s,p),g={status:d.status,statusText:d.statusText,data:d.data,headers:d.headers,config:d.config,url:l};if(g.status>=200&&g.status<300)return g;let b=ty[g.status];throw new ir(r,g,b!=null?b:"Generic Error")}async function Re(r){return await Nn({...r,method:"GET"})}async function lt(r){return await Nn({...r,method:"POST"})}async function Zo(r){let e=[],t,n=r.params;for(;;){n.start=t;let a=await Re({url:r.url,endpoint:r.endpoint,params:n,originMethod:r.originMethod,overrides:r.overrides});if(t=a.headers["x-aptos-cursor"],delete a.headers,e.push(...a.data),t==null)break}return e}var zr={mainnet:"https://indexer.mainnet.aptoslabs.com/v1/graphql",testnet:"https://indexer-testnet.staging.gcp.aptosdev.com/v1/graphql",devnet:"https://indexer-devnet.staging.gcp.aptosdev.com/v1/graphql",local:"http://127.0.0.1:8090/v1/graphql"},es={mainnet:"https://fullnode.mainnet.aptoslabs.com/v1",testnet:"https://fullnode.testnet.aptoslabs.com/v1",devnet:"https://fullnode.devnet.aptoslabs.com/v1",local:"http://127.0.0.1:8080/v1"},Oa={"https://fullnode.mainnet.aptoslabs.com/v1":"mainnet","https://fullnode.testnet.aptoslabs.com/v1":"testnet","https://fullnode.devnet.aptoslabs.com/v1":"devnet","http://127.0.0.1:8080/v1":"local"},e_=(a=>(a.MAINNET="mainnet",a.TESTNET="testnet",a.DEVNET="devnet",a.LOCAL="local",a))(e_||{});var F=class r{static fromBuffer(e){return r.fromUint8Array(e)}static fromUint8Array(e){return new r(aa(e))}static ensure(e){return typeof e=="string"?new r(e):e}constructor(e){e.startsWith("0x")?this.hexString=e:this.hexString=`0x${e}`}hex(){return this.hexString}noPrefix(){return this.hexString.slice(2)}toString(){return this.hex()}toShortString(){return`0x${this.hexString.replace(/^0x0*/,"")}`}toUint8Array(){return Uint8Array.from(oa(this.noPrefix()))}};var $={};Tr($,{AccountAddress:()=>re,AccountAuthenticator:()=>dt,AccountAuthenticatorEd25519:()=>Ra,AccountAuthenticatorMultiEd25519:()=>Da,ArgumentABI:()=>mr,AuthenticationKey:()=>pn,ChainId:()=>Xr,ChangeSet:()=>as,Ed25519PublicKey:()=>De,Ed25519Signature:()=>Je,EntryFunction:()=>yr,EntryFunctionABI:()=>Jt,FeePayerRawTransaction:()=>Kr,Identifier:()=>Ce,Module:()=>ns,ModuleId:()=>Ot,MultiAgentRawTransaction:()=>jr,MultiEd25519PublicKey:()=>qt,MultiEd25519Signature:()=>qr,MultiSig:()=>Ha,MultiSigTransactionPayload:()=>La,RawTransaction:()=>wt,RawTransactionWithData:()=>Un,RotationProofChallenge:()=>ss,Script:()=>Qr,ScriptABI:()=>gr,SignedTransaction:()=>lr,StructTag:()=>Wt,Transaction:()=>Ga,TransactionArgument:()=>Xe,TransactionArgumentAddress:()=>an,TransactionArgumentBool:()=>sn,TransactionArgumentU128:()=>rn,TransactionArgumentU16:()=>Zr,TransactionArgumentU256:()=>nn,TransactionArgumentU32:()=>en,TransactionArgumentU64:()=>tn,TransactionArgumentU8:()=>Yr,TransactionArgumentU8Vector:()=>on,TransactionAuthenticator:()=>Ct,TransactionAuthenticatorEd25519:()=>Gr,TransactionAuthenticatorFeePayer:()=>Va,TransactionAuthenticatorMultiAgent:()=>Pa,TransactionAuthenticatorMultiEd25519:()=>$r,TransactionPayload:()=>dr,TransactionPayloadEntryFunction:()=>Jr,TransactionPayloadMultisig:()=>qa,TransactionPayloadScript:()=>Wr,TransactionScriptABI:()=>_n,TypeArgumentABI:()=>br,TypeTag:()=>we,TypeTagAddress:()=>it,TypeTagBool:()=>bt,TypeTagParser:()=>pt,TypeTagParserError:()=>Ua,TypeTagSigner:()=>Na,TypeTagStruct:()=>_t,TypeTagU128:()=>gt,TypeTagU16:()=>$t,TypeTagU256:()=>jt,TypeTagU32:()=>Qt,TypeTagU64:()=>mt,TypeTagU8:()=>rt,TypeTagVector:()=>Kt,UserTransaction:()=>$a,WriteSet:()=>os,objectStructTag:()=>ly,optionStructTag:()=>yy,stringStructTag:()=>za});var Fa={};Tr(Fa,{Deserializer:()=>ur,Serializer:()=>Se,bcsSerializeBool:()=>py,bcsSerializeBytes:()=>Tt,bcsSerializeFixedBytes:()=>cy,bcsSerializeStr:()=>uy,bcsSerializeU128:()=>iy,bcsSerializeU16:()=>oy,bcsSerializeU256:()=>_y,bcsSerializeU32:()=>sy,bcsSerializeU8:()=>Ea,bcsSerializeUint64:()=>ay,bcsToBytes:()=>Le,deserializeVector:()=>Pe,serializeVector:()=>ke,serializeVectorWithFunc:()=>ny});var t_=2**8-1,r_=2**16-1,_r=2**32-1,kt=BigInt(2**64)-BigInt(1),ts=BigInt(2**128)-BigInt(1),n_=BigInt(2**256)-BigInt(1);var Se=class{constructor(){this.buffer=new ArrayBuffer(64),this.offset=0}ensureBufferWillHandleSize(e){for(;this.buffer.byteLength<this.offset+e;){let t=new ArrayBuffer(this.buffer.byteLength*2);new Uint8Array(t).set(new Uint8Array(this.buffer)),this.buffer=t}}serialize(e){this.ensureBufferWillHandleSize(e.length),new Uint8Array(this.buffer,this.offset).set(e),this.offset+=e.length}serializeWithFunction(e,t,n){this.ensureBufferWillHandleSize(t);let a=new DataView(this.buffer,this.offset);e.apply(a,[0,n,!0]),this.offset+=t}serializeStr(e){let t=new TextEncoder;this.serializeBytes(t.encode(e))}serializeBytes(e){this.serializeU32AsUleb128(e.length),this.serialize(e)}serializeFixedBytes(e){this.serialize(e)}serializeBool(e){if(typeof e!="boolean")throw new Error("Value needs to be a boolean");let t=e?1:0;this.serialize(new Uint8Array([t]))}serializeU8(e){this.serialize(new Uint8Array([e]))}serializeU16(e){this.serializeWithFunction(DataView.prototype.setUint16,2,e)}serializeU32(e){this.serializeWithFunction(DataView.prototype.setUint32,4,e)}serializeU64(e){let t=BigInt(e.toString())&BigInt(_r),n=BigInt(e.toString())>>BigInt(32);this.serializeU32(Number(t)),this.serializeU32(Number(n))}serializeU128(e){let t=BigInt(e.toString())&kt,n=BigInt(e.toString())>>BigInt(64);this.serializeU64(t),this.serializeU64(n)}serializeU256(e){let t=BigInt(e.toString())&ts,n=BigInt(e.toString())>>BigInt(128);this.serializeU128(t),this.serializeU128(n)}serializeU32AsUleb128(e){let t=e,n=[];for(;t>>>7;)n.push(t&127|128),t>>>=7;n.push(t),this.serialize(new Uint8Array(n))}getBytes(){return new Uint8Array(this.buffer).slice(0,this.offset)}};de([pr(0,t_)],Se.prototype,"serializeU8",1),de([pr(0,r_)],Se.prototype,"serializeU16",1),de([pr(0,_r)],Se.prototype,"serializeU32",1),de([pr(BigInt(0),kt)],Se.prototype,"serializeU64",1),de([pr(BigInt(0),ts)],Se.prototype,"serializeU128",1),de([pr(BigInt(0),n_)],Se.prototype,"serializeU256",1),de([pr(0,_r)],Se.prototype,"serializeU32AsUleb128",1);function pr(r,e,t){return(n,a,i)=>{let s=i.value;return i.value=function(l){let d=BigInt(l.toString());if(d>BigInt(e.toString())||d<BigInt(r.toString()))throw new Error(t||"Value is out of range");s.apply(this,[l])},i}}var ur=class{constructor(e){this.buffer=new ArrayBuffer(e.length),new Uint8Array(this.buffer).set(e,0),this.offset=0}read(e){if(this.offset+e>this.buffer.byteLength)throw new Error("Reached to the end of buffer");let t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}deserializeStr(){let e=this.deserializeBytes();return new TextDecoder().decode(e)}deserializeBytes(){let e=this.deserializeUleb128AsU32();return new Uint8Array(this.read(e))}deserializeFixedBytes(e){return new Uint8Array(this.read(e))}deserializeBool(){let e=new Uint8Array(this.read(1))[0];if(e!==1&&e!==0)throw new Error("Invalid boolean value");return e===1}deserializeU8(){return new DataView(this.read(1)).getUint8(0)}deserializeU16(){return new DataView(this.read(2)).getUint16(0,!0)}deserializeU32(){return new DataView(this.read(4)).getUint32(0,!0)}deserializeU64(){let e=this.deserializeU32(),t=this.deserializeU32();return BigInt(BigInt(t)<<BigInt(32)|BigInt(e))}deserializeU128(){let e=this.deserializeU64(),t=this.deserializeU64();return BigInt(t<<BigInt(64)|e)}deserializeU256(){let e=this.deserializeU128(),t=this.deserializeU128();return BigInt(t<<BigInt(128)|e)}deserializeUleb128AsU32(){let e=BigInt(0),t=0;for(;e<_r;){let n=this.deserializeU8();if(e|=BigInt(n&127)<<BigInt(t),!(n&128))break;t+=7}if(e>_r)throw new Error("Overflow while parsing uleb128-encoded uint32 value");return Number(e)}};function ke(r,e){e.serializeU32AsUleb128(r.length),r.forEach(t=>{t.serialize(e)})}function ny(r,e){let t=new Se;t.serializeU32AsUleb128(r.length);let n=t[e];return r.forEach(a=>{n.call(t,a)}),t.getBytes()}function Pe(r,e){let t=r.deserializeUleb128AsU32(),n=[];for(let a=0;a<t;a+=1)n.push(e.deserialize(r));return n}function Le(r){let e=new Se;return r.serialize(e),e.getBytes()}function ay(r){let e=new Se;return e.serializeU64(r),e.getBytes()}function Ea(r){let e=new Se;return e.serializeU8(r),e.getBytes()}function oy(r){let e=new Se;return e.serializeU16(r),e.getBytes()}function sy(r){let e=new Se;return e.serializeU32(r),e.getBytes()}function iy(r){let e=new Se;return e.serializeU128(r),e.getBytes()}function _y(r){let e=new Se;return e.serializeU256(r),e.getBytes()}function py(r){let e=new Se;return e.serializeBool(r),e.getBytes()}function uy(r){let e=new Se;return e.serializeStr(r),e.getBytes()}function Tt(r){let e=new Se;return e.serializeBytes(r),e.getBytes()}function cy(r){let e=new Se;return e.serializeFixedBytes(r),e.getBytes()}var $e=class $e{constructor(e){if(e.length!==$e.LENGTH)throw new Error("Expected address of length 32");this.address=e}static fromHex(e){let t=F.ensure(e);t.noPrefix().length%2!==0&&(t=new F(`0${t.noPrefix()}`));let n=t.toUint8Array();if(n.length>$e.LENGTH)throw new Error("Hex string is too long. Address's length is 32 bytes.");if(n.length===$e.LENGTH)return new $e(n);let a=new Uint8Array($e.LENGTH);return a.set(n,$e.LENGTH-n.length),new $e(a)}static isValid(e){if(e==="")return!1;let t=F.ensure(e);return t.noPrefix().length%2!==0&&(t=new F(`0${t.noPrefix()}`)),t.toUint8Array().length<=$e.LENGTH}toHexString(){return F.fromUint8Array(this.address).hex()}serialize(e){e.serializeFixedBytes(this.address)}static deserialize(e){return new $e(e.deserializeFixedBytes($e.LENGTH))}static standardizeAddress(e){let t=e.toLowerCase();return`0x${(t.startsWith("0x")?t.slice(2):t).padStart(64,"0")}`}};$e.LENGTH=32,$e.CORE_CODE_ADDRESS=$e.fromHex("0x1");var re=$e;var Lr=class Lr{constructor(e){if(e.length!==Lr.LENGTH)throw new Error(`Ed25519PublicKey length should be ${Lr.LENGTH}`);this.value=e}toBytes(){return this.value}serialize(e){e.serializeBytes(this.value)}static deserialize(e){let t=e.deserializeBytes();return new Lr(t)}};Lr.LENGTH=32;var De=Lr,Hr=class Hr{constructor(e){this.value=e;if(e.length!==Hr.LENGTH)throw new Error(`Ed25519Signature length should be ${Hr.LENGTH}`)}serialize(e){e.serializeBytes(this.value)}static deserialize(e){let t=e.deserializeBytes();return new Hr(t)}};Hr.LENGTH=64;var Je=Hr;var rs=32,qt=class r{constructor(e,t){this.public_keys=e;this.threshold=t;if(t>rs)throw new Error(`"threshold" cannot be larger than ${rs}`)}toBytes(){let e=new Uint8Array(this.public_keys.length*De.LENGTH+1);return this.public_keys.forEach((t,n)=>{e.set(t.value,n*De.LENGTH)}),e[this.public_keys.length*De.LENGTH]=this.threshold,e}serialize(e){e.serializeBytes(this.toBytes())}static deserialize(e){let t=e.deserializeBytes(),n=t[t.length-1],a=[];for(let i=0;i<t.length-1;i+=De.LENGTH){let s=i;a.push(new De(t.subarray(s,s+De.LENGTH)))}return new r(a,n)}},cr=class cr{constructor(e,t){this.signatures=e;this.bitmap=t;if(t.length!==cr.BITMAP_LEN)throw new Error(`"bitmap" length should be ${cr.BITMAP_LEN}`)}toBytes(){let e=new Uint8Array(this.signatures.length*Je.LENGTH+cr.BITMAP_LEN);return this.signatures.forEach((t,n)=>{e.set(t.value,n*Je.LENGTH)}),e.set(this.bitmap,this.signatures.length*Je.LENGTH),e}static createBitmap(e){let n=new Uint8Array([0,0,0,0]),a=new Set;return e.forEach(i=>{if(i>=rs)throw new Error(`Invalid bit value ${i}.`);if(a.has(i))throw new Error("Duplicated bits detected.");a.add(i);let s=Math.floor(i/8),p=n[s];p|=128>>i%8,n[s]=p}),n}serialize(e){e.serializeBytes(this.toBytes())}static deserialize(e){let t=e.deserializeBytes(),n=t.subarray(t.length-4),a=[];for(let i=0;i<t.length-n.length;i+=Je.LENGTH){let s=i;a.push(new Je(t.subarray(s,s+Je.LENGTH)))}return new cr(a,n)}};cr.BITMAP_LEN=4;var qr=cr;var Ct=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return Gr.load(e);case 1:return $r.load(e);case 2:return Pa.load(e);case 3:return Va.load(e);default:throw new Error(`Unknown variant index for TransactionAuthenticator: ${t}`)}}},Gr=class r extends Ct{constructor(t,n){super();this.public_key=t;this.signature=n}serialize(t){t.serializeU32AsUleb128(0),this.public_key.serialize(t),this.signature.serialize(t)}static load(t){let n=De.deserialize(t),a=Je.deserialize(t);return new r(n,a)}},$r=class r extends Ct{constructor(t,n){super();this.public_key=t;this.signature=n}serialize(t){t.serializeU32AsUleb128(1),this.public_key.serialize(t),this.signature.serialize(t)}static load(t){let n=qt.deserialize(t),a=qr.deserialize(t);return new r(n,a)}},Pa=class r extends Ct{constructor(t,n,a){super();this.sender=t;this.secondary_signer_addresses=n;this.secondary_signers=a}serialize(t){t.serializeU32AsUleb128(2),this.sender.serialize(t),ke(this.secondary_signer_addresses,t),ke(this.secondary_signers,t)}static load(t){let n=dt.deserialize(t),a=Pe(t,re),i=Pe(t,dt);return new r(n,a,i)}},Va=class r extends Ct{constructor(t,n,a,i){super();this.sender=t;this.secondary_signer_addresses=n;this.secondary_signers=a;this.fee_payer=i}serialize(t){t.serializeU32AsUleb128(3),this.sender.serialize(t),ke(this.secondary_signer_addresses,t),ke(this.secondary_signers,t),this.fee_payer.address.serialize(t),this.fee_payer.authenticator.serialize(t)}static load(t){let n=dt.deserialize(t),a=Pe(t,re),i=Pe(t,dt),s=re.deserialize(t),p=dt.deserialize(t),l={address:s,authenticator:p};return new r(n,a,i,l)}},dt=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return Ra.load(e);case 1:return Da.load(e);default:throw new Error(`Unknown variant index for AccountAuthenticator: ${t}`)}}},Ra=class r extends dt{constructor(t,n){super();this.public_key=t;this.signature=n}serialize(t){t.serializeU32AsUleb128(0),this.public_key.serialize(t),this.signature.serialize(t)}static load(t){let n=De.deserialize(t),a=Je.deserialize(t);return new r(n,a)}},Da=class r extends dt{constructor(t,n){super();this.public_key=t;this.signature=n}serialize(t){t.serializeU32AsUleb128(1),this.public_key.serialize(t),this.signature.serialize(t)}static load(t){let n=qt.deserialize(t),a=qr.deserialize(t);return new r(n,a)}};var Ce=class r{constructor(e){this.value=e}serialize(e){e.serializeStr(this.value)}static deserialize(e){let t=e.deserializeStr();return new r(t)}};var we=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return bt.load(e);case 1:return rt.load(e);case 2:return mt.load(e);case 3:return gt.load(e);case 4:return it.load(e);case 5:return Na.load(e);case 6:return Kt.load(e);case 7:return _t.load(e);case 8:return $t.load(e);case 9:return Qt.load(e);case 10:return jt.load(e);default:throw new Error(`Unknown variant index for TypeTag: ${t}`)}}},bt=class r extends we{serialize(e){e.serializeU32AsUleb128(0)}static load(e){return new r}},rt=class r extends we{serialize(e){e.serializeU32AsUleb128(1)}static load(e){return new r}},$t=class r extends we{serialize(e){e.serializeU32AsUleb128(8)}static load(e){return new r}},Qt=class r extends we{serialize(e){e.serializeU32AsUleb128(9)}static load(e){return new r}},mt=class r extends we{serialize(e){e.serializeU32AsUleb128(2)}static load(e){return new r}},gt=class r extends we{serialize(e){e.serializeU32AsUleb128(3)}static load(e){return new r}},jt=class r extends we{serialize(e){e.serializeU32AsUleb128(10)}static load(e){return new r}},it=class r extends we{serialize(e){e.serializeU32AsUleb128(4)}static load(e){return new r}},Na=class r extends we{serialize(e){e.serializeU32AsUleb128(5)}static load(e){return new r}},Kt=class r extends we{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(6),this.value.serialize(t)}static load(t){let n=we.deserialize(t);return new r(n)}},_t=class r extends we{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(7),this.value.serialize(t)}static load(t){let n=Wt.deserialize(t);return new r(n)}isStringTypeTag(){return this.value.module_name.value==="string"&&this.value.name.value==="String"&&this.value.address.toHexString()===re.CORE_CODE_ADDRESS.toHexString()}},Wt=class r{constructor(e,t,n,a){this.address=e;this.module_name=t;this.name=n;this.type_args=a}static fromString(e){let t=new pt(e).parseTypeTag();return new r(t.value.address,t.value.module_name,t.value.name,t.value.type_args)}serialize(e){this.address.serialize(e),this.module_name.serialize(e),this.name.serialize(e),ke(this.type_args,e)}static deserialize(e){let t=re.deserialize(e),n=Ce.deserialize(e),a=Ce.deserialize(e),i=Pe(e,we);return new r(t,n,a,i)}},za=new Wt(re.fromHex("0x1"),new Ce("string"),new Ce("String"),[]);function yy(r){return new Wt(re.fromHex("0x1"),new Ce("option"),new Ce("Option"),[r])}function ly(r){return new Wt(re.fromHex("0x1"),new Ce("object"),new Ce("Object"),[r])}function Gt(r){throw new Ua(r)}function a_(r){return!!r.match(/\s/)}function o_(r){return!!r.match(/[_A-Za-z0-9]/g)}function dy(r){return!!r.match(/T\d+/g)}function by(r,e){let t=r[e];if(t===":"){if(r.slice(e,e+2)==="::")return[["COLON","::"],2];Gt("Unrecognized token.")}else{if(t==="<")return[["LT","<"],1];if(t===">")return[["GT",">"],1];if(t===",")return[["COMMA",","],1];if(a_(t)){let n="";for(let a=e;a<r.length;a+=1){let i=r[a];if(a_(i))n=`${n}${i}`;else break}return[["SPACE",n],n.length]}else if(o_(t)){let n="";for(let a=e;a<r.length;a+=1){let i=r[a];if(o_(i))n=`${n}${i}`;else break}return dy(n)?[["GENERIC",n],n.length]:[["IDENT",n],n.length]}}throw new Error("Unrecognized token.")}function my(r){let e=0,t=[];for(;e<r.length;){let[n,a]=by(r,e);n[0]!=="SPACE"&&t.push(n),e+=a}return t}var pt=class r{constructor(e,t){this.typeTags=[];this.tokens=my(e),this.typeTags=t||[]}consume(e){let t=this.tokens.shift();(!t||t[1]!==e)&&Gt("Invalid type tag.")}consumeWholeGeneric(){for(this.consume("<");this.tokens[0][1]!==">";)this.tokens[0][1]==="<"?this.consumeWholeGeneric():this.tokens.shift();this.consume(">")}parseCommaList(e,t){let n=[];for(this.tokens.length<=0&&Gt("Invalid type tag.");this.tokens[0][1]!==e&&(n.push(this.parseTypeTag()),!(this.tokens.length>0&&this.tokens[0][1]===e||(this.consume(","),this.tokens.length>0&&this.tokens[0][1]===e&&t)));)this.tokens.length<=0&&Gt("Invalid type tag.");return n}parseTypeTag(){this.tokens.length===0&&Gt("Invalid type tag.");let[e,t]=this.tokens.shift();if(t==="u8")return new rt;if(t==="u16")return new $t;if(t==="u32")return new Qt;if(t==="u64")return new mt;if(t==="u128")return new gt;if(t==="u256")return new jt;if(t==="bool")return new bt;if(t==="address")return new it;if(t==="vector"){this.consume("<");let n=this.parseTypeTag();return this.consume(">"),new Kt(n)}if(t==="string")return new _t(za);if(e==="IDENT"&&(t.startsWith("0x")||t.startsWith("0X"))){let n=re.fromHex(t);this.consume("::");let[a,i]=this.tokens.shift();a!=="IDENT"&&Gt("Invalid type tag."),this.consume("::");let[s,p]=this.tokens.shift();if(s!=="IDENT"&&Gt("Invalid type tag."),re.CORE_CODE_ADDRESS.toHexString()===n.toHexString()&&i==="object"&&p==="Object")return this.consumeWholeGeneric(),new it;let l=[];this.tokens.length>0&&this.tokens[0][1]==="<"&&(this.consume("<"),l=this.parseCommaList(">",!0),this.consume(">"));let d=new Wt(n,new Ce(i),new Ce(p),l);return new _t(d)}if(e==="GENERIC"){this.typeTags.length===0&&Gt("Can't convert generic type since no typeTags were specified.");let n=parseInt(t.substring(1),10);return new r(this.typeTags[n]).parseTypeTag()}throw new Error("Invalid type tag.")}},Ua=class extends Error{constructor(e){super(e),this.name="TypeTagParserError"}};var wt=class r{constructor(e,t,n,a,i,s,p){this.sender=e;this.sequence_number=t;this.payload=n;this.max_gas_amount=a;this.gas_unit_price=i;this.expiration_timestamp_secs=s;this.chain_id=p}serialize(e){this.sender.serialize(e),e.serializeU64(this.sequence_number),this.payload.serialize(e),e.serializeU64(this.max_gas_amount),e.serializeU64(this.gas_unit_price),e.serializeU64(this.expiration_timestamp_secs),this.chain_id.serialize(e)}static deserialize(e){let t=re.deserialize(e),n=e.deserializeU64(),a=dr.deserialize(e),i=e.deserializeU64(),s=e.deserializeU64(),p=e.deserializeU64(),l=Xr.deserialize(e);return new r(t,n,a,i,s,p,l)}},Qr=class r{constructor(e,t,n){this.code=e;this.ty_args=t;this.args=n}serialize(e){e.serializeBytes(this.code),ke(this.ty_args,e),ke(this.args,e)}static deserialize(e){let t=e.deserializeBytes(),n=Pe(e,we),a=Pe(e,Xe);return new r(t,n,a)}},yr=class r{constructor(e,t,n,a){this.module_name=e;this.function_name=t;this.ty_args=n;this.args=a}static natural(e,t,n,a){return new r(Ot.fromStr(e),new Ce(t),n,a)}static natual(e,t,n,a){return r.natural(e,t,n,a)}serialize(e){this.module_name.serialize(e),this.function_name.serialize(e),ke(this.ty_args,e),e.serializeU32AsUleb128(this.args.length),this.args.forEach(t=>{e.serializeBytes(t)})}static deserialize(e){let t=Ot.deserialize(e),n=Ce.deserialize(e),a=Pe(e,we),i=e.deserializeUleb128AsU32(),s=[];for(let l=0;l<i;l+=1)s.push(e.deserializeBytes());let p=s;return new r(t,n,a,p)}},La=class r{constructor(e){this.transaction_payload=e}serialize(e){e.serializeU32AsUleb128(0),this.transaction_payload.serialize(e)}static deserialize(e){return e.deserializeUleb128AsU32(),new r(yr.deserialize(e))}},Ha=class r{constructor(e,t){this.multisig_address=e;this.transaction_payload=t}serialize(e){this.multisig_address.serialize(e),this.transaction_payload===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.transaction_payload.serialize(e))}static deserialize(e){let t=re.deserialize(e),n=e.deserializeBool(),a;return n&&(a=La.deserialize(e)),new r(t,a)}},ns=class r{constructor(e){this.code=e}serialize(e){e.serializeBytes(this.code)}static deserialize(e){let t=e.deserializeBytes();return new r(t)}},Ot=class r{constructor(e,t){this.address=e;this.name=t}static fromStr(e){let t=e.split("::");if(t.length!==2)throw new Error("Invalid module id.");return new r(re.fromHex(new F(t[0])),new Ce(t[1]))}serialize(e){this.address.serialize(e),this.name.serialize(e)}static deserialize(e){let t=re.deserialize(e),n=Ce.deserialize(e);return new r(t,n)}},as=class{serialize(e){throw new Error("Not implemented.")}static deserialize(e){throw new Error("Not implemented.")}},os=class{serialize(e){throw new Error("Not implmented.")}static deserialize(e){throw new Error("Not implmented.")}},lr=class r{constructor(e,t){this.raw_txn=e;this.authenticator=t}serialize(e){this.raw_txn.serialize(e),this.authenticator.serialize(e)}static deserialize(e){let t=wt.deserialize(e),n=Ct.deserialize(e);return new r(t,n)}},Un=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return jr.load(e);case 1:return Kr.load(e);default:throw new Error(`Unknown variant index for RawTransactionWithData: ${t}`)}}},jr=class r extends Un{constructor(t,n){super();this.raw_txn=t;this.secondary_signer_addresses=n}serialize(t){t.serializeU32AsUleb128(0),this.raw_txn.serialize(t),ke(this.secondary_signer_addresses,t)}static load(t){let n=wt.deserialize(t),a=Pe(t,re);return new r(n,a)}},Kr=class r extends Un{constructor(t,n,a){super();this.raw_txn=t;this.secondary_signer_addresses=n;this.fee_payer_address=a}serialize(t){t.serializeU32AsUleb128(1),this.raw_txn.serialize(t),ke(this.secondary_signer_addresses,t),this.fee_payer_address.serialize(t)}static load(t){let n=wt.deserialize(t),a=Pe(t,re),i=re.deserialize(t);return new r(n,a,i)}},dr=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return Wr.load(e);case 2:return Jr.load(e);case 3:return qa.load(e);default:throw new Error(`Unknown variant index for TransactionPayload: ${t}`)}}},Wr=class r extends dr{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(0),this.value.serialize(t)}static load(t){let n=Qr.deserialize(t);return new r(n)}},Jr=class r extends dr{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(2),this.value.serialize(t)}static load(t){let n=yr.deserialize(t);return new r(n)}},qa=class r extends dr{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(3),this.value.serialize(t)}static load(t){let n=Ha.deserialize(t);return new r(n)}},Xr=class r{constructor(e){this.value=e}serialize(e){e.serializeU8(this.value)}static deserialize(e){let t=e.deserializeU8();return new r(t)}},Xe=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return Yr.load(e);case 1:return tn.load(e);case 2:return rn.load(e);case 3:return an.load(e);case 4:return on.load(e);case 5:return sn.load(e);case 6:return Zr.load(e);case 7:return en.load(e);case 8:return nn.load(e);default:throw new Error(`Unknown variant index for TransactionArgument: ${t}`)}}},Yr=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(0),t.serializeU8(this.value)}static load(t){let n=t.deserializeU8();return new r(n)}},Zr=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(6),t.serializeU16(this.value)}static load(t){let n=t.deserializeU16();return new r(n)}},en=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(7),t.serializeU32(this.value)}static load(t){let n=t.deserializeU32();return new r(n)}},tn=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(1),t.serializeU64(this.value)}static load(t){let n=t.deserializeU64();return new r(n)}},rn=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(2),t.serializeU128(this.value)}static load(t){let n=t.deserializeU128();return new r(n)}},nn=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(8),t.serializeU256(this.value)}static load(t){let n=t.deserializeU256();return new r(n)}},an=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(3),this.value.serialize(t)}static load(t){let n=re.deserialize(t);return new r(n)}},on=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(4),t.serializeBytes(this.value)}static load(t){let n=t.deserializeBytes();return new r(n)}},sn=class r extends Xe{constructor(t){super();this.value=t}serialize(t){t.serializeU32AsUleb128(5),t.serializeBool(this.value)}static load(t){let n=t.deserializeBool();return new r(n)}},Ga=class{getHashSalt(){let e=ct.create();return e.update("APTOS::Transaction"),e.digest()}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return $a.load(e);default:throw new Error(`Unknown variant index for Transaction: ${t}`)}}},$a=class r extends Ga{constructor(t){super();this.value=t}hash(){let t=ct.create();return t.update(this.getHashSalt()),t.update(Le(this)),t.digest()}serialize(t){t.serializeU32AsUleb128(0),this.value.serialize(t)}static load(t){return new r(lr.deserialize(t))}};var br=class r{constructor(e){this.name=e}serialize(e){e.serializeStr(this.name)}static deserialize(e){let t=e.deserializeStr();return new r(t)}},mr=class r{constructor(e,t){this.name=e;this.type_tag=t}serialize(e){e.serializeStr(this.name),this.type_tag.serialize(e)}static deserialize(e){let t=e.deserializeStr(),n=we.deserialize(e);return new r(t,n)}},gr=class{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return _n.load(e);case 1:return Jt.load(e);default:throw new Error(`Unknown variant index for TransactionPayload: ${t}`)}}},_n=class r extends gr{constructor(t,n,a,i,s){super();this.name=t;this.doc=n;this.code=a;this.ty_args=i;this.args=s}serialize(t){t.serializeU32AsUleb128(0),t.serializeStr(this.name),t.serializeStr(this.doc),t.serializeBytes(this.code),ke(this.ty_args,t),ke(this.args,t)}static load(t){let n=t.deserializeStr(),a=t.deserializeStr(),i=t.deserializeBytes(),s=Pe(t,br),p=Pe(t,mr);return new r(n,a,i,s,p)}},Jt=class r extends gr{constructor(t,n,a,i,s){super();this.name=t;this.module_name=n;this.doc=a;this.ty_args=i;this.args=s}serialize(t){t.serializeU32AsUleb128(1),t.serializeStr(this.name),this.module_name.serialize(t),t.serializeStr(this.doc),ke(this.ty_args,t),ke(this.args,t)}static load(t){let n=t.deserializeStr(),a=Ot.deserialize(t),i=t.deserializeStr(),s=Pe(t,br),p=Pe(t,mr);return new r(n,a,i,s,p)}};var ut=class ut{constructor(e){if(e.length!==ut.LENGTH)throw new Error("Expected a byte array of length 32");this.bytes=e}static fromMultiEd25519PublicKey(e){let t=e.toBytes(),n=new Uint8Array(t.length+1);n.set(t),n.set([ut.MULTI_ED25519_SCHEME],t.length);let a=ct.create();return a.update(n),new ut(a.digest())}static fromEd25519PublicKey(e){let t=e.value,n=new Uint8Array(t.length+1);n.set(t),n.set([ut.ED25519_SCHEME],t.length);let a=ct.create();return a.update(n),new ut(a.digest())}derivedAddress(){return F.fromUint8Array(this.bytes)}};ut.LENGTH=32,ut.MULTI_ED25519_SCHEME=1,ut.ED25519_SCHEME=0,ut.DERIVE_RESOURCE_ACCOUNT_SCHEME=255;var pn=ut;var ss=class{constructor(e,t,n,a,i,s,p){this.accountAddress=e;this.moduleName=t;this.structName=n;this.sequenceNumber=a;this.originator=i;this.currentAuthKey=s;this.newPublicKey=p}serialize(e){this.accountAddress.serialize(e),e.serializeStr(this.moduleName),e.serializeStr(this.structName),e.serializeU64(this.sequenceNumber),this.originator.serialize(e),this.currentAuthKey.serialize(e),e.serializeBytes(this.newPublicKey)}};var un=class un{static fromAptosAccountObject(e){return new un(F.ensure(e.privateKeyHex).toUint8Array(),e.address)}static isValidPath(e){return/^m\/44'\/637'\/[0-9]+'\/[0-9]+'\/[0-9]+'+$/.test(e)}static fromDerivePath(e,t){if(!un.isValidPath(e))throw new Error("Invalid derivation path");let n=t.trim().split(/\s+/).map(i=>i.toLowerCase()).join(" "),{key:a}=Oo(e,aa(ri(n)));return new un(a)}constructor(e,t){e?this.signingKey=zn.default.sign.keyPair.fromSeed(e.slice(0,32)):this.signingKey=zn.default.sign.keyPair(),this.accountAddress=F.ensure(t||this.authKey().hex())}address(){return this.accountAddress}authKey(){let e=new De(this.signingKey.publicKey);return pn.fromEd25519PublicKey(e).derivedAddress()}static getResourceAccountAddress(e,t){let n=Le(re.fromHex(e)),a=new Uint8Array([...n,...t,pn.DERIVE_RESOURCE_ACCOUNT_SCHEME]),i=ct.create();return i.update(a),F.fromUint8Array(i.digest())}static getCollectionID(e,t){let n=new TextEncoder().encode(`${e}::${t}`),a=$s.create();return a.update(n),F.fromUint8Array(a.digest())}pubKey(){return F.fromUint8Array(this.signingKey.publicKey)}signBuffer(e){let t=zn.default.sign.detached(e,this.signingKey.secretKey);return F.fromUint8Array(t)}signHexString(e){let t=F.ensure(e).toUint8Array();return this.signBuffer(t)}verifySignature(e,t){let n=F.ensure(e).toUint8Array(),a=F.ensure(t).toUint8Array();return zn.default.sign.detached.verify(n,a,this.signingKey.publicKey)}toPrivateKeyObject(){return{address:this.address().hex(),publicKeyHex:this.pubKey().hex(),privateKeyHex:F.fromUint8Array(this.signingKey.secretKey.slice(0,32)).hex()}}};de([Pr()],un.prototype,"authKey",1);var fr=un;function cn(r){return r instanceof fr?r.address():F.ensure(r)}var Ln=`
    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {
  token_standard
  token_properties_mutated_v1
  token_data_id
  table_type_v1
  storage_id
  property_version_v1
  owner_address
  last_transaction_version
  last_transaction_timestamp
  is_soulbound_v2
  is_fungible_v2
  amount
  current_token_data {
    collection_id
    description
    is_fungible_v2
    largest_property_version_v1
    last_transaction_timestamp
    last_transaction_version
    maximum
    supply
    token_data_id
    token_name
    token_properties
    token_standard
    token_uri
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      max_supply
      mutable_description
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
  }
}
    `,gy=`
    fragment TokenDataFields on current_token_datas {
  creator_address
  collection_name
  description
  metadata_uri
  name
  token_data_id_hash
  collection_data_id_hash
}
    `,fy=`
    fragment CollectionDataFields on current_collection_datas {
  metadata_uri
  supply
  description
  collection_name
  collection_data_id_hash
  table_handle
  creator_address
}
    `,s_=`
    fragment TokenActivitiesFields on token_activities_v2 {
  after_value
  before_value
  entry_function_id_str
  event_account_address
  event_index
  from_address
  is_fungible_v2
  property_version_v1
  to_address
  token_amount
  token_data_id
  token_standard
  transaction_timestamp
  transaction_version
  type
}
    `,i_=`
    query getAccountCoinsDataCount($address: String) {
  current_fungible_asset_balances_aggregate(
    where: {owner_address: {_eq: $address}}
  ) {
    aggregate {
      count
    }
  }
}
    `,__=`
    query getAccountCoinsData($where_condition: current_fungible_asset_balances_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_fungible_asset_balances_order_by!]) {
  current_fungible_asset_balances(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    amount
    asset_type
    is_frozen
    is_primary
    last_transaction_timestamp
    last_transaction_version
    owner_address
    storage_id
    token_standard
    metadata {
      token_standard
      symbol
      supply_aggregator_table_key_v1
      supply_aggregator_table_handle_v1
      project_uri
      name
      last_transaction_version
      last_transaction_timestamp
      icon_uri
      decimals
      creator_address
      asset_type
    }
  }
}
    `,p_=`
    query getAccountCurrentTokens($address: String!, $offset: Int, $limit: Int) {
  current_token_ownerships(
    where: {owner_address: {_eq: $address}, amount: {_gt: 0}}
    order_by: [{last_transaction_version: desc}, {creator_address: asc}, {collection_name: asc}, {name: asc}]
    offset: $offset
    limit: $limit
  ) {
    amount
    current_token_data {
      ...TokenDataFields
    }
    current_collection_data {
      ...CollectionDataFields
    }
    last_transaction_version
    property_version
  }
}
    ${gy}
${fy}`,u_=`
    query getAccountTokensCount($where_condition: current_token_ownerships_v2_bool_exp, $offset: Int, $limit: Int) {
  current_token_ownerships_v2_aggregate(
    where: $where_condition
    offset: $offset
    limit: $limit
  ) {
    aggregate {
      count
    }
  }
}
    `,c_=`
    query getAccountTransactionsCount($address: String) {
  account_transactions_aggregate(where: {account_address: {_eq: $address}}) {
    aggregate {
      count
    }
  }
}
    `,y_=`
    query getAccountTransactionsData($where_condition: account_transactions_bool_exp!, $offset: Int, $limit: Int, $order_by: [account_transactions_order_by!]) {
  account_transactions(
    where: $where_condition
    order_by: $order_by
    limit: $limit
    offset: $offset
  ) {
    token_activities_v2 {
      ...TokenActivitiesFields
    }
    transaction_version
    account_address
  }
}
    ${s_}`,l_=`
    query getCollectionData($where_condition: current_collections_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_collections_v2_order_by!]) {
  current_collections_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    collection_id
    collection_name
    creator_address
    current_supply
    description
    last_transaction_timestamp
    last_transaction_version
    max_supply
    mutable_description
    mutable_uri
    table_handle_v1
    token_standard
    total_minted_v2
    uri
  }
}
    `,d_=`
    query getCollectionsWithOwnedTokens($where_condition: current_collection_ownership_v2_view_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_collection_ownership_v2_view_order_by!]) {
  current_collection_ownership_v2_view(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      mutable_description
      max_supply
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
    collection_id
    collection_name
    collection_uri
    creator_address
    distinct_tokens
    last_transaction_version
    owner_address
    single_token_uri
  }
}
    `,b_=`
    query getCurrentObjects($where_condition: current_objects_bool_exp, $offset: Int, $limit: Int, $order_by: [current_objects_order_by!]) {
  current_objects(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    allow_ungated_transfer
    state_key_hash
    owner_address
    object_address
    last_transaction_version
    last_guid_creation_num
    is_deleted
  }
}
    `,m_=`
    query getDelegatedStakingActivities($delegatorAddress: String, $poolAddress: String) {
  delegated_staking_activities(
    where: {delegator_address: {_eq: $delegatorAddress}, pool_address: {_eq: $poolAddress}}
  ) {
    amount
    delegator_address
    event_index
    event_type
    pool_address
    transaction_version
  }
}
    `,g_=`
    query getIndexerLedgerInfo {
  ledger_infos {
    chain_id
  }
}
    `,f_=`
    query getNumberOfDelegators($poolAddress: String) {
  num_active_delegator_per_pool(
    where: {pool_address: {_eq: $poolAddress}, num_active_delegator: {_gt: "0"}}
    distinct_on: pool_address
  ) {
    num_active_delegator
    pool_address
  }
}
    `,M_=`
    query getOwnedTokens($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${Ln}`,S_=`
    query getOwnedTokensByTokenData($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${Ln}`,I_=`
    query getTokenActivities($where_condition: token_activities_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [token_activities_v2_order_by!]) {
  token_activities_v2(
    where: $where_condition
    order_by: $order_by
    offset: $offset
    limit: $limit
  ) {
    ...TokenActivitiesFields
  }
}
    ${s_}`,x_=`
    query getTokenActivitiesCount($token_id: String) {
  token_activities_v2_aggregate(where: {token_data_id: {_eq: $token_id}}) {
    aggregate {
      count
    }
  }
}
    `,h_=`
    query getTokenCurrentOwnerData($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${Ln}`,v_=`
    query getTokenData($where_condition: current_token_datas_v2_bool_exp, $offset: Int, $limit: Int, $order_by: [current_token_datas_v2_order_by!]) {
  current_token_datas_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    collection_id
    description
    is_fungible_v2
    largest_property_version_v1
    last_transaction_timestamp
    last_transaction_version
    maximum
    supply
    token_data_id
    token_name
    token_properties
    token_standard
    token_uri
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      max_supply
      mutable_description
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
  }
}
    `,A_=`
    query getTokenOwnedFromCollection($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${Ln}`,B_=`
    query getTokenOwnersData($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${Ln}`,k_=`
    query getTopUserTransactions($limit: Int) {
  user_transactions(limit: $limit, order_by: {version: desc}) {
    version
  }
}
    `,T_=`
    query getUserTransactions($where_condition: user_transactions_bool_exp!, $offset: Int, $limit: Int, $order_by: [user_transactions_order_by!]) {
  user_transactions(
    order_by: $order_by
    where: $where_condition
    limit: $limit
    offset: $offset
  ) {
    version
  }
}
    `;function Qa(r,e,t){if(!(e!=null&&e.includes(typeof r)))throw new Error(t||`Invalid arg: ${r} type should be ${e instanceof Array?e.join(" or "):e}`)}function is(r){if(Qa(r,["boolean","string"]),typeof r=="boolean")return r;if(r==="true")return!0;if(r==="false")return!1;throw new Error("Invalid boolean string.")}function Mr(r){if(Qa(r,["number","string"]),typeof r=="number")return r;let e=Number.parseInt(r,10);if(Number.isNaN(e))throw new Error("Invalid number string.");return e}function Sr(r){return Qa(r,["number","bigint","string"]),BigInt(r)}function yn(r,e,t){_s(r,e,t,0)}function _s(r,e,t,n){if(e instanceof bt)t.serializeBool(is(r));else if(e instanceof rt)t.serializeU8(Mr(r));else if(e instanceof $t)t.serializeU16(Mr(r));else if(e instanceof Qt)t.serializeU32(Mr(r));else if(e instanceof mt)t.serializeU64(Sr(r));else if(e instanceof gt)t.serializeU128(Sr(r));else if(e instanceof jt)t.serializeU256(Sr(r));else if(e instanceof it)C_(r,t);else if(e instanceof Kt)My(r,e,t,n);else if(e instanceof _t)Sy(r,e,t,n);else throw new Error("Unsupported arg type.")}function C_(r,e){let t;if(typeof r=="string"||r instanceof F)t=re.fromHex(r);else if(r instanceof re)t=r;else throw new Error("Invalid account address.");t.serialize(e)}function My(r,e,t,n){if(e.value instanceof rt){if(r instanceof Uint8Array){t.serializeBytes(r);return}if(r instanceof F){t.serializeBytes(r.toUint8Array());return}if(typeof r=="string"){t.serializeStr(r);return}}if(!Array.isArray(r))throw new Error("Invalid vector args.");t.serializeU32AsUleb128(r.length),r.forEach(a=>_s(a,e.value,t,n+1))}function Sy(r,e,t,n){let{address:a,module_name:i,name:s,type_args:p}=e.value,l=`${F.fromUint8Array(a.address).toShortString()}::${i.value}::${s.value}`;if(l==="0x1::string::String")Qa(r,["string"]),t.serializeStr(r);else if(l==="0x1::object::Object")C_(r,t);else if(l==="0x1::option::Option"){if(p.length!==1)throw new Error(`Option has the wrong number of type arguments ${p.length}`);Iy(r,p[0],t,n)}else throw new Error("Unsupported struct type in function argument")}function Iy(r,e,t,n){r==null?t.serializeU32AsUleb128(0):(t.serializeU32AsUleb128(1),_s(r,e,t,n+1))}function ps(r,e){if(e instanceof bt)return new sn(is(r));if(e instanceof rt)return new Yr(Mr(r));if(e instanceof $t)return new Zr(Mr(r));if(e instanceof Qt)return new en(Mr(r));if(e instanceof mt)return new tn(Sr(r));if(e instanceof gt)return new rn(Sr(r));if(e instanceof jt)return new nn(Sr(r));if(e instanceof it){let t;if(typeof r=="string"||r instanceof F)t=re.fromHex(r);else if(r instanceof re)t=r;else throw new Error("Invalid account address.");return new an(t)}if(e instanceof Kt&&e.value instanceof rt){if(!(r instanceof Uint8Array))throw new Error(`${r} should be an instance of Uint8Array`);return new on(r)}throw new Error("Unknown type for TransactionArgument.")}var xy="APTOS::RawTransaction",w_="APTOS::RawTransactionWithData",Qe=class{constructor(e,t){this.rawTxnBuilder=t;this.signingFunction=e}build(e,t,n){if(!this.rawTxnBuilder)throw new Error("this.rawTxnBuilder doesn't exist.");return this.rawTxnBuilder.build(e,t,n)}static getSigningMessage(e){let t=ct.create();if(e instanceof wt)t.update(xy);else if(e instanceof jr)t.update(w_);else if(e instanceof Kr)t.update(w_);else throw new Error("Unknown transaction type.");let n=t.digest(),a=Le(e),i=new Uint8Array(n.length+a.length);return i.set(n),i.set(a,n.length),i}},Ir=class extends Qe{constructor(t,n,a){super(t,a);this.publicKey=n}rawToSigned(t){let n=Qe.getSigningMessage(t),a=this.signingFunction(n),i=new Gr(new De(this.publicKey),a);return new lr(t,i)}sign(t){return Le(this.rawToSigned(t))}},Hn=class extends Qe{constructor(t,n){super(t);this.publicKey=n}rawToSigned(t){let n=Qe.getSigningMessage(t),a=this.signingFunction(n),i=new $r(this.publicKey,a);return new lr(t,i)}sign(t){return Le(this.rawToSigned(t))}},ja=class r{constructor(e,t){this.abiMap=new Map,e.forEach(n=>{let a=new ur(n),i=gr.deserialize(a),s;if(i instanceof Jt){let p=i,{address:l,name:d}=p.module_name;s=`${F.fromUint8Array(l.address).toShortString()}::${d.value}::${p.name}`}else s=i.name;if(this.abiMap.has(s))throw new Error("Found conflicting ABI interfaces");this.abiMap.set(s,i)}),this.builderConfig={maxGasAmount:BigInt(ya),expSecFromNow:la,...t}}static toBCSArgs(e,t){if(e.length!==t.length)throw new Error("Wrong number of args provided.");return t.map((n,a)=>{let i=new Se;return yn(n,e[a].type_tag,i),i.getBytes()})}static toTransactionArguments(e,t){if(e.length!==t.length)throw new Error("Wrong number of args provided.");return t.map((n,a)=>ps(n,e[a].type_tag))}setSequenceNumber(e){this.builderConfig.sequenceNumber=BigInt(e)}buildTransactionPayload(e,t,n){let a=t.map(p=>new pt(p).parseTypeTag()),i;if(!this.abiMap.has(e))throw new Error(`Cannot find function: ${e}`);let s=this.abiMap.get(e);if(s instanceof Jt){let p=s,l=r.toBCSArgs(p.args,n);i=new Jr(new yr(p.module_name,new Ce(p.name),a,l))}else if(s instanceof _n){let p=s,l=r.toTransactionArguments(p.args,n);i=new Wr(new Qr(p.code,a,l))}else throw new Error("Unknown ABI format.");return i}build(e,t,n){let{sender:a,sequenceNumber:i,gasUnitPrice:s,maxGasAmount:p,expSecFromNow:l,chainId:d}=this.builderConfig;if(!s)throw new Error("No gasUnitPrice provided.");let g=a instanceof re?a:re.fromHex(a),b=BigInt(Math.floor(Date.now()/1e3)+Number(l)),I=this.buildTransactionPayload(e,t,n);if(I)return new wt(g,BigInt(i),I,BigInt(p),BigInt(s),b,new Xr(Number(d)));throw new Error("Invalid ABI.")}},fe=class{constructor(e,t){this.aptosClient=e;this.builderConfig=t}async fetchABI(e){let n=(await this.aptosClient.getAccountModules(e)).map(i=>i.abi).flatMap(i=>i.exposed_functions.filter(s=>s.is_entry).map(s=>({fullName:`${i.address}::${i.name}::${s.name}`,...s}))),a=new Map;return n.forEach(i=>{a.set(i.fullName,i)}),a}async build(e,t,n){if(e=(he=>he.replace(/^0[xX]0*/g,"0x"))(e),e.split("::").length!==3)throw new Error("'func' needs to be a fully qualified function name in format <address>::<module>::<function>, e.g. 0x1::coin::transfer");let[s,p]=e.split("::"),l=await this.fetchABI(s);if(!l.has(e))throw new Error(`${e} doesn't exist.`);let d=l.get(e),b=d.params.filter(he=>he!=="signer"&&he!=="&signer").map((he,w)=>new mr(`var${w}`,new pt(he,t).parseTypeTag())),I=new Jt(d.name,Ot.fromStr(`${s}::${p}`),"",d.generic_type_params.map((he,w)=>new br(`${w}`)),b),{sender:B,...x}=this.builderConfig,S=B instanceof re?F.fromUint8Array(B.address):B,[{sequence_number:V},Z,{gas_estimate:K}]=await Promise.all([x!=null&&x.sequenceNumber?Promise.resolve({sequence_number:x==null?void 0:x.sequenceNumber}):this.aptosClient.getAccount(S),x!=null&&x.chainId?Promise.resolve(x==null?void 0:x.chainId):this.aptosClient.getChainId(),x!=null&&x.gasUnitPrice?Promise.resolve({gas_estimate:x==null?void 0:x.gasUnitPrice}):this.aptosClient.estimateGasPrice()]);return new ja([Le(I)],{sender:B,sequenceNumber:V,chainId:Z,gasUnitPrice:BigInt(K),...x}).build(e,t,n)}};de([ma(10*60*1e3)],fe.prototype,"fetchABI",1);var xe=class xe{constructor(e,t,n=!1){if(!e)throw new Error("Node URL cannot be empty.");n?this.nodeUrl=e:this.nodeUrl=bi(e),this.config=t==null?{}:{...t}}async getAccount(e){let{data:t}=await Re({url:this.nodeUrl,endpoint:`accounts/${F.ensure(e).hex()}`,originMethod:"getAccount",overrides:{...this.config}});return t}async getAccountTransactions(e,t){let{data:n}=await Re({url:this.nodeUrl,endpoint:`accounts/${F.ensure(e).hex()}/transactions`,originMethod:"getAccountTransactions",params:{start:t==null?void 0:t.start,limit:t==null?void 0:t.limit},overrides:{...this.config}});return n}async getAccountModules(e,t){return await Zo({url:this.nodeUrl,endpoint:`accounts/${e}/modules`,params:{ledger_version:t==null?void 0:t.ledgerVersion,limit:1e3},originMethod:"getAccountModules",overrides:{...this.config}})}async getAccountModule(e,t,n){let{data:a}=await Re({url:this.nodeUrl,endpoint:`accounts/${F.ensure(e).hex()}/module/${t}`,originMethod:"getAccountModule",params:{ledger_version:n==null?void 0:n.ledgerVersion},overrides:{...this.config}});return a}async getAccountResources(e,t){return await Zo({url:this.nodeUrl,endpoint:`accounts/${e}/resources`,params:{ledger_version:t==null?void 0:t.ledgerVersion,limit:9999},originMethod:"getAccountResources",overrides:{...this.config}})}async getAccountResource(e,t,n){let{data:a}=await Re({url:this.nodeUrl,endpoint:`accounts/${F.ensure(e).hex()}/resource/${t}`,originMethod:"getAccountResource",params:{ledger_version:n==null?void 0:n.ledgerVersion},overrides:{...this.config}});return a}static generateBCSTransaction(e,t){return new Ir(a=>{let i=e.signBuffer(a);return new $.Ed25519Signature(i.toUint8Array())},e.pubKey().toUint8Array()).sign(t)}static generateBCSSimulation(e,t){return new Ir(a=>{let i=new Uint8Array(64);return new $.Ed25519Signature(i)},e.pubKey().toUint8Array()).sign(t)}async generateTransaction(e,t,n){let a={sender:e};if(n!=null&&n.sequence_number&&(a.sequenceNumber=n.sequence_number),n!=null&&n.gas_unit_price&&(a.gasUnitPrice=n.gas_unit_price),n!=null&&n.max_gas_amount&&(a.maxGasAmount=n.max_gas_amount),n!=null&&n.expiration_timestamp_secs){let s=Number.parseInt(n.expiration_timestamp_secs,10);a.expSecFromNow=s-Math.floor(Date.now()/1e3)}return new fe(this,a).build(t.function,t.type_arguments,t.arguments)}async generateFeePayerTransaction(e,t,n,a=[],i){let s=await this.generateTransaction(e,t,i),p=a.map(d=>re.fromHex(d));return new $.FeePayerRawTransaction(s,p,re.fromHex(n))}async submitFeePayerTransaction(e,t,n,a=[]){let i=new $.TransactionAuthenticatorFeePayer(t,e.secondary_signer_addresses,a,{address:e.fee_payer_address,authenticator:n}),s=Le(new $.SignedTransaction(e.raw_txn,i));return await this.submitSignedBCSTransaction(s)}async signMultiTransaction(e,t){let n=new $.Ed25519Signature(e.signBuffer(Qe.getSigningMessage(t)).toUint8Array()),a=new $.AccountAuthenticatorEd25519(new $.Ed25519PublicKey(e.signingKey.publicKey),n);return Promise.resolve(a)}async signTransaction(e,t){return Promise.resolve(xe.generateBCSTransaction(e,t))}async getEventsByCreationNumber(e,t,n){let{data:a}=await Re({url:this.nodeUrl,endpoint:`accounts/${F.ensure(e).hex()}/events/${t}`,originMethod:"getEventsByCreationNumber",params:{start:n==null?void 0:n.start,limit:n==null?void 0:n.limit},overrides:{...this.config}});return a}async getEventsByEventHandle(e,t,n,a){let{data:i}=await Re({url:this.nodeUrl,endpoint:`accounts/${F.ensure(e).hex()}/events/${t}/${n}`,originMethod:"getEventsByEventHandle",params:{start:a==null?void 0:a.start,limit:a==null?void 0:a.limit},overrides:{...this.config}});return i}async submitTransaction(e){return this.submitSignedBCSTransaction(e)}async simulateTransaction(e,t,n){let a;return e instanceof fr?a=xe.generateBCSSimulation(e,t):e instanceof qt?a=new Hn(()=>{let{threshold:s}=e,p=[],l=[];for(let g=0;g<s;g+=1)p.push(g),l.push(new $.Ed25519Signature(new Uint8Array(64)));let d=$.MultiEd25519Signature.createBitmap(p);return new $.MultiEd25519Signature(l,d)},e).sign(t):a=new Ir(()=>{let s=new Uint8Array(64);return new $.Ed25519Signature(s)},e.toBytes()).sign(t),this.submitBCSSimulation(a,n)}async submitSignedBCSTransaction(e){let{data:t}=await lt({url:this.nodeUrl,body:e,endpoint:"transactions",originMethod:"submitSignedBCSTransaction",contentType:"application/x.aptos.signed_transaction+bcs",overrides:{...this.config}});return t}async submitBCSSimulation(e,t){var i,s,p;let n={estimate_gas_unit_price:(i=t==null?void 0:t.estimateGasUnitPrice)!=null?i:!1,estimate_max_gas_amount:(s=t==null?void 0:t.estimateMaxGasAmount)!=null?s:!1,estimate_prioritized_gas_unit_price:(p=t==null?void 0:t.estimatePrioritizedGasUnitPrice)!=null?p:!1},{data:a}=await lt({url:this.nodeUrl,body:e,endpoint:"transactions/simulate",params:n,originMethod:"submitBCSSimulation",contentType:"application/x.aptos.signed_transaction+bcs",overrides:{...this.config}});return a}async getTransactions(e){var n;let{data:t}=await Re({url:this.nodeUrl,endpoint:"transactions",originMethod:"getTransactions",params:{start:(n=e==null?void 0:e.start)==null?void 0:n.toString(),limit:e==null?void 0:e.limit},overrides:{...this.config}});return t}async getTransactionByHash(e){let{data:t}=await Re({url:this.nodeUrl,endpoint:`transactions/by_hash/${e}`,originMethod:"getTransactionByHash",overrides:{...this.config}});return t}async getTransactionByVersion(e){let{data:t}=await Re({url:this.nodeUrl,endpoint:`transactions/by_version/${e}`,originMethod:"getTransactionByVersion",overrides:{...this.config}});return t}async transactionPending(e){try{return(await this.getTransactionByHash(e)).type==="pending_transaction"}catch(t){if((t==null?void 0:t.status)===404)return!0;throw t}}async waitForTransactionWithResult(e,t){var l,d;let n=(l=t==null?void 0:t.timeoutSecs)!=null?l:da,a=(d=t==null?void 0:t.checkSuccess)!=null?d:!1,i=!0,s=0,p;for(;i&&!(s>=n);){try{if(p=await this.getTransactionByHash(e),i=p.type==="pending_transaction",!i)break}catch(g){let b=g instanceof Ve,I=b&&g.status!==404&&g.status>=400&&g.status<500;if(!b||I)throw g}await or(1e3),s+=1}if(p===void 0)throw new Error(`Waiting for transaction ${e} failed`);if(i)throw new Ka(`Waiting for transaction ${e} timed out after ${n} seconds`,p);if(!a)return p;if(!(p!=null&&p.success))throw new Wa(`Transaction ${e} failed with an error: ${p.vm_status}`,p);return p}async waitForTransaction(e,t){await this.waitForTransactionWithResult(e,t)}async getLedgerInfo(){let{data:e}=await Re({url:this.nodeUrl,originMethod:"getLedgerInfo",overrides:{...this.config}});return e}async getChainId(){return(await this.getLedgerInfo()).chain_id}async getTableItem(e,t,n){var i;return(await lt({url:this.nodeUrl,body:t,endpoint:`tables/${e}/item`,originMethod:"getTableItem",params:{ledger_version:(i=n==null?void 0:n.ledgerVersion)==null?void 0:i.toString()},overrides:{...this.config}})).data}async generateRawTransaction(e,t,n){let[{sequence_number:a},i,{gas_estimate:s}]=await Promise.all([n!=null&&n.providedSequenceNumber?Promise.resolve({sequence_number:n.providedSequenceNumber}):this.getAccount(e),this.getChainId(),n!=null&&n.gasUnitPrice?Promise.resolve({gas_estimate:n.gasUnitPrice}):this.estimateGasPrice()]),{maxGasAmount:p,gasUnitPrice:l,expireTimestamp:d}={maxGasAmount:BigInt(ya),gasUnitPrice:BigInt(s),expireTimestamp:BigInt(Math.floor(Date.now()/1e3)+la),...n};return new $.RawTransaction($.AccountAddress.fromHex(e),BigInt(a),t,p,l,d,new $.ChainId(i))}async generateSignSubmitTransaction(e,t,n){let a=await this.generateRawTransaction(e.address(),t,n),i=xe.generateBCSTransaction(e,a);return(await this.submitSignedBCSTransaction(i)).hash}async signAndSubmitTransaction(e,t){let n=xe.generateBCSTransaction(e,t);return(await this.submitSignedBCSTransaction(n)).hash}async publishPackage(e,t,n,a){let i=new Se;ke(n,i);let s=new $.TransactionPayloadEntryFunction($.EntryFunction.natural("0x1::code","publish_package_txn",[],[Tt(t),i.getBytes()]));return this.generateSignSubmitTransaction(e,s,a)}async createResourceAccountAndPublishPackage(e,t,n,a,i){let s=new Se;ke(a,s);let p=new $.TransactionPayloadEntryFunction($.EntryFunction.natural("0x1::resource_account","create_resource_account_and_publish_package",[],[Tt(t),Tt(n),s.getBytes()]));return this.generateSignSubmitTransaction(e,p,i)}async generateSignSubmitWaitForTransaction(e,t,n){let a=await this.generateSignSubmitTransaction(e,t,n);return this.waitForTransactionWithResult(a,n)}async estimateGasPrice(){let{data:e}=await Re({url:this.nodeUrl,endpoint:"estimate_gas_price",originMethod:"estimateGasPrice",overrides:{...this.config}});return e}async estimateMaxGasAmount(e){let t=`0x1::coin::CoinStore<${sr}>`,[{gas_estimate:n},a]=await Promise.all([this.estimateGasPrice(),this.getAccountResources(e)]),i=a.find(p=>p.type===t);return BigInt(i.data.coin.value)/BigInt(n)}async rotateAuthKeyEd25519(e,t,n){let{sequence_number:a,authentication_key:i}=await this.getAccount(e.address()),s=new fr(t),p=new $.RotationProofChallenge($.AccountAddress.CORE_CODE_ADDRESS,"account","RotationProofChallenge",BigInt(a),$.AccountAddress.fromHex(e.address()),new $.AccountAddress(new F(i).toUint8Array()),s.pubKey().toUint8Array()),l=F.fromUint8Array(Le(p)),d=e.signHexString(l),g=s.signHexString(l),b=new $.TransactionPayloadEntryFunction($.EntryFunction.natural("0x1::account","rotate_authentication_key",[],[Ea(0),Tt(e.pubKey().toUint8Array()),Ea(0),Tt(s.pubKey().toUint8Array()),Tt(d.toUint8Array()),Tt(g.toUint8Array())])),I=await this.generateRawTransaction(e.address(),b,n),B=xe.generateBCSTransaction(e,I);return this.submitSignedBCSTransaction(B)}async lookupOriginalAddress(e){let t=await this.getAccountResource("0x1","0x1::account::OriginatingAddress"),{address_map:{handle:n}}=t.data,a=await this.getTableItem(n,{key_type:"address",value_type:"address",key:F.ensure(e).hex()});return new F(a)}async getBlockByHeight(e,t){let{data:n}=await Re({url:this.nodeUrl,endpoint:`blocks/by_height/${e}`,originMethod:"getBlockByHeight",params:{with_transactions:t},overrides:{...this.config}});return n}async getBlockByVersion(e,t){let{data:n}=await Re({url:this.nodeUrl,endpoint:`blocks/by_version/${e}`,originMethod:"getBlockByVersion",params:{with_transactions:t},overrides:{...this.config}});return n}async view(e,t){let{data:n}=await lt({url:this.nodeUrl,body:e,endpoint:"view",originMethod:"getTableItem",params:{ledger_version:t},overrides:{...this.config}});return n}clearCache(e){gi(e)}};de([Ee],xe.prototype,"getAccount",1),de([Ee],xe.prototype,"getAccountTransactions",1),de([Ee,ma(10*60*1e3)],xe.prototype,"getAccountModules",1),de([Ee],xe.prototype,"getAccountModule",1),de([Ee],xe.prototype,"getAccountResources",1),de([Ee],xe.prototype,"getAccountResource",1),de([Ee],xe.prototype,"getEventsByCreationNumber",1),de([Ee],xe.prototype,"getEventsByEventHandle",1),de([Ee],xe.prototype,"submitSignedBCSTransaction",1),de([Ee],xe.prototype,"submitBCSSimulation",1),de([Ee],xe.prototype,"getTransactions",1),de([Ee],xe.prototype,"getTransactionByHash",1),de([Ee],xe.prototype,"getTransactionByVersion",1),de([Ee],xe.prototype,"getLedgerInfo",1),de([Pr()],xe.prototype,"getChainId",1),de([Ee],xe.prototype,"getTableItem",1),de([Ee,Pr({ttlMs:5*60*1e3,tags:["gas_estimates"]})],xe.prototype,"estimateGasPrice",1),de([Ee],xe.prototype,"estimateMaxGasAmount",1),de([Ee],xe.prototype,"getBlockByHeight",1),de([Ee],xe.prototype,"getBlockByVersion",1),de([Ee],xe.prototype,"view",1);var Me=xe,Ka=class extends Error{constructor(t,n){super(t);this.lastSubmittedTransaction=n}},Wa=class extends Error{constructor(t,n){super(t);this.transaction=n}},Ve=class extends Error{constructor(t,n,a,i){super(n);this.status=t;this.message=n;this.errorCode=a;this.vmErrorCode=i}};function Ee(r,e,t){let n=t.value;return t.value=async function(...i){var s,p;try{return await n.apply(this,[...i])}catch(l){throw l instanceof ir?new Ve(l.status,JSON.stringify({message:l.message,...l.data}),(s=l.data)==null?void 0:s.error_code,(p=l.data)==null?void 0:p.vm_error_code):l}},t}var ln=class r{constructor(e,t){this.endpoint=e,this.config=t}static validateAddress(e){if(e.length<66)throw new Error(`${e} is less than 66 chars long.`)}async queryIndexer(e){let t=await lt({url:this.endpoint,body:e,overrides:{WITH_CREDENTIALS:!1,...this.config}});if(t.data.errors)throw new Ve(t.data.errors[0].extensions.code,JSON.stringify({message:t.data.errors[0].message,error_code:t.data.errors[0].extensions.code}));return t.data.data}async getIndexerLedgerInfo(){let e={query:g_};return this.queryIndexer(e)}async getAccountNFTs(e,t){let n=F.ensure(e).hex();r.validateAddress(n);let a={query:p_,variables:{address:n,offset:t==null?void 0:t.offset,limit:t==null?void 0:t.limit}};return this.queryIndexer(a)}async getTokenActivities(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let a={token_data_id:{_eq:n}};t!=null&&t.tokenStandard&&(a.token_standard={_eq:t==null?void 0:t.tokenStandard});let i={query:I_,variables:{where_condition:a,offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getTokenActivitiesCount(e){let t={query:x_,variables:{token_id:e}};return this.queryIndexer(t)}async getAccountTokensCount(e,t){var s,p;let n={owner_address:{_eq:e},amount:{_gt:"0"}};t!=null&&t.tokenStandard&&(n.token_standard={_eq:t==null?void 0:t.tokenStandard});let a=F.ensure(e).hex();r.validateAddress(a);let i={query:u_,variables:{where_condition:n,offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit}};return this.queryIndexer(i)}async getTokenData(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let a={token_data_id:{_eq:n}};t!=null&&t.tokenStandard&&(a.token_standard={_eq:t==null?void 0:t.tokenStandard});let i={query:v_,variables:{where_condition:a,offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getTokenOwnersData(e,t,n){var p,l;let a=F.ensure(e).hex();r.validateAddress(a);let i={token_data_id:{_eq:a},amount:{_gt:"0"}};t&&(i.property_version_v1={_eq:t}),n!=null&&n.tokenStandard&&(i.token_standard={_eq:n==null?void 0:n.tokenStandard});let s={query:B_,variables:{where_condition:i,offset:(p=n==null?void 0:n.options)==null?void 0:p.offset,limit:(l=n==null?void 0:n.options)==null?void 0:l.limit,order_by:n==null?void 0:n.orderBy}};return this.queryIndexer(s)}async getTokenCurrentOwnerData(e,t,n){var p,l;let a=F.ensure(e).hex();r.validateAddress(a);let i={token_data_id:{_eq:a},amount:{_gt:"0"}};t&&(i.property_version_v1={_eq:t}),n!=null&&n.tokenStandard&&(i.token_standard={_eq:n==null?void 0:n.tokenStandard});let s={query:h_,variables:{where_condition:i,offset:(p=n==null?void 0:n.options)==null?void 0:p.offset,limit:(l=n==null?void 0:n.options)==null?void 0:l.limit,order_by:n==null?void 0:n.orderBy}};return this.queryIndexer(s)}async getOwnedTokens(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let a={owner_address:{_eq:n},amount:{_gt:0}};t!=null&&t.tokenStandard&&(a.token_standard={_eq:t==null?void 0:t.tokenStandard});let i={query:M_,variables:{where_condition:a,offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getOwnedTokensByTokenData(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let a={token_data_id:{_eq:n},amount:{_gt:0}};t!=null&&t.tokenStandard&&(a.token_standard={_eq:t==null?void 0:t.tokenStandard});let i={query:S_,variables:{where_condition:a,offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getTokenOwnedFromCollectionAddress(e,t,n){var l,d;let a=F.ensure(e).hex();r.validateAddress(a);let i=F.ensure(t).hex();r.validateAddress(i);let s={owner_address:{_eq:a},current_token_data:{collection_id:{_eq:i}},amount:{_gt:0}};n!=null&&n.tokenStandard&&(s.token_standard={_eq:n==null?void 0:n.tokenStandard});let p={query:A_,variables:{where_condition:s,offset:(l=n==null?void 0:n.options)==null?void 0:l.offset,limit:(d=n==null?void 0:n.options)==null?void 0:d.limit,order_by:n==null?void 0:n.orderBy}};return this.queryIndexer(p)}async getTokenOwnedFromCollectionNameAndCreatorAddress(e,t,n,a){let i=await this.getCollectionAddress(n,t,a);return await this.getTokenOwnedFromCollectionAddress(e,i,a)}async getCollectionData(e,t,n){var p,l;let a=F.ensure(e).hex();r.validateAddress(a);let i={collection_name:{_eq:t},creator_address:{_eq:a}};n!=null&&n.tokenStandard&&(i.token_standard={_eq:n==null?void 0:n.tokenStandard});let s={query:l_,variables:{where_condition:i,offset:(p=n==null?void 0:n.options)==null?void 0:p.offset,limit:(l=n==null?void 0:n.options)==null?void 0:l.limit,order_by:n==null?void 0:n.orderBy}};return this.queryIndexer(s)}async getCollectionAddress(e,t,n){return(await this.getCollectionData(e,t,n)).current_collections_v2[0].collection_id}async getCollectionsWithOwnedTokens(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let a={owner_address:{_eq:n}};t!=null&&t.tokenStandard&&(a.current_collection={token_standard:{_eq:t==null?void 0:t.tokenStandard}});let i={query:d_,variables:{where_condition:a,offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getAccountTransactionsCount(e){let t=F.ensure(e).hex();r.validateAddress(t);let n={query:c_,variables:{address:t}};return this.queryIndexer(n)}async getAccountTransactionsData(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let i={query:y_,variables:{where_condition:{account_address:{_eq:n}},offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getTopUserTransactions(e){let t={query:k_,variables:{limit:e}};return this.queryIndexer(t)}async getUserTransactions(e){var a,i;let t={version:{_lte:e==null?void 0:e.startVersion}},n={query:T_,variables:{where_condition:t,offset:(a=e==null?void 0:e.options)==null?void 0:a.offset,limit:(i=e==null?void 0:e.options)==null?void 0:i.limit,order_by:e==null?void 0:e.orderBy}};return this.queryIndexer(n)}async getDelegatedStakingActivities(e,t){let n=F.ensure(e).hex(),a=F.ensure(t).hex();r.validateAddress(n),r.validateAddress(a);let i={query:m_,variables:{delegatorAddress:n,poolAddress:a}};return this.queryIndexer(i)}async getNumberOfDelegators(e){let t=F.ensure(e).hex();r.validateAddress(t);let n={query:f_,variables:{poolAddress:t}};return this.queryIndexer(n)}async getAccountCoinsData(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let i={query:__,variables:{where_condition:{owner_address:{_eq:n}},offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}async getAccountCoinsDataCount(e){let t=F.ensure(e).hex();r.validateAddress(t);let n={query:i_,variables:{address:t}};return this.queryIndexer(n)}async getAccountOwnedObjects(e,t){var s,p;let n=F.ensure(e).hex();r.validateAddress(n);let i={query:b_,variables:{where_condition:{owner_address:{_eq:n}},offset:(s=t==null?void 0:t.options)==null?void 0:s.offset,limit:(p=t==null?void 0:t.options)==null?void 0:p.limit,order_by:t==null?void 0:t.orderBy}};return this.queryIndexer(i)}};var Xt=class{constructor(e,t,n=!1){let a=null,i=null;if(typeof e=="object"&&hy(e)?(a=e.fullnodeUrl,i=e.indexerUrl,this.network="CUSTOM"):(a=es[e],i=zr[e],this.network=e),this.network==="CUSTOM"&&!a)throw new Error("fullnode url is not provided");i&&(this.indexerClient=new ln(i,t)),this.aptosClient=new Me(a,t,n)}};function O_(r,e,t){Object.getOwnPropertyNames(e.prototype).forEach(n=>{let a=Object.getOwnPropertyDescriptor(e.prototype,n);a&&(a.value=function(...i){return this[t][n](...i)},Object.defineProperty(r.prototype,n,a))}),Object.getOwnPropertyNames(e).forEach(n=>{let a=Object.getOwnPropertyDescriptor(e,n);a&&(a.value=function(...i){return this[t][n](...i)},!r.hasOwnProperty.call(r,n)&&Object.defineProperty(r,n,a))})}O_(Xt,Me,"aptosClient");O_(Xt,ln,"indexerClient");function hy(r){return r.fullnodeUrl!==void 0&&typeof r.fullnodeUrl=="string"}var dn=class{constructor(e,t){this.type=e,this.value=t}},Yt=class{constructor(){this.data={}}setProperty(e,t){this.data[e]=t}};function Ja(r){let e;return r==="string"||r==="String"?e=new _t(za):e=new pt(r).parseTypeTag(),e}function bn(r,e){if(r.length!==e.length)throw new Error("Length of property values and types not match");let t=new Array;return e.forEach((n,a)=>{try{let i=Ja(n),s=new Se;yn(r[a],i,s),t.push(s.getBytes())}catch(i){t.push(new TextEncoder().encode(r[a]))}}),t}function Xa(r,e){if(!r||!e)throw new Error("value or type can not be empty");try{let t=Ja(e),n=new Se;return yn(r,t,n),n.getBytes()}catch(t){return new TextEncoder().encode(r)}}function Ya(r){let e=r.map.data,t=new Yt;return e.forEach(n=>{let{key:a}=n,i=n.value.value,s=n.value.type,p=Ja(s),l=E_(p,i),d=new dn(s,l);t.setProperty(a,d)}),t}function E_(r,e){let t=new ur(new F(e).toUint8Array()),n="";return r instanceof rt?n=t.deserializeU8().toString():r instanceof mt?n=t.deserializeU64().toString():r instanceof gt?n=t.deserializeU128().toString():r instanceof bt?n=t.deserializeBool()?"true":"false":r instanceof it?n=F.fromUint8Array(t.deserializeFixedBytes(32)).hex():r instanceof _t&&r.isStringTypeTag()?n=t.deserializeStr():n=e,n}var Za={};Tr(Za,{PropertyMap:()=>Yt,PropertyValue:()=>dn,Token:()=>Gn,TokenData:()=>qn});var qn=class{constructor(e,t,n,a,i,s,p,l){this.collection=e,this.description=t,this.name=n,this.maximum=a,this.supply=i,this.uri=s,this.default_properties=Ya(p),this.mutability_config=l}},Gn=class{constructor(e,t,n){this.id=e,this.amount=t,this.token_properties=Ya(n)}};var us=class{constructor(e){this.aptosClient=e}async createCollection(e,t,n,a,i=kt,s){let l=await new fe(this.aptosClient,{sender:e.address(),...s}).build("0x3::token::create_collection_script",[],[t,n,a,i,[!1,!1,!1]]),d=Me.generateBCSTransaction(e,l);return(await this.aptosClient.submitSignedBCSTransaction(d)).hash}async createToken(e,t,n,a,i,s,p=kt,l=e.address(),d=0,g=0,b=[],I=[],B=[],x){let V=await new fe(this.aptosClient,{sender:e.address(),...x}).build("0x3::token::create_token_script",[],[t,n,a,i,p,s,l,d,g,[!1,!1,!1,!1,!1],b,bn(I,B),B]),Z=Me.generateBCSTransaction(e,V);return(await this.aptosClient.submitSignedBCSTransaction(Z)).hash}async createTokenWithMutabilityConfig(e,t,n,a,i,s,p=kt,l=e.address(),d=0,g=0,b=[],I=[],B=[],x=[!1,!1,!1,!1,!1],S){let Z=await new fe(this.aptosClient,{sender:e.address(),...S}).build("0x3::token::create_token_script",[],[t,n,a,i,p,s,l,d,g,x,b,I,B]),K=Me.generateBCSTransaction(e,Z);return(await this.aptosClient.submitSignedBCSTransaction(K)).hash}async offerToken(e,t,n,a,i,s,p=0,l){let g=await new fe(this.aptosClient,{sender:e.address(),...l}).build("0x3::token_transfers::offer_script",[],[t,n,a,i,p,s]),b=Me.generateBCSTransaction(e,g);return(await this.aptosClient.submitSignedBCSTransaction(b)).hash}async claimToken(e,t,n,a,i,s=0,p){let d=await new fe(this.aptosClient,{sender:e.address(),...p}).build("0x3::token_transfers::claim_script",[],[t,n,a,i,s]),g=Me.generateBCSTransaction(e,d);return(await this.aptosClient.submitSignedBCSTransaction(g)).hash}async cancelTokenOffer(e,t,n,a,i,s=0,p){let d=await new fe(this.aptosClient,{sender:e.address(),...p}).build("0x3::token_transfers::cancel_offer_script",[],[t,n,a,i,s]),g=Me.generateBCSTransaction(e,d);return(await this.aptosClient.submitSignedBCSTransaction(g)).hash}async directTransferToken(e,t,n,a,i,s,p=0,l){let g=await new fe(this.aptosClient,{sender:e.address(),...l}).build("0x3::token::direct_transfer_script",[],[n,a,i,p,s]),b=new $.MultiAgentRawTransaction(g,[$.AccountAddress.fromHex(t.address())]),I=new $.Ed25519Signature(e.signBuffer(Qe.getSigningMessage(b)).toUint8Array()),B=new $.AccountAuthenticatorEd25519(new $.Ed25519PublicKey(e.signingKey.publicKey),I),x=new $.Ed25519Signature(t.signBuffer(Qe.getSigningMessage(b)).toUint8Array()),S=new $.AccountAuthenticatorEd25519(new $.Ed25519PublicKey(t.signingKey.publicKey),x),V=new $.TransactionAuthenticatorMultiAgent(B,[$.AccountAddress.fromHex(t.address())],[S]),Z=Le(new $.SignedTransaction(g,V));return(await this.aptosClient.submitSignedBCSTransaction(Z)).hash}async directTransferTokenWithFeePayer(e,t,n,a,i,s,p,l=0,d){let b=await new fe(this.aptosClient,{sender:e.address(),...d}).build("0x3::token::direct_transfer_script",[],[n,a,i,l,s]),I=new $.FeePayerRawTransaction(b,[$.AccountAddress.fromHex(t.address())],$.AccountAddress.fromHex(p.address())),B=new $.Ed25519Signature(e.signBuffer(Qe.getSigningMessage(I)).toUint8Array()),x=new $.AccountAuthenticatorEd25519(new $.Ed25519PublicKey(e.signingKey.publicKey),B),S=new $.Ed25519Signature(t.signBuffer(Qe.getSigningMessage(I)).toUint8Array()),V=new $.AccountAuthenticatorEd25519(new $.Ed25519PublicKey(t.signingKey.publicKey),S),Z=new $.Ed25519Signature(p.signBuffer(Qe.getSigningMessage(I)).toUint8Array()),K=new $.AccountAuthenticatorEd25519(new $.Ed25519PublicKey(p.signingKey.publicKey),Z),H=new $.TransactionAuthenticatorFeePayer(x,[$.AccountAddress.fromHex(t.address())],[V],{address:$.AccountAddress.fromHex(p.address()),authenticator:K}),he=Le(new $.SignedTransaction(b,H));return(await this.aptosClient.submitSignedBCSTransaction(he)).hash}async optInTokenTransfer(e,t,n){let i=await new fe(this.aptosClient,{sender:e.address(),...n}).build("0x3::token::opt_in_direct_transfer",[],[t]),s=Me.generateBCSTransaction(e,i);return(await this.aptosClient.submitSignedBCSTransaction(s)).hash}async transferWithOptIn(e,t,n,a,i,s,p,l){let g=await new fe(this.aptosClient,{sender:e.address(),...l}).build("0x3::token::transfer_with_opt_in",[],[t,n,a,i,s,p]),b=Me.generateBCSTransaction(e,g);return(await this.aptosClient.submitSignedBCSTransaction(b)).hash}async burnByCreator(e,t,n,a,i,s,p){let d=await new fe(this.aptosClient,{sender:e.address(),...p}).build("0x3::token::burn_by_creator",[],[t,n,a,i,s]),g=Me.generateBCSTransaction(e,d);return(await this.aptosClient.submitSignedBCSTransaction(g)).hash}async burnByOwner(e,t,n,a,i,s,p){let d=await new fe(this.aptosClient,{sender:e.address(),...p}).build("0x3::token::burn",[],[t,n,a,i,s]),g=Me.generateBCSTransaction(e,d);return(await this.aptosClient.submitSignedBCSTransaction(g)).hash}async mutateTokenProperties(e,t,n,a,i,s,p,l,d,g,b){let B=await new fe(this.aptosClient,{sender:e.address(),...b}).build("0x3::token::mutate_token_properties",[],[t,n,a,i,s,p,l,d,g]),x=Me.generateBCSTransaction(e,B);return(await this.aptosClient.submitSignedBCSTransaction(x)).hash}async getCollectionData(e,t){let a=(await this.aptosClient.getAccountResources(e)).find(l=>l.type==="0x3::token::Collections"),{handle:i}=a.data.collection_data,s={key_type:"0x1::string::String",value_type:"0x3::token::CollectionData",key:t};return await this.aptosClient.getTableItem(i,s)}async getTokenData(e,t,n){let a=e instanceof F?e.hex():e,i=await this.aptosClient.getAccountResource(a,"0x3::token::Collections"),{handle:s}=i.data.token_data,l={key_type:"0x3::token::TokenDataId",value_type:"0x3::token::TokenData",key:{creator:a,collection:t,name:n}},d=await this.aptosClient.getTableItem(s,l);return new qn(d.collection,d.description,d.name,d.maximum,d.supply,d.uri,d.default_properties,d.mutability_config)}async getToken(e,t,n,a="0"){let i={creator:e instanceof F?e.hex():e,collection:t,name:n};return this.getTokenForAccount(e,{token_data_id:i,property_version:a})}async getTokenForAccount(e,t){let n=await this.aptosClient.getAccountResource(e instanceof F?e.hex():e,"0x3::token::TokenStore"),{handle:a}=n.data.tokens,i={key_type:"0x3::token::TokenId",value_type:"0x3::token::Token",key:t};try{let s=await this.aptosClient.getTableItem(a,i);return new Gn(s.id,s.amount,s.token_properties)}catch(s){return(s==null?void 0:s.status)===404?{id:t,amount:"0",token_properties:new Yt}:s}}};var Zt=class{constructor(e){this.assetType="0x1::fungible_asset::Metadata";this.provider=e}async transfer(e,t,n,a,i){let s=await this.generateTransfer(e,t,n,a,i);return await this.provider.signAndSubmitTransaction(e,s)}async getPrimaryBalance(e,t){let n={function:"0x1::primary_fungible_store::balance",type_arguments:[this.assetType],arguments:[F.ensure(e).hex(),F.ensure(t).hex()]},a=await this.provider.view(n);return BigInt(a[0])}async generateTransfer(e,t,n,a,i){return await new fe(this.provider,{sender:e.address(),...i}).build("0x1::primary_fungible_store::transfer",[this.assetType],[F.ensure(t).hex(),F.ensure(n).hex(),a])}};var mn={BOOLEAN:"bool",U8:"u8",U16:"u16",U32:"u32",U64:"u64",U128:"u128",U256:"u256",ADDRESS:"address",VECTOR:"vector<u8>",STRING:"string"},cs=class{constructor(e){this.tokenType="0x4::token::Token";this.provider=e}async submitTransaction(e,t,n,a,i){let p=await new fe(this.provider,{sender:e.address(),...i}).build(`0x4::aptos_token::${t}`,n,a),l=Me.generateBCSTransaction(e,p);return(await this.provider.submitSignedBCSTransaction(l)).hash}async createCollection(e,t,n,a,i=kt,s,p){var l,d,g,b,I,B,x,S,V,Z,K;return this.submitTransaction(e,"create_collection",[],[t,i,n,a,(l=s==null?void 0:s.mutableDescription)!=null?l:!0,(d=s==null?void 0:s.mutableRoyalty)!=null?d:!0,(g=s==null?void 0:s.mutableURI)!=null?g:!0,(b=s==null?void 0:s.mutableTokenDescription)!=null?b:!0,(I=s==null?void 0:s.mutableTokenName)!=null?I:!0,(B=s==null?void 0:s.mutableTokenProperties)!=null?B:!0,(x=s==null?void 0:s.mutableTokenURI)!=null?x:!0,(S=s==null?void 0:s.tokensBurnableByCreator)!=null?S:!0,(V=s==null?void 0:s.tokensFreezableByCreator)!=null?V:!0,(Z=s==null?void 0:s.royaltyNumerator)!=null?Z:0,(K=s==null?void 0:s.royaltyDenominator)!=null?K:1],p)}async mint(e,t,n,a,i,s=[],p=[],l=[],d){return this.submitTransaction(e,"mint",[],[t,n,a,i,s,p,bn(l,p)],d)}async mintSoulBound(e,t,n,a,i,s,p=[],l=[],d=[],g){return this.submitTransaction(e,"mint_soul_bound",[],[t,n,a,i,p,l,bn(d,l),s.address().hex()],g)}async burnToken(e,t,n,a){return this.submitTransaction(e,"burn",[n||this.tokenType],[F.ensure(t).hex()],a)}async freezeTokenTransafer(e,t,n,a){return this.submitTransaction(e,"freeze_transfer",[n||this.tokenType],[F.ensure(t).hex()],a)}async unfreezeTokenTransafer(e,t,n,a){return this.submitTransaction(e,"unfreeze_transfer",[n||this.tokenType],[F.ensure(t).hex()],a)}async setTokenDescription(e,t,n,a,i){return this.submitTransaction(e,"set_description",[a||this.tokenType],[F.ensure(t).hex(),n],i)}async setTokenName(e,t,n,a,i){return this.submitTransaction(e,"set_name",[a||this.tokenType],[F.ensure(t).hex(),n],i)}async setTokenURI(e,t,n,a,i){return this.submitTransaction(e,"set_uri",[a||this.tokenType],[F.ensure(t).hex(),n],i)}async addTokenProperty(e,t,n,a,i,s,p){return this.submitTransaction(e,"add_property",[s||this.tokenType],[F.ensure(t).hex(),n,mn[a],Xa(i,mn[a])],p)}async removeTokenProperty(e,t,n,a,i){return this.submitTransaction(e,"remove_property",[a||this.tokenType],[F.ensure(t).hex(),n],i)}async updateTokenProperty(e,t,n,a,i,s,p){return this.submitTransaction(e,"update_property",[s||this.tokenType],[F.ensure(t).hex(),n,mn[a],Xa(i,mn[a])],p)}async addTypedProperty(e,t,n,a,i,s,p){return this.submitTransaction(e,"add_typed_property",[s||this.tokenType,mn[a]],[F.ensure(t).hex(),n,i],p)}async updateTypedProperty(e,t,n,a,i,s,p){return this.submitTransaction(e,"update_typed_property",[s||this.tokenType,mn[a]],[F.ensure(t).hex(),n,i],p)}async transferTokenOwnership(e,t,n,a,i){let p=await new fe(this.provider,{sender:e.address(),...i}).build("0x1::object::transfer",[a||this.tokenType],[F.ensure(t).hex(),F.ensure(n).hex()]),l=Me.generateBCSTransaction(e,p);return(await this.provider.submitSignedBCSTransaction(l)).hash}async transfer(e,t){let n=t;if(n==null&&(n=(await this.provider.getTokenData(F.ensure(e.tokenAddress).hex())).current_token_datas_v2[0].is_fungible_v2),n){let s=e;return await new Zt(this.provider).transfer(s.owner,s.tokenAddress,s.recipient,s.amount,s.extraArgs)}let a=e;return await this.transferTokenOwnership(a.owner,a.tokenAddress,a.recipient,a.tokenType,a.extraArgs)}async burnObject(e,t,n,a){let s=await new fe(this.provider,{sender:e.address(),...a}).build("0x1::object::burn",[n||"0x1::object::ObjectCore"],[F.ensure(t).hex()]),p=Me.generateBCSTransaction(e,s);return(await this.provider.submitSignedBCSTransaction(p)).hash}};var ys="0x1::aptos_account::transfer_coins",F_="0x1::coin::transfer",ls=class{constructor(e){this.aptosClient=e}async transfer(e,t,n,a){var B,x,S;let i=((B=a==null?void 0:a.coinType)!=null?B:"").toString().includes("::");if(a!=null&&a.coinType&&!i&&re.isValid(a.coinType)){console.warn("to transfer a fungible asset, use `FungibleAssetClient()` class for better support");let V=new Xt({fullnodeUrl:this.aptosClient.nodeUrl,indexerUrl:(x=zr[Oa[this.aptosClient.nodeUrl]])!=null?x:this.aptosClient.nodeUrl});return await new Zt(V).transfer(e,a==null?void 0:a.coinType,cn(t),n)}let s=(S=a==null?void 0:a.coinType)!=null?S:sr,p;(a==null?void 0:a.createReceiverIfMissing)===void 0?p=ys:p=a!=null&&a.createReceiverIfMissing?ys:F_;let l=cn(t),g=await new fe(this.aptosClient,{sender:e.address(),...a}).build(p,[s],[l,n]),b=Me.generateBCSTransaction(e,g);return(await this.aptosClient.submitSignedBCSTransaction(b)).hash}async checkBalance(e,t){var l,d,g;let n=((l=t==null?void 0:t.coinType)!=null?l:"").toString().includes("::");if(t!=null&&t.coinType&&!n&&re.isValid(t.coinType)){console.warn("to check balance of a fungible asset, use `FungibleAssetClient()` class for better support");let b=new Xt({fullnodeUrl:this.aptosClient.nodeUrl,indexerUrl:(d=zr[Oa[this.aptosClient.nodeUrl]])!=null?d:this.aptosClient.nodeUrl});return await new Zt(b).getPrimaryBalance(cn(e),t==null?void 0:t.coinType)}let i=`0x1::coin::CoinStore<${(g=t==null?void 0:t.coinType)!=null?g:sr}>`,s=cn(e),p=await this.aptosClient.getAccountResource(s,i);return BigInt(p.data.coin.value)}};var ds=class extends Me{constructor(t,n,a){super(t,a);if(!n)throw new Error("Faucet URL cannot be empty.");this.faucetUrl=n,this.config=a}async fundAccount(t,n,a=da){let{data:i}=await lt({url:this.faucetUrl,endpoint:"mint",body:null,params:{address:F.ensure(t).noPrefix(),amount:n},overrides:{...this.config},originMethod:"fundAccount"}),s=[];for(let p=0;p<i.length;p+=1){let l=i[p];s.push(this.waitForTransaction(l,{timeoutSecs:a}))}return await Promise.all(s),i}};var bs={testnet:"0x5f8fd2347449685cf41d4db97926ec3a096eaf381332be4f1318ad4d16a8497c",mainnet:"0x867ed1f6bf916171b1de3ee92849b8978b7d1b9e0a8cc982a3d19d535dfd9c0c"},Et=/^[a-z\d][a-z\d-]{1,61}[a-z\d]$/,P_=new RegExp("^(?:(?<subdomain>[^.]+)\\.(?!apt$))?(?<domain>[^.]+)(?:\\.apt)?$"),ms=class{constructor(e,t){var n;if(this.provider=e,!bs[this.provider.network]&&!t)throw new Error("Error: For custom providers, you must pass in a contract address");this.contractAddress=(n=bs[this.provider.network])!=null?n:t}async getPrimaryNameByAddress(e){let n=(await this.provider.getAccountResource(this.contractAddress,`${this.contractAddress}::domains::ReverseLookupRegistryV1`)).data,{handle:a}=n.registry,i={key_type:"address",value_type:`${this.contractAddress}::domains::NameRecordKeyV1`,key:e};try{let s=await this.provider.getTableItem(a,i);return s.subdomain_name.vec[0]?`${s.subdomain_name.vec[0]}.${s.domain_name}`:s.domain_name}catch(s){if(s.status===404)return null;throw new Error(s)}}async getAddressByName(e){var i,s;let{domain:t,subdomain:n}=(s=(i=e.match(P_))==null?void 0:i.groups)!=null?s:{};if(!t)return null;let a=n?await this.getRegistrationForSubdomainName(t,n):await this.getRegistrationForDomainName(t);return a===null?null:a.target}async mintAptosName(e,t,n=1,a){if(t.match(Et)===null)throw new Ve(400,`Name ${t} is not valid`);let i=await this.getRegistrationForDomainName(t);if(i&&Math.ceil(Date.now()/1e3)<i.expirationTimestampSeconds)throw new Ve(400,`Name ${t} is not available`);let p=await new fe(this.provider.aptosClient,{sender:e.address(),...a}).build(`${this.contractAddress}::domains::register_domain`,[],[t,n]),l=Me.generateBCSTransaction(e,p);return(await this.provider.submitSignedBCSTransaction(l)).hash}async mintAptosSubdomain(e,t,n,a,i){if(n.match(Et)===null)throw new Ve(400,`Domain name ${n} is not valid`);if(t.match(Et)===null)throw new Ve(400,`Subdomain name ${t} is not valid`);let s=await this.getRegistrationForSubdomainName(n,t);if(s&&Math.ceil(Date.now()/1e3)<s.expirationTimestampSeconds)throw new Ve(400,`Name ${t}.${n} is not available`);let p=await this.getRegistrationForDomainName(n);if(p===null)throw new Ve(400,`Domain name ${n} does not exist`);let l=Math.ceil(Date.now()/1e3);if(p.expirationTimestampSeconds<l)throw new Ve(400,`Domain name ${n} expired`);let d=a||p.expirationTimestampSeconds;if(d<l)throw new Ve(400,`Expiration for ${t}.${n} is before now`);let b=await new fe(this.provider.aptosClient,{sender:e.address(),...i}).build(`${this.contractAddress}::domains::register_subdomain`,[],[t,n,d]),I=Me.generateBCSTransaction(e,b);return(await this.provider.submitSignedBCSTransaction(I)).hash}async setSubdomainAddress(e,t,n,a,i){let s=re.standardizeAddress(a);if(n.match(Et)===null)throw new Ve(400,`Name ${n} is not valid`);if(t.match(Et)===null)throw new Ve(400,`Name ${t} is not valid`);let l=await new fe(this.provider.aptosClient,{sender:e.address(),...i}).build(`${this.contractAddress}::domains::set_subdomain_address`,[],[t,n,s]),d=Me.generateBCSTransaction(e,l);return(await this.provider.submitSignedBCSTransaction(d)).hash}async initReverseLookupRegistry(e,t){let a=await new fe(this.provider.aptosClient,{sender:e.address(),...t}).build(`${this.contractAddress}::domains::init_reverse_lookup_registry_v1`,[],[]),i=Me.generateBCSTransaction(e,a);return(await this.provider.submitSignedBCSTransaction(i)).hash}async getRegistrationForDomainName(e){if(e.match(Et)===null)return null;let n=(await this.provider.getAccountResource(this.contractAddress,`${this.contractAddress}::domains::NameRegistryV1`)).data,{handle:a}=n.registry,i={key_type:`${this.contractAddress}::domains::NameRecordKeyV1`,value_type:`${this.contractAddress}::domains::NameRecordV1`,key:{subdomain_name:{vec:[]},domain_name:e}};try{let s=await this.provider.getTableItem(a,i);return{target:s.target_address.vec.length===1?s.target_address.vec[0]:null,expirationTimestampSeconds:s.expiration_time_sec}}catch(s){if(s.status===404)return null;throw new Error(s)}}async getRegistrationForSubdomainName(e,t){if(e.match(Et)===null||t.match(Et)===null)return null;let a=(await this.provider.getAccountResource(this.contractAddress,`${this.contractAddress}::domains::NameRegistryV1`)).data,{handle:i}=a.registry,s={key_type:`${this.contractAddress}::domains::NameRecordKeyV1`,value_type:`${this.contractAddress}::domains::NameRecordV1`,key:{subdomain_name:{vec:[t]},domain_name:e}};try{let p=await this.provider.getTableItem(i,s);return{target:p.target_address.vec.length===1?p.target_address.vec[0]:null,expirationTimestampSeconds:p.expiration_time_sec}}catch(p){if(p.status===404)return null;throw new Error(p)}}};var eo=()=>Math.floor(Date.now()/1e3),$n=class{constructor(e,t,n,a,i){this.lastUncommintedNumber=null;this.currentNumber=null;this.lock=!1;this.provider=e,this.account=t,this.maxWaitTime=n,this.maximumInFlight=a,this.sleepTime=i}async nextSequenceNumber(){for(;this.lock;)await or(this.sleepTime);this.lock=!0;let e=BigInt(0);try{if((this.lastUncommintedNumber===null||this.currentNumber===null)&&await this.initialize(),this.currentNumber-this.lastUncommintedNumber>=this.maximumInFlight){await this.update();let t=eo();for(;this.currentNumber-this.lastUncommintedNumber>=this.maximumInFlight;)await or(this.sleepTime),eo()-t>this.maxWaitTime?(console.warn(`Waited over 30 seconds for a transaction to commit, resyncing ${this.account.address()}`),await this.initialize()):await this.update()}e=this.currentNumber,this.currentNumber+=BigInt(1)}catch(t){console.error("error in getting next sequence number for this account",t)}finally{this.lock=!1}return e}async initialize(){let{sequence_number:e}=await this.provider.getAccount(this.account.address());this.currentNumber=BigInt(e),this.lastUncommintedNumber=BigInt(e)}async update(){let{sequence_number:e}=await this.provider.getAccount(this.account.address());return this.lastUncommintedNumber=BigInt(e),this.lastUncommintedNumber}async synchronize(){if(this.lastUncommintedNumber!==this.currentNumber){for(;this.lock;)await or(this.sleepTime);this.lock=!0;try{await this.update();let e=eo();for(;this.lastUncommintedNumber!==this.currentNumber;)eo()-e>this.maxWaitTime?(console.warn(`Waited over 30 seconds for a transaction to commit, resyncing ${this.account.address()}`),await this.initialize()):(await or(this.sleepTime),await this.update())}catch(e){console.error("error in synchronizing this account sequence number with the one on chain",e)}finally{this.lock=!1}}}};var D_=go(R_(),1);var N_=D_.default;var gn=class{constructor(){this.queue=[];this.pendingDequeue=[];this.cancelled=!1}enqueue(e){if(this.cancelled=!1,this.pendingDequeue.length>0){let t=this.pendingDequeue.shift();t==null||t.resolve(e);return}this.queue.push(e)}async dequeue(){return this.queue.length>0?Promise.resolve(this.queue.shift()):new Promise((e,t)=>{this.pendingDequeue.push({resolve:e,reject:t})})}isEmpty(){return this.queue.length===0}cancel(){this.cancelled=!0,this.pendingDequeue.forEach(async({reject:e})=>{e(new fn("Task cancelled"))}),this.pendingDequeue=[],this.queue.length=0}isCancelled(){return this.cancelled}pendingDequeueLength(){return this.pendingDequeue.length}},fn=class extends Error{};var U_="fulfilled",z_=(a=>(a.TransactionSent="transactionSent",a.TransactionSendFailed="transactionsendFailed",a.TransactionExecuted="transactionExecuted",a.TransactionExecutionFailed="transactionexecutionFailed",a))(z_||{}),fs=class extends N_{constructor(t,n,a=30,i=100,s=10){super();this.taskQueue=new gn;this.transactionsQueue=new gn;this.outstandingTransactions=new gn;this.sentTransactions=[];this.executedTransactions=[];this.provider=t,this.account=n,this.started=!1,this.accountSequnceNumber=new $n(t,n,a,i,s)}async submitNextTransaction(){try{for(;;){if(this.transactionsQueue.isEmpty())return;let t=await this.accountSequnceNumber.nextSequenceNumber();if(t===null)return;let n=await this.generateNextTransaction(this.account,t);if(!n)return;let a=this.provider.submitSignedBCSTransaction(n);await this.outstandingTransactions.enqueue([a,t])}}catch(t){if(t instanceof fn)return;console.log(t)}}async processTransactions(){try{for(;;){let t=[],n=[],[a,i]=await this.outstandingTransactions.dequeue();for(t.push(a),n.push(i);!this.outstandingTransactions.isEmpty();)[a,i]=await this.outstandingTransactions.dequeue(),t.push(a),n.push(i);let s=await Promise.allSettled(t);for(let p=0;p<s.length&&p<n.length;p+=1){let l=s[p];i=n[p],l.status===U_?(this.sentTransactions.push([l.value.hash,i,null]),this.emit("transactionSent",[this.sentTransactions.length,l.value.hash]),await this.checkTransaction(l,i)):(this.sentTransactions.push([l.status,i,l.reason]),this.emit("transactionsendFailed",[this.sentTransactions.length,l.reason]))}}}catch(t){if(t instanceof fn)return;console.log(t)}}async checkTransaction(t,n){let a=[];a.push(this.provider.waitForTransactionWithResult(t.value.hash,{checkSuccess:!0}));let i=await Promise.allSettled(a);for(let s=0;s<i.length;s+=1){let p=i[s];p.status===U_?(this.executedTransactions.push([p.value.hash,n,null]),this.emit("transactionExecuted",[this.executedTransactions.length,p.value.hash])):(this.executedTransactions.push([p.status,n,p.reason]),this.emit("transactionexecutionFailed",[this.executedTransactions.length,p.reason]))}}async push(t){await this.transactionsQueue.enqueue(t)}async generateNextTransaction(t,n){if(this.transactionsQueue.isEmpty())return;let a=await this.transactionsQueue.dequeue(),i=await this.provider.generateRawTransaction(t.address(),a,{providedSequenceNumber:n});return Me.generateBCSTransaction(t,i)}async run(){try{for(;!this.taskQueue.isCancelled();)await(await this.taskQueue.dequeue())()}catch(t){throw new Error(t)}}start(){if(this.started)throw new Error("worker has already started");this.started=!0,this.taskQueue.enqueue(()=>this.submitNextTransaction()),this.taskQueue.enqueue(()=>this.processTransactions()),this.run()}stop(){if(this.taskQueue.isCancelled())throw new Error("worker has already stopped");this.started=!1,this.taskQueue.cancel()}};var ro={};Tr(ro,{AptosErrorCode:()=>Ms,MoveFunctionVisibility:()=>Ss,RoleType:()=>Is});var Ms=(w=>(w.ACCOUNT_NOT_FOUND="account_not_found",w.RESOURCE_NOT_FOUND="resource_not_found",w.MODULE_NOT_FOUND="module_not_found",w.STRUCT_FIELD_NOT_FOUND="struct_field_not_found",w.VERSION_NOT_FOUND="version_not_found",w.TRANSACTION_NOT_FOUND="transaction_not_found",w.TABLE_ITEM_NOT_FOUND="table_item_not_found",w.BLOCK_NOT_FOUND="block_not_found",w.STATE_VALUE_NOT_FOUND="state_value_not_found",w.VERSION_PRUNED="version_pruned",w.BLOCK_PRUNED="block_pruned",w.INVALID_INPUT="invalid_input",w.INVALID_TRANSACTION_UPDATE="invalid_transaction_update",w.SEQUENCE_NUMBER_TOO_OLD="sequence_number_too_old",w.VM_ERROR="vm_error",w.HEALTH_CHECK_FAILED="health_check_failed",w.MEMPOOL_IS_FULL="mempool_is_full",w.INTERNAL_ERROR="internal_error",w.WEB_FRAMEWORK_ERROR="web_framework_error",w.BCS_NOT_SUPPORTED="bcs_not_supported",w.API_DISABLED="api_disabled",w))(Ms||{});var Ss=(n=>(n.PRIVATE="private",n.PUBLIC="public",n.FRIEND="friend",n))(Ss||{});var Is=(t=>(t.VALIDATOR="validator",t.FULL_NODE="full_node",t))(Is||{});var L_=(t=>(t.AccountAddress="account_address",t.TransactionVersion="transaction_version",t))(L_||{}),H_=(n=>(n.AccountAddress="account_address",n.MinBlockHeight="min_block_height",n.NumDistinctVersions="num_distinct_versions",n))(H_||{}),q_=(t=>(t.AccountAddress="account_address",t.TransactionVersion="transaction_version",t))(q_||{}),G_=(t=>(t.Address="address",t.TransactionVersion="transaction_version",t))(G_||{}),$_=(d=>(d.BlockHeight="block_height",d.Epoch="epoch",d.FailedProposerIndices="failed_proposer_indices",d.Id="id",d.PreviousBlockVotesBitvec="previous_block_votes_bitvec",d.Proposer="proposer",d.Round="round",d.Timestamp="timestamp",d.Version="version",d))($_||{}),Q_=(S=>(S.ActivityType="activity_type",S.Amount="amount",S.BlockHeight="block_height",S.CoinType="coin_type",S.EntryFunctionIdStr="entry_function_id_str",S.EventAccountAddress="event_account_address",S.EventCreationNumber="event_creation_number",S.EventIndex="event_index",S.EventSequenceNumber="event_sequence_number",S.IsGasFee="is_gas_fee",S.IsTransactionSuccess="is_transaction_success",S.OwnerAddress="owner_address",S.StorageRefundAmount="storage_refund_amount",S.TransactionTimestamp="transaction_timestamp",S.TransactionVersion="transaction_version",S))(Q_||{}),j_=(s=>(s.Amount="amount",s.CoinType="coin_type",s.CoinTypeHash="coin_type_hash",s.OwnerAddress="owner_address",s.TransactionTimestamp="transaction_timestamp",s.TransactionVersion="transaction_version",s))(j_||{}),K_=(g=>(g.CoinType="coin_type",g.CoinTypeHash="coin_type_hash",g.CreatorAddress="creator_address",g.Decimals="decimals",g.Name="name",g.SupplyAggregatorTableHandle="supply_aggregator_table_handle",g.SupplyAggregatorTableKey="supply_aggregator_table_key",g.Symbol="symbol",g.TransactionCreatedTimestamp="transaction_created_timestamp",g.TransactionVersionCreated="transaction_version_created",g))(K_||{}),W_=(s=>(s.CoinType="coin_type",s.CoinTypeHash="coin_type_hash",s.Supply="supply",s.TransactionEpoch="transaction_epoch",s.TransactionTimestamp="transaction_timestamp",s.TransactionVersion="transaction_version",s))(W_||{}),J_=(B=>(B.CollectionDataIdHash="collection_data_id_hash",B.CollectionName="collection_name",B.CreatorAddress="creator_address",B.Description="description",B.DescriptionMutable="description_mutable",B.Maximum="maximum",B.MaximumMutable="maximum_mutable",B.MetadataUri="metadata_uri",B.Supply="supply",B.TableHandle="table_handle",B.TransactionTimestamp="transaction_timestamp",B.TransactionVersion="transaction_version",B.UriMutable="uri_mutable",B))(J_||{}),X_=(p=>(p.Domain="domain",p.ExpirationTimestamp="expiration_timestamp",p.IsDeleted="is_deleted",p.LastTransactionVersion="last_transaction_version",p.RegisteredAddress="registered_address",p.Subdomain="subdomain",p.TokenName="token_name",p))(X_||{}),Y_=(l=>(l.Domain="domain",l.ExpirationTimestamp="expiration_timestamp",l.IsDeleted="is_deleted",l.LastTransactionVersion="last_transaction_version",l.RegisteredAddress="registered_address",l.Subdomain="subdomain",l.TokenName="token_name",l.TokenStandard="token_standard",l))(Y_||{}),Z_=(b=>(b.Domain="domain",b.DomainWithSuffix="domain_with_suffix",b.ExpirationTimestamp="expiration_timestamp",b.IsActive="is_active",b.IsPrimary="is_primary",b.LastTransactionVersion="last_transaction_version",b.OwnerAddress="owner_address",b.RegisteredAddress="registered_address",b.Subdomain="subdomain",b.TokenName="token_name",b.TokenStandard="token_standard",b))(Z_||{}),ep=(s=>(s.Amount="amount",s.CoinType="coin_type",s.CoinTypeHash="coin_type_hash",s.LastTransactionTimestamp="last_transaction_timestamp",s.LastTransactionVersion="last_transaction_version",s.OwnerAddress="owner_address",s))(ep||{}),tp=(B=>(B.CollectionDataIdHash="collection_data_id_hash",B.CollectionName="collection_name",B.CreatorAddress="creator_address",B.Description="description",B.DescriptionMutable="description_mutable",B.LastTransactionTimestamp="last_transaction_timestamp",B.LastTransactionVersion="last_transaction_version",B.Maximum="maximum",B.MaximumMutable="maximum_mutable",B.MetadataUri="metadata_uri",B.Supply="supply",B.TableHandle="table_handle",B.UriMutable="uri_mutable",B))(tp||{}),rp=(l=>(l.CollectionId="collection_id",l.CollectionName="collection_name",l.CollectionUri="collection_uri",l.CreatorAddress="creator_address",l.DistinctTokens="distinct_tokens",l.LastTransactionVersion="last_transaction_version",l.OwnerAddress="owner_address",l.SingleTokenUri="single_token_uri",l))(rp||{}),np=(x=>(x.CollectionId="collection_id",x.CollectionName="collection_name",x.CreatorAddress="creator_address",x.CurrentSupply="current_supply",x.Description="description",x.LastTransactionTimestamp="last_transaction_timestamp",x.LastTransactionVersion="last_transaction_version",x.MaxSupply="max_supply",x.MutableDescription="mutable_description",x.MutableUri="mutable_uri",x.TableHandleV1="table_handle_v1",x.TokenStandard="token_standard",x.TotalMintedV2="total_minted_v2",x.Uri="uri",x))(np||{}),ap=(p=>(p.ActiveTableHandle="active_table_handle",p.InactiveTableHandle="inactive_table_handle",p.LastTransactionVersion="last_transaction_version",p.OperatorCommissionPercentage="operator_commission_percentage",p.StakingPoolAddress="staking_pool_address",p.TotalCoins="total_coins",p.TotalShares="total_shares",p))(ap||{}),op=(p=>(p.DelegationPoolAddress="delegation_pool_address",p.DelegatorAddress="delegator_address",p.LastTransactionTimestamp="last_transaction_timestamp",p.LastTransactionVersion="last_transaction_version",p.PendingVoter="pending_voter",p.TableHandle="table_handle",p.Voter="voter",p))(op||{}),sp=(p=>(p.DelegatorAddress="delegator_address",p.LastTransactionVersion="last_transaction_version",p.ParentTableHandle="parent_table_handle",p.PoolAddress="pool_address",p.PoolType="pool_type",p.Shares="shares",p.TableHandle="table_handle",p))(sp||{}),ip=(d=>(d.Amount="amount",d.AssetType="asset_type",d.IsFrozen="is_frozen",d.IsPrimary="is_primary",d.LastTransactionTimestamp="last_transaction_timestamp",d.LastTransactionVersion="last_transaction_version",d.OwnerAddress="owner_address",d.StorageId="storage_id",d.TokenStandard="token_standard",d))(ip||{}),_p=(p=>(p.AllowUngatedTransfer="allow_ungated_transfer",p.IsDeleted="is_deleted",p.LastGuidCreationNum="last_guid_creation_num",p.LastTransactionVersion="last_transaction_version",p.ObjectAddress="object_address",p.OwnerAddress="owner_address",p.StateKeyHash="state_key_hash",p))(_p||{}),pp=(a=>(a.LastTransactionVersion="last_transaction_version",a.OperatorAddress="operator_address",a.StakingPoolAddress="staking_pool_address",a.VoterAddress="voter_address",a))(pp||{}),up=(p=>(p.DecodedKey="decoded_key",p.DecodedValue="decoded_value",p.IsDeleted="is_deleted",p.Key="key",p.KeyHash="key_hash",p.LastTransactionVersion="last_transaction_version",p.TableHandle="table_handle",p))(up||{}),cp=(w=>(w.CollectionDataIdHash="collection_data_id_hash",w.CollectionName="collection_name",w.CreatorAddress="creator_address",w.DefaultProperties="default_properties",w.Description="description",w.DescriptionMutable="description_mutable",w.LargestPropertyVersion="largest_property_version",w.LastTransactionTimestamp="last_transaction_timestamp",w.LastTransactionVersion="last_transaction_version",w.Maximum="maximum",w.MaximumMutable="maximum_mutable",w.MetadataUri="metadata_uri",w.Name="name",w.PayeeAddress="payee_address",w.PropertiesMutable="properties_mutable",w.RoyaltyMutable="royalty_mutable",w.RoyaltyPointsDenominator="royalty_points_denominator",w.RoyaltyPointsNumerator="royalty_points_numerator",w.Supply="supply",w.TokenDataIdHash="token_data_id_hash",w.UriMutable="uri_mutable",w))(cp||{}),yp=(B=>(B.CollectionId="collection_id",B.Description="description",B.IsFungibleV2="is_fungible_v2",B.LargestPropertyVersionV1="largest_property_version_v1",B.LastTransactionTimestamp="last_transaction_timestamp",B.LastTransactionVersion="last_transaction_version",B.Maximum="maximum",B.Supply="supply",B.TokenDataId="token_data_id",B.TokenName="token_name",B.TokenProperties="token_properties",B.TokenStandard="token_standard",B.TokenUri="token_uri",B))(yp||{}),lp=(I=>(I.Amount="amount",I.CollectionDataIdHash="collection_data_id_hash",I.CollectionName="collection_name",I.CreatorAddress="creator_address",I.LastTransactionTimestamp="last_transaction_timestamp",I.LastTransactionVersion="last_transaction_version",I.Name="name",I.OwnerAddress="owner_address",I.PropertyVersion="property_version",I.TableType="table_type",I.TokenDataIdHash="token_data_id_hash",I.TokenProperties="token_properties",I))(lp||{}),dp=(I=>(I.Amount="amount",I.IsFungibleV2="is_fungible_v2",I.IsSoulboundV2="is_soulbound_v2",I.LastTransactionTimestamp="last_transaction_timestamp",I.LastTransactionVersion="last_transaction_version",I.OwnerAddress="owner_address",I.PropertyVersionV1="property_version_v1",I.StorageId="storage_id",I.TableTypeV1="table_type_v1",I.TokenDataId="token_data_id",I.TokenPropertiesMutatedV1="token_properties_mutated_v1",I.TokenStandard="token_standard",I))(dp||{}),bp=(x=>(x.Amount="amount",x.CollectionDataIdHash="collection_data_id_hash",x.CollectionId="collection_id",x.CollectionName="collection_name",x.CreatorAddress="creator_address",x.FromAddress="from_address",x.LastTransactionTimestamp="last_transaction_timestamp",x.LastTransactionVersion="last_transaction_version",x.Name="name",x.PropertyVersion="property_version",x.TableHandle="table_handle",x.ToAddress="to_address",x.TokenDataId="token_data_id",x.TokenDataIdHash="token_data_id_hash",x))(bp||{}),mp=(t=>(t.Asc="ASC",t.Desc="DESC",t))(mp||{}),gp=(s=>(s.Amount="amount",s.DelegatorAddress="delegator_address",s.EventIndex="event_index",s.EventType="event_type",s.PoolAddress="pool_address",s.TransactionVersion="transaction_version",s))(gp||{}),fp=(t=>(t.FirstTransactionVersion="first_transaction_version",t.StakingPoolAddress="staking_pool_address",t))(fp||{}),Mp=(t=>(t.DelegatorAddress="delegator_address",t.PoolAddress="pool_address",t))(Mp||{}),Sp=(d=>(d.AccountAddress="account_address",d.CreationNumber="creation_number",d.Data="data",d.EventIndex="event_index",d.IndexedType="indexed_type",d.SequenceNumber="sequence_number",d.TransactionBlockHeight="transaction_block_height",d.TransactionVersion="transaction_version",d.Type="type",d))(Sp||{}),Ip=(V=>(V.Amount="amount",V.AssetType="asset_type",V.BlockHeight="block_height",V.EntryFunctionIdStr="entry_function_id_str",V.EventIndex="event_index",V.GasFeePayerAddress="gas_fee_payer_address",V.IsFrozen="is_frozen",V.IsGasFee="is_gas_fee",V.IsTransactionSuccess="is_transaction_success",V.OwnerAddress="owner_address",V.StorageId="storage_id",V.StorageRefundAmount="storage_refund_amount",V.TokenStandard="token_standard",V.TransactionTimestamp="transaction_timestamp",V.TransactionVersion="transaction_version",V.Type="type",V))(Ip||{}),xp=(I=>(I.AssetType="asset_type",I.CreatorAddress="creator_address",I.Decimals="decimals",I.IconUri="icon_uri",I.LastTransactionTimestamp="last_transaction_timestamp",I.LastTransactionVersion="last_transaction_version",I.Name="name",I.ProjectUri="project_uri",I.SupplyAggregatorTableHandleV1="supply_aggregator_table_handle_v1",I.SupplyAggregatorTableKeyV1="supply_aggregator_table_key_v1",I.Symbol="symbol",I.TokenStandard="token_standard",I))(xp||{}),hp=(t=>(t.Db="db",t.IsIndexerUp="is_indexer_up",t))(hp||{}),vp=(e=>(e.ChainId="chain_id",e))(vp||{}),Ap=(t=>(t.Address="address",t.TransactionVersion="transaction_version",t))(Ap||{}),Bp=(H=>(H.BuyItNowPrice="buy_it_now_price",H.CoinType="coin_type",H.CollectionId="collection_id",H.ContractAddress="contract_address",H.CurrentBidPrice="current_bid_price",H.CurrentBidder="current_bidder",H.EntryFunctionIdStr="entry_function_id_str",H.ExpirationTime="expiration_time",H.FeeScheduleId="fee_schedule_id",H.IsDeleted="is_deleted",H.LastTransactionTimestamp="last_transaction_timestamp",H.LastTransactionVersion="last_transaction_version",H.ListingId="listing_id",H.Marketplace="marketplace",H.Seller="seller",H.StartingBidPrice="starting_bid_price",H.TokenAmount="token_amount",H.TokenDataId="token_data_id",H.TokenStandard="token_standard",H))(Bp||{}),kp=(S=>(S.Buyer="buyer",S.CoinType="coin_type",S.CollectionId="collection_id",S.CollectionOfferId="collection_offer_id",S.ContractAddress="contract_address",S.EntryFunctionIdStr="entry_function_id_str",S.ExpirationTime="expiration_time",S.FeeScheduleId="fee_schedule_id",S.IsDeleted="is_deleted",S.ItemPrice="item_price",S.LastTransactionTimestamp="last_transaction_timestamp",S.LastTransactionVersion="last_transaction_version",S.Marketplace="marketplace",S.RemainingTokenAmount="remaining_token_amount",S.TokenStandard="token_standard",S))(kp||{}),Tp=(S=>(S.CoinType="coin_type",S.CollectionId="collection_id",S.ContractAddress="contract_address",S.EntryFunctionIdStr="entry_function_id_str",S.FeeScheduleId="fee_schedule_id",S.IsDeleted="is_deleted",S.LastTransactionTimestamp="last_transaction_timestamp",S.LastTransactionVersion="last_transaction_version",S.ListingId="listing_id",S.Marketplace="marketplace",S.Price="price",S.Seller="seller",S.TokenAmount="token_amount",S.TokenDataId="token_data_id",S.TokenStandard="token_standard",S))(Tp||{}),Cp=(V=>(V.Buyer="buyer",V.CoinType="coin_type",V.CollectionId="collection_id",V.ContractAddress="contract_address",V.EntryFunctionIdStr="entry_function_id_str",V.ExpirationTime="expiration_time",V.FeeScheduleId="fee_schedule_id",V.IsDeleted="is_deleted",V.LastTransactionTimestamp="last_transaction_timestamp",V.LastTransactionVersion="last_transaction_version",V.Marketplace="marketplace",V.OfferId="offer_id",V.Price="price",V.TokenAmount="token_amount",V.TokenDataId="token_data_id",V.TokenStandard="token_standard",V))(Cp||{}),wp=(w=>(w.Buyer="buyer",w.CoinType="coin_type",w.CollectionId="collection_id",w.CollectionName="collection_name",w.ContractAddress="contract_address",w.CreatorAddress="creator_address",w.EntryFunctionIdStr="entry_function_id_str",w.EventIndex="event_index",w.EventType="event_type",w.FeeScheduleId="fee_schedule_id",w.Marketplace="marketplace",w.OfferOrListingId="offer_or_listing_id",w.Price="price",w.PropertyVersion="property_version",w.Seller="seller",w.TokenAmount="token_amount",w.TokenDataId="token_data_id",w.TokenName="token_name",w.TokenStandard="token_standard",w.TransactionTimestamp="transaction_timestamp",w.TransactionVersion="transaction_version",w))(wp||{}),Op=(d=>(d.AnimationOptimizerRetryCount="animation_optimizer_retry_count",d.AssetUri="asset_uri",d.CdnAnimationUri="cdn_animation_uri",d.CdnImageUri="cdn_image_uri",d.CdnJsonUri="cdn_json_uri",d.ImageOptimizerRetryCount="image_optimizer_retry_count",d.JsonParserRetryCount="json_parser_retry_count",d.RawAnimationUri="raw_animation_uri",d.RawImageUri="raw_image_uri",d))(Op||{}),Ep=(t=>(t.NumActiveDelegator="num_active_delegator",t.PoolAddress="pool_address",t))(Ep||{}),Fp=(s=>(s.Asc="asc",s.AscNullsFirst="asc_nulls_first",s.AscNullsLast="asc_nulls_last",s.Desc="desc",s.DescNullsFirst="desc_nulls_first",s.DescNullsLast="desc_nulls_last",s))(Fp||{}),Pp=(n=>(n.LastSuccessVersion="last_success_version",n.LastUpdated="last_updated",n.Processor="processor",n))(Pp||{}),Vp=(p=>(p.NumVotes="num_votes",p.ProposalId="proposal_id",p.ShouldPass="should_pass",p.StakingPoolAddress="staking_pool_address",p.TransactionTimestamp="transaction_timestamp",p.TransactionVersion="transaction_version",p.VoterAddress="voter_address",p))(Vp||{}),Rp=(s=>(s.DecodedKey="decoded_key",s.DecodedValue="decoded_value",s.Key="key",s.TableHandle="table_handle",s.TransactionVersion="transaction_version",s.WriteSetChangeIndex="write_set_change_index",s))(Rp||{}),Dp=(n=>(n.Handle="handle",n.KeyType="key_type",n.ValueType="value_type",n))(Dp||{}),Np=(K=>(K.CoinAmount="coin_amount",K.CoinType="coin_type",K.CollectionDataIdHash="collection_data_id_hash",K.CollectionName="collection_name",K.CreatorAddress="creator_address",K.EventAccountAddress="event_account_address",K.EventCreationNumber="event_creation_number",K.EventIndex="event_index",K.EventSequenceNumber="event_sequence_number",K.FromAddress="from_address",K.Name="name",K.PropertyVersion="property_version",K.ToAddress="to_address",K.TokenAmount="token_amount",K.TokenDataIdHash="token_data_id_hash",K.TransactionTimestamp="transaction_timestamp",K.TransactionVersion="transaction_version",K.TransferType="transfer_type",K))(Np||{}),Up=(S=>(S.AfterValue="after_value",S.BeforeValue="before_value",S.EntryFunctionIdStr="entry_function_id_str",S.EventAccountAddress="event_account_address",S.EventIndex="event_index",S.FromAddress="from_address",S.IsFungibleV2="is_fungible_v2",S.PropertyVersionV1="property_version_v1",S.ToAddress="to_address",S.TokenAmount="token_amount",S.TokenDataId="token_data_id",S.TokenStandard="token_standard",S.TransactionTimestamp="transaction_timestamp",S.TransactionVersion="transaction_version",S.Type="type",S))(Up||{}),zp=(w=>(w.CollectionDataIdHash="collection_data_id_hash",w.CollectionName="collection_name",w.CreatorAddress="creator_address",w.DefaultProperties="default_properties",w.Description="description",w.DescriptionMutable="description_mutable",w.LargestPropertyVersion="largest_property_version",w.Maximum="maximum",w.MaximumMutable="maximum_mutable",w.MetadataUri="metadata_uri",w.Name="name",w.PayeeAddress="payee_address",w.PropertiesMutable="properties_mutable",w.RoyaltyMutable="royalty_mutable",w.RoyaltyPointsDenominator="royalty_points_denominator",w.RoyaltyPointsNumerator="royalty_points_numerator",w.Supply="supply",w.TokenDataIdHash="token_data_id_hash",w.TransactionTimestamp="transaction_timestamp",w.TransactionVersion="transaction_version",w.UriMutable="uri_mutable",w))(zp||{}),Lp=(I=>(I.Amount="amount",I.CollectionDataIdHash="collection_data_id_hash",I.CollectionName="collection_name",I.CreatorAddress="creator_address",I.Name="name",I.OwnerAddress="owner_address",I.PropertyVersion="property_version",I.TableHandle="table_handle",I.TableType="table_type",I.TokenDataIdHash="token_data_id_hash",I.TransactionTimestamp="transaction_timestamp",I.TransactionVersion="transaction_version",I))(Lp||{}),Hp=(d=>(d.CollectionDataIdHash="collection_data_id_hash",d.CollectionName="collection_name",d.CreatorAddress="creator_address",d.Name="name",d.PropertyVersion="property_version",d.TokenDataIdHash="token_data_id_hash",d.TokenProperties="token_properties",d.TransactionTimestamp="transaction_timestamp",d.TransactionVersion="transaction_version",d))(Hp||{}),qp=(b=>(b.BlockHeight="block_height",b.EntryFunctionIdStr="entry_function_id_str",b.Epoch="epoch",b.ExpirationTimestampSecs="expiration_timestamp_secs",b.GasUnitPrice="gas_unit_price",b.MaxGasAmount="max_gas_amount",b.ParentSignatureType="parent_signature_type",b.Sender="sender",b.SequenceNumber="sequence_number",b.Timestamp="timestamp",b.Version="version",b))(qp||{});return tu(By);})();
/*! Bundled license information:

@noble/hashes/esm/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@scure/base/lib/esm/index.js:
  (*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
//# sourceMappingURL=index.global.js.map