{"ast": null, "code": "var e = \"aptos:onNetworkChange\";\nexport { e as a };", "map": {"version": 3, "names": ["e", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosOnNetworkChange.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { NetworkInfo } from '../misc'\n\n/** Version of the feature. */\nexport type AptosOnNetworkChangeVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosOnNetworkChangeNamespace = 'aptos:onNetworkChange'\n/** TODO: docs */\nexport type AptosOnNetworkChangeFeature = {\n  /** Namespace for the feature. */\n  [AptosOnNetworkChangeNamespace]: {\n    /** Version of the feature API. */\n    version: AptosOnNetworkChangeVersion\n    onNetworkChange: AptosOnNetworkChangeMethod\n  }\n}\n/** TODO: docs */\nexport type AptosOnNetworkChangeMethod = (input: AptosOnNetworkChangeInput) => Promise<void>\n/** TODO: docs */\nexport type AptosOnNetworkChangeInput = (newNetwork: NetworkInfo) => void\n"], "mappings": "AAQO,IAAMA,CAAA,GAAgC;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}