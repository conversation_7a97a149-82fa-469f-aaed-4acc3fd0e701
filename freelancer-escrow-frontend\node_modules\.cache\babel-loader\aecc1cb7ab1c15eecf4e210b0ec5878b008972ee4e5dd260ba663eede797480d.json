{"ast": null, "code": "import { d as o } from \"./chunk-VTKPSYKA.mjs\";\nimport { b as i } from \"./chunk-AYKZA676.mjs\";\nimport { b as n } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b as s } from \"./chunk-BF46IXHH.mjs\";\nvar c = class a {\n  constructor(t) {\n    this.signingScheme = 0;\n    let {\n      privateKey: e,\n      address: r\n    } = t;\n    this.privateKey = e, this.publicKey = e.publicKey(), this.accountAddress = r ? s.from(r) : this.publicKey.authKey().derivedAddress();\n  }\n  static generate() {\n    let t = n.generate();\n    return new a({\n      privateKey: t\n    });\n  }\n  static fromDerivationPath(t) {\n    let {\n        path: e,\n        mnemonic: r\n      } = t,\n      u = n.fromDerivationPath(e, r);\n    return new a({\n      privateKey: u\n    });\n  }\n  verifySignature(t) {\n    return this.publicKey.verifySignature(t);\n  }\n  signWithAuthenticator(t) {\n    return new i(this.publicKey, this.privateKey.sign(t));\n  }\n  signTransactionWithAuthenticator(t) {\n    return new i(this.publicKey, this.signTransaction(t));\n  }\n  sign(t) {\n    return this.privateKey.sign(t);\n  }\n  signTransaction(t) {\n    return this.sign(o(t));\n  }\n};\nexport { c as a };", "map": {"version": 3, "names": ["c", "a", "constructor", "t", "signingScheme", "privateKey", "e", "address", "r", "public<PERSON>ey", "accountAddress", "s", "from", "auth<PERSON><PERSON>", "derivedAddress", "generate", "n", "fromDerivationPath", "path", "mnemonic", "u", "verifySignature", "signWithAuthenticator", "i", "sign", "signTransactionWithAuthenticator", "signTransaction", "o"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\account\\Ed25519Account.ts"], "sourcesContent": ["import { AccountAuthenticatorEd25519 } from \"../transactions/authenticator/account\";\nimport { HexInput, SigningScheme } from \"../types\";\nimport { AccountAddress, AccountAddressInput } from \"../core/accountAddress\";\nimport { Ed25519<PERSON>ri<PERSON><PERSON><PERSON>, Ed25519<PERSON><PERSON><PERSON><PERSON><PERSON>, Ed25519Signature } from \"../core/crypto\";\nimport type { Account } from \"./Account\";\nimport { AnyRawTransaction } from \"../transactions/types\";\nimport { generateSigningMessageForTransaction } from \"../transactions/transactionBuilder/signingMessage\";\n\nexport interface Ed25519SignerConstructorArgs {\n  privateKey: Ed25519PrivateKey;\n  address?: AccountAddressInput;\n}\n\nexport interface Ed25519SignerFromDerivationPathArgs {\n  path: string;\n  mnemonic: string;\n}\n\nexport interface VerifyEd25519SignatureArgs {\n  message: HexInput;\n  signature: Ed25519Signature;\n}\n\n/**\n * Signer implementation for the Ed25519 authentication scheme.\n * This extends an {@link Ed25519Account} by adding signing capabilities through an {@link Ed25519PrivateKey}.\n *\n * Note: Generating a signer instance does not create the account on-chain.\n */\nexport class Ed25519Account implements Account {\n  /**\n   * Private key associated with the account\n   */\n  readonly privateKey: Ed25519PrivateKey;\n\n  readonly publicKey: Ed25519PublicKey;\n\n  readonly accountAddress: AccountAddress;\n\n  readonly signingScheme = SigningScheme.Ed25519;\n\n  // region Constructors\n\n  constructor(args: Ed25519SignerConstructorArgs) {\n    const { privateKey, address } = args;\n    this.privateKey = privateKey;\n    this.publicKey = privateKey.publicKey();\n    this.accountAddress = address ? AccountAddress.from(address) : this.publicKey.authKey().derivedAddress();\n  }\n\n  /**\n   * Derives a signer from a randomly generated private key\n   */\n  static generate() {\n    const privateKey = Ed25519PrivateKey.generate();\n    return new Ed25519Account({ privateKey });\n  }\n\n  /**\n   * Derives an account with bip44 path and mnemonics\n   *\n   * @param args.path the BIP44 derive hardened path e.g. m/44'/637'/0'/0'/0'\n   * Detailed description: {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}\n   * @param args.mnemonic the mnemonic seed phrase of the account\n   */\n  static fromDerivationPath(args: Ed25519SignerFromDerivationPathArgs) {\n    const { path, mnemonic } = args;\n    const privateKey = Ed25519PrivateKey.fromDerivationPath(path, mnemonic);\n    return new Ed25519Account({ privateKey });\n  }\n\n  // endregion\n\n  // region Account\n\n  /**\n   * Verify the given message and signature with the public key.\n   *\n   * @param args.message raw message data in HexInput format\n   * @param args.signature signed message Signature\n   * @returns\n   */\n  verifySignature(args: VerifyEd25519SignatureArgs): boolean {\n    return this.publicKey.verifySignature(args);\n  }\n\n  /**\n   * Sign a message using the account's Ed25519 private key.\n   * @param message the signing message, as binary input\n   * @return the AccountAuthenticator containing the signature, together with the account's public key\n   */\n  signWithAuthenticator(message: HexInput): AccountAuthenticatorEd25519 {\n    return new AccountAuthenticatorEd25519(this.publicKey, this.privateKey.sign(message));\n  }\n\n  /**\n   * Sign a transaction using the account's Ed25519 private key.\n   * @param transaction the raw transaction\n   * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key\n   */\n  signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorEd25519 {\n    return new AccountAuthenticatorEd25519(this.publicKey, this.signTransaction(transaction));\n  }\n\n  /**\n   * Sign the given message using the account's Ed25519 private key.\n   * @param message in HexInput format\n   * @returns Signature\n   */\n  sign(message: HexInput): Ed25519Signature {\n    return this.privateKey.sign(message);\n  }\n\n  /**\n   * Sign the given transaction using the available signing capabilities.\n   * @param transaction the transaction to be signed\n   * @returns Signature\n   */\n  signTransaction(transaction: AnyRawTransaction): Ed25519Signature {\n    return this.sign(generateSigningMessageForTransaction(transaction));\n  }\n\n  // endregion\n}\n"], "mappings": ";;;;AA6BO,IAAMA,CAAA,GAAN,MAAMC,CAAkC;EAc7CC,YAAYC,CAAA,EAAoC;IAJhD,KAASC,aAAA,GAAgB;IAKvB,IAAM;MAAEC,UAAA,EAAAC,CAAA;MAAYC,OAAA,EAAAC;IAAQ,IAAIL,CAAA;IAChC,KAAKE,UAAA,GAAaC,CAAA,EAClB,KAAKG,SAAA,GAAYH,CAAA,CAAWG,SAAA,CAAU,GACtC,KAAKC,cAAA,GAAiBF,CAAA,GAAUG,CAAA,CAAeC,IAAA,CAAKJ,CAAO,IAAI,KAAKC,SAAA,CAAUI,OAAA,CAAQ,EAAEC,cAAA,CAAe,CACzG;EAAA;EAKA,OAAOC,SAAA,EAAW;IAChB,IAAMZ,CAAA,GAAaa,CAAA,CAAkBD,QAAA,CAAS;IAC9C,OAAO,IAAId,CAAA,CAAe;MAAEI,UAAA,EAAAF;IAAW,CAAC,CAC1C;EAAA;EASA,OAAOc,mBAAmBd,CAAA,EAA2C;IACnE,IAAM;QAAEe,IAAA,EAAAZ,CAAA;QAAMa,QAAA,EAAAX;MAAS,IAAIL,CAAA;MACrBiB,CAAA,GAAaJ,CAAA,CAAkBC,kBAAA,CAAmBX,CAAA,EAAME,CAAQ;IACtE,OAAO,IAAIP,CAAA,CAAe;MAAEI,UAAA,EAAAe;IAAW,CAAC,CAC1C;EAAA;EAaAC,gBAAgBlB,CAAA,EAA2C;IACzD,OAAO,KAAKM,SAAA,CAAUY,eAAA,CAAgBlB,CAAI,CAC5C;EAAA;EAOAmB,sBAAsBnB,CAAA,EAAgD;IACpE,OAAO,IAAIoB,CAAA,CAA4B,KAAKd,SAAA,EAAW,KAAKJ,UAAA,CAAWmB,IAAA,CAAKrB,CAAO,CAAC,CACtF;EAAA;EAOAsB,iCAAiCtB,CAAA,EAA6D;IAC5F,OAAO,IAAIoB,CAAA,CAA4B,KAAKd,SAAA,EAAW,KAAKiB,eAAA,CAAgBvB,CAAW,CAAC,CAC1F;EAAA;EAOAqB,KAAKrB,CAAA,EAAqC;IACxC,OAAO,KAAKE,UAAA,CAAWmB,IAAA,CAAKrB,CAAO,CACrC;EAAA;EAOAuB,gBAAgBvB,CAAA,EAAkD;IAChE,OAAO,KAAKqB,IAAA,CAAKG,CAAA,CAAqCxB,CAAW,CAAC,CACpE;EAAA;AAGF;AAAA,SAAAH,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}