{"ast": null, "code": "// src/version.ts\nvar WALLET_ADAPTER_CORE_VERSION = \"3.16.0\";\n\n// src/WalletCore.ts\nimport { TxnBuilderTypes as TxnBuilderTypes4, BCS as BCS2 } from \"aptos\";\nimport { Network as Network3, AccountAuthenticatorEd25519, Ed25519P<PERSON><PERSON><PERSON>ey, Ed25519Signature, AptosConfig as AptosConfig3, Aptos as Aptos3, generateRawTransaction, SimpleTransaction, NetworkToChainId } from \"@aptos-labs/ts-sdk\";\nimport EventEmitter2 from \"eventemitter3\";\nimport { getAptosWallets, UserResponseStatus as UserResponseStatus2, isWalletWithRequiredFeatureSet } from \"@aptos-labs/wallet-standard\";\n\n// src/AIP62StandardWallets/sdkWallets.ts\nimport { TWallet } from \"@atomrigslab/aptos-wallet-adapter\";\nvar sdkWallets = [];\nsdkWallets.push(new TWallet());\nvar sdkWallets_default = sdkWallets;\n\n// src/constants.ts\nvar WalletReadyState = /* @__PURE__ */(WalletReadyState2 => {\n  WalletReadyState2[\"Installed\"] = \"Installed\";\n  WalletReadyState2[\"NotDetected\"] = \"NotDetected\";\n  WalletReadyState2[\"Loadable\"] = \"Loadable\";\n  WalletReadyState2[\"Unsupported\"] = \"Unsupported\";\n  return WalletReadyState2;\n})(WalletReadyState || {});\nvar NetworkName = /* @__PURE__ */(NetworkName2 => {\n  NetworkName2[\"Mainnet\"] = \"mainnet\";\n  NetworkName2[\"Testnet\"] = \"testnet\";\n  NetworkName2[\"Devnet\"] = \"devnet\";\n  return NetworkName2;\n})(NetworkName || {});\nvar ChainIdToAnsSupportedNetworkMap = {\n  \"1\": \"mainnet\",\n  \"2\": \"testnet\"\n};\n\n// src/error/index.ts\nvar WalletError = class extends Error {\n  constructor(message, error) {\n    super(message);\n    this.error = error;\n  }\n};\nvar WalletNotSelectedError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletNotSelectedError\";\n  }\n};\nvar WalletNotReadyError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletNotReadyError\";\n  }\n};\nvar WalletConnectionError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletConnectionError\";\n  }\n};\nvar WalletDisconnectionError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletDisconnectionError\";\n  }\n};\nvar WalletAccountError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletAccountError\";\n  }\n};\nvar WalletGetNetworkError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletGetNetworkError\";\n  }\n};\nvar WalletAccountChangeError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletAccountChangeError\";\n  }\n};\nvar WalletNetworkChangeError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletNetworkChangeError\";\n  }\n};\nvar WalletNotConnectedError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletNotConnectedError\";\n  }\n};\nvar WalletSignMessageError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletSignMessageError\";\n  }\n};\nvar WalletSignMessageAndVerifyError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletSignMessageAndVerifyError\";\n  }\n};\nvar WalletSignAndSubmitMessageError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletSignAndSubmitMessageError\";\n  }\n};\nvar WalletSignTransactionError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletSignTransactionError\";\n  }\n};\nvar WalletNotSupportedMethod = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletNotSupportedMethod\";\n  }\n};\nvar WalletChangeNetworkError = class extends WalletError {\n  constructor() {\n    super(...arguments);\n    this.name = \"WalletChangeNetworkError\";\n  }\n};\n\n// src/LegacyWalletPlugins/WalletCoreV1.ts\nimport { HexString } from \"aptos\";\nimport EventEmitter from \"eventemitter3\";\nimport { Buffer } from \"buffer\";\nimport { generateTransactionPayload as generateTransactionPayload2 } from \"@aptos-labs/ts-sdk\";\nimport nacl from \"tweetnacl\";\n\n// src/LegacyWalletPlugins/conversion.ts\nimport { Network, TypeTag, generateTransactionPayload } from \"@aptos-labs/ts-sdk\";\nimport { BCS, TxnBuilderTypes } from \"aptos\";\nfunction convertNetwork(networkInfo) {\n  switch (networkInfo == null ? void 0 : networkInfo.name) {\n    case \"mainnet\":\n      return Network.MAINNET;\n    case \"testnet\":\n      return Network.TESTNET;\n    case \"devnet\":\n      return Network.DEVNET;\n    default:\n      throw new Error(\"Invalid Aptos network name\");\n  }\n}\nfunction convertV2TransactionPayloadToV1BCSPayload(payload) {\n  const deserializer = new BCS.Deserializer(payload.bcsToBytes());\n  return TxnBuilderTypes.TransactionPayload.deserialize(deserializer);\n}\nfunction convertV2PayloadToV1JSONPayload(payload) {\n  var _a, _b;\n  if (\"bytecode\" in payload) {\n    throw new Error(\"script payload not supported\");\n  } else if (\"multisigAddress\" in payload) {\n    const stringTypeTags = (_a = payload.typeArguments) == null ? void 0 : _a.map(typeTag => {\n      if (typeTag instanceof TypeTag) {\n        return typeTag.toString();\n      }\n      return typeTag;\n    });\n    const newPayload = {\n      type: \"multisig_payload\",\n      multisig_address: payload.multisigAddress.toString(),\n      function: payload.function,\n      type_arguments: stringTypeTags || [],\n      arguments: payload.functionArguments\n    };\n    return newPayload;\n  } else {\n    const stringTypeTags = (_b = payload.typeArguments) == null ? void 0 : _b.map(typeTag => {\n      if (typeTag instanceof TypeTag) {\n        return typeTag.toString();\n      }\n      return typeTag;\n    });\n    const newPayload = {\n      type: \"entry_function_payload\",\n      function: payload.function,\n      type_arguments: stringTypeTags || [],\n      arguments: payload.functionArguments\n    };\n    return newPayload;\n  }\n}\nasync function generateTransactionPayloadFromV1Input(aptosConfig, inputV1) {\n  if (\"function\" in inputV1) {\n    const inputV2 = {\n      function: inputV1.function,\n      functionArguments: inputV1.arguments,\n      typeArguments: inputV1.type_arguments\n    };\n    return generateTransactionPayload({\n      ...inputV2,\n      aptosConfig\n    });\n  }\n  throw new Error(\"Payload type not supported\");\n}\n\n// src/utils/scopePollingDetectionStrategy.ts\nfunction scopePollingDetectionStrategy(detect) {\n  if (typeof window === \"undefined\" || typeof document === \"undefined\") return;\n  const disposers = [];\n  function detectAndDispose() {\n    const detected = detect();\n    if (detected) {\n      for (const dispose of disposers) {\n        dispose();\n      }\n    }\n  }\n  const interval = setInterval(detectAndDispose, 1e3);\n  disposers.push(() => clearInterval(interval));\n  if (document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", detectAndDispose, {\n      once: true\n    });\n    disposers.push(() => document.removeEventListener(\"DOMContentLoaded\", detectAndDispose));\n  }\n  if (document.readyState !== \"complete\") {\n    window.addEventListener(\"load\", detectAndDispose, {\n      once: true\n    });\n    disposers.push(() => window.removeEventListener(\"load\", detectAndDispose));\n  }\n  detectAndDispose();\n}\n\n// src/utils/localStorage.ts\nvar LOCAL_STORAGE_ITEM_KEY = \"AptosWalletName\";\nfunction setLocalStorage(walletName) {\n  localStorage.setItem(LOCAL_STORAGE_ITEM_KEY, walletName);\n}\nfunction removeLocalStorage() {\n  localStorage.removeItem(LOCAL_STORAGE_ITEM_KEY);\n}\nfunction getLocalStorage() {\n  localStorage.getItem(LOCAL_STORAGE_ITEM_KEY);\n}\n\n// src/utils/helpers.ts\nimport { Aptos, AptosConfig as AptosConfig2, Network as Network2, NetworkToNodeAPI, Serializable } from \"@aptos-labs/ts-sdk\";\nfunction isMobile() {\n  return /Mobile|iP(hone|od|ad)|Android|BlackBerry|IEMobile|Kindle|NetFront|Silk-Accelerated|(hpw|web)OS|Fennec|Minimo|Opera M(obi|ini)|Blazer|Dolfin|Dolphin|Skyfire|Zune/i.test(navigator.userAgent);\n}\nfunction isInAppBrowser() {\n  const isIphone = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent);\n  const isAndroid = /(Android).*Version\\/[\\d.]+.*Chrome\\/[^\\s]+ Mobile/i.test(navigator.userAgent);\n  return isIphone || isAndroid;\n}\nfunction isRedirectable() {\n  if (typeof navigator === \"undefined\" || !navigator) return false;\n  return isMobile() && !isInAppBrowser();\n}\nfunction generalizedErrorMessage(error) {\n  return typeof error === \"object\" && \"message\" in error ? error.message : error;\n}\nvar areBCSArguments = args => {\n  if (args.length === 0) return false;\n  return args.every(arg => arg instanceof Serializable);\n};\nvar getAptosConfig = networkInfo => {\n  if (!networkInfo) {\n    throw new Error(\"Undefined network\");\n  }\n  if (isAptosNetwork(networkInfo)) {\n    return new AptosConfig2({\n      network: convertNetwork(networkInfo)\n    });\n  }\n  return new AptosConfig2({\n    network: Network2.CUSTOM,\n    fullnode: networkInfo.url\n  });\n};\nvar isAptosNetwork = networkInfo => {\n  if (!networkInfo) {\n    throw new Error(\"Undefined network\");\n  }\n  return NetworkToNodeAPI[networkInfo.name] !== void 0;\n};\nvar fetchDevnetChainId = async () => {\n  const aptos = new Aptos();\n  return await aptos.getChainId();\n};\n\n// src/LegacyWalletPlugins/WalletCoreV1.ts\nvar WalletCoreV1 = class extends EventEmitter {\n  async connect(wallet) {\n    const account = await wallet.connect();\n    return account;\n  }\n  async resolveSignAndSubmitTransaction(payloadData, network, wallet, transactionInput) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    if (areBCSArguments(payloadData.functionArguments)) {\n      const aptosConfig = getAptosConfig(network);\n      const newPayload = await generateTransactionPayload2({\n        ...payloadData,\n        aptosConfig\n      });\n      const oldTransactionPayload2 = convertV2TransactionPayloadToV1BCSPayload(newPayload);\n      return await this.signAndSubmitBCSTransaction(oldTransactionPayload2, wallet, {\n        max_gas_amount: ((_a = transactionInput.options) == null ? void 0 : _a.maxGasAmount) ? BigInt((_b = transactionInput.options) == null ? void 0 : _b.maxGasAmount) : void 0,\n        gas_unit_price: ((_c = transactionInput.options) == null ? void 0 : _c.gasUnitPrice) ? BigInt((_d = transactionInput.options) == null ? void 0 : _d.gasUnitPrice) : void 0\n      });\n    }\n    const oldTransactionPayload = convertV2PayloadToV1JSONPayload(payloadData);\n    return await this.signAndSubmitTransaction(oldTransactionPayload, wallet, {\n      max_gas_amount: ((_e = transactionInput.options) == null ? void 0 : _e.maxGasAmount) ? BigInt((_f = transactionInput.options) == null ? void 0 : _f.maxGasAmount) : void 0,\n      gas_unit_price: ((_g = transactionInput.options) == null ? void 0 : _g.gasUnitPrice) ? BigInt((_h = transactionInput.options) == null ? void 0 : _h.gasUnitPrice) : void 0\n    });\n  }\n  async signAndSubmitTransaction(transaction, wallet, options) {\n    try {\n      const response = await wallet.signAndSubmitTransaction(transaction, options);\n      return response;\n    } catch (error) {\n      const errMsg = typeof error == \"object\" && \"message\" in error ? error.message : error;\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n  async signAndSubmitBCSTransaction(transaction, wallet, options) {\n    if (!(\"signAndSubmitBCSTransaction\" in wallet)) {\n      throw new WalletNotSupportedMethod(`Submit a BCS Transaction is not supported by ${wallet.name}`).message;\n    }\n    try {\n      const response = await wallet.signAndSubmitBCSTransaction(transaction, options);\n      return response;\n    } catch (error) {\n      const errMsg = typeof error == \"object\" && \"message\" in error ? error.message : error;\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n  async signTransaction(transaction, wallet, options) {\n    try {\n      const response = await wallet.signTransaction(transaction, options);\n      return response;\n    } catch (error) {\n      const errMsg = typeof error == \"object\" && \"message\" in error ? error.message : error;\n      throw new WalletSignTransactionError(errMsg).message;\n    }\n  }\n  async signMessageAndVerify(message, wallet, account) {\n    try {\n      const response = await wallet.signMessage(message);\n      if (!response) throw new WalletSignMessageAndVerifyError(\"Failed to sign a message\").message;\n      console.log(\"signMessageAndVerify signMessage response\", response);\n      let verified = false;\n      if (Array.isArray(response.signature)) {\n        const {\n          fullMessage,\n          signature,\n          bitmap\n        } = response;\n        if (bitmap) {\n          const minKeysRequired = account.minKeysRequired;\n          if (signature.length < minKeysRequired) {\n            verified = false;\n          } else {\n            const bits = Array.from(bitmap).flatMap(n => Array.from({\n              length: 8\n            }).map((_, i) => n >> i & 1));\n            const index = bits.map((_, i) => i).filter(i => bits[i]);\n            const publicKeys = account.publicKey;\n            const matchedPublicKeys = publicKeys.filter((_, i) => index.includes(i));\n            verified = true;\n            for (let i = 0; i < signature.length; i++) {\n              const isSigVerified = nacl.sign.detached.verify(Buffer.from(fullMessage), Buffer.from(signature[i], \"hex\"), Buffer.from(matchedPublicKeys[i], \"hex\"));\n              if (!isSigVerified) {\n                verified = false;\n                break;\n              }\n            }\n          }\n        } else {\n          throw new WalletSignMessageAndVerifyError(\"Failed to get a bitmap\").message;\n        }\n      } else {\n        const currentAccountPublicKey = new HexString(account.publicKey);\n        const signature = new HexString(response.signature);\n        verified = nacl.sign.detached.verify(Buffer.from(response.fullMessage), Buffer.from(signature.noPrefix(), \"hex\"), Buffer.from(currentAccountPublicKey.noPrefix(), \"hex\"));\n      }\n      return verified;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageAndVerifyError(errMsg).message;\n    }\n  }\n};\n\n// src/LegacyWalletPlugins/types.ts\nimport { TxnBuilderTypes as TxnBuilderTypes3, Types as Types3 } from \"aptos\";\n\n// src/AIP62StandardWallets/WalletStandard.ts\nimport { UserResponseStatus } from \"@aptos-labs/wallet-standard\";\nimport { MultiEd25519Signature, MultiEd25519PublicKey } from \"@aptos-labs/ts-sdk\";\nvar WalletStandardCore = class {\n  async connect(wallet) {\n    const response = await wallet.connect();\n    if (response.status === UserResponseStatus.REJECTED) {\n      throw new WalletConnectionError(\"User has rejected the request\").message;\n    }\n    return response.args;\n  }\n  async signAndSubmitTransaction(transactionInput, aptos, account, wallet) {\n    try {\n      const transaction = await aptos.transaction.build.simple({\n        sender: account.address.toString(),\n        data: transactionInput.data,\n        options: transactionInput.options\n      });\n      const response = await wallet.signAndSubmitTransaction(transaction);\n      if (response.status === UserResponseStatus.REJECTED) {\n        throw new WalletConnectionError(\"User has rejected the request\").message;\n      }\n      return response.args;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n  async signTransaction(transaction, wallet, asFeePayer) {\n    const response = await wallet.signTransaction(transaction, asFeePayer);\n    if (response.status === UserResponseStatus.REJECTED) {\n      throw new WalletConnectionError(\"User has rejected the request\").message;\n    }\n    return response.args;\n  }\n  async signMessage(message, wallet) {\n    try {\n      const response = await wallet.signMessage(message);\n      if (response.status === UserResponseStatus.REJECTED) {\n        throw new WalletConnectionError(\"User has rejected the request\").message;\n      }\n      return response.args;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageError(errMsg).message;\n    }\n  }\n  async signMessageAndVerify(message, wallet) {\n    try {\n      const response = await wallet.signMessage(message);\n      const account = await wallet.account();\n      if (response.status === UserResponseStatus.REJECTED) {\n        throw new WalletConnectionError(\"Failed to sign a message\").message;\n      }\n      let verified = false;\n      if (response.args.signature instanceof MultiEd25519Signature) {\n        if (!(account.publicKey instanceof MultiEd25519PublicKey)) {\n          throw new WalletSignMessageAndVerifyError(\"Public key and Signature type mismatch\").message;\n        }\n        const {\n          fullMessage,\n          signature\n        } = response.args;\n        const bitmap = signature.bitmap;\n        if (bitmap) {\n          const minKeysRequired = account.publicKey.threshold;\n          if (signature.signatures.length < minKeysRequired) {\n            verified = false;\n          } else {\n            verified = account.publicKey.verifySignature({\n              message: new TextEncoder().encode(fullMessage),\n              signature\n            });\n          }\n        }\n      } else {\n        verified = account.publicKey.verifySignature({\n          message: new TextEncoder().encode(response.args.fullMessage),\n          signature: response.args.signature\n        });\n      }\n      return verified;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageAndVerifyError(errMsg).message;\n    }\n  }\n};\n\n// src/ga/index.ts\nvar GA4 = class {\n  constructor() {\n    this.aptosGAID = \"G-GNVVWBL3J9\";\n    this.injectGA(this.aptosGAID);\n  }\n  gtag(a, b, c) {\n    let dataLayer = window.dataLayer || [];\n    dataLayer.push(arguments);\n  }\n  injectGA(gaID) {\n    if (typeof window === \"undefined\") return;\n    if (!gaID) return;\n    const head = document.getElementsByTagName(\"head\")[0];\n    var myScript = document.createElement(\"script\");\n    myScript.setAttribute(\"src\", `https://www.googletagmanager.com/gtag/js?id=${gaID}`);\n    const that = this;\n    myScript.onload = function () {\n      that.gtag(\"js\", new Date());\n      that.gtag(\"config\", `${gaID}`, {\n        send_page_view: false\n      });\n    };\n    head.insertBefore(myScript, head.children[1]);\n  }\n};\n\n// src/AIP62StandardWallets/registry.ts\nvar aptosStandardSupportedWalletList = [{\n  name: \"Nightly\",\n  url: \"https://chromewebstore.google.com/detail/nightly/fiikommddbeccaoicoejoniammnalkfa?hl=en\",\n  icon: \"data:image/svg+xml;base64,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\",\n  readyState: \"NotDetected\" /* NotDetected */,\n  isAIP62Standard: true\n}, {\n  name: \"Petra\",\n  url: \"https://chromewebstore.google.com/detail/petra-aptos-wallet/ejjladinnckdgjemekebdpeokbikhfci?hl=en\",\n  icon: \"data:image/png;base64,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\",\n  readyState: \"NotDetected\" /* NotDetected */,\n  isAIP62Standard: true\n}];\n\n// src/WalletCore.ts\nvar WalletCore = class extends EventEmitter2 {\n  constructor(plugins, optInWallets) {\n    super();\n    this._wallets = [];\n    this._optInWallets = [];\n    this._standard_wallets = [];\n    this._all_wallets = [];\n    this._wallet = null;\n    this._account = null;\n    this._network = null;\n    this.walletCoreV1 = new WalletCoreV1();\n    this.walletStandardCore = new WalletStandardCore();\n    this._connecting = false;\n    this._connected = false;\n    this.ga4 = new GA4();\n    this.standardizeStandardWalletToPluginWalletType = standardWallet => {\n      var _a, _b, _c;\n      let standardWalletConvertedToWallet = {\n        name: standardWallet.name,\n        url: standardWallet.url,\n        icon: standardWallet.icon,\n        provider: standardWallet,\n        connect: standardWallet.features[\"aptos:connect\"].connect,\n        disconnect: standardWallet.features[\"aptos:disconnect\"].disconnect,\n        network: standardWallet.features[\"aptos:network\"].network,\n        account: standardWallet.features[\"aptos:account\"].account,\n        signAndSubmitTransaction: (_a = standardWallet.features[\"aptos:signAndSubmitTransaction\"]) == null ? void 0 : _a.signAndSubmitTransaction,\n        signMessage: standardWallet.features[\"aptos:signMessage\"].signMessage,\n        onAccountChange: standardWallet.features[\"aptos:onAccountChange\"].onAccountChange,\n        onNetworkChange: standardWallet.features[\"aptos:onNetworkChange\"].onNetworkChange,\n        signTransaction: standardWallet.features[\"aptos:signTransaction\"].signTransaction,\n        openInMobileApp: (_b = standardWallet.features[\"aptos:openInMobileApp\"]) == null ? void 0 : _b.openInMobileApp,\n        changeNetwork: (_c = standardWallet.features[\"aptos:changeNetwork\"]) == null ? void 0 : _c.changeNetwork,\n        readyState: \"Installed\" /* Installed */,\n        isAIP62Standard: true\n      };\n      this._all_wallets = this._all_wallets.filter(item => item.name !== standardWalletConvertedToWallet.name);\n      this._all_wallets.push(standardWalletConvertedToWallet);\n      this.emit(\"standardWalletsAdded\", standardWalletConvertedToWallet);\n    };\n    this._wallets = plugins;\n    this._optInWallets = optInWallets;\n    this.scopePollingDetectionStrategy();\n    this.fetchAptosWallets();\n  }\n  scopePollingDetectionStrategy() {\n    var _a;\n    (_a = this._wallets) == null ? void 0 : _a.forEach(wallet => {\n      this._all_wallets.push(wallet);\n      if (!wallet.readyState) {\n        wallet.readyState = typeof window === \"undefined\" || typeof document === \"undefined\" ? \"Unsupported\" /* Unsupported */ : \"NotDetected\" /* NotDetected */;\n      }\n      if (typeof window !== \"undefined\") {\n        scopePollingDetectionStrategy(() => {\n          const providerName = wallet.providerName || wallet.name.toLowerCase();\n          if (Object.keys(window).includes(providerName)) {\n            wallet.readyState = \"Installed\" /* Installed */;\n            wallet.provider = window[providerName];\n            this.emit(\"readyStateChange\", wallet);\n            return true;\n          }\n          return false;\n        });\n      }\n    });\n  }\n  fetchAptosWallets() {\n    let {\n      aptosWallets,\n      on\n    } = getAptosWallets();\n    this.setWallets(aptosWallets);\n    if (typeof window === \"undefined\") return;\n    const that = this;\n    const removeRegisterListener = on(\"register\", function () {\n      let {\n        aptosWallets: aptosWallets2\n      } = getAptosWallets();\n      that.setWallets(aptosWallets2);\n    });\n    const removeUnregisterListener = on(\"unregister\", function () {\n      let {\n        aptosWallets: aptosWallets2\n      } = getAptosWallets();\n      that.setWallets(aptosWallets2);\n    });\n  }\n  appendNotDetectedStandardSupportedWallets(aptosStandardWallets) {\n    aptosStandardSupportedWalletList.map(supportedWallet => {\n      if (this.excludeWallet(supportedWallet.name)) {\n        return;\n      }\n      const existingWalletIndex = aptosStandardWallets.findIndex(wallet => wallet.name == supportedWallet.name);\n      if (existingWalletIndex === -1) {\n        this._all_wallets.push(supportedWallet);\n        this.emit(\"standardWalletsAdded\", supportedWallet);\n      }\n    });\n  }\n  setWallets(extensionwWallets) {\n    const aptosStandardWallets = [];\n    [...sdkWallets_default, ...extensionwWallets].map(wallet => {\n      if (this.excludeWallet(wallet.name)) {\n        return;\n      }\n      const isValid = isWalletWithRequiredFeatureSet(wallet);\n      if (isValid) {\n        wallet.readyState = \"Installed\" /* Installed */;\n        aptosStandardWallets.push(wallet);\n        this.standardizeStandardWalletToPluginWalletType(wallet);\n      }\n    });\n    this._standard_wallets = aptosStandardWallets;\n    this.appendNotDetectedStandardSupportedWallets(this._standard_wallets);\n  }\n  excludeWallet(walletName) {\n    if (this._optInWallets.length > 0 && !this._optInWallets.includes(walletName)) {\n      return true;\n    }\n    return false;\n  }\n  recordEvent(eventName, additionalInfo) {\n    var _a, _b, _c;\n    this.ga4.gtag(\"event\", `wallet_adapter_${eventName}`, {\n      wallet: (_a = this._wallet) == null ? void 0 : _a.name,\n      network: (_b = this._network) == null ? void 0 : _b.name,\n      network_url: (_c = this._network) == null ? void 0 : _c.url,\n      adapter_core_version: WALLET_ADAPTER_CORE_VERSION,\n      send_to: \"G-GNVVWBL3J9\",\n      ...additionalInfo\n    });\n  }\n  ensureWalletExists(wallet) {\n    if (!wallet) {\n      throw new WalletNotConnectedError().name;\n    }\n    if (!(wallet.readyState === \"Loadable\" /* Loadable */ || wallet.readyState === \"Installed\" /* Installed */)) throw new WalletNotReadyError(\"Wallet is not set\").name;\n  }\n  ensureAccountExists(account) {\n    if (!account) {\n      throw new WalletAccountError(\"Account is not set\").name;\n    }\n  }\n  doesWalletExist() {\n    if (!this._connected || this._connecting || !this._wallet) throw new WalletNotConnectedError().name;\n    if (!(this._wallet.readyState === \"Loadable\" /* Loadable */ || this._wallet.readyState === \"Installed\" /* Installed */)) throw new WalletNotReadyError().name;\n    return true;\n  }\n  clearData() {\n    this._connected = false;\n    this.setWallet(null);\n    this.setAccount(null);\n    this.setNetwork(null);\n    removeLocalStorage();\n  }\n  async setAnsName() {\n    var _a;\n    if (((_a = this._network) == null ? void 0 : _a.chainId) && this._account) {\n      if (!ChainIdToAnsSupportedNetworkMap[this._network.chainId] || !isAptosNetwork(this._network)) {\n        this._account.ansName = void 0;\n        return;\n      }\n      const aptosConfig = new AptosConfig3({\n        network: convertNetwork(this._network)\n      });\n      const aptos = new Aptos3(aptosConfig);\n      const name = await aptos.ans.getPrimaryName({\n        address: this._account.address\n      });\n      this._account.ansName = name;\n    }\n  }\n  setWallet(wallet) {\n    this._wallet = wallet;\n  }\n  setAccount(account) {\n    var _a;\n    if (account === null) {\n      this._account = null;\n      return;\n    }\n    if ((_a = this._wallet) == null ? void 0 : _a.isAIP62Standard) {\n      if (\"status\" in account) {\n        const connectStandardAccount = account;\n        if (connectStandardAccount.status === UserResponseStatus2.REJECTED) {\n          this._connecting = false;\n          throw new WalletConnectionError(\"User has rejected the request\").message;\n        }\n        this._account = {\n          address: connectStandardAccount.args.address.toString(),\n          publicKey: connectStandardAccount.args.publicKey.toString(),\n          ansName: connectStandardAccount.args.ansName\n        };\n        return;\n      } else {\n        const standardAccount = account;\n        this._account = {\n          address: standardAccount.address.toString(),\n          publicKey: standardAccount.publicKey.toString(),\n          ansName: standardAccount.ansName\n        };\n        return;\n      }\n    }\n    this._account = {\n      ...account\n    };\n    return;\n  }\n  setNetwork(network) {\n    var _a, _b, _c;\n    if (network === null) {\n      this._network = null;\n      return;\n    }\n    if ((_a = this._wallet) == null ? void 0 : _a.isAIP62Standard) {\n      const standardizeNetwork = network;\n      this.recordEvent(\"network_change\", {\n        from: (_b = this._network) == null ? void 0 : _b.name,\n        to: standardizeNetwork.name\n      });\n      this._network = {\n        name: standardizeNetwork.name.toLowerCase(),\n        chainId: standardizeNetwork.chainId.toString(),\n        url: standardizeNetwork.url\n      };\n      return;\n    }\n    this.recordEvent(\"network_change\", {\n      from: (_c = this._network) == null ? void 0 : _c.name,\n      to: network.name\n    });\n    this._network = {\n      ...network,\n      name: network.name.toLowerCase()\n    };\n  }\n  isConnected() {\n    return this._connected;\n  }\n  get wallets() {\n    return this._all_wallets;\n  }\n  get pluginWallets() {\n    return this._wallets;\n  }\n  get standardWallets() {\n    return this._standard_wallets;\n  }\n  get wallet() {\n    try {\n      if (!this._wallet) return null;\n      return {\n        name: this._wallet.name,\n        icon: this._wallet.icon,\n        url: this._wallet.url\n      };\n    } catch (error) {\n      throw new WalletNotSelectedError(error).message;\n    }\n  }\n  get account() {\n    try {\n      return this._account;\n    } catch (error) {\n      throw new WalletAccountError(error).message;\n    }\n  }\n  get network() {\n    try {\n      return this._network;\n    } catch (error) {\n      throw new WalletGetNetworkError(error).message;\n    }\n  }\n  async connect(walletName) {\n    var _a;\n    const allDetectedWallets = this._all_wallets;\n    const selectedWallet = allDetectedWallets.find(wallet => wallet.name === walletName);\n    if (!selectedWallet) return;\n    if (this._connected) {\n      if (((_a = this._wallet) == null ? void 0 : _a.name) === walletName) throw new WalletConnectionError(`${walletName} wallet is already connected`).message;\n    }\n    if (isRedirectable() && selectedWallet.readyState !== \"Installed\" /* Installed */) {\n      if (selectedWallet.isAIP62Standard && selectedWallet.openInMobileApp) {\n        selectedWallet.openInMobileApp();\n        return;\n      }\n      if (selectedWallet.deeplinkProvider) {\n        const url = encodeURIComponent(window.location.href);\n        const location = selectedWallet.deeplinkProvider({\n          url\n        });\n        window.location.href = location;\n      }\n      return;\n    }\n    if (selectedWallet.readyState !== \"Installed\" /* Installed */ && selectedWallet.readyState !== \"Loadable\" /* Loadable */) {\n      return;\n    }\n    await this.connectWallet(selectedWallet);\n  }\n  async connectWallet(selectedWallet) {\n    try {\n      this._connecting = true;\n      this.setWallet(selectedWallet);\n      let account;\n      if (selectedWallet.isAIP62Standard) {\n        account = await this.walletStandardCore.connect(selectedWallet);\n      } else {\n        account = await this.walletCoreV1.connect(selectedWallet);\n      }\n      this.setAccount(account);\n      const network = await selectedWallet.network();\n      this.setNetwork(network);\n      await this.setAnsName();\n      setLocalStorage(selectedWallet.name);\n      this._connected = true;\n      this.recordEvent(\"wallet_connect\");\n      this.emit(\"connect\", account);\n    } catch (error) {\n      this.clearData();\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletConnectionError(errMsg).message;\n    } finally {\n      this._connecting = false;\n    }\n  }\n  async disconnect() {\n    try {\n      this.ensureWalletExists(this._wallet);\n      await this._wallet.disconnect();\n      this.clearData();\n      this.recordEvent(\"wallet_disconnect\");\n      this.emit(\"disconnect\");\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletDisconnectionError(errMsg).message;\n    }\n  }\n  async signAndSubmitTransaction(transactionInput) {\n    try {\n      if (\"function\" in transactionInput.data) {\n        if (transactionInput.data.function === \"0x1::account::rotate_authentication_key_call\") {\n          throw new WalletSignAndSubmitMessageError(\"SCAM SITE DETECTED\").message;\n        }\n      }\n      this.ensureWalletExists(this._wallet);\n      this.ensureAccountExists(this._account);\n      this.recordEvent(\"sign_and_submit_transaction\");\n      const payloadData = transactionInput.data;\n      const aptosConfig = getAptosConfig(this._network);\n      const aptos = new Aptos3(aptosConfig);\n      if (this._wallet.signAndSubmitTransaction) {\n        if (this._wallet.isAIP62Standard) {\n          const {\n            hash,\n            ...output\n          } = await this.walletStandardCore.signAndSubmitTransaction(transactionInput, aptos, this._account, this._wallet);\n          return {\n            hash,\n            output\n          };\n        } else {\n          const {\n            hash,\n            ...output\n          } = await this.walletCoreV1.resolveSignAndSubmitTransaction(payloadData, this._network, this._wallet, transactionInput);\n          return {\n            hash,\n            output\n          };\n        }\n      }\n      const transaction = await aptos.transaction.build.simple({\n        sender: this._account.address,\n        data: transactionInput.data,\n        options: transactionInput.options\n      });\n      const senderAuthenticator = await this.signTransaction(transaction);\n      const response = await this.submitTransaction({\n        transaction,\n        senderAuthenticator\n      });\n      return response;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n  async signTransaction(transactionOrPayload, asFeePayer, options) {\n    var _a, _b, _c, _d;\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.recordEvent(\"sign_transaction\");\n      if (this._wallet.signTransaction) {\n        if (this._wallet.isAIP62Standard) {\n          if (\"rawTransaction\" in transactionOrPayload) {\n            return await this.walletStandardCore.signTransaction(transactionOrPayload, this._wallet, asFeePayer);\n          } else {\n            const aptosConfig = getAptosConfig(this._network);\n            this.ensureAccountExists(this._account);\n            const sender = this._account.address;\n            const payload = await generateTransactionPayloadFromV1Input(aptosConfig, transactionOrPayload);\n            const optionsV1 = options;\n            const optionsV2 = {\n              accountSequenceNumber: optionsV1 == null ? void 0 : optionsV1.sequenceNumber,\n              expireTimestamp: (_a = optionsV1 == null ? void 0 : optionsV1.expireTimestamp) != null ? _a : optionsV1 == null ? void 0 : optionsV1.expirationTimestamp,\n              gasUnitPrice: (_b = optionsV1 == null ? void 0 : optionsV1.gasUnitPrice) != null ? _b : optionsV1 == null ? void 0 : optionsV1.gas_unit_price,\n              maxGasAmount: (_c = optionsV1 == null ? void 0 : optionsV1.maxGasAmount) != null ? _c : optionsV1 == null ? void 0 : optionsV1.max_gas_amount\n            };\n            const rawTransaction = await generateRawTransaction({\n              aptosConfig,\n              payload,\n              sender,\n              options: optionsV2\n            });\n            return await this.walletStandardCore.signTransaction(new SimpleTransaction(rawTransaction), this._wallet, false);\n          }\n        }\n        if (\"rawTransaction\" in transactionOrPayload) {\n          const accountAuthenticator = await this._wallet.signTransaction(transactionOrPayload, asFeePayer);\n          return accountAuthenticator;\n        } else {\n          const response = await this.walletCoreV1.signTransaction(transactionOrPayload, this._wallet, {\n            max_gas_amount: (options == null ? void 0 : options.maxGasAmount) ? BigInt(options == null ? void 0 : options.maxGasAmount) : void 0,\n            gas_unit_price: (options == null ? void 0 : options.gasUnitPrice) ? BigInt(options == null ? void 0 : options.gasUnitPrice) : void 0\n          });\n          if (!response) {\n            throw new Error(\"error\");\n          }\n          const deserializer1 = new BCS2.Deserializer(response);\n          const deserializedSignature = TxnBuilderTypes4.SignedTransaction.deserialize(deserializer1);\n          const transactionAuthenticator = deserializedSignature.authenticator;\n          const publicKey = transactionAuthenticator.public_key.value;\n          const signature = transactionAuthenticator.signature.value;\n          const accountAuthenticator = new AccountAuthenticatorEd25519(new Ed25519PublicKey(publicKey), new Ed25519Signature(signature));\n          return accountAuthenticator;\n        }\n      }\n      throw new WalletNotSupportedMethod(`Sign Transaction is not supported by ${(_d = this.wallet) == null ? void 0 : _d.name}`).message;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignTransactionError(errMsg).message;\n    }\n  }\n  async signMessage(message) {\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.recordEvent(\"sign_message\");\n      if (this._wallet.isAIP62Standard) {\n        return await this.walletStandardCore.signMessage(message, this._wallet);\n      }\n      const response = await this._wallet.signMessage(message);\n      return response;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageError(errMsg).message;\n    }\n  }\n  async submitTransaction(transaction) {\n    try {\n      this.ensureWalletExists(this._wallet);\n      const {\n        additionalSignersAuthenticators\n      } = transaction;\n      const transactionType = additionalSignersAuthenticators !== void 0 ? \"multi-agent\" : \"simple\";\n      this.recordEvent(\"submit_transaction\", {\n        transaction_type: transactionType\n      });\n      if (this._wallet.submitTransaction) {\n        const pendingTransaction = await this._wallet.submitTransaction(transaction);\n        return pendingTransaction;\n      }\n      const aptosConfig = new AptosConfig3({\n        network: convertNetwork(this.network)\n      });\n      const aptos = new Aptos3(aptosConfig);\n      if (additionalSignersAuthenticators !== void 0) {\n        const multiAgentTxn = {\n          ...transaction,\n          additionalSignersAuthenticators\n        };\n        return aptos.transaction.submit.multiAgent(multiAgentTxn);\n      } else {\n        return aptos.transaction.submit.simple(transaction);\n      }\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignTransactionError(errMsg).message;\n    }\n  }\n  async onAccountChange() {\n    try {\n      this.ensureWalletExists(this._wallet);\n      await this._wallet.onAccountChange(async data => {\n        this.setAccount(data);\n        await this.setAnsName();\n        this.recordEvent(\"account_change\");\n        this.emit(\"accountChange\", this._account);\n      });\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletAccountChangeError(errMsg).message;\n    }\n  }\n  async onNetworkChange() {\n    try {\n      this.ensureWalletExists(this._wallet);\n      await this._wallet.onNetworkChange(async data => {\n        this.setNetwork(data);\n        await this.setAnsName();\n        this.emit(\"networkChange\", this._network);\n      });\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletNetworkChangeError(errMsg).message;\n    }\n  }\n  async changeNetwork(network) {\n    var _a;\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.recordEvent(\"change_network_request\", {\n        from: (_a = this._network) == null ? void 0 : _a.name,\n        to: network\n      });\n      const chainId = network === Network3.DEVNET ? await fetchDevnetChainId() : NetworkToChainId[network];\n      if (this._wallet.changeNetwork) {\n        const networkInfo = {\n          name: network,\n          chainId\n        };\n        const response = await this._wallet.changeNetwork(networkInfo);\n        if (response.status === UserResponseStatus2.REJECTED) {\n          throw new WalletConnectionError(\"User has rejected the request\").message;\n        }\n        return response.args;\n      }\n      throw new WalletChangeNetworkError(`${this._wallet.name} does not support changing network request`).message;\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletChangeNetworkError(errMsg).message;\n    }\n  }\n  async signMessageAndVerify(message) {\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.ensureAccountExists(this._account);\n      this.recordEvent(\"sign_message_and_verify\");\n      if (this._wallet.isAIP62Standard) {\n        return this.walletStandardCore.signMessageAndVerify(message, this._wallet);\n      }\n      return await this.walletCoreV1.signMessageAndVerify(message, this._wallet, this._account);\n    } catch (error) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageAndVerifyError(errMsg).message;\n    }\n  }\n};\n\n// src/index.ts\nif (typeof window !== \"undefined\") {\n  window.WALLET_ADAPTER_CORE_VERSION = WALLET_ADAPTER_CORE_VERSION;\n}\nexport { ChainIdToAnsSupportedNetworkMap, NetworkName, TxnBuilderTypes3 as TxnBuilderTypes, Types3 as Types, WalletCore, WalletCoreV1, WalletReadyState, WalletStandardCore, areBCSArguments, convertNetwork, convertV2PayloadToV1JSONPayload, convertV2TransactionPayloadToV1BCSPayload, fetchDevnetChainId, generalizedErrorMessage, generateTransactionPayloadFromV1Input, getAptosConfig, getLocalStorage, isAptosNetwork, isInAppBrowser, isMobile, isRedirectable, removeLocalStorage, scopePollingDetectionStrategy, setLocalStorage };", "map": {"version": 3, "names": ["WALLET_ADAPTER_CORE_VERSION", "TxnBuilderTypes", "TxnBuilderTypes4", "BCS", "BCS2", "Network", "Network3", "AccountAuthenticatorEd25519", "Ed25519PublicKey", "Ed25519Signature", "AptosConfig", "AptosConfig3", "Aptos", "Aptos3", "generateRawTransaction", "SimpleTransaction", "NetworkToChainId", "EventEmitter2", "getAptosWallets", "UserResponseStatus", "UserResponseStatus2", "isWalletWithRequiredFeatureSet", "TWallet", "sdkWallets", "push", "sdkWallets_default", "WalletReadyState", "WalletReadyState2", "NetworkName", "NetworkName2", "ChainIdToAnsSupportedNetworkMap", "WalletError", "Error", "constructor", "message", "error", "WalletNotSelectedError", "arguments", "name", "WalletNotReadyError", "WalletConnectionError", "WalletDisconnectionError", "WalletAccountError", "WalletGetNetworkError", "WalletAccountChangeError", "WalletNetworkChangeError", "WalletNotConnectedError", "WalletSignMessageError", "WalletSignMessageAndVerifyError", "WalletSignAndSubmitMessageError", "WalletSignTransactionError", "WalletNotSupportedMethod", "WalletChangeNetworkError", "HexString", "EventEmitter", "<PERSON><PERSON><PERSON>", "generateTransactionPayload", "generateTransactionPayload2", "nacl", "TypeTag", "convertNetwork", "networkInfo", "MAINNET", "TESTNET", "DEVNET", "convertV2TransactionPayloadToV1BCSPayload", "payload", "deserializer", "Deserializer", "bcsToBytes", "TransactionPayload", "deserialize", "convertV2PayloadToV1JSONPayload", "_a", "_b", "stringTypeTags", "typeArguments", "map", "typeTag", "toString", "newPayload", "type", "multisig_address", "multisigAddress", "function", "type_arguments", "functionArguments", "generateTransactionPayloadFromV1Input", "aptosConfig", "inputV1", "inputV2", "scopePollingDetectionStrategy", "detect", "window", "document", "disposers", "detectAndDispose", "detected", "dispose", "interval", "setInterval", "clearInterval", "readyState", "addEventListener", "once", "removeEventListener", "LOCAL_STORAGE_ITEM_KEY", "setLocalStorage", "walletName", "localStorage", "setItem", "removeLocalStorage", "removeItem", "getLocalStorage", "getItem", "AptosConfig2", "Network2", "NetworkToNodeAPI", "Serializable", "isMobile", "test", "navigator", "userAgent", "isInAppBrowser", "isIphone", "isAndroid", "isRedirectable", "generalizedErrorMessage", "areBCSArguments", "args", "length", "every", "arg", "getAptosConfig", "isAptosNetwork", "network", "CUSTOM", "fullnode", "url", "fetchDevnetChainId", "aptos", "get<PERSON>hainId", "WalletCoreV1", "connect", "wallet", "account", "resolveSignAndSubmitTransaction", "payloadData", "transactionInput", "_c", "_d", "_e", "_f", "_g", "_h", "oldTransactionPayload2", "signAndSubmitBCSTransaction", "max_gas_amount", "options", "maxGasAmount", "BigInt", "gas_unit_price", "gasUnitPrice", "oldTransactionPayload", "signAndSubmitTransaction", "transaction", "response", "errMsg", "signTransaction", "signMessageAndVerify", "signMessage", "console", "log", "verified", "Array", "isArray", "signature", "fullMessage", "bitmap", "minKeysRequired", "bits", "from", "flatMap", "n", "_", "i", "index", "filter", "publicKeys", "public<PERSON>ey", "matchedPublicKeys", "includes", "isSigVerified", "sign", "detached", "verify", "currentAccountPublicKey", "noPrefix", "TxnBuilderTypes3", "Types", "Types3", "MultiEd25519Signature", "MultiEd25519PublicKey", "WalletStandardCore", "status", "REJECTED", "build", "simple", "sender", "address", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "signatures", "verifySignature", "TextEncoder", "encode", "GA4", "aptosGAID", "injectGA", "gtag", "a", "b", "c", "dataLayer", "gaID", "head", "getElementsByTagName", "myScript", "createElement", "setAttribute", "that", "onload", "Date", "send_page_view", "insertBefore", "children", "aptosStandardSupportedWalletList", "icon", "isAIP62Standard", "WalletCore", "plugins", "optInWallets", "_wallets", "_optInWallets", "_standard_wallets", "_all_wallets", "_wallet", "_account", "_network", "walletCoreV1", "walletStandardCore", "_connecting", "_connected", "ga4", "standardizeStandardWalletToPluginWalletType", "standardWallet", "standardWalletConvertedToWallet", "provider", "features", "disconnect", "onAccountChange", "onNetworkChange", "openInMobileApp", "changeNetwork", "item", "emit", "fetchAptosWallets", "for<PERSON>ach", "providerName", "toLowerCase", "Object", "keys", "aptosWallets", "on", "setWallets", "removeRegisterListener", "aptosWallets2", "removeUnregisterListener", "appendNotDetectedStandardSupportedWallets", "aptosStandardWallets", "supportedWallet", "excludeWallet", "existingWalletIndex", "findIndex", "extensionwWallets", "<PERSON><PERSON><PERSON><PERSON>", "recordEvent", "eventName", "additionalInfo", "network_url", "adapter_core_version", "send_to", "ensureWalletExists", "ensureAccountExists", "doesWalletExist", "clearData", "setWallet", "setAccount", "setNetwork", "setAnsName", "chainId", "ans<PERSON>ame", "ans", "getPrimaryName", "connectStandardAccount", "standardAccount", "standardizeNetwork", "to", "isConnected", "wallets", "pluginWallets", "standardWallets", "allDetectedWallets", "<PERSON><PERSON><PERSON><PERSON>", "find", "deeplinkProvider", "encodeURIComponent", "location", "href", "connectWallet", "hash", "output", "senderAuthenticator", "submitTransaction", "transactionOrPayload", "optionsV1", "optionsV2", "accountSequenceNumber", "sequenceNumber", "expireTimestamp", "expirationTimestamp", "rawTransaction", "accountAuthenticator", "deserializer1", "deserializedSignature", "SignedTransaction", "transactionAuthenticator", "authenticator", "public_key", "value", "additionalSignersAuthenticators", "transactionType", "transaction_type", "pendingTransaction", "multiAgentTxn", "submit", "multiAgent"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\version.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\WalletCore.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\AIP62StandardWallets\\sdkWallets.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\constants.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\error\\index.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\LegacyWalletPlugins\\WalletCoreV1.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\LegacyWalletPlugins\\conversion.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\utils\\scopePollingDetectionStrategy.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\utils\\localStorage.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\utils\\helpers.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\LegacyWalletPlugins\\types.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\AIP62StandardWallets\\WalletStandard.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\ga\\index.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\AIP62StandardWallets\\registry.ts", "C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-adapter-core\\src\\index.ts"], "sourcesContent": ["export const WALLET_ADAPTER_CORE_VERSION = \"3.16.0\";\n", "import { TxnBuilderTypes, Types, BCS } from \"aptos\";\nimport {\n  Network,\n  AnyRawTransaction,\n  AccountAuthenticator,\n  AccountAuthenticatorEd25519,\n  Ed25519PublicKey,\n  InputGenerateTransactionOptions,\n  Ed25519Signature,\n  AptosConfig,\n  InputSubmitTransactionData,\n  PendingTransactionResponse,\n  Aptos,\n  generateRawTransaction,\n  SimpleTransaction,\n  NetworkToChainId,\n} from \"@aptos-labs/ts-sdk\";\nimport EventEmitter from \"eventemitter3\";\nimport {\n  AccountInfo as StandardAccountInfo,\n  AptosChangeNetworkOutput,\n  AptosWallet,\n  getAptosWallets,\n  NetworkInfo as StandardNetworkInfo,\n  UserResponse,\n  UserResponseStatus,\n  isWalletWithRequiredFeatureSet,\n} from \"@aptos-labs/wallet-standard\";\n\nimport SDKWallets from \"./AIP62StandardWallets/sdkWallets\";\nimport { ChainIdToAnsSupportedNetworkMap, WalletReadyState } from \"./constants\";\nimport {\n  WalletAccountChangeError,\n  WalletAccountError,\n  WalletChangeNetworkError,\n  WalletConnectionError,\n  WalletDisconnectionError,\n  WalletGetNetworkError,\n  WalletNetworkChangeError,\n  WalletNotConnectedError,\n  WalletNotReadyError,\n  WalletNotSelectedError,\n  WalletNotSupportedMethod,\n  WalletSignAndSubmitMessageError,\n  WalletSignMessageAndVerifyError,\n  WalletSignMessageError,\n  WalletSignTransactionError,\n} from \"./error\";\nimport {\n  AccountInfo,\n  InputTransactionData,\n  NetworkInfo,\n  SignMessagePayload,\n  SignMessageResponse,\n  Wallet,\n  WalletCoreEvents,\n  WalletInfo,\n  WalletName,\n  WalletCoreV1,\n  CompatibleTransactionOptions,\n  convertNetwork,\n  generateTransactionPayloadFromV1Input,\n} from \"./LegacyWalletPlugins\";\nimport {\n  fetchDevnetChainId,\n  generalizedErrorMessage,\n  getAptosConfig,\n  isAptosNetwork,\n  isRedirectable,\n  removeLocalStorage,\n  scopePollingDetectionStrategy,\n  setLocalStorage,\n} from \"./utils\";\nimport {\n  AptosStandardWallet,\n  WalletStandardCore,\n  AptosStandardSupportedWallet,\n  AvailableWallets,\n} from \"./AIP62StandardWallets\";\nimport { GA4 } from \"./ga\";\nimport { WALLET_ADAPTER_CORE_VERSION } from \"./version\";\nimport { aptosStandardSupportedWalletList } from \"./AIP62StandardWallets/registry\";\n\nexport type IAptosWallet = AptosStandardWallet & Wallet;\n\nexport class WalletCore extends EventEmitter<WalletCoreEvents> {\n  // Private array to hold legacy wallet adapter plugins\n  private _wallets: ReadonlyArray<Wallet> = [];\n\n  // Private array that holds all the Wallets a dapp decided to opt-in to\n  private _optInWallets: ReadonlyArray<AvailableWallets> = [];\n\n  // Private array to hold compatible AIP-62 standard wallets\n  private _standard_wallets: ReadonlyArray<AptosStandardWallet> = [];\n\n  // Private array to hold all wallets (legacy wallet adapter plugins AND compatible AIP-62 standard wallets)\n  // while providing support for legacy and new wallet standard\n  private _all_wallets: Array<Wallet | AptosStandardSupportedWallet> = [];\n\n  // Current connected wallet\n  private _wallet: Wallet | null = null;\n\n  // Current connected account\n  private _account: AccountInfo | null = null;\n\n  // Current connected network\n  private _network: NetworkInfo | null = null;\n\n  // WalletCoreV1 property to interact with wallet adapter v1 (legacy wallet adapter plugins) functionality\n  private readonly walletCoreV1: WalletCoreV1 = new WalletCoreV1();\n\n  // WalletStandardCore property to interact with wallet adapter v2 (compatible AIP-62 standard wallets) functionality\n  private readonly walletStandardCore: WalletStandardCore =\n    new WalletStandardCore();\n\n  // Indicates whether the dapp is currently connecting with a wallet\n  private _connecting: boolean = false;\n\n  // Indicates whether the dapp is connected with a wallet\n  private _connected: boolean = false;\n\n  // Google Analytics 4 module\n  private readonly ga4: GA4 = new GA4();\n\n  /**\n   * Core functionality constructor.\n   * For legacy wallet adapter v1 support we expect the dapp to pass in wallet plugins,\n   * since AIP-62 standard support this is optional for dapps.\n   *\n   * @param plugins legacy wallet adapter v1 wallet plugins\n   */\n  constructor(\n    plugins: ReadonlyArray<Wallet>,\n    optInWallets: ReadonlyArray<AvailableWallets>\n  ) {\n    super();\n    this._wallets = plugins;\n    this._optInWallets = optInWallets;\n    // Strategy to detect legacy wallet adapter v1 wallet plugins\n    this.scopePollingDetectionStrategy();\n    // Strategy to detect AIP-62 standard compatible wallets (extension + SDK wallets)\n    this.fetchAptosWallets();\n  }\n\n  private scopePollingDetectionStrategy() {\n    this._wallets?.forEach((wallet: Wallet) => {\n      this._all_wallets.push(wallet);\n      if (!wallet.readyState) {\n        wallet.readyState =\n          typeof window === \"undefined\" || typeof document === \"undefined\"\n            ? WalletReadyState.Unsupported\n            : WalletReadyState.NotDetected;\n      }\n      if (typeof window !== \"undefined\") {\n        scopePollingDetectionStrategy(() => {\n          const providerName = wallet.providerName || wallet.name.toLowerCase();\n          if (Object.keys(window).includes(providerName)) {\n            wallet.readyState = WalletReadyState.Installed;\n            wallet.provider = window[providerName as any];\n            this.emit(\"readyStateChange\", wallet);\n            return true;\n          }\n          return false;\n        });\n      }\n    });\n  }\n\n  private fetchAptosWallets() {\n    let { aptosWallets, on } = getAptosWallets();\n    this.setWallets(aptosWallets);\n\n    if (typeof window === \"undefined\") return;\n    // Adds an event listener for new wallets that get registered after the dapp has been loaded,\n    // receiving an unsubscribe function, which it can later use to remove the listener\n    const that = this;\n    const removeRegisterListener = on(\"register\", function () {\n      let { aptosWallets } = getAptosWallets();\n      that.setWallets(aptosWallets);\n    });\n\n    const removeUnregisterListener = on(\"unregister\", function () {\n      let { aptosWallets } = getAptosWallets();\n      that.setWallets(aptosWallets);\n    });\n  }\n\n  // Append wallets from wallet standard support registry to the `all_wallets` array\n  private appendNotDetectedStandardSupportedWallets(\n    aptosStandardWallets: ReadonlyArray<AptosStandardWallet>\n  ) {\n    aptosStandardSupportedWalletList.map((supportedWallet) => {\n      if (this.excludeWallet(supportedWallet.name)) {\n        return;\n      }\n      const existingWalletIndex = aptosStandardWallets.findIndex(\n        (wallet) => wallet.name == supportedWallet.name\n      );\n\n      // If wallet does not exist, append it from the supported wallets list\n      if (existingWalletIndex === -1) {\n        this._all_wallets.push(supportedWallet);\n        this.emit(\"standardWalletsAdded\", supportedWallet);\n      }\n    });\n  }\n\n  /**\n   * Set potential Standard compatible SDK + extension wallets\n   *\n   * Loop over local SDK and Extensions wallets\n   * 1) check it is Standard compatible\n   * 2) Update their readyState to Installed (for a future UI detection)\n   * 3) push the wallet into a local wallets array\n   * 4) standardize each wallet to the Wallet Plugin type interface for legacy compatibility\n   *\n   * @param extensionwWallets\n   */\n  private setWallets(extensionwWallets: readonly AptosWallet[]) {\n    const aptosStandardWallets: AptosStandardWallet[] = [];\n\n    [...SDKWallets, ...extensionwWallets].map((wallet: AptosStandardWallet) => {\n      if (this.excludeWallet(wallet.name)) {\n        return;\n      }\n      const isValid = isWalletWithRequiredFeatureSet(wallet);\n      if (isValid) {\n        wallet.readyState = WalletReadyState.Installed;\n        aptosStandardWallets.push(wallet);\n        this.standardizeStandardWalletToPluginWalletType(wallet);\n      }\n    });\n\n    this._standard_wallets = aptosStandardWallets;\n    // Append AIP-62 compatible wallets that are not detected on the user machine\n    this.appendNotDetectedStandardSupportedWallets(this._standard_wallets);\n  }\n\n  /**\n   * A function that excludes a wallet the dapp doesnt want to include\n   *\n   * @param walletName\n   * @returns\n   */\n  excludeWallet(walletName: string): boolean {\n    // If _optInWallets is not empty, and does not include the provided wallet,\n    // return true to exclude the wallet, otherwise return false\n    if (\n      this._optInWallets.length > 0 &&\n      !this._optInWallets.includes(walletName as AvailableWallets)\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * To maintain support for both plugins and AIP-62 standard wallets,\n   * without introducing dapps breaking changes, we convert\n   * AIP-62 standard compatible wallets to the legacy adapter wallet plugin type.\n   *\n   * @param standardWallet An AIP-62 standard compatible wallet\n   */\n  private standardizeStandardWalletToPluginWalletType = (\n    standardWallet: AptosStandardWallet\n  ) => {\n    let standardWalletConvertedToWallet: Wallet = {\n      name: standardWallet.name as WalletName,\n      url: standardWallet.url,\n      icon: standardWallet.icon,\n      provider: standardWallet,\n      connect: standardWallet.features[\"aptos:connect\"].connect,\n      disconnect: standardWallet.features[\"aptos:disconnect\"].disconnect,\n      network: standardWallet.features[\"aptos:network\"].network,\n      account: standardWallet.features[\"aptos:account\"].account,\n      signAndSubmitTransaction:\n        standardWallet.features[\"aptos:signAndSubmitTransaction\"]\n          ?.signAndSubmitTransaction,\n      signMessage: standardWallet.features[\"aptos:signMessage\"].signMessage,\n      onAccountChange:\n        standardWallet.features[\"aptos:onAccountChange\"].onAccountChange,\n      onNetworkChange:\n        standardWallet.features[\"aptos:onNetworkChange\"].onNetworkChange,\n      signTransaction:\n        standardWallet.features[\"aptos:signTransaction\"].signTransaction,\n      openInMobileApp:\n        standardWallet.features[\"aptos:openInMobileApp\"]?.openInMobileApp,\n      changeNetwork:\n        standardWallet.features[\"aptos:changeNetwork\"]?.changeNetwork,\n      readyState: WalletReadyState.Installed,\n      isAIP62Standard: true,\n    };\n\n    // Remove optional duplications in the _all_wallets array\n    this._all_wallets = this._all_wallets.filter(\n      (item) => item.name !== standardWalletConvertedToWallet.name\n    );\n    this._all_wallets.push(standardWalletConvertedToWallet);\n\n    this.emit(\"standardWalletsAdded\", standardWalletConvertedToWallet);\n  };\n\n  private recordEvent(eventName: string, additionalInfo?: object) {\n    this.ga4.gtag(\"event\", `wallet_adapter_${eventName}`, {\n      wallet: this._wallet?.name,\n      network: this._network?.name,\n      network_url: this._network?.url,\n      adapter_core_version: WALLET_ADAPTER_CORE_VERSION,\n      send_to: process.env.GAID,\n      ...additionalInfo,\n    });\n  }\n\n  /**\n   * Helper function to ensure wallet exists\n   *\n   * @param wallet A wallet\n   */\n  private ensureWalletExists(wallet: Wallet | null): asserts wallet is Wallet {\n    if (!wallet) {\n      throw new WalletNotConnectedError().name;\n    }\n    if (\n      !(\n        wallet.readyState === WalletReadyState.Loadable ||\n        wallet.readyState === WalletReadyState.Installed\n      )\n    )\n      throw new WalletNotReadyError(\"Wallet is not set\").name;\n  }\n\n  /**\n   * Helper function to ensure account exists\n   *\n   * @param account An account\n   */\n  private ensureAccountExists(\n    account: AccountInfo | null\n  ): asserts account is AccountInfo {\n    if (!account) {\n      throw new WalletAccountError(\"Account is not set\").name;\n    }\n  }\n\n  /**\n   * @deprecated use ensureWalletExists\n   */\n  private doesWalletExist(): boolean | WalletNotConnectedError {\n    if (!this._connected || this._connecting || !this._wallet)\n      throw new WalletNotConnectedError().name;\n    if (\n      !(\n        this._wallet.readyState === WalletReadyState.Loadable ||\n        this._wallet.readyState === WalletReadyState.Installed\n      )\n    )\n      throw new WalletNotReadyError().name;\n    return true;\n  }\n\n  /**\n   * Function to cleat wallet adapter data.\n   *\n   * - Removes current connected wallet state\n   * - Removes current connected account state\n   * - Removes current connected network state\n   * - Removes autoconnect local storage value\n   */\n  private clearData(): void {\n    this._connected = false;\n    this.setWallet(null);\n    this.setAccount(null);\n    this.setNetwork(null);\n    removeLocalStorage();\n  }\n\n  /**\n   * Queries and sets ANS name for the current connected wallet account\n   */\n  private async setAnsName(): Promise<void> {\n    if (this._network?.chainId && this._account) {\n      // ANS supports only MAINNET or TESTNET\n      if (\n        !ChainIdToAnsSupportedNetworkMap[this._network.chainId] ||\n        !isAptosNetwork(this._network)\n      ) {\n        this._account.ansName = undefined;\n        return;\n      }\n\n      const aptosConfig = new AptosConfig({\n        network: convertNetwork(this._network),\n      });\n      const aptos = new Aptos(aptosConfig);\n      const name = await aptos.ans.getPrimaryName({\n        address: this._account.address,\n      });\n\n      this._account.ansName = name;\n    }\n  }\n\n  /**\n   * Sets the connected wallet\n   *\n   * @param wallet A wallet\n   */\n  setWallet(wallet: Wallet | null): void {\n    this._wallet = wallet;\n  }\n\n  /**\n   * Sets the connected account\n   *\n   * `AccountInfo` type comes from a legacy wallet adapter plugin\n   * `StandardAccountInfo` type comes from AIP-62 standard compatible wallet when onAccountChange event is called\n   * `UserResponse<StandardAccountInfo>` type comes from AIP-62 standard compatible wallet on wallet connect\n   *\n   * @param account An account\n   */\n  setAccount(\n    account:\n      | AccountInfo\n      | StandardAccountInfo\n      | UserResponse<StandardAccountInfo>\n      | null\n  ): void {\n    if (account === null) {\n      this._account = null;\n      return;\n    }\n\n    // Check if wallet is of type AIP-62 standard\n    if (this._wallet?.isAIP62Standard) {\n      // Check if account is of type UserResponse<StandardAccountInfo> which means the `account`\n      // comes from the `connect` method\n      if (\"status\" in account) {\n        const connectStandardAccount =\n          account as UserResponse<StandardAccountInfo>;\n        if (connectStandardAccount.status === UserResponseStatus.REJECTED) {\n          this._connecting = false;\n          throw new WalletConnectionError(\"User has rejected the request\")\n            .message;\n        }\n        // account is of type\n        this._account = {\n          address: connectStandardAccount.args.address.toString(),\n          publicKey: connectStandardAccount.args.publicKey.toString(),\n          ansName: connectStandardAccount.args.ansName,\n        };\n        return;\n      } else {\n        // account is of type `StandardAccountInfo` which means it comes from onAccountChange event\n        const standardAccount = account as StandardAccountInfo;\n        this._account = {\n          address: standardAccount.address.toString(),\n          publicKey: standardAccount.publicKey.toString(),\n          ansName: standardAccount.ansName,\n        };\n        return;\n      }\n    }\n\n    // account is of type `AccountInfo`\n    this._account = { ...(account as AccountInfo) };\n    return;\n  }\n\n  /**\n   * Sets the connected network\n   *\n   * `NetworkInfo` type comes from a legacy wallet adapter plugin\n   * `StandardNetworkInfo` type comes from AIP-62 standard compatible wallet\n   *\n   * @param network A network\n   */\n  setNetwork(network: NetworkInfo | StandardNetworkInfo | null): void {\n    if (network === null) {\n      this._network = null;\n      return;\n    }\n    if (this._wallet?.isAIP62Standard) {\n      const standardizeNetwork = network as StandardNetworkInfo;\n      this.recordEvent(\"network_change\", {\n        from: this._network?.name,\n        to: standardizeNetwork.name,\n      });\n      this._network = {\n        name: standardizeNetwork.name.toLowerCase() as Network,\n        chainId: standardizeNetwork.chainId.toString(),\n        url: standardizeNetwork.url,\n      };\n\n      return;\n    }\n\n    this.recordEvent(\"network_change\", {\n      from: this._network?.name,\n      to: network.name,\n    });\n    this._network = {\n      ...(network as NetworkInfo),\n      name: network.name.toLowerCase() as Network,\n    };\n  }\n\n  /**\n   * Helper function to detect whether a wallet is connected\n   *\n   * @returns boolean\n   */\n  isConnected(): boolean {\n    return this._connected;\n  }\n\n  /**\n   * Getter to fetch all detected wallets\n   */\n  get wallets(): ReadonlyArray<Wallet | AptosStandardSupportedWallet> {\n    return this._all_wallets;\n  }\n\n  /**\n   * Getter to fetch all detected plugin wallets\n   */\n  get pluginWallets(): ReadonlyArray<Wallet> {\n    return this._wallets;\n  }\n\n  /**\n   * Getter to fetch all detected AIP-62 standard compatible wallets\n   */\n  get standardWallets(): ReadonlyArray<AptosStandardWallet> {\n    return this._standard_wallets;\n  }\n\n  /**\n   * Getter for the current connected wallet\n   *\n   * @return wallet info\n   * @throws WalletNotSelectedError\n   */\n  get wallet(): WalletInfo | null {\n    try {\n      if (!this._wallet) return null;\n      return {\n        name: this._wallet.name,\n        icon: this._wallet.icon,\n        url: this._wallet.url,\n      };\n    } catch (error: any) {\n      throw new WalletNotSelectedError(error).message;\n    }\n  }\n\n  /**\n   * Getter for the current connected account\n   *\n   * @return account info\n   * @throws WalletAccountError\n   */\n  get account(): AccountInfo | null {\n    try {\n      return this._account;\n    } catch (error: any) {\n      throw new WalletAccountError(error).message;\n    }\n  }\n\n  /**\n   * Getter for the current wallet network\n   *\n   * @return network info\n   * @throws WalletGetNetworkError\n   */\n  get network(): NetworkInfo | null {\n    try {\n      return this._network;\n    } catch (error: any) {\n      throw new WalletGetNetworkError(error).message;\n    }\n  }\n\n  /**\n   * Helper function to run some checks before we connect with a wallet.\n   *\n   * @param walletName. The wallet name we want to connect with.\n   */\n  async connect(walletName: string): Promise<void | string> {\n    // Checks the wallet exists in the detected wallets array\n\n    const allDetectedWallets = this._all_wallets as Array<Wallet>;\n\n    const selectedWallet = allDetectedWallets.find(\n      (wallet: Wallet) => wallet.name === walletName\n    );\n    if (!selectedWallet) return;\n\n    // Check if wallet is already connected\n    if (this._connected) {\n      // if the selected wallet is already connected, we don't need to connect again\n      if (this._wallet?.name === walletName)\n        throw new WalletConnectionError(\n          `${walletName} wallet is already connected`\n        ).message;\n    }\n\n    // Check if we are in a redirectable view (i.e on mobile AND not in an in-app browser)\n    // Ignore if wallet is installed (iOS extension)\n    if (\n      isRedirectable() &&\n      selectedWallet.readyState !== WalletReadyState.Installed\n    ) {\n      // use wallet deep link\n      if (selectedWallet.isAIP62Standard && selectedWallet.openInMobileApp) {\n        selectedWallet.openInMobileApp();\n        return;\n      }\n      if (selectedWallet.deeplinkProvider) {\n        const url = encodeURIComponent(window.location.href);\n        const location = selectedWallet.deeplinkProvider({ url });\n        window.location.href = location;\n      }\n      return;\n    }\n\n    // Check wallet state is Installed or Loadable\n    if (\n      selectedWallet.readyState !== WalletReadyState.Installed &&\n      selectedWallet.readyState !== WalletReadyState.Loadable\n    ) {\n      return;\n    }\n\n    // Now we can connect to the wallet\n    await this.connectWallet(selectedWallet);\n  }\n\n  /**\n   * Connects a wallet to the dapp.\n   * On connect success, we set the current account and the network, and keeping the selected wallet\n   * name in LocalStorage to support autoConnect function.\n   *\n   * @param selectedWallet. The wallet we want to connect.\n   * @emit emits \"connect\" event\n   * @throws WalletConnectionError\n   */\n  async connectWallet(selectedWallet: Wallet): Promise<void> {\n    try {\n      this._connecting = true;\n      this.setWallet(selectedWallet);\n      let account;\n      if (selectedWallet.isAIP62Standard) {\n        account = await this.walletStandardCore.connect(selectedWallet);\n      } else {\n        account = await this.walletCoreV1.connect(selectedWallet);\n      }\n      this.setAccount(account);\n      const network = await selectedWallet.network();\n      this.setNetwork(network);\n      await this.setAnsName();\n      setLocalStorage(selectedWallet.name);\n      this._connected = true;\n      this.recordEvent(\"wallet_connect\");\n      this.emit(\"connect\", account);\n    } catch (error: any) {\n      this.clearData();\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletConnectionError(errMsg).message;\n    } finally {\n      this._connecting = false;\n    }\n  }\n\n  /**\n   * Disconnect the current connected wallet. On success, we clear the\n   * current account, current network and LocalStorage data.\n   *\n   * @emit emits \"disconnect\" event\n   * @throws WalletDisconnectionError\n   */\n  async disconnect(): Promise<void> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      await this._wallet.disconnect();\n      this.clearData();\n      this.recordEvent(\"wallet_disconnect\");\n      this.emit(\"disconnect\");\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletDisconnectionError(errMsg).message;\n    }\n  }\n\n  /**\n   * Signs and submits a transaction to chain\n   *\n   * @param transactionInput InputTransactionData\n   * @param options optional. A configuration object to generate a transaction by\n   * @returns The pending transaction hash (V1 output) | PendingTransactionResponse (V2 output)\n   */\n  async signAndSubmitTransaction(\n    transactionInput: InputTransactionData\n  ): Promise<\n    { hash: Types.HexEncodedBytes; output?: any } | PendingTransactionResponse\n  > {\n    try {\n      if (\"function\" in transactionInput.data) {\n        if (\n          transactionInput.data.function ===\n          \"0x1::account::rotate_authentication_key_call\"\n        ) {\n          throw new WalletSignAndSubmitMessageError(\"SCAM SITE DETECTED\")\n            .message;\n        }\n      }\n\n      this.ensureWalletExists(this._wallet);\n      this.ensureAccountExists(this._account);\n      this.recordEvent(\"sign_and_submit_transaction\");\n      // get the payload piece from the input\n      const payloadData = transactionInput.data;\n      const aptosConfig = getAptosConfig(this._network);\n\n      const aptos = new Aptos(aptosConfig);\n\n      if (this._wallet.signAndSubmitTransaction) {\n        // if wallet is compatible with the AIP-62 standard\n        if (this._wallet.isAIP62Standard) {\n          const { hash, ...output } =\n            await this.walletStandardCore.signAndSubmitTransaction(\n              transactionInput,\n              aptos,\n              this._account,\n              this._wallet\n            );\n          return { hash, output };\n        } else {\n          // Else use wallet plugin\n          const { hash, ...output } =\n            await this.walletCoreV1.resolveSignAndSubmitTransaction(\n              payloadData,\n              this._network,\n              this._wallet,\n              transactionInput\n            );\n          return { hash, output };\n        }\n      }\n\n      // If wallet does not support signAndSubmitTransaction\n      // the adapter will sign and submit it for the dapp.\n      // Note: This should happen only for AIP-62 standard compatible wallets since\n      // signAndSubmitTransaction is not a required function implementation\n      const transaction = await aptos.transaction.build.simple({\n        sender: this._account.address,\n        data: transactionInput.data,\n        options: transactionInput.options,\n      });\n\n      const senderAuthenticator = await this.signTransaction(transaction);\n      const response = await this.submitTransaction({\n        transaction,\n        senderAuthenticator,\n      });\n      return response;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n\n  /**\n   * Signs a transaction\n   *\n   * To support both existing wallet adapter V1 and V2, we support 2 input types\n   *\n   * @param transactionOrPayload AnyRawTransaction - V2 input | Types.TransactionPayload - V1 input\n   * @param options optional. V1 input\n   *\n   * @returns AccountAuthenticator\n   */\n  async signTransaction(\n    transactionOrPayload: AnyRawTransaction | Types.TransactionPayload,\n    asFeePayer?: boolean,\n    options?: InputGenerateTransactionOptions\n  ): Promise<AccountAuthenticator> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.recordEvent(\"sign_transaction\");\n      // Make sure wallet supports signTransaction\n      if (this._wallet.signTransaction) {\n        // If current connected wallet is AIP-62 standard compatible\n        // we want to make sure the transaction input is what the\n        // standard expects, i,e new sdk v2 input\n        if (this._wallet.isAIP62Standard) {\n          // if rawTransaction prop it means transaction input data is\n          // compatible with new sdk v2 input\n          if (\"rawTransaction\" in transactionOrPayload) {\n            return await this.walletStandardCore.signTransaction(\n              transactionOrPayload,\n              this._wallet,\n              asFeePayer\n            );\n          } else {\n            const aptosConfig = getAptosConfig(this._network);\n            this.ensureAccountExists(this._account);\n            const sender = this._account.address;\n            const payload = await generateTransactionPayloadFromV1Input(\n              aptosConfig,\n              transactionOrPayload\n            );\n            const optionsV1 = options as CompatibleTransactionOptions;\n            const optionsV2 = {\n              accountSequenceNumber: optionsV1?.sequenceNumber,\n              expireTimestamp:\n                optionsV1?.expireTimestamp ?? optionsV1?.expirationTimestamp,\n              gasUnitPrice:\n                optionsV1?.gasUnitPrice ?? optionsV1?.gas_unit_price,\n              maxGasAmount:\n                optionsV1?.maxGasAmount ?? optionsV1?.max_gas_amount,\n            };\n            const rawTransaction = await generateRawTransaction({\n              aptosConfig,\n              payload,\n              sender,\n              options: optionsV2,\n            });\n            return await this.walletStandardCore.signTransaction(\n              new SimpleTransaction(rawTransaction),\n              this._wallet,\n              false\n            );\n          }\n        }\n\n        // If current connected wallet is legacy compatible with wallet standard\n\n        // if input is AnyRawTransaction, i.e new sdk v2 input\n        if (\"rawTransaction\" in transactionOrPayload) {\n          const accountAuthenticator = (await this._wallet.signTransaction(\n            transactionOrPayload,\n            asFeePayer\n          )) as AccountAuthenticator;\n\n          return accountAuthenticator;\n        } else {\n          const response = await this.walletCoreV1.signTransaction(\n            transactionOrPayload as Types.TransactionPayload,\n            this._wallet!,\n            {\n              max_gas_amount: options?.maxGasAmount\n                ? BigInt(options?.maxGasAmount)\n                : undefined,\n              gas_unit_price: options?.gasUnitPrice\n                ? BigInt(options?.gasUnitPrice)\n                : undefined,\n            }\n          );\n\n          if (!response) {\n            throw new Error(\"error\");\n          }\n\n          // Convert retuned bcs serialized SignedTransaction into V2 AccountAuthenticator\n          const deserializer1 = new BCS.Deserializer(response);\n          const deserializedSignature =\n            TxnBuilderTypes.SignedTransaction.deserialize(deserializer1);\n          const transactionAuthenticator =\n            deserializedSignature.authenticator as TxnBuilderTypes.TransactionAuthenticatorEd25519;\n\n          const publicKey = transactionAuthenticator.public_key.value;\n          const signature = transactionAuthenticator.signature.value;\n\n          const accountAuthenticator = new AccountAuthenticatorEd25519(\n            new Ed25519PublicKey(publicKey),\n            new Ed25519Signature(signature)\n          );\n          return accountAuthenticator;\n        }\n      }\n\n      // If we are here it means this wallet does not support signTransaction\n      throw new WalletNotSupportedMethod(\n        `Sign Transaction is not supported by ${this.wallet?.name}`\n      ).message;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignTransactionError(errMsg).message;\n    }\n  }\n\n  /**\n   * Sign message (doesnt submit to chain).\n   *\n   * @param message\n   * @return response from the wallet's signMessage function\n   * @throws WalletSignMessageError\n   */\n  async signMessage(message: SignMessagePayload): Promise<SignMessageResponse> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.recordEvent(\"sign_message\");\n      if (this._wallet.isAIP62Standard) {\n        return await this.walletStandardCore.signMessage(message, this._wallet);\n      }\n      const response = await this._wallet!.signMessage(message);\n      return response as SignMessageResponse;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageError(errMsg).message;\n    }\n  }\n\n  /**\n   * Submits transaction to chain\n   *\n   * @param transaction\n   * @returns PendingTransactionResponse\n   */\n  async submitTransaction(\n    transaction: InputSubmitTransactionData\n  ): Promise<PendingTransactionResponse> {\n    try {\n      this.ensureWalletExists(this._wallet);\n\n      const { additionalSignersAuthenticators } = transaction;\n      const transactionType =\n        additionalSignersAuthenticators !== undefined\n          ? \"multi-agent\"\n          : \"simple\";\n      this.recordEvent(\"submit_transaction\", {\n        transaction_type: transactionType,\n      });\n      // If wallet supports submitTransaction transaction function\n      if (this._wallet.submitTransaction) {\n        const pendingTransaction =\n          await this._wallet.submitTransaction(transaction);\n        return pendingTransaction;\n      }\n\n      // Else have the adapter submit the transaction\n\n      const aptosConfig = new AptosConfig({\n        network: convertNetwork(this.network),\n      });\n      const aptos = new Aptos(aptosConfig);\n      if (additionalSignersAuthenticators !== undefined) {\n        const multiAgentTxn = {\n          ...transaction,\n          additionalSignersAuthenticators,\n        };\n        return aptos.transaction.submit.multiAgent(multiAgentTxn);\n      } else {\n        return aptos.transaction.submit.simple(transaction);\n      }\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignTransactionError(errMsg).message;\n    }\n  }\n\n  /**\n  Event for when account has changed on the wallet\n  @return the new account info\n  @throws WalletAccountChangeError\n  */\n  async onAccountChange(): Promise<void> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      await this._wallet.onAccountChange(\n        async (data: AccountInfo | StandardAccountInfo) => {\n          this.setAccount(data);\n          await this.setAnsName();\n          this.recordEvent(\"account_change\");\n          this.emit(\"accountChange\", this._account);\n        }\n      );\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletAccountChangeError(errMsg).message;\n    }\n  }\n\n  /**\n  Event for when network has changed on the wallet\n  @return the new network info\n  @throws WalletNetworkChangeError\n  */\n  async onNetworkChange(): Promise<void> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      await this._wallet.onNetworkChange(\n        async (data: NetworkInfo | StandardNetworkInfo) => {\n          this.setNetwork(data);\n          await this.setAnsName();\n          this.emit(\"networkChange\", this._network);\n        }\n      );\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletNetworkChangeError(errMsg).message;\n    }\n  }\n\n  /**\n   * Sends a change network request to the wallet to change the connected network\n   *\n   * @param network\n   * @returns AptosChangeNetworkOutput\n   */\n  async changeNetwork(network: Network): Promise<AptosChangeNetworkOutput> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.recordEvent(\"change_network_request\", {\n        from: this._network?.name,\n        to: network,\n      });\n      const chainId =\n        network === Network.DEVNET\n          ? await fetchDevnetChainId()\n          : NetworkToChainId[network];\n      if (this._wallet.changeNetwork) {\n        const networkInfo: StandardNetworkInfo = {\n          name: network,\n          chainId,\n        };\n        const response = await this._wallet.changeNetwork(networkInfo);\n        if (response.status === UserResponseStatus.REJECTED) {\n          throw new WalletConnectionError(\"User has rejected the request\")\n            .message;\n        }\n        return response.args;\n      }\n      throw new WalletChangeNetworkError(\n        `${this._wallet.name} does not support changing network request`\n      ).message;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletChangeNetworkError(errMsg).message;\n    }\n  }\n\n  /**\n   * Signs a message and verifies the signer\n   * @param message SignMessagePayload\n   * @returns boolean\n   */\n  async signMessageAndVerify(message: SignMessagePayload): Promise<boolean> {\n    try {\n      this.ensureWalletExists(this._wallet);\n      this.ensureAccountExists(this._account);\n      this.recordEvent(\"sign_message_and_verify\");\n      // If current connected wallet is AIP-62 standard compatible\n      if (this._wallet.isAIP62Standard) {\n        return this.walletStandardCore.signMessageAndVerify(\n          message,\n          this._wallet\n        );\n      }\n\n      return await this.walletCoreV1.signMessageAndVerify(\n        message,\n        this._wallet,\n        this._account\n      );\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageAndVerifyError(errMsg).message;\n    }\n  }\n}\n", "import { TWallet } from \"@atomrigslab/aptos-wallet-adapter\";\nimport { AptosStandardWallet } from \"./WalletStandard\";\n\nconst sdkWallets: AptosStandardWallet[] = [];\n\nsdkWallets.push(new TWallet());\n\nexport default sdkWallets;\n", "export enum WalletReadyState {\n  /**\n   * User-installable wallets can typically be detected by scanning for an API\n   * that they've injected into the global context. If such an API is present,\n   * we consider the wallet to have been installed.\n   */\n  Installed = \"Installed\",\n  NotDetected = \"NotDetected\",\n  /**\n   * Loadable wallets are always available to you. Since you can load them at\n   * any time, it's meaningless to say that they have been detected.\n   */\n  Loadable = \"Loadable\",\n  /**\n   * If a wallet is not supported on a given platform (eg. server-rendering, or\n   * mobile) then it will stay in the `Unsupported` state.\n   */\n  Unsupported = \"Unsupported\",\n}\n\nexport enum NetworkName {\n  Mainnet = \"mainnet\",\n  Testnet = \"testnet\",\n  Devnet = \"devnet\",\n}\n\nexport const ChainIdToAnsSupportedNetworkMap: Record<string, string> = {\n  \"1\": \"mainnet\", // mainnet\n  \"2\": \"testnet\", // testnet\n};\n", "export class WalletError extends Error {\n  public error: any;\n\n  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n  constructor(message?: string, error?: any) {\n    super(message);\n    this.error = error;\n  }\n}\n\nexport class WalletNotSelectedError extends WalletError {\n  name = \"WalletNotSelectedError\";\n}\n\nexport class WalletNotReadyError extends WalletError {\n  name = \"WalletNotReadyError\";\n}\n\nexport class WalletLoadError extends WalletError {\n  name = \"WalletLoadError\";\n}\n\nexport class WalletConfigError extends WalletError {\n  name = \"WalletConfigError\";\n}\n\nexport class WalletConnectionError extends WalletError {\n  name = \"WalletConnectionError\";\n}\n\nexport class WalletDisconnectedError extends WalletError {\n  name = \"WalletDisconnectedError\";\n}\n\nexport class WalletDisconnectionError extends WalletError {\n  name = \"WalletDisconnectionError\";\n}\n\nexport class WalletAccountError extends WalletError {\n  name = \"WalletAccountError\";\n}\nexport class WalletGetNetworkError extends WalletError {\n  name = \"WalletGetNetworkError\";\n}\n\nexport class WalletAccountChangeError extends WalletError {\n  name = \"WalletAccountChangeError\";\n}\n\nexport class WalletNetworkChangeError extends WalletError {\n  name = \"WalletNetworkChangeError\";\n}\n\nexport class WalletPublicKeyError extends WalletError {\n  name = \"WalletPublicKeyError\";\n}\n\nexport class WalletKeypairError extends WalletError {\n  name = \"WalletKeypairError\";\n}\n\nexport class WalletNotConnectedError extends WalletError {\n  name = \"WalletNotConnectedError\";\n}\n\nexport class WalletSendTransactionError extends WalletError {\n  name = \"WalletSendTransactionError\";\n}\n\nexport class WalletSignMessageError extends WalletError {\n  name = \"WalletSignMessageError\";\n}\n\nexport class WalletSignMessageAndVerifyError extends WalletError {\n  name = \"WalletSignMessageAndVerifyError\";\n}\n\nexport class WalletSignAndSubmitMessageError extends WalletError {\n  name = \"WalletSignAndSubmitMessageError\";\n}\n\nexport class WalletSignTransactionError extends WalletError {\n  name = \"WalletSignTransactionError\";\n}\n\nexport class WalletTimeoutError extends WalletError {\n  name = \"WalletTimeoutError\";\n}\n\nexport class WalletWindowBlockedError extends WalletError {\n  name = \"WalletWindowBlockedError\";\n}\n\nexport class WalletWindowClosedError extends WalletError {\n  name = \"WalletWindowClosedError\";\n}\n\nexport class WalletResponseError extends WalletError {\n  name = \"WalletResponseError\";\n}\n\nexport class WalletNotSupportedMethod extends WalletError {\n  name = \"WalletNotSupportedMethod\";\n}\n\nexport class WalletChangeNetworkError extends WalletError {\n  name = \"WalletChangeNetworkError\";\n}\n", "import { HexString, TxnBuilderTypes, Types } from \"aptos\";\nimport EventEmitter from \"eventemitter3\";\nimport { Buffer } from \"buffer\";\nimport {\n  InputEntryFunctionDataWithRemoteABI,\n  InputGenerateTransactionPayloadData,\n  generateTransactionPayload,\n} from \"@aptos-labs/ts-sdk\";\nimport nacl from \"tweetnacl\";\nimport {\n  WalletNotSupportedMethod,\n  WalletSignAndSubmitMessageError,\n  WalletSignMessageAndVerifyError,\n  WalletSignTransactionError,\n} from \"../error\";\nimport {\n  Wallet,\n  WalletCoreEvents,\n  TransactionOptions,\n  NetworkInfo,\n  InputTransactionData,\n  AccountInfo,\n  SignMessagePayload,\n  SignMessageResponse,\n} from \"./types\";\n\nimport {\n  convertV2PayloadToV1JSONPayload,\n  convertV2TransactionPayloadToV1BCSPayload,\n} from \"./conversion\";\nimport {\n  areBCSArguments,\n  generalizedErrorMessage,\n  getAptosConfig,\n} from \"../utils\";\n\nexport class WalletCoreV1 extends EventEmitter<WalletCoreEvents> {\n  async connect(wallet: Wallet) {\n    const account = await wallet.connect();\n    return account;\n  }\n\n  /**\n   * Resolve the transaction type (BCS arguments or Simple arguments)\n   *\n   * @param payloadData\n   * @param network\n   * @param wallet\n   * @param transactionInput\n   *\n   * @returns\n   */\n  async resolveSignAndSubmitTransaction(\n    payloadData: InputGenerateTransactionPayloadData,\n    network: NetworkInfo | null,\n    wallet: Wallet,\n    transactionInput: InputTransactionData\n  ) {\n    // first check if each argument is a BCS serialized argument\n    if (areBCSArguments(payloadData.functionArguments)) {\n      const aptosConfig = getAptosConfig(network);\n      const newPayload = await generateTransactionPayload({\n        ...(payloadData as InputEntryFunctionDataWithRemoteABI),\n        aptosConfig: aptosConfig,\n      });\n      const oldTransactionPayload =\n        convertV2TransactionPayloadToV1BCSPayload(newPayload);\n      // Call and return signAndSubmitBCSTransaction response\n      return await this.signAndSubmitBCSTransaction(\n        oldTransactionPayload,\n        wallet!,\n        {\n          max_gas_amount: transactionInput.options?.maxGasAmount\n            ? BigInt(transactionInput.options?.maxGasAmount)\n            : undefined,\n          gas_unit_price: transactionInput.options?.gasUnitPrice\n            ? BigInt(transactionInput.options?.gasUnitPrice)\n            : undefined,\n        }\n      );\n    }\n\n    // if it is not a bcs serialized arguments transaction, convert to the old\n    // json format\n    const oldTransactionPayload = convertV2PayloadToV1JSONPayload(payloadData);\n    return await this.signAndSubmitTransaction(oldTransactionPayload, wallet!, {\n      max_gas_amount: transactionInput.options?.maxGasAmount\n        ? BigInt(transactionInput.options?.maxGasAmount)\n        : undefined,\n      gas_unit_price: transactionInput.options?.gasUnitPrice\n        ? BigInt(transactionInput.options?.gasUnitPrice)\n        : undefined,\n    });\n  }\n\n  /**\n  Sign and submit an entry (not bcs serialized) transaction type to chain.\n  @param transaction a non-bcs serialized transaction\n  @param options max_gas_amount and gas_unit_limit\n  @return response from the wallet's signAndSubmitTransaction function\n  @throws WalletSignAndSubmitMessageError\n  */\n  async signAndSubmitTransaction(\n    transaction: Types.TransactionPayload,\n    wallet: Wallet,\n    options?: TransactionOptions\n  ): Promise<any> {\n    try {\n      const response = await (wallet as any).signAndSubmitTransaction(\n        transaction,\n        options\n      );\n      return response;\n    } catch (error: any) {\n      const errMsg =\n        typeof error == \"object\" && \"message\" in error ? error.message : error;\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n\n  /**\n   Sign and submit a bsc serialized transaction type to chain.\n   @param transaction a bcs serialized transaction\n   @param options max_gas_amount and gas_unit_limit\n   @return response from the wallet's signAndSubmitBCSTransaction function\n   @throws WalletSignAndSubmitMessageError\n   */\n  async signAndSubmitBCSTransaction(\n    transaction: TxnBuilderTypes.TransactionPayload,\n    wallet: Wallet,\n    options?: TransactionOptions\n  ): Promise<any> {\n    if (!(\"signAndSubmitBCSTransaction\" in wallet)) {\n      throw new WalletNotSupportedMethod(\n        `Submit a BCS Transaction is not supported by ${wallet.name}`\n      ).message;\n    }\n    try {\n      const response = await (wallet as any).signAndSubmitBCSTransaction(\n        transaction,\n        options\n      );\n      return response;\n    } catch (error: any) {\n      const errMsg =\n        typeof error == \"object\" && \"message\" in error ? error.message : error;\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n\n  /**\n   Sign transaction\n   @param transaction\n   @param options max_gas_amount and gas_unit_limit\n   @return response from the wallet's signTransaction function\n   @throws WalletSignTransactionError\n   */\n  async signTransaction(\n    transaction: Types.TransactionPayload,\n    wallet: Wallet,\n    options?: TransactionOptions\n  ): Promise<Uint8Array | null> {\n    try {\n      const response = await (wallet as any).signTransaction(\n        transaction,\n        options\n      );\n      return response;\n    } catch (error: any) {\n      const errMsg =\n        typeof error == \"object\" && \"message\" in error ? error.message : error;\n      throw new WalletSignTransactionError(errMsg).message;\n    }\n  }\n\n  /**\n   * Signs a message and verifies the signer\n   * @param message SignMessagePayload\n   * @returns boolean\n   */\n  async signMessageAndVerify(\n    message: SignMessagePayload,\n    wallet: Wallet,\n    account: AccountInfo\n  ): Promise<boolean> {\n    try {\n      const response = await wallet.signMessage(message);\n      if (!response)\n        throw new WalletSignMessageAndVerifyError(\"Failed to sign a message\")\n          .message;\n      console.log(\"signMessageAndVerify signMessage response\", response);\n\n      // Verify that the bytes were signed using the private key that matches the known public key\n      let verified = false;\n      if (Array.isArray((response as SignMessageResponse).signature)) {\n        // multi sig wallets\n        const { fullMessage, signature, bitmap } =\n          response as SignMessageResponse;\n        if (bitmap) {\n          const minKeysRequired = account.minKeysRequired as number;\n          if ((signature as string[]).length < minKeysRequired) {\n            verified = false;\n          } else {\n            // Getting an array which marks the keys signing the message with 1, while marking 0 for the keys not being used.\n            const bits = Array.from(bitmap).flatMap((n) =>\n              Array.from({ length: 8 }).map((_, i) => (n >> i) & 1)\n            );\n            // Filter out indexes of the keys we need\n            const index = bits.map((_, i) => i).filter((i) => bits[i]);\n\n            const publicKeys = account.publicKey as string[];\n            const matchedPublicKeys = publicKeys.filter(\n              (_: string, i: number) => index.includes(i)\n            );\n\n            verified = true;\n            for (let i = 0; i < (signature as string[]).length; i++) {\n              const isSigVerified = nacl.sign.detached.verify(\n                Buffer.from(fullMessage),\n                Buffer.from((signature as string[])[i], \"hex\"),\n                Buffer.from(matchedPublicKeys[i], \"hex\")\n              ); // `isSigVerified` should be `true` for every signature\n\n              if (!isSigVerified) {\n                verified = false;\n                break;\n              }\n            }\n          }\n        } else {\n          throw new WalletSignMessageAndVerifyError(\"Failed to get a bitmap\")\n            .message;\n        }\n      } else {\n        // single sig wallets\n        // support for when address doesnt have hex prefix (0x)\n        const currentAccountPublicKey = new HexString(\n          account.publicKey as string\n        );\n        // support for when address doesnt have hex prefix (0x)\n        const signature = new HexString(\n          (response as SignMessageResponse).signature as string\n        );\n        verified = nacl.sign.detached.verify(\n          Buffer.from((response as SignMessageResponse).fullMessage),\n          Buffer.from(signature.noPrefix(), \"hex\"),\n          Buffer.from(currentAccountPublicKey.noPrefix(), \"hex\")\n        );\n      }\n      return verified;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageAndVerifyError(errMsg).message;\n    }\n  }\n}\n", "import {\n  Network,\n  TransactionPayload,\n  InputGenerateTransactionPayloadData,\n  TypeTag,\n  AptosConfig,\n  InputEntryFunctionData,\n  InputMultiSigData,\n  MoveFunctionId,\n  generateTransactionPayload,\n  TransactionPayloadEntryFunction,\n} from \"@aptos-labs/ts-sdk\";\nimport { NetworkInfo as StandardNetworkInfo } from \"@aptos-labs/wallet-standard\";\nimport { BCS, TxnBuilderTypes, Types } from \"aptos\";\nimport { NetworkInfo } from \"./types\";\n\n// old => new\nexport function convertNetwork(\n  networkInfo: NetworkInfo | StandardNetworkInfo | null\n): Network {\n  switch (networkInfo?.name) {\n    case \"mainnet\" as Network:\n      return Network.MAINNET;\n    case \"testnet\" as Network:\n      return Network.TESTNET;\n    case \"devnet\" as Network:\n      return Network.DEVNET;\n    default:\n      throw new Error(\"Invalid Aptos network name\");\n  }\n}\n\n// new => old\nexport function convertV2TransactionPayloadToV1BCSPayload(\n  payload: TransactionPayload\n): TxnBuilderTypes.TransactionPayload {\n  const deserializer = new BCS.Deserializer(payload.bcsToBytes());\n  return TxnBuilderTypes.TransactionPayload.deserialize(deserializer);\n}\n\nexport function convertV2PayloadToV1JSONPayload(\n  payload: InputGenerateTransactionPayloadData\n): Types.TransactionPayload {\n  if (\"bytecode\" in payload) {\n    // is a script payload\n    throw new Error(\"script payload not supported\");\n    // is multisig function payload\n  } else if (\"multisigAddress\" in payload) {\n    const stringTypeTags: string[] | undefined = payload.typeArguments?.map(\n      (typeTag) => {\n        if (typeTag instanceof TypeTag) {\n          return typeTag.toString();\n        }\n        return typeTag;\n      }\n    );\n    const newPayload: Types.TransactionPayload = {\n      type: \"multisig_payload\",\n      multisig_address: payload.multisigAddress.toString(),\n      function: payload.function,\n      type_arguments: stringTypeTags || [],\n      arguments: payload.functionArguments,\n    };\n\n    return newPayload;\n  } else {\n    // is entry function payload\n    const stringTypeTags: string[] | undefined = payload.typeArguments?.map(\n      (typeTag) => {\n        if (typeTag instanceof TypeTag) {\n          return typeTag.toString();\n        }\n        return typeTag;\n      }\n    );\n    const newPayload: Types.TransactionPayload = {\n      type: \"entry_function_payload\",\n      function: payload.function,\n      type_arguments: stringTypeTags || [],\n      arguments: payload.functionArguments,\n    };\n\n    return newPayload;\n  }\n}\n\nexport async function generateTransactionPayloadFromV1Input(\n  aptosConfig: AptosConfig,\n  inputV1: Types.TransactionPayload\n): Promise<TransactionPayloadEntryFunction> {\n  if (\"function\" in inputV1) {\n    const inputV2: InputEntryFunctionData | InputMultiSigData = {\n      function: inputV1.function as MoveFunctionId,\n      functionArguments: inputV1.arguments,\n      typeArguments: inputV1.type_arguments,\n    };\n    return generateTransactionPayload({ ...inputV2, aptosConfig });\n  }\n\n  throw new Error(\"Payload type not supported\");\n}\n\nexport interface CompatibleTransactionOptions {\n  expireTimestamp?: number;\n  expirationSecondsFromNow?: number;\n  expirationTimestamp?: number;\n  gasUnitPrice?: number;\n  gas_unit_price?: number;\n  maxGasAmount?: number;\n  max_gas_amount?: number;\n  sender?: string;\n  sequenceNumber?: number;\n}\n", "export function scopePollingDetectionStrategy(detect: () => boolean): void {\n  // Early return when server-side rendering\n  if (typeof window === \"undefined\" || typeof document === \"undefined\") return;\n\n  const disposers: (() => void)[] = [];\n\n  function detectAndDispose() {\n    const detected = detect();\n    if (detected) {\n      for (const dispose of disposers) {\n        dispose();\n      }\n    }\n  }\n\n  // Strategy #1: Try detecting every second.\n  const interval =\n    // TODO: #334 Replace with idle callback strategy.\n    setInterval(detectAndDispose, 1000);\n  disposers.push(() => clearInterval(interval));\n\n  // Strategy #2: Detect as soon as the DOM becomes 'ready'/'interactive'.\n  if (\n    // Implies that `DOMContentLoaded` has not yet fired.\n    document.readyState === \"loading\"\n  ) {\n    document.addEventListener(\"DOMContentLoaded\", detectAndDispose, {\n      once: true,\n    });\n    disposers.push(() =>\n      document.removeEventListener(\"DOMContentLoaded\", detectAndDispose)\n    );\n  }\n\n  // Strategy #3: Detect after the `window` has fully loaded.\n  if (\n    // If the `complete` state has been reached, we're too late.\n    document.readyState !== \"complete\"\n  ) {\n    window.addEventListener(\"load\", detectAndDispose, { once: true });\n    disposers.push(() => window.removeEventListener(\"load\", detectAndDispose));\n  }\n\n  // Strategy #4: Detect synchronously, now.\n  detectAndDispose();\n}\n", "import { WalletName } from \"../LegacyWalletPlugins/types\";\n\nconst LOCAL_STORAGE_ITEM_KEY = \"AptosWalletName\";\n\nexport function setLocalStorage(walletName: WalletName) {\n  localStorage.setItem(LOCAL_STORAGE_ITEM_KEY, walletName);\n}\n\nexport function removeLocalStorage() {\n  localStorage.removeItem(LOCAL_STORAGE_ITEM_KEY);\n}\n\nexport function getLocalStorage() {\n  localStorage.getItem(LOCAL_STORAGE_ITEM_KEY);\n}\n", "import {\n  Aptos,\n  AptosConfig,\n  EntryFunctionArgumentTypes,\n  Network,\n  NetworkToNodeAPI,\n  Serializable,\n  SimpleEntryFunctionArgumentTypes,\n} from \"@aptos-labs/ts-sdk\";\nimport { NetworkInfo as StandardNetworkInfo } from \"@aptos-labs/wallet-standard\";\nimport { NetworkInfo } from \"../LegacyWalletPlugins/types\";\nimport { convertNetwork } from \"../LegacyWalletPlugins/conversion\";\n\nexport function isMobile(): boolean {\n  return /Mobile|iP(hone|od|ad)|Android|BlackBerry|IEMobile|Kindle|NetFront|Silk-Accelerated|(hpw|web)OS|Fennec|Minimo|Opera M(obi|ini)|Blazer|Dolfin|Dolphin|Skyfire|Zune/i.test(\n    navigator.userAgent\n  );\n}\n\nexport function isInAppBrowser(): boolean {\n  const isIphone = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(\n    navigator.userAgent\n  );\n\n  const isAndroid = /(Android).*Version\\/[\\d.]+.*Chrome\\/[^\\s]+ Mobile/i.test(\n    navigator.userAgent\n  );\n\n  return isIphone || isAndroid;\n}\n\nexport function isRedirectable(): boolean {\n  // SSR: return false\n  if (typeof navigator === \"undefined\" || !navigator) return false;\n\n  // if we are on mobile and NOT in a in-app browser we will redirect to a wallet app\n\n  return isMobile() && !isInAppBrowser();\n}\n\nexport function generalizedErrorMessage(error: any): string {\n  return typeof error === \"object\" && \"message\" in error\n    ? error.message\n    : error;\n}\n\n// Helper function to check if input arguments are BCS serialized arguments.\n// In @aptos-labs/ts-sdk each move representative class extends\n// Serializable, so if each argument is of an instance of a class\n// the extends Serializable - we know these are BCS arguments\nexport const areBCSArguments = (\n  args: Array<EntryFunctionArgumentTypes | SimpleEntryFunctionArgumentTypes>\n): boolean => {\n  // `every` returns true if the array is empty, so\n  // first check the array length\n  if (args.length === 0) return false;\n  return args.every(\n    (arg: EntryFunctionArgumentTypes | SimpleEntryFunctionArgumentTypes) =>\n      arg instanceof Serializable\n  );\n};\n\n/**\n * Helper function to get AptosConfig that supports Aptos and Custom networks\n *\n * @param networkInfo\n * @returns AptosConfig\n */\nexport const getAptosConfig = (\n  networkInfo: NetworkInfo | StandardNetworkInfo | null\n): AptosConfig => {\n  if (!networkInfo) {\n    throw new Error(\"Undefined network\");\n  }\n  if (isAptosNetwork(networkInfo)) {\n    return new AptosConfig({\n      network: convertNetwork(networkInfo),\n    });\n  }\n  return new AptosConfig({\n    network: Network.CUSTOM,\n    fullnode: networkInfo.url,\n  });\n};\n\n/**\n * Helper function to resolve if the current connected network is an Aptos network\n *\n * @param networkInfo\n * @returns boolean\n */\nexport const isAptosNetwork = (\n  networkInfo: NetworkInfo | StandardNetworkInfo | null\n): boolean => {\n  if (!networkInfo) {\n    throw new Error(\"Undefined network\");\n  }\n  return NetworkToNodeAPI[networkInfo.name] !== undefined;\n};\n\n/**\n * Helper function to fetch Devnet chain id\n */\nexport const fetchDevnetChainId = async (): Promise<number> => {\n  const aptos = new Aptos(); // default to devnet\n  return await aptos.getChainId();\n};\n", "import { Types } from \"aptos\";\nimport {\n  Network,\n  InputGenerateTransactionOptions,\n  InputSubmitTransactionData,\n  PendingTransactionResponse,\n  AccountAddressInput,\n  InputGenerateTransactionPayloadData,\n  AnyRawTransaction,\n  Signature,\n} from \"@aptos-labs/ts-sdk\";\nimport { WalletReadyState } from \"../constants\";\nimport {\n  AptosSignAndSubmitTransactionOutput,\n  AptosSignMessageOutput,\n  UserResponse,\n  AccountInfo as StandardAccountInfo,\n  NetworkInfo as StandardNetworkInfo,\n  AptosChangeNetworkMethod,\n} from \"@aptos-labs/wallet-standard\";\nimport { AptosStandardSupportedWallet } from \"../AIP62StandardWallets/types\";\n\nexport { TxnBuilderTypes, Types } from \"aptos\";\nexport type {\n  InputGenerateTransactionData,\n  InputGenerateTransactionOptions,\n  AnyRawTransaction,\n  InputSubmitTransactionData,\n  PendingTransactionResponse,\n  AccountAuthenticator,\n  Network,\n} from \"@aptos-labs/ts-sdk\";\n\nexport type {\n  NetworkInfo as StandardNetworkInfo,\n  AptosChangeNetworkOutput,\n} from \"@aptos-labs/wallet-standard\";\n\n// WalletName is a nominal type that wallet adapters should use, e.g. `'MyCryptoWallet' as WalletName<'MyCryptoWallet'>`\nexport type WalletName<T extends string = string> = T & {\n  __brand__: \"WalletName\";\n};\n\nexport type NetworkInfo = {\n  name: Network;\n  chainId?: string;\n  url?: string;\n};\n\nexport type WalletInfo = {\n  name: WalletName;\n  icon: string;\n  url: string;\n};\n\nexport type AccountInfo = {\n  address: string;\n  publicKey: string | string[];\n  minKeysRequired?: number;\n  ansName?: string | null;\n};\n\nexport interface AptosWalletErrorResult {\n  code: number;\n  name: string;\n  message: string;\n}\n\nexport declare interface WalletCoreEvents {\n  connect(account: AccountInfo | null): void;\n  disconnect(): void;\n  readyStateChange(wallet: Wallet): void;\n  standardWalletsAdded(wallets: Wallet | AptosStandardSupportedWallet): void;\n  networkChange(network: NetworkInfo | null): void;\n  accountChange(account: AccountInfo | null): void;\n}\n\nexport interface SignMessagePayload {\n  address?: boolean; // Should we include the address of the account in the message\n  application?: boolean; // Should we include the domain of the dapp\n  chainId?: boolean; // Should we include the current chain id the wallet is connected to\n  message: string; // The message to be signed and displayed to the user\n  nonce: string; // A nonce the dapp should generate\n}\n\nexport interface SignMessageResponse {\n  address?: string;\n  application?: string;\n  chainId?: number;\n  fullMessage: string; // The message that was generated to sign\n  message: string; // The message passed in by the user\n  nonce: string;\n  prefix: \"APTOS\"; // Should always be APTOS\n  signature: string | string[] | Signature; // The signed full message\n  bitmap?: Uint8Array; // a 4-byte (32 bits) bit-vector of length N\n}\n\nexport type OnNetworkChange = (\n  callBack: (networkInfo: NetworkInfo | StandardNetworkInfo) => Promise<void>\n) => Promise<void>;\n\nexport type OnAccountChange = (\n  callBack: (accountInfo: AccountInfo | StandardAccountInfo) => Promise<any>\n) => Promise<void>;\n\nexport interface AdapterPluginEvents {\n  onNetworkChange: OnNetworkChange;\n  onAccountChange: OnAccountChange;\n}\n\n// TODO add signTransaction()\nexport interface AdapterPluginProps<Name extends string = string> {\n  name: WalletName<Name>;\n  url: string;\n  icon: `data:image/${\"svg+xml\" | \"webp\" | \"png\" | \"gif\"};base64,${string}`;\n  providerName?: string;\n  provider: any;\n  // Compatible with legacy wallet plugin\n  deeplinkProvider?: (data: { url: string }) => string;\n  // Comaptible with AIP-62 standard wallet\n  openInMobileApp?: () => void;\n  connect(): Promise<any>;\n  disconnect: () => Promise<any>;\n  network: () => Promise<any>;\n  signAndSubmitTransaction?(\n    transaction:\n      | Types.TransactionPayload\n      | InputTransactionData\n      | AnyRawTransaction,\n    options?: InputGenerateTransactionOptions\n  ): Promise<\n    | { hash: Types.HexEncodedBytes; output?: any }\n    | PendingTransactionResponse\n    | UserResponse<AptosSignAndSubmitTransactionOutput>\n  >;\n  submitTransaction?(\n    transaction: InputSubmitTransactionData\n  ): Promise<PendingTransactionResponse>;\n  signMessage<T extends SignMessagePayload>(\n    message: T\n  ): Promise<SignMessageResponse | UserResponse<AptosSignMessageOutput>>;\n  signTransaction?( // `any` type for backwards compatibility, especially for identity connect\n    transactionOrPayload: any,\n    optionsOrAsFeePayer?: any\n  ): Promise<any>;\n  account?: () => Promise<AccountInfo | StandardAccountInfo>;\n  changeNetwork?: AptosChangeNetworkMethod;\n}\n\nexport type AdapterPlugin<Name extends string = string> =\n  AdapterPluginProps<Name> & AdapterPluginEvents;\n\nexport type Wallet<Name extends string = string> = AdapterPlugin<Name> & {\n  readyState?: WalletReadyState;\n  isAIP62Standard?: boolean;\n};\n\nexport interface TransactionOptions {\n  max_gas_amount?: bigint;\n  gas_unit_price?: bigint;\n}\n\nexport type InputTransactionData = {\n  sender?: AccountAddressInput;\n  data: InputGenerateTransactionPayloadData;\n  options?: InputGenerateTransactionOptions;\n};\n\n// To be used by a wallet plugin\nexport interface PluginProvider {\n  connect: () => Promise<AccountInfo>;\n  account: () => Promise<AccountInfo>;\n  disconnect: () => Promise<void>;\n  signAndSubmitTransaction: (\n    transaction: any,\n    options?: any\n  ) => Promise<{ hash: Types.HexEncodedBytes } | AptosWalletErrorResult>;\n  signMessage: (message: SignMessagePayload) => Promise<SignMessageResponse>;\n  network: () => Promise<NetworkInfo>;\n  onAccountChange: (\n    listener: (newAddress: AccountInfo) => Promise<void>\n  ) => Promise<void>;\n  onNetworkChange: OnNetworkChange;\n}\n", "import {\n  UserResponse,\n  AptosSignTransactionOutput,\n  AptosSignMessageOutput,\n  AptosSignMessageInput,\n  AptosWallet,\n  UserResponseStatus,\n  AptosSignAndSubmitTransactionOutput,\n  AccountInfo as StandardAccountInfo,\n  AptosConnectOutput,\n} from \"@aptos-labs/wallet-standard\";\nimport {\n  AnyRawTransaction,\n  PendingTransactionResponse,\n  Aptos,\n  MultiEd25519Signature,\n  MultiEd25519PublicKey,\n} from \"@aptos-labs/ts-sdk\";\n\nimport { WalletReadyState } from \"../constants\";\nimport {\n  WalletConnectionError,\n  WalletSignAndSubmitMessageError,\n  WalletSignMessageAndVerifyError,\n  WalletSignMessageError,\n} from \"../error\";\nimport {\n  AccountInfo,\n  InputTransactionData,\n  Wallet,\n} from \"../LegacyWalletPlugins\";\nimport { generalizedErrorMessage } from \"../utils\";\n\nexport type AptosStandardWallet = AptosWallet & {\n  readyState?: WalletReadyState;\n};\n\nexport class WalletStandardCore {\n  async connect(wallet: Wallet) {\n    const response =\n      (await wallet.connect()) as UserResponse<AptosConnectOutput>;\n\n    if (response.status === UserResponseStatus.REJECTED) {\n      throw new WalletConnectionError(\"User has rejected the request\").message;\n    }\n    return response.args;\n  }\n\n  /**\n   * Signs and submits a transaction to chain\n   *\n   * @param transactionInput InputTransactionData\n   * @returns PendingTransactionResponse\n   */\n  async signAndSubmitTransaction(\n    transactionInput: InputTransactionData,\n    aptos: Aptos,\n    account: AccountInfo,\n    wallet: Wallet\n  ): Promise<PendingTransactionResponse> {\n    try {\n      const transaction = await aptos.transaction.build.simple({\n        sender: account.address.toString(),\n        data: transactionInput.data,\n        options: transactionInput.options,\n      });\n      const response = (await wallet.signAndSubmitTransaction!(\n        transaction\n      )) as UserResponse<AptosSignAndSubmitTransactionOutput>;\n      if (response.status === UserResponseStatus.REJECTED) {\n        throw new WalletConnectionError(\"User has rejected the request\")\n          .message;\n      }\n\n      return response.args;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignAndSubmitMessageError(errMsg).message;\n    }\n  }\n\n  /**\n   * Signs a transaction\n   *\n   * To support both existing wallet adapter V1 and V2, we support 2 input types\n   *\n   * @param transactionOrPayload AnyRawTransaction\n   * @param options asFeePayer. To sign a transaction as the fee payer sponsor\n   *\n   * @returns AptosSignTransactionOutput\n   */\n  async signTransaction(\n    transaction: AnyRawTransaction,\n    wallet: Wallet,\n    asFeePayer?: boolean\n  ): Promise<AptosSignTransactionOutput> {\n    const response = (await wallet.signTransaction!(\n      transaction,\n      asFeePayer\n    )) as UserResponse<AptosSignTransactionOutput>;\n    if (response.status === UserResponseStatus.REJECTED) {\n      throw new WalletConnectionError(\"User has rejected the request\").message;\n    }\n    return response.args;\n  }\n\n  /**\n   * Sign message\n   *\n   * @param message AptosSignMessageInput\n   * @return AptosSignMessageOutput\n   * @throws WalletSignMessageError\n   */\n  async signMessage(\n    message: AptosSignMessageInput,\n    wallet: Wallet\n  ): Promise<AptosSignMessageOutput> {\n    try {\n      const response = (await wallet.signMessage(\n        message\n      )) as UserResponse<AptosSignMessageOutput>;\n      if (response.status === UserResponseStatus.REJECTED) {\n        throw new WalletConnectionError(\"User has rejected the request\")\n          .message;\n      }\n      return response.args;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageError(errMsg).message;\n    }\n  }\n\n  /**\n   * Signs a message and verifies the signer\n   * @param message AptosSignMessageInput\n   * @returns boolean\n   */\n  async signMessageAndVerify(\n    message: AptosSignMessageInput,\n    wallet: Wallet\n  ): Promise<boolean> {\n    try {\n      // sign the message\n      const response = (await wallet.signMessage(\n        message\n      )) as UserResponse<AptosSignMessageOutput>;\n      // standard wallet account() method is a required method\n      const account = (await wallet.account!()) as StandardAccountInfo;\n\n      if (response.status === UserResponseStatus.REJECTED) {\n        throw new WalletConnectionError(\"Failed to sign a message\").message;\n      }\n\n      let verified = false;\n      // if is a multi sig wallet with a MultiEd25519Signature type\n      if (response.args.signature instanceof MultiEd25519Signature) {\n        if (!(account.publicKey instanceof MultiEd25519PublicKey)) {\n          throw new WalletSignMessageAndVerifyError(\n            \"Public key and Signature type mismatch\"\n          ).message;\n        }\n        const { fullMessage, signature } = response.args;\n        const bitmap = signature.bitmap;\n        if (bitmap) {\n          const minKeysRequired = account.publicKey.threshold;\n          if (signature.signatures.length < minKeysRequired) {\n            verified = false;\n          } else {\n            verified = account.publicKey.verifySignature({\n              message: new TextEncoder().encode(fullMessage),\n              signature,\n            });\n          }\n        }\n      } else {\n        verified = account.publicKey.verifySignature({\n          message: new TextEncoder().encode(response.args.fullMessage),\n          signature: response.args.signature,\n        });\n      }\n      return verified;\n    } catch (error: any) {\n      const errMsg = generalizedErrorMessage(error);\n      throw new WalletSignMessageAndVerifyError(errMsg).message;\n    }\n  }\n}\n", "export class GA4 {\n  readonly aptosGAID: string | undefined = process.env.GAID;\n\n  constructor() {\n    // Inject Aptos Google Analytics 4 script\n    this.injectGA(this.aptosGAID);\n  }\n\n  gtag(a: string, b: string | object, c?: object) {\n    let dataLayer = (window as any).dataLayer || [];\n    dataLayer.push(arguments);\n  }\n\n  private injectGA(gaID?: string) {\n    if (typeof window === \"undefined\") return;\n    if (!gaID) return;\n\n    const head = document.getElementsByTagName(\"head\")[0];\n\n    var myScript = document.createElement(\"script\");\n\n    myScript.setAttribute(\n      \"src\",\n      `https://www.googletagmanager.com/gtag/js?id=${gaID}`\n    );\n\n    const that = this;\n    myScript.onload = function () {\n      that.gtag(\"js\", new Date());\n      that.gtag(\"config\", `${gaID}`, {\n        send_page_view: false,\n      });\n    };\n\n    head.insertBefore(myScript, head.children[1]);\n  }\n}\n", "import { WalletName } from \"../LegacyWalletPlugins/types\";\nimport { WalletReadyState } from \"../constants\";\nimport { AptosStandardSupportedWallet } from \"./types\";\n\n/**\n * Registry of AIP-62 wallet standard supported wallets.\n * This list is used to show supported wallets even if they are not installed on the user machine.\n *\n * AIP-62 compatible wallets are required to add their wallet info here if they want to be detected by the adapter\n *\n * name - The wallet name\n * url - The wallet website url (where users can download it from)\n * icon - The wallet icon\n */\nexport const aptosStandardSupportedWalletList: Array<AptosStandardSupportedWallet> =\n  [\n    {\n      name: \"Nightly\" as WalletName<\"Nightly\">,\n      url: \"https://chromewebstore.google.com/detail/nightly/fiikommddbeccaoicoejoniammnalkfa?hl=en\",\n      icon: \"data:image/svg+xml;base64,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\",\n      readyState: WalletReadyState.NotDetected,\n      isAIP62Standard: true,\n    },\n    {\n      name: \"<PERSON>\" as WalletName<\"Petra\">,\n      url: \"https://chromewebstore.google.com/detail/petra-aptos-wallet/ejjladinnckdgjemekebdpeokbikhfci?hl=en\",\n      icon: \"data:image/png;base64,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\",\n      readyState: WalletReadyState.NotDetected,\n      isAIP62Standard: true,\n    },\n  ];\n", "import { WALLET_ADAPTER_CORE_VERSION } from \"./version\";\n\nexport { WalletCore } from \"./WalletCore\";\nexport * from \"./LegacyWalletPlugins\";\nexport * from \"./constants\";\nexport * from \"./utils\";\nexport * from \"./AIP62StandardWallets\";\n\nif (typeof window !== \"undefined\") {\n  (window as any).WALLET_ADAPTER_CORE_VERSION = WALLET_ADAPTER_CORE_VERSION;\n}\n"], "mappings": ";AAAO,IAAMA,2BAAA,GAA8B;;;ACA3C,SAASC,eAAA,IAAAC,gBAAA,EAAwBC,GAAA,IAAAC,IAAA,QAAW;AAC5C,SACEC,OAAA,IAAAC,QAAA,EAGAC,2BAAA,EACAC,gBAAA,EAEAC,gBAAA,EACAC,WAAA,IAAAC,YAAA,EAGAC,KAAA,IAAAC,MAAA,EACAC,sBAAA,EACAC,iBAAA,EACAC,gBAAA,QACK;AACP,OAAOC,aAAA,MAAkB;AACzB,SAIEC,eAAA,EAGAC,kBAAA,IAAAC,mBAAA,EACAC,8BAAA,QACK;;;AC3BP,SAASC,OAAA,QAAe;AAGxB,IAAMC,UAAA,GAAoC,EAAC;AAE3CA,UAAA,CAAWC,IAAA,CAAK,IAAIF,OAAA,CAAQ,CAAC;AAE7B,IAAOG,kBAAA,GAAQF,UAAA;;;ACPR,IAAKG,gBAAA,GAAL,gBAAKC,iBAAA,IAAL;EAMLA,iBAAA,gBAAY;EACZA,iBAAA,kBAAc;EAKdA,iBAAA,eAAW;EAKXA,iBAAA,kBAAc;EAjBJ,OAAAA,iBAAA;AAAA,GAAAD,gBAAA;AAoBL,IAAKE,WAAA,GAAL,gBAAKC,YAAA,IAAL;EACLA,YAAA,cAAU;EACVA,YAAA,cAAU;EACVA,YAAA,aAAS;EAHC,OAAAA,YAAA;AAAA,GAAAD,WAAA;AAML,IAAME,+BAAA,GAA0D;EACrE,KAAK;EACL,KAAK;AACP;;;AC7BO,IAAMC,WAAA,GAAN,cAA0BC,KAAA,CAAM;EAIrCC,YAAYC,OAAA,EAAkBC,KAAA,EAAa;IACzC,MAAMD,OAAO;IACb,KAAKC,KAAA,GAAQA,KAAA;EACf;AACF;AAEO,IAAMC,sBAAA,GAAN,cAAqCL,WAAA,CAAY;EAAjDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMC,mBAAA,GAAN,cAAkCR,WAAA,CAAY;EAA9CE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAUO,IAAME,qBAAA,GAAN,cAAoCT,WAAA,CAAY;EAAhDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAMO,IAAMG,wBAAA,GAAN,cAAuCV,WAAA,CAAY;EAAnDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMI,kBAAA,GAAN,cAAiCX,WAAA,CAAY;EAA7CE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AACO,IAAMK,qBAAA,GAAN,cAAoCZ,WAAA,CAAY;EAAhDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMM,wBAAA,GAAN,cAAuCb,WAAA,CAAY;EAAnDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMO,wBAAA,GAAN,cAAuCd,WAAA,CAAY;EAAnDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAUO,IAAMQ,uBAAA,GAAN,cAAsCf,WAAA,CAAY;EAAlDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAMO,IAAMS,sBAAA,GAAN,cAAqChB,WAAA,CAAY;EAAjDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMU,+BAAA,GAAN,cAA8CjB,WAAA,CAAY;EAA1DE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMW,+BAAA,GAAN,cAA8ClB,WAAA,CAAY;EAA1DE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMY,0BAAA,GAAN,cAAyCnB,WAAA,CAAY;EAArDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAkBO,IAAMa,wBAAA,GAAN,cAAuCpB,WAAA,CAAY;EAAnDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;AAEO,IAAMc,wBAAA,GAAN,cAAuCrB,WAAA,CAAY;EAAnDE,YAAA;IAAA,SAAAI,SAAA;IACL,KAAAC,IAAA,GAAO;EAAA;AACT;;;AC3GA,SAASe,SAAA,QAAyC;AAClD,OAAOC,YAAA,MAAkB;AACzB,SAASC,MAAA,QAAc;AACvB,SAGEC,0BAAA,IAAAC,2BAAA,QACK;AACP,OAAOC,IAAA,MAAU;;;ACRjB,SACErD,OAAA,EAGAsD,OAAA,EAKAH,0BAAA,QAEK;AAEP,SAASrD,GAAA,EAAKF,eAAA,QAA8B;AAIrC,SAAS2D,eACdC,WAAA,EACS;EACT,QAAQA,WAAA,oBAAAA,WAAA,CAAavB,IAAA;IAAA,KACd;MACH,OAAOjC,OAAA,CAAQyD,OAAA;IAAA,KACZ;MACH,OAAOzD,OAAA,CAAQ0D,OAAA;IAAA,KACZ;MACH,OAAO1D,OAAA,CAAQ2D,MAAA;IAAA;MAEf,MAAM,IAAIhC,KAAA,CAAM,4BAA4B;EAAA;AAElD;AAGO,SAASiC,0CACdC,OAAA,EACoC;EACpC,MAAMC,YAAA,GAAe,IAAIhE,GAAA,CAAIiE,YAAA,CAAaF,OAAA,CAAQG,UAAA,CAAW,CAAC;EAC9D,OAAOpE,eAAA,CAAgBqE,kBAAA,CAAmBC,WAAA,CAAYJ,YAAY;AACpE;AAEO,SAASK,gCACdN,OAAA,EAC0B;EA1C5B,IAAAO,EAAA,EAAAC,EAAA;EA2CE,IAAI,cAAcR,OAAA,EAAS;IAEzB,MAAM,IAAIlC,KAAA,CAAM,8BAA8B;EAEhD,WAAW,qBAAqBkC,OAAA,EAAS;IACvC,MAAMS,cAAA,IAAuCF,EAAA,GAAAP,OAAA,CAAQU,aAAA,KAAR,gBAAAH,EAAA,CAAuBI,GAAA,CACjEC,OAAA,IAAY;MACX,IAAIA,OAAA,YAAmBnB,OAAA,EAAS;QAC9B,OAAOmB,OAAA,CAAQC,QAAA,CAAS;MAC1B;MACA,OAAOD,OAAA;IACT;IAEF,MAAME,UAAA,GAAuC;MAC3CC,IAAA,EAAM;MACNC,gBAAA,EAAkBhB,OAAA,CAAQiB,eAAA,CAAgBJ,QAAA,CAAS;MACnDK,QAAA,EAAUlB,OAAA,CAAQkB,QAAA;MAClBC,cAAA,EAAgBV,cAAA,IAAkB,EAAC;MACnCtC,SAAA,EAAW6B,OAAA,CAAQoB;IACrB;IAEA,OAAON,UAAA;EACT,OAAO;IAEL,MAAML,cAAA,IAAuCD,EAAA,GAAAR,OAAA,CAAQU,aAAA,KAAR,gBAAAF,EAAA,CAAuBG,GAAA,CACjEC,OAAA,IAAY;MACX,IAAIA,OAAA,YAAmBnB,OAAA,EAAS;QAC9B,OAAOmB,OAAA,CAAQC,QAAA,CAAS;MAC1B;MACA,OAAOD,OAAA;IACT;IAEF,MAAME,UAAA,GAAuC;MAC3CC,IAAA,EAAM;MACNG,QAAA,EAAUlB,OAAA,CAAQkB,QAAA;MAClBC,cAAA,EAAgBV,cAAA,IAAkB,EAAC;MACnCtC,SAAA,EAAW6B,OAAA,CAAQoB;IACrB;IAEA,OAAON,UAAA;EACT;AACF;AAEA,eAAsBO,sCACpBC,WAAA,EACAC,OAAA,EAC0C;EAC1C,IAAI,cAAcA,OAAA,EAAS;IACzB,MAAMC,OAAA,GAAsD;MAC1DN,QAAA,EAAUK,OAAA,CAAQL,QAAA;MAClBE,iBAAA,EAAmBG,OAAA,CAAQpD,SAAA;MAC3BuC,aAAA,EAAea,OAAA,CAAQJ;IACzB;IACA,OAAO7B,0BAAA,CAA2B;MAAE,GAAGkC,OAAA;MAASF;IAAY,CAAC;EAC/D;EAEA,MAAM,IAAIxD,KAAA,CAAM,4BAA4B;AAC9C;;;ACpGO,SAAS2D,8BAA8BC,MAAA,EAA6B;EAEzE,IAAI,OAAOC,MAAA,KAAW,eAAe,OAAOC,QAAA,KAAa,aAAa;EAEtE,MAAMC,SAAA,GAA4B,EAAC;EAEnC,SAASC,iBAAA,EAAmB;IAC1B,MAAMC,QAAA,GAAWL,MAAA,CAAO;IACxB,IAAIK,QAAA,EAAU;MACZ,WAAWC,OAAA,IAAWH,SAAA,EAAW;QAC/BG,OAAA,CAAQ;MACV;IACF;EACF;EAGA,MAAMC,QAAA,GAEJC,WAAA,CAAYJ,gBAAA,EAAkB,GAAI;EACpCD,SAAA,CAAUvE,IAAA,CAAK,MAAM6E,aAAA,CAAcF,QAAQ,CAAC;EAG5C,IAEEL,QAAA,CAASQ,UAAA,KAAe,WACxB;IACAR,QAAA,CAASS,gBAAA,CAAiB,oBAAoBP,gBAAA,EAAkB;MAC9DQ,IAAA,EAAM;IACR,CAAC;IACDT,SAAA,CAAUvE,IAAA,CAAK,MACbsE,QAAA,CAASW,mBAAA,CAAoB,oBAAoBT,gBAAgB,CACnE;EACF;EAGA,IAEEF,QAAA,CAASQ,UAAA,KAAe,YACxB;IACAT,MAAA,CAAOU,gBAAA,CAAiB,QAAQP,gBAAA,EAAkB;MAAEQ,IAAA,EAAM;IAAK,CAAC;IAChET,SAAA,CAAUvE,IAAA,CAAK,MAAMqE,MAAA,CAAOY,mBAAA,CAAoB,QAAQT,gBAAgB,CAAC;EAC3E;EAGAA,gBAAA,CAAiB;AACnB;;;AC3CA,IAAMU,sBAAA,GAAyB;AAExB,SAASC,gBAAgBC,UAAA,EAAwB;EACtDC,YAAA,CAAaC,OAAA,CAAQJ,sBAAA,EAAwBE,UAAU;AACzD;AAEO,SAASG,mBAAA,EAAqB;EACnCF,YAAA,CAAaG,UAAA,CAAWN,sBAAsB;AAChD;AAEO,SAASO,gBAAA,EAAkB;EAChCJ,YAAA,CAAaK,OAAA,CAAQR,sBAAsB;AAC7C;;;ACdA,SACE9F,KAAA,EACAF,WAAA,IAAAyG,YAAA,EAEA9G,OAAA,IAAA+G,QAAA,EACAC,gBAAA,EACAC,YAAA,QAEK;AAKA,SAASC,SAAA,EAAoB;EAClC,OAAO,oKAAoKC,IAAA,CACzKC,SAAA,CAAUC,SACZ;AACF;AAEO,SAASC,eAAA,EAA0B;EACxC,MAAMC,QAAA,GAAW,+CAA+CJ,IAAA,CAC9DC,SAAA,CAAUC,SACZ;EAEA,MAAMG,SAAA,GAAY,qDAAqDL,IAAA,CACrEC,SAAA,CAAUC,SACZ;EAEA,OAAOE,QAAA,IAAYC,SAAA;AACrB;AAEO,SAASC,eAAA,EAA0B;EAExC,IAAI,OAAOL,SAAA,KAAc,eAAe,CAACA,SAAA,EAAW,OAAO;EAI3D,OAAOF,QAAA,CAAS,KAAK,CAACI,cAAA,CAAe;AACvC;AAEO,SAASI,wBAAwB5F,KAAA,EAAoB;EAC1D,OAAO,OAAOA,KAAA,KAAU,YAAY,aAAaA,KAAA,GAC7CA,KAAA,CAAMD,OAAA,GACNC,KAAA;AACN;AAMO,IAAM6F,eAAA,GACXC,IAAA,IACY;EAGZ,IAAIA,IAAA,CAAKC,MAAA,KAAW,GAAG,OAAO;EAC9B,OAAOD,IAAA,CAAKE,KAAA,CACTC,GAAA,IACCA,GAAA,YAAed,YACnB;AACF;AAQO,IAAMe,cAAA,GACXxE,WAAA,IACgB;EAChB,IAAI,CAACA,WAAA,EAAa;IAChB,MAAM,IAAI7B,KAAA,CAAM,mBAAmB;EACrC;EACA,IAAIsG,cAAA,CAAezE,WAAW,GAAG;IAC/B,OAAO,IAAIsD,YAAA,CAAY;MACrBoB,OAAA,EAAS3E,cAAA,CAAeC,WAAW;IACrC,CAAC;EACH;EACA,OAAO,IAAIsD,YAAA,CAAY;IACrBoB,OAAA,EAASnB,QAAA,CAAQoB,MAAA;IACjBC,QAAA,EAAU5E,WAAA,CAAY6E;EACxB,CAAC;AACH;AAQO,IAAMJ,cAAA,GACXzE,WAAA,IACY;EACZ,IAAI,CAACA,WAAA,EAAa;IAChB,MAAM,IAAI7B,KAAA,CAAM,mBAAmB;EACrC;EACA,OAAOqF,gBAAA,CAAiBxD,WAAA,CAAYvB,IAAA,MAAU;AAChD;AAKO,IAAMqG,kBAAA,GAAqB,MAAAA,CAAA,KAA6B;EAC7D,MAAMC,KAAA,GAAQ,IAAIhI,KAAA,CAAM;EACxB,OAAO,MAAMgI,KAAA,CAAMC,UAAA,CAAW;AAChC;;;AJtEO,IAAMC,YAAA,GAAN,cAA2BxF,YAAA,CAA+B;EAC/D,MAAMyF,QAAQC,MAAA,EAAgB;IAC5B,MAAMC,OAAA,GAAU,MAAMD,MAAA,CAAOD,OAAA,CAAQ;IACrC,OAAOE,OAAA;EACT;EAYA,MAAMC,gCACJC,WAAA,EACAZ,OAAA,EACAS,MAAA,EACAI,gBAAA,EACA;IAzDJ,IAAA3E,EAAA,EAAAC,EAAA,EAAA2E,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;IA2DI,IAAI1B,eAAA,CAAgBmB,WAAA,CAAY7D,iBAAiB,GAAG;MAClD,MAAME,WAAA,GAAc6C,cAAA,CAAeE,OAAO;MAC1C,MAAMvD,UAAA,GAAa,MAAMvB,2BAAA,CAA2B;QAClD,GAAI0F,WAAA;QACJ3D;MACF,CAAC;MACD,MAAMmE,sBAAA,GACJ1F,yCAAA,CAA0Ce,UAAU;MAEtD,OAAO,MAAM,KAAK4E,2BAAA,CAChBD,sBAAA,EACAX,MAAA,EACA;QACEa,cAAA,IAAgBpF,EAAA,GAAA2E,gBAAA,CAAiBU,OAAA,KAAjB,gBAAArF,EAAA,CAA0BsF,YAAA,IACtCC,MAAA,EAAOtF,EAAA,GAAA0E,gBAAA,CAAiBU,OAAA,KAAjB,gBAAApF,EAAA,CAA0BqF,YAAY,IAC7C;QACJE,cAAA,IAAgBZ,EAAA,GAAAD,gBAAA,CAAiBU,OAAA,KAAjB,gBAAAT,EAAA,CAA0Ba,YAAA,IACtCF,MAAA,EAAOV,EAAA,GAAAF,gBAAA,CAAiBU,OAAA,KAAjB,gBAAAR,EAAA,CAA0BY,YAAY,IAC7C;MACN,CACF;IACF;IAIA,MAAMC,qBAAA,GAAwB3F,+BAAA,CAAgC2E,WAAW;IACzE,OAAO,MAAM,KAAKiB,wBAAA,CAAyBD,qBAAA,EAAuBnB,MAAA,EAAS;MACzEa,cAAA,IAAgBN,EAAA,GAAAH,gBAAA,CAAiBU,OAAA,KAAjB,gBAAAP,EAAA,CAA0BQ,YAAA,IACtCC,MAAA,EAAOR,EAAA,GAAAJ,gBAAA,CAAiBU,OAAA,KAAjB,gBAAAN,EAAA,CAA0BO,YAAY,IAC7C;MACJE,cAAA,IAAgBR,EAAA,GAAAL,gBAAA,CAAiBU,OAAA,KAAjB,gBAAAL,EAAA,CAA0BS,YAAA,IACtCF,MAAA,EAAON,EAAA,GAAAN,gBAAA,CAAiBU,OAAA,KAAjB,gBAAAJ,EAAA,CAA0BQ,YAAY,IAC7C;IACN,CAAC;EACH;EASA,MAAME,yBACJC,WAAA,EACArB,MAAA,EACAc,OAAA,EACc;IACd,IAAI;MACF,MAAMQ,QAAA,GAAW,MAAOtB,MAAA,CAAeoB,wBAAA,CACrCC,WAAA,EACAP,OACF;MACA,OAAOQ,QAAA;IACT,SAASnI,KAAA,EAAP;MACA,MAAMoI,MAAA,GACJ,OAAOpI,KAAA,IAAS,YAAY,aAAaA,KAAA,GAAQA,KAAA,CAAMD,OAAA,GAAUC,KAAA;MACnE,MAAM,IAAIc,+BAAA,CAAgCsH,MAAM,EAAErI,OAAA;IACpD;EACF;EASA,MAAM0H,4BACJS,WAAA,EACArB,MAAA,EACAc,OAAA,EACc;IACd,IAAI,EAAE,iCAAiCd,MAAA,GAAS;MAC9C,MAAM,IAAI7F,wBAAA,CACR,gDAAgD6F,MAAA,CAAO1G,IAAA,EACzD,EAAEJ,OAAA;IACJ;IACA,IAAI;MACF,MAAMoI,QAAA,GAAW,MAAOtB,MAAA,CAAeY,2BAAA,CACrCS,WAAA,EACAP,OACF;MACA,OAAOQ,QAAA;IACT,SAASnI,KAAA,EAAP;MACA,MAAMoI,MAAA,GACJ,OAAOpI,KAAA,IAAS,YAAY,aAAaA,KAAA,GAAQA,KAAA,CAAMD,OAAA,GAAUC,KAAA;MACnE,MAAM,IAAIc,+BAAA,CAAgCsH,MAAM,EAAErI,OAAA;IACpD;EACF;EASA,MAAMsI,gBACJH,WAAA,EACArB,MAAA,EACAc,OAAA,EAC4B;IAC5B,IAAI;MACF,MAAMQ,QAAA,GAAW,MAAOtB,MAAA,CAAewB,eAAA,CACrCH,WAAA,EACAP,OACF;MACA,OAAOQ,QAAA;IACT,SAASnI,KAAA,EAAP;MACA,MAAMoI,MAAA,GACJ,OAAOpI,KAAA,IAAS,YAAY,aAAaA,KAAA,GAAQA,KAAA,CAAMD,OAAA,GAAUC,KAAA;MACnE,MAAM,IAAIe,0BAAA,CAA2BqH,MAAM,EAAErI,OAAA;IAC/C;EACF;EAOA,MAAMuI,qBACJvI,OAAA,EACA8G,MAAA,EACAC,OAAA,EACkB;IAClB,IAAI;MACF,MAAMqB,QAAA,GAAW,MAAMtB,MAAA,CAAO0B,WAAA,CAAYxI,OAAO;MACjD,IAAI,CAACoI,QAAA,EACH,MAAM,IAAItH,+BAAA,CAAgC,0BAA0B,EACjEd,OAAA;MACLyI,OAAA,CAAQC,GAAA,CAAI,6CAA6CN,QAAQ;MAGjE,IAAIO,QAAA,GAAW;MACf,IAAIC,KAAA,CAAMC,OAAA,CAAST,QAAA,CAAiCU,SAAS,GAAG;QAE9D,MAAM;UAAEC,WAAA;UAAaD,SAAA;UAAWE;QAAO,IACrCZ,QAAA;QACF,IAAIY,MAAA,EAAQ;UACV,MAAMC,eAAA,GAAkBlC,OAAA,CAAQkC,eAAA;UAChC,IAAKH,SAAA,CAAuB9C,MAAA,GAASiD,eAAA,EAAiB;YACpDN,QAAA,GAAW;UACb,OAAO;YAEL,MAAMO,IAAA,GAAON,KAAA,CAAMO,IAAA,CAAKH,MAAM,EAAEI,OAAA,CAASC,CAAA,IACvCT,KAAA,CAAMO,IAAA,CAAK;cAAEnD,MAAA,EAAQ;YAAE,CAAC,EAAErD,GAAA,CAAI,CAAC2G,CAAA,EAAGC,CAAA,KAAOF,CAAA,IAAKE,CAAA,GAAK,CAAC,CACtD;YAEA,MAAMC,KAAA,GAAQN,IAAA,CAAKvG,GAAA,CAAI,CAAC2G,CAAA,EAAGC,CAAA,KAAMA,CAAC,EAAEE,MAAA,CAAQF,CAAA,IAAML,IAAA,CAAKK,CAAA,CAAE;YAEzD,MAAMG,UAAA,GAAa3C,OAAA,CAAQ4C,SAAA;YAC3B,MAAMC,iBAAA,GAAoBF,UAAA,CAAWD,MAAA,CACnC,CAACH,CAAA,EAAWC,CAAA,KAAcC,KAAA,CAAMK,QAAA,CAASN,CAAC,CAC5C;YAEAZ,QAAA,GAAW;YACX,SAASY,CAAA,GAAI,GAAGA,CAAA,GAAKT,SAAA,CAAuB9C,MAAA,EAAQuD,CAAA,IAAK;cACvD,MAAMO,aAAA,GAAgBtI,IAAA,CAAKuI,IAAA,CAAKC,QAAA,CAASC,MAAA,CACvC5I,MAAA,CAAO8H,IAAA,CAAKJ,WAAW,GACvB1H,MAAA,CAAO8H,IAAA,CAAML,SAAA,CAAuBS,CAAA,GAAI,KAAK,GAC7ClI,MAAA,CAAO8H,IAAA,CAAKS,iBAAA,CAAkBL,CAAA,GAAI,KAAK,CACzC;cAEA,IAAI,CAACO,aAAA,EAAe;gBAClBnB,QAAA,GAAW;gBACX;cACF;YACF;UACF;QACF,OAAO;UACL,MAAM,IAAI7H,+BAAA,CAAgC,wBAAwB,EAC/Dd,OAAA;QACL;MACF,OAAO;QAGL,MAAMkK,uBAAA,GAA0B,IAAI/I,SAAA,CAClC4F,OAAA,CAAQ4C,SACV;QAEA,MAAMb,SAAA,GAAY,IAAI3H,SAAA,CACnBiH,QAAA,CAAiCU,SACpC;QACAH,QAAA,GAAWnH,IAAA,CAAKuI,IAAA,CAAKC,QAAA,CAASC,MAAA,CAC5B5I,MAAA,CAAO8H,IAAA,CAAMf,QAAA,CAAiCW,WAAW,GACzD1H,MAAA,CAAO8H,IAAA,CAAKL,SAAA,CAAUqB,QAAA,CAAS,GAAG,KAAK,GACvC9I,MAAA,CAAO8H,IAAA,CAAKe,uBAAA,CAAwBC,QAAA,CAAS,GAAG,KAAK,CACvD;MACF;MACA,OAAOxB,QAAA;IACT,SAAS1I,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIa,+BAAA,CAAgCuH,MAAM,EAAErI,OAAA;IACpD;EACF;AACF;;;AKzOA,SAASjC,eAAA,IAAAqM,gBAAA,EAAiBC,KAAA,IAAAC,MAAA,QAAa;;;ACtBvC,SAMErL,kBAAA,QAIK;AACP,SAIEsL,qBAAA,EACAC,qBAAA,QACK;AAoBA,IAAMC,kBAAA,GAAN,MAAyB;EAC9B,MAAM5D,QAAQC,MAAA,EAAgB;IAC5B,MAAMsB,QAAA,GACH,MAAMtB,MAAA,CAAOD,OAAA,CAAQ;IAExB,IAAIuB,QAAA,CAASsC,MAAA,KAAWzL,kBAAA,CAAmB0L,QAAA,EAAU;MACnD,MAAM,IAAIrK,qBAAA,CAAsB,+BAA+B,EAAEN,OAAA;IACnE;IACA,OAAOoI,QAAA,CAASrC,IAAA;EAClB;EAQA,MAAMmC,yBACJhB,gBAAA,EACAR,KAAA,EACAK,OAAA,EACAD,MAAA,EACqC;IACrC,IAAI;MACF,MAAMqB,WAAA,GAAc,MAAMzB,KAAA,CAAMyB,WAAA,CAAYyC,KAAA,CAAMC,MAAA,CAAO;QACvDC,MAAA,EAAQ/D,OAAA,CAAQgE,OAAA,CAAQlI,QAAA,CAAS;QACjCmI,IAAA,EAAM9D,gBAAA,CAAiB8D,IAAA;QACvBpD,OAAA,EAASV,gBAAA,CAAiBU;MAC5B,CAAC;MACD,MAAMQ,QAAA,GAAY,MAAMtB,MAAA,CAAOoB,wBAAA,CAC7BC,WACF;MACA,IAAIC,QAAA,CAASsC,MAAA,KAAWzL,kBAAA,CAAmB0L,QAAA,EAAU;QACnD,MAAM,IAAIrK,qBAAA,CAAsB,+BAA+B,EAC5DN,OAAA;MACL;MAEA,OAAOoI,QAAA,CAASrC,IAAA;IAClB,SAAS9F,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIc,+BAAA,CAAgCsH,MAAM,EAAErI,OAAA;IACpD;EACF;EAYA,MAAMsI,gBACJH,WAAA,EACArB,MAAA,EACAmE,UAAA,EACqC;IACrC,MAAM7C,QAAA,GAAY,MAAMtB,MAAA,CAAOwB,eAAA,CAC7BH,WAAA,EACA8C,UACF;IACA,IAAI7C,QAAA,CAASsC,MAAA,KAAWzL,kBAAA,CAAmB0L,QAAA,EAAU;MACnD,MAAM,IAAIrK,qBAAA,CAAsB,+BAA+B,EAAEN,OAAA;IACnE;IACA,OAAOoI,QAAA,CAASrC,IAAA;EAClB;EASA,MAAMyC,YACJxI,OAAA,EACA8G,MAAA,EACiC;IACjC,IAAI;MACF,MAAMsB,QAAA,GAAY,MAAMtB,MAAA,CAAO0B,WAAA,CAC7BxI,OACF;MACA,IAAIoI,QAAA,CAASsC,MAAA,KAAWzL,kBAAA,CAAmB0L,QAAA,EAAU;QACnD,MAAM,IAAIrK,qBAAA,CAAsB,+BAA+B,EAC5DN,OAAA;MACL;MACA,OAAOoI,QAAA,CAASrC,IAAA;IAClB,SAAS9F,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIY,sBAAA,CAAuBwH,MAAM,EAAErI,OAAA;IAC3C;EACF;EAOA,MAAMuI,qBACJvI,OAAA,EACA8G,MAAA,EACkB;IAClB,IAAI;MAEF,MAAMsB,QAAA,GAAY,MAAMtB,MAAA,CAAO0B,WAAA,CAC7BxI,OACF;MAEA,MAAM+G,OAAA,GAAW,MAAMD,MAAA,CAAOC,OAAA,CAAS;MAEvC,IAAIqB,QAAA,CAASsC,MAAA,KAAWzL,kBAAA,CAAmB0L,QAAA,EAAU;QACnD,MAAM,IAAIrK,qBAAA,CAAsB,0BAA0B,EAAEN,OAAA;MAC9D;MAEA,IAAI2I,QAAA,GAAW;MAEf,IAAIP,QAAA,CAASrC,IAAA,CAAK+C,SAAA,YAAqByB,qBAAA,EAAuB;QAC5D,IAAI,EAAExD,OAAA,CAAQ4C,SAAA,YAAqBa,qBAAA,GAAwB;UACzD,MAAM,IAAI1J,+BAAA,CACR,wCACF,EAAEd,OAAA;QACJ;QACA,MAAM;UAAE+I,WAAA;UAAaD;QAAU,IAAIV,QAAA,CAASrC,IAAA;QAC5C,MAAMiD,MAAA,GAASF,SAAA,CAAUE,MAAA;QACzB,IAAIA,MAAA,EAAQ;UACV,MAAMC,eAAA,GAAkBlC,OAAA,CAAQ4C,SAAA,CAAUuB,SAAA;UAC1C,IAAIpC,SAAA,CAAUqC,UAAA,CAAWnF,MAAA,GAASiD,eAAA,EAAiB;YACjDN,QAAA,GAAW;UACb,OAAO;YACLA,QAAA,GAAW5B,OAAA,CAAQ4C,SAAA,CAAUyB,eAAA,CAAgB;cAC3CpL,OAAA,EAAS,IAAIqL,WAAA,CAAY,EAAEC,MAAA,CAAOvC,WAAW;cAC7CD;YACF,CAAC;UACH;QACF;MACF,OAAO;QACLH,QAAA,GAAW5B,OAAA,CAAQ4C,SAAA,CAAUyB,eAAA,CAAgB;UAC3CpL,OAAA,EAAS,IAAIqL,WAAA,CAAY,EAAEC,MAAA,CAAOlD,QAAA,CAASrC,IAAA,CAAKgD,WAAW;UAC3DD,SAAA,EAAWV,QAAA,CAASrC,IAAA,CAAK+C;QAC3B,CAAC;MACH;MACA,OAAOH,QAAA;IACT,SAAS1I,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIa,+BAAA,CAAgCuH,MAAM,EAAErI,OAAA;IACpD;EACF;AACF;;;AC1LO,IAAMuL,GAAA,GAAN,MAAU;EAGfxL,YAAA,EAAc;IAFd,KAASyL,SAAA,GAAgC;IAIvC,KAAKC,QAAA,CAAS,KAAKD,SAAS;EAC9B;EAEAE,KAAKC,CAAA,EAAWC,CAAA,EAAoBC,CAAA,EAAY;IAC9C,IAAIC,SAAA,GAAanI,MAAA,CAAemI,SAAA,IAAa,EAAC;IAC9CA,SAAA,CAAUxM,IAAA,CAAKa,SAAS;EAC1B;EAEQsL,SAASM,IAAA,EAAe;IAC9B,IAAI,OAAOpI,MAAA,KAAW,aAAa;IACnC,IAAI,CAACoI,IAAA,EAAM;IAEX,MAAMC,IAAA,GAAOpI,QAAA,CAASqI,oBAAA,CAAqB,MAAM,EAAE;IAEnD,IAAIC,QAAA,GAAWtI,QAAA,CAASuI,aAAA,CAAc,QAAQ;IAE9CD,QAAA,CAASE,YAAA,CACP,OACA,+CAA+CL,IAAA,EACjD;IAEA,MAAMM,IAAA,GAAO;IACbH,QAAA,CAASI,MAAA,GAAS,YAAY;MAC5BD,IAAA,CAAKX,IAAA,CAAK,MAAM,IAAIa,IAAA,CAAK,CAAC;MAC1BF,IAAA,CAAKX,IAAA,CAAK,UAAU,GAAGK,IAAA,IAAQ;QAC7BS,cAAA,EAAgB;MAClB,CAAC;IACH;IAEAR,IAAA,CAAKS,YAAA,CAAaP,QAAA,EAAUF,IAAA,CAAKU,QAAA,CAAS,EAAE;EAC9C;AACF;;;ACtBO,IAAMC,gCAAA,GACX,CACE;EACEvM,IAAA,EAAM;EACNoG,GAAA,EAAK;EACLoG,IAAA,EAAM;EACNxI,UAAA;EACAyI,eAAA,EAAiB;AACnB,GACA;EACEzM,IAAA,EAAM;EACNoG,GAAA,EAAK;EACLoG,IAAA,EAAM;EACNxI,UAAA;EACAyI,eAAA,EAAiB;AACnB,EACF;;;AZuDK,IAAMC,UAAA,GAAN,cAAyB/N,aAAA,CAA+B;EA8C7DgB,YACEgN,OAAA,EACAC,YAAA,EACA;IACA,MAAM;IAhDR,KAAQC,QAAA,GAAkC,EAAC;IAG3C,KAAQC,aAAA,GAAiD,EAAC;IAG1D,KAAQC,iBAAA,GAAwD,EAAC;IAIjE,KAAQC,YAAA,GAA6D,EAAC;IAGtE,KAAQC,OAAA,GAAyB;IAGjC,KAAQC,QAAA,GAA+B;IAGvC,KAAQC,QAAA,GAA+B;IAGvC,KAAiBC,YAAA,GAA6B,IAAI5G,YAAA,CAAa;IAG/D,KAAiB6G,kBAAA,GACf,IAAIhD,kBAAA,CAAmB;IAGzB,KAAQiD,WAAA,GAAuB;IAG/B,KAAQC,UAAA,GAAsB;IAG9B,KAAiBC,GAAA,GAAW,IAAIrC,GAAA,CAAI;IA6IpC,KAAQsC,2CAAA,GACNC,cAAA,IACG;MAzQP,IAAAvL,EAAA,EAAAC,EAAA,EAAA2E,EAAA;MA0QI,IAAI4G,+BAAA,GAA0C;QAC5C3N,IAAA,EAAM0N,cAAA,CAAe1N,IAAA;QACrBoG,GAAA,EAAKsH,cAAA,CAAetH,GAAA;QACpBoG,IAAA,EAAMkB,cAAA,CAAelB,IAAA;QACrBoB,QAAA,EAAUF,cAAA;QACVjH,OAAA,EAASiH,cAAA,CAAeG,QAAA,CAAS,iBAAiBpH,OAAA;QAClDqH,UAAA,EAAYJ,cAAA,CAAeG,QAAA,CAAS,oBAAoBC,UAAA;QACxD7H,OAAA,EAASyH,cAAA,CAAeG,QAAA,CAAS,iBAAiB5H,OAAA;QAClDU,OAAA,EAAS+G,cAAA,CAAeG,QAAA,CAAS,iBAAiBlH,OAAA;QAClDmB,wBAAA,GACE3F,EAAA,GAAAuL,cAAA,CAAeG,QAAA,CAAS,sCAAxB,gBAAA1L,EAAA,CACI2F,wBAAA;QACNM,WAAA,EAAasF,cAAA,CAAeG,QAAA,CAAS,qBAAqBzF,WAAA;QAC1D2F,eAAA,EACEL,cAAA,CAAeG,QAAA,CAAS,yBAAyBE,eAAA;QACnDC,eAAA,EACEN,cAAA,CAAeG,QAAA,CAAS,yBAAyBG,eAAA;QACnD9F,eAAA,EACEwF,cAAA,CAAeG,QAAA,CAAS,yBAAyB3F,eAAA;QACnD+F,eAAA,GACE7L,EAAA,GAAAsL,cAAA,CAAeG,QAAA,CAAS,6BAAxB,gBAAAzL,EAAA,CAAkD6L,eAAA;QACpDC,aAAA,GACEnH,EAAA,GAAA2G,cAAA,CAAeG,QAAA,CAAS,2BAAxB,gBAAA9G,EAAA,CAAgDmH,aAAA;QAClDlK,UAAA;QACAyI,eAAA,EAAiB;MACnB;MAGA,KAAKO,YAAA,GAAe,KAAKA,YAAA,CAAa3D,MAAA,CACnC8E,IAAA,IAASA,IAAA,CAAKnO,IAAA,KAAS2N,+BAAA,CAAgC3N,IAC1D;MACA,KAAKgN,YAAA,CAAa9N,IAAA,CAAKyO,+BAA+B;MAEtD,KAAKS,IAAA,CAAK,wBAAwBT,+BAA+B;IACnE;IApKE,KAAKd,QAAA,GAAWF,OAAA;IAChB,KAAKG,aAAA,GAAgBF,YAAA;IAErB,KAAKvJ,6BAAA,CAA8B;IAEnC,KAAKgL,iBAAA,CAAkB;EACzB;EAEQhL,8BAAA,EAAgC;IAhJ1C,IAAAlB,EAAA;IAiJI,CAAAA,EAAA,QAAK0K,QAAA,KAAL,gBAAA1K,EAAA,CAAemM,OAAA,CAAS5H,MAAA,IAAmB;MACzC,KAAKsG,YAAA,CAAa9N,IAAA,CAAKwH,MAAM;MAC7B,IAAI,CAACA,MAAA,CAAO1C,UAAA,EAAY;QACtB0C,MAAA,CAAO1C,UAAA,GACL,OAAOT,MAAA,KAAW,eAAe,OAAOC,QAAA,KAAa;MAGzD;MACA,IAAI,OAAOD,MAAA,KAAW,aAAa;QACjCF,6BAAA,CAA8B,MAAM;UAClC,MAAMkL,YAAA,GAAe7H,MAAA,CAAO6H,YAAA,IAAgB7H,MAAA,CAAO1G,IAAA,CAAKwO,WAAA,CAAY;UACpE,IAAIC,MAAA,CAAOC,IAAA,CAAKnL,MAAM,EAAEkG,QAAA,CAAS8E,YAAY,GAAG;YAC9C7H,MAAA,CAAO1C,UAAA;YACP0C,MAAA,CAAOkH,QAAA,GAAWrK,MAAA,CAAOgL,YAAA;YACzB,KAAKH,IAAA,CAAK,oBAAoB1H,MAAM;YACpC,OAAO;UACT;UACA,OAAO;QACT,CAAC;MACH;IACF;EACF;EAEQ2H,kBAAA,EAAoB;IAC1B,IAAI;MAAEM,YAAA;MAAcC;IAAG,IAAIhQ,eAAA,CAAgB;IAC3C,KAAKiQ,UAAA,CAAWF,YAAY;IAE5B,IAAI,OAAOpL,MAAA,KAAW,aAAa;IAGnC,MAAM0I,IAAA,GAAO;IACb,MAAM6C,sBAAA,GAAyBF,EAAA,CAAG,YAAY,YAAY;MACxD,IAAI;QAAED,YAAA,EAAAI;MAAa,IAAInQ,eAAA,CAAgB;MACvCqN,IAAA,CAAK4C,UAAA,CAAWE,aAAY;IAC9B,CAAC;IAED,MAAMC,wBAAA,GAA2BJ,EAAA,CAAG,cAAc,YAAY;MAC5D,IAAI;QAAED,YAAA,EAAAI;MAAa,IAAInQ,eAAA,CAAgB;MACvCqN,IAAA,CAAK4C,UAAA,CAAWE,aAAY;IAC9B,CAAC;EACH;EAGQE,0CACNC,oBAAA,EACA;IACA3C,gCAAA,CAAiChK,GAAA,CAAK4M,eAAA,IAAoB;MACxD,IAAI,KAAKC,aAAA,CAAcD,eAAA,CAAgBnP,IAAI,GAAG;QAC5C;MACF;MACA,MAAMqP,mBAAA,GAAsBH,oBAAA,CAAqBI,SAAA,CAC9C5I,MAAA,IAAWA,MAAA,CAAO1G,IAAA,IAAQmP,eAAA,CAAgBnP,IAC7C;MAGA,IAAIqP,mBAAA,KAAwB,IAAI;QAC9B,KAAKrC,YAAA,CAAa9N,IAAA,CAAKiQ,eAAe;QACtC,KAAKf,IAAA,CAAK,wBAAwBe,eAAe;MACnD;IACF,CAAC;EACH;EAaQN,WAAWU,iBAAA,EAA2C;IAC5D,MAAML,oBAAA,GAA8C,EAAC;IAErD,CAAC,GAAG/P,kBAAA,EAAY,GAAGoQ,iBAAiB,EAAEhN,GAAA,CAAKmE,MAAA,IAAgC;MACzE,IAAI,KAAK0I,aAAA,CAAc1I,MAAA,CAAO1G,IAAI,GAAG;QACnC;MACF;MACA,MAAMwP,OAAA,GAAUzQ,8BAAA,CAA+B2H,MAAM;MACrD,IAAI8I,OAAA,EAAS;QACX9I,MAAA,CAAO1C,UAAA;QACPkL,oBAAA,CAAqBhQ,IAAA,CAAKwH,MAAM;QAChC,KAAK+G,2CAAA,CAA4C/G,MAAM;MACzD;IACF,CAAC;IAED,KAAKqG,iBAAA,GAAoBmC,oBAAA;IAEzB,KAAKD,yCAAA,CAA0C,KAAKlC,iBAAiB;EACvE;EAQAqC,cAAc9K,UAAA,EAA6B;IAGzC,IACE,KAAKwI,aAAA,CAAclH,MAAA,GAAS,KAC5B,CAAC,KAAKkH,aAAA,CAAcrD,QAAA,CAASnF,UAA8B,GAC3D;MACA,OAAO;IACT;IACA,OAAO;EACT;EAgDQmL,YAAYC,SAAA,EAAmBC,cAAA,EAAyB;IA9SlE,IAAAxN,EAAA,EAAAC,EAAA,EAAA2E,EAAA;IA+SI,KAAKyG,GAAA,CAAIlC,IAAA,CAAK,SAAS,kBAAkBoE,SAAA,IAAa;MACpDhJ,MAAA,GAAQvE,EAAA,QAAK8K,OAAA,KAAL,gBAAA9K,EAAA,CAAcnC,IAAA;MACtBiG,OAAA,GAAS7D,EAAA,QAAK+K,QAAA,KAAL,gBAAA/K,EAAA,CAAepC,IAAA;MACxB4P,WAAA,GAAa7I,EAAA,QAAKoG,QAAA,KAAL,gBAAApG,EAAA,CAAeX,GAAA;MAC5ByJ,oBAAA,EAAsBnS,2BAAA;MACtBoS,OAAA,EAAS;MACT,GAAGH;IACL,CAAC;EACH;EAOQI,mBAAmBrJ,MAAA,EAAiD;IAC1E,IAAI,CAACA,MAAA,EAAQ;MACX,MAAM,IAAIlG,uBAAA,CAAwB,EAAER,IAAA;IACtC;IACA,IACE,EACE0G,MAAA,CAAO1C,UAAA,kCACP0C,MAAA,CAAO1C,UAAA,mCAGT,MAAM,IAAI/D,mBAAA,CAAoB,mBAAmB,EAAED,IAAA;EACvD;EAOQgQ,oBACNrJ,OAAA,EACgC;IAChC,IAAI,CAACA,OAAA,EAAS;MACZ,MAAM,IAAIvG,kBAAA,CAAmB,oBAAoB,EAAEJ,IAAA;IACrD;EACF;EAKQiQ,gBAAA,EAAqD;IAC3D,IAAI,CAAC,KAAK1C,UAAA,IAAc,KAAKD,WAAA,IAAe,CAAC,KAAKL,OAAA,EAChD,MAAM,IAAIzM,uBAAA,CAAwB,EAAER,IAAA;IACtC,IACE,EACE,KAAKiN,OAAA,CAAQjJ,UAAA,kCACb,KAAKiJ,OAAA,CAAQjJ,UAAA,mCAGf,MAAM,IAAI/D,mBAAA,CAAoB,EAAED,IAAA;IAClC,OAAO;EACT;EAUQkQ,UAAA,EAAkB;IACxB,KAAK3C,UAAA,GAAa;IAClB,KAAK4C,SAAA,CAAU,IAAI;IACnB,KAAKC,UAAA,CAAW,IAAI;IACpB,KAAKC,UAAA,CAAW,IAAI;IACpB5L,kBAAA,CAAmB;EACrB;EAKA,MAAc6L,WAAA,EAA4B;IA3X5C,IAAAnO,EAAA;IA4XI,MAAIA,EAAA,QAAKgL,QAAA,KAAL,gBAAAhL,EAAA,CAAeoO,OAAA,KAAW,KAAKrD,QAAA,EAAU;MAE3C,IACE,CAAC1N,+BAAA,CAAgC,KAAK2N,QAAA,CAASoD,OAAA,KAC/C,CAACvK,cAAA,CAAe,KAAKmH,QAAQ,GAC7B;QACA,KAAKD,QAAA,CAASsD,OAAA,GAAU;QACxB;MACF;MAEA,MAAMtN,WAAA,GAAc,IAAI7E,YAAA,CAAY;QAClC4H,OAAA,EAAS3E,cAAA,CAAe,KAAK6L,QAAQ;MACvC,CAAC;MACD,MAAM7G,KAAA,GAAQ,IAAI/H,MAAA,CAAM2E,WAAW;MACnC,MAAMlD,IAAA,GAAO,MAAMsG,KAAA,CAAMmK,GAAA,CAAIC,cAAA,CAAe;QAC1C/F,OAAA,EAAS,KAAKuC,QAAA,CAASvC;MACzB,CAAC;MAED,KAAKuC,QAAA,CAASsD,OAAA,GAAUxQ,IAAA;IAC1B;EACF;EAOAmQ,UAAUzJ,MAAA,EAA6B;IACrC,KAAKuG,OAAA,GAAUvG,MAAA;EACjB;EAWA0J,WACEzJ,OAAA,EAKM;IA1aV,IAAAxE,EAAA;IA2aI,IAAIwE,OAAA,KAAY,MAAM;MACpB,KAAKuG,QAAA,GAAW;MAChB;IACF;IAGA,KAAI/K,EAAA,QAAK8K,OAAA,KAAL,gBAAA9K,EAAA,CAAcsK,eAAA,EAAiB;MAGjC,IAAI,YAAY9F,OAAA,EAAS;QACvB,MAAMgK,sBAAA,GACJhK,OAAA;QACF,IAAIgK,sBAAA,CAAuBrG,MAAA,KAAWxL,mBAAA,CAAmByL,QAAA,EAAU;UACjE,KAAK+C,WAAA,GAAc;UACnB,MAAM,IAAIpN,qBAAA,CAAsB,+BAA+B,EAC5DN,OAAA;QACL;QAEA,KAAKsN,QAAA,GAAW;UACdvC,OAAA,EAASgG,sBAAA,CAAuBhL,IAAA,CAAKgF,OAAA,CAAQlI,QAAA,CAAS;UACtD8G,SAAA,EAAWoH,sBAAA,CAAuBhL,IAAA,CAAK4D,SAAA,CAAU9G,QAAA,CAAS;UAC1D+N,OAAA,EAASG,sBAAA,CAAuBhL,IAAA,CAAK6K;QACvC;QACA;MACF,OAAO;QAEL,MAAMI,eAAA,GAAkBjK,OAAA;QACxB,KAAKuG,QAAA,GAAW;UACdvC,OAAA,EAASiG,eAAA,CAAgBjG,OAAA,CAAQlI,QAAA,CAAS;UAC1C8G,SAAA,EAAWqH,eAAA,CAAgBrH,SAAA,CAAU9G,QAAA,CAAS;UAC9C+N,OAAA,EAASI,eAAA,CAAgBJ;QAC3B;QACA;MACF;IACF;IAGA,KAAKtD,QAAA,GAAW;MAAE,GAAIvG;IAAwB;IAC9C;EACF;EAUA0J,WAAWpK,OAAA,EAAyD;IA5dtE,IAAA9D,EAAA,EAAAC,EAAA,EAAA2E,EAAA;IA6dI,IAAId,OAAA,KAAY,MAAM;MACpB,KAAKkH,QAAA,GAAW;MAChB;IACF;IACA,KAAIhL,EAAA,QAAK8K,OAAA,KAAL,gBAAA9K,EAAA,CAAcsK,eAAA,EAAiB;MACjC,MAAMoE,kBAAA,GAAqB5K,OAAA;MAC3B,KAAKwJ,WAAA,CAAY,kBAAkB;QACjC1G,IAAA,GAAM3G,EAAA,QAAK+K,QAAA,KAAL,gBAAA/K,EAAA,CAAepC,IAAA;QACrB8Q,EAAA,EAAID,kBAAA,CAAmB7Q;MACzB,CAAC;MACD,KAAKmN,QAAA,GAAW;QACdnN,IAAA,EAAM6Q,kBAAA,CAAmB7Q,IAAA,CAAKwO,WAAA,CAAY;QAC1C+B,OAAA,EAASM,kBAAA,CAAmBN,OAAA,CAAQ9N,QAAA,CAAS;QAC7C2D,GAAA,EAAKyK,kBAAA,CAAmBzK;MAC1B;MAEA;IACF;IAEA,KAAKqJ,WAAA,CAAY,kBAAkB;MACjC1G,IAAA,GAAMhC,EAAA,QAAKoG,QAAA,KAAL,gBAAApG,EAAA,CAAe/G,IAAA;MACrB8Q,EAAA,EAAI7K,OAAA,CAAQjG;IACd,CAAC;IACD,KAAKmN,QAAA,GAAW;MACd,GAAIlH,OAAA;MACJjG,IAAA,EAAMiG,OAAA,CAAQjG,IAAA,CAAKwO,WAAA,CAAY;IACjC;EACF;EAOAuC,YAAA,EAAuB;IACrB,OAAO,KAAKxD,UAAA;EACd;EAKA,IAAIyD,QAAA,EAAgE;IAClE,OAAO,KAAKhE,YAAA;EACd;EAKA,IAAIiE,cAAA,EAAuC;IACzC,OAAO,KAAKpE,QAAA;EACd;EAKA,IAAIqE,gBAAA,EAAsD;IACxD,OAAO,KAAKnE,iBAAA;EACd;EAQA,IAAIrG,OAAA,EAA4B;IAC9B,IAAI;MACF,IAAI,CAAC,KAAKuG,OAAA,EAAS,OAAO;MAC1B,OAAO;QACLjN,IAAA,EAAM,KAAKiN,OAAA,CAAQjN,IAAA;QACnBwM,IAAA,EAAM,KAAKS,OAAA,CAAQT,IAAA;QACnBpG,GAAA,EAAK,KAAK6G,OAAA,CAAQ7G;MACpB;IACF,SAASvG,KAAA,EAAP;MACA,MAAM,IAAIC,sBAAA,CAAuBD,KAAK,EAAED,OAAA;IAC1C;EACF;EAQA,IAAI+G,QAAA,EAA8B;IAChC,IAAI;MACF,OAAO,KAAKuG,QAAA;IACd,SAASrN,KAAA,EAAP;MACA,MAAM,IAAIO,kBAAA,CAAmBP,KAAK,EAAED,OAAA;IACtC;EACF;EAQA,IAAIqG,QAAA,EAA8B;IAChC,IAAI;MACF,OAAO,KAAKkH,QAAA;IACd,SAAStN,KAAA,EAAP;MACA,MAAM,IAAIQ,qBAAA,CAAsBR,KAAK,EAAED,OAAA;IACzC;EACF;EAOA,MAAM6G,QAAQnC,UAAA,EAA4C;IA5kB5D,IAAAnC,EAAA;IA+kBI,MAAMgP,kBAAA,GAAqB,KAAKnE,YAAA;IAEhC,MAAMoE,cAAA,GAAiBD,kBAAA,CAAmBE,IAAA,CACvC3K,MAAA,IAAmBA,MAAA,CAAO1G,IAAA,KAASsE,UACtC;IACA,IAAI,CAAC8M,cAAA,EAAgB;IAGrB,IAAI,KAAK7D,UAAA,EAAY;MAEnB,MAAIpL,EAAA,QAAK8K,OAAA,KAAL,gBAAA9K,EAAA,CAAcnC,IAAA,MAASsE,UAAA,EACzB,MAAM,IAAIpE,qBAAA,CACR,GAAGoE,UAAA,8BACL,EAAE1E,OAAA;IACN;IAIA,IACE4F,cAAA,CAAe,KACf4L,cAAA,CAAepN,UAAA,kCACf;MAEA,IAAIoN,cAAA,CAAe3E,eAAA,IAAmB2E,cAAA,CAAenD,eAAA,EAAiB;QACpEmD,cAAA,CAAenD,eAAA,CAAgB;QAC/B;MACF;MACA,IAAImD,cAAA,CAAeE,gBAAA,EAAkB;QACnC,MAAMlL,GAAA,GAAMmL,kBAAA,CAAmBhO,MAAA,CAAOiO,QAAA,CAASC,IAAI;QACnD,MAAMD,QAAA,GAAWJ,cAAA,CAAeE,gBAAA,CAAiB;UAAElL;QAAI,CAAC;QACxD7C,MAAA,CAAOiO,QAAA,CAASC,IAAA,GAAOD,QAAA;MACzB;MACA;IACF;IAGA,IACEJ,cAAA,CAAepN,UAAA,oCACfoN,cAAA,CAAepN,UAAA,gCACf;MACA;IACF;IAGA,MAAM,KAAK0N,aAAA,CAAcN,cAAc;EACzC;EAWA,MAAMM,cAAcN,cAAA,EAAuC;IACzD,IAAI;MACF,KAAK9D,WAAA,GAAc;MACnB,KAAK6C,SAAA,CAAUiB,cAAc;MAC7B,IAAIzK,OAAA;MACJ,IAAIyK,cAAA,CAAe3E,eAAA,EAAiB;QAClC9F,OAAA,GAAU,MAAM,KAAK0G,kBAAA,CAAmB5G,OAAA,CAAQ2K,cAAc;MAChE,OAAO;QACLzK,OAAA,GAAU,MAAM,KAAKyG,YAAA,CAAa3G,OAAA,CAAQ2K,cAAc;MAC1D;MACA,KAAKhB,UAAA,CAAWzJ,OAAO;MACvB,MAAMV,OAAA,GAAU,MAAMmL,cAAA,CAAenL,OAAA,CAAQ;MAC7C,KAAKoK,UAAA,CAAWpK,OAAO;MACvB,MAAM,KAAKqK,UAAA,CAAW;MACtBjM,eAAA,CAAgB+M,cAAA,CAAepR,IAAI;MACnC,KAAKuN,UAAA,GAAa;MAClB,KAAKkC,WAAA,CAAY,gBAAgB;MACjC,KAAKrB,IAAA,CAAK,WAAWzH,OAAO;IAC9B,SAAS9G,KAAA,EAAP;MACA,KAAKqQ,SAAA,CAAU;MACf,MAAMjI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIK,qBAAA,CAAsB+H,MAAM,EAAErI,OAAA;IAC1C,UAAE;MACA,KAAK0N,WAAA,GAAc;IACrB;EACF;EASA,MAAMQ,WAAA,EAA4B;IAChC,IAAI;MACF,KAAKiC,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,MAAM,KAAKA,OAAA,CAAQa,UAAA,CAAW;MAC9B,KAAKoC,SAAA,CAAU;MACf,KAAKT,WAAA,CAAY,mBAAmB;MACpC,KAAKrB,IAAA,CAAK,YAAY;IACxB,SAASvO,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIM,wBAAA,CAAyB8H,MAAM,EAAErI,OAAA;IAC7C;EACF;EASA,MAAMkI,yBACJhB,gBAAA,EAGA;IACA,IAAI;MACF,IAAI,cAAcA,gBAAA,CAAiB8D,IAAA,EAAM;QACvC,IACE9D,gBAAA,CAAiB8D,IAAA,CAAK9H,QAAA,KACtB,gDACA;UACA,MAAM,IAAInC,+BAAA,CAAgC,oBAAoB,EAC3Df,OAAA;QACL;MACF;MAEA,KAAKmQ,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,KAAK+C,mBAAA,CAAoB,KAAK9C,QAAQ;MACtC,KAAKuC,WAAA,CAAY,6BAA6B;MAE9C,MAAM5I,WAAA,GAAcC,gBAAA,CAAiB8D,IAAA;MACrC,MAAM1H,WAAA,GAAc6C,cAAA,CAAe,KAAKoH,QAAQ;MAEhD,MAAM7G,KAAA,GAAQ,IAAI/H,MAAA,CAAM2E,WAAW;MAEnC,IAAI,KAAK+J,OAAA,CAAQnF,wBAAA,EAA0B;QAEzC,IAAI,KAAKmF,OAAA,CAAQR,eAAA,EAAiB;UAChC,MAAM;YAAEkF,IAAA;YAAA,GAASC;UAAO,IACtB,MAAM,KAAKvE,kBAAA,CAAmBvF,wBAAA,CAC5BhB,gBAAA,EACAR,KAAA,EACA,KAAK4G,QAAA,EACL,KAAKD,OACP;UACF,OAAO;YAAE0E,IAAA;YAAMC;UAAO;QACxB,OAAO;UAEL,MAAM;YAAED,IAAA;YAAA,GAASC;UAAO,IACtB,MAAM,KAAKxE,YAAA,CAAaxG,+BAAA,CACtBC,WAAA,EACA,KAAKsG,QAAA,EACL,KAAKF,OAAA,EACLnG,gBACF;UACF,OAAO;YAAE6K,IAAA;YAAMC;UAAO;QACxB;MACF;MAMA,MAAM7J,WAAA,GAAc,MAAMzB,KAAA,CAAMyB,WAAA,CAAYyC,KAAA,CAAMC,MAAA,CAAO;QACvDC,MAAA,EAAQ,KAAKwC,QAAA,CAASvC,OAAA;QACtBC,IAAA,EAAM9D,gBAAA,CAAiB8D,IAAA;QACvBpD,OAAA,EAASV,gBAAA,CAAiBU;MAC5B,CAAC;MAED,MAAMqK,mBAAA,GAAsB,MAAM,KAAK3J,eAAA,CAAgBH,WAAW;MAClE,MAAMC,QAAA,GAAW,MAAM,KAAK8J,iBAAA,CAAkB;QAC5C/J,WAAA;QACA8J;MACF,CAAC;MACD,OAAO7J,QAAA;IACT,SAASnI,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIc,+BAAA,CAAgCsH,MAAM,EAAErI,OAAA;IACpD;EACF;EAYA,MAAMsI,gBACJ6J,oBAAA,EACAlH,UAAA,EACArD,OAAA,EAC+B;IAlxBnC,IAAArF,EAAA,EAAAC,EAAA,EAAA2E,EAAA,EAAAC,EAAA;IAmxBI,IAAI;MACF,KAAK+I,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,KAAKwC,WAAA,CAAY,kBAAkB;MAEnC,IAAI,KAAKxC,OAAA,CAAQ/E,eAAA,EAAiB;QAIhC,IAAI,KAAK+E,OAAA,CAAQR,eAAA,EAAiB;UAGhC,IAAI,oBAAoBsF,oBAAA,EAAsB;YAC5C,OAAO,MAAM,KAAK1E,kBAAA,CAAmBnF,eAAA,CACnC6J,oBAAA,EACA,KAAK9E,OAAA,EACLpC,UACF;UACF,OAAO;YACL,MAAM3H,WAAA,GAAc6C,cAAA,CAAe,KAAKoH,QAAQ;YAChD,KAAK6C,mBAAA,CAAoB,KAAK9C,QAAQ;YACtC,MAAMxC,MAAA,GAAS,KAAKwC,QAAA,CAASvC,OAAA;YAC7B,MAAM/I,OAAA,GAAU,MAAMqB,qCAAA,CACpBC,WAAA,EACA6O,oBACF;YACA,MAAMC,SAAA,GAAYxK,OAAA;YAClB,MAAMyK,SAAA,GAAY;cAChBC,qBAAA,EAAuBF,SAAA,oBAAAA,SAAA,CAAWG,cAAA;cAClCC,eAAA,GACEjQ,EAAA,GAAA6P,SAAA,oBAAAA,SAAA,CAAWI,eAAA,KAAX,OAAAjQ,EAAA,GAA8B6P,SAAA,oBAAAA,SAAA,CAAWK,mBAAA;cAC3CzK,YAAA,GACExF,EAAA,GAAA4P,SAAA,oBAAAA,SAAA,CAAWpK,YAAA,KAAX,OAAAxF,EAAA,GAA2B4P,SAAA,oBAAAA,SAAA,CAAWrK,cAAA;cACxCF,YAAA,GACEV,EAAA,GAAAiL,SAAA,oBAAAA,SAAA,CAAWvK,YAAA,KAAX,OAAAV,EAAA,GAA2BiL,SAAA,oBAAAA,SAAA,CAAWzK;YAC1C;YACA,MAAM+K,cAAA,GAAiB,MAAM9T,sBAAA,CAAuB;cAClD0E,WAAA;cACAtB,OAAA;cACA8I,MAAA;cACAlD,OAAA,EAASyK;YACX,CAAC;YACD,OAAO,MAAM,KAAK5E,kBAAA,CAAmBnF,eAAA,CACnC,IAAIzJ,iBAAA,CAAkB6T,cAAc,GACpC,KAAKrF,OAAA,EACL,KACF;UACF;QACF;QAKA,IAAI,oBAAoB8E,oBAAA,EAAsB;UAC5C,MAAMQ,oBAAA,GAAwB,MAAM,KAAKtF,OAAA,CAAQ/E,eAAA,CAC/C6J,oBAAA,EACAlH,UACF;UAEA,OAAO0H,oBAAA;QACT,OAAO;UACL,MAAMvK,QAAA,GAAW,MAAM,KAAKoF,YAAA,CAAalF,eAAA,CACvC6J,oBAAA,EACA,KAAK9E,OAAA,EACL;YACE1F,cAAA,GAAgBC,OAAA,oBAAAA,OAAA,CAASC,YAAA,IACrBC,MAAA,CAAOF,OAAA,oBAAAA,OAAA,CAASC,YAAY,IAC5B;YACJE,cAAA,GAAgBH,OAAA,oBAAAA,OAAA,CAASI,YAAA,IACrBF,MAAA,CAAOF,OAAA,oBAAAA,OAAA,CAASI,YAAY,IAC5B;UACN,CACF;UAEA,IAAI,CAACI,QAAA,EAAU;YACb,MAAM,IAAItI,KAAA,CAAM,OAAO;UACzB;UAGA,MAAM8S,aAAA,GAAgB,IAAI1U,IAAA,CAAIgE,YAAA,CAAakG,QAAQ;UACnD,MAAMyK,qBAAA,GACJ7U,gBAAA,CAAgB8U,iBAAA,CAAkBzQ,WAAA,CAAYuQ,aAAa;UAC7D,MAAMG,wBAAA,GACJF,qBAAA,CAAsBG,aAAA;UAExB,MAAMrJ,SAAA,GAAYoJ,wBAAA,CAAyBE,UAAA,CAAWC,KAAA;UACtD,MAAMpK,SAAA,GAAYiK,wBAAA,CAAyBjK,SAAA,CAAUoK,KAAA;UAErD,MAAMP,oBAAA,GAAuB,IAAItU,2BAAA,CAC/B,IAAIC,gBAAA,CAAiBqL,SAAS,GAC9B,IAAIpL,gBAAA,CAAiBuK,SAAS,CAChC;UACA,OAAO6J,oBAAA;QACT;MACF;MAGA,MAAM,IAAI1R,wBAAA,CACR,yCAAwCmG,EAAA,QAAKN,MAAA,KAAL,gBAAAM,EAAA,CAAahH,IAAA,EACvD,EAAEJ,OAAA;IACJ,SAASC,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIe,0BAAA,CAA2BqH,MAAM,EAAErI,OAAA;IAC/C;EACF;EASA,MAAMwI,YAAYxI,OAAA,EAA2D;IAC3E,IAAI;MACF,KAAKmQ,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,KAAKwC,WAAA,CAAY,cAAc;MAC/B,IAAI,KAAKxC,OAAA,CAAQR,eAAA,EAAiB;QAChC,OAAO,MAAM,KAAKY,kBAAA,CAAmBjF,WAAA,CAAYxI,OAAA,EAAS,KAAKqN,OAAO;MACxE;MACA,MAAMjF,QAAA,GAAW,MAAM,KAAKiF,OAAA,CAAS7E,WAAA,CAAYxI,OAAO;MACxD,OAAOoI,QAAA;IACT,SAASnI,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIY,sBAAA,CAAuBwH,MAAM,EAAErI,OAAA;IAC3C;EACF;EAQA,MAAMkS,kBACJ/J,WAAA,EACqC;IACrC,IAAI;MACF,KAAKgI,kBAAA,CAAmB,KAAK9C,OAAO;MAEpC,MAAM;QAAE8F;MAAgC,IAAIhL,WAAA;MAC5C,MAAMiL,eAAA,GACJD,+BAAA,KAAoC,SAChC,gBACA;MACN,KAAKtD,WAAA,CAAY,sBAAsB;QACrCwD,gBAAA,EAAkBD;MACpB,CAAC;MAED,IAAI,KAAK/F,OAAA,CAAQ6E,iBAAA,EAAmB;QAClC,MAAMoB,kBAAA,GACJ,MAAM,KAAKjG,OAAA,CAAQ6E,iBAAA,CAAkB/J,WAAW;QAClD,OAAOmL,kBAAA;MACT;MAIA,MAAMhQ,WAAA,GAAc,IAAI7E,YAAA,CAAY;QAClC4H,OAAA,EAAS3E,cAAA,CAAe,KAAK2E,OAAO;MACtC,CAAC;MACD,MAAMK,KAAA,GAAQ,IAAI/H,MAAA,CAAM2E,WAAW;MACnC,IAAI6P,+BAAA,KAAoC,QAAW;QACjD,MAAMI,aAAA,GAAgB;UACpB,GAAGpL,WAAA;UACHgL;QACF;QACA,OAAOzM,KAAA,CAAMyB,WAAA,CAAYqL,MAAA,CAAOC,UAAA,CAAWF,aAAa;MAC1D,OAAO;QACL,OAAO7M,KAAA,CAAMyB,WAAA,CAAYqL,MAAA,CAAO3I,MAAA,CAAO1C,WAAW;MACpD;IACF,SAASlI,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIe,0BAAA,CAA2BqH,MAAM,EAAErI,OAAA;IAC/C;EACF;EAOA,MAAMmO,gBAAA,EAAiC;IACrC,IAAI;MACF,KAAKgC,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,MAAM,KAAKA,OAAA,CAAQc,eAAA,CACjB,MAAOnD,IAAA,IAA4C;QACjD,KAAKwF,UAAA,CAAWxF,IAAI;QACpB,MAAM,KAAK0F,UAAA,CAAW;QACtB,KAAKb,WAAA,CAAY,gBAAgB;QACjC,KAAKrB,IAAA,CAAK,iBAAiB,KAAKlB,QAAQ;MAC1C,CACF;IACF,SAASrN,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIS,wBAAA,CAAyB2H,MAAM,EAAErI,OAAA;IAC7C;EACF;EAOA,MAAMoO,gBAAA,EAAiC;IACrC,IAAI;MACF,KAAK+B,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,MAAM,KAAKA,OAAA,CAAQe,eAAA,CACjB,MAAOpD,IAAA,IAA4C;QACjD,KAAKyF,UAAA,CAAWzF,IAAI;QACpB,MAAM,KAAK0F,UAAA,CAAW;QACtB,KAAKlC,IAAA,CAAK,iBAAiB,KAAKjB,QAAQ;MAC1C,CACF;IACF,SAAStN,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIU,wBAAA,CAAyB0H,MAAM,EAAErI,OAAA;IAC7C;EACF;EAQA,MAAMsO,cAAcjI,OAAA,EAAqD;IAn/B3E,IAAA9D,EAAA;IAo/BI,IAAI;MACF,KAAK4N,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,KAAKwC,WAAA,CAAY,0BAA0B;QACzC1G,IAAA,GAAM5G,EAAA,QAAKgL,QAAA,KAAL,gBAAAhL,EAAA,CAAenC,IAAA;QACrB8Q,EAAA,EAAI7K;MACN,CAAC;MACD,MAAMsK,OAAA,GACJtK,OAAA,KAAYjI,QAAA,CAAQ0D,MAAA,GAChB,MAAM2E,kBAAA,CAAmB,IACzB3H,gBAAA,CAAiBuH,OAAA;MACvB,IAAI,KAAKgH,OAAA,CAAQiB,aAAA,EAAe;QAC9B,MAAM3M,WAAA,GAAmC;UACvCvB,IAAA,EAAMiG,OAAA;UACNsK;QACF;QACA,MAAMvI,QAAA,GAAW,MAAM,KAAKiF,OAAA,CAAQiB,aAAA,CAAc3M,WAAW;QAC7D,IAAIyG,QAAA,CAASsC,MAAA,KAAWxL,mBAAA,CAAmByL,QAAA,EAAU;UACnD,MAAM,IAAIrK,qBAAA,CAAsB,+BAA+B,EAC5DN,OAAA;QACL;QACA,OAAOoI,QAAA,CAASrC,IAAA;MAClB;MACA,MAAM,IAAI7E,wBAAA,CACR,GAAG,KAAKmM,OAAA,CAAQjN,IAAA,4CAClB,EAAEJ,OAAA;IACJ,SAASC,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIiB,wBAAA,CAAyBmH,MAAM,EAAErI,OAAA;IAC7C;EACF;EAOA,MAAMuI,qBAAqBvI,OAAA,EAA+C;IACxE,IAAI;MACF,KAAKmQ,kBAAA,CAAmB,KAAK9C,OAAO;MACpC,KAAK+C,mBAAA,CAAoB,KAAK9C,QAAQ;MACtC,KAAKuC,WAAA,CAAY,yBAAyB;MAE1C,IAAI,KAAKxC,OAAA,CAAQR,eAAA,EAAiB;QAChC,OAAO,KAAKY,kBAAA,CAAmBlF,oBAAA,CAC7BvI,OAAA,EACA,KAAKqN,OACP;MACF;MAEA,OAAO,MAAM,KAAKG,YAAA,CAAajF,oBAAA,CAC7BvI,OAAA,EACA,KAAKqN,OAAA,EACL,KAAKC,QACP;IACF,SAASrN,KAAA,EAAP;MACA,MAAMoI,MAAA,GAASxC,uBAAA,CAAwB5F,KAAK;MAC5C,MAAM,IAAIa,+BAAA,CAAgCuH,MAAM,EAAErI,OAAA;IACpD;EACF;AACF;;;AaviCA,IAAI,OAAO2D,MAAA,KAAW,aAAa;EAChCA,MAAA,CAAe7F,2BAAA,GAA8BA,2BAAA;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}