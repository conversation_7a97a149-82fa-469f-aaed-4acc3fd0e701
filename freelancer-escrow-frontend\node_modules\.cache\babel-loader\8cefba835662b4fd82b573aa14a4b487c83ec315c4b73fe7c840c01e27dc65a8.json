{"ast": null, "code": "import { a } from \"./chunk-GHYE26Q5.mjs\";\nimport { a as p } from \"./chunk-Z4YHE4A5.mjs\";\nvar l = {\n  400: \"Bad Request\",\n  401: \"Unauthorized\",\n  403: \"Forbidden\",\n  404: \"Not Found\",\n  429: \"Too Many Requests\",\n  500: \"Internal Server Error\",\n  502: \"Bad Gateway\",\n  503: \"Service Unavailable\"\n};\nasync function c(s, R) {\n  let {\n      url: o,\n      method: i,\n      body: u,\n      contentType: d,\n      params: t,\n      overrides: e,\n      originMethod: n\n    } = s,\n    r = {\n      ...e?.HEADERS,\n      \"x-aptos-client\": `aptos-typescript-sdk/${p}`,\n      \"content-type\": d ?? \"application/json\",\n      \"x-aptos-typescript-sdk-origin-method\": n\n    };\n  return e?.AUTH_TOKEN && (r.Authorization = `Bearer ${e?.AUTH_TOKEN}`), e?.API_KEY && (r.Authorization = `Bearer ${e?.API_KEY}`), R.provider({\n    url: o,\n    method: i,\n    body: u,\n    params: t,\n    headers: r,\n    overrides: e\n  });\n}\nasync function N(s, R, o) {\n  let {\n      url: i,\n      path: u\n    } = s,\n    d = u ? `${i}/${u}` : i,\n    t = await c({\n      ...s,\n      url: d\n    }, R.client),\n    e = {\n      status: t.status,\n      statusText: t.statusText,\n      data: t.data,\n      headers: t.headers,\n      config: t.config,\n      request: t.request,\n      url: d\n    };\n  if (e.status === 401) throw new a(s, e, `Error: ${e.data}`);\n  if (o === \"Indexer\") {\n    let r = e.data;\n    if (r.errors) throw new a(s, e, `Indexer error: ${r.errors[0].message}` ?? `Indexer unhandled Error ${t.status} : ${t.statusText}`);\n    e.data = r.data;\n  } else if ((o === \"Pepper\" || o === \"Prover\") && e.status >= 400) throw new a(s, e, `${t.data}`);\n  if (e.status >= 200 && e.status < 300) return e;\n  let n;\n  throw e && e.data && \"message\" in e.data && \"error_code\" in e.data ? n = JSON.stringify(e.data) : e.status in l ? n = l[e.status] : n = `Unhandled Error ${e.status} : ${e.statusText}`, new a(s, e, `${o} error: ${n}`);\n}\nexport { c as a, N as b };", "map": {"version": 3, "names": ["l", "c", "s", "R", "url", "o", "method", "i", "body", "u", "contentType", "d", "params", "t", "overrides", "e", "originMethod", "n", "r", "HEADERS", "p", "AUTH_TOKEN", "Authorization", "API_KEY", "provider", "headers", "N", "path", "client", "status", "statusText", "data", "config", "request", "a", "errors", "message", "JSON", "stringify", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\client\\core.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { AptosApiError, AptosResponse } from \"./types\";\nimport { VERSION } from \"../version\";\nimport { AnyNumber, AptosRequest, Client, ClientRequest, ClientResponse, MimeType } from \"../types\";\nimport { AptosApiType } from \"../utils\";\n\n/**\n * Meaningful errors map\n */\nconst errors: Record<number, string> = {\n  400: \"Bad Request\",\n  401: \"Unauthorized\",\n  403: \"Forbidden\",\n  404: \"Not Found\",\n  429: \"Too Many Requests\",\n  500: \"Internal Server Error\",\n  502: \"Bad Gateway\",\n  503: \"Service Unavailable\",\n};\n\n/**\n * Given a url and method, sends the request with axios and\n * returns the response.\n */\nexport async function request<Req, Res>(options: ClientRequest<Req>, client: Client): Promise<ClientResponse<Res>> {\n  const { url, method, body, contentType, params, overrides, originMethod } = options;\n  const headers: Record<string, string | AnyNumber | boolean | undefined> = {\n    ...overrides?.HEADERS,\n    \"x-aptos-client\": `aptos-typescript-sdk/${VERSION}`,\n    \"content-type\": contentType ?? MimeType.JSON,\n    \"x-aptos-typescript-sdk-origin-method\": originMethod,\n  };\n\n  if (overrides?.AUTH_TOKEN) {\n    headers.Authorization = `Bearer ${overrides?.AUTH_TOKEN}`;\n  }\n  if (overrides?.API_KEY) {\n    headers.Authorization = `Bearer ${overrides?.API_KEY}`;\n  }\n\n  /*\n   * make a call using the @aptos-labs/aptos-client package\n   * {@link https://www.npmjs.com/package/@aptos-labs/aptos-client}\n   */\n  return client.provider<Req, Res>({\n    url,\n    method,\n    body,\n    params,\n    headers,\n    overrides,\n  });\n}\n\n/**\n * The main function to use when doing an API request.\n *\n * @param options AptosRequest\n * @param aptosConfig The config information for the SDK client instance\n * @returns the response or AptosApiError\n */\nexport async function aptosRequest<Req extends {}, Res extends {}>(\n  options: AptosRequest,\n  aptosConfig: AptosConfig,\n  apiType: AptosApiType,\n): Promise<AptosResponse<Req, Res>> {\n  const { url, path } = options;\n  const fullUrl = path ? `${url}/${path}` : url;\n  const response = await request<Req, Res>({ ...options, url: fullUrl }, aptosConfig.client);\n\n  const result: AptosResponse<Req, Res> = {\n    status: response.status,\n    statusText: response.statusText!,\n    data: response.data,\n    headers: response.headers,\n    config: response.config,\n    request: response.request,\n    url: fullUrl,\n  };\n\n  // Handle case for `Unauthorized` error (i.e API_KEY error)\n  if (result.status === 401) {\n    throw new AptosApiError(options, result, `Error: ${result.data}`);\n  }\n\n  // to support both fullnode and indexer responses,\n  // check if it is an indexer query, and adjust response.data\n  if (apiType === AptosApiType.INDEXER) {\n    const indexerResponse = result.data as any;\n    // Handle Indexer general errors\n    if (indexerResponse.errors) {\n      throw new AptosApiError(\n        options,\n        result,\n        `Indexer error: ${indexerResponse.errors[0].message}` ??\n          `Indexer unhandled Error ${response.status} : ${response.statusText}`,\n      );\n    }\n    result.data = indexerResponse.data as Res;\n  } else if (apiType === AptosApiType.PEPPER || apiType === AptosApiType.PROVER) {\n    if (result.status >= 400) {\n      throw new AptosApiError(options, result, `${response.data}`);\n    }\n  }\n\n  if (result.status >= 200 && result.status < 300) {\n    return result;\n  }\n\n  let errorMessage: string;\n\n  if (result && result.data && \"message\" in result.data && \"error_code\" in result.data) {\n    errorMessage = JSON.stringify(result.data);\n  } else if (result.status in errors) {\n    // If it's not an API type, it must come form infra, these are prehandled\n    errorMessage = errors[result.status];\n  } else {\n    // Everything else is unhandled\n    errorMessage = `Unhandled Error ${result.status} : ${result.statusText}`;\n  }\n\n  // We have to explicitly check for all request types, because if the error is a non-indexer error, but\n  // comes from an indexer request (e.g. 404), we'll need to mention it appropriately\n  throw new AptosApiError(options, result, `${apiType} error: ${errorMessage}`);\n}\n"], "mappings": ";;AAYA,IAAMA,CAAA,GAAiC;EACrC,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACP;AAMA,eAAsBC,EAAkBC,CAAA,EAA6BC,CAAA,EAA8C;EACjH,IAAM;MAAEC,GAAA,EAAAC,CAAA;MAAKC,MAAA,EAAAC,CAAA;MAAQC,IAAA,EAAAC,CAAA;MAAMC,WAAA,EAAAC,CAAA;MAAaC,MAAA,EAAAC,CAAA;MAAQC,SAAA,EAAAC,CAAA;MAAWC,YAAA,EAAAC;IAAa,IAAIf,CAAA;IACtEgB,CAAA,GAAoE;MACxE,GAAGH,CAAA,EAAWI,OAAA;MACd,kBAAkB,wBAAwBC,CAAO;MACjD,gBAAgBT,CAAA;MAChB,wCAAwCM;IAC1C;EAEA,OAAIF,CAAA,EAAWM,UAAA,KACbH,CAAA,CAAQI,aAAA,GAAgB,UAAUP,CAAA,EAAWM,UAAU,KAErDN,CAAA,EAAWQ,OAAA,KACbL,CAAA,CAAQI,aAAA,GAAgB,UAAUP,CAAA,EAAWQ,OAAO,KAO/CpB,CAAA,CAAOqB,QAAA,CAAmB;IAC/BpB,GAAA,EAAAC,CAAA;IACAC,MAAA,EAAAC,CAAA;IACAC,IAAA,EAAAC,CAAA;IACAG,MAAA,EAAAC,CAAA;IACAY,OAAA,EAAAP,CAAA;IACAJ,SAAA,EAAAC;EACF,CAAC,CACH;AAAA;AASA,eAAsBW,EACpBxB,CAAA,EACAC,CAAA,EACAE,CAAA,EACkC;EAClC,IAAM;MAAED,GAAA,EAAAG,CAAA;MAAKoB,IAAA,EAAAlB;IAAK,IAAIP,CAAA;IAChBS,CAAA,GAAUF,CAAA,GAAO,GAAGF,CAAG,IAAIE,CAAI,KAAKF,CAAA;IACpCM,CAAA,GAAW,MAAMZ,CAAA,CAAkB;MAAE,GAAGC,CAAA;MAASE,GAAA,EAAKO;IAAQ,GAAGR,CAAA,CAAYyB,MAAM;IAEnFb,CAAA,GAAkC;MACtCc,MAAA,EAAQhB,CAAA,CAASgB,MAAA;MACjBC,UAAA,EAAYjB,CAAA,CAASiB,UAAA;MACrBC,IAAA,EAAMlB,CAAA,CAASkB,IAAA;MACfN,OAAA,EAASZ,CAAA,CAASY,OAAA;MAClBO,MAAA,EAAQnB,CAAA,CAASmB,MAAA;MACjBC,OAAA,EAASpB,CAAA,CAASoB,OAAA;MAClB7B,GAAA,EAAKO;IACP;EAGA,IAAII,CAAA,CAAOc,MAAA,KAAW,KACpB,MAAM,IAAIK,CAAA,CAAchC,CAAA,EAASa,CAAA,EAAQ,UAAUA,CAAA,CAAOgB,IAAI,EAAE;EAKlE,IAAI1B,CAAA,gBAAkC;IACpC,IAAMa,CAAA,GAAkBH,CAAA,CAAOgB,IAAA;IAE/B,IAAIb,CAAA,CAAgBiB,MAAA,EAClB,MAAM,IAAID,CAAA,CACRhC,CAAA,EACAa,CAAA,EACA,kBAAkBG,CAAA,CAAgBiB,MAAA,CAAO,CAAC,EAAEC,OAAO,MACjD,2BAA2BvB,CAAA,CAASgB,MAAM,MAAMhB,CAAA,CAASiB,UAAU,EACvE;IAEFf,CAAA,CAAOgB,IAAA,GAAOb,CAAA,CAAgBa,IAChC;EAAA,YAAW1B,CAAA,iBAAmCA,CAAA,kBACxCU,CAAA,CAAOc,MAAA,IAAU,KACnB,MAAM,IAAIK,CAAA,CAAchC,CAAA,EAASa,CAAA,EAAQ,GAAGF,CAAA,CAASkB,IAAI,EAAE;EAI/D,IAAIhB,CAAA,CAAOc,MAAA,IAAU,OAAOd,CAAA,CAAOc,MAAA,GAAS,KAC1C,OAAOd,CAAA;EAGT,IAAIE,CAAA;EAEJ,MAAIF,CAAA,IAAUA,CAAA,CAAOgB,IAAA,IAAQ,aAAahB,CAAA,CAAOgB,IAAA,IAAQ,gBAAgBhB,CAAA,CAAOgB,IAAA,GAC9Ed,CAAA,GAAeoB,IAAA,CAAKC,SAAA,CAAUvB,CAAA,CAAOgB,IAAI,IAChChB,CAAA,CAAOc,MAAA,IAAU7B,CAAA,GAE1BiB,CAAA,GAAejB,CAAA,CAAOe,CAAA,CAAOc,MAAM,IAGnCZ,CAAA,GAAe,mBAAmBF,CAAA,CAAOc,MAAM,MAAMd,CAAA,CAAOe,UAAU,IAKlE,IAAII,CAAA,CAAchC,CAAA,EAASa,CAAA,EAAQ,GAAGV,CAAO,WAAWY,CAAY,EAAE,CAC9E;AAAA;AAAA,SAAAhB,CAAA,IAAAiC,CAAA,EAAAR,CAAA,IAAAa,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}