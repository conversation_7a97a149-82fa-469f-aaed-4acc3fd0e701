{"ast": null, "code": "import { b as h } from \"./chunk-F74FF323.mjs\";\nimport { a as y } from \"./chunk-BOYYQAB4.mjs\";\nimport { b as n } from \"./chunk-BF46IXHH.mjs\";\nimport { a as _ } from \"./chunk-A63SMUOU.mjs\";\nvar t = class i extends _ {\n    constructor(e, s, a, r, c, d, l) {\n      super(), this.sender = e, this.sequence_number = s, this.payload = a, this.max_gas_amount = r, this.gas_unit_price = c, this.expiration_timestamp_secs = d, this.chain_id = l;\n    }\n    serialize(e) {\n      this.sender.serialize(e), e.serializeU64(this.sequence_number), this.payload.serialize(e), e.serializeU64(this.max_gas_amount), e.serializeU64(this.gas_unit_price), e.serializeU64(this.expiration_timestamp_secs), this.chain_id.serialize(e);\n    }\n    static deserialize(e) {\n      let s = n.deserialize(e),\n        a = e.deserializeU64(),\n        r = h.deserialize(e),\n        c = e.deserializeU64(),\n        d = e.deserializeU64(),\n        l = e.deserializeU64(),\n        m = y.deserialize(e);\n      return new i(s, a, r, c, d, l, m);\n    }\n  },\n  o = class extends _ {\n    static deserialize(e) {\n      let s = e.deserializeUleb128AsU32();\n      switch (s) {\n        case 0:\n          return u.load(e);\n        case 1:\n          return p.load(e);\n        default:\n          throw new Error(`Unknown variant index for RawTransactionWithData: ${s}`);\n      }\n    }\n  },\n  u = class i extends o {\n    constructor(e, s) {\n      super(), this.raw_txn = e, this.secondary_signer_addresses = s;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(0), this.raw_txn.serialize(e), e.serializeVector(this.secondary_signer_addresses);\n    }\n    static load(e) {\n      let s = t.deserialize(e),\n        a = e.deserializeVector(n);\n      return new i(s, a);\n    }\n  },\n  p = class i extends o {\n    constructor(e, s, a) {\n      super(), this.raw_txn = e, this.secondary_signer_addresses = s, this.fee_payer_address = a;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(1), this.raw_txn.serialize(e), e.serializeVector(this.secondary_signer_addresses), this.fee_payer_address.serialize(e);\n    }\n    static load(e) {\n      let s = t.deserialize(e),\n        a = e.deserializeVector(n),\n        r = n.deserialize(e);\n      return new i(s, a, r);\n    }\n  };\nexport { t as a, o as b, u as c, p as d };", "map": {"version": 3, "names": ["t", "i", "_", "constructor", "e", "s", "a", "r", "c", "d", "l", "sender", "sequence_number", "payload", "max_gas_amount", "gas_unit_price", "expiration_timestamp_secs", "chain_id", "serialize", "serializeU64", "deserialize", "n", "deserializeU64", "h", "m", "y", "o", "deserializeUleb128AsU32", "u", "load", "p", "Error", "raw_txn", "secondary_signer_addresses", "serializeU32AsUleb128", "serializeVector", "deserializeVector", "fee_payer_address", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\rawTransaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { ChainId } from \"./chainId\";\nimport { AccountAddress } from \"../../core\";\nimport { TransactionPayload } from \"./transactionPayload\";\nimport { TransactionVariants } from \"../../types\";\n\n/**\n * Representation of a Raw Transaction that can serialized and deserialized\n */\nexport class RawTransaction extends Serializable {\n  public readonly sender: AccountAddress;\n\n  public readonly sequence_number: bigint;\n\n  public readonly payload: TransactionPayload;\n\n  public readonly max_gas_amount: bigint;\n\n  public readonly gas_unit_price: bigint;\n\n  public readonly expiration_timestamp_secs: bigint;\n\n  public readonly chain_id: ChainId;\n\n  /**\n   * RawTransactions contain the metadata and payloads that can be submitted to Aptos chain for execution.\n   * RawTransactions must be signed before Aptos chain can execute them.\n   *\n   * @param sender The sender Account Address\n   * @param sequence_number Sequence number of this transaction. This must match the sequence number stored in\n   *   the sender's account at the time the transaction executes.\n   * @param payload Instructions for the Aptos Blockchain, including publishing a module,\n   *   execute an entry function or execute a script payload.\n   * @param max_gas_amount Maximum total gas to spend for this transaction. The account must have more\n   *   than this gas or the transaction will be discarded during validation.\n   * @param gas_unit_price Price to be paid per gas unit.\n   * @param expiration_timestamp_secs The blockchain timestamp at which the blockchain would discard this transaction.\n   * @param chain_id The chain ID of the blockchain that this transaction is intended to be run on.\n   */\n  constructor(\n    sender: AccountAddress,\n    sequence_number: bigint,\n    payload: TransactionPayload,\n    max_gas_amount: bigint,\n    gas_unit_price: bigint,\n    expiration_timestamp_secs: bigint,\n    chain_id: ChainId,\n  ) {\n    super();\n    this.sender = sender;\n    this.sequence_number = sequence_number;\n    this.payload = payload;\n    this.max_gas_amount = max_gas_amount;\n    this.gas_unit_price = gas_unit_price;\n    this.expiration_timestamp_secs = expiration_timestamp_secs;\n    this.chain_id = chain_id;\n  }\n\n  serialize(serializer: Serializer): void {\n    this.sender.serialize(serializer);\n    serializer.serializeU64(this.sequence_number);\n    this.payload.serialize(serializer);\n    serializer.serializeU64(this.max_gas_amount);\n    serializer.serializeU64(this.gas_unit_price);\n    serializer.serializeU64(this.expiration_timestamp_secs);\n    this.chain_id.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): RawTransaction {\n    const sender = AccountAddress.deserialize(deserializer);\n    const sequence_number = deserializer.deserializeU64();\n    const payload = TransactionPayload.deserialize(deserializer);\n    const max_gas_amount = deserializer.deserializeU64();\n    const gas_unit_price = deserializer.deserializeU64();\n    const expiration_timestamp_secs = deserializer.deserializeU64();\n    const chain_id = ChainId.deserialize(deserializer);\n    return new RawTransaction(\n      sender,\n      sequence_number,\n      payload,\n      max_gas_amount,\n      gas_unit_price,\n      expiration_timestamp_secs,\n      chain_id,\n    );\n  }\n}\n\n/**\n * Representation of a Raw Transaction With Data that can serialized and deserialized\n */\nexport abstract class RawTransactionWithData extends Serializable {\n  /**\n   * Serialize a Raw Transaction With Data\n   */\n  abstract serialize(serializer: Serializer): void;\n\n  /**\n   * Deserialize a Raw Transaction With Data\n   */\n  static deserialize(deserializer: Deserializer): RawTransactionWithData {\n    // index enum variant\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case TransactionVariants.MultiAgentTransaction:\n        return MultiAgentRawTransaction.load(deserializer);\n      case TransactionVariants.FeePayerTransaction:\n        return FeePayerRawTransaction.load(deserializer);\n      default:\n        throw new Error(`Unknown variant index for RawTransactionWithData: ${index}`);\n    }\n  }\n}\n\n/**\n * Representation of a Multi Agent Transaction that can serialized and deserialized\n */\nexport class MultiAgentRawTransaction extends RawTransactionWithData {\n  /**\n   * The raw transaction\n   */\n  public readonly raw_txn: RawTransaction;\n\n  /**\n   * The secondary signers on this transaction\n   */\n  public readonly secondary_signer_addresses: Array<AccountAddress>;\n\n  constructor(raw_txn: RawTransaction, secondary_signer_addresses: Array<AccountAddress>) {\n    super();\n    this.raw_txn = raw_txn;\n    this.secondary_signer_addresses = secondary_signer_addresses;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionVariants.MultiAgentTransaction);\n    this.raw_txn.serialize(serializer);\n    serializer.serializeVector(this.secondary_signer_addresses);\n  }\n\n  static load(deserializer: Deserializer): MultiAgentRawTransaction {\n    const rawTxn = RawTransaction.deserialize(deserializer);\n    const secondarySignerAddresses = deserializer.deserializeVector(AccountAddress);\n\n    return new MultiAgentRawTransaction(rawTxn, secondarySignerAddresses);\n  }\n}\n\n/**\n * Representation of a Fee Payer Transaction that can serialized and deserialized\n */\nexport class FeePayerRawTransaction extends RawTransactionWithData {\n  /**\n   * The raw transaction\n   */\n  public readonly raw_txn: RawTransaction;\n\n  /**\n   * The secondary signers on this transaction - optional and can be empty\n   */\n  public readonly secondary_signer_addresses: Array<AccountAddress>;\n\n  /**\n   * The fee payer account address\n   */\n  public readonly fee_payer_address: AccountAddress;\n\n  constructor(\n    raw_txn: RawTransaction,\n    secondary_signer_addresses: Array<AccountAddress>,\n    fee_payer_address: AccountAddress,\n  ) {\n    super();\n    this.raw_txn = raw_txn;\n    this.secondary_signer_addresses = secondary_signer_addresses;\n    this.fee_payer_address = fee_payer_address;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionVariants.FeePayerTransaction);\n    this.raw_txn.serialize(serializer);\n    serializer.serializeVector(this.secondary_signer_addresses);\n    this.fee_payer_address.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): FeePayerRawTransaction {\n    const rawTxn = RawTransaction.deserialize(deserializer);\n    const secondarySignerAddresses = deserializer.deserializeVector(AccountAddress);\n    const feePayerAddress = AccountAddress.deserialize(deserializer);\n\n    return new FeePayerRawTransaction(rawTxn, secondarySignerAddresses, feePayerAddress);\n  }\n}\n"], "mappings": ";;;;AAeO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAuBC,CAAa;IA8B/CC,YACEC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACA;MACA,MAAM,GACN,KAAKC,MAAA,GAASP,CAAA,EACd,KAAKQ,eAAA,GAAkBP,CAAA,EACvB,KAAKQ,OAAA,GAAUP,CAAA,EACf,KAAKQ,cAAA,GAAiBP,CAAA,EACtB,KAAKQ,cAAA,GAAiBP,CAAA,EACtB,KAAKQ,yBAAA,GAA4BP,CAAA,EACjC,KAAKQ,QAAA,GAAWP,CAClB;IAAA;IAEAQ,UAAUd,CAAA,EAA8B;MACtC,KAAKO,MAAA,CAAOO,SAAA,CAAUd,CAAU,GAChCA,CAAA,CAAWe,YAAA,CAAa,KAAKP,eAAe,GAC5C,KAAKC,OAAA,CAAQK,SAAA,CAAUd,CAAU,GACjCA,CAAA,CAAWe,YAAA,CAAa,KAAKL,cAAc,GAC3CV,CAAA,CAAWe,YAAA,CAAa,KAAKJ,cAAc,GAC3CX,CAAA,CAAWe,YAAA,CAAa,KAAKH,yBAAyB,GACtD,KAAKC,QAAA,CAASC,SAAA,CAAUd,CAAU,CACpC;IAAA;IAEA,OAAOgB,YAAYhB,CAAA,EAA4C;MAC7D,IAAMC,CAAA,GAASgB,CAAA,CAAeD,WAAA,CAAYhB,CAAY;QAChDE,CAAA,GAAkBF,CAAA,CAAakB,cAAA,CAAe;QAC9Cf,CAAA,GAAUgB,CAAA,CAAmBH,WAAA,CAAYhB,CAAY;QACrDI,CAAA,GAAiBJ,CAAA,CAAakB,cAAA,CAAe;QAC7Cb,CAAA,GAAiBL,CAAA,CAAakB,cAAA,CAAe;QAC7CZ,CAAA,GAA4BN,CAAA,CAAakB,cAAA,CAAe;QACxDE,CAAA,GAAWC,CAAA,CAAQL,WAAA,CAAYhB,CAAY;MACjD,OAAO,IAAIH,CAAA,CACTI,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAc,CACF,CACF;IAAA;EACF;EAKsBE,CAAA,GAAf,cAA8CxB,CAAa;IAShE,OAAOkB,YAAYhB,CAAA,EAAoD;MAErE,IAAMC,CAAA,GAAQD,CAAA,CAAauB,uBAAA,CAAwB;MACnD,QAAQtB,CAAA;QACN;UACE,OAAOuB,CAAA,CAAyBC,IAAA,CAAKzB,CAAY;QACnD;UACE,OAAO0B,CAAA,CAAuBD,IAAA,CAAKzB,CAAY;QACjD;UACE,MAAM,IAAI2B,KAAA,CAAM,qDAAqD1B,CAAK,EAAE,CAChF;MAAA;IACF;EACF;EAKauB,CAAA,GAAN,MAAM3B,CAAA,SAAiCyB,CAAuB;IAWnEvB,YAAYC,CAAA,EAAyBC,CAAA,EAAmD;MACtF,MAAM,GACN,KAAK2B,OAAA,GAAU5B,CAAA,EACf,KAAK6B,0BAAA,GAA6B5B,CACpC;IAAA;IAEAa,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAW8B,qBAAA,EAA+D,GAC1E,KAAKF,OAAA,CAAQd,SAAA,CAAUd,CAAU,GACjCA,CAAA,CAAW+B,eAAA,CAAgB,KAAKF,0BAA0B,CAC5D;IAAA;IAEA,OAAOJ,KAAKzB,CAAA,EAAsD;MAChE,IAAMC,CAAA,GAASL,CAAA,CAAeoB,WAAA,CAAYhB,CAAY;QAChDE,CAAA,GAA2BF,CAAA,CAAagC,iBAAA,CAAkBf,CAAc;MAE9E,OAAO,IAAIpB,CAAA,CAAyBI,CAAA,EAAQC,CAAwB,CACtE;IAAA;EACF;EAKawB,CAAA,GAAN,MAAM7B,CAAA,SAA+ByB,CAAuB;IAgBjEvB,YACEC,CAAA,EACAC,CAAA,EACAC,CAAA,EACA;MACA,MAAM,GACN,KAAK0B,OAAA,GAAU5B,CAAA,EACf,KAAK6B,0BAAA,GAA6B5B,CAAA,EAClC,KAAKgC,iBAAA,GAAoB/B,CAC3B;IAAA;IAEAY,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAW8B,qBAAA,EAA6D,GACxE,KAAKF,OAAA,CAAQd,SAAA,CAAUd,CAAU,GACjCA,CAAA,CAAW+B,eAAA,CAAgB,KAAKF,0BAA0B,GAC1D,KAAKI,iBAAA,CAAkBnB,SAAA,CAAUd,CAAU,CAC7C;IAAA;IAEA,OAAOyB,KAAKzB,CAAA,EAAoD;MAC9D,IAAMC,CAAA,GAASL,CAAA,CAAeoB,WAAA,CAAYhB,CAAY;QAChDE,CAAA,GAA2BF,CAAA,CAAagC,iBAAA,CAAkBf,CAAc;QACxEd,CAAA,GAAkBc,CAAA,CAAeD,WAAA,CAAYhB,CAAY;MAE/D,OAAO,IAAIH,CAAA,CAAuBI,CAAA,EAAQC,CAAA,EAA0BC,CAAe,CACrF;IAAA;EACF;AAAA,SAAAP,CAAA,IAAAM,CAAA,EAAAoB,CAAA,IAAAY,CAAA,EAAAV,CAAA,IAAApB,CAAA,EAAAsB,CAAA,IAAArB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}