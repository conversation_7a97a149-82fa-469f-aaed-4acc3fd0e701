{"ast": null, "code": "var t = \"aptos:devnet\",\n  o = \"aptos:testnet\",\n  A = \"aptos:localnet\",\n  T = \"aptos:mainnet\",\n  e = [t, o, A, T];\nexport { t as a, o as b, A as c, T as d, e };", "map": {"version": 3, "names": ["t", "o", "A", "T", "e", "a", "b", "c", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\chains.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nexport type ChainsId = `${string}:${string}` // e.g. 'aptos:devnet'\n/** APTOS Devnet */\nexport const APTOS_DEVNET_CHAIN = 'aptos:devnet'\n\n/** APTOS Testnet */\nexport const APTOS_TESTNET_CHAIN = 'aptos:testnet'\n\n/** APTOS Localnet */\nexport const APTOS_LOCALNET_CHAIN = 'aptos:localnet'\n\n/** APTOS Mainnet */\nexport const APTOS_MAINNET_CHAIN = 'aptos:mainnet'\n\nexport const APTOS_CHAINS = [\n  APTOS_DEVNET_CHAIN,\n  APTOS_TESTNET_CHAIN,\n  APTOS_LOCALNET_CHAIN,\n  APTOS_MAINNET_CHAIN\n] as const\n\nexport type AptosChain =\n  | typeof APTOS_DEVNET_CHAIN\n  | typeof APTOS_TESTNET_CHAIN\n  | typeof APTOS_LOCALNET_CHAIN\n  | typeof APTOS_MAINNET_CHAIN\n"], "mappings": "AAKO,IAAMA,CAAA,GAAqB;EAGrBC,CAAA,GAAsB;EAGtBC,CAAA,GAAuB;EAGvBC,CAAA,GAAsB;EAEtBC,CAAA,GAAe,CAC1BJ,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CACF;AAAA,SAAAH,CAAA,IAAAK,CAAA,EAAAJ,CAAA,IAAAK,CAAA,EAAAJ,CAAA,IAAAK,CAAA,EAAAJ,CAAA,IAAAK,CAAA,EAAAJ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}