{"ast": null, "code": "/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nimport { abytes, aexists, ahash, clean, Hash, toBytes } from \"./utils.js\";\nexport class HMAC extends Hash {\n  constructor(hash, _key) {\n    super();\n    this.finished = false;\n    this.destroyed = false;\n    ahash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create();\n    if (typeof this.iHash.update !== 'function') throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create();\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    clean(pad);\n  }\n  update(buf) {\n    aexists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out) {\n    aexists(this);\n    abytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest() {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to) {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to || (to = Object.create(Object.getPrototypeOf(this), {}));\n    const {\n      oHash,\n      iHash,\n      finished,\n      destroyed,\n      blockLen,\n      outputLen\n    } = this;\n    to = to;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  clone() {\n    return this._cloneInto();\n  }\n  destroy() {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nexport const hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);", "map": {"version": 3, "names": ["abytes", "aexists", "ahash", "clean", "Hash", "toBytes", "HMAC", "constructor", "hash", "_key", "finished", "destroyed", "key", "iHash", "create", "update", "Error", "blockLen", "outputLen", "pad", "Uint8Array", "set", "length", "digest", "i", "oHash", "buf", "digestInto", "out", "destroy", "_cloneInto", "to", "Object", "getPrototypeOf", "clone", "hmac", "message"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\hmac.ts"], "sourcesContent": ["/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nimport { abytes, aexists, ahash, clean, Hash, toBytes, type CHash, type Input } from './utils.ts';\n\nexport class HMAC<T extends Hash<T>> extends Hash<HMAC<T>> {\n  oHash: T;\n  iHash: T;\n  blockLen: number;\n  outputLen: number;\n  private finished = false;\n  private destroyed = false;\n\n  constructor(hash: CHash, _key: Input) {\n    super();\n    ahash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create() as T;\n    if (typeof this.iHash.update !== 'function')\n      throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create() as T;\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    clean(pad);\n  }\n  update(buf: Input): this {\n    aexists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    abytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest(): Uint8Array {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to?: HMAC<T>): HMAC<T> {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to ||= Object.create(Object.getPrototypeOf(this), {});\n    const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n    to = to as this;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  clone(): HMAC<T> {\n    return this._cloneInto();\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n}\n\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nexport const hmac: {\n  (hash: CHash, key: Input, message: Input): Uint8Array;\n  create(hash: CHash, key: Input): HMAC<any>;\n} = (hash: CHash, key: Input, message: Input): Uint8Array =>\n  new HMAC<any>(hash, key).update(message).digest();\nhmac.create = (hash: CHash, key: Input) => new HMAC<any>(hash, key);\n"], "mappings": "AAAA;;;;AAIA,SAASA,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAgC,YAAY;AAEjG,OAAM,MAAOC,IAAwB,SAAQF,IAAa;EAQxDG,YAAYC,IAAW,EAAEC,IAAW;IAClC,KAAK,EAAE;IAJD,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,KAAK;IAIvBT,KAAK,CAACM,IAAI,CAAC;IACX,MAAMI,GAAG,GAAGP,OAAO,CAACI,IAAI,CAAC;IACzB,IAAI,CAACI,KAAK,GAAGL,IAAI,CAACM,MAAM,EAAO;IAC/B,IAAI,OAAO,IAAI,CAACD,KAAK,CAACE,MAAM,KAAK,UAAU,EACzC,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;IACxE,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACI,QAAQ;IACnC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,KAAK,CAACK,SAAS;IACrC,MAAMD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAME,GAAG,GAAG,IAAIC,UAAU,CAACH,QAAQ,CAAC;IACpC;IACAE,GAAG,CAACE,GAAG,CAACT,GAAG,CAACU,MAAM,GAAGL,QAAQ,GAAGT,IAAI,CAACM,MAAM,EAAE,CAACC,MAAM,CAACH,GAAG,CAAC,CAACW,MAAM,EAAE,GAAGX,GAAG,CAAC;IACzE,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEL,GAAG,CAACK,CAAC,CAAC,IAAI,IAAI;IACnD,IAAI,CAACX,KAAK,CAACE,MAAM,CAACI,GAAG,CAAC;IACtB;IACA,IAAI,CAACM,KAAK,GAAGjB,IAAI,CAACM,MAAM,EAAO;IAC/B;IACA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEL,GAAG,CAACK,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI;IAC1D,IAAI,CAACC,KAAK,CAACV,MAAM,CAACI,GAAG,CAAC;IACtBhB,KAAK,CAACgB,GAAG,CAAC;EACZ;EACAJ,MAAMA,CAACW,GAAU;IACfzB,OAAO,CAAC,IAAI,CAAC;IACb,IAAI,CAACY,KAAK,CAACE,MAAM,CAACW,GAAG,CAAC;IACtB,OAAO,IAAI;EACb;EACAC,UAAUA,CAACC,GAAe;IACxB3B,OAAO,CAAC,IAAI,CAAC;IACbD,MAAM,CAAC4B,GAAG,EAAE,IAAI,CAACV,SAAS,CAAC;IAC3B,IAAI,CAACR,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,KAAK,CAACc,UAAU,CAACC,GAAG,CAAC;IAC1B,IAAI,CAACH,KAAK,CAACV,MAAM,CAACa,GAAG,CAAC;IACtB,IAAI,CAACH,KAAK,CAACE,UAAU,CAACC,GAAG,CAAC;IAC1B,IAAI,CAACC,OAAO,EAAE;EAChB;EACAN,MAAMA,CAAA;IACJ,MAAMK,GAAG,GAAG,IAAIR,UAAU,CAAC,IAAI,CAACK,KAAK,CAACP,SAAS,CAAC;IAChD,IAAI,CAACS,UAAU,CAACC,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ;EACAE,UAAUA,CAACC,EAAY;IACrB;IACAA,EAAE,KAAFA,EAAE,GAAKC,MAAM,CAAClB,MAAM,CAACkB,MAAM,CAACC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACrD,MAAM;MAAER,KAAK;MAAEZ,KAAK;MAAEH,QAAQ;MAAEC,SAAS;MAAEM,QAAQ;MAAEC;IAAS,CAAE,GAAG,IAAI;IACvEa,EAAE,GAAGA,EAAU;IACfA,EAAE,CAACrB,QAAQ,GAAGA,QAAQ;IACtBqB,EAAE,CAACpB,SAAS,GAAGA,SAAS;IACxBoB,EAAE,CAACd,QAAQ,GAAGA,QAAQ;IACtBc,EAAE,CAACb,SAAS,GAAGA,SAAS;IACxBa,EAAE,CAACN,KAAK,GAAGA,KAAK,CAACK,UAAU,CAACC,EAAE,CAACN,KAAK,CAAC;IACrCM,EAAE,CAAClB,KAAK,GAAGA,KAAK,CAACiB,UAAU,CAACC,EAAE,CAAClB,KAAK,CAAC;IACrC,OAAOkB,EAAE;EACX;EACAG,KAAKA,CAAA;IACH,OAAO,IAAI,CAACJ,UAAU,EAAE;EAC1B;EACAD,OAAOA,CAAA;IACL,IAAI,CAAClB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACc,KAAK,CAACI,OAAO,EAAE;IACpB,IAAI,CAAChB,KAAK,CAACgB,OAAO,EAAE;EACtB;;AAGF;;;;;;;;;;AAUA,OAAO,MAAMM,IAAI,GAGbA,CAAC3B,IAAW,EAAEI,GAAU,EAAEwB,OAAc,KAC1C,IAAI9B,IAAI,CAAME,IAAI,EAAEI,GAAG,CAAC,CAACG,MAAM,CAACqB,OAAO,CAAC,CAACb,MAAM,EAAE;AACnDY,IAAI,CAACrB,MAAM,GAAG,CAACN,IAAW,EAAEI,GAAU,KAAK,IAAIN,IAAI,CAAME,IAAI,EAAEI,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}