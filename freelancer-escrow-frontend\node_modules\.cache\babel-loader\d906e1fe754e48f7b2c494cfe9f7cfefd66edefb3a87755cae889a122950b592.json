{"ast": null, "code": "/**\n * SECG secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Belongs to Koblitz curves: it has efficiently-computable GLV endomorphism ψ,\n * check out {@link EndomorphismOpts}. Seems to be rigid (not backdoored).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha2.js';\nimport { randomBytes } from '@noble/hashes/utils.js';\nimport { createCurve } from \"./_shortw_utils.js\";\nimport { createHasher, isogenyMap } from \"./abstract/hash-to-curve.js\";\nimport { Field, mapHashToField, mod, pow2 } from \"./abstract/modular.js\";\nimport { _normFnElement, mapToCurveSimpleSWU } from \"./abstract/weierstrass.js\";\nimport { bytesToNumberBE, concatBytes, ensureBytes, inRange, numberToBytesBE, utf8ToBytes } from \"./utils.js\";\n// Seems like generator was produced from some seed:\n// `Point.BASE.multiply(Point.Fn.inv(2n, N)).toAffine().x`\n// // gives short x 0x3b78ce563f89a0ed9414f5aa28ad0d96d6795f9c63n\nconst secp256k1_CURVE = {\n  p: BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f'),\n  n: BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141'),\n  h: BigInt(1),\n  a: BigInt(0),\n  b: BigInt(7),\n  Gx: BigInt('0x79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n  Gy: BigInt('0x483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8')\n};\nconst secp256k1_ENDO = {\n  beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n  basises: [[BigInt('0x3086d221a7d46bcde86c90e49284eb15'), -BigInt('0xe4437ed6010e88286f547fa90abfe4c3')], [BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8'), BigInt('0x3086d221a7d46bcde86c90e49284eb15')]]\n};\nconst _0n = /* @__PURE__ */BigInt(0);\nconst _1n = /* @__PURE__ */BigInt(1);\nconst _2n = /* @__PURE__ */BigInt(2);\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n  const P = secp256k1_CURVE.p;\n  // prettier-ignore\n  const _3n = BigInt(3),\n    _6n = BigInt(6),\n    _11n = BigInt(11),\n    _22n = BigInt(22);\n  // prettier-ignore\n  const _23n = BigInt(23),\n    _44n = BigInt(44),\n    _88n = BigInt(88);\n  const b2 = y * y * y % P; // x^3, 11\n  const b3 = b2 * b2 * y % P; // x^7\n  const b6 = pow2(b3, _3n, P) * b3 % P;\n  const b9 = pow2(b6, _3n, P) * b3 % P;\n  const b11 = pow2(b9, _2n, P) * b2 % P;\n  const b22 = pow2(b11, _11n, P) * b11 % P;\n  const b44 = pow2(b22, _22n, P) * b22 % P;\n  const b88 = pow2(b44, _44n, P) * b44 % P;\n  const b176 = pow2(b88, _88n, P) * b88 % P;\n  const b220 = pow2(b176, _44n, P) * b44 % P;\n  const b223 = pow2(b220, _3n, P) * b3 % P;\n  const t1 = pow2(b223, _23n, P) * b22 % P;\n  const t2 = pow2(t1, _6n, P) * b2 % P;\n  const root = pow2(t2, _2n, P);\n  if (!Fpk1.eql(Fpk1.sqr(root), y)) throw new Error('Cannot find square root');\n  return root;\n}\nconst Fpk1 = Field(secp256k1_CURVE.p, {\n  sqrt: sqrtMod\n});\n/**\n * secp256k1 curve, ECDSA and ECDH methods.\n *\n * Field: `2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n`\n *\n * @example\n * ```js\n * import { secp256k1 } from '@noble/curves/secp256k1';\n * const { secretKey, publicKey } = secp256k1.keygen();\n * const msg = new TextEncoder().encode('hello');\n * const sig = secp256k1.sign(msg, secretKey);\n * const isValid = secp256k1.verify(sig, msg, publicKey) === true;\n * ```\n */\nexport const secp256k1 = createCurve({\n  ...secp256k1_CURVE,\n  Fp: Fpk1,\n  lowS: true,\n  endo: secp256k1_ENDO\n}, sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n  let tagP = TAGGED_HASH_PREFIXES[tag];\n  if (tagP === undefined) {\n    const tagH = sha256(utf8ToBytes(tag));\n    tagP = concatBytes(tagH, tagH);\n    TAGGED_HASH_PREFIXES[tag] = tagP;\n  }\n  return sha256(concatBytes(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = point => point.toBytes(true).slice(1);\nconst Pointk1 = /* @__PURE__ */(() => secp256k1.Point)();\nconst hasEven = y => y % _2n === _0n;\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n  const {\n    Fn,\n    BASE\n  } = Pointk1;\n  const d_ = _normFnElement(Fn, priv);\n  const p = BASE.multiply(d_); // P = d'⋅G; 0 < d' < n check is done inside\n  const scalar = hasEven(p.y) ? d_ : Fn.neg(d_);\n  return {\n    scalar,\n    bytes: pointToBytes(p)\n  };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n  const Fp = Fpk1;\n  if (!Fp.isValidNot0(x)) throw new Error('invalid x: Fail if x ≥ p');\n  const xx = Fp.create(x * x);\n  const c = Fp.create(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n  let y = Fp.sqrt(c); // Let y = c^(p+1)/4 mod p. Same as sqrt().\n  // Return the unique point P such that x(P) = x and\n  // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n  if (!hasEven(y)) y = Fp.neg(y);\n  const p = Pointk1.fromAffine({\n    x,\n    y\n  });\n  p.assertValidity();\n  return p;\n}\nconst num = bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n  return Pointk1.Fn.create(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(secretKey) {\n  return schnorrGetExtPubKey(secretKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, secretKey, auxRand = randomBytes(32)) {\n  const {\n    Fn\n  } = Pointk1;\n  const m = ensureBytes('message', message);\n  const {\n    bytes: px,\n    scalar: d\n  } = schnorrGetExtPubKey(secretKey); // checks for isWithinCurveOrder\n  const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n  const t = Fn.toBytes(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n  const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n  // Let k' = int(rand) mod n. Fail if k' = 0. Let R = k'⋅G\n  const {\n    bytes: rx,\n    scalar: k\n  } = schnorrGetExtPubKey(rand);\n  const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n  const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n  sig.set(rx, 0);\n  sig.set(Fn.toBytes(Fn.create(k + e * d)), 32);\n  // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n  if (!schnorrVerify(sig, m, px)) throw new Error('sign: Invalid signature produced');\n  return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n  const {\n    Fn,\n    BASE\n  } = Pointk1;\n  const sig = ensureBytes('signature', signature, 64);\n  const m = ensureBytes('message', message);\n  const pub = ensureBytes('publicKey', publicKey, 32);\n  try {\n    const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n    const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n    if (!inRange(r, _1n, secp256k1_CURVE.p)) return false;\n    const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n    if (!inRange(s, _1n, secp256k1_CURVE.n)) return false;\n    // int(challenge(bytes(r)||bytes(P)||m))%n\n    const e = challenge(Fn.toBytes(r), pointToBytes(P), m);\n    // R = s⋅G - e⋅P, where -eP == (n-e)P\n    const R = BASE.multiplyUnsafe(s).add(P.multiplyUnsafe(Fn.neg(e)));\n    const {\n      x,\n      y\n    } = R.toAffine();\n    // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    if (R.is0() || !hasEven(y) || x !== r) return false;\n    return true;\n  } catch (error) {\n    return false;\n  }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * ```js\n * import { schnorr } from '@noble/curves/secp256k1';\n * const { secretKey, publicKey } = schnorr.keygen();\n * // const publicKey = schnorr.getPublicKey(secretKey);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, secretKey);\n * const isValid = schnorr.verify(sig, msg, publicKey);\n * ```\n */\nexport const schnorr = /* @__PURE__ */(() => {\n  const size = 32;\n  const seedLength = 48;\n  const randomSecretKey = (seed = randomBytes(seedLength)) => {\n    return mapHashToField(seed, secp256k1_CURVE.n);\n  };\n  // TODO: remove\n  secp256k1.utils.randomSecretKey;\n  function keygen(seed) {\n    const secretKey = randomSecretKey(seed);\n    return {\n      secretKey,\n      publicKey: schnorrGetPublicKey(secretKey)\n    };\n  }\n  return {\n    keygen,\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    Point: Pointk1,\n    utils: {\n      randomSecretKey: randomSecretKey,\n      randomPrivateKey: randomSecretKey,\n      taggedHash,\n      // TODO: remove\n      lift_x,\n      pointToBytes,\n      numberToBytesBE,\n      bytesToNumberBE,\n      mod\n    },\n    lengths: {\n      secretKey: size,\n      publicKey: size,\n      publicKeyHasPrefix: false,\n      signature: size * 2,\n      seed: seedLength\n    }\n  };\n})();\nconst isoMap = /* @__PURE__ */(() => isogenyMap(Fpk1, [\n// xNum\n['0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7', '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581', '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262', '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c'],\n// xDen\n['0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b', '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14', '0x0000000000000000000000000000000000000000000000000000000000000001' // LAST 1\n],\n// yNum\n['0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c', '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3', '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931', '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84'],\n// yDen\n['0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b', '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573', '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f', '0x0000000000000000000000000000000000000000000000000000000000000001' // LAST 1\n]].map(i => i.map(j => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */(() => mapToCurveSimpleSWU(Fpk1, {\n  A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n  B: BigInt('1771'),\n  Z: Fpk1.create(BigInt('-11'))\n}))();\n/** Hashing / encoding to secp256k1 points / field. RFC 9380 methods. */\nexport const secp256k1_hasher = /* @__PURE__ */(() => createHasher(secp256k1.Point, scalars => {\n  const {\n    x,\n    y\n  } = mapSWU(Fpk1.create(scalars[0]));\n  return isoMap(x, y);\n}, {\n  DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n  encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n  p: Fpk1.ORDER,\n  m: 1,\n  k: 128,\n  expand: 'xmd',\n  hash: sha256\n}))();\n/** @deprecated use `import { secp256k1_hasher } from '@noble/curves/secp256k1.js';` */\nexport const hashToCurve = /* @__PURE__ */(() => secp256k1_hasher.hashToCurve)();\n/** @deprecated use `import { secp256k1_hasher } from '@noble/curves/secp256k1.js';` */\nexport const encodeToCurve = /* @__PURE__ */(() => secp256k1_hasher.encodeToCurve)();", "map": {"version": 3, "names": ["sha256", "randomBytes", "createCurve", "createHasher", "isogenyMap", "Field", "mapHashToField", "mod", "pow2", "_normFnElement", "mapToCurveSimpleSWU", "bytesToNumberBE", "concatBytes", "ensureBytes", "inRange", "numberToBytesBE", "utf8ToBytes", "secp256k1_CURVE", "p", "BigInt", "n", "h", "a", "b", "Gx", "Gy", "secp256k1_ENDO", "beta", "basises", "_0n", "_1n", "_2n", "sqrtMod", "y", "P", "_3n", "_6n", "_11n", "_22n", "_23n", "_44n", "_88n", "b2", "b3", "b6", "b9", "b11", "b22", "b44", "b88", "b176", "b220", "b223", "t1", "t2", "root", "Fpk1", "eql", "sqr", "Error", "sqrt", "secp256k1", "Fp", "lowS", "endo", "TAGGED_HASH_PREFIXES", "taggedHash", "tag", "messages", "tagP", "undefined", "tagH", "pointToBytes", "point", "toBytes", "slice", "Pointk1", "Point", "has<PERSON>ven", "schnorrGetExtPubKey", "priv", "Fn", "BASE", "d_", "multiply", "scalar", "neg", "bytes", "lift_x", "x", "isValidNot0", "xx", "create", "c", "fromAffine", "assertValidity", "num", "challenge", "args", "schnorrGetPublicKey", "secret<PERSON>ey", "schnorrSign", "message", "auxRand", "m", "px", "d", "t", "rand", "rx", "k", "e", "sig", "Uint8Array", "set", "schnorrVerify", "signature", "public<PERSON>ey", "pub", "r", "subarray", "s", "R", "multiplyUnsafe", "add", "toAffine", "is0", "error", "schnorr", "size", "seedLength", "randomSec<PERSON><PERSON>ey", "seed", "utils", "keygen", "getPublicKey", "sign", "verify", "randomPrivateKey", "lengths", "publicKeyHasPrefix", "isoMap", "map", "i", "j", "mapSWU", "A", "B", "Z", "secp256k1_hasher", "scalars", "DST", "encodeDST", "ORDER", "expand", "hash", "hashToCurve", "encodeToCurve"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\secp256k1.ts"], "sourcesContent": ["/**\n * SECG secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Belongs to Koblitz curves: it has efficiently-computable GLV endomorphism ψ,\n * check out {@link EndomorphismOpts}. Seems to be rigid (not backdoored).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha2.js';\nimport { randomBytes } from '@noble/hashes/utils.js';\nimport { createCurve, type CurveFnWithCreate } from './_shortw_utils.ts';\nimport type { CurveLengths } from './abstract/curve.ts';\nimport {\n  createHasher,\n  type H2CHasher,\n  type H2CMethod,\n  isogenyMap,\n} from './abstract/hash-to-curve.ts';\nimport { Field, mapHashToField, mod, pow2 } from './abstract/modular.ts';\nimport {\n  _normFnElement,\n  type EndomorphismOpts,\n  mapToCurveSimpleSWU,\n  type WeierstrassPoint as PointType,\n  type WeierstrassOpts,\n  type WeierstrassPointCons,\n} from './abstract/weierstrass.ts';\nimport type { Hex, PrivKey } from './utils.ts';\nimport {\n  bytesToNumberBE,\n  concatBytes,\n  ensureBytes,\n  inRange,\n  numberToBytesBE,\n  utf8ToBytes,\n} from './utils.ts';\n\n// Seems like generator was produced from some seed:\n// `Point.BASE.multiply(Point.Fn.inv(2n, N)).toAffine().x`\n// // gives short x 0x3b78ce563f89a0ed9414f5aa28ad0d96d6795f9c63n\nconst secp256k1_CURVE: WeierstrassOpts<bigint> = {\n  p: BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f'),\n  n: BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141'),\n  h: BigInt(1),\n  a: BigInt(0),\n  b: BigInt(7),\n  Gx: BigInt('0x79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n  Gy: BigInt('0x483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8'),\n};\n\nconst secp256k1_ENDO: EndomorphismOpts = {\n  beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n  basises: [\n    [BigInt('0x3086d221a7d46bcde86c90e49284eb15'), -BigInt('0xe4437ed6010e88286f547fa90abfe4c3')],\n    [BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8'), BigInt('0x3086d221a7d46bcde86c90e49284eb15')],\n  ],\n};\n\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\n\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y: bigint): bigint {\n  const P = secp256k1_CURVE.p;\n  // prettier-ignore\n  const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n  // prettier-ignore\n  const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n  const b2 = (y * y * y) % P; // x^3, 11\n  const b3 = (b2 * b2 * y) % P; // x^7\n  const b6 = (pow2(b3, _3n, P) * b3) % P;\n  const b9 = (pow2(b6, _3n, P) * b3) % P;\n  const b11 = (pow2(b9, _2n, P) * b2) % P;\n  const b22 = (pow2(b11, _11n, P) * b11) % P;\n  const b44 = (pow2(b22, _22n, P) * b22) % P;\n  const b88 = (pow2(b44, _44n, P) * b44) % P;\n  const b176 = (pow2(b88, _88n, P) * b88) % P;\n  const b220 = (pow2(b176, _44n, P) * b44) % P;\n  const b223 = (pow2(b220, _3n, P) * b3) % P;\n  const t1 = (pow2(b223, _23n, P) * b22) % P;\n  const t2 = (pow2(t1, _6n, P) * b2) % P;\n  const root = pow2(t2, _2n, P);\n  if (!Fpk1.eql(Fpk1.sqr(root), y)) throw new Error('Cannot find square root');\n  return root;\n}\n\nconst Fpk1 = Field(secp256k1_CURVE.p, { sqrt: sqrtMod });\n\n/**\n * secp256k1 curve, ECDSA and ECDH methods.\n *\n * Field: `2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n`\n *\n * @example\n * ```js\n * import { secp256k1 } from '@noble/curves/secp256k1';\n * const { secretKey, publicKey } = secp256k1.keygen();\n * const msg = new TextEncoder().encode('hello');\n * const sig = secp256k1.sign(msg, secretKey);\n * const isValid = secp256k1.verify(sig, msg, publicKey) === true;\n * ```\n */\nexport const secp256k1: CurveFnWithCreate = createCurve(\n  { ...secp256k1_CURVE, Fp: Fpk1, lowS: true, endo: secp256k1_ENDO },\n  sha256\n);\n\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES: { [tag: string]: Uint8Array } = {};\nfunction taggedHash(tag: string, ...messages: Uint8Array[]): Uint8Array {\n  let tagP = TAGGED_HASH_PREFIXES[tag];\n  if (tagP === undefined) {\n    const tagH = sha256(utf8ToBytes(tag));\n    tagP = concatBytes(tagH, tagH);\n    TAGGED_HASH_PREFIXES[tag] = tagP;\n  }\n  return sha256(concatBytes(tagP, ...messages));\n}\n\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point: PointType<bigint>) => point.toBytes(true).slice(1);\nconst Pointk1 = /* @__PURE__ */ (() => secp256k1.Point)();\nconst hasEven = (y: bigint) => y % _2n === _0n;\n\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv: PrivKey) {\n  const { Fn, BASE } = Pointk1;\n  const d_ = _normFnElement(Fn, priv);\n  const p = BASE.multiply(d_); // P = d'⋅G; 0 < d' < n check is done inside\n  const scalar = hasEven(p.y) ? d_ : Fn.neg(d_);\n  return { scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x: bigint): PointType<bigint> {\n  const Fp = Fpk1;\n  if (!Fp.isValidNot0(x)) throw new Error('invalid x: Fail if x ≥ p');\n  const xx = Fp.create(x * x);\n  const c = Fp.create(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n  let y = Fp.sqrt(c); // Let y = c^(p+1)/4 mod p. Same as sqrt().\n  // Return the unique point P such that x(P) = x and\n  // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n  if (!hasEven(y)) y = Fp.neg(y);\n  const p = Pointk1.fromAffine({ x, y });\n  p.assertValidity();\n  return p;\n}\nconst num = bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args: Uint8Array[]): bigint {\n  return Pointk1.Fn.create(num(taggedHash('BIP0340/challenge', ...args)));\n}\n\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(secretKey: Hex): Uint8Array {\n  return schnorrGetExtPubKey(secretKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message: Hex, secretKey: PrivKey, auxRand: Hex = randomBytes(32)): Uint8Array {\n  const { Fn } = Pointk1;\n  const m = ensureBytes('message', message);\n  const { bytes: px, scalar: d } = schnorrGetExtPubKey(secretKey); // checks for isWithinCurveOrder\n  const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n  const t = Fn.toBytes(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n  const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n  // Let k' = int(rand) mod n. Fail if k' = 0. Let R = k'⋅G\n  const { bytes: rx, scalar: k } = schnorrGetExtPubKey(rand);\n  const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n  const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n  sig.set(rx, 0);\n  sig.set(Fn.toBytes(Fn.create(k + e * d)), 32);\n  // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n  if (!schnorrVerify(sig, m, px)) throw new Error('sign: Invalid signature produced');\n  return sig;\n}\n\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature: Hex, message: Hex, publicKey: Hex): boolean {\n  const { Fn, BASE } = Pointk1;\n  const sig = ensureBytes('signature', signature, 64);\n  const m = ensureBytes('message', message);\n  const pub = ensureBytes('publicKey', publicKey, 32);\n  try {\n    const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n    const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n    if (!inRange(r, _1n, secp256k1_CURVE.p)) return false;\n    const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n    if (!inRange(s, _1n, secp256k1_CURVE.n)) return false;\n    // int(challenge(bytes(r)||bytes(P)||m))%n\n    const e = challenge(Fn.toBytes(r), pointToBytes(P), m);\n    // R = s⋅G - e⋅P, where -eP == (n-e)P\n    const R = BASE.multiplyUnsafe(s).add(P.multiplyUnsafe(Fn.neg(e)));\n    const { x, y } = R.toAffine();\n    // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    if (R.is0() || !hasEven(y) || x !== r) return false;\n    return true;\n  } catch (error) {\n    return false;\n  }\n}\n\nexport type SecpSchnorr = {\n  keygen: (seed?: Uint8Array) => { secretKey: Uint8Array; publicKey: Uint8Array };\n  getPublicKey: typeof schnorrGetPublicKey;\n  sign: typeof schnorrSign;\n  verify: typeof schnorrVerify;\n  Point: WeierstrassPointCons<bigint>;\n  utils: {\n    randomSecretKey: (seed?: Uint8Array) => Uint8Array;\n    pointToBytes: (point: PointType<bigint>) => Uint8Array;\n    lift_x: typeof lift_x;\n    taggedHash: typeof taggedHash;\n\n    /** @deprecated use `randomSecretKey` */\n    randomPrivateKey: (seed?: Uint8Array) => Uint8Array;\n    /** @deprecated use `utils` */\n    numberToBytesBE: typeof numberToBytesBE;\n    /** @deprecated use `utils` */\n    bytesToNumberBE: typeof bytesToNumberBE;\n    /** @deprecated use `modular` */\n    mod: typeof mod;\n  };\n  lengths: CurveLengths;\n};\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * ```js\n * import { schnorr } from '@noble/curves/secp256k1';\n * const { secretKey, publicKey } = schnorr.keygen();\n * // const publicKey = schnorr.getPublicKey(secretKey);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, secretKey);\n * const isValid = schnorr.verify(sig, msg, publicKey);\n * ```\n */\nexport const schnorr: SecpSchnorr = /* @__PURE__ */ (() => {\n  const size = 32;\n  const seedLength = 48;\n  const randomSecretKey = (seed = randomBytes(seedLength)): Uint8Array => {\n    return mapHashToField(seed, secp256k1_CURVE.n);\n  };\n  // TODO: remove\n  secp256k1.utils.randomSecretKey;\n  function keygen(seed?: Uint8Array) {\n    const secretKey = randomSecretKey(seed);\n    return { secretKey, publicKey: schnorrGetPublicKey(secretKey) };\n  }\n  return {\n    keygen,\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    Point: Pointk1,\n    utils: {\n      randomSecretKey: randomSecretKey,\n      randomPrivateKey: randomSecretKey,\n      taggedHash,\n\n      // TODO: remove\n      lift_x,\n      pointToBytes,\n      numberToBytesBE,\n      bytesToNumberBE,\n      mod,\n    },\n    lengths: {\n      secretKey: size,\n      publicKey: size,\n      publicKeyHasPrefix: false,\n      signature: size * 2,\n      seed: seedLength,\n    },\n  };\n})();\n\nconst isoMap = /* @__PURE__ */ (() =>\n  isogenyMap(\n    Fpk1,\n    [\n      // xNum\n      [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n      ],\n      // xDen\n      [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n      // yNum\n      [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n      ],\n      // yDen\n      [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n    ].map((i) => i.map((j) => BigInt(j))) as [bigint[], bigint[], bigint[], bigint[]]\n  ))();\nconst mapSWU = /* @__PURE__ */ (() =>\n  mapToCurveSimpleSWU(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n  }))();\n\n/** Hashing / encoding to secp256k1 points / field. RFC 9380 methods. */\nexport const secp256k1_hasher: H2CHasher<bigint> = /* @__PURE__ */ (() =>\n  createHasher(\n    secp256k1.Point,\n    (scalars: bigint[]) => {\n      const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n      return isoMap(x, y);\n    },\n    {\n      DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n      encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n      p: Fpk1.ORDER,\n      m: 1,\n      k: 128,\n      expand: 'xmd',\n      hash: sha256,\n    }\n  ))();\n\n/** @deprecated use `import { secp256k1_hasher } from '@noble/curves/secp256k1.js';` */\nexport const hashToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() =>\n  secp256k1_hasher.hashToCurve)();\n\n/** @deprecated use `import { secp256k1_hasher } from '@noble/curves/secp256k1.js';` */\nexport const encodeToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() =>\n  secp256k1_hasher.encodeToCurve)();\n"], "mappings": "AAAA;;;;;;;AAOA;AACA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,WAAW,QAAgC,oBAAoB;AAExE,SACEC,YAAY,EAGZC,UAAU,QACL,6BAA6B;AACpC,SAASC,KAAK,EAAEC,cAAc,EAAEC,GAAG,EAAEC,IAAI,QAAQ,uBAAuB;AACxE,SACEC,cAAc,EAEdC,mBAAmB,QAId,2BAA2B;AAElC,SACEC,eAAe,EACfC,WAAW,EACXC,WAAW,EACXC,OAAO,EACPC,eAAe,EACfC,WAAW,QACN,YAAY;AAEnB;AACA;AACA;AACA,MAAMC,eAAe,GAA4B;EAC/CC,CAAC,EAAEC,MAAM,CAAC,oEAAoE,CAAC;EAC/EC,CAAC,EAAED,MAAM,CAAC,oEAAoE,CAAC;EAC/EE,CAAC,EAAEF,MAAM,CAAC,CAAC,CAAC;EACZG,CAAC,EAAEH,MAAM,CAAC,CAAC,CAAC;EACZI,CAAC,EAAEJ,MAAM,CAAC,CAAC,CAAC;EACZK,EAAE,EAAEL,MAAM,CAAC,oEAAoE,CAAC;EAChFM,EAAE,EAAEN,MAAM,CAAC,oEAAoE;CAChF;AAED,MAAMO,cAAc,GAAqB;EACvCC,IAAI,EAAER,MAAM,CAAC,oEAAoE,CAAC;EAClFS,OAAO,EAAE,CACP,CAACT,MAAM,CAAC,oCAAoC,CAAC,EAAE,CAACA,MAAM,CAAC,oCAAoC,CAAC,CAAC,EAC7F,CAACA,MAAM,CAAC,qCAAqC,CAAC,EAAEA,MAAM,CAAC,oCAAoC,CAAC,CAAC;CAEhG;AAED,MAAMU,GAAG,GAAG,eAAgBV,MAAM,CAAC,CAAC,CAAC;AACrC,MAAMW,GAAG,GAAG,eAAgBX,MAAM,CAAC,CAAC,CAAC;AACrC,MAAMY,GAAG,GAAG,eAAgBZ,MAAM,CAAC,CAAC,CAAC;AAErC;;;;AAIA,SAASa,OAAOA,CAACC,CAAS;EACxB,MAAMC,CAAC,GAAGjB,eAAe,CAACC,CAAC;EAC3B;EACA,MAAMiB,GAAG,GAAGhB,MAAM,CAAC,CAAC,CAAC;IAAEiB,GAAG,GAAGjB,MAAM,CAAC,CAAC,CAAC;IAAEkB,IAAI,GAAGlB,MAAM,CAAC,EAAE,CAAC;IAAEmB,IAAI,GAAGnB,MAAM,CAAC,EAAE,CAAC;EAC5E;EACA,MAAMoB,IAAI,GAAGpB,MAAM,CAAC,EAAE,CAAC;IAAEqB,IAAI,GAAGrB,MAAM,CAAC,EAAE,CAAC;IAAEsB,IAAI,GAAGtB,MAAM,CAAC,EAAE,CAAC;EAC7D,MAAMuB,EAAE,GAAIT,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAIC,CAAC,CAAC,CAAC;EAC5B,MAAMS,EAAE,GAAID,EAAE,GAAGA,EAAE,GAAGT,CAAC,GAAIC,CAAC,CAAC,CAAC;EAC9B,MAAMU,EAAE,GAAIpC,IAAI,CAACmC,EAAE,EAAER,GAAG,EAAED,CAAC,CAAC,GAAGS,EAAE,GAAIT,CAAC;EACtC,MAAMW,EAAE,GAAIrC,IAAI,CAACoC,EAAE,EAAET,GAAG,EAAED,CAAC,CAAC,GAAGS,EAAE,GAAIT,CAAC;EACtC,MAAMY,GAAG,GAAItC,IAAI,CAACqC,EAAE,EAAEd,GAAG,EAAEG,CAAC,CAAC,GAAGQ,EAAE,GAAIR,CAAC;EACvC,MAAMa,GAAG,GAAIvC,IAAI,CAACsC,GAAG,EAAET,IAAI,EAAEH,CAAC,CAAC,GAAGY,GAAG,GAAIZ,CAAC;EAC1C,MAAMc,GAAG,GAAIxC,IAAI,CAACuC,GAAG,EAAET,IAAI,EAAEJ,CAAC,CAAC,GAAGa,GAAG,GAAIb,CAAC;EAC1C,MAAMe,GAAG,GAAIzC,IAAI,CAACwC,GAAG,EAAER,IAAI,EAAEN,CAAC,CAAC,GAAGc,GAAG,GAAId,CAAC;EAC1C,MAAMgB,IAAI,GAAI1C,IAAI,CAACyC,GAAG,EAAER,IAAI,EAAEP,CAAC,CAAC,GAAGe,GAAG,GAAIf,CAAC;EAC3C,MAAMiB,IAAI,GAAI3C,IAAI,CAAC0C,IAAI,EAAEV,IAAI,EAAEN,CAAC,CAAC,GAAGc,GAAG,GAAId,CAAC;EAC5C,MAAMkB,IAAI,GAAI5C,IAAI,CAAC2C,IAAI,EAAEhB,GAAG,EAAED,CAAC,CAAC,GAAGS,EAAE,GAAIT,CAAC;EAC1C,MAAMmB,EAAE,GAAI7C,IAAI,CAAC4C,IAAI,EAAEb,IAAI,EAAEL,CAAC,CAAC,GAAGa,GAAG,GAAIb,CAAC;EAC1C,MAAMoB,EAAE,GAAI9C,IAAI,CAAC6C,EAAE,EAAEjB,GAAG,EAAEF,CAAC,CAAC,GAAGQ,EAAE,GAAIR,CAAC;EACtC,MAAMqB,IAAI,GAAG/C,IAAI,CAAC8C,EAAE,EAAEvB,GAAG,EAAEG,CAAC,CAAC;EAC7B,IAAI,CAACsB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,IAAI,CAAC,EAAEtB,CAAC,CAAC,EAAE,MAAM,IAAI0B,KAAK,CAAC,yBAAyB,CAAC;EAC5E,OAAOJ,IAAI;AACb;AAEA,MAAMC,IAAI,GAAGnD,KAAK,CAACY,eAAe,CAACC,CAAC,EAAE;EAAE0C,IAAI,EAAE5B;AAAO,CAAE,CAAC;AAExD;;;;;;;;;;;;;;AAcA,OAAO,MAAM6B,SAAS,GAAsB3D,WAAW,CACrD;EAAE,GAAGe,eAAe;EAAE6C,EAAE,EAAEN,IAAI;EAAEO,IAAI,EAAE,IAAI;EAAEC,IAAI,EAAEtC;AAAc,CAAE,EAClE1B,MAAM,CACP;AAED;AACA;AACA;AACA,MAAMiE,oBAAoB,GAAkC,EAAE;AAC9D,SAASC,UAAUA,CAACC,GAAW,EAAE,GAAGC,QAAsB;EACxD,IAAIC,IAAI,GAAGJ,oBAAoB,CAACE,GAAG,CAAC;EACpC,IAAIE,IAAI,KAAKC,SAAS,EAAE;IACtB,MAAMC,IAAI,GAAGvE,MAAM,CAACgB,WAAW,CAACmD,GAAG,CAAC,CAAC;IACrCE,IAAI,GAAGzD,WAAW,CAAC2D,IAAI,EAAEA,IAAI,CAAC;IAC9BN,oBAAoB,CAACE,GAAG,CAAC,GAAGE,IAAI;EAClC;EACA,OAAOrE,MAAM,CAACY,WAAW,CAACyD,IAAI,EAAE,GAAGD,QAAQ,CAAC,CAAC;AAC/C;AAEA;AACA,MAAMI,YAAY,GAAIC,KAAwB,IAAKA,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;AAC/E,MAAMC,OAAO,GAAG,eAAgB,CAAC,MAAMf,SAAS,CAACgB,KAAK,EAAC,CAAE;AACzD,MAAMC,OAAO,GAAI7C,CAAS,IAAKA,CAAC,GAAGF,GAAG,KAAKF,GAAG;AAE9C;AACA,SAASkD,mBAAmBA,CAACC,IAAa;EACxC,MAAM;IAAEC,EAAE;IAAEC;EAAI,CAAE,GAAGN,OAAO;EAC5B,MAAMO,EAAE,GAAG1E,cAAc,CAACwE,EAAE,EAAED,IAAI,CAAC;EACnC,MAAM9D,CAAC,GAAGgE,IAAI,CAACE,QAAQ,CAACD,EAAE,CAAC,CAAC,CAAC;EAC7B,MAAME,MAAM,GAAGP,OAAO,CAAC5D,CAAC,CAACe,CAAC,CAAC,GAAGkD,EAAE,GAAGF,EAAE,CAACK,GAAG,CAACH,EAAE,CAAC;EAC7C,OAAO;IAAEE,MAAM;IAAEE,KAAK,EAAEf,YAAY,CAACtD,CAAC;EAAC,CAAE;AAC3C;AACA;;;;AAIA,SAASsE,MAAMA,CAACC,CAAS;EACvB,MAAM3B,EAAE,GAAGN,IAAI;EACf,IAAI,CAACM,EAAE,CAAC4B,WAAW,CAACD,CAAC,CAAC,EAAE,MAAM,IAAI9B,KAAK,CAAC,0BAA0B,CAAC;EACnE,MAAMgC,EAAE,GAAG7B,EAAE,CAAC8B,MAAM,CAACH,CAAC,GAAGA,CAAC,CAAC;EAC3B,MAAMI,CAAC,GAAG/B,EAAE,CAAC8B,MAAM,CAACD,EAAE,GAAGF,CAAC,GAAGtE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,IAAIc,CAAC,GAAG6B,EAAE,CAACF,IAAI,CAACiC,CAAC,CAAC,CAAC,CAAC;EACpB;EACA;EACA,IAAI,CAACf,OAAO,CAAC7C,CAAC,CAAC,EAAEA,CAAC,GAAG6B,EAAE,CAACwB,GAAG,CAACrD,CAAC,CAAC;EAC9B,MAAMf,CAAC,GAAG0D,OAAO,CAACkB,UAAU,CAAC;IAAEL,CAAC;IAAExD;EAAC,CAAE,CAAC;EACtCf,CAAC,CAAC6E,cAAc,EAAE;EAClB,OAAO7E,CAAC;AACV;AACA,MAAM8E,GAAG,GAAGrF,eAAe;AAC3B;;;AAGA,SAASsF,SAASA,CAAC,GAAGC,IAAkB;EACtC,OAAOtB,OAAO,CAACK,EAAE,CAACW,MAAM,CAACI,GAAG,CAAC9B,UAAU,CAAC,mBAAmB,EAAE,GAAGgC,IAAI,CAAC,CAAC,CAAC;AACzE;AAEA;;;AAGA,SAASC,mBAAmBA,CAACC,SAAc;EACzC,OAAOrB,mBAAmB,CAACqB,SAAS,CAAC,CAACb,KAAK,CAAC,CAAC;AAC/C;AAEA;;;;AAIA,SAASc,WAAWA,CAACC,OAAY,EAAEF,SAAkB,EAAEG,OAAA,GAAetG,WAAW,CAAC,EAAE,CAAC;EACnF,MAAM;IAAEgF;EAAE,CAAE,GAAGL,OAAO;EACtB,MAAM4B,CAAC,GAAG3F,WAAW,CAAC,SAAS,EAAEyF,OAAO,CAAC;EACzC,MAAM;IAAEf,KAAK,EAAEkB,EAAE;IAAEpB,MAAM,EAAEqB;EAAC,CAAE,GAAG3B,mBAAmB,CAACqB,SAAS,CAAC,CAAC,CAAC;EACjE,MAAM9E,CAAC,GAAGT,WAAW,CAAC,SAAS,EAAE0F,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;EAC/C,MAAMI,CAAC,GAAG1B,EAAE,CAACP,OAAO,CAACgC,CAAC,GAAGV,GAAG,CAAC9B,UAAU,CAAC,aAAa,EAAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,MAAMsF,IAAI,GAAG1C,UAAU,CAAC,eAAe,EAAEyC,CAAC,EAAEF,EAAE,EAAED,CAAC,CAAC,CAAC,CAAC;EACpD;EACA,MAAM;IAAEjB,KAAK,EAAEsB,EAAE;IAAExB,MAAM,EAAEyB;EAAC,CAAE,GAAG/B,mBAAmB,CAAC6B,IAAI,CAAC;EAC1D,MAAMG,CAAC,GAAGd,SAAS,CAACY,EAAE,EAAEJ,EAAE,EAAED,CAAC,CAAC,CAAC,CAAC;EAChC,MAAMQ,GAAG,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;EAChCD,GAAG,CAACE,GAAG,CAACL,EAAE,EAAE,CAAC,CAAC;EACdG,GAAG,CAACE,GAAG,CAACjC,EAAE,CAACP,OAAO,CAACO,EAAE,CAACW,MAAM,CAACkB,CAAC,GAAGC,CAAC,GAAGL,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7C;EACA,IAAI,CAACS,aAAa,CAACH,GAAG,EAAER,CAAC,EAAEC,EAAE,CAAC,EAAE,MAAM,IAAI9C,KAAK,CAAC,kCAAkC,CAAC;EACnF,OAAOqD,GAAG;AACZ;AAEA;;;;AAIA,SAASG,aAAaA,CAACC,SAAc,EAAEd,OAAY,EAAEe,SAAc;EACjE,MAAM;IAAEpC,EAAE;IAAEC;EAAI,CAAE,GAAGN,OAAO;EAC5B,MAAMoC,GAAG,GAAGnG,WAAW,CAAC,WAAW,EAAEuG,SAAS,EAAE,EAAE,CAAC;EACnD,MAAMZ,CAAC,GAAG3F,WAAW,CAAC,SAAS,EAAEyF,OAAO,CAAC;EACzC,MAAMgB,GAAG,GAAGzG,WAAW,CAAC,WAAW,EAAEwG,SAAS,EAAE,EAAE,CAAC;EACnD,IAAI;IACF,MAAMnF,CAAC,GAAGsD,MAAM,CAACQ,GAAG,CAACsB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B,MAAMC,CAAC,GAAGvB,GAAG,CAACgB,GAAG,CAACQ,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC1G,OAAO,CAACyG,CAAC,EAAEzF,GAAG,EAAEb,eAAe,CAACC,CAAC,CAAC,EAAE,OAAO,KAAK;IACrD,MAAMuG,CAAC,GAAGzB,GAAG,CAACgB,GAAG,CAACQ,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC1G,OAAO,CAAC2G,CAAC,EAAE3F,GAAG,EAAEb,eAAe,CAACG,CAAC,CAAC,EAAE,OAAO,KAAK;IACrD;IACA,MAAM2F,CAAC,GAAGd,SAAS,CAAChB,EAAE,CAACP,OAAO,CAAC6C,CAAC,CAAC,EAAE/C,YAAY,CAACtC,CAAC,CAAC,EAAEsE,CAAC,CAAC;IACtD;IACA,MAAMkB,CAAC,GAAGxC,IAAI,CAACyC,cAAc,CAACF,CAAC,CAAC,CAACG,GAAG,CAAC1F,CAAC,CAACyF,cAAc,CAAC1C,EAAE,CAACK,GAAG,CAACyB,CAAC,CAAC,CAAC,CAAC;IACjE,MAAM;MAAEtB,CAAC;MAAExD;IAAC,CAAE,GAAGyF,CAAC,CAACG,QAAQ,EAAE;IAC7B;IACA,IAAIH,CAAC,CAACI,GAAG,EAAE,IAAI,CAAChD,OAAO,CAAC7C,CAAC,CAAC,IAAIwD,CAAC,KAAK8B,CAAC,EAAE,OAAO,KAAK;IACnD,OAAO,IAAI;EACb,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF;AAyBA;;;;;;;;;;;;;AAaA,OAAO,MAAMC,OAAO,GAAgB,eAAgB,CAAC,MAAK;EACxD,MAAMC,IAAI,GAAG,EAAE;EACf,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,eAAe,GAAGA,CAACC,IAAI,GAAGnI,WAAW,CAACiI,UAAU,CAAC,KAAgB;IACrE,OAAO5H,cAAc,CAAC8H,IAAI,EAAEnH,eAAe,CAACG,CAAC,CAAC;EAChD,CAAC;EACD;EACAyC,SAAS,CAACwE,KAAK,CAACF,eAAe;EAC/B,SAASG,MAAMA,CAACF,IAAiB;IAC/B,MAAMhC,SAAS,GAAG+B,eAAe,CAACC,IAAI,CAAC;IACvC,OAAO;MAAEhC,SAAS;MAAEiB,SAAS,EAAElB,mBAAmB,CAACC,SAAS;IAAC,CAAE;EACjE;EACA,OAAO;IACLkC,MAAM;IACNC,YAAY,EAAEpC,mBAAmB;IACjCqC,IAAI,EAAEnC,WAAW;IACjBoC,MAAM,EAAEtB,aAAa;IACrBtC,KAAK,EAAED,OAAO;IACdyD,KAAK,EAAE;MACLF,eAAe,EAAEA,eAAe;MAChCO,gBAAgB,EAAEP,eAAe;MACjCjE,UAAU;MAEV;MACAsB,MAAM;MACNhB,YAAY;MACZzD,eAAe;MACfJ,eAAe;MACfJ;KACD;IACDoI,OAAO,EAAE;MACPvC,SAAS,EAAE6B,IAAI;MACfZ,SAAS,EAAEY,IAAI;MACfW,kBAAkB,EAAE,KAAK;MACzBxB,SAAS,EAAEa,IAAI,GAAG,CAAC;MACnBG,IAAI,EAAEF;;GAET;AACH,CAAC,EAAC,CAAE;AAEJ,MAAMW,MAAM,GAAG,eAAgB,CAAC,MAC9BzI,UAAU,CACRoD,IAAI,EACJ;AACE;AACA,CACE,oEAAoE,EACpE,mEAAmE,EACnE,oEAAoE,EACpE,oEAAoE,CACrE;AACD;AACA,CACE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CAAE;AAAA,CACvE;AACD;AACA,CACE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CACrE;AACD;AACA,CACE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CAAE;AAAA,CACvE,CACF,CAACsF,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACD,GAAG,CAAEE,CAAC,IAAK7H,MAAM,CAAC6H,CAAC,CAAC,CAAC,CAA6C,CAClF,EAAC,CAAE;AACN,MAAMC,MAAM,GAAG,eAAgB,CAAC,MAC9BvI,mBAAmB,CAAC8C,IAAI,EAAE;EACxB0F,CAAC,EAAE/H,MAAM,CAAC,oEAAoE,CAAC;EAC/EgI,CAAC,EAAEhI,MAAM,CAAC,MAAM,CAAC;EACjBiI,CAAC,EAAE5F,IAAI,CAACoC,MAAM,CAACzE,MAAM,CAAC,KAAK,CAAC;CAC7B,CAAC,EAAC,CAAE;AAEP;AACA,OAAO,MAAMkI,gBAAgB,GAAsB,eAAgB,CAAC,MAClElJ,YAAY,CACV0D,SAAS,CAACgB,KAAK,EACdyE,OAAiB,IAAI;EACpB,MAAM;IAAE7D,CAAC;IAAExD;EAAC,CAAE,GAAGgH,MAAM,CAACzF,IAAI,CAACoC,MAAM,CAAC0D,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOT,MAAM,CAACpD,CAAC,EAAExD,CAAC,CAAC;AACrB,CAAC,EACD;EACEsH,GAAG,EAAE,gCAAgC;EACrCC,SAAS,EAAE,gCAAgC;EAC3CtI,CAAC,EAAEsC,IAAI,CAACiG,KAAK;EACbjD,CAAC,EAAE,CAAC;EACJM,CAAC,EAAE,GAAG;EACN4C,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE3J;CACP,CACF,EAAC,CAAE;AAEN;AACA,OAAO,MAAM4J,WAAW,GAAsB,eAAgB,CAAC,MAC7DP,gBAAgB,CAACO,WAAW,EAAC,CAAE;AAEjC;AACA,OAAO,MAAMC,aAAa,GAAsB,eAAgB,CAAC,MAC/DR,gBAAgB,CAACQ,aAAa,EAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}