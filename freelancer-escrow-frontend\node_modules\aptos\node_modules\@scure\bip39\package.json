{"name": "@scure/bip39", "version": "1.2.1", "description": "Secure, audited & minimal implementation of BIP39 mnemonic phrases", "main": "index.js", "files": ["index.js", "index.d.ts", "wordlists/*.js", "wordlists/*.d.ts", "esm"], "types": "index.d.ts", "dependencies": {"@noble/hashes": "~1.3.0", "@scure/base": "~1.1.0"}, "devDependencies": {"micro-should": "0.4.0", "prettier": "2.8.4", "typescript": "5.0.2"}, "author": "<PERSON> (https://paulmillr.com)", "homepage": "https://paulmillr.com/", "repository": {"type": "git", "url": "https://github.com/paulmillr/scure-bip39.git"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "scripts": {"build": "tsc && tsc -p tsconfig.esm.json", "lint": "prettier --check 'src/**/*.ts' 'test/*.test.ts'", "format": "prettier --write 'src/**/*.ts' 'test/*.test.ts'", "test": "cd test && tsc && node bip39.test.js"}, "exports": {".": {"types": "./index.d.ts", "import": "./esm/index.js", "default": "./index.js"}, "./index": {"types": "./index.d.ts", "import": "./esm/index.js", "default": "./index.js"}, "./wordlists/czech": {"types": "./wordlists/czech.d.ts", "import": "./esm/wordlists/czech.js", "default": "./wordlists/czech.js"}, "./wordlists/english": {"types": "./wordlists/english.d.ts", "import": "./esm/wordlists/english.js", "default": "./wordlists/english.js"}, "./wordlists/french": {"types": "./wordlists/french.d.ts", "import": "./esm/wordlists/french.js", "default": "./wordlists/french.js"}, "./wordlists/italian": {"types": "./wordlists/italian.d.ts", "import": "./esm/wordlists/italian.js", "default": "./wordlists/italian.js"}, "./wordlists/japanese": {"types": "./wordlists/japanese.d.ts", "import": "./esm/wordlists/japanese.js", "default": "./wordlists/japanese.js"}, "./wordlists/korean": {"types": "./wordlists/korean.d.ts", "import": "./esm/wordlists/korean.js", "default": "./wordlists/korean.js"}, "./wordlists/simplified-chinese": {"types": "./wordlists/simplified-chinese.d.ts", "import": "./esm/wordlists/simplified-chinese.js", "default": "./wordlists/simplified-chinese.js"}, "./wordlists/spanish": {"types": "./wordlists/spanish.d.ts", "import": "./esm/wordlists/spanish.js", "default": "./wordlists/spanish.js"}, "./wordlists/traditional-chinese": {"types": "./wordlists/traditional-chinese.d.ts", "import": "./esm/wordlists/traditional-chinese.js", "default": "./wordlists/traditional-chinese.js"}}, "keywords": ["bip39", "mnemonic", "phrase", "code", "bip0039", "bip-39", "micro", "scure", "wordlist", "noble"], "funding": "https://paulmillr.com/funding/"}