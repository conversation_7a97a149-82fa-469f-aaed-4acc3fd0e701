{"ast": null, "code": "import { a as c } from \"./chunk-MHZ64FIW.mjs\";\nimport { a as n } from \"./chunk-QL2QFRKV.mjs\";\nimport { b as i } from \"./chunk-A2Z7I2EY.mjs\";\nvar o = class {\n  static generate(e = {}) {\n    let {\n      scheme: t = 0,\n      legacy: r = !0\n    } = e;\n    return t === 0 && r ? n.generate() : c.generate({\n      scheme: t\n    });\n  }\n  static fromPrivateKey(e) {\n    let {\n      privateKey: t,\n      address: r,\n      legacy: a = !0\n    } = e;\n    return t instanceof i && a ? new n({\n      privateKey: t,\n      address: r\n    }) : new c({\n      privateKey: t,\n      address: r\n    });\n  }\n  static fromPrivateKeyAndAddress(e) {\n    return this.fromPrivateKey(e);\n  }\n  static fromDerivationPath(e) {\n    let {\n      scheme: t = 0,\n      mnemonic: r,\n      path: a,\n      legacy: s = !0\n    } = e;\n    return t === 0 && s ? n.fromDerivationPath({\n      mnemonic: r,\n      path: a\n    }) : c.fromDerivationPath({\n      scheme: t,\n      mnemonic: r,\n      path: a\n    });\n  }\n  static authKey(e) {\n    let {\n      publicKey: t\n    } = e;\n    return t.authKey();\n  }\n  verifySignature(e) {\n    return this.publicKey.verifySignature(e);\n  }\n};\nexport { o as a };", "map": {"version": 3, "names": ["o", "generate", "e", "scheme", "t", "legacy", "r", "n", "c", "fromPrivateKey", "privateKey", "address", "a", "i", "fromPrivateKeyAndAddress", "fromDerivationPath", "mnemonic", "path", "s", "auth<PERSON><PERSON>", "public<PERSON>ey", "verifySignature"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\account\\Account.ts"], "sourcesContent": ["import type { AccountAuthenticator } from \"../transactions/authenticator/account\";\nimport { HexInput, SigningScheme, SigningSchemeInput } from \"../types\";\nimport type { AccountAddress, AccountAddressInput } from \"../core/accountAddress\";\nimport { Authentication<PERSON>ey } from \"../core/authenticationKey\";\nimport { AccountPublicKey, Ed25519PrivateKey, PrivateKey, Signature, VerifySignatureArgs } from \"../core/crypto\";\nimport { Ed25519Account } from \"./Ed25519Account\";\nimport { SingleKeyAccount } from \"./SingleKeyAccount\";\nimport { AnyRawTransaction } from \"../transactions/types\";\n\n/**\n * Arguments for creating an `Ed25519Account` from an `Ed25519PrivateKey`.\n * This is the default input type when passing an `Ed25519PrivateKey`.\n * In order to use the SingleKey authentication scheme, `legacy` needs to be explicitly set to false.\n */\nexport interface CreateEd25519AccountFromPrivateKeyArgs {\n  privateKey: Ed25519PrivateKey;\n  address?: AccountAddressInput;\n  legacy?: true;\n}\n\n/**\n * Arguments for creating an `SingleKeyAccount` from an `Ed25519PrivateKey`.\n * The `legacy` argument needs to be explicitly set to false in order to\n * use the `SingleKey` authentication scheme.\n */\nexport interface CreateEd25519SingleKeyAccountFromPrivateKeyArgs {\n  privateKey: Ed25519PrivateKey;\n  address?: AccountAddressInput;\n  legacy: false;\n}\n\n/**\n * Arguments for creating an `SingleKeyAccount` from any supported private key\n * that is not an `Ed25519PrivateKey`.\n * The `legacy` argument defaults to false and cannot be explicitly set to true.\n */\nexport interface CreateSingleKeyAccountFromPrivateKeyArgs {\n  privateKey: Exclude<PrivateKey, Ed25519PrivateKey>;\n  address?: AccountAddressInput;\n  legacy?: false;\n}\n\n/**\n * Arguments for creating an opaque `Account` from any supported private key.\n * This is used when the private key type is not known at compilation time.\n */\nexport interface CreateAccountFromPrivateKeyArgs {\n  privateKey: PrivateKey;\n  address?: AccountAddressInput;\n  legacy?: boolean;\n}\n\n/**\n * Arguments for generating an `Ed25519Account`.\n * This is the input type used by default.\n */\nexport interface GenerateEd25519AccountArgs {\n  scheme?: SigningSchemeInput.Ed25519;\n  legacy?: true;\n}\n\n/**\n * Arguments for generating an `SingleKeyAccount` with ah underlying `Ed25519PrivateKey`.\n * The `legacy` argument needs to be explicitly set to false,\n * otherwise an `Ed25519Account` will be returned instead.\n */\nexport interface GenerateEd25519SingleKeyAccountArgs {\n  scheme?: SigningSchemeInput.Ed25519;\n  legacy: false;\n}\n\n/**\n * Arguments for generating an `SingleKeyAccount` with any supported private key\n * that is not an `Ed25519PrivateKey`.\n * The `legacy` argument defaults to false and cannot be explicitly set to true.\n */\nexport interface GenerateSingleKeyAccountArgs {\n  scheme: Exclude<SigningSchemeInput, SigningSchemeInput.Ed25519>;\n  legacy?: false;\n}\n\n/**\n * Arguments for generating an opaque `Account`.\n * This is used when the input signature scheme is not known at compilation time.\n */\nexport interface GenerateAccountArgs {\n  scheme?: SigningSchemeInput;\n  legacy?: boolean;\n}\n\n/**\n * Arguments for deriving a private key from a mnemonic phrase and a BIP44 path.\n */\nexport interface PrivateKeyFromDerivationPathArgs {\n  path: string;\n  mnemonic: string;\n}\n\n/**\n * Interface for a generic Aptos account.\n *\n * The interface is defined as abstract class to provide a single entrypoint for account generation,\n * either through `Account.generate()` or `Account.fromDerivationPath`.\n * Despite this being an abstract class, it should be treated as an interface and enforced using\n * the `implements` keyword.\n *\n * Note: Generating an account instance does not create the account on-chain.\n */\nexport abstract class Account {\n  /**\n   * Public key associated with the account\n   */\n  abstract readonly publicKey: AccountPublicKey;\n\n  /**\n   * Account address associated with the account\n   */\n  abstract readonly accountAddress: AccountAddress;\n\n  /**\n   * Signing scheme used to sign transactions\n   */\n  abstract signingScheme: SigningScheme;\n\n  /**\n   * Derives an account from a randomly generated private key.\n   * @param args.scheme The signature scheme to use, to generate the private key\n   * @param args.legacy Whether to use a legacy authentication scheme, when applicable\n   * @returns An account compatible with the provided signature scheme\n   */\n  static generate(args?: GenerateEd25519AccountArgs): Ed25519Account;\n  static generate(args: GenerateEd25519SingleKeyAccountArgs): SingleKeyAccount;\n  static generate(args: GenerateSingleKeyAccountArgs): SingleKeyAccount;\n  static generate(args: GenerateAccountArgs): Account;\n  static generate(args: GenerateAccountArgs = {}) {\n    const { scheme = SigningSchemeInput.Ed25519, legacy = true } = args;\n    if (scheme === SigningSchemeInput.Ed25519 && legacy) {\n      return Ed25519Account.generate();\n    }\n    return SingleKeyAccount.generate({ scheme });\n  }\n\n  /**\n   * Creates an account from the provided private key.\n   *\n   * @param args.privateKey a valid private key\n   * @param args.address the account's address. If not provided, it will be derived from the public key.\n   * @param args.legacy Whether to use a legacy authentication scheme, when applicable\n   */\n  static fromPrivateKey(args: CreateEd25519AccountFromPrivateKeyArgs): Ed25519Account;\n  static fromPrivateKey(args: CreateEd25519SingleKeyAccountFromPrivateKeyArgs): SingleKeyAccount;\n  static fromPrivateKey(args: CreateSingleKeyAccountFromPrivateKeyArgs): SingleKeyAccount;\n  static fromPrivateKey(args: CreateAccountFromPrivateKeyArgs): Account;\n  static fromPrivateKey(args: CreateAccountFromPrivateKeyArgs) {\n    const { privateKey, address, legacy = true } = args;\n    if (privateKey instanceof Ed25519PrivateKey && legacy) {\n      return new Ed25519Account({\n        privateKey,\n        address,\n      });\n    }\n    return new SingleKeyAccount({ privateKey, address });\n  }\n\n  /**\n   * @deprecated use `fromPrivateKey` instead.\n   * Instantiates an account given a private key and a specified account address.\n   * This is primarily used to instantiate an `Account` that has had its authentication key rotated.\n   *\n   * @param args.privateKey PrivateKey - the underlying private key for the account\n   * @param args.address AccountAddress - The account address the `Account` will sign for\n   * @param args.legacy optional. If set to false, the keypair generated is a Unified keypair. Defaults\n   * to generating a Legacy Ed25519 keypair\n   *\n   * @returns Account\n   */\n  static fromPrivateKeyAndAddress(args: CreateAccountFromPrivateKeyArgs) {\n    return this.fromPrivateKey(args);\n  }\n\n  /**\n   * Derives an account with bip44 path and mnemonics\n   *\n   * @param args.scheme The signature scheme to derive the private key with\n   * @param args.path the BIP44 derive hardened path (e.g. m/44'/637'/0'/0'/0') for Ed25519,\n   * or non-hardened path (e.g. m/44'/637'/0'/0/0) for secp256k1\n   * Detailed description: {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}\n   * @param args.mnemonic the mnemonic seed phrase of the account\n   */\n  static fromDerivationPath(args: GenerateEd25519AccountArgs & PrivateKeyFromDerivationPathArgs): Ed25519Account;\n  static fromDerivationPath(\n    args: GenerateEd25519SingleKeyAccountArgs & PrivateKeyFromDerivationPathArgs,\n  ): SingleKeyAccount;\n  static fromDerivationPath(args: GenerateSingleKeyAccountArgs & PrivateKeyFromDerivationPathArgs): SingleKeyAccount;\n  static fromDerivationPath(args: GenerateAccountArgs & PrivateKeyFromDerivationPathArgs): Account;\n  static fromDerivationPath(args: GenerateAccountArgs & PrivateKeyFromDerivationPathArgs) {\n    const { scheme = SigningSchemeInput.Ed25519, mnemonic, path, legacy = true } = args;\n    if (scheme === SigningSchemeInput.Ed25519 && legacy) {\n      return Ed25519Account.fromDerivationPath({ mnemonic, path });\n    }\n    return SingleKeyAccount.fromDerivationPath({ scheme, mnemonic, path });\n  }\n\n  /**\n   * @deprecated use `publicKey.authKey()` instead.\n   * This key enables account owners to rotate their private key(s)\n   * associated with the account without changing the address that hosts their account.\n   * See here for more info: {@link https://aptos.dev/concepts/accounts#single-signer-authentication}\n   *\n   * @param args.publicKey PublicKey - public key of the account\n   * @returns The authentication key for the associated account\n   */\n  static authKey(args: { publicKey: AccountPublicKey }): AuthenticationKey {\n    const { publicKey } = args;\n    return publicKey.authKey();\n  }\n\n  /**\n   * Sign a message using the available signing capabilities.\n   * @param message the signing message, as binary input\n   * @return the AccountAuthenticator containing the signature, together with the account's public key\n   */\n  abstract signWithAuthenticator(message: HexInput): AccountAuthenticator;\n\n  /**\n   * Sign a transaction using the available signing capabilities.\n   * @param transaction the raw transaction\n   * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key\n   */\n  abstract signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticator;\n\n  /**\n   * Sign the given message using the available signing capabilities.\n   * @param message in HexInput format\n   * @returns Signature\n   */\n  abstract sign(message: HexInput): Signature;\n\n  /**\n   * Sign the given transaction using the available signing capabilities.\n   * @param transaction the transaction to be signed\n   * @returns Signature\n   */\n  abstract signTransaction(transaction: AnyRawTransaction): Signature;\n\n  /**\n   * Verify the given message and signature with the public key.\n   * @param args.message raw message data in HexInput format\n   * @param args.signature signed message Signature\n   * @returns\n   */\n  verifySignature(args: VerifySignatureArgs): boolean {\n    return this.publicKey.verifySignature(args);\n  }\n}\n"], "mappings": ";;;AA4GO,IAAeA,CAAA,GAAf,MAAuB;EA0B5B,OAAOC,SAASC,CAAA,GAA4B,CAAC,GAAG;IAC9C,IAAM;MAAEC,MAAA,EAAAC,CAAA;MAAqCC,MAAA,EAAAC,CAAA,GAAS;IAAK,IAAIJ,CAAA;IAC/D,OAAIE,CAAA,KAAW,KAA8BE,CAAA,GACpCC,CAAA,CAAeN,QAAA,CAAS,IAE1BO,CAAA,CAAiBP,QAAA,CAAS;MAAEE,MAAA,EAAAC;IAAO,CAAC,CAC7C;EAAA;EAaA,OAAOK,eAAeP,CAAA,EAAuC;IAC3D,IAAM;MAAEQ,UAAA,EAAAN,CAAA;MAAYO,OAAA,EAAAL,CAAA;MAASD,MAAA,EAAAO,CAAA,GAAS;IAAK,IAAIV,CAAA;IAC/C,OAAIE,CAAA,YAAsBS,CAAA,IAAqBD,CAAA,GACtC,IAAIL,CAAA,CAAe;MACxBG,UAAA,EAAAN,CAAA;MACAO,OAAA,EAAAL;IACF,CAAC,IAEI,IAAIE,CAAA,CAAiB;MAAEE,UAAA,EAAAN,CAAA;MAAYO,OAAA,EAAAL;IAAQ,CAAC,CACrD;EAAA;EAcA,OAAOQ,yBAAyBZ,CAAA,EAAuC;IACrE,OAAO,KAAKO,cAAA,CAAeP,CAAI,CACjC;EAAA;EAiBA,OAAOa,mBAAmBb,CAAA,EAA8D;IACtF,IAAM;MAAEC,MAAA,EAAAC,CAAA;MAAqCY,QAAA,EAAAV,CAAA;MAAUW,IAAA,EAAAL,CAAA;MAAMP,MAAA,EAAAa,CAAA,GAAS;IAAK,IAAIhB,CAAA;IAC/E,OAAIE,CAAA,KAAW,KAA8Bc,CAAA,GACpCX,CAAA,CAAeQ,kBAAA,CAAmB;MAAEC,QAAA,EAAAV,CAAA;MAAUW,IAAA,EAAAL;IAAK,CAAC,IAEtDJ,CAAA,CAAiBO,kBAAA,CAAmB;MAAEZ,MAAA,EAAAC,CAAA;MAAQY,QAAA,EAAAV,CAAA;MAAUW,IAAA,EAAAL;IAAK,CAAC,CACvE;EAAA;EAWA,OAAOO,QAAQjB,CAAA,EAA0D;IACvE,IAAM;MAAEkB,SAAA,EAAAhB;IAAU,IAAIF,CAAA;IACtB,OAAOE,CAAA,CAAUe,OAAA,CAAQ,CAC3B;EAAA;EAoCAE,gBAAgBnB,CAAA,EAAoC;IAClD,OAAO,KAAKkB,SAAA,CAAUC,eAAA,CAAgBnB,CAAI,CAC5C;EAAA;AACF;AAAA,SAAAF,CAAA,IAAAY,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}