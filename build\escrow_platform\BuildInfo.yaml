---
compiled_package_info:
  package_name: escrow_platform
  address_alias_instantiation:
    Extensions: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_framework: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_fungible_asset: 000000000000000000000000000000000000000000000000000000000000000a
    aptos_std: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_token: "0000000000000000000000000000000000000000000000000000000000000003"
    core_resources: 000000000000000000000000000000000000000000000000000000000a550c18
    escrow_platform: 6b70a18147b3a8130c96197760056e1648fe0c8e6371a4c8b32eb6af09a5d563
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    vm: "0000000000000000000000000000000000000000000000000000000000000000"
    vm_reserved: "0000000000000000000000000000000000000000000000000000000000000000"
  source_digest: 1C209A99108F4EF3DEB8CC0D176A4FAC82856A7FAE127869C9BA5F25FCFA9C9E
  build_flags:
    dev_mode: false
    test_mode: false
    override_std: ~
    generate_docs: false
    generate_abis: false
    generate_move_model: true
    full_model_generation: true
    install_dir: ~
    force_recompilation: false
    additional_named_addresses: {}
    fetch_deps_only: false
    skip_fetch_latest_git_deps: true
    compiler_config:
      bytecode_version: 7
      known_attributes:
        - bytecode_instruction
        - deprecated
        - event
        - expected_failure
        - "fmt::skip"
        - legacy_entry_fun
        - "lint::allow_unsafe_randomness"
        - "lint::skip"
        - module_lock
        - "mutation::skip"
        - native_interface
        - persistent
        - randomness
        - resource_group
        - resource_group_member
        - test
        - test_only
        - verify_only
        - view
      skip_attribute_checks: false
      compiler_version: V2_0
      language_version: "2.1"
      experiments:
        - optimize=on
dependencies:
  - AptosFramework
  - AptosStdlib
  - MoveStdlib
bytecode_deps: []
