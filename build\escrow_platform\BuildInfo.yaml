---
compiled_package_info:
  package_name: escrow_platform
  address_alias_instantiation:
    Extensions: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_framework: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_fungible_asset: 000000000000000000000000000000000000000000000000000000000000000a
    aptos_std: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_token: "0000000000000000000000000000000000000000000000000000000000000003"
    core_resources: 000000000000000000000000000000000000000000000000000000000a550c18
    escrow_platform: 96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    vm: "0000000000000000000000000000000000000000000000000000000000000000"
    vm_reserved: "0000000000000000000000000000000000000000000000000000000000000000"
  source_digest: BBD922AA224E6D0B5434E7536F56B230B68E6C6C0F095CFD0BDD31C36BA90C91
  build_flags:
    dev_mode: false
    test_mode: false
    override_std: ~
    generate_docs: false
    generate_abis: false
    generate_move_model: true
    full_model_generation: true
    install_dir: ~
    force_recompilation: false
    additional_named_addresses: {}
    fetch_deps_only: false
    skip_fetch_latest_git_deps: true
    compiler_config:
      bytecode_version: 7
      known_attributes:
        - bytecode_instruction
        - deprecated
        - event
        - expected_failure
        - "fmt::skip"
        - legacy_entry_fun
        - "lint::allow_unsafe_randomness"
        - "lint::skip"
        - module_lock
        - "mutation::skip"
        - native_interface
        - persistent
        - randomness
        - resource_group
        - resource_group_member
        - test
        - test_only
        - verify_only
        - view
      skip_attribute_checks: false
      compiler_version: V2_0
      language_version: "2.1"
      experiments:
        - optimize=on
dependencies:
  - AptosFramework
  - AptosStdlib
  - MoveStdlib
bytecode_deps: []
