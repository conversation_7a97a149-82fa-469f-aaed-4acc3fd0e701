import React from 'react';
import { Loader2 } from 'lucide-react';

const Loading = ({
  size = 'md',
  text = '',
  className = '',
  variant = 'spinner',
  ...props
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };
  
  if (variant === 'spinner') {
    return (
      <div className={`flex items-center justify-center ${className}`} {...props}>
        <Loader2 className={`animate-spin text-primary-600 ${sizeClasses[size]}`} />
        {text && <span className="ml-2 text-secondary-600">{text}</span>}
      </div>
    );
  }
  
  if (variant === 'dots') {
    return (
      <div className={`flex items-center justify-center space-x-1 ${className}`} {...props}>
        <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        {text && <span className="ml-3 text-secondary-600">{text}</span>}
      </div>
    );
  }
  
  if (variant === 'pulse') {
    return (
      <div className={`flex items-center justify-center ${className}`} {...props}>
        <div className={`bg-primary-600 rounded-full animate-pulse ${sizeClasses[size]}`}></div>
        {text && <span className="ml-2 text-secondary-600">{text}</span>}
      </div>
    );
  }
  
  return null;
};

const LoadingSkeleton = ({
  lines = 3,
  className = '',
  ...props
}) => {
  return (
    <div className={`space-y-3 ${className}`} {...props}>
      {Array.from({ length: lines }).map((_, index) => (
        <div key={index} className="skeleton-text"></div>
      ))}
    </div>
  );
};

const LoadingCard = ({ className = '', ...props }) => {
  return (
    <div className={`card p-6 ${className}`} {...props}>
      <div className="flex items-center space-x-4 mb-4">
        <div className="skeleton-avatar"></div>
        <div className="flex-1 space-y-2">
          <div className="skeleton-title"></div>
          <div className="skeleton-text w-1/2"></div>
        </div>
      </div>
      <div className="space-y-2">
        <div className="skeleton-text"></div>
        <div className="skeleton-text"></div>
        <div className="skeleton-text w-3/4"></div>
      </div>
      <div className="flex justify-between items-center mt-4">
        <div className="skeleton-button"></div>
        <div className="skeleton-text w-16"></div>
      </div>
    </div>
  );
};

const LoadingPage = ({ text = 'Loading...', className = '', ...props }) => {
  return (
    <div className={`flex items-center justify-center min-h-64 ${className}`} {...props}>
      <div className="text-center">
        <Loading size="xl" />
        <p className="mt-4 text-secondary-600">{text}</p>
      </div>
    </div>
  );
};

Loading.Skeleton = LoadingSkeleton;
Loading.Card = LoadingCard;
Loading.Page = LoadingPage;

export default Loading;
