{"ast": null, "code": "import { hmac as c } from \"@noble/hashes/hmac\";\nimport { sha512 as p } from \"@noble/hashes/sha512\";\nimport * as i from \"@scure/bip39\";\nvar d = /^m\\/44'\\/637'\\/[0-9]+'\\/[0-9]+'\\/[0-9]+'?$/,\n  m = /^m\\/44'\\/637'\\/[0-9]+'\\/[0-9]+\\/[0-9]+$/,\n  y = (t => (t.ED25519 = \"ed25519 seed\", t))(y || {}),\n  g = 2147483648;\nfunction D(e) {\n  return m.test(e);\n}\nfunction E(e) {\n  return d.test(e);\n}\nvar A = (e, t) => {\n    let r = c.create(p, e).update(t).digest();\n    return {\n      key: r.slice(0, 32),\n      chainCode: r.slice(32)\n    };\n  },\n  f = ({\n    key: e,\n    chainCode: t\n  }, r) => {\n    let n = new ArrayBuffer(4);\n    new DataView(n).setUint32(0, r);\n    let o = new Uint8Array(n),\n      s = new Uint8Array([0]),\n      a = new Uint8Array([...s, ...e, ...o]);\n    return A(t, a);\n  },\n  x = e => e.replace(\"'\", \"\"),\n  U = e => e.split(\"/\").slice(1).map(x),\n  h = e => {\n    let t = e.trim().split(/\\s+/).map(r => r.toLowerCase()).join(\" \");\n    return i.mnemonicToSeedSync(t);\n  };\nexport { d as a, m as b, y as c, g as d, D as e, E as f, A as g, f as h, U as i, h as j };", "map": {"version": 3, "names": ["hmac", "c", "sha512", "p", "i", "d", "m", "y", "t", "ED25519", "g", "D", "e", "test", "E", "A", "<PERSON><PERSON><PERSON>", "r", "create", "update", "digest", "key", "slice", "chainCode", "f", "CKDPriv", "n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "setUint32", "o", "Uint8Array", "s", "a", "x", "replace", "U", "split", "map", "h", "trim", "toLowerCase", "join", "mnemonicToSeedSync", "b", "j"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\hdKey.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { hmac } from \"@noble/hashes/hmac\";\nimport { sha512 } from \"@noble/hashes/sha512\";\nimport * as bip39 from \"@scure/bip39\";\n\nexport type DerivedKeys = {\n  key: Uint8Array;\n  chainCode: Uint8Array;\n};\n\n/**\n * Aptos derive path is 637\n */\nexport const APTOS_HARDENED_REGEX = /^m\\/44'\\/637'\\/[0-9]+'\\/[0-9]+'\\/[0-9]+'?$/;\nexport const APTOS_BIP44_REGEX = /^m\\/44'\\/637'\\/[0-9]+'\\/[0-9]+\\/[0-9]+$/;\n\n/**\n * A list of supported key types and associated seeds\n */\nexport enum KeyType {\n  ED25519 = \"ed25519 seed\",\n}\n\nexport const HARDENED_OFFSET = 0x80000000;\n\n/**\n * Aptos derive path is 637\n *\n * Parse and validate a path that is compliant to BIP-44 in form m/44'/637'/{account_index}'/{change_index}/{address_index}\n * for Secp256k1\n *\n * Note that for secp256k1, last two components must be non-hardened.\n *\n * @param path path string (e.g. `m/44'/637'/0'/0/0`).\n */\nexport function isValidBIP44Path(path: string): boolean {\n  return APTOS_BIP44_REGEX.test(path);\n}\n\n/**\n * Aptos derive path is 637\n *\n * Parse and validate a path that is compliant to SLIP-0010 and BIP-44\n * in form m/44'/637'/{account_index}'/{change_index}'/{address_index}'.\n * See SLIP-0010 {@link https://github.com/satoshilabs/slips/blob/master/slip-0044.md}\n * See BIP-44 {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}\n *\n * Note that for Ed25519, all components must be hardened.\n * This is because non-hardened [PK] derivation would not work due to Ed25519's lack of a key homomorphism.\n * Specifically, you cannot derive the PK associated with derivation path a/b/c given the PK of a/b.\n * This is because the PK in Ed25519 is, more or less, computed as 𝑔𝐻(𝑠𝑘),\n * with the hash function breaking the homomorphism.\n *\n * @param path path string (e.g. `m/44'/637'/0'/0'/0'`).\n */\nexport function isValidHardenedPath(path: string): boolean {\n  return APTOS_HARDENED_REGEX.test(path);\n}\n\nexport const deriveKey = (hashSeed: Uint8Array | string, data: Uint8Array | string): DerivedKeys => {\n  const digest = hmac.create(sha512, hashSeed).update(data).digest();\n  return {\n    key: digest.slice(0, 32),\n    chainCode: digest.slice(32),\n  };\n};\n\n/**\n * Derive a child key from the private key\n * @param key\n * @param chainCode\n * @param index\n * @constructor\n */\nexport const CKDPriv = ({ key, chainCode }: DerivedKeys, index: number): DerivedKeys => {\n  const buffer = new ArrayBuffer(4);\n  new DataView(buffer).setUint32(0, index);\n  const indexBytes = new Uint8Array(buffer);\n  const zero = new Uint8Array([0]);\n  const data = new Uint8Array([...zero, ...key, ...indexBytes]);\n  return deriveKey(chainCode, data);\n};\n\nconst removeApostrophes = (val: string): string => val.replace(\"'\", \"\");\n\n/**\n * Splits derive path into segments\n * @param path\n */\nexport const splitPath = (path: string): Array<string> => path.split(\"/\").slice(1).map(removeApostrophes);\n\n/**\n * Normalizes the mnemonic by removing extra whitespace and making it lowercase\n * @param mnemonic the mnemonic seed phrase\n */\nexport const mnemonicToSeed = (mnemonic: string): Uint8Array => {\n  const normalizedMnemonic = mnemonic\n    .trim()\n    .split(/\\s+/)\n    .map((part) => part.toLowerCase())\n    .join(\" \");\n  return bip39.mnemonicToSeedSync(normalizedMnemonic);\n};\n"], "mappings": "AAGA,SAASA,IAAA,IAAAC,CAAA,QAAY;AACrB,SAASC,MAAA,IAAAC,CAAA,QAAc;AACvB,YAAYC,CAAA,MAAW;AAUhB,IAAMC,CAAA,GAAuB;EACvBC,CAAA,GAAoB;EAKrBC,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,OAAA,GAAU,gBADAD,CAAA,GAAAD,CAAA;EAICG,CAAA,GAAkB;AAYxB,SAASC,EAAiBC,CAAA,EAAuB;EACtD,OAAON,CAAA,CAAkBO,IAAA,CAAKD,CAAI,CACpC;AAAA;AAkBO,SAASE,EAAoBF,CAAA,EAAuB;EACzD,OAAOP,CAAA,CAAqBQ,IAAA,CAAKD,CAAI,CACvC;AAAA;AAEO,IAAMG,CAAA,GAAYC,CAACJ,CAAA,EAA+BJ,CAAA,KAA2C;IAClG,IAAMS,CAAA,GAAShB,CAAA,CAAKiB,MAAA,CAAOf,CAAA,EAAQS,CAAQ,EAAEO,MAAA,CAAOX,CAAI,EAAEY,MAAA,CAAO;IACjE,OAAO;MACLC,GAAA,EAAKJ,CAAA,CAAOK,KAAA,CAAM,GAAG,EAAE;MACvBC,SAAA,EAAWN,CAAA,CAAOK,KAAA,CAAM,EAAE;IAC5B,CACF;EAAA;EASaE,CAAA,GAAUC,CAAC;IAAEJ,GAAA,EAAAT,CAAA;IAAKW,SAAA,EAAAf;EAAU,GAAgBS,CAAA,KAA+B;IACtF,IAAMS,CAAA,GAAS,IAAIC,WAAA,CAAY,CAAC;IAChC,IAAIC,QAAA,CAASF,CAAM,EAAEG,SAAA,CAAU,GAAGZ,CAAK;IACvC,IAAMa,CAAA,GAAa,IAAIC,UAAA,CAAWL,CAAM;MAClCM,CAAA,GAAO,IAAID,UAAA,CAAW,CAAC,CAAC,CAAC;MACzBE,CAAA,GAAO,IAAIF,UAAA,CAAW,CAAC,GAAGC,CAAA,EAAM,GAAGpB,CAAA,EAAK,GAAGkB,CAAU,CAAC;IAC5D,OAAOf,CAAA,CAAUP,CAAA,EAAWyB,CAAI,CAClC;EAAA;EAEMC,CAAA,GAAqBtB,CAAA,IAAwBA,CAAA,CAAIuB,OAAA,CAAQ,KAAK,EAAE;EAMzDC,CAAA,GAAaxB,CAAA,IAAgCA,CAAA,CAAKyB,KAAA,CAAM,GAAG,EAAEf,KAAA,CAAM,CAAC,EAAEgB,GAAA,CAAIJ,CAAiB;EAM3FK,CAAA,GAAkB3B,CAAA,IAAiC;IAC9D,IAAMJ,CAAA,GAAqBI,CAAA,CACxB4B,IAAA,CAAK,EACLH,KAAA,CAAM,KAAK,EACXC,GAAA,CAAKrB,CAAA,IAASA,CAAA,CAAKwB,WAAA,CAAY,CAAC,EAChCC,IAAA,CAAK,GAAG;IACX,OAAatC,CAAA,CAAAuC,kBAAA,CAAmBnC,CAAkB,CACpD;EAAA;AAAA,SAAAH,CAAA,IAAA4B,CAAA,EAAA3B,CAAA,IAAAsC,CAAA,EAAArC,CAAA,IAAAN,CAAA,EAAAS,CAAA,IAAAL,CAAA,EAAAM,CAAA,IAAAC,CAAA,EAAAE,CAAA,IAAAU,CAAA,EAAAT,CAAA,IAAAL,CAAA,EAAAc,CAAA,IAAAe,CAAA,EAAAH,CAAA,IAAAhC,CAAA,EAAAmC,CAAA,IAAAM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}