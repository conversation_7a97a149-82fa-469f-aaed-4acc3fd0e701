{"ast": null, "code": "import { a as o, b as h } from \"./chunk-QVWBJJRF.mjs\";\nimport { b as m } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as g } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as b } from \"./chunk-77NXCSLY.mjs\";\nfunction d(l) {\n  let e = l;\n  return e -= e >> 1 & 1431655765, e = (e & 858993459) + (e >> 2 & 858993459), (e + (e >> 4) & 252645135) * 16843009 >> 24;\n}\nvar f = class l extends m {\n    constructor(e) {\n      super();\n      let {\n        publicKeys: r,\n        signaturesRequired: t\n      } = e;\n      if (t < 1) throw new Error(\"The number of required signatures needs to be greater than 0\");\n      if (r.length < t) throw new Error(`Provided ${r.length} public keys is smaller than the ${t} required signatures`);\n      this.publicKeys = r.map(i => i instanceof o ? i : new o(i)), this.signaturesRequired = t;\n    }\n    verifySignature(e) {\n      throw new Error(\"not implemented\");\n    }\n    authKey() {\n      return b.fromSchemeAndBytes({\n        scheme: 3,\n        input: this.toUint8Array()\n      });\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    serialize(e) {\n      e.serializeVector(this.publicKeys), e.serializeU8(this.signaturesRequired);\n    }\n    static deserialize(e) {\n      let r = e.deserializeVector(o),\n        t = e.deserializeU8();\n      return new l({\n        publicKeys: r,\n        signaturesRequired: t\n      });\n    }\n    createBitmap(e) {\n      let {\n          bits: r\n        } = e,\n        t = 128,\n        i = new Uint8Array([0, 0, 0, 0]),\n        a = new Set();\n      return r.forEach((n, u) => {\n        if (u + 1 > this.publicKeys.length) throw new Error(`Signature index ${u + 1} is out of public keys range, ${this.publicKeys.length}.`);\n        if (a.has(n)) throw new Error(`Duplicate bit ${n} detected.`);\n        a.add(n);\n        let c = Math.floor(n / 8),\n          y = i[c];\n        y |= t >> n % 8, i[c] = y;\n      }), i;\n    }\n    getIndex(e) {\n      let r = e instanceof o ? e : new o(e),\n        t = this.publicKeys.findIndex(i => i.toString() === r.toString());\n      if (t !== -1) return t;\n      throw new Error(\"Public key not found in MultiKey\");\n    }\n  },\n  s = class s extends g {\n    constructor(e) {\n      super();\n      let {\n        signatures: r,\n        bitmap: t\n      } = e;\n      if (r.length > s.MAX_SIGNATURES_SUPPORTED) throw new Error(`The number of signatures cannot be greater than ${s.MAX_SIGNATURES_SUPPORTED}`);\n      if (this.signatures = r.map(a => a instanceof h ? a : new h(a)), !(t instanceof Uint8Array)) this.bitmap = s.createBitmap({\n        bits: t\n      });else {\n        if (t.length !== s.BITMAP_LEN) throw new Error(`\"bitmap\" length should be ${s.BITMAP_LEN}`);\n        this.bitmap = t;\n      }\n      let i = this.bitmap.reduce((a, n) => a + d(n), 0);\n      if (i !== this.signatures.length) throw new Error(`Expecting ${i} signatures from the bitmap, but got ${this.signatures.length}`);\n    }\n    static createBitmap(e) {\n      let {\n          bits: r\n        } = e,\n        t = 128,\n        i = new Uint8Array([0, 0, 0, 0]),\n        a = new Set();\n      return r.forEach(n => {\n        if (n >= s.MAX_SIGNATURES_SUPPORTED) throw new Error(`Cannot have a signature larger than ${s.MAX_SIGNATURES_SUPPORTED - 1}.`);\n        if (a.has(n)) throw new Error(\"Duplicate bits detected.\");\n        a.add(n);\n        let u = Math.floor(n / 8),\n          c = i[u];\n        c |= t >> n % 8, i[u] = c;\n      }), i;\n    }\n    toUint8Array() {\n      return this.bcsToBytes();\n    }\n    serialize(e) {\n      e.serializeVector(this.signatures), e.serializeBytes(this.bitmap);\n    }\n    static deserialize(e) {\n      let r = e.deserializeVector(h),\n        t = e.deserializeBytes();\n      return new s({\n        signatures: r,\n        bitmap: t\n      });\n    }\n  };\ns.BITMAP_LEN = 4, s.MAX_SIGNATURES_SUPPORTED = s.BITMAP_LEN * 8;\nvar p = s;\nexport { f as a, p as b };", "map": {"version": 3, "names": ["d", "l", "e", "f", "m", "constructor", "publicKeys", "r", "signaturesRequired", "t", "Error", "length", "map", "i", "o", "verifySignature", "auth<PERSON><PERSON>", "b", "fromSchemeAndBytes", "scheme", "input", "toUint8Array", "bcsToBytes", "serialize", "serializeVector", "serializeU8", "deserialize", "deserializeVector", "deserializeU8", "createBitmap", "bits", "Uint8Array", "a", "Set", "for<PERSON>ach", "n", "u", "has", "add", "c", "Math", "floor", "y", "getIndex", "findIndex", "toString", "s", "g", "signatures", "bitmap", "MAX_SIGNATURES_SUPPORTED", "h", "BITMAP_LEN", "reduce", "serializeBytes", "deserializeBytes", "p"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\multiKey.ts"], "sourcesContent": ["import { SigningScheme as AuthenticationKeyScheme } from \"../../types\";\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializer } from \"../../bcs/serializer\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { AccountPublicKey, PublicKey, VerifySignatureArgs } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { AnyPublicKey, AnySignature } from \"./singleKey\";\n\n/* eslint-disable no-bitwise */\n\nfunction bitCount(byte: number) {\n  let n = byte;\n  n -= (n >> 1) & 0x55555555;\n  n = (n & 0x33333333) + ((n >> 2) & 0x33333333);\n  return (((n + (n >> 4)) & 0xf0f0f0f) * 0x1010101) >> 24;\n}\n\n/* eslint-enable no-bitwise */\n\n/**\n * Represents the public key of a multi-agent account.\n *\n * The public keys of each individual agent can be any type of public key supported by Aptos.\n * Since [AIP-55](https://github.com/aptos-foundation/AIPs/pull/263) Aptos supports\n * `Legacy` and `Unified` authentication keys.\n */\nexport class MultiKey extends AccountPublicKey {\n  /**\n   * List of any public keys\n   */\n  public readonly publicKeys: AnyPublicKey[];\n\n  /**\n   * The minimum number of valid signatures required, for the number of public keys specified\n   */\n  public readonly signaturesRequired: number;\n\n  // region Constructors\n\n  constructor(args: { publicKeys: Array<PublicKey>; signaturesRequired: number }) {\n    super();\n    const { publicKeys, signaturesRequired } = args;\n\n    // Validate number of public keys is greater than signature required\n    if (signaturesRequired < 1) {\n      throw new Error(\"The number of required signatures needs to be greater than 0\");\n    }\n\n    // Validate number of public keys is greater than signature required\n    if (publicKeys.length < signaturesRequired) {\n      throw new Error(\n        `Provided ${publicKeys.length} public keys is smaller than the ${signaturesRequired} required signatures`,\n      );\n    }\n\n    // Make sure that all keys are normalized to the SingleKey authentication scheme\n    this.publicKeys = publicKeys.map((publicKey) =>\n      publicKey instanceof AnyPublicKey ? publicKey : new AnyPublicKey(publicKey),\n    );\n\n    this.signaturesRequired = signaturesRequired;\n  }\n\n  // endregion\n\n  // region AccountPublicKey\n\n  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars\n  verifySignature(args: VerifySignatureArgs): boolean {\n    throw new Error(\"not implemented\");\n  }\n\n  authKey(): AuthenticationKey {\n    return AuthenticationKey.fromSchemeAndBytes({\n      scheme: AuthenticationKeyScheme.MultiKey,\n      input: this.toUint8Array(),\n    });\n  }\n\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeVector(this.publicKeys);\n    serializer.serializeU8(this.signaturesRequired);\n  }\n\n  static deserialize(deserializer: Deserializer): MultiKey {\n    const keys = deserializer.deserializeVector(AnyPublicKey);\n    const signaturesRequired = deserializer.deserializeU8();\n\n    return new MultiKey({ publicKeys: keys, signaturesRequired });\n  }\n\n  // endregion\n\n  /**\n   * Create a bitmap that holds the mapping from the original public keys\n   * to the signatures passed in\n   *\n   * @param args.bits array of the index mapping to the matching public keys\n   * @returns Uint8array bit map\n   */\n  createBitmap(args: { bits: number[] }): Uint8Array {\n    const { bits } = args;\n    // Bits are read from left to right. e.g. 0b10000000 represents the first bit is set in one byte.\n    // The decimal value of 0b10000000 is 128.\n    const firstBitInByte = 128;\n    const bitmap = new Uint8Array([0, 0, 0, 0]);\n\n    // Check if duplicates exist in bits\n    const dupCheckSet = new Set();\n\n    bits.forEach((bit: number, idx: number) => {\n      if (idx + 1 > this.publicKeys.length) {\n        throw new Error(`Signature index ${idx + 1} is out of public keys range, ${this.publicKeys.length}.`);\n      }\n\n      if (dupCheckSet.has(bit)) {\n        throw new Error(`Duplicate bit ${bit} detected.`);\n      }\n\n      dupCheckSet.add(bit);\n\n      const byteOffset = Math.floor(bit / 8);\n\n      let byte = bitmap[byteOffset];\n\n      // eslint-disable-next-line no-bitwise\n      byte |= firstBitInByte >> bit % 8;\n\n      bitmap[byteOffset] = byte;\n    });\n\n    return bitmap;\n  }\n\n  /**\n   * Get the index of the provided public key.\n   *\n   * @param publicKey array of the index mapping to the matching public keys\n   * @returns the corresponding index of the publicKey, if it exists\n   */\n  getIndex(publicKey: PublicKey): number {\n    const anyPublicKey = publicKey instanceof AnyPublicKey ? publicKey : new AnyPublicKey(publicKey);\n    const index = this.publicKeys.findIndex((pk) => pk.toString() === anyPublicKey.toString());\n\n    if (index !== -1) {\n      return index;\n    }\n    throw new Error(\"Public key not found in MultiKey\");\n  }\n}\n\nexport class MultiKeySignature extends Signature {\n  /**\n   * Number of bytes in the bitmap representing who signed the transaction (32-bits)\n   */\n  static BITMAP_LEN: number = 4;\n\n  /**\n   * Maximum number of Ed25519 signatures supported\n   */\n  static MAX_SIGNATURES_SUPPORTED = MultiKeySignature.BITMAP_LEN * 8;\n\n  /**\n   * The list of underlying Ed25519 signatures\n   */\n  public readonly signatures: AnySignature[];\n\n  /**\n   * 32-bit Bitmap representing who signed the transaction\n   *\n   * This is represented where each public key can be masked to determine whether the message was signed by that key.\n   */\n  public readonly bitmap: Uint8Array;\n\n  /**\n   * Signature for a K-of-N multi-sig transaction.\n   *\n   * @see {@link\n   * https://aptos.dev/integration/creating-a-signed-transaction/#multisignature-transactions | Creating a Signed Transaction}\n   *\n   * @param args.signatures A list of signatures\n   * @param args.bitmap 4 bytes, at most 32 signatures are supported. If Nth bit value is `1`, the Nth\n   * signature should be provided in `signatures`. Bits are read from left to right\n   */\n  constructor(args: { signatures: Array<Signature | AnySignature>; bitmap: Uint8Array | number[] }) {\n    super();\n    const { signatures, bitmap } = args;\n\n    if (signatures.length > MultiKeySignature.MAX_SIGNATURES_SUPPORTED) {\n      throw new Error(`The number of signatures cannot be greater than ${MultiKeySignature.MAX_SIGNATURES_SUPPORTED}`);\n    }\n\n    // Make sure that all signatures are normalized to the SingleKey authentication scheme\n    this.signatures = signatures.map((signature) =>\n      signature instanceof AnySignature ? signature : new AnySignature(signature),\n    );\n\n    if (!(bitmap instanceof Uint8Array)) {\n      this.bitmap = MultiKeySignature.createBitmap({ bits: bitmap });\n    } else if (bitmap.length !== MultiKeySignature.BITMAP_LEN) {\n      throw new Error(`\"bitmap\" length should be ${MultiKeySignature.BITMAP_LEN}`);\n    } else {\n      this.bitmap = bitmap;\n    }\n\n    const nSignatures = this.bitmap.reduce((acc, byte) => acc + bitCount(byte), 0);\n    if (nSignatures !== this.signatures.length) {\n      throw new Error(`Expecting ${nSignatures} signatures from the bitmap, but got ${this.signatures.length}`);\n    }\n  }\n\n  /**\n   * Helper method to create a bitmap out of the specified bit positions\n   * @param args.bits The bitmap positions that should be set. A position starts at index 0.\n   * Valid position should range between 0 and 31.\n   * @example\n   * Here's an example of valid `bits`\n   * ```\n   * [0, 2, 31]\n   * ```\n   * `[0, 2, 31]` means the 1st, 3rd and 32nd bits should be set in the bitmap.\n   * The result bitmap should be 0b1010000000000000000000000000001\n   *\n   * @returns bitmap that is 32bit long\n   */\n  static createBitmap(args: { bits: number[] }): Uint8Array {\n    const { bits } = args;\n    // Bits are read from left to right. e.g. 0b10000000 represents the first bit is set in one byte.\n    // The decimal value of 0b10000000 is 128.\n    const firstBitInByte = 128;\n    const bitmap = new Uint8Array([0, 0, 0, 0]);\n\n    // Check if duplicates exist in bits\n    const dupCheckSet = new Set();\n\n    bits.forEach((bit: number) => {\n      if (bit >= MultiKeySignature.MAX_SIGNATURES_SUPPORTED) {\n        throw new Error(`Cannot have a signature larger than ${MultiKeySignature.MAX_SIGNATURES_SUPPORTED - 1}.`);\n      }\n\n      if (dupCheckSet.has(bit)) {\n        throw new Error(\"Duplicate bits detected.\");\n      }\n\n      dupCheckSet.add(bit);\n\n      const byteOffset = Math.floor(bit / 8);\n\n      let byte = bitmap[byteOffset];\n\n      // eslint-disable-next-line no-bitwise\n      byte |= firstBitInByte >> bit % 8;\n\n      bitmap[byteOffset] = byte;\n    });\n\n    return bitmap;\n  }\n\n  // region Signature\n\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    // Note: we should not need to serialize the vector length, as it can be derived from the bitmap\n    serializer.serializeVector(this.signatures);\n    serializer.serializeBytes(this.bitmap);\n  }\n\n  static deserialize(deserializer: Deserializer): MultiKeySignature {\n    const signatures = deserializer.deserializeVector(AnySignature);\n    const bitmap = deserializer.deserializeBytes();\n    return new MultiKeySignature({ signatures, bitmap });\n  }\n\n  // endregion\n}\n"], "mappings": ";;;;AAUA,SAASA,EAASC,CAAA,EAAc;EAC9B,IAAIC,CAAA,GAAID,CAAA;EACR,OAAAC,CAAA,IAAMA,CAAA,IAAK,IAAK,YAChBA,CAAA,IAAKA,CAAA,GAAI,cAAgBA,CAAA,IAAK,IAAK,aACzBA,CAAA,IAAKA,CAAA,IAAK,KAAM,aAAa,YAAc,EACvD;AAAA;AAWO,IAAMC,CAAA,GAAN,MAAMF,CAAA,SAAiBG,CAAiB;IAa7CC,YAAYH,CAAA,EAAoE;MAC9E,MAAM;MACN,IAAM;QAAEI,UAAA,EAAAC,CAAA;QAAYC,kBAAA,EAAAC;MAAmB,IAAIP,CAAA;MAG3C,IAAIO,CAAA,GAAqB,GACvB,MAAM,IAAIC,KAAA,CAAM,8DAA8D;MAIhF,IAAIH,CAAA,CAAWI,MAAA,GAASF,CAAA,EACtB,MAAM,IAAIC,KAAA,CACR,YAAYH,CAAA,CAAWI,MAAM,oCAAoCF,CAAkB,sBACrF;MAIF,KAAKH,UAAA,GAAaC,CAAA,CAAWK,GAAA,CAAKC,CAAA,IAChCA,CAAA,YAAqBC,CAAA,GAAeD,CAAA,GAAY,IAAIC,CAAA,CAAaD,CAAS,CAC5E,GAEA,KAAKL,kBAAA,GAAqBC,CAC5B;IAAA;IAOAM,gBAAgBb,CAAA,EAAoC;MAClD,MAAM,IAAIQ,KAAA,CAAM,iBAAiB,CACnC;IAAA;IAEAM,QAAA,EAA6B;MAC3B,OAAOC,CAAA,CAAkBC,kBAAA,CAAmB;QAC1CC,MAAA;QACAC,KAAA,EAAO,KAAKC,YAAA,CAAa;MAC3B,CAAC,CACH;IAAA;IAEAA,aAAA,EAA2B;MACzB,OAAO,KAAKC,UAAA,CAAW,CACzB;IAAA;IAMAC,UAAUrB,CAAA,EAA8B;MACtCA,CAAA,CAAWsB,eAAA,CAAgB,KAAKlB,UAAU,GAC1CJ,CAAA,CAAWuB,WAAA,CAAY,KAAKjB,kBAAkB,CAChD;IAAA;IAEA,OAAOkB,YAAYxB,CAAA,EAAsC;MACvD,IAAMK,CAAA,GAAOL,CAAA,CAAayB,iBAAA,CAAkBb,CAAY;QAClDL,CAAA,GAAqBP,CAAA,CAAa0B,aAAA,CAAc;MAEtD,OAAO,IAAI3B,CAAA,CAAS;QAAEK,UAAA,EAAYC,CAAA;QAAMC,kBAAA,EAAAC;MAAmB,CAAC,CAC9D;IAAA;IAWAoB,aAAa3B,CAAA,EAAsC;MACjD,IAAM;UAAE4B,IAAA,EAAAvB;QAAK,IAAIL,CAAA;QAGXO,CAAA,GAAiB;QACjBI,CAAA,GAAS,IAAIkB,UAAA,CAAW,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAGpCC,CAAA,GAAc,IAAIC,GAAA;MAExB,OAAA1B,CAAA,CAAK2B,OAAA,CAAQ,CAACC,CAAA,EAAaC,CAAA,KAAgB;QACzC,IAAIA,CAAA,GAAM,IAAI,KAAK9B,UAAA,CAAWK,MAAA,EAC5B,MAAM,IAAID,KAAA,CAAM,mBAAmB0B,CAAA,GAAM,CAAC,iCAAiC,KAAK9B,UAAA,CAAWK,MAAM,GAAG;QAGtG,IAAIqB,CAAA,CAAYK,GAAA,CAAIF,CAAG,GACrB,MAAM,IAAIzB,KAAA,CAAM,iBAAiByB,CAAG,YAAY;QAGlDH,CAAA,CAAYM,GAAA,CAAIH,CAAG;QAEnB,IAAMI,CAAA,GAAaC,IAAA,CAAKC,KAAA,CAAMN,CAAA,GAAM,CAAC;UAEjCO,CAAA,GAAO7B,CAAA,CAAO0B,CAAU;QAG5BG,CAAA,IAAQjC,CAAA,IAAkB0B,CAAA,GAAM,GAEhCtB,CAAA,CAAO0B,CAAU,IAAIG,CACvB;MAAA,CAAC,GAEM7B,CACT;IAAA;IAQA8B,SAASzC,CAAA,EAA8B;MACrC,IAAMK,CAAA,GAAeL,CAAA,YAAqBY,CAAA,GAAeZ,CAAA,GAAY,IAAIY,CAAA,CAAaZ,CAAS;QACzFO,CAAA,GAAQ,KAAKH,UAAA,CAAWsC,SAAA,CAAW/B,CAAA,IAAOA,CAAA,CAAGgC,QAAA,CAAS,MAAMtC,CAAA,CAAasC,QAAA,CAAS,CAAC;MAEzF,IAAIpC,CAAA,KAAU,IACZ,OAAOA,CAAA;MAET,MAAM,IAAIC,KAAA,CAAM,kCAAkC,CACpD;IAAA;EACF;EAEaoC,CAAA,GAAN,MAAMA,CAAA,SAA0BC,CAAU;IAiC/C1C,YAAYH,CAAA,EAAsF;MAChG,MAAM;MACN,IAAM;QAAE8C,UAAA,EAAAzC,CAAA;QAAY0C,MAAA,EAAAxC;MAAO,IAAIP,CAAA;MAE/B,IAAIK,CAAA,CAAWI,MAAA,GAASmC,CAAA,CAAkBI,wBAAA,EACxC,MAAM,IAAIxC,KAAA,CAAM,mDAAmDoC,CAAA,CAAkBI,wBAAwB,EAAE;MAQjH,IAJA,KAAKF,UAAA,GAAazC,CAAA,CAAWK,GAAA,CAAKoB,CAAA,IAChCA,CAAA,YAAqBmB,CAAA,GAAenB,CAAA,GAAY,IAAImB,CAAA,CAAanB,CAAS,CAC5E,GAEI,EAAEvB,CAAA,YAAkBsB,UAAA,GACtB,KAAKkB,MAAA,GAASH,CAAA,CAAkBjB,YAAA,CAAa;QAAEC,IAAA,EAAMrB;MAAO,CAAC,OACxD;QAAA,IAAIA,CAAA,CAAOE,MAAA,KAAWmC,CAAA,CAAkBM,UAAA,EAC7C,MAAM,IAAI1C,KAAA,CAAM,6BAA6BoC,CAAA,CAAkBM,UAAU,EAAE;QAE3E,KAAKH,MAAA,GAASxC,CAAA;MAAA;MAGhB,IAAMI,CAAA,GAAc,KAAKoC,MAAA,CAAOI,MAAA,CAAO,CAACrB,CAAA,EAAKG,CAAA,KAASH,CAAA,GAAMhC,CAAA,CAASmC,CAAI,GAAG,CAAC;MAC7E,IAAItB,CAAA,KAAgB,KAAKmC,UAAA,CAAWrC,MAAA,EAClC,MAAM,IAAID,KAAA,CAAM,aAAaG,CAAW,wCAAwC,KAAKmC,UAAA,CAAWrC,MAAM,EAAE,CAE5G;IAAA;IAgBA,OAAOkB,aAAa3B,CAAA,EAAsC;MACxD,IAAM;UAAE4B,IAAA,EAAAvB;QAAK,IAAIL,CAAA;QAGXO,CAAA,GAAiB;QACjBI,CAAA,GAAS,IAAIkB,UAAA,CAAW,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAGpCC,CAAA,GAAc,IAAIC,GAAA;MAExB,OAAA1B,CAAA,CAAK2B,OAAA,CAASC,CAAA,IAAgB;QAC5B,IAAIA,CAAA,IAAOW,CAAA,CAAkBI,wBAAA,EAC3B,MAAM,IAAIxC,KAAA,CAAM,uCAAuCoC,CAAA,CAAkBI,wBAAA,GAA2B,CAAC,GAAG;QAG1G,IAAIlB,CAAA,CAAYK,GAAA,CAAIF,CAAG,GACrB,MAAM,IAAIzB,KAAA,CAAM,0BAA0B;QAG5CsB,CAAA,CAAYM,GAAA,CAAIH,CAAG;QAEnB,IAAMC,CAAA,GAAaI,IAAA,CAAKC,KAAA,CAAMN,CAAA,GAAM,CAAC;UAEjCI,CAAA,GAAO1B,CAAA,CAAOuB,CAAU;QAG5BG,CAAA,IAAQ9B,CAAA,IAAkB0B,CAAA,GAAM,GAEhCtB,CAAA,CAAOuB,CAAU,IAAIG,CACvB;MAAA,CAAC,GAEM1B,CACT;IAAA;IAIAQ,aAAA,EAA2B;MACzB,OAAO,KAAKC,UAAA,CAAW,CACzB;IAAA;IAMAC,UAAUrB,CAAA,EAA8B;MAEtCA,CAAA,CAAWsB,eAAA,CAAgB,KAAKwB,UAAU,GAC1C9C,CAAA,CAAWoD,cAAA,CAAe,KAAKL,MAAM,CACvC;IAAA;IAEA,OAAOvB,YAAYxB,CAAA,EAA+C;MAChE,IAAMK,CAAA,GAAaL,CAAA,CAAayB,iBAAA,CAAkBwB,CAAY;QACxD1C,CAAA,GAASP,CAAA,CAAaqD,gBAAA,CAAiB;MAC7C,OAAO,IAAIT,CAAA,CAAkB;QAAEE,UAAA,EAAAzC,CAAA;QAAY0C,MAAA,EAAAxC;MAAO,CAAC,CACrD;IAAA;EAGF;AAnIaqC,CAAA,CAIJM,UAAA,GAAqB,GAJjBN,CAAA,CASJI,wBAAA,GAA2BJ,CAAA,CAAkBM,UAAA,GAAa;AAT5D,IAAMI,CAAA,GAANV,CAAA;AAAA,SAAA3C,CAAA,IAAA6B,CAAA,EAAAwB,CAAA,IAAAvC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}