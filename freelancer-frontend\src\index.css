@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, var(--secondary-50) 0%, var(--primary-50) 100%);
  color: var(--secondary-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-100);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-400);
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(
    90deg,
    var(--secondary-200) 25%,
    var(--secondary-100) 50%,
    var(--secondary-200) 75%
  );
  background-size: 200px 100%;
}

/* Button Components */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500 shadow-sm hover:shadow-md;
}

.btn-success {
  @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-md hover:shadow-lg;
}

.btn-warning {
  @apply btn bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500 shadow-md hover:shadow-lg;
}

.btn-danger {
  @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-md hover:shadow-lg;
}

.btn-ghost {
  @apply btn text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 focus:ring-secondary-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-xl {
  @apply px-8 py-4 text-lg;
}

/* Card Components */
.card {
  @apply bg-white rounded-xl shadow-md border border-secondary-200 overflow-hidden transition-all duration-200;
}

.card-hover {
  @apply card hover:shadow-lg hover:-translate-y-1;
}

.card-interactive {
  @apply card-hover cursor-pointer;
}

.card-header {
  @apply px-6 py-4 border-b border-secondary-200 bg-secondary-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-secondary-200 bg-secondary-50;
}

/* Form Components */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-secondary-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white;
}

.form-input:hover {
  @apply border-secondary-400;
}

.form-input.error {
  @apply border-red-500 focus:ring-red-500;
}

.form-input.success {
  @apply border-green-500 focus:ring-green-500;
}

.form-textarea {
  @apply form-input resize-vertical min-h-[100px];
}

.form-select {
  @apply form-input cursor-pointer;
}

.form-error {
  @apply text-red-600 text-sm;
}

.form-help {
  @apply text-secondary-500 text-sm;
}

/* Badge Components */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply badge bg-primary-100 text-primary-800;
}

.badge-secondary {
  @apply badge bg-secondary-100 text-secondary-800;
}

.badge-success {
  @apply badge bg-green-100 text-green-800;
}

.badge-warning {
  @apply badge bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply badge bg-red-100 text-red-800;
}

.badge-info {
  @apply badge bg-blue-100 text-blue-800;
}

/* Status Indicators */
.status-dot {
  @apply inline-block w-2 h-2 rounded-full;
}

.status-online {
  @apply status-dot bg-green-500;
}

.status-offline {
  @apply status-dot bg-red-500;
}

.status-away {
  @apply status-dot bg-yellow-500;
}

/* Loading States */
.skeleton {
  @apply animate-shimmer bg-secondary-200 rounded;
}

.skeleton-text {
  @apply skeleton h-4 w-full;
}

.skeleton-title {
  @apply skeleton h-6 w-3/4;
}

.skeleton-avatar {
  @apply skeleton w-10 h-10 rounded-full;
}

.skeleton-button {
  @apply skeleton h-10 w-24;
}

/* Modal and Overlay */
.overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-200;
}

.modal {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
}

.modal-content {
  @apply bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-fade-in;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-secondary-200;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-secondary-200;
}

/* Dropdown */
.dropdown {
  @apply relative inline-block;
}

.dropdown-menu {
  @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 py-1 z-50 opacity-0 invisible transition-all duration-200;
}

.dropdown-menu.show {
  @apply opacity-100 visible;
}

.dropdown-item {
  @apply block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900 transition-colors duration-150;
}

/* Tooltip */
.tooltip {
  @apply relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-secondary-900 rounded opacity-0 invisible transition-all duration-200 whitespace-nowrap;
}

.tooltip:hover::before {
  @apply opacity-100 visible;
}

/* Layout Utilities */
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section {
  @apply py-12 lg:py-16;
}

.section-sm {
  @apply py-8 lg:py-12;
}

.section-lg {
  @apply py-16 lg:py-24;
}

/* Text Utilities */
.text-gradient {
  @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
}

.text-balance {
  text-wrap: balance;
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Responsive Grid */
.grid-responsive {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Custom Scrollbar for specific elements */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--secondary-300) var(--secondary-100);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--secondary-100);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--secondary-300);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-400);
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    @apply shadow-none border border-secondary-300;
  }

  .btn {
    @apply shadow-none;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    --secondary-50: #0f172a;
    --secondary-100: #1e293b;
    --secondary-200: #334155;
    --secondary-300: #475569;
    --secondary-400: #64748b;
    --secondary-500: #94a3b8;
    --secondary-600: #cbd5e1;
    --secondary-700: #e2e8f0;
    --secondary-800: #f1f5f9;
    --secondary-900: #f8fafc;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-secondary-900;
  }

  .btn {
    @apply border-2 border-current;
  }
}
