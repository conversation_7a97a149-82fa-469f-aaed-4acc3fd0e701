{"ast": null, "code": "var n = \"aptos:signTransaction\";\nexport { n as a };", "map": {"version": 3, "names": ["n", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\wallet-standard\\src\\features\\aptosSignTransaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AnyRawTransaction, AccountAuthenticator } from '@aptos-labs/ts-sdk'\nimport { UserResponse } from '../misc'\n\n/** Version of the feature. */\nexport type AptosSignTransactionVersion = '1.0.0'\n/** Name of the feature. */\nexport const AptosSignTransactionNamespace = 'aptos:signTransaction'\n/**\n * A Wallet Standard feature for signing a Aptos transaction, and returning the\n * account authenticator.\n */\nexport type AptosSignTransactionFeature = {\n  /** Namespace for the feature. */\n  [AptosSignTransactionNamespace]: {\n    /** Version of the feature API. */\n    version: AptosSignTransactionVersion\n    signTransaction: AptosSignTransactionMethod\n  }\n}\n/** TODO: docs */\nexport type AptosSignTransactionMethod = (\n  transaction: AnyRawTransaction,\n  asFeePayer?: boolean\n) => Promise<UserResponse<AptosSignTransactionOutput>>\n\n/** Output of signing transactions. */\nexport type AptosSignTransactionOutput = AccountAuthenticator\n"], "mappings": "AASO,IAAMA,CAAA,GAAgC;AAAA,SAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}