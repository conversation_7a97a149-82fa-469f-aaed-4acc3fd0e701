import React, { useState } from 'react';
import { useEscrow } from '../contexts/EscrowContext';
import { X, Calendar, DollarSign, User, FileText, Tag } from 'lucide-react';

const CreateEscrowModal = ({ onClose, onSuccess }) => {
  const { createEscrow, loading } = useEscrow();
  const [formData, setFormData] = useState({
    freelancerAddress: '',
    title: '',
    description: '',
    category: '',
    amount: '',
    deadline: ''
  });
  const [errors, setErrors] = useState({});

  const categories = [
    'Web Development',
    'Mobile Development',
    'Design',
    'Writing',
    'Marketing',
    'Blockchain',
    'Data Science',
    'Other'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.freelancerAddress.trim()) {
      newErrors.freelancerAddress = 'Freelancer address is required';
    } else if (!formData.freelancerAddress.startsWith('0x')) {
      newErrors.freelancerAddress = 'Address must start with 0x';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length > 1000) {
      newErrors.description = 'Description must be less than 1000 characters';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.amount) {
      newErrors.amount = 'Amount is required';
    } else if (parseFloat(formData.amount) < 0.01) {
      newErrors.amount = 'Minimum amount is 0.01 APT';
    }

    if (!formData.deadline) {
      newErrors.deadline = 'Deadline is required';
    } else if (new Date(formData.deadline) <= new Date()) {
      newErrors.deadline = 'Deadline must be in the future';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      const deadlineTimestamp = Math.floor(new Date(formData.deadline).getTime() / 1000);
      
      await createEscrow({
        freelancerAddress: formData.freelancerAddress,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        amount: formData.amount,
        deadline: deadlineTimestamp
      });

      onSuccess();
    } catch (error) {
      console.error('Error creating escrow:', error);
      alert('Failed to create project: ' + error.message);
    }
  };

  const calculatePlatformFee = () => {
    const amount = parseFloat(formData.amount) || 0;
    return (amount * 0.025).toFixed(4); // 2.5% platform fee
  };

  const calculateTotal = () => {
    const amount = parseFloat(formData.amount) || 0;
    const fee = parseFloat(calculatePlatformFee());
    return (amount + fee).toFixed(4);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-secondary-200">
          <h2 className="text-xl font-semibold text-secondary-900">Create New Project</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-secondary-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-secondary-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Freelancer Address */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              <User className="w-4 h-4 inline mr-2" />
              Freelancer Address
            </label>
            <input
              type="text"
              name="freelancerAddress"
              value={formData.freelancerAddress}
              onChange={handleInputChange}
              placeholder="0x..."
              className={`input-field ${errors.freelancerAddress ? 'border-red-500' : ''}`}
            />
            {errors.freelancerAddress && (
              <p className="text-red-500 text-sm mt-1">{errors.freelancerAddress}</p>
            )}
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              <FileText className="w-4 h-4 inline mr-2" />
              Project Title
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter project title"
              className={`input-field ${errors.title ? 'border-red-500' : ''}`}
            />
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              Project Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Describe the project requirements..."
              rows={4}
              className={`input-field ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-2">
              <Tag className="w-4 h-4 inline mr-2" />
              Category
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className={`input-field ${errors.category ? 'border-red-500' : ''}`}
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            {errors.category && (
              <p className="text-red-500 text-sm mt-1">{errors.category}</p>
            )}
          </div>

          {/* Amount and Deadline */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                <DollarSign className="w-4 h-4 inline mr-2" />
                Amount (APT)
              </label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0.01"
                className={`input-field ${errors.amount ? 'border-red-500' : ''}`}
              />
              {errors.amount && (
                <p className="text-red-500 text-sm mt-1">{errors.amount}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-2" />
                Deadline
              </label>
              <input
                type="datetime-local"
                name="deadline"
                value={formData.deadline}
                onChange={handleInputChange}
                className={`input-field ${errors.deadline ? 'border-red-500' : ''}`}
              />
              {errors.deadline && (
                <p className="text-red-500 text-sm mt-1">{errors.deadline}</p>
              )}
            </div>
          </div>

          {/* Cost Breakdown */}
          {formData.amount && (
            <div className="bg-secondary-50 p-4 rounded-lg">
              <h3 className="font-medium text-secondary-900 mb-2">Cost Breakdown</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Project Amount:</span>
                  <span>{formData.amount} APT</span>
                </div>
                <div className="flex justify-between">
                  <span>Platform Fee (2.5%):</span>
                  <span>{calculatePlatformFee()} APT</span>
                </div>
                <div className="flex justify-between font-medium border-t border-secondary-200 pt-1">
                  <span>Total:</span>
                  <span>{calculateTotal()} APT</span>
                </div>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary"
            >
              {loading ? 'Creating...' : 'Create Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateEscrowModal;
