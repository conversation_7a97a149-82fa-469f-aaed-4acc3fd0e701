{"ast": null, "code": "/**\n * Utils for modular division and fields.\n * Field over 11 is a finite (Galois) field is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { _validateObject, anumber, bitMask, bytesToNumberBE, bytesToNumberLE, ensureBytes, numberToBytesBE, numberToBytesLE } from \"../utils.js\";\n// prettier-ignore\nconst _0n = BigInt(0),\n  _1n = BigInt(1),\n  _2n = /* @__PURE__ */BigInt(2),\n  _3n = /* @__PURE__ */BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */BigInt(4),\n  _5n = /* @__PURE__ */BigInt(5),\n  _7n = /* @__PURE__ */BigInt(7);\n// prettier-ignore\nconst _8n = /* @__PURE__ */BigInt(8),\n  _9n = /* @__PURE__ */BigInt(9),\n  _16n = /* @__PURE__ */BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n  const result = a % b;\n  return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num, power, modulo) {\n  return FpPow(Field(modulo), num, power);\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x, power, modulo) {\n  let res = x;\n  while (power-- > _0n) {\n    res *= res;\n    res %= modulo;\n  }\n  return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number, modulo) {\n  if (number === _0n) throw new Error('invert: expected non-zero number');\n  if (modulo <= _0n) throw new Error('invert: expected positive modulus, got ' + modulo);\n  // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n  let a = mod(number, modulo);\n  let b = modulo;\n  // prettier-ignore\n  let x = _0n,\n    y = _1n,\n    u = _1n,\n    v = _0n;\n  while (a !== _0n) {\n    // JIT applies optimization if those two lines follow each other\n    const q = b / a;\n    const r = b % a;\n    const m = x - u * q;\n    const n = y - v * q;\n    // prettier-ignore\n    b = a, a = r, x = u, y = v, u = m, v = n;\n  }\n  const gcd = b;\n  if (gcd !== _1n) throw new Error('invert: does not exist');\n  return mod(x, modulo);\n}\nfunction assertIsSquare(Fp, root, n) {\n  if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n}\n// Not all roots are possible! Example which will throw:\n// const NUM =\n// n = 72057594037927816n;\n// Fp = Field(BigInt('0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaab'));\nfunction sqrt3mod4(Fp, n) {\n  const p1div4 = (Fp.ORDER + _1n) / _4n;\n  const root = Fp.pow(n, p1div4);\n  assertIsSquare(Fp, root, n);\n  return root;\n}\nfunction sqrt5mod8(Fp, n) {\n  const p5div8 = (Fp.ORDER - _5n) / _8n;\n  const n2 = Fp.mul(n, _2n);\n  const v = Fp.pow(n2, p5div8);\n  const nv = Fp.mul(n, v);\n  const i = Fp.mul(Fp.mul(nv, _2n), v);\n  const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n  assertIsSquare(Fp, root, n);\n  return root;\n}\n// Based on RFC9380, Kong algorithm\n// prettier-ignore\nfunction sqrt9mod16(P) {\n  const Fp_ = Field(P);\n  const tn = tonelliShanks(P);\n  const c1 = tn(Fp_, Fp_.neg(Fp_.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n  const c2 = tn(Fp_, c1); //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n  const c3 = tn(Fp_, Fp_.neg(c1)); //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n  const c4 = (P + _7n) / _16n; //  4. c4 = (q + 7) / 16        # Integer arithmetic\n  return (Fp, n) => {\n    let tv1 = Fp.pow(n, c4); //  1. tv1 = x^c4\n    let tv2 = Fp.mul(tv1, c1); //  2. tv2 = c1 * tv1\n    const tv3 = Fp.mul(tv1, c2); //  3. tv3 = c2 * tv1\n    const tv4 = Fp.mul(tv1, c3); //  4. tv4 = c3 * tv1\n    const e1 = Fp.eql(Fp.sqr(tv2), n); //  5.  e1 = (tv2^2) == x\n    const e2 = Fp.eql(Fp.sqr(tv3), n); //  6.  e2 = (tv3^2) == x\n    tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n    tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n    const e3 = Fp.eql(Fp.sqr(tv2), n); //  9.  e3 = (tv2^2) == x\n    const root = Fp.cmov(tv1, tv2, e3); // 10.  z = CMOV(tv1, tv2, e3)   # Select sqrt from tv1 & tv2\n    assertIsSquare(Fp, root, n);\n    return root;\n  };\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n  // Initialization (precomputation).\n  // Caching initialization could boost perf by 7%.\n  if (P < _3n) throw new Error('sqrt is not defined for small field');\n  // Factor P - 1 = Q * 2^S, where Q is odd\n  let Q = P - _1n;\n  let S = 0;\n  while (Q % _2n === _0n) {\n    Q /= _2n;\n    S++;\n  }\n  // Find the first quadratic non-residue Z >= 2\n  let Z = _2n;\n  const _Fp = Field(P);\n  while (FpLegendre(_Fp, Z) === 1) {\n    // Basic primality test for P. After x iterations, chance of\n    // not finding quadratic non-residue is 2^x, so 2^1000.\n    if (Z++ > 1000) throw new Error('Cannot find square root: probably non-prime P');\n  }\n  // Fast-path; usually done before Z, but we do \"primality test\".\n  if (S === 1) return sqrt3mod4;\n  // Slow-path\n  // TODO: test on Fp2 and others\n  let cc = _Fp.pow(Z, Q); // c = z^Q\n  const Q1div2 = (Q + _1n) / _2n;\n  return function tonelliSlow(Fp, n) {\n    if (Fp.is0(n)) return n;\n    // Check if n is a quadratic residue using Legendre symbol\n    if (FpLegendre(Fp, n) !== 1) throw new Error('Cannot find square root');\n    // Initialize variables for the main loop\n    let M = S;\n    let c = Fp.mul(Fp.ONE, cc); // c = z^Q, move cc from field _Fp into field Fp\n    let t = Fp.pow(n, Q); // t = n^Q, first guess at the fudge factor\n    let R = Fp.pow(n, Q1div2); // R = n^((Q+1)/2), first guess at the square root\n    // Main loop\n    // while t != 1\n    while (!Fp.eql(t, Fp.ONE)) {\n      if (Fp.is0(t)) return Fp.ZERO; // if t=0 return R=0\n      let i = 1;\n      // Find the smallest i >= 1 such that t^(2^i) ≡ 1 (mod P)\n      let t_tmp = Fp.sqr(t); // t^(2^1)\n      while (!Fp.eql(t_tmp, Fp.ONE)) {\n        i++;\n        t_tmp = Fp.sqr(t_tmp); // t^(2^2)...\n        if (i === M) throw new Error('Cannot find square root');\n      }\n      // Calculate the exponent for b: 2^(M - i - 1)\n      const exponent = _1n << BigInt(M - i - 1); // bigint is important\n      const b = Fp.pow(c, exponent); // b = 2^(M - i - 1)\n      // Update variables\n      M = i;\n      c = Fp.sqr(b); // c = b^2\n      t = Fp.mul(t, c); // t = (t * b^2)\n      R = Fp.mul(R, b); // R = R*b\n    }\n    return R;\n  };\n}\n/**\n * Square root for a finite field. Will try optimized versions first:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P) {\n  // P ≡ 3 (mod 4) => √n = n^((P+1)/4)\n  if (P % _4n === _3n) return sqrt3mod4;\n  // P ≡ 5 (mod 8) => Atkin algorithm, page 10 of https://eprint.iacr.org/2012/685.pdf\n  if (P % _8n === _5n) return sqrt5mod8;\n  // P ≡ 9 (mod 16) => Kong algorithm, page 11 of https://eprint.iacr.org/2012/685.pdf (algorithm 4)\n  if (P % _16n === _9n) return sqrt9mod16(P);\n  // Tonelli-Shanks algorithm\n  return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = ['create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr', 'eql', 'add', 'sub', 'mul', 'pow', 'div', 'addN', 'subN', 'mulN', 'sqrN'];\nexport function validateField(field) {\n  const initial = {\n    ORDER: 'bigint',\n    MASK: 'bigint',\n    BYTES: 'number',\n    BITS: 'number'\n  };\n  const opts = FIELD_FIELDS.reduce((map, val) => {\n    map[val] = 'function';\n    return map;\n  }, initial);\n  _validateObject(field, opts);\n  // const max = 16384;\n  // if (field.BYTES < 1 || field.BYTES > max) throw new Error('invalid field');\n  // if (field.BITS < 1 || field.BITS > 8 * max) throw new Error('invalid field');\n  return field;\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(Fp, num, power) {\n  if (power < _0n) throw new Error('invalid exponent, negatives unsupported');\n  if (power === _0n) return Fp.ONE;\n  if (power === _1n) return num;\n  let p = Fp.ONE;\n  let d = num;\n  while (power > _0n) {\n    if (power & _1n) p = Fp.mul(p, d);\n    d = Fp.sqr(d);\n    power >>= _1n;\n  }\n  return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * Exception-free. Will return `undefined` for 0 elements.\n * @param passZero map 0 to 0 (instead of undefined)\n */\nexport function FpInvertBatch(Fp, nums, passZero = false) {\n  const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : undefined);\n  // Walk from first to last, multiply them by each other MOD p\n  const multipliedAcc = nums.reduce((acc, num, i) => {\n    if (Fp.is0(num)) return acc;\n    inverted[i] = acc;\n    return Fp.mul(acc, num);\n  }, Fp.ONE);\n  // Invert last element\n  const invertedAcc = Fp.inv(multipliedAcc);\n  // Walk from last to first, multiply them by inverted each other MOD p\n  nums.reduceRight((acc, num, i) => {\n    if (Fp.is0(num)) return acc;\n    inverted[i] = Fp.mul(acc, inverted[i]);\n    return Fp.mul(acc, num);\n  }, invertedAcc);\n  return inverted;\n}\n// TODO: remove\nexport function FpDiv(Fp, lhs, rhs) {\n  return Fp.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));\n}\n/**\n * Legendre symbol.\n * Legendre constant is used to calculate Legendre symbol (a | p)\n * which denotes the value of a^((p-1)/2) (mod p).\n *\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre(Fp, n) {\n  // We can use 3rd argument as optional cache of this value\n  // but seems unneeded for now. The operation is very fast.\n  const p1mod2 = (Fp.ORDER - _1n) / _2n;\n  const powered = Fp.pow(n, p1mod2);\n  const yes = Fp.eql(powered, Fp.ONE);\n  const zero = Fp.eql(powered, Fp.ZERO);\n  const no = Fp.eql(powered, Fp.neg(Fp.ONE));\n  if (!yes && !zero && !no) throw new Error('invalid Legendre symbol result');\n  return yes ? 1 : zero ? 0 : -1;\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(Fp, n) {\n  const l = FpLegendre(Fp, n);\n  return l === 1;\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n  // Bit size, byte size of CURVE.n\n  if (nBitLength !== undefined) anumber(nBitLength);\n  const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n  const nByteLength = Math.ceil(_nBitLength / 8);\n  return {\n    nBitLength: _nBitLength,\n    nByteLength\n  };\n}\n/**\n * Creates a finite field. Major performance optimizations:\n * * 1. Denormalized operations like mulN instead of mul.\n * * 2. Identical object shape: never add or remove keys.\n * * 3. `Object.freeze`.\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n *\n * Note about field properties:\n * * CHARACTERISTIC p = prime number, number of elements in main subgroup.\n * * ORDER q = similar to cofactor in curves, may be composite `q = p^m`.\n *\n * @param ORDER field order, probably prime, or could be composite\n * @param bitLen how many bits the field consumes\n * @param isLE (default: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLenOrOpts,\n// TODO: use opts only in v2?\nisLE = false, opts = {}) {\n  if (ORDER <= _0n) throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n  let _nbitLength = undefined;\n  let _sqrt = undefined;\n  let modFromBytes = false;\n  let allowedLengths = undefined;\n  if (typeof bitLenOrOpts === 'object' && bitLenOrOpts != null) {\n    if (opts.sqrt || isLE) throw new Error('cannot specify opts in two arguments');\n    const _opts = bitLenOrOpts;\n    if (_opts.BITS) _nbitLength = _opts.BITS;\n    if (_opts.sqrt) _sqrt = _opts.sqrt;\n    if (typeof _opts.isLE === 'boolean') isLE = _opts.isLE;\n    if (typeof _opts.modFromBytes === 'boolean') modFromBytes = _opts.modFromBytes;\n    allowedLengths = _opts.allowedLengths;\n  } else {\n    if (typeof bitLenOrOpts === 'number') _nbitLength = bitLenOrOpts;\n    if (opts.sqrt) _sqrt = opts.sqrt;\n  }\n  const {\n    nBitLength: BITS,\n    nByteLength: BYTES\n  } = nLength(ORDER, _nbitLength);\n  if (BYTES > 2048) throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n  let sqrtP; // cached sqrtP\n  const f = Object.freeze({\n    ORDER,\n    isLE,\n    BITS,\n    BYTES,\n    MASK: bitMask(BITS),\n    ZERO: _0n,\n    ONE: _1n,\n    allowedLengths: allowedLengths,\n    create: num => mod(num, ORDER),\n    isValid: num => {\n      if (typeof num !== 'bigint') throw new Error('invalid field element: expected bigint, got ' + typeof num);\n      return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n    },\n    is0: num => num === _0n,\n    // is valid and invertible\n    isValidNot0: num => !f.is0(num) && f.isValid(num),\n    isOdd: num => (num & _1n) === _1n,\n    neg: num => mod(-num, ORDER),\n    eql: (lhs, rhs) => lhs === rhs,\n    sqr: num => mod(num * num, ORDER),\n    add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n    sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n    mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n    pow: (num, power) => FpPow(f, num, power),\n    div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n    // Same as above, but doesn't normalize\n    sqrN: num => num * num,\n    addN: (lhs, rhs) => lhs + rhs,\n    subN: (lhs, rhs) => lhs - rhs,\n    mulN: (lhs, rhs) => lhs * rhs,\n    inv: num => invert(num, ORDER),\n    sqrt: _sqrt || (n => {\n      if (!sqrtP) sqrtP = FpSqrt(ORDER);\n      return sqrtP(f, n);\n    }),\n    toBytes: num => isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES),\n    fromBytes: (bytes, skipValidation = true) => {\n      if (allowedLengths) {\n        if (!allowedLengths.includes(bytes.length) || bytes.length > BYTES) {\n          throw new Error('Field.fromBytes: expected ' + allowedLengths + ' bytes, got ' + bytes.length);\n        }\n        const padded = new Uint8Array(BYTES);\n        // isLE add 0 to right, !isLE to the left.\n        padded.set(bytes, isLE ? 0 : padded.length - bytes.length);\n        bytes = padded;\n      }\n      if (bytes.length !== BYTES) throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n      let scalar = isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n      if (modFromBytes) scalar = mod(scalar, ORDER);\n      if (!skipValidation) if (!f.isValid(scalar)) throw new Error('invalid field element: outside of range 0..ORDER');\n      // NOTE: we don't validate scalar here, please use isValid. This done such way because some\n      // protocol may allow non-reduced scalar that reduced later or changed some other way.\n      return scalar;\n    },\n    // TODO: we don't need it here, move out to separate fn\n    invertBatch: lst => FpInvertBatch(f, lst),\n    // We can't move this out because Fp6, Fp12 implement it\n    // and it's unclear what to return in there.\n    cmov: (a, b, c) => c ? b : a\n  });\n  return Object.freeze(f);\n}\n// Generic random scalar, we can do same for other fields if via Fp2.mul(Fp2.ONE, Fp2.random)?\n// This allows unsafe methods like ignore bias or zero. These unsafe, but often used in different protocols (if deterministic RNG).\n// which mean we cannot force this via opts.\n// Not sure what to do with randomBytes, we can accept it inside opts if wanted.\n// Probably need to export getMinHashLength somewhere?\n// random(bytes?: Uint8Array, unsafeAllowZero = false, unsafeAllowBias = false) {\n//   const LEN = !unsafeAllowBias ? getMinHashLength(ORDER) : BYTES;\n//   if (bytes === undefined) bytes = randomBytes(LEN); // _opts.randomBytes?\n//   const num = isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n//   // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n//   const reduced = unsafeAllowZero ? mod(num, ORDER) : mod(num, ORDER - _1n) + _1n;\n//   return reduced;\n// },\nexport function FpSqrtOdd(Fp, elm) {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n  hash = ensureBytes('privateHash', hash);\n  const hashLen = hash.length;\n  const minLen = nLength(groupOrder).nByteLength + 8;\n  if (minLen < 24 || hashLen < minLen || hashLen > 1024) throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n  const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n  return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n  if (typeof fieldOrder !== 'bigint') throw new Error('field order must be bigint');\n  const bitLength = fieldOrder.toString(2).length;\n  return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n  const length = getFieldBytesLength(fieldOrder);\n  return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n  const len = key.length;\n  const fieldLen = getFieldBytesLength(fieldOrder);\n  const minLen = getMinHashLength(fieldOrder);\n  // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n  if (len < 16 || len < minLen || len > 1024) throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n  const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n  // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n  const reduced = mod(num, fieldOrder - _1n) + _1n;\n  return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}", "map": {"version": 3, "names": ["_validateObject", "anumber", "bitMask", "bytesToNumberBE", "bytesToNumberLE", "ensureBytes", "numberToBytesBE", "numberToBytesLE", "_0n", "BigInt", "_1n", "_2n", "_3n", "_4n", "_5n", "_7n", "_8n", "_9n", "_16n", "mod", "a", "b", "result", "pow", "num", "power", "modulo", "FpPow", "Field", "pow2", "x", "res", "invert", "number", "Error", "y", "u", "v", "q", "r", "m", "n", "gcd", "assertIsSquare", "Fp", "root", "eql", "sqr", "sqrt3mod4", "p1div4", "ORDER", "sqrt5mod8", "p5div8", "n2", "mul", "nv", "i", "sub", "ONE", "sqrt9mod16", "P", "Fp_", "tn", "tonelliShanks", "c1", "neg", "c2", "c3", "c4", "tv1", "tv2", "tv3", "tv4", "e1", "e2", "cmov", "e3", "Q", "S", "Z", "_Fp", "FpLegendre", "cc", "Q1div2", "<PERSON><PERSON><PERSON><PERSON>", "is0", "M", "c", "t", "R", "ZERO", "t_tmp", "exponent", "FpSqrt", "isNegativeLE", "FIELD_FIELDS", "validateField", "field", "initial", "MASK", "BYTES", "BITS", "opts", "reduce", "map", "val", "p", "d", "FpInvertBatch", "nums", "passZero", "inverted", "Array", "length", "fill", "undefined", "multipliedAcc", "acc", "invertedAcc", "inv", "reduceRight", "FpDiv", "lhs", "rhs", "p1mod2", "powered", "yes", "zero", "no", "FpIsSquare", "l", "nLength", "nBitLength", "_nBitLength", "toString", "nByteLength", "Math", "ceil", "bitLenOrOpts", "isLE", "_nbitLength", "_sqrt", "modFromBytes", "allowedLengths", "sqrt", "_opts", "sqrtP", "f", "Object", "freeze", "create", "<PERSON><PERSON><PERSON><PERSON>", "isValidNot0", "isOdd", "add", "div", "sqrN", "addN", "subN", "mulN", "toBytes", "fromBytes", "bytes", "skipValidation", "includes", "padded", "Uint8Array", "set", "scalar", "invertBatch", "lst", "FpSqrtOdd", "elm", "FpSqrtEven", "hashToPrivateScalar", "hash", "groupOrder", "hashLen", "minLen", "getFieldBytesLength", "fieldOrder", "bitLength", "get<PERSON>in<PERSON>ash<PERSON><PERSON><PERSON>", "mapHashToField", "key", "len", "fieldLen", "reduced"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\abstract\\modular.ts"], "sourcesContent": ["/**\n * Utils for modular division and fields.\n * Field over 11 is a finite (Galois) field is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  _validateObject,\n  anumber,\n  bitMask,\n  bytesToNumberBE,\n  bytesToNumberLE,\n  ensureBytes,\n  numberToBytesBE,\n  numberToBytesLE,\n} from '../utils.ts';\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _7n = /* @__PURE__ */ BigInt(7);\n// prettier-ignore\nconst _8n = /* @__PURE__ */ BigInt(8), _9n = /* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n\n// Calculates a modulo b\nexport function mod(a: bigint, b: bigint): bigint {\n  const result = a % b;\n  return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num: bigint, power: bigint, modulo: bigint): bigint {\n  return FpPow(Field(modulo), num, power);\n}\n\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x: bigint, power: bigint, modulo: bigint): bigint {\n  let res = x;\n  while (power-- > _0n) {\n    res *= res;\n    res %= modulo;\n  }\n  return res;\n}\n\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number: bigint, modulo: bigint): bigint {\n  if (number === _0n) throw new Error('invert: expected non-zero number');\n  if (modulo <= _0n) throw new Error('invert: expected positive modulus, got ' + modulo);\n  // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n  let a = mod(number, modulo);\n  let b = modulo;\n  // prettier-ignore\n  let x = _0n, y = _1n, u = _1n, v = _0n;\n  while (a !== _0n) {\n    // JIT applies optimization if those two lines follow each other\n    const q = b / a;\n    const r = b % a;\n    const m = x - u * q;\n    const n = y - v * q;\n    // prettier-ignore\n    b = a, a = r, x = u, y = v, u = m, v = n;\n  }\n  const gcd = b;\n  if (gcd !== _1n) throw new Error('invert: does not exist');\n  return mod(x, modulo);\n}\n\nfunction assertIsSquare<T>(Fp: IField<T>, root: T, n: T): void {\n  if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n}\n\n// Not all roots are possible! Example which will throw:\n// const NUM =\n// n = 72057594037927816n;\n// Fp = Field(BigInt('0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaab'));\nfunction sqrt3mod4<T>(Fp: IField<T>, n: T) {\n  const p1div4 = (Fp.ORDER + _1n) / _4n;\n  const root = Fp.pow(n, p1div4);\n  assertIsSquare(Fp, root, n);\n  return root;\n}\n\nfunction sqrt5mod8<T>(Fp: IField<T>, n: T) {\n  const p5div8 = (Fp.ORDER - _5n) / _8n;\n  const n2 = Fp.mul(n, _2n);\n  const v = Fp.pow(n2, p5div8);\n  const nv = Fp.mul(n, v);\n  const i = Fp.mul(Fp.mul(nv, _2n), v);\n  const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n  assertIsSquare(Fp, root, n);\n  return root;\n}\n\n// Based on RFC9380, Kong algorithm\n// prettier-ignore\nfunction sqrt9mod16(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  const Fp_ = Field(P);\n  const tn = tonelliShanks(P);\n  const c1 = tn(Fp_, Fp_.neg(Fp_.ONE));//  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n  const c2 = tn(Fp_, c1);              //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n  const c3 = tn(Fp_, Fp_.neg(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n  const c4 = (P + _7n) / _16n;         //  4. c4 = (q + 7) / 16        # Integer arithmetic\n  return <T>(Fp: IField<T>, n: T) => {\n    let tv1 = Fp.pow(n, c4);           //  1. tv1 = x^c4\n    let tv2 = Fp.mul(tv1, c1);         //  2. tv2 = c1 * tv1\n    const tv3 = Fp.mul(tv1, c2);       //  3. tv3 = c2 * tv1\n    const tv4 = Fp.mul(tv1, c3);       //  4. tv4 = c3 * tv1\n    const e1 = Fp.eql(Fp.sqr(tv2), n); //  5.  e1 = (tv2^2) == x\n    const e2 = Fp.eql(Fp.sqr(tv3), n); //  6.  e2 = (tv3^2) == x\n    tv1 = Fp.cmov(tv1, tv2, e1);       //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n    tv2 = Fp.cmov(tv4, tv3, e2);       //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n    const e3 = Fp.eql(Fp.sqr(tv2), n); //  9.  e3 = (tv2^2) == x\n    const root = Fp.cmov(tv1, tv2, e3);// 10.  z = CMOV(tv1, tv2, e3)   # Select sqrt from tv1 & tv2\n    assertIsSquare(Fp, root, n);\n    return root;\n  };\n}\n\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  // Initialization (precomputation).\n  // Caching initialization could boost perf by 7%.\n  if (P < _3n) throw new Error('sqrt is not defined for small field');\n  // Factor P - 1 = Q * 2^S, where Q is odd\n  let Q = P - _1n;\n  let S = 0;\n  while (Q % _2n === _0n) {\n    Q /= _2n;\n    S++;\n  }\n\n  // Find the first quadratic non-residue Z >= 2\n  let Z = _2n;\n  const _Fp = Field(P);\n  while (FpLegendre(_Fp, Z) === 1) {\n    // Basic primality test for P. After x iterations, chance of\n    // not finding quadratic non-residue is 2^x, so 2^1000.\n    if (Z++ > 1000) throw new Error('Cannot find square root: probably non-prime P');\n  }\n  // Fast-path; usually done before Z, but we do \"primality test\".\n  if (S === 1) return sqrt3mod4;\n\n  // Slow-path\n  // TODO: test on Fp2 and others\n  let cc = _Fp.pow(Z, Q); // c = z^Q\n  const Q1div2 = (Q + _1n) / _2n;\n  return function tonelliSlow<T>(Fp: IField<T>, n: T): T {\n    if (Fp.is0(n)) return n;\n    // Check if n is a quadratic residue using Legendre symbol\n    if (FpLegendre(Fp, n) !== 1) throw new Error('Cannot find square root');\n\n    // Initialize variables for the main loop\n    let M = S;\n    let c = Fp.mul(Fp.ONE, cc); // c = z^Q, move cc from field _Fp into field Fp\n    let t = Fp.pow(n, Q); // t = n^Q, first guess at the fudge factor\n    let R = Fp.pow(n, Q1div2); // R = n^((Q+1)/2), first guess at the square root\n\n    // Main loop\n    // while t != 1\n    while (!Fp.eql(t, Fp.ONE)) {\n      if (Fp.is0(t)) return Fp.ZERO; // if t=0 return R=0\n      let i = 1;\n\n      // Find the smallest i >= 1 such that t^(2^i) ≡ 1 (mod P)\n      let t_tmp = Fp.sqr(t); // t^(2^1)\n      while (!Fp.eql(t_tmp, Fp.ONE)) {\n        i++;\n        t_tmp = Fp.sqr(t_tmp); // t^(2^2)...\n        if (i === M) throw new Error('Cannot find square root');\n      }\n\n      // Calculate the exponent for b: 2^(M - i - 1)\n      const exponent = _1n << BigInt(M - i - 1); // bigint is important\n      const b = Fp.pow(c, exponent); // b = 2^(M - i - 1)\n\n      // Update variables\n      M = i;\n      c = Fp.sqr(b); // c = b^2\n      t = Fp.mul(t, c); // t = (t * b^2)\n      R = Fp.mul(R, b); // R = R*b\n    }\n    return R;\n  };\n}\n\n/**\n * Square root for a finite field. Will try optimized versions first:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  // P ≡ 3 (mod 4) => √n = n^((P+1)/4)\n  if (P % _4n === _3n) return sqrt3mod4;\n  // P ≡ 5 (mod 8) => Atkin algorithm, page 10 of https://eprint.iacr.org/2012/685.pdf\n  if (P % _8n === _5n) return sqrt5mod8;\n  // P ≡ 9 (mod 16) => Kong algorithm, page 11 of https://eprint.iacr.org/2012/685.pdf (algorithm 4)\n  if (P % _16n === _9n) return sqrt9mod16(P);\n  // Tonelli-Shanks algorithm\n  return tonelliShanks(P);\n}\n\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num: bigint, modulo: bigint): boolean =>\n  (mod(num, modulo) & _1n) === _1n;\n\n/** Field is not always over prime: for example, Fp2 has ORDER(q)=p^m. */\nexport interface IField<T> {\n  ORDER: bigint;\n  isLE: boolean;\n  BYTES: number;\n  BITS: number;\n  MASK: bigint;\n  ZERO: T;\n  ONE: T;\n  // 1-arg\n  create: (num: T) => T;\n  isValid: (num: T) => boolean;\n  is0: (num: T) => boolean;\n  isValidNot0: (num: T) => boolean;\n  neg(num: T): T;\n  inv(num: T): T;\n  sqrt(num: T): T;\n  sqr(num: T): T;\n  // 2-args\n  eql(lhs: T, rhs: T): boolean;\n  add(lhs: T, rhs: T): T;\n  sub(lhs: T, rhs: T): T;\n  mul(lhs: T, rhs: T | bigint): T;\n  pow(lhs: T, power: bigint): T;\n  div(lhs: T, rhs: T | bigint): T;\n  // N for NonNormalized (for now)\n  addN(lhs: T, rhs: T): T;\n  subN(lhs: T, rhs: T): T;\n  mulN(lhs: T, rhs: T | bigint): T;\n  sqrN(num: T): T;\n\n  // Optional\n  // Should be same as sgn0 function in\n  // [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#section-4.1).\n  // NOTE: sgn0 is 'negative in LE', which is same as odd. And negative in LE is kinda strange definition anyway.\n  isOdd?(num: T): boolean; // Odd instead of even since we have it for Fp2\n  allowedLengths?: number[];\n  // legendre?(num: T): T;\n  invertBatch: (lst: T[]) => T[];\n  toBytes(num: T): Uint8Array;\n  fromBytes(bytes: Uint8Array, skipValidation?: boolean): T;\n  // If c is False, CMOV returns a, otherwise it returns b.\n  cmov(a: T, b: T, c: boolean): T;\n}\n// prettier-ignore\nconst FIELD_FIELDS = [\n  'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n  'eql', 'add', 'sub', 'mul', 'pow', 'div',\n  'addN', 'subN', 'mulN', 'sqrN'\n] as const;\nexport function validateField<T>(field: IField<T>): IField<T> {\n  const initial = {\n    ORDER: 'bigint',\n    MASK: 'bigint',\n    BYTES: 'number',\n    BITS: 'number',\n  } as Record<string, string>;\n  const opts = FIELD_FIELDS.reduce((map, val: string) => {\n    map[val] = 'function';\n    return map;\n  }, initial);\n  _validateObject(field, opts);\n  // const max = 16384;\n  // if (field.BYTES < 1 || field.BYTES > max) throw new Error('invalid field');\n  // if (field.BITS < 1 || field.BITS > 8 * max) throw new Error('invalid field');\n  return field;\n}\n\n// Generic field functions\n\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow<T>(Fp: IField<T>, num: T, power: bigint): T {\n  if (power < _0n) throw new Error('invalid exponent, negatives unsupported');\n  if (power === _0n) return Fp.ONE;\n  if (power === _1n) return num;\n  let p = Fp.ONE;\n  let d = num;\n  while (power > _0n) {\n    if (power & _1n) p = Fp.mul(p, d);\n    d = Fp.sqr(d);\n    power >>= _1n;\n  }\n  return p;\n}\n\n/**\n * Efficiently invert an array of Field elements.\n * Exception-free. Will return `undefined` for 0 elements.\n * @param passZero map 0 to 0 (instead of undefined)\n */\nexport function FpInvertBatch<T>(Fp: IField<T>, nums: T[], passZero = false): T[] {\n  const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : undefined);\n  // Walk from first to last, multiply them by each other MOD p\n  const multipliedAcc = nums.reduce((acc, num, i) => {\n    if (Fp.is0(num)) return acc;\n    inverted[i] = acc;\n    return Fp.mul(acc, num);\n  }, Fp.ONE);\n  // Invert last element\n  const invertedAcc = Fp.inv(multipliedAcc);\n  // Walk from last to first, multiply them by inverted each other MOD p\n  nums.reduceRight((acc, num, i) => {\n    if (Fp.is0(num)) return acc;\n    inverted[i] = Fp.mul(acc, inverted[i]);\n    return Fp.mul(acc, num);\n  }, invertedAcc);\n  return inverted;\n}\n\n// TODO: remove\nexport function FpDiv<T>(Fp: IField<T>, lhs: T, rhs: T | bigint): T {\n  return Fp.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));\n}\n\n/**\n * Legendre symbol.\n * Legendre constant is used to calculate Legendre symbol (a | p)\n * which denotes the value of a^((p-1)/2) (mod p).\n *\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre<T>(Fp: IField<T>, n: T): -1 | 0 | 1 {\n  // We can use 3rd argument as optional cache of this value\n  // but seems unneeded for now. The operation is very fast.\n  const p1mod2 = (Fp.ORDER - _1n) / _2n;\n  const powered = Fp.pow(n, p1mod2);\n  const yes = Fp.eql(powered, Fp.ONE);\n  const zero = Fp.eql(powered, Fp.ZERO);\n  const no = Fp.eql(powered, Fp.neg(Fp.ONE));\n  if (!yes && !zero && !no) throw new Error('invalid Legendre symbol result');\n  return yes ? 1 : zero ? 0 : -1;\n}\n\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare<T>(Fp: IField<T>, n: T): boolean {\n  const l = FpLegendre(Fp, n);\n  return l === 1;\n}\n\nexport type NLength = { nByteLength: number; nBitLength: number };\n// CURVE.n lengths\nexport function nLength(n: bigint, nBitLength?: number): NLength {\n  // Bit size, byte size of CURVE.n\n  if (nBitLength !== undefined) anumber(nBitLength);\n  const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n  const nByteLength = Math.ceil(_nBitLength / 8);\n  return { nBitLength: _nBitLength, nByteLength };\n}\n\ntype FpField = IField<bigint> & Required<Pick<IField<bigint>, 'isOdd'>>;\ntype SqrtFn = (n: bigint) => bigint;\ntype FieldOpts = Partial<{\n  sqrt: SqrtFn;\n  isLE: boolean;\n  BITS: number;\n  modFromBytes: boolean; // bls12-381 requires mod(n) instead of rejecting keys >= n\n  allowedLengths?: readonly number[]; // for P521 (adds padding for smaller sizes)\n}>;\n/**\n * Creates a finite field. Major performance optimizations:\n * * 1. Denormalized operations like mulN instead of mul.\n * * 2. Identical object shape: never add or remove keys.\n * * 3. `Object.freeze`.\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n *\n * Note about field properties:\n * * CHARACTERISTIC p = prime number, number of elements in main subgroup.\n * * ORDER q = similar to cofactor in curves, may be composite `q = p^m`.\n *\n * @param ORDER field order, probably prime, or could be composite\n * @param bitLen how many bits the field consumes\n * @param isLE (default: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(\n  ORDER: bigint,\n  bitLenOrOpts?: number | FieldOpts, // TODO: use opts only in v2?\n  isLE = false,\n  opts: { sqrt?: SqrtFn } = {}\n): Readonly<FpField> {\n  if (ORDER <= _0n) throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n  let _nbitLength: number | undefined = undefined;\n  let _sqrt: SqrtFn | undefined = undefined;\n  let modFromBytes: boolean = false;\n  let allowedLengths: undefined | readonly number[] = undefined;\n  if (typeof bitLenOrOpts === 'object' && bitLenOrOpts != null) {\n    if (opts.sqrt || isLE) throw new Error('cannot specify opts in two arguments');\n    const _opts = bitLenOrOpts;\n    if (_opts.BITS) _nbitLength = _opts.BITS;\n    if (_opts.sqrt) _sqrt = _opts.sqrt;\n    if (typeof _opts.isLE === 'boolean') isLE = _opts.isLE;\n    if (typeof _opts.modFromBytes === 'boolean') modFromBytes = _opts.modFromBytes;\n    allowedLengths = _opts.allowedLengths;\n  } else {\n    if (typeof bitLenOrOpts === 'number') _nbitLength = bitLenOrOpts;\n    if (opts.sqrt) _sqrt = opts.sqrt;\n  }\n  const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, _nbitLength);\n  if (BYTES > 2048) throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n  let sqrtP: ReturnType<typeof FpSqrt>; // cached sqrtP\n  const f: Readonly<FpField> = Object.freeze({\n    ORDER,\n    isLE,\n    BITS,\n    BYTES,\n    MASK: bitMask(BITS),\n    ZERO: _0n,\n    ONE: _1n,\n    allowedLengths: allowedLengths,\n    create: (num) => mod(num, ORDER),\n    isValid: (num) => {\n      if (typeof num !== 'bigint')\n        throw new Error('invalid field element: expected bigint, got ' + typeof num);\n      return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n    },\n    is0: (num) => num === _0n,\n    // is valid and invertible\n    isValidNot0: (num: bigint) => !f.is0(num) && f.isValid(num),\n    isOdd: (num) => (num & _1n) === _1n,\n    neg: (num) => mod(-num, ORDER),\n    eql: (lhs, rhs) => lhs === rhs,\n\n    sqr: (num) => mod(num * num, ORDER),\n    add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n    sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n    mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n    pow: (num, power) => FpPow(f, num, power),\n    div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n\n    // Same as above, but doesn't normalize\n    sqrN: (num) => num * num,\n    addN: (lhs, rhs) => lhs + rhs,\n    subN: (lhs, rhs) => lhs - rhs,\n    mulN: (lhs, rhs) => lhs * rhs,\n\n    inv: (num) => invert(num, ORDER),\n    sqrt:\n      _sqrt ||\n      ((n) => {\n        if (!sqrtP) sqrtP = FpSqrt(ORDER);\n        return sqrtP(f, n);\n      }),\n    toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n    fromBytes: (bytes, skipValidation = true) => {\n      if (allowedLengths) {\n        if (!allowedLengths.includes(bytes.length) || bytes.length > BYTES) {\n          throw new Error(\n            'Field.fromBytes: expected ' + allowedLengths + ' bytes, got ' + bytes.length\n          );\n        }\n        const padded = new Uint8Array(BYTES);\n        // isLE add 0 to right, !isLE to the left.\n        padded.set(bytes, isLE ? 0 : padded.length - bytes.length);\n        bytes = padded;\n      }\n      if (bytes.length !== BYTES)\n        throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n      let scalar = isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n      if (modFromBytes) scalar = mod(scalar, ORDER);\n      if (!skipValidation)\n        if (!f.isValid(scalar)) throw new Error('invalid field element: outside of range 0..ORDER');\n      // NOTE: we don't validate scalar here, please use isValid. This done such way because some\n      // protocol may allow non-reduced scalar that reduced later or changed some other way.\n      return scalar;\n    },\n    // TODO: we don't need it here, move out to separate fn\n    invertBatch: (lst) => FpInvertBatch(f, lst),\n    // We can't move this out because Fp6, Fp12 implement it\n    // and it's unclear what to return in there.\n    cmov: (a, b, c) => (c ? b : a),\n  } as FpField);\n  return Object.freeze(f);\n}\n\n// Generic random scalar, we can do same for other fields if via Fp2.mul(Fp2.ONE, Fp2.random)?\n// This allows unsafe methods like ignore bias or zero. These unsafe, but often used in different protocols (if deterministic RNG).\n// which mean we cannot force this via opts.\n// Not sure what to do with randomBytes, we can accept it inside opts if wanted.\n// Probably need to export getMinHashLength somewhere?\n// random(bytes?: Uint8Array, unsafeAllowZero = false, unsafeAllowBias = false) {\n//   const LEN = !unsafeAllowBias ? getMinHashLength(ORDER) : BYTES;\n//   if (bytes === undefined) bytes = randomBytes(LEN); // _opts.randomBytes?\n//   const num = isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n//   // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n//   const reduced = unsafeAllowZero ? mod(num, ORDER) : mod(num, ORDER - _1n) + _1n;\n//   return reduced;\n// },\n\nexport function FpSqrtOdd<T>(Fp: IField<T>, elm: T): T {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? root : Fp.neg(root);\n}\n\nexport function FpSqrtEven<T>(Fp: IField<T>, elm: T): T {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(\n  hash: string | Uint8Array,\n  groupOrder: bigint,\n  isLE = false\n): bigint {\n  hash = ensureBytes('privateHash', hash);\n  const hashLen = hash.length;\n  const minLen = nLength(groupOrder).nByteLength + 8;\n  if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n    throw new Error(\n      'hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen\n    );\n  const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n  return mod(num, groupOrder - _1n) + _1n;\n}\n\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder: bigint): number {\n  if (typeof fieldOrder !== 'bigint') throw new Error('field order must be bigint');\n  const bitLength = fieldOrder.toString(2).length;\n  return Math.ceil(bitLength / 8);\n}\n\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder: bigint): number {\n  const length = getFieldBytesLength(fieldOrder);\n  return length + Math.ceil(length / 2);\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key: Uint8Array, fieldOrder: bigint, isLE = false): Uint8Array {\n  const len = key.length;\n  const fieldLen = getFieldBytesLength(fieldOrder);\n  const minLen = getMinHashLength(fieldOrder);\n  // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n  if (len < 16 || len < minLen || len > 1024)\n    throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n  const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n  // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n  const reduced = mod(num, fieldOrder - _1n) + _1n;\n  return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n"], "mappings": "AAAA;;;;;;AAMA;AACA,SACEA,eAAe,EACfC,OAAO,EACPC,OAAO,EACPC,eAAe,EACfC,eAAe,EACfC,WAAW,EACXC,eAAe,EACfC,eAAe,QACV,aAAa;AAEpB;AACA,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;EAAEC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;EAAEE,GAAG,GAAG,eAAgBF,MAAM,CAAC,CAAC,CAAC;EAAEG,GAAG,GAAG,eAAgBH,MAAM,CAAC,CAAC,CAAC;AACxG;AACA,MAAMI,GAAG,GAAG,eAAgBJ,MAAM,CAAC,CAAC,CAAC;EAAEK,GAAG,GAAG,eAAgBL,MAAM,CAAC,CAAC,CAAC;EAAEM,GAAG,GAAG,eAAgBN,MAAM,CAAC,CAAC,CAAC;AACvG;AACA,MAAMO,GAAG,GAAG,eAAgBP,MAAM,CAAC,CAAC,CAAC;EAAEQ,GAAG,GAAG,eAAgBR,MAAM,CAAC,CAAC,CAAC;EAAES,IAAI,GAAG,eAAgBT,MAAM,CAAC,EAAE,CAAC;AAEzG;AACA,OAAM,SAAUU,GAAGA,CAACC,CAAS,EAAEC,CAAS;EACtC,MAAMC,MAAM,GAAGF,CAAC,GAAGC,CAAC;EACpB,OAAOC,MAAM,IAAId,GAAG,GAAGc,MAAM,GAAGD,CAAC,GAAGC,MAAM;AAC5C;AACA;;;;;;AAMA,OAAM,SAAUC,GAAGA,CAACC,GAAW,EAAEC,KAAa,EAAEC,MAAc;EAC5D,OAAOC,KAAK,CAACC,KAAK,CAACF,MAAM,CAAC,EAAEF,GAAG,EAAEC,KAAK,CAAC;AACzC;AAEA;AACA,OAAM,SAAUI,IAAIA,CAACC,CAAS,EAAEL,KAAa,EAAEC,MAAc;EAC3D,IAAIK,GAAG,GAAGD,CAAC;EACX,OAAOL,KAAK,EAAE,GAAGjB,GAAG,EAAE;IACpBuB,GAAG,IAAIA,GAAG;IACVA,GAAG,IAAIL,MAAM;EACf;EACA,OAAOK,GAAG;AACZ;AAEA;;;;AAIA,OAAM,SAAUC,MAAMA,CAACC,MAAc,EAAEP,MAAc;EACnD,IAAIO,MAAM,KAAKzB,GAAG,EAAE,MAAM,IAAI0B,KAAK,CAAC,kCAAkC,CAAC;EACvE,IAAIR,MAAM,IAAIlB,GAAG,EAAE,MAAM,IAAI0B,KAAK,CAAC,yCAAyC,GAAGR,MAAM,CAAC;EACtF;EACA,IAAIN,CAAC,GAAGD,GAAG,CAACc,MAAM,EAAEP,MAAM,CAAC;EAC3B,IAAIL,CAAC,GAAGK,MAAM;EACd;EACA,IAAII,CAAC,GAAGtB,GAAG;IAAE2B,CAAC,GAAGzB,GAAG;IAAE0B,CAAC,GAAG1B,GAAG;IAAE2B,CAAC,GAAG7B,GAAG;EACtC,OAAOY,CAAC,KAAKZ,GAAG,EAAE;IAChB;IACA,MAAM8B,CAAC,GAAGjB,CAAC,GAAGD,CAAC;IACf,MAAMmB,CAAC,GAAGlB,CAAC,GAAGD,CAAC;IACf,MAAMoB,CAAC,GAAGV,CAAC,GAAGM,CAAC,GAAGE,CAAC;IACnB,MAAMG,CAAC,GAAGN,CAAC,GAAGE,CAAC,GAAGC,CAAC;IACnB;IACAjB,CAAC,GAAGD,CAAC,EAAEA,CAAC,GAAGmB,CAAC,EAAET,CAAC,GAAGM,CAAC,EAAED,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGI,CAAC,EAAEH,CAAC,GAAGI,CAAC;EAC1C;EACA,MAAMC,GAAG,GAAGrB,CAAC;EACb,IAAIqB,GAAG,KAAKhC,GAAG,EAAE,MAAM,IAAIwB,KAAK,CAAC,wBAAwB,CAAC;EAC1D,OAAOf,GAAG,CAACW,CAAC,EAAEJ,MAAM,CAAC;AACvB;AAEA,SAASiB,cAAcA,CAAIC,EAAa,EAAEC,IAAO,EAAEJ,CAAI;EACrD,IAAI,CAACG,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACF,IAAI,CAAC,EAAEJ,CAAC,CAAC,EAAE,MAAM,IAAIP,KAAK,CAAC,yBAAyB,CAAC;AAC1E;AAEA;AACA;AACA;AACA;AACA,SAASc,SAASA,CAAIJ,EAAa,EAAEH,CAAI;EACvC,MAAMQ,MAAM,GAAG,CAACL,EAAE,CAACM,KAAK,GAAGxC,GAAG,IAAIG,GAAG;EACrC,MAAMgC,IAAI,GAAGD,EAAE,CAACrB,GAAG,CAACkB,CAAC,EAAEQ,MAAM,CAAC;EAC9BN,cAAc,CAACC,EAAE,EAAEC,IAAI,EAAEJ,CAAC,CAAC;EAC3B,OAAOI,IAAI;AACb;AAEA,SAASM,SAASA,CAAIP,EAAa,EAAEH,CAAI;EACvC,MAAMW,MAAM,GAAG,CAACR,EAAE,CAACM,KAAK,GAAGpC,GAAG,IAAIE,GAAG;EACrC,MAAMqC,EAAE,GAAGT,EAAE,CAACU,GAAG,CAACb,CAAC,EAAE9B,GAAG,CAAC;EACzB,MAAM0B,CAAC,GAAGO,EAAE,CAACrB,GAAG,CAAC8B,EAAE,EAAED,MAAM,CAAC;EAC5B,MAAMG,EAAE,GAAGX,EAAE,CAACU,GAAG,CAACb,CAAC,EAAEJ,CAAC,CAAC;EACvB,MAAMmB,CAAC,GAAGZ,EAAE,CAACU,GAAG,CAACV,EAAE,CAACU,GAAG,CAACC,EAAE,EAAE5C,GAAG,CAAC,EAAE0B,CAAC,CAAC;EACpC,MAAMQ,IAAI,GAAGD,EAAE,CAACU,GAAG,CAACC,EAAE,EAAEX,EAAE,CAACa,GAAG,CAACD,CAAC,EAAEZ,EAAE,CAACc,GAAG,CAAC,CAAC;EAC1Cf,cAAc,CAACC,EAAE,EAAEC,IAAI,EAAEJ,CAAC,CAAC;EAC3B,OAAOI,IAAI;AACb;AAEA;AACA;AACA,SAASc,UAAUA,CAACC,CAAS;EAC3B,MAAMC,GAAG,GAAGjC,KAAK,CAACgC,CAAC,CAAC;EACpB,MAAME,EAAE,GAAGC,aAAa,CAACH,CAAC,CAAC;EAC3B,MAAMI,EAAE,GAAGF,EAAE,CAACD,GAAG,EAAEA,GAAG,CAACI,GAAG,CAACJ,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;EACrC,MAAMQ,EAAE,GAAGJ,EAAE,CAACD,GAAG,EAAEG,EAAE,CAAC,CAAC,CAAc;EACrC,MAAMG,EAAE,GAAGL,EAAE,CAACD,GAAG,EAAEA,GAAG,CAACI,GAAG,CAACD,EAAE,CAAC,CAAC,CAAC,CAAK;EACrC,MAAMI,EAAE,GAAG,CAACR,CAAC,GAAG7C,GAAG,IAAIG,IAAI,CAAC,CAAS;EACrC,OAAO,CAAI0B,EAAa,EAAEH,CAAI,KAAI;IAChC,IAAI4B,GAAG,GAAGzB,EAAE,CAACrB,GAAG,CAACkB,CAAC,EAAE2B,EAAE,CAAC,CAAC,CAAW;IACnC,IAAIE,GAAG,GAAG1B,EAAE,CAACU,GAAG,CAACe,GAAG,EAAEL,EAAE,CAAC,CAAC,CAAS;IACnC,MAAMO,GAAG,GAAG3B,EAAE,CAACU,GAAG,CAACe,GAAG,EAAEH,EAAE,CAAC,CAAC,CAAO;IACnC,MAAMM,GAAG,GAAG5B,EAAE,CAACU,GAAG,CAACe,GAAG,EAAEF,EAAE,CAAC,CAAC,CAAO;IACnC,MAAMM,EAAE,GAAG7B,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACuB,GAAG,CAAC,EAAE7B,CAAC,CAAC,CAAC,CAAC;IACnC,MAAMiC,EAAE,GAAG9B,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACwB,GAAG,CAAC,EAAE9B,CAAC,CAAC,CAAC,CAAC;IACnC4B,GAAG,GAAGzB,EAAE,CAAC+B,IAAI,CAACN,GAAG,EAAEC,GAAG,EAAEG,EAAE,CAAC,CAAC,CAAO;IACnCH,GAAG,GAAG1B,EAAE,CAAC+B,IAAI,CAACH,GAAG,EAAED,GAAG,EAAEG,EAAE,CAAC,CAAC,CAAO;IACnC,MAAME,EAAE,GAAGhC,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACuB,GAAG,CAAC,EAAE7B,CAAC,CAAC,CAAC,CAAC;IACnC,MAAMI,IAAI,GAAGD,EAAE,CAAC+B,IAAI,CAACN,GAAG,EAAEC,GAAG,EAAEM,EAAE,CAAC,CAAC;IACnCjC,cAAc,CAACC,EAAE,EAAEC,IAAI,EAAEJ,CAAC,CAAC;IAC3B,OAAOI,IAAI;EACb,CAAC;AACH;AAEA;;;;;;;AAOA,OAAM,SAAUkB,aAAaA,CAACH,CAAS;EACrC;EACA;EACA,IAAIA,CAAC,GAAGhD,GAAG,EAAE,MAAM,IAAIsB,KAAK,CAAC,qCAAqC,CAAC;EACnE;EACA,IAAI2C,CAAC,GAAGjB,CAAC,GAAGlD,GAAG;EACf,IAAIoE,CAAC,GAAG,CAAC;EACT,OAAOD,CAAC,GAAGlE,GAAG,KAAKH,GAAG,EAAE;IACtBqE,CAAC,IAAIlE,GAAG;IACRmE,CAAC,EAAE;EACL;EAEA;EACA,IAAIC,CAAC,GAAGpE,GAAG;EACX,MAAMqE,GAAG,GAAGpD,KAAK,CAACgC,CAAC,CAAC;EACpB,OAAOqB,UAAU,CAACD,GAAG,EAAED,CAAC,CAAC,KAAK,CAAC,EAAE;IAC/B;IACA;IACA,IAAIA,CAAC,EAAE,GAAG,IAAI,EAAE,MAAM,IAAI7C,KAAK,CAAC,+CAA+C,CAAC;EAClF;EACA;EACA,IAAI4C,CAAC,KAAK,CAAC,EAAE,OAAO9B,SAAS;EAE7B;EACA;EACA,IAAIkC,EAAE,GAAGF,GAAG,CAACzD,GAAG,CAACwD,CAAC,EAAEF,CAAC,CAAC,CAAC,CAAC;EACxB,MAAMM,MAAM,GAAG,CAACN,CAAC,GAAGnE,GAAG,IAAIC,GAAG;EAC9B,OAAO,SAASyE,WAAWA,CAAIxC,EAAa,EAAEH,CAAI;IAChD,IAAIG,EAAE,CAACyC,GAAG,CAAC5C,CAAC,CAAC,EAAE,OAAOA,CAAC;IACvB;IACA,IAAIwC,UAAU,CAACrC,EAAE,EAAEH,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM,IAAIP,KAAK,CAAC,yBAAyB,CAAC;IAEvE;IACA,IAAIoD,CAAC,GAAGR,CAAC;IACT,IAAIS,CAAC,GAAG3C,EAAE,CAACU,GAAG,CAACV,EAAE,CAACc,GAAG,EAAEwB,EAAE,CAAC,CAAC,CAAC;IAC5B,IAAIM,CAAC,GAAG5C,EAAE,CAACrB,GAAG,CAACkB,CAAC,EAAEoC,CAAC,CAAC,CAAC,CAAC;IACtB,IAAIY,CAAC,GAAG7C,EAAE,CAACrB,GAAG,CAACkB,CAAC,EAAE0C,MAAM,CAAC,CAAC,CAAC;IAE3B;IACA;IACA,OAAO,CAACvC,EAAE,CAACE,GAAG,CAAC0C,CAAC,EAAE5C,EAAE,CAACc,GAAG,CAAC,EAAE;MACzB,IAAId,EAAE,CAACyC,GAAG,CAACG,CAAC,CAAC,EAAE,OAAO5C,EAAE,CAAC8C,IAAI,CAAC,CAAC;MAC/B,IAAIlC,CAAC,GAAG,CAAC;MAET;MACA,IAAImC,KAAK,GAAG/C,EAAE,CAACG,GAAG,CAACyC,CAAC,CAAC,CAAC,CAAC;MACvB,OAAO,CAAC5C,EAAE,CAACE,GAAG,CAAC6C,KAAK,EAAE/C,EAAE,CAACc,GAAG,CAAC,EAAE;QAC7BF,CAAC,EAAE;QACHmC,KAAK,GAAG/C,EAAE,CAACG,GAAG,CAAC4C,KAAK,CAAC,CAAC,CAAC;QACvB,IAAInC,CAAC,KAAK8B,CAAC,EAAE,MAAM,IAAIpD,KAAK,CAAC,yBAAyB,CAAC;MACzD;MAEA;MACA,MAAM0D,QAAQ,GAAGlF,GAAG,IAAID,MAAM,CAAC6E,CAAC,GAAG9B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3C,MAAMnC,CAAC,GAAGuB,EAAE,CAACrB,GAAG,CAACgE,CAAC,EAAEK,QAAQ,CAAC,CAAC,CAAC;MAE/B;MACAN,CAAC,GAAG9B,CAAC;MACL+B,CAAC,GAAG3C,EAAE,CAACG,GAAG,CAAC1B,CAAC,CAAC,CAAC,CAAC;MACfmE,CAAC,GAAG5C,EAAE,CAACU,GAAG,CAACkC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;MAClBE,CAAC,GAAG7C,EAAE,CAACU,GAAG,CAACmC,CAAC,EAAEpE,CAAC,CAAC,CAAC,CAAC;IACpB;IACA,OAAOoE,CAAC;EACV,CAAC;AACH;AAEA;;;;;;;;;;;AAWA,OAAM,SAAUI,MAAMA,CAACjC,CAAS;EAC9B;EACA,IAAIA,CAAC,GAAG/C,GAAG,KAAKD,GAAG,EAAE,OAAOoC,SAAS;EACrC;EACA,IAAIY,CAAC,GAAG5C,GAAG,KAAKF,GAAG,EAAE,OAAOqC,SAAS;EACrC;EACA,IAAIS,CAAC,GAAG1C,IAAI,KAAKD,GAAG,EAAE,OAAO0C,UAAU,CAACC,CAAC,CAAC;EAC1C;EACA,OAAOG,aAAa,CAACH,CAAC,CAAC;AACzB;AAEA;AACA,OAAO,MAAMkC,YAAY,GAAGA,CAACtE,GAAW,EAAEE,MAAc,KACtD,CAACP,GAAG,CAACK,GAAG,EAAEE,MAAM,CAAC,GAAGhB,GAAG,MAAMA,GAAG;AA8ClC;AACA,MAAMqF,YAAY,GAAG,CACnB,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EACvD,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CACtB;AACV,OAAM,SAAUC,aAAaA,CAAIC,KAAgB;EAC/C,MAAMC,OAAO,GAAG;IACdhD,KAAK,EAAE,QAAQ;IACfiD,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;GACmB;EAC3B,MAAMC,IAAI,GAAGP,YAAY,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAW,KAAI;IACpDD,GAAG,CAACC,GAAG,CAAC,GAAG,UAAU;IACrB,OAAOD,GAAG;EACZ,CAAC,EAAEN,OAAO,CAAC;EACXlG,eAAe,CAACiG,KAAK,EAAEK,IAAI,CAAC;EAC5B;EACA;EACA;EACA,OAAOL,KAAK;AACd;AAEA;AAEA;;;;AAIA,OAAM,SAAUtE,KAAKA,CAAIiB,EAAa,EAAEpB,GAAM,EAAEC,KAAa;EAC3D,IAAIA,KAAK,GAAGjB,GAAG,EAAE,MAAM,IAAI0B,KAAK,CAAC,yCAAyC,CAAC;EAC3E,IAAIT,KAAK,KAAKjB,GAAG,EAAE,OAAOoC,EAAE,CAACc,GAAG;EAChC,IAAIjC,KAAK,KAAKf,GAAG,EAAE,OAAOc,GAAG;EAC7B,IAAIkF,CAAC,GAAG9D,EAAE,CAACc,GAAG;EACd,IAAIiD,CAAC,GAAGnF,GAAG;EACX,OAAOC,KAAK,GAAGjB,GAAG,EAAE;IAClB,IAAIiB,KAAK,GAAGf,GAAG,EAAEgG,CAAC,GAAG9D,EAAE,CAACU,GAAG,CAACoD,CAAC,EAAEC,CAAC,CAAC;IACjCA,CAAC,GAAG/D,EAAE,CAACG,GAAG,CAAC4D,CAAC,CAAC;IACblF,KAAK,KAAKf,GAAG;EACf;EACA,OAAOgG,CAAC;AACV;AAEA;;;;;AAKA,OAAM,SAAUE,aAAaA,CAAIhE,EAAa,EAAEiE,IAAS,EAAEC,QAAQ,GAAG,KAAK;EACzE,MAAMC,QAAQ,GAAG,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,CAAC,CAACC,IAAI,CAACJ,QAAQ,GAAGlE,EAAE,CAAC8C,IAAI,GAAGyB,SAAS,CAAC;EAC5E;EACA,MAAMC,aAAa,GAAGP,IAAI,CAACN,MAAM,CAAC,CAACc,GAAG,EAAE7F,GAAG,EAAEgC,CAAC,KAAI;IAChD,IAAIZ,EAAE,CAACyC,GAAG,CAAC7D,GAAG,CAAC,EAAE,OAAO6F,GAAG;IAC3BN,QAAQ,CAACvD,CAAC,CAAC,GAAG6D,GAAG;IACjB,OAAOzE,EAAE,CAACU,GAAG,CAAC+D,GAAG,EAAE7F,GAAG,CAAC;EACzB,CAAC,EAAEoB,EAAE,CAACc,GAAG,CAAC;EACV;EACA,MAAM4D,WAAW,GAAG1E,EAAE,CAAC2E,GAAG,CAACH,aAAa,CAAC;EACzC;EACAP,IAAI,CAACW,WAAW,CAAC,CAACH,GAAG,EAAE7F,GAAG,EAAEgC,CAAC,KAAI;IAC/B,IAAIZ,EAAE,CAACyC,GAAG,CAAC7D,GAAG,CAAC,EAAE,OAAO6F,GAAG;IAC3BN,QAAQ,CAACvD,CAAC,CAAC,GAAGZ,EAAE,CAACU,GAAG,CAAC+D,GAAG,EAAEN,QAAQ,CAACvD,CAAC,CAAC,CAAC;IACtC,OAAOZ,EAAE,CAACU,GAAG,CAAC+D,GAAG,EAAE7F,GAAG,CAAC;EACzB,CAAC,EAAE8F,WAAW,CAAC;EACf,OAAOP,QAAQ;AACjB;AAEA;AACA,OAAM,SAAUU,KAAKA,CAAI7E,EAAa,EAAE8E,GAAM,EAAEC,GAAe;EAC7D,OAAO/E,EAAE,CAACU,GAAG,CAACoE,GAAG,EAAE,OAAOC,GAAG,KAAK,QAAQ,GAAG3F,MAAM,CAAC2F,GAAG,EAAE/E,EAAE,CAACM,KAAK,CAAC,GAAGN,EAAE,CAAC2E,GAAG,CAACI,GAAG,CAAC,CAAC;AACnF;AAEA;;;;;;;;;AASA,OAAM,SAAU1C,UAAUA,CAAIrC,EAAa,EAAEH,CAAI;EAC/C;EACA;EACA,MAAMmF,MAAM,GAAG,CAAChF,EAAE,CAACM,KAAK,GAAGxC,GAAG,IAAIC,GAAG;EACrC,MAAMkH,OAAO,GAAGjF,EAAE,CAACrB,GAAG,CAACkB,CAAC,EAAEmF,MAAM,CAAC;EACjC,MAAME,GAAG,GAAGlF,EAAE,CAACE,GAAG,CAAC+E,OAAO,EAAEjF,EAAE,CAACc,GAAG,CAAC;EACnC,MAAMqE,IAAI,GAAGnF,EAAE,CAACE,GAAG,CAAC+E,OAAO,EAAEjF,EAAE,CAAC8C,IAAI,CAAC;EACrC,MAAMsC,EAAE,GAAGpF,EAAE,CAACE,GAAG,CAAC+E,OAAO,EAAEjF,EAAE,CAACqB,GAAG,CAACrB,EAAE,CAACc,GAAG,CAAC,CAAC;EAC1C,IAAI,CAACoE,GAAG,IAAI,CAACC,IAAI,IAAI,CAACC,EAAE,EAAE,MAAM,IAAI9F,KAAK,CAAC,gCAAgC,CAAC;EAC3E,OAAO4F,GAAG,GAAG,CAAC,GAAGC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC;AAEA;AACA,OAAM,SAAUE,UAAUA,CAAIrF,EAAa,EAAEH,CAAI;EAC/C,MAAMyF,CAAC,GAAGjD,UAAU,CAACrC,EAAE,EAAEH,CAAC,CAAC;EAC3B,OAAOyF,CAAC,KAAK,CAAC;AAChB;AAGA;AACA,OAAM,SAAUC,OAAOA,CAAC1F,CAAS,EAAE2F,UAAmB;EACpD;EACA,IAAIA,UAAU,KAAKjB,SAAS,EAAElH,OAAO,CAACmI,UAAU,CAAC;EACjD,MAAMC,WAAW,GAAGD,UAAU,KAAKjB,SAAS,GAAGiB,UAAU,GAAG3F,CAAC,CAAC6F,QAAQ,CAAC,CAAC,CAAC,CAACrB,MAAM;EAChF,MAAMsB,WAAW,GAAGC,IAAI,CAACC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;EAC9C,OAAO;IAAED,UAAU,EAAEC,WAAW;IAAEE;EAAW,CAAE;AACjD;AAWA;;;;;;;;;;;;;;;;;;;AAmBA,OAAM,SAAU3G,KAAKA,CACnBsB,KAAa,EACbwF,YAAiC;AAAE;AACnCC,IAAI,GAAG,KAAK,EACZrC,IAAA,GAA0B,EAAE;EAE5B,IAAIpD,KAAK,IAAI1C,GAAG,EAAE,MAAM,IAAI0B,KAAK,CAAC,yCAAyC,GAAGgB,KAAK,CAAC;EACpF,IAAI0F,WAAW,GAAuBzB,SAAS;EAC/C,IAAI0B,KAAK,GAAuB1B,SAAS;EACzC,IAAI2B,YAAY,GAAY,KAAK;EACjC,IAAIC,cAAc,GAAkC5B,SAAS;EAC7D,IAAI,OAAOuB,YAAY,KAAK,QAAQ,IAAIA,YAAY,IAAI,IAAI,EAAE;IAC5D,IAAIpC,IAAI,CAAC0C,IAAI,IAAIL,IAAI,EAAE,MAAM,IAAIzG,KAAK,CAAC,sCAAsC,CAAC;IAC9E,MAAM+G,KAAK,GAAGP,YAAY;IAC1B,IAAIO,KAAK,CAAC5C,IAAI,EAAEuC,WAAW,GAAGK,KAAK,CAAC5C,IAAI;IACxC,IAAI4C,KAAK,CAACD,IAAI,EAAEH,KAAK,GAAGI,KAAK,CAACD,IAAI;IAClC,IAAI,OAAOC,KAAK,CAACN,IAAI,KAAK,SAAS,EAAEA,IAAI,GAAGM,KAAK,CAACN,IAAI;IACtD,IAAI,OAAOM,KAAK,CAACH,YAAY,KAAK,SAAS,EAAEA,YAAY,GAAGG,KAAK,CAACH,YAAY;IAC9EC,cAAc,GAAGE,KAAK,CAACF,cAAc;EACvC,CAAC,MAAM;IACL,IAAI,OAAOL,YAAY,KAAK,QAAQ,EAAEE,WAAW,GAAGF,YAAY;IAChE,IAAIpC,IAAI,CAAC0C,IAAI,EAAEH,KAAK,GAAGvC,IAAI,CAAC0C,IAAI;EAClC;EACA,MAAM;IAAEZ,UAAU,EAAE/B,IAAI;IAAEkC,WAAW,EAAEnC;EAAK,CAAE,GAAG+B,OAAO,CAACjF,KAAK,EAAE0F,WAAW,CAAC;EAC5E,IAAIxC,KAAK,GAAG,IAAI,EAAE,MAAM,IAAIlE,KAAK,CAAC,gDAAgD,CAAC;EACnF,IAAIgH,KAAgC,CAAC,CAAC;EACtC,MAAMC,CAAC,GAAsBC,MAAM,CAACC,MAAM,CAAC;IACzCnG,KAAK;IACLyF,IAAI;IACJtC,IAAI;IACJD,KAAK;IACLD,IAAI,EAAEjG,OAAO,CAACmG,IAAI,CAAC;IACnBX,IAAI,EAAElF,GAAG;IACTkD,GAAG,EAAEhD,GAAG;IACRqI,cAAc,EAAEA,cAAc;IAC9BO,MAAM,EAAG9H,GAAG,IAAKL,GAAG,CAACK,GAAG,EAAE0B,KAAK,CAAC;IAChCqG,OAAO,EAAG/H,GAAG,IAAI;MACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,EACzB,MAAM,IAAIU,KAAK,CAAC,8CAA8C,GAAG,OAAOV,GAAG,CAAC;MAC9E,OAAOhB,GAAG,IAAIgB,GAAG,IAAIA,GAAG,GAAG0B,KAAK,CAAC,CAAC;IACpC,CAAC;IACDmC,GAAG,EAAG7D,GAAG,IAAKA,GAAG,KAAKhB,GAAG;IACzB;IACAgJ,WAAW,EAAGhI,GAAW,IAAK,CAAC2H,CAAC,CAAC9D,GAAG,CAAC7D,GAAG,CAAC,IAAI2H,CAAC,CAACI,OAAO,CAAC/H,GAAG,CAAC;IAC3DiI,KAAK,EAAGjI,GAAG,IAAK,CAACA,GAAG,GAAGd,GAAG,MAAMA,GAAG;IACnCuD,GAAG,EAAGzC,GAAG,IAAKL,GAAG,CAAC,CAACK,GAAG,EAAE0B,KAAK,CAAC;IAC9BJ,GAAG,EAAEA,CAAC4E,GAAG,EAAEC,GAAG,KAAKD,GAAG,KAAKC,GAAG;IAE9B5E,GAAG,EAAGvB,GAAG,IAAKL,GAAG,CAACK,GAAG,GAAGA,GAAG,EAAE0B,KAAK,CAAC;IACnCwG,GAAG,EAAEA,CAAChC,GAAG,EAAEC,GAAG,KAAKxG,GAAG,CAACuG,GAAG,GAAGC,GAAG,EAAEzE,KAAK,CAAC;IACxCO,GAAG,EAAEA,CAACiE,GAAG,EAAEC,GAAG,KAAKxG,GAAG,CAACuG,GAAG,GAAGC,GAAG,EAAEzE,KAAK,CAAC;IACxCI,GAAG,EAAEA,CAACoE,GAAG,EAAEC,GAAG,KAAKxG,GAAG,CAACuG,GAAG,GAAGC,GAAG,EAAEzE,KAAK,CAAC;IACxC3B,GAAG,EAAEA,CAACC,GAAG,EAAEC,KAAK,KAAKE,KAAK,CAACwH,CAAC,EAAE3H,GAAG,EAAEC,KAAK,CAAC;IACzCkI,GAAG,EAAEA,CAACjC,GAAG,EAAEC,GAAG,KAAKxG,GAAG,CAACuG,GAAG,GAAG1F,MAAM,CAAC2F,GAAG,EAAEzE,KAAK,CAAC,EAAEA,KAAK,CAAC;IAEvD;IACA0G,IAAI,EAAGpI,GAAG,IAAKA,GAAG,GAAGA,GAAG;IACxBqI,IAAI,EAAEA,CAACnC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG;IAC7BmC,IAAI,EAAEA,CAACpC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG;IAC7BoC,IAAI,EAAEA,CAACrC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG;IAE7BJ,GAAG,EAAG/F,GAAG,IAAKQ,MAAM,CAACR,GAAG,EAAE0B,KAAK,CAAC;IAChC8F,IAAI,EACFH,KAAK,KACHpG,CAAC,IAAI;MACL,IAAI,CAACyG,KAAK,EAAEA,KAAK,GAAGrD,MAAM,CAAC3C,KAAK,CAAC;MACjC,OAAOgG,KAAK,CAACC,CAAC,EAAE1G,CAAC,CAAC;IACpB,CAAC,CAAC;IACJuH,OAAO,EAAGxI,GAAG,IAAMmH,IAAI,GAAGpI,eAAe,CAACiB,GAAG,EAAE4E,KAAK,CAAC,GAAG9F,eAAe,CAACkB,GAAG,EAAE4E,KAAK,CAAE;IACpF6D,SAAS,EAAEA,CAACC,KAAK,EAAEC,cAAc,GAAG,IAAI,KAAI;MAC1C,IAAIpB,cAAc,EAAE;QAClB,IAAI,CAACA,cAAc,CAACqB,QAAQ,CAACF,KAAK,CAACjD,MAAM,CAAC,IAAIiD,KAAK,CAACjD,MAAM,GAAGb,KAAK,EAAE;UAClE,MAAM,IAAIlE,KAAK,CACb,4BAA4B,GAAG6G,cAAc,GAAG,cAAc,GAAGmB,KAAK,CAACjD,MAAM,CAC9E;QACH;QACA,MAAMoD,MAAM,GAAG,IAAIC,UAAU,CAAClE,KAAK,CAAC;QACpC;QACAiE,MAAM,CAACE,GAAG,CAACL,KAAK,EAAEvB,IAAI,GAAG,CAAC,GAAG0B,MAAM,CAACpD,MAAM,GAAGiD,KAAK,CAACjD,MAAM,CAAC;QAC1DiD,KAAK,GAAGG,MAAM;MAChB;MACA,IAAIH,KAAK,CAACjD,MAAM,KAAKb,KAAK,EACxB,MAAM,IAAIlE,KAAK,CAAC,4BAA4B,GAAGkE,KAAK,GAAG,cAAc,GAAG8D,KAAK,CAACjD,MAAM,CAAC;MACvF,IAAIuD,MAAM,GAAG7B,IAAI,GAAGvI,eAAe,CAAC8J,KAAK,CAAC,GAAG/J,eAAe,CAAC+J,KAAK,CAAC;MACnE,IAAIpB,YAAY,EAAE0B,MAAM,GAAGrJ,GAAG,CAACqJ,MAAM,EAAEtH,KAAK,CAAC;MAC7C,IAAI,CAACiH,cAAc,EACjB,IAAI,CAAChB,CAAC,CAACI,OAAO,CAACiB,MAAM,CAAC,EAAE,MAAM,IAAItI,KAAK,CAAC,kDAAkD,CAAC;MAC7F;MACA;MACA,OAAOsI,MAAM;IACf,CAAC;IACD;IACAC,WAAW,EAAGC,GAAG,IAAK9D,aAAa,CAACuC,CAAC,EAAEuB,GAAG,CAAC;IAC3C;IACA;IACA/F,IAAI,EAAEA,CAACvD,CAAC,EAAEC,CAAC,EAAEkE,CAAC,KAAMA,CAAC,GAAGlE,CAAC,GAAGD;GAClB,CAAC;EACb,OAAOgI,MAAM,CAACC,MAAM,CAACF,CAAC,CAAC;AACzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,OAAM,SAAUwB,SAASA,CAAI/H,EAAa,EAAEgI,GAAM;EAChD,IAAI,CAAChI,EAAE,CAAC6G,KAAK,EAAE,MAAM,IAAIvH,KAAK,CAAC,0BAA0B,CAAC;EAC1D,MAAMW,IAAI,GAAGD,EAAE,CAACoG,IAAI,CAAC4B,GAAG,CAAC;EACzB,OAAOhI,EAAE,CAAC6G,KAAK,CAAC5G,IAAI,CAAC,GAAGA,IAAI,GAAGD,EAAE,CAACqB,GAAG,CAACpB,IAAI,CAAC;AAC7C;AAEA,OAAM,SAAUgI,UAAUA,CAAIjI,EAAa,EAAEgI,GAAM;EACjD,IAAI,CAAChI,EAAE,CAAC6G,KAAK,EAAE,MAAM,IAAIvH,KAAK,CAAC,0BAA0B,CAAC;EAC1D,MAAMW,IAAI,GAAGD,EAAE,CAACoG,IAAI,CAAC4B,GAAG,CAAC;EACzB,OAAOhI,EAAE,CAAC6G,KAAK,CAAC5G,IAAI,CAAC,GAAGD,EAAE,CAACqB,GAAG,CAACpB,IAAI,CAAC,GAAGA,IAAI;AAC7C;AAEA;;;;;;AAMA,OAAM,SAAUiI,mBAAmBA,CACjCC,IAAyB,EACzBC,UAAkB,EAClBrC,IAAI,GAAG,KAAK;EAEZoC,IAAI,GAAG1K,WAAW,CAAC,aAAa,EAAE0K,IAAI,CAAC;EACvC,MAAME,OAAO,GAAGF,IAAI,CAAC9D,MAAM;EAC3B,MAAMiE,MAAM,GAAG/C,OAAO,CAAC6C,UAAU,CAAC,CAACzC,WAAW,GAAG,CAAC;EAClD,IAAI2C,MAAM,GAAG,EAAE,IAAID,OAAO,GAAGC,MAAM,IAAID,OAAO,GAAG,IAAI,EACnD,MAAM,IAAI/I,KAAK,CACb,gCAAgC,GAAGgJ,MAAM,GAAG,4BAA4B,GAAGD,OAAO,CACnF;EACH,MAAMzJ,GAAG,GAAGmH,IAAI,GAAGvI,eAAe,CAAC2K,IAAI,CAAC,GAAG5K,eAAe,CAAC4K,IAAI,CAAC;EAChE,OAAO5J,GAAG,CAACK,GAAG,EAAEwJ,UAAU,GAAGtK,GAAG,CAAC,GAAGA,GAAG;AACzC;AAEA;;;;;;AAMA,OAAM,SAAUyK,mBAAmBA,CAACC,UAAkB;EACpD,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE,MAAM,IAAIlJ,KAAK,CAAC,4BAA4B,CAAC;EACjF,MAAMmJ,SAAS,GAAGD,UAAU,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAACrB,MAAM;EAC/C,OAAOuB,IAAI,CAACC,IAAI,CAAC4C,SAAS,GAAG,CAAC,CAAC;AACjC;AAEA;;;;;;;AAOA,OAAM,SAAUC,gBAAgBA,CAACF,UAAkB;EACjD,MAAMnE,MAAM,GAAGkE,mBAAmB,CAACC,UAAU,CAAC;EAC9C,OAAOnE,MAAM,GAAGuB,IAAI,CAACC,IAAI,CAACxB,MAAM,GAAG,CAAC,CAAC;AACvC;AAEA;;;;;;;;;;;;;AAaA,OAAM,SAAUsE,cAAcA,CAACC,GAAe,EAAEJ,UAAkB,EAAEzC,IAAI,GAAG,KAAK;EAC9E,MAAM8C,GAAG,GAAGD,GAAG,CAACvE,MAAM;EACtB,MAAMyE,QAAQ,GAAGP,mBAAmB,CAACC,UAAU,CAAC;EAChD,MAAMF,MAAM,GAAGI,gBAAgB,CAACF,UAAU,CAAC;EAC3C;EACA,IAAIK,GAAG,GAAG,EAAE,IAAIA,GAAG,GAAGP,MAAM,IAAIO,GAAG,GAAG,IAAI,EACxC,MAAM,IAAIvJ,KAAK,CAAC,WAAW,GAAGgJ,MAAM,GAAG,4BAA4B,GAAGO,GAAG,CAAC;EAC5E,MAAMjK,GAAG,GAAGmH,IAAI,GAAGvI,eAAe,CAACoL,GAAG,CAAC,GAAGrL,eAAe,CAACqL,GAAG,CAAC;EAC9D;EACA,MAAMG,OAAO,GAAGxK,GAAG,CAACK,GAAG,EAAE4J,UAAU,GAAG1K,GAAG,CAAC,GAAGA,GAAG;EAChD,OAAOiI,IAAI,GAAGpI,eAAe,CAACoL,OAAO,EAAED,QAAQ,CAAC,GAAGpL,eAAe,CAACqL,OAAO,EAAED,QAAQ,CAAC;AACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}