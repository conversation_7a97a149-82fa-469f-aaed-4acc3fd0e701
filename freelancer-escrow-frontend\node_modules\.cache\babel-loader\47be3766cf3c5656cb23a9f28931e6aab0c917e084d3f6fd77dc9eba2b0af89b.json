{"ast": null, "code": "import { a as i } from \"./chunk-A63SMUOU.mjs\";\nvar r = class a extends i {\n  constructor(e) {\n    super(), this.chainId = e;\n  }\n  serialize(e) {\n    e.serializeU8(this.chainId);\n  }\n  static deserialize(e) {\n    let s = e.deserializeU8();\n    return new a(s);\n  }\n};\nexport { r as a };", "map": {"version": 3, "names": ["r", "a", "i", "constructor", "e", "chainId", "serialize", "serializeU8", "deserialize", "s", "deserializeU8"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\chainId.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../../bcs/serializer\";\nimport { Deserializer } from \"../../bcs/deserializer\";\n\n/**\n * Representation of a ChainId that can serialized and deserialized\n */\nexport class ChainId extends Serializable {\n  public readonly chainId: number;\n\n  constructor(chainId: number) {\n    super();\n    this.chainId = chainId;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU8(this.chainId);\n  }\n\n  static deserialize(deserializer: Deserializer): ChainId {\n    const chainId = deserializer.deserializeU8();\n    return new ChainId(chainId);\n  }\n}\n"], "mappings": ";AASO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAgBC,CAAa;EAGxCC,YAAYC,CAAA,EAAiB;IAC3B,MAAM,GACN,KAAKC,OAAA,GAAUD,CACjB;EAAA;EAEAE,UAAUF,CAAA,EAA8B;IACtCA,CAAA,CAAWG,WAAA,CAAY,KAAKF,OAAO,CACrC;EAAA;EAEA,OAAOG,YAAYJ,CAAA,EAAqC;IACtD,IAAMK,CAAA,GAAUL,CAAA,CAAaM,aAAA,CAAc;IAC3C,OAAO,IAAIT,CAAA,CAAQQ,CAAO,CAC5B;EAAA;AACF;AAAA,SAAAT,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}