{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = unstringifyBigInts;\nfunction unstringifyBigInts(o) {\n  if (Array.isArray(o)) {\n    return o.map(unstringifyBigInts);\n  } else if (typeof o == 'object') {\n    const res = {};\n    for (const [key, val] of Object.entries(o)) {\n      res[key] = unstringifyBigInts(val);\n    }\n    return res;\n  }\n  // base64 decode\n  const byteArray = Uint8Array.from(atob(o), c => c.charCodeAt(0));\n  const hex = [...byteArray].map(x => x.toString(16).padStart(2, '0')).join('');\n  return BigInt(`0x${hex}`);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "unstringifyBigInts", "o", "Array", "isArray", "map", "res", "key", "val", "entries", "byteArray", "Uint8Array", "from", "atob", "c", "charCodeAt", "hex", "x", "toString", "padStart", "join", "BigInt"], "sources": ["C:/Users/<USER>/Desktop/Freelancer-Platform-on-Aptos--DAPPS-main/freelancer-escrow-frontend/node_modules/poseidon-lite/poseidon/unstringify.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = unstringifyBigInts;\nfunction unstringifyBigInts(o) {\n  if (Array.isArray(o)) {\n    return o.map(unstringifyBigInts);\n  } else if (typeof o == 'object') {\n    const res = {};\n    for (const [key, val] of Object.entries(o)) {\n      res[key] = unstringifyBigInts(val);\n    }\n    return res;\n  }\n  // base64 decode\n  const byteArray = Uint8Array.from(atob(o), c => c.charCodeAt(0));\n  const hex = [...byteArray].map(x => x.toString(16).padStart(2, '0')).join('');\n  return BigInt(`0x${hex}`);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,kBAAkB;AACpC,SAASA,kBAAkBA,CAACC,CAAC,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACG,GAAG,CAACJ,kBAAkB,CAAC;EAClC,CAAC,MAAM,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;IAC/B,MAAMI,GAAG,GAAG,CAAC,CAAC;IACd,KAAK,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,IAAIZ,MAAM,CAACa,OAAO,CAACP,CAAC,CAAC,EAAE;MAC1CI,GAAG,CAACC,GAAG,CAAC,GAAGN,kBAAkB,CAACO,GAAG,CAAC;IACpC;IACA,OAAOF,GAAG;EACZ;EACA;EACA,MAAMI,SAAS,GAAGC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACX,CAAC,CAAC,EAAEY,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;EAChE,MAAMC,GAAG,GAAG,CAAC,GAAGN,SAAS,CAAC,CAACL,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAC7E,OAAOC,MAAM,CAAC,KAAKL,GAAG,EAAE,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}