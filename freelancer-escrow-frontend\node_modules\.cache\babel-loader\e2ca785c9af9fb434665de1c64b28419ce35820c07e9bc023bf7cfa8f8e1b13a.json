{"ast": null, "code": "/**\n * Twisted <PERSON> curve. The formula is: ax² + y² = 1 + dx²y².\n * For design rationale of types / exports, see weierstrass module documentation.\n * Untwisted Edwards curves exist, but they aren't used in real-world protocols.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { _validateObject, _abool2 as abool, _abytes2 as abytes, aInRange, bytesToHex, bytesToNumberLE, concatBytes, copyBytes, ensureBytes, isBytes, memoized, notImplemented, randomBytes as randomBytesWeb } from \"../utils.js\";\nimport { _createCurveFields, normalizeZ, pippenger, wNAF } from \"./curve.js\";\nimport { Field } from \"./modular.js\";\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0),\n  _1n = BigInt(1),\n  _2n = BigInt(2),\n  _8n = BigInt(8);\nfunction isEdValidXY(Fp, CURVE, x, y) {\n  const x2 = Fp.sqr(x);\n  const y2 = Fp.sqr(y);\n  const left = Fp.add(Fp.mul(CURVE.a, x2), y2);\n  const right = Fp.add(Fp.ONE, Fp.mul(CURVE.d, Fp.mul(x2, y2)));\n  return Fp.eql(left, right);\n}\nexport function edwards(params, extraOpts = {}) {\n  const validated = _createCurveFields('edwards', params, extraOpts, extraOpts.FpFnLE);\n  const {\n    Fp,\n    Fn\n  } = validated;\n  let CURVE = validated.CURVE;\n  const {\n    h: cofactor\n  } = CURVE;\n  _validateObject(extraOpts, {}, {\n    uvRatio: 'function'\n  });\n  // Important:\n  // There are some places where Fp.BYTES is used instead of nByteLength.\n  // So far, everything has been tested with curves of Fp.BYTES == nByteLength.\n  // TODO: test and find curves which behave otherwise.\n  const MASK = _2n << BigInt(Fn.BYTES * 8) - _1n;\n  const modP = n => Fp.create(n); // Function overrides\n  // sqrt(u/v)\n  const uvRatio = extraOpts.uvRatio || ((u, v) => {\n    try {\n      return {\n        isValid: true,\n        value: Fp.sqrt(Fp.div(u, v))\n      };\n    } catch (e) {\n      return {\n        isValid: false,\n        value: _0n\n      };\n    }\n  });\n  // Validate whether the passed curve params are valid.\n  // equation ax² + y² = 1 + dx²y² should work for generator point.\n  if (!isEdValidXY(Fp, CURVE, CURVE.Gx, CURVE.Gy)) throw new Error('bad curve params: generator point');\n  /**\n   * Asserts coordinate is valid: 0 <= n < MASK.\n   * Coordinates >= Fp.ORDER are allowed for zip215.\n   */\n  function acoord(title, n, banZero = false) {\n    const min = banZero ? _1n : _0n;\n    aInRange('coordinate ' + title, n, min, MASK);\n    return n;\n  }\n  function aextpoint(other) {\n    if (!(other instanceof Point)) throw new Error('ExtendedPoint expected');\n  }\n  // Converts Extended point to default (x, y) coordinates.\n  // Can accept precomputed Z^-1 - for example, from invertBatch.\n  const toAffineMemo = memoized((p, iz) => {\n    const {\n      X,\n      Y,\n      Z\n    } = p;\n    const is0 = p.is0();\n    if (iz == null) iz = is0 ? _8n : Fp.inv(Z); // 8 was chosen arbitrarily\n    const x = modP(X * iz);\n    const y = modP(Y * iz);\n    const zz = Fp.mul(Z, iz);\n    if (is0) return {\n      x: _0n,\n      y: _1n\n    };\n    if (zz !== _1n) throw new Error('invZ was invalid');\n    return {\n      x,\n      y\n    };\n  });\n  const assertValidMemo = memoized(p => {\n    const {\n      a,\n      d\n    } = CURVE;\n    if (p.is0()) throw new Error('bad point: ZERO'); // TODO: optimize, with vars below?\n    // Equation in affine coordinates: ax² + y² = 1 + dx²y²\n    // Equation in projective coordinates (X/Z, Y/Z, Z):  (aX² + Y²)Z² = Z⁴ + dX²Y²\n    const {\n      X,\n      Y,\n      Z,\n      T\n    } = p;\n    const X2 = modP(X * X); // X²\n    const Y2 = modP(Y * Y); // Y²\n    const Z2 = modP(Z * Z); // Z²\n    const Z4 = modP(Z2 * Z2); // Z⁴\n    const aX2 = modP(X2 * a); // aX²\n    const left = modP(Z2 * modP(aX2 + Y2)); // (aX² + Y²)Z²\n    const right = modP(Z4 + modP(d * modP(X2 * Y2))); // Z⁴ + dX²Y²\n    if (left !== right) throw new Error('bad point: equation left != right (1)');\n    // In Extended coordinates we also have T, which is x*y=T/Z: check X*Y == Z*T\n    const XY = modP(X * Y);\n    const ZT = modP(Z * T);\n    if (XY !== ZT) throw new Error('bad point: equation left != right (2)');\n    return true;\n  });\n  // Extended Point works in extended coordinates: (X, Y, Z, T) ∋ (x=X/Z, y=Y/Z, T=xy).\n  // https://en.wikipedia.org/wiki/Twisted_Edwards_curve#Extended_coordinates\n  class Point {\n    constructor(X, Y, Z, T) {\n      this.X = acoord('x', X);\n      this.Y = acoord('y', Y);\n      this.Z = acoord('z', Z, true);\n      this.T = acoord('t', T);\n      Object.freeze(this);\n    }\n    static CURVE() {\n      return CURVE;\n    }\n    static fromAffine(p) {\n      if (p instanceof Point) throw new Error('extended point not allowed');\n      const {\n        x,\n        y\n      } = p || {};\n      acoord('x', x);\n      acoord('y', y);\n      return new Point(x, y, _1n, modP(x * y));\n    }\n    // Uses algo from RFC8032 5.1.3.\n    static fromBytes(bytes, zip215 = false) {\n      const len = Fp.BYTES;\n      const {\n        a,\n        d\n      } = CURVE;\n      bytes = copyBytes(abytes(bytes, len, 'point'));\n      abool(zip215, 'zip215');\n      const normed = copyBytes(bytes); // copy again, we'll manipulate it\n      const lastByte = bytes[len - 1]; // select last byte\n      normed[len - 1] = lastByte & ~0x80; // clear last bit\n      const y = bytesToNumberLE(normed);\n      // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n      // RFC8032 prohibits >= p, but ZIP215 doesn't\n      // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n      // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n      const max = zip215 ? MASK : Fp.ORDER;\n      aInRange('point.y', y, _0n, max);\n      // Ed25519: x² = (y²-1)/(dy²+1) mod p. Ed448: x² = (y²-1)/(dy²-1) mod p. Generic case:\n      // ax²+y²=1+dx²y² => y²-1=dx²y²-ax² => y²-1=x²(dy²-a) => x²=(y²-1)/(dy²-a)\n      const y2 = modP(y * y); // denominator is always non-0 mod p.\n      const u = modP(y2 - _1n); // u = y² - 1\n      const v = modP(d * y2 - a); // v = d y² + 1.\n      let {\n        isValid,\n        value: x\n      } = uvRatio(u, v); // √(u/v)\n      if (!isValid) throw new Error('bad point: invalid y coordinate');\n      const isXOdd = (x & _1n) === _1n; // There are 2 square roots. Use x_0 bit to select proper\n      const isLastByteOdd = (lastByte & 0x80) !== 0; // x_0, last bit\n      if (!zip215 && x === _0n && isLastByteOdd)\n        // if x=0 and x_0 = 1, fail\n        throw new Error('bad point: x=0 and x_0=1');\n      if (isLastByteOdd !== isXOdd) x = modP(-x); // if x_0 != x mod 2, set x = p-x\n      return Point.fromAffine({\n        x,\n        y\n      });\n    }\n    static fromHex(bytes, zip215 = false) {\n      return Point.fromBytes(ensureBytes('point', bytes), zip215);\n    }\n    get x() {\n      return this.toAffine().x;\n    }\n    get y() {\n      return this.toAffine().y;\n    }\n    precompute(windowSize = 8, isLazy = true) {\n      wnaf.createCache(this, windowSize);\n      if (!isLazy) this.multiply(_2n); // random number\n      return this;\n    }\n    // Useful in fromAffine() - not for fromBytes(), which always created valid points.\n    assertValidity() {\n      assertValidMemo(this);\n    }\n    // Compare one point to another.\n    equals(other) {\n      aextpoint(other);\n      const {\n        X: X1,\n        Y: Y1,\n        Z: Z1\n      } = this;\n      const {\n        X: X2,\n        Y: Y2,\n        Z: Z2\n      } = other;\n      const X1Z2 = modP(X1 * Z2);\n      const X2Z1 = modP(X2 * Z1);\n      const Y1Z2 = modP(Y1 * Z2);\n      const Y2Z1 = modP(Y2 * Z1);\n      return X1Z2 === X2Z1 && Y1Z2 === Y2Z1;\n    }\n    is0() {\n      return this.equals(Point.ZERO);\n    }\n    negate() {\n      // Flips point sign to a negative one (-x, y in affine coords)\n      return new Point(modP(-this.X), this.Y, this.Z, modP(-this.T));\n    }\n    // Fast algo for doubling Extended Point.\n    // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#doubling-dbl-2008-hwcd\n    // Cost: 4M + 4S + 1*a + 6add + 1*2.\n    double() {\n      const {\n        a\n      } = CURVE;\n      const {\n        X: X1,\n        Y: Y1,\n        Z: Z1\n      } = this;\n      const A = modP(X1 * X1); // A = X12\n      const B = modP(Y1 * Y1); // B = Y12\n      const C = modP(_2n * modP(Z1 * Z1)); // C = 2*Z12\n      const D = modP(a * A); // D = a*A\n      const x1y1 = X1 + Y1;\n      const E = modP(modP(x1y1 * x1y1) - A - B); // E = (X1+Y1)2-A-B\n      const G = D + B; // G = D+B\n      const F = G - C; // F = G-C\n      const H = D - B; // H = D-B\n      const X3 = modP(E * F); // X3 = E*F\n      const Y3 = modP(G * H); // Y3 = G*H\n      const T3 = modP(E * H); // T3 = E*H\n      const Z3 = modP(F * G); // Z3 = F*G\n      return new Point(X3, Y3, Z3, T3);\n    }\n    // Fast algo for adding 2 Extended Points.\n    // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#addition-add-2008-hwcd\n    // Cost: 9M + 1*a + 1*d + 7add.\n    add(other) {\n      aextpoint(other);\n      const {\n        a,\n        d\n      } = CURVE;\n      const {\n        X: X1,\n        Y: Y1,\n        Z: Z1,\n        T: T1\n      } = this;\n      const {\n        X: X2,\n        Y: Y2,\n        Z: Z2,\n        T: T2\n      } = other;\n      const A = modP(X1 * X2); // A = X1*X2\n      const B = modP(Y1 * Y2); // B = Y1*Y2\n      const C = modP(T1 * d * T2); // C = T1*d*T2\n      const D = modP(Z1 * Z2); // D = Z1*Z2\n      const E = modP((X1 + Y1) * (X2 + Y2) - A - B); // E = (X1+Y1)*(X2+Y2)-A-B\n      const F = D - C; // F = D-C\n      const G = D + C; // G = D+C\n      const H = modP(B - a * A); // H = B-a*A\n      const X3 = modP(E * F); // X3 = E*F\n      const Y3 = modP(G * H); // Y3 = G*H\n      const T3 = modP(E * H); // T3 = E*H\n      const Z3 = modP(F * G); // Z3 = F*G\n      return new Point(X3, Y3, Z3, T3);\n    }\n    subtract(other) {\n      return this.add(other.negate());\n    }\n    // Constant-time multiplication.\n    multiply(scalar) {\n      // 1 <= scalar < L\n      if (!Fn.isValidNot0(scalar)) throw new Error('invalid scalar: expected 1 <= sc < curve.n');\n      const {\n        p,\n        f\n      } = wnaf.cached(this, scalar, p => normalizeZ(Point, p));\n      return normalizeZ(Point, [p, f])[0];\n    }\n    // Non-constant-time multiplication. Uses double-and-add algorithm.\n    // It's faster, but should only be used when you don't care about\n    // an exposed private key e.g. sig verification.\n    // Does NOT allow scalars higher than CURVE.n.\n    // Accepts optional accumulator to merge with multiply (important for sparse scalars)\n    multiplyUnsafe(scalar, acc = Point.ZERO) {\n      // 0 <= scalar < L\n      if (!Fn.isValid(scalar)) throw new Error('invalid scalar: expected 0 <= sc < curve.n');\n      if (scalar === _0n) return Point.ZERO;\n      if (this.is0() || scalar === _1n) return this;\n      return wnaf.unsafe(this, scalar, p => normalizeZ(Point, p), acc);\n    }\n    // Checks if point is of small order.\n    // If you add something to small order point, you will have \"dirty\"\n    // point with torsion component.\n    // Multiplies point by cofactor and checks if the result is 0.\n    isSmallOrder() {\n      return this.multiplyUnsafe(cofactor).is0();\n    }\n    // Multiplies point by curve order and checks if the result is 0.\n    // Returns `false` is the point is dirty.\n    isTorsionFree() {\n      return wnaf.unsafe(this, CURVE.n).is0();\n    }\n    // Converts Extended point to default (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    toAffine(invertedZ) {\n      return toAffineMemo(this, invertedZ);\n    }\n    clearCofactor() {\n      if (cofactor === _1n) return this;\n      return this.multiplyUnsafe(cofactor);\n    }\n    toBytes() {\n      const {\n        x,\n        y\n      } = this.toAffine();\n      // Fp.toBytes() allows non-canonical encoding of y (>= p).\n      const bytes = Fp.toBytes(y);\n      // Each y has 2 valid points: (x, y), (x,-y).\n      // When compressing, it's enough to store y and use the last byte to encode sign of x\n      bytes[bytes.length - 1] |= x & _1n ? 0x80 : 0;\n      return bytes;\n    }\n    toHex() {\n      return bytesToHex(this.toBytes());\n    }\n    toString() {\n      return `<Point ${this.is0() ? 'ZERO' : this.toHex()}>`;\n    }\n    // TODO: remove\n    get ex() {\n      return this.X;\n    }\n    get ey() {\n      return this.Y;\n    }\n    get ez() {\n      return this.Z;\n    }\n    get et() {\n      return this.T;\n    }\n    static normalizeZ(points) {\n      return normalizeZ(Point, points);\n    }\n    static msm(points, scalars) {\n      return pippenger(Point, Fn, points, scalars);\n    }\n    _setWindowSize(windowSize) {\n      this.precompute(windowSize);\n    }\n    toRawBytes() {\n      return this.toBytes();\n    }\n  }\n  // base / generator point\n  Point.BASE = new Point(CURVE.Gx, CURVE.Gy, _1n, modP(CURVE.Gx * CURVE.Gy));\n  // zero / infinity / identity point\n  Point.ZERO = new Point(_0n, _1n, _1n, _0n); // 0, 1, 1, 0\n  // math field\n  Point.Fp = Fp;\n  // scalar field\n  Point.Fn = Fn;\n  const wnaf = new wNAF(Point, Fn.BITS);\n  Point.BASE.precompute(8); // Enable precomputes. Slows down first publicKey computation by 20ms.\n  return Point;\n}\n/**\n * Base class for prime-order points like Ristretto255 and Decaf448.\n * These points eliminate cofactor issues by representing equivalence classes\n * of Edwards curve points.\n */\nexport class PrimeEdwardsPoint {\n  constructor(ep) {\n    this.ep = ep;\n  }\n  // Static methods that must be implemented by subclasses\n  static fromBytes(_bytes) {\n    notImplemented();\n  }\n  static fromHex(_hex) {\n    notImplemented();\n  }\n  get x() {\n    return this.toAffine().x;\n  }\n  get y() {\n    return this.toAffine().y;\n  }\n  // Common implementations\n  clearCofactor() {\n    // no-op for prime-order groups\n    return this;\n  }\n  assertValidity() {\n    this.ep.assertValidity();\n  }\n  toAffine(invertedZ) {\n    return this.ep.toAffine(invertedZ);\n  }\n  toHex() {\n    return bytesToHex(this.toBytes());\n  }\n  toString() {\n    return this.toHex();\n  }\n  isTorsionFree() {\n    return true;\n  }\n  isSmallOrder() {\n    return false;\n  }\n  add(other) {\n    this.assertSame(other);\n    return this.init(this.ep.add(other.ep));\n  }\n  subtract(other) {\n    this.assertSame(other);\n    return this.init(this.ep.subtract(other.ep));\n  }\n  multiply(scalar) {\n    return this.init(this.ep.multiply(scalar));\n  }\n  multiplyUnsafe(scalar) {\n    return this.init(this.ep.multiplyUnsafe(scalar));\n  }\n  double() {\n    return this.init(this.ep.double());\n  }\n  negate() {\n    return this.init(this.ep.negate());\n  }\n  precompute(windowSize, isLazy) {\n    return this.init(this.ep.precompute(windowSize, isLazy));\n  }\n  /** @deprecated use `toBytes` */\n  toRawBytes() {\n    return this.toBytes();\n  }\n}\n/**\n * Initializes EdDSA signatures over given Edwards curve.\n */\nexport function eddsa(Point, cHash, eddsaOpts = {}) {\n  if (typeof cHash !== 'function') throw new Error('\"hash\" function param is required');\n  _validateObject(eddsaOpts, {}, {\n    adjustScalarBytes: 'function',\n    randomBytes: 'function',\n    domain: 'function',\n    prehash: 'function',\n    mapToCurve: 'function'\n  });\n  const {\n    prehash\n  } = eddsaOpts;\n  const {\n    BASE,\n    Fp,\n    Fn\n  } = Point;\n  const randomBytes = eddsaOpts.randomBytes || randomBytesWeb;\n  const adjustScalarBytes = eddsaOpts.adjustScalarBytes || (bytes => bytes);\n  const domain = eddsaOpts.domain || ((data, ctx, phflag) => {\n    abool(phflag, 'phflag');\n    if (ctx.length || phflag) throw new Error('Contexts/pre-hash are not supported');\n    return data;\n  }); // NOOP\n  // Little-endian SHA512 with modulo n\n  function modN_LE(hash) {\n    return Fn.create(bytesToNumberLE(hash)); // Not Fn.fromBytes: it has length limit\n  }\n  // Get the hashed private scalar per RFC8032 5.1.5\n  function getPrivateScalar(key) {\n    const len = lengths.secretKey;\n    key = ensureBytes('private key', key, len);\n    // Hash private key with curve's hash function to produce uniformingly random input\n    // Check byte lengths: ensure(64, h(ensure(32, key)))\n    const hashed = ensureBytes('hashed private key', cHash(key), 2 * len);\n    const head = adjustScalarBytes(hashed.slice(0, len)); // clear first half bits, produce FE\n    const prefix = hashed.slice(len, 2 * len); // second half is called key prefix (5.1.6)\n    const scalar = modN_LE(head); // The actual private scalar\n    return {\n      head,\n      prefix,\n      scalar\n    };\n  }\n  /** Convenience method that creates public key from scalar. RFC8032 5.1.5 */\n  function getExtendedPublicKey(secretKey) {\n    const {\n      head,\n      prefix,\n      scalar\n    } = getPrivateScalar(secretKey);\n    const point = BASE.multiply(scalar); // Point on Edwards curve aka public key\n    const pointBytes = point.toBytes();\n    return {\n      head,\n      prefix,\n      scalar,\n      point,\n      pointBytes\n    };\n  }\n  /** Calculates EdDSA pub key. RFC8032 5.1.5. */\n  function getPublicKey(secretKey) {\n    return getExtendedPublicKey(secretKey).pointBytes;\n  }\n  // int('LE', SHA512(dom2(F, C) || msgs)) mod N\n  function hashDomainToScalar(context = Uint8Array.of(), ...msgs) {\n    const msg = concatBytes(...msgs);\n    return modN_LE(cHash(domain(msg, ensureBytes('context', context), !!prehash)));\n  }\n  /** Signs message with privateKey. RFC8032 5.1.6 */\n  function sign(msg, secretKey, options = {}) {\n    msg = ensureBytes('message', msg);\n    if (prehash) msg = prehash(msg); // for ed25519ph etc.\n    const {\n      prefix,\n      scalar,\n      pointBytes\n    } = getExtendedPublicKey(secretKey);\n    const r = hashDomainToScalar(options.context, prefix, msg); // r = dom2(F, C) || prefix || PH(M)\n    const R = BASE.multiply(r).toBytes(); // R = rG\n    const k = hashDomainToScalar(options.context, R, pointBytes, msg); // R || A || PH(M)\n    const s = Fn.create(r + k * scalar); // S = (r + k * s) mod L\n    if (!Fn.isValid(s)) throw new Error('sign failed: invalid s'); // 0 <= s < L\n    const rs = concatBytes(R, Fn.toBytes(s));\n    return abytes(rs, lengths.signature, 'result');\n  }\n  // verification rule is either zip215 or rfc8032 / nist186-5. Consult fromHex:\n  const verifyOpts = {\n    zip215: true\n  };\n  /**\n   * Verifies EdDSA signature against message and public key. RFC8032 5.1.7.\n   * An extended group equation is checked.\n   */\n  function verify(sig, msg, publicKey, options = verifyOpts) {\n    const {\n      context,\n      zip215\n    } = options;\n    const len = lengths.signature;\n    sig = ensureBytes('signature', sig, len);\n    msg = ensureBytes('message', msg);\n    publicKey = ensureBytes('publicKey', publicKey, lengths.publicKey);\n    if (zip215 !== undefined) abool(zip215, 'zip215');\n    if (prehash) msg = prehash(msg); // for ed25519ph, etc\n    const mid = len / 2;\n    const r = sig.subarray(0, mid);\n    const s = bytesToNumberLE(sig.subarray(mid, len));\n    let A, R, SB;\n    try {\n      // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n      // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n      // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n      A = Point.fromBytes(publicKey, zip215);\n      R = Point.fromBytes(r, zip215);\n      SB = BASE.multiplyUnsafe(s); // 0 <= s < l is done inside\n    } catch (error) {\n      return false;\n    }\n    if (!zip215 && A.isSmallOrder()) return false; // zip215 allows public keys of small order\n    const k = hashDomainToScalar(context, R.toBytes(), A.toBytes(), msg);\n    const RkA = R.add(A.multiplyUnsafe(k));\n    // Extended group equation\n    // [8][S]B = [8]R + [8][k]A'\n    return RkA.subtract(SB).clearCofactor().is0();\n  }\n  const _size = Fp.BYTES; // 32 for ed25519, 57 for ed448\n  const lengths = {\n    secretKey: _size,\n    publicKey: _size,\n    signature: 2 * _size,\n    seed: _size\n  };\n  function randomSecretKey(seed = randomBytes(lengths.seed)) {\n    return abytes(seed, lengths.seed, 'seed');\n  }\n  function keygen(seed) {\n    const secretKey = utils.randomSecretKey(seed);\n    return {\n      secretKey,\n      publicKey: getPublicKey(secretKey)\n    };\n  }\n  function isValidSecretKey(key) {\n    return isBytes(key) && key.length === Fn.BYTES;\n  }\n  function isValidPublicKey(key, zip215) {\n    try {\n      return !!Point.fromBytes(key, zip215);\n    } catch (error) {\n      return false;\n    }\n  }\n  const utils = {\n    getExtendedPublicKey,\n    randomSecretKey,\n    isValidSecretKey,\n    isValidPublicKey,\n    /**\n     * Converts ed public key to x public key. Uses formula:\n     * - ed25519:\n     *   - `(u, v) = ((1+y)/(1-y), sqrt(-486664)*u/x)`\n     *   - `(x, y) = (sqrt(-486664)*u/v, (u-1)/(u+1))`\n     * - ed448:\n     *   - `(u, v) = ((y-1)/(y+1), sqrt(156324)*u/x)`\n     *   - `(x, y) = (sqrt(156324)*u/v, (1+u)/(1-u))`\n     */\n    toMontgomery(publicKey) {\n      const {\n        y\n      } = Point.fromBytes(publicKey);\n      const size = lengths.publicKey;\n      const is25519 = size === 32;\n      if (!is25519 && size !== 57) throw new Error('only defined for 25519 and 448');\n      const u = is25519 ? Fp.div(_1n + y, _1n - y) : Fp.div(y - _1n, y + _1n);\n      return Fp.toBytes(u);\n    },\n    toMontgomerySecret(secretKey) {\n      const size = lengths.secretKey;\n      abytes(secretKey, size);\n      const hashed = cHash(secretKey.subarray(0, size));\n      return adjustScalarBytes(hashed).subarray(0, size);\n    },\n    /** @deprecated */\n    randomPrivateKey: randomSecretKey,\n    /** @deprecated */\n    precompute(windowSize = 8, point = Point.BASE) {\n      return point.precompute(windowSize, false);\n    }\n  };\n  return Object.freeze({\n    keygen,\n    getPublicKey,\n    sign,\n    verify,\n    utils,\n    Point,\n    lengths\n  });\n}\nfunction _eddsa_legacy_opts_to_new(c) {\n  const CURVE = {\n    a: c.a,\n    d: c.d,\n    p: c.Fp.ORDER,\n    n: c.n,\n    h: c.h,\n    Gx: c.Gx,\n    Gy: c.Gy\n  };\n  const Fp = c.Fp;\n  const Fn = Field(CURVE.n, c.nBitLength, true);\n  const curveOpts = {\n    Fp,\n    Fn,\n    uvRatio: c.uvRatio\n  };\n  const eddsaOpts = {\n    randomBytes: c.randomBytes,\n    adjustScalarBytes: c.adjustScalarBytes,\n    domain: c.domain,\n    prehash: c.prehash,\n    mapToCurve: c.mapToCurve\n  };\n  return {\n    CURVE,\n    curveOpts,\n    hash: c.hash,\n    eddsaOpts\n  };\n}\nfunction _eddsa_new_output_to_legacy(c, eddsa) {\n  const Point = eddsa.Point;\n  const legacy = Object.assign({}, eddsa, {\n    ExtendedPoint: Point,\n    CURVE: c,\n    nBitLength: Point.Fn.BITS,\n    nByteLength: Point.Fn.BYTES\n  });\n  return legacy;\n}\n// TODO: remove. Use eddsa\nexport function twistedEdwards(c) {\n  const {\n    CURVE,\n    curveOpts,\n    hash,\n    eddsaOpts\n  } = _eddsa_legacy_opts_to_new(c);\n  const Point = edwards(CURVE, curveOpts);\n  const EDDSA = eddsa(Point, hash, eddsaOpts);\n  return _eddsa_new_output_to_legacy(c, EDDSA);\n}", "map": {"version": 3, "names": ["_validateObject", "_abool2", "abool", "_abytes2", "abytes", "aInRange", "bytesToHex", "bytesToNumberLE", "concatBytes", "copyBytes", "ensureBytes", "isBytes", "memoized", "notImplemented", "randomBytes", "randomBytesWeb", "_createCurveFields", "normalizeZ", "pippenger", "wNAF", "Field", "_0n", "BigInt", "_1n", "_2n", "_8n", "isEdValidXY", "Fp", "CURVE", "x", "y", "x2", "sqr", "y2", "left", "add", "mul", "a", "right", "ONE", "d", "eql", "edwards", "params", "extraOpts", "validated", "FpFnLE", "Fn", "h", "cofactor", "uvRatio", "MASK", "BYTES", "modP", "n", "create", "u", "v", "<PERSON><PERSON><PERSON><PERSON>", "value", "sqrt", "div", "e", "Gx", "Gy", "Error", "acoord", "title", "banZero", "min", "aextpoint", "other", "Point", "toAffineMemo", "p", "iz", "X", "Y", "Z", "is0", "inv", "zz", "assertValidMemo", "T", "X2", "Y2", "Z2", "Z4", "aX2", "XY", "ZT", "constructor", "Object", "freeze", "fromAffine", "fromBytes", "bytes", "zip215", "len", "normed", "lastByte", "max", "ORDER", "isXOdd", "isLastByteOdd", "fromHex", "toAffine", "precompute", "windowSize", "isLazy", "wnaf", "createCache", "multiply", "assertValidity", "equals", "X1", "Y1", "Z1", "X1Z2", "X2Z1", "Y1Z2", "Y2Z1", "ZERO", "negate", "double", "A", "B", "C", "D", "x1y1", "E", "G", "F", "H", "X3", "Y3", "T3", "Z3", "T1", "T2", "subtract", "scalar", "isValidNot0", "f", "cached", "multiplyUnsafe", "acc", "unsafe", "isSmallOrder", "isTorsionFree", "invertedZ", "clearCofactor", "toBytes", "length", "toHex", "toString", "ex", "ey", "ez", "et", "points", "msm", "scalars", "_setWindowSize", "toRawBytes", "BASE", "BITS", "PrimeEdwardsPoint", "ep", "_bytes", "_hex", "assertSame", "init", "eddsa", "cHash", "eddsaOpts", "adjustScalarBytes", "domain", "prehash", "mapToCurve", "data", "ctx", "phflag", "modN_LE", "hash", "getPrivateScalar", "key", "lengths", "secret<PERSON>ey", "hashed", "head", "slice", "prefix", "getExtendedPublicKey", "point", "pointBytes", "getPublicKey", "hashDomainToScalar", "context", "Uint8Array", "of", "msgs", "msg", "sign", "options", "r", "R", "k", "s", "rs", "signature", "verifyOpts", "verify", "sig", "public<PERSON>ey", "undefined", "mid", "subarray", "SB", "error", "RkA", "_size", "seed", "randomSec<PERSON><PERSON>ey", "keygen", "utils", "isValidSecret<PERSON>", "isValidPublicKey", "toMontgomery", "size", "is25519", "toMontgomerySecret", "randomPrivateKey", "_eddsa_legacy_opts_to_new", "c", "nBitLength", "curveOpts", "_eddsa_new_output_to_legacy", "legacy", "assign", "ExtendedPoint", "nByteLength", "twistedEdwards", "EDDSA"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\curves\\src\\abstract\\edwards.ts"], "sourcesContent": ["/**\n * Twisted <PERSON> curve. The formula is: ax² + y² = 1 + dx²y².\n * For design rationale of types / exports, see weierstrass module documentation.\n * Untwisted Edwards curves exist, but they aren't used in real-world protocols.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  _validateObject,\n  _abool2 as abool,\n  _abytes2 as abytes,\n  aInRange,\n  bytesToHex,\n  bytesToNumberLE,\n  concatBytes,\n  copyBytes,\n  ensureBytes,\n  isBytes,\n  memoized,\n  notImplemented,\n  randomBytes as randomBytesWeb,\n  type FHash,\n  type Hex,\n} from '../utils.ts';\nimport {\n  _createCurveFields,\n  normalizeZ,\n  pippenger,\n  wNAF,\n  type AffinePoint,\n  type BasicCurve,\n  type CurveLengths,\n  type CurvePoint,\n  type CurvePointCons,\n} from './curve.ts';\nimport { Field, type IField, type NLength } from './modular.ts';\n\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _8n = BigInt(8);\n\nexport type UVRatio = (u: bigint, v: bigint) => { isValid: boolean; value: bigint };\n\n/** Instance of Extended Point with coordinates in X, Y, Z, T. */\nexport interface EdwardsPoint extends CurvePoint<bigint, EdwardsPoint> {\n  /** extended X coordinate. Different from affine x. */\n  readonly X: bigint;\n  /** extended Y coordinate. Different from affine y. */\n  readonly Y: bigint;\n  /** extended Z coordinate */\n  readonly Z: bigint;\n  /** extended T coordinate */\n  readonly T: bigint;\n\n  /** @deprecated use `toBytes` */\n  toRawBytes(): Uint8Array;\n  /** @deprecated use `p.precompute(windowSize)` */\n  _setWindowSize(windowSize: number): void;\n  /** @deprecated use .X */\n  readonly ex: bigint;\n  /** @deprecated use .Y */\n  readonly ey: bigint;\n  /** @deprecated use .Z */\n  readonly ez: bigint;\n  /** @deprecated use .T */\n  readonly et: bigint;\n}\n/** Static methods of Extended Point with coordinates in X, Y, Z, T. */\nexport interface EdwardsPointCons extends CurvePointCons<EdwardsPoint> {\n  new (X: bigint, Y: bigint, Z: bigint, T: bigint): EdwardsPoint;\n  CURVE(): EdwardsOpts;\n  fromBytes(bytes: Uint8Array, zip215?: boolean): EdwardsPoint;\n  fromHex(hex: Hex, zip215?: boolean): EdwardsPoint;\n  /** @deprecated use `import { pippenger } from '@noble/curves/abstract/curve.js';` */\n  msm(points: EdwardsPoint[], scalars: bigint[]): EdwardsPoint;\n}\n/** @deprecated use EdwardsPoint */\nexport type ExtPointType = EdwardsPoint;\n/** @deprecated use EdwardsPointCons */\nexport type ExtPointConstructor = EdwardsPointCons;\n\n/**\n * Twisted Edwards curve options.\n *\n * * a: formula param\n * * d: formula param\n * * p: prime characteristic (order) of finite field, in which arithmetics is done\n * * n: order of prime subgroup a.k.a total amount of valid curve points\n * * h: cofactor. h*n is group order; n is subgroup order\n * * Gx: x coordinate of generator point a.k.a. base point\n * * Gy: y coordinate of generator point\n */\nexport type EdwardsOpts = Readonly<{\n  p: bigint;\n  n: bigint;\n  h: bigint;\n  a: bigint;\n  d: bigint;\n  Gx: bigint;\n  Gy: bigint;\n}>;\n\n/**\n * Extra curve options for Twisted Edwards.\n *\n * * Fp: redefined Field over curve.p\n * * Fn: redefined Field over curve.n\n * * uvRatio: helper function for decompression, calculating √(u/v)\n */\nexport type EdwardsExtraOpts = Partial<{\n  Fp: IField<bigint>;\n  Fn: IField<bigint>;\n  FpFnLE: boolean;\n  uvRatio: (u: bigint, v: bigint) => { isValid: boolean; value: bigint };\n}>;\n\n/**\n * EdDSA (Edwards Digital Signature algorithm) options.\n *\n * * hash: hash function used to hash secret keys and messages\n * * adjustScalarBytes: clears bits to get valid field element\n * * domain: Used for hashing\n * * mapToCurve: for hash-to-curve standard\n * * prehash: RFC 8032 pre-hashing of messages to sign() / verify()\n * * randomBytes: function generating random bytes, used for randomSecretKey\n */\nexport type EdDSAOpts = Partial<{\n  adjustScalarBytes: (bytes: Uint8Array) => Uint8Array;\n  domain: (data: Uint8Array, ctx: Uint8Array, phflag: boolean) => Uint8Array;\n  mapToCurve: (scalar: bigint[]) => AffinePoint<bigint>;\n  prehash: FHash;\n  randomBytes: (bytesLength?: number) => Uint8Array;\n}>;\n\n/**\n * EdDSA (Edwards Digital Signature algorithm) interface.\n *\n * Allows to create and verify signatures, create public and secret keys.\n */\nexport interface EdDSA {\n  keygen: (seed?: Uint8Array) => { secretKey: Uint8Array; publicKey: Uint8Array };\n  getPublicKey: (secretKey: Hex) => Uint8Array;\n  sign: (message: Hex, secretKey: Hex, options?: { context?: Hex }) => Uint8Array;\n  verify: (\n    sig: Hex,\n    message: Hex,\n    publicKey: Hex,\n    options?: { context?: Hex; zip215: boolean }\n  ) => boolean;\n  Point: EdwardsPointCons;\n  utils: {\n    randomSecretKey: (seed?: Uint8Array) => Uint8Array;\n    isValidSecretKey: (secretKey: Uint8Array) => boolean;\n    isValidPublicKey: (publicKey: Uint8Array, zip215?: boolean) => boolean;\n\n    /**\n     * Converts ed public key to x public key.\n     *\n     * There is NO `fromMontgomery`:\n     * - There are 2 valid ed25519 points for every x25519, with flipped coordinate\n     * - Sometimes there are 0 valid ed25519 points, because x25519 *additionally*\n     *   accepts inputs on the quadratic twist, which can't be moved to ed25519\n     *\n     * @example\n     * ```js\n     * const someonesPub = ed25519.getPublicKey(ed25519.utils.randomSecretKey());\n     * const aPriv = x25519.utils.randomSecretKey();\n     * x25519.getSharedSecret(aPriv, ed25519.utils.toMontgomery(someonesPub))\n     * ```\n     */\n    toMontgomery: (publicKey: Uint8Array) => Uint8Array;\n    /**\n     * Converts ed secret key to x secret key.\n     * @example\n     * ```js\n     * const someonesPub = x25519.getPublicKey(x25519.utils.randomSecretKey());\n     * const aPriv = ed25519.utils.randomSecretKey();\n     * x25519.getSharedSecret(ed25519.utils.toMontgomerySecret(aPriv), someonesPub)\n     * ```\n     */\n    toMontgomerySecret: (privateKey: Uint8Array) => Uint8Array;\n    getExtendedPublicKey: (key: Hex) => {\n      head: Uint8Array;\n      prefix: Uint8Array;\n      scalar: bigint;\n      point: EdwardsPoint;\n      pointBytes: Uint8Array;\n    };\n\n    /** @deprecated use `randomSecretKey` */\n    randomPrivateKey: (seed?: Uint8Array) => Uint8Array;\n    /** @deprecated use `point.precompute()` */\n    precompute: (windowSize?: number, point?: EdwardsPoint) => EdwardsPoint;\n  };\n  lengths: CurveLengths;\n}\n\nfunction isEdValidXY(Fp: IField<bigint>, CURVE: EdwardsOpts, x: bigint, y: bigint): boolean {\n  const x2 = Fp.sqr(x);\n  const y2 = Fp.sqr(y);\n  const left = Fp.add(Fp.mul(CURVE.a, x2), y2);\n  const right = Fp.add(Fp.ONE, Fp.mul(CURVE.d, Fp.mul(x2, y2)));\n  return Fp.eql(left, right);\n}\n\nexport function edwards(params: EdwardsOpts, extraOpts: EdwardsExtraOpts = {}): EdwardsPointCons {\n  const validated = _createCurveFields('edwards', params, extraOpts, extraOpts.FpFnLE);\n  const { Fp, Fn } = validated;\n  let CURVE = validated.CURVE as EdwardsOpts;\n  const { h: cofactor } = CURVE;\n  _validateObject(extraOpts, {}, { uvRatio: 'function' });\n\n  // Important:\n  // There are some places where Fp.BYTES is used instead of nByteLength.\n  // So far, everything has been tested with curves of Fp.BYTES == nByteLength.\n  // TODO: test and find curves which behave otherwise.\n  const MASK = _2n << (BigInt(Fn.BYTES * 8) - _1n);\n  const modP = (n: bigint) => Fp.create(n); // Function overrides\n\n  // sqrt(u/v)\n  const uvRatio =\n    extraOpts.uvRatio ||\n    ((u: bigint, v: bigint) => {\n      try {\n        return { isValid: true, value: Fp.sqrt(Fp.div(u, v)) };\n      } catch (e) {\n        return { isValid: false, value: _0n };\n      }\n    });\n\n  // Validate whether the passed curve params are valid.\n  // equation ax² + y² = 1 + dx²y² should work for generator point.\n  if (!isEdValidXY(Fp, CURVE, CURVE.Gx, CURVE.Gy))\n    throw new Error('bad curve params: generator point');\n\n  /**\n   * Asserts coordinate is valid: 0 <= n < MASK.\n   * Coordinates >= Fp.ORDER are allowed for zip215.\n   */\n  function acoord(title: string, n: bigint, banZero = false) {\n    const min = banZero ? _1n : _0n;\n    aInRange('coordinate ' + title, n, min, MASK);\n    return n;\n  }\n\n  function aextpoint(other: unknown) {\n    if (!(other instanceof Point)) throw new Error('ExtendedPoint expected');\n  }\n  // Converts Extended point to default (x, y) coordinates.\n  // Can accept precomputed Z^-1 - for example, from invertBatch.\n  const toAffineMemo = memoized((p: Point, iz?: bigint): AffinePoint<bigint> => {\n    const { X, Y, Z } = p;\n    const is0 = p.is0();\n    if (iz == null) iz = is0 ? _8n : (Fp.inv(Z) as bigint); // 8 was chosen arbitrarily\n    const x = modP(X * iz);\n    const y = modP(Y * iz);\n    const zz = Fp.mul(Z, iz);\n    if (is0) return { x: _0n, y: _1n };\n    if (zz !== _1n) throw new Error('invZ was invalid');\n    return { x, y };\n  });\n  const assertValidMemo = memoized((p: Point) => {\n    const { a, d } = CURVE;\n    if (p.is0()) throw new Error('bad point: ZERO'); // TODO: optimize, with vars below?\n    // Equation in affine coordinates: ax² + y² = 1 + dx²y²\n    // Equation in projective coordinates (X/Z, Y/Z, Z):  (aX² + Y²)Z² = Z⁴ + dX²Y²\n    const { X, Y, Z, T } = p;\n    const X2 = modP(X * X); // X²\n    const Y2 = modP(Y * Y); // Y²\n    const Z2 = modP(Z * Z); // Z²\n    const Z4 = modP(Z2 * Z2); // Z⁴\n    const aX2 = modP(X2 * a); // aX²\n    const left = modP(Z2 * modP(aX2 + Y2)); // (aX² + Y²)Z²\n    const right = modP(Z4 + modP(d * modP(X2 * Y2))); // Z⁴ + dX²Y²\n    if (left !== right) throw new Error('bad point: equation left != right (1)');\n    // In Extended coordinates we also have T, which is x*y=T/Z: check X*Y == Z*T\n    const XY = modP(X * Y);\n    const ZT = modP(Z * T);\n    if (XY !== ZT) throw new Error('bad point: equation left != right (2)');\n    return true;\n  });\n\n  // Extended Point works in extended coordinates: (X, Y, Z, T) ∋ (x=X/Z, y=Y/Z, T=xy).\n  // https://en.wikipedia.org/wiki/Twisted_Edwards_curve#Extended_coordinates\n  class Point implements EdwardsPoint {\n    // base / generator point\n    static readonly BASE = new Point(CURVE.Gx, CURVE.Gy, _1n, modP(CURVE.Gx * CURVE.Gy));\n    // zero / infinity / identity point\n    static readonly ZERO = new Point(_0n, _1n, _1n, _0n); // 0, 1, 1, 0\n    // math field\n    static readonly Fp = Fp;\n    // scalar field\n    static readonly Fn = Fn;\n\n    readonly X: bigint;\n    readonly Y: bigint;\n    readonly Z: bigint;\n    readonly T: bigint;\n\n    constructor(X: bigint, Y: bigint, Z: bigint, T: bigint) {\n      this.X = acoord('x', X);\n      this.Y = acoord('y', Y);\n      this.Z = acoord('z', Z, true);\n      this.T = acoord('t', T);\n      Object.freeze(this);\n    }\n\n    static CURVE(): EdwardsOpts {\n      return CURVE;\n    }\n\n    static fromAffine(p: AffinePoint<bigint>): Point {\n      if (p instanceof Point) throw new Error('extended point not allowed');\n      const { x, y } = p || {};\n      acoord('x', x);\n      acoord('y', y);\n      return new Point(x, y, _1n, modP(x * y));\n    }\n\n    // Uses algo from RFC8032 5.1.3.\n    static fromBytes(bytes: Uint8Array, zip215 = false): Point {\n      const len = Fp.BYTES;\n      const { a, d } = CURVE;\n      bytes = copyBytes(abytes(bytes, len, 'point'));\n      abool(zip215, 'zip215');\n      const normed = copyBytes(bytes); // copy again, we'll manipulate it\n      const lastByte = bytes[len - 1]; // select last byte\n      normed[len - 1] = lastByte & ~0x80; // clear last bit\n      const y = bytesToNumberLE(normed);\n\n      // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n      // RFC8032 prohibits >= p, but ZIP215 doesn't\n      // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n      // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n      const max = zip215 ? MASK : Fp.ORDER;\n      aInRange('point.y', y, _0n, max);\n\n      // Ed25519: x² = (y²-1)/(dy²+1) mod p. Ed448: x² = (y²-1)/(dy²-1) mod p. Generic case:\n      // ax²+y²=1+dx²y² => y²-1=dx²y²-ax² => y²-1=x²(dy²-a) => x²=(y²-1)/(dy²-a)\n      const y2 = modP(y * y); // denominator is always non-0 mod p.\n      const u = modP(y2 - _1n); // u = y² - 1\n      const v = modP(d * y2 - a); // v = d y² + 1.\n      let { isValid, value: x } = uvRatio(u, v); // √(u/v)\n      if (!isValid) throw new Error('bad point: invalid y coordinate');\n      const isXOdd = (x & _1n) === _1n; // There are 2 square roots. Use x_0 bit to select proper\n      const isLastByteOdd = (lastByte & 0x80) !== 0; // x_0, last bit\n      if (!zip215 && x === _0n && isLastByteOdd)\n        // if x=0 and x_0 = 1, fail\n        throw new Error('bad point: x=0 and x_0=1');\n      if (isLastByteOdd !== isXOdd) x = modP(-x); // if x_0 != x mod 2, set x = p-x\n      return Point.fromAffine({ x, y });\n    }\n    static fromHex(bytes: Uint8Array, zip215 = false): Point {\n      return Point.fromBytes(ensureBytes('point', bytes), zip215);\n    }\n\n    get x(): bigint {\n      return this.toAffine().x;\n    }\n    get y(): bigint {\n      return this.toAffine().y;\n    }\n\n    precompute(windowSize: number = 8, isLazy = true) {\n      wnaf.createCache(this, windowSize);\n      if (!isLazy) this.multiply(_2n); // random number\n      return this;\n    }\n\n    // Useful in fromAffine() - not for fromBytes(), which always created valid points.\n    assertValidity(): void {\n      assertValidMemo(this);\n    }\n\n    // Compare one point to another.\n    equals(other: Point): boolean {\n      aextpoint(other);\n      const { X: X1, Y: Y1, Z: Z1 } = this;\n      const { X: X2, Y: Y2, Z: Z2 } = other;\n      const X1Z2 = modP(X1 * Z2);\n      const X2Z1 = modP(X2 * Z1);\n      const Y1Z2 = modP(Y1 * Z2);\n      const Y2Z1 = modP(Y2 * Z1);\n      return X1Z2 === X2Z1 && Y1Z2 === Y2Z1;\n    }\n\n    is0(): boolean {\n      return this.equals(Point.ZERO);\n    }\n\n    negate(): Point {\n      // Flips point sign to a negative one (-x, y in affine coords)\n      return new Point(modP(-this.X), this.Y, this.Z, modP(-this.T));\n    }\n\n    // Fast algo for doubling Extended Point.\n    // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#doubling-dbl-2008-hwcd\n    // Cost: 4M + 4S + 1*a + 6add + 1*2.\n    double(): Point {\n      const { a } = CURVE;\n      const { X: X1, Y: Y1, Z: Z1 } = this;\n      const A = modP(X1 * X1); // A = X12\n      const B = modP(Y1 * Y1); // B = Y12\n      const C = modP(_2n * modP(Z1 * Z1)); // C = 2*Z12\n      const D = modP(a * A); // D = a*A\n      const x1y1 = X1 + Y1;\n      const E = modP(modP(x1y1 * x1y1) - A - B); // E = (X1+Y1)2-A-B\n      const G = D + B; // G = D+B\n      const F = G - C; // F = G-C\n      const H = D - B; // H = D-B\n      const X3 = modP(E * F); // X3 = E*F\n      const Y3 = modP(G * H); // Y3 = G*H\n      const T3 = modP(E * H); // T3 = E*H\n      const Z3 = modP(F * G); // Z3 = F*G\n      return new Point(X3, Y3, Z3, T3);\n    }\n\n    // Fast algo for adding 2 Extended Points.\n    // https://hyperelliptic.org/EFD/g1p/auto-twisted-extended.html#addition-add-2008-hwcd\n    // Cost: 9M + 1*a + 1*d + 7add.\n    add(other: Point) {\n      aextpoint(other);\n      const { a, d } = CURVE;\n      const { X: X1, Y: Y1, Z: Z1, T: T1 } = this;\n      const { X: X2, Y: Y2, Z: Z2, T: T2 } = other;\n      const A = modP(X1 * X2); // A = X1*X2\n      const B = modP(Y1 * Y2); // B = Y1*Y2\n      const C = modP(T1 * d * T2); // C = T1*d*T2\n      const D = modP(Z1 * Z2); // D = Z1*Z2\n      const E = modP((X1 + Y1) * (X2 + Y2) - A - B); // E = (X1+Y1)*(X2+Y2)-A-B\n      const F = D - C; // F = D-C\n      const G = D + C; // G = D+C\n      const H = modP(B - a * A); // H = B-a*A\n      const X3 = modP(E * F); // X3 = E*F\n      const Y3 = modP(G * H); // Y3 = G*H\n      const T3 = modP(E * H); // T3 = E*H\n      const Z3 = modP(F * G); // Z3 = F*G\n      return new Point(X3, Y3, Z3, T3);\n    }\n\n    subtract(other: Point): Point {\n      return this.add(other.negate());\n    }\n\n    // Constant-time multiplication.\n    multiply(scalar: bigint): Point {\n      // 1 <= scalar < L\n      if (!Fn.isValidNot0(scalar)) throw new Error('invalid scalar: expected 1 <= sc < curve.n');\n      const { p, f } = wnaf.cached(this, scalar, (p) => normalizeZ(Point, p));\n      return normalizeZ(Point, [p, f])[0];\n    }\n\n    // Non-constant-time multiplication. Uses double-and-add algorithm.\n    // It's faster, but should only be used when you don't care about\n    // an exposed private key e.g. sig verification.\n    // Does NOT allow scalars higher than CURVE.n.\n    // Accepts optional accumulator to merge with multiply (important for sparse scalars)\n    multiplyUnsafe(scalar: bigint, acc = Point.ZERO): Point {\n      // 0 <= scalar < L\n      if (!Fn.isValid(scalar)) throw new Error('invalid scalar: expected 0 <= sc < curve.n');\n      if (scalar === _0n) return Point.ZERO;\n      if (this.is0() || scalar === _1n) return this;\n      return wnaf.unsafe(this, scalar, (p) => normalizeZ(Point, p), acc);\n    }\n\n    // Checks if point is of small order.\n    // If you add something to small order point, you will have \"dirty\"\n    // point with torsion component.\n    // Multiplies point by cofactor and checks if the result is 0.\n    isSmallOrder(): boolean {\n      return this.multiplyUnsafe(cofactor).is0();\n    }\n\n    // Multiplies point by curve order and checks if the result is 0.\n    // Returns `false` is the point is dirty.\n    isTorsionFree(): boolean {\n      return wnaf.unsafe(this, CURVE.n).is0();\n    }\n\n    // Converts Extended point to default (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    toAffine(invertedZ?: bigint): AffinePoint<bigint> {\n      return toAffineMemo(this, invertedZ);\n    }\n\n    clearCofactor(): Point {\n      if (cofactor === _1n) return this;\n      return this.multiplyUnsafe(cofactor);\n    }\n\n    toBytes(): Uint8Array {\n      const { x, y } = this.toAffine();\n      // Fp.toBytes() allows non-canonical encoding of y (>= p).\n      const bytes = Fp.toBytes(y);\n      // Each y has 2 valid points: (x, y), (x,-y).\n      // When compressing, it's enough to store y and use the last byte to encode sign of x\n      bytes[bytes.length - 1] |= x & _1n ? 0x80 : 0;\n      return bytes;\n    }\n    toHex(): string {\n      return bytesToHex(this.toBytes());\n    }\n\n    toString() {\n      return `<Point ${this.is0() ? 'ZERO' : this.toHex()}>`;\n    }\n\n    // TODO: remove\n    get ex(): bigint {\n      return this.X;\n    }\n    get ey(): bigint {\n      return this.Y;\n    }\n    get ez(): bigint {\n      return this.Z;\n    }\n    get et(): bigint {\n      return this.T;\n    }\n    static normalizeZ(points: Point[]): Point[] {\n      return normalizeZ(Point, points);\n    }\n    static msm(points: Point[], scalars: bigint[]): Point {\n      return pippenger(Point, Fn, points, scalars);\n    }\n    _setWindowSize(windowSize: number) {\n      this.precompute(windowSize);\n    }\n    toRawBytes(): Uint8Array {\n      return this.toBytes();\n    }\n  }\n  const wnaf = new wNAF(Point, Fn.BITS);\n  Point.BASE.precompute(8); // Enable precomputes. Slows down first publicKey computation by 20ms.\n  return Point;\n}\n\n/**\n * Base class for prime-order points like Ristretto255 and Decaf448.\n * These points eliminate cofactor issues by representing equivalence classes\n * of Edwards curve points.\n */\nexport abstract class PrimeEdwardsPoint<T extends PrimeEdwardsPoint<T>>\n  implements CurvePoint<bigint, T>\n{\n  static BASE: PrimeEdwardsPoint<any>;\n  static ZERO: PrimeEdwardsPoint<any>;\n  static Fp: IField<bigint>;\n  static Fn: IField<bigint>;\n\n  protected readonly ep: EdwardsPoint;\n\n  constructor(ep: EdwardsPoint) {\n    this.ep = ep;\n  }\n\n  // Abstract methods that must be implemented by subclasses\n  abstract toBytes(): Uint8Array;\n  abstract equals(other: T): boolean;\n\n  // Static methods that must be implemented by subclasses\n  static fromBytes(_bytes: Uint8Array): any {\n    notImplemented();\n  }\n\n  static fromHex(_hex: Hex): any {\n    notImplemented();\n  }\n\n  get x(): bigint {\n    return this.toAffine().x;\n  }\n  get y(): bigint {\n    return this.toAffine().y;\n  }\n\n  // Common implementations\n  clearCofactor(): T {\n    // no-op for prime-order groups\n    return this as any;\n  }\n\n  assertValidity(): void {\n    this.ep.assertValidity();\n  }\n\n  toAffine(invertedZ?: bigint): AffinePoint<bigint> {\n    return this.ep.toAffine(invertedZ);\n  }\n\n  toHex(): string {\n    return bytesToHex(this.toBytes());\n  }\n\n  toString(): string {\n    return this.toHex();\n  }\n\n  isTorsionFree(): boolean {\n    return true;\n  }\n\n  isSmallOrder(): boolean {\n    return false;\n  }\n\n  add(other: T): T {\n    this.assertSame(other);\n    return this.init(this.ep.add(other.ep));\n  }\n\n  subtract(other: T): T {\n    this.assertSame(other);\n    return this.init(this.ep.subtract(other.ep));\n  }\n\n  multiply(scalar: bigint): T {\n    return this.init(this.ep.multiply(scalar));\n  }\n\n  multiplyUnsafe(scalar: bigint): T {\n    return this.init(this.ep.multiplyUnsafe(scalar));\n  }\n\n  double(): T {\n    return this.init(this.ep.double());\n  }\n\n  negate(): T {\n    return this.init(this.ep.negate());\n  }\n\n  precompute(windowSize?: number, isLazy?: boolean): T {\n    return this.init(this.ep.precompute(windowSize, isLazy));\n  }\n\n  // Helper methods\n  abstract is0(): boolean;\n  protected abstract assertSame(other: T): void;\n  protected abstract init(ep: EdwardsPoint): T;\n\n  /** @deprecated use `toBytes` */\n  toRawBytes(): Uint8Array {\n    return this.toBytes();\n  }\n}\n\n/**\n * Initializes EdDSA signatures over given Edwards curve.\n */\nexport function eddsa(Point: EdwardsPointCons, cHash: FHash, eddsaOpts: EdDSAOpts = {}): EdDSA {\n  if (typeof cHash !== 'function') throw new Error('\"hash\" function param is required');\n  _validateObject(\n    eddsaOpts,\n    {},\n    {\n      adjustScalarBytes: 'function',\n      randomBytes: 'function',\n      domain: 'function',\n      prehash: 'function',\n      mapToCurve: 'function',\n    }\n  );\n\n  const { prehash } = eddsaOpts;\n  const { BASE, Fp, Fn } = Point;\n\n  const randomBytes = eddsaOpts.randomBytes || randomBytesWeb;\n  const adjustScalarBytes = eddsaOpts.adjustScalarBytes || ((bytes: Uint8Array) => bytes);\n  const domain =\n    eddsaOpts.domain ||\n    ((data: Uint8Array, ctx: Uint8Array, phflag: boolean) => {\n      abool(phflag, 'phflag');\n      if (ctx.length || phflag) throw new Error('Contexts/pre-hash are not supported');\n      return data;\n    }); // NOOP\n\n  // Little-endian SHA512 with modulo n\n  function modN_LE(hash: Uint8Array): bigint {\n    return Fn.create(bytesToNumberLE(hash)); // Not Fn.fromBytes: it has length limit\n  }\n\n  // Get the hashed private scalar per RFC8032 5.1.5\n  function getPrivateScalar(key: Hex) {\n    const len = lengths.secretKey;\n    key = ensureBytes('private key', key, len);\n    // Hash private key with curve's hash function to produce uniformingly random input\n    // Check byte lengths: ensure(64, h(ensure(32, key)))\n    const hashed = ensureBytes('hashed private key', cHash(key), 2 * len);\n    const head = adjustScalarBytes(hashed.slice(0, len)); // clear first half bits, produce FE\n    const prefix = hashed.slice(len, 2 * len); // second half is called key prefix (5.1.6)\n    const scalar = modN_LE(head); // The actual private scalar\n    return { head, prefix, scalar };\n  }\n\n  /** Convenience method that creates public key from scalar. RFC8032 5.1.5 */\n  function getExtendedPublicKey(secretKey: Hex) {\n    const { head, prefix, scalar } = getPrivateScalar(secretKey);\n    const point = BASE.multiply(scalar); // Point on Edwards curve aka public key\n    const pointBytes = point.toBytes();\n    return { head, prefix, scalar, point, pointBytes };\n  }\n\n  /** Calculates EdDSA pub key. RFC8032 5.1.5. */\n  function getPublicKey(secretKey: Hex): Uint8Array {\n    return getExtendedPublicKey(secretKey).pointBytes;\n  }\n\n  // int('LE', SHA512(dom2(F, C) || msgs)) mod N\n  function hashDomainToScalar(context: Hex = Uint8Array.of(), ...msgs: Uint8Array[]) {\n    const msg = concatBytes(...msgs);\n    return modN_LE(cHash(domain(msg, ensureBytes('context', context), !!prehash)));\n  }\n\n  /** Signs message with privateKey. RFC8032 5.1.6 */\n  function sign(msg: Hex, secretKey: Hex, options: { context?: Hex } = {}): Uint8Array {\n    msg = ensureBytes('message', msg);\n    if (prehash) msg = prehash(msg); // for ed25519ph etc.\n    const { prefix, scalar, pointBytes } = getExtendedPublicKey(secretKey);\n    const r = hashDomainToScalar(options.context, prefix, msg); // r = dom2(F, C) || prefix || PH(M)\n    const R = BASE.multiply(r).toBytes(); // R = rG\n    const k = hashDomainToScalar(options.context, R, pointBytes, msg); // R || A || PH(M)\n    const s = Fn.create(r + k * scalar); // S = (r + k * s) mod L\n    if (!Fn.isValid(s)) throw new Error('sign failed: invalid s'); // 0 <= s < L\n    const rs = concatBytes(R, Fn.toBytes(s));\n    return abytes(rs, lengths.signature, 'result');\n  }\n\n  // verification rule is either zip215 or rfc8032 / nist186-5. Consult fromHex:\n  const verifyOpts: { context?: Hex; zip215?: boolean } = { zip215: true };\n\n  /**\n   * Verifies EdDSA signature against message and public key. RFC8032 5.1.7.\n   * An extended group equation is checked.\n   */\n  function verify(sig: Hex, msg: Hex, publicKey: Hex, options = verifyOpts): boolean {\n    const { context, zip215 } = options;\n    const len = lengths.signature;\n    sig = ensureBytes('signature', sig, len);\n    msg = ensureBytes('message', msg);\n    publicKey = ensureBytes('publicKey', publicKey, lengths.publicKey);\n    if (zip215 !== undefined) abool(zip215, 'zip215');\n    if (prehash) msg = prehash(msg); // for ed25519ph, etc\n\n    const mid = len / 2;\n    const r = sig.subarray(0, mid);\n    const s = bytesToNumberLE(sig.subarray(mid, len));\n    let A, R, SB;\n    try {\n      // zip215=true is good for consensus-critical apps. =false follows RFC8032 / NIST186-5.\n      // zip215=true:  0 <= y < MASK (2^256 for ed25519)\n      // zip215=false: 0 <= y < P (2^255-19 for ed25519)\n      A = Point.fromBytes(publicKey, zip215);\n      R = Point.fromBytes(r, zip215);\n      SB = BASE.multiplyUnsafe(s); // 0 <= s < l is done inside\n    } catch (error) {\n      return false;\n    }\n    if (!zip215 && A.isSmallOrder()) return false; // zip215 allows public keys of small order\n\n    const k = hashDomainToScalar(context, R.toBytes(), A.toBytes(), msg);\n    const RkA = R.add(A.multiplyUnsafe(k));\n    // Extended group equation\n    // [8][S]B = [8]R + [8][k]A'\n    return RkA.subtract(SB).clearCofactor().is0();\n  }\n\n  const _size = Fp.BYTES; // 32 for ed25519, 57 for ed448\n  const lengths = {\n    secretKey: _size,\n    publicKey: _size,\n    signature: 2 * _size,\n    seed: _size,\n  };\n  function randomSecretKey(seed = randomBytes(lengths.seed)): Uint8Array {\n    return abytes(seed, lengths.seed, 'seed');\n  }\n  function keygen(seed?: Uint8Array) {\n    const secretKey = utils.randomSecretKey(seed);\n    return { secretKey, publicKey: getPublicKey(secretKey) };\n  }\n  function isValidSecretKey(key: Uint8Array): boolean {\n    return isBytes(key) && key.length === Fn.BYTES;\n  }\n  function isValidPublicKey(key: Uint8Array, zip215?: boolean): boolean {\n    try {\n      return !!Point.fromBytes(key, zip215);\n    } catch (error) {\n      return false;\n    }\n  }\n\n  const utils = {\n    getExtendedPublicKey,\n    randomSecretKey,\n    isValidSecretKey,\n    isValidPublicKey,\n    /**\n     * Converts ed public key to x public key. Uses formula:\n     * - ed25519:\n     *   - `(u, v) = ((1+y)/(1-y), sqrt(-486664)*u/x)`\n     *   - `(x, y) = (sqrt(-486664)*u/v, (u-1)/(u+1))`\n     * - ed448:\n     *   - `(u, v) = ((y-1)/(y+1), sqrt(156324)*u/x)`\n     *   - `(x, y) = (sqrt(156324)*u/v, (1+u)/(1-u))`\n     */\n    toMontgomery(publicKey: Uint8Array): Uint8Array {\n      const { y } = Point.fromBytes(publicKey);\n      const size = lengths.publicKey;\n      const is25519 = size === 32;\n      if (!is25519 && size !== 57) throw new Error('only defined for 25519 and 448');\n      const u = is25519 ? Fp.div(_1n + y, _1n - y) : Fp.div(y - _1n, y + _1n);\n      return Fp.toBytes(u);\n    },\n\n    toMontgomerySecret(secretKey: Uint8Array): Uint8Array {\n      const size = lengths.secretKey;\n      abytes(secretKey, size);\n      const hashed = cHash(secretKey.subarray(0, size));\n      return adjustScalarBytes(hashed).subarray(0, size);\n    },\n\n    /** @deprecated */\n    randomPrivateKey: randomSecretKey,\n    /** @deprecated */\n    precompute(windowSize = 8, point: EdwardsPoint = Point.BASE): EdwardsPoint {\n      return point.precompute(windowSize, false);\n    },\n  };\n\n  return Object.freeze({\n    keygen,\n    getPublicKey,\n    sign,\n    verify,\n    utils,\n    Point,\n    lengths,\n  });\n}\n\n// TODO: remove everything below\nexport type CurveType = BasicCurve<bigint> & {\n  a: bigint; // curve param a\n  d: bigint; // curve param d\n  /** @deprecated the property will be removed in next release */\n  hash: FHash; // Hashing\n  randomBytes?: (bytesLength?: number) => Uint8Array; // CSPRNG\n  adjustScalarBytes?: (bytes: Uint8Array) => Uint8Array; // clears bits to get valid field elemtn\n  domain?: (data: Uint8Array, ctx: Uint8Array, phflag: boolean) => Uint8Array; // Used for hashing\n  uvRatio?: UVRatio; // Ratio √(u/v)\n  prehash?: FHash; // RFC 8032 pre-hashing of messages to sign() / verify()\n  mapToCurve?: (scalar: bigint[]) => AffinePoint<bigint>; // for hash-to-curve standard\n};\nexport type CurveTypeWithLength = Readonly<CurveType & Partial<NLength>>;\nexport type CurveFn = {\n  /** @deprecated the property will be removed in next release */\n  CURVE: CurveType;\n  keygen: EdDSA['keygen'];\n  getPublicKey: EdDSA['getPublicKey'];\n  sign: EdDSA['sign'];\n  verify: EdDSA['verify'];\n  Point: EdwardsPointCons;\n  /** @deprecated use `Point` */\n  ExtendedPoint: EdwardsPointCons;\n  utils: EdDSA['utils'];\n  lengths: CurveLengths;\n};\nexport type EdComposed = {\n  CURVE: EdwardsOpts;\n  curveOpts: EdwardsExtraOpts;\n  hash: FHash;\n  eddsaOpts: EdDSAOpts;\n};\nfunction _eddsa_legacy_opts_to_new(c: CurveTypeWithLength): EdComposed {\n  const CURVE: EdwardsOpts = {\n    a: c.a,\n    d: c.d,\n    p: c.Fp.ORDER,\n    n: c.n,\n    h: c.h,\n    Gx: c.Gx,\n    Gy: c.Gy,\n  };\n  const Fp = c.Fp;\n  const Fn = Field(CURVE.n, c.nBitLength, true);\n  const curveOpts: EdwardsExtraOpts = { Fp, Fn, uvRatio: c.uvRatio };\n  const eddsaOpts: EdDSAOpts = {\n    randomBytes: c.randomBytes,\n    adjustScalarBytes: c.adjustScalarBytes,\n    domain: c.domain,\n    prehash: c.prehash,\n    mapToCurve: c.mapToCurve,\n  };\n  return { CURVE, curveOpts, hash: c.hash, eddsaOpts };\n}\nfunction _eddsa_new_output_to_legacy(c: CurveTypeWithLength, eddsa: EdDSA): CurveFn {\n  const Point = eddsa.Point;\n  const legacy = Object.assign({}, eddsa, {\n    ExtendedPoint: Point,\n    CURVE: c,\n    nBitLength: Point.Fn.BITS,\n    nByteLength: Point.Fn.BYTES,\n  });\n  return legacy;\n}\n// TODO: remove. Use eddsa\nexport function twistedEdwards(c: CurveTypeWithLength): CurveFn {\n  const { CURVE, curveOpts, hash, eddsaOpts } = _eddsa_legacy_opts_to_new(c);\n  const Point = edwards(CURVE, curveOpts);\n  const EDDSA = eddsa(Point, hash, eddsaOpts);\n  return _eddsa_new_output_to_legacy(c, EDDSA);\n}\n"], "mappings": "AAAA;;;;;;AAMA;AACA,SACEA,eAAe,EACfC,OAAO,IAAIC,KAAK,EAChBC,QAAQ,IAAIC,MAAM,EAClBC,QAAQ,EACRC,UAAU,EACVC,eAAe,EACfC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,WAAW,IAAIC,cAAc,QAGxB,aAAa;AACpB,SACEC,kBAAkB,EAClBC,UAAU,EACVC,SAAS,EACTC,IAAI,QAMC,YAAY;AACnB,SAASC,KAAK,QAAmC,cAAc;AAE/D;AACA;AACA,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;EAAEC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;EAAEE,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;EAAEG,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC;AA8JxE,SAASI,WAAWA,CAACC,EAAkB,EAAEC,KAAkB,EAAEC,CAAS,EAAEC,CAAS;EAC/E,MAAMC,EAAE,GAAGJ,EAAE,CAACK,GAAG,CAACH,CAAC,CAAC;EACpB,MAAMI,EAAE,GAAGN,EAAE,CAACK,GAAG,CAACF,CAAC,CAAC;EACpB,MAAMI,IAAI,GAAGP,EAAE,CAACQ,GAAG,CAACR,EAAE,CAACS,GAAG,CAACR,KAAK,CAACS,CAAC,EAAEN,EAAE,CAAC,EAAEE,EAAE,CAAC;EAC5C,MAAMK,KAAK,GAAGX,EAAE,CAACQ,GAAG,CAACR,EAAE,CAACY,GAAG,EAAEZ,EAAE,CAACS,GAAG,CAACR,KAAK,CAACY,CAAC,EAAEb,EAAE,CAACS,GAAG,CAACL,EAAE,EAAEE,EAAE,CAAC,CAAC,CAAC;EAC7D,OAAON,EAAE,CAACc,GAAG,CAACP,IAAI,EAAEI,KAAK,CAAC;AAC5B;AAEA,OAAM,SAAUI,OAAOA,CAACC,MAAmB,EAAEC,SAAA,GAA8B,EAAE;EAC3E,MAAMC,SAAS,GAAG7B,kBAAkB,CAAC,SAAS,EAAE2B,MAAM,EAAEC,SAAS,EAAEA,SAAS,CAACE,MAAM,CAAC;EACpF,MAAM;IAAEnB,EAAE;IAAEoB;EAAE,CAAE,GAAGF,SAAS;EAC5B,IAAIjB,KAAK,GAAGiB,SAAS,CAACjB,KAAoB;EAC1C,MAAM;IAAEoB,CAAC,EAAEC;EAAQ,CAAE,GAAGrB,KAAK;EAC7B5B,eAAe,CAAC4C,SAAS,EAAE,EAAE,EAAE;IAAEM,OAAO,EAAE;EAAU,CAAE,CAAC;EAEvD;EACA;EACA;EACA;EACA,MAAMC,IAAI,GAAG3B,GAAG,IAAKF,MAAM,CAACyB,EAAE,CAACK,KAAK,GAAG,CAAC,CAAC,GAAG7B,GAAI;EAChD,MAAM8B,IAAI,GAAIC,CAAS,IAAK3B,EAAE,CAAC4B,MAAM,CAACD,CAAC,CAAC,CAAC,CAAC;EAE1C;EACA,MAAMJ,OAAO,GACXN,SAAS,CAACM,OAAO,KAChB,CAACM,CAAS,EAAEC,CAAS,KAAI;IACxB,IAAI;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,KAAK,EAAEhC,EAAE,CAACiC,IAAI,CAACjC,EAAE,CAACkC,GAAG,CAACL,CAAC,EAAEC,CAAC,CAAC;MAAC,CAAE;IACxD,CAAC,CAAC,OAAOK,CAAC,EAAE;MACV,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEtC;MAAG,CAAE;IACvC;EACF,CAAC,CAAC;EAEJ;EACA;EACA,IAAI,CAACK,WAAW,CAACC,EAAE,EAAEC,KAAK,EAAEA,KAAK,CAACmC,EAAE,EAAEnC,KAAK,CAACoC,EAAE,CAAC,EAC7C,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;EAEtD;;;;EAIA,SAASC,MAAMA,CAACC,KAAa,EAAEb,CAAS,EAAEc,OAAO,GAAG,KAAK;IACvD,MAAMC,GAAG,GAAGD,OAAO,GAAG7C,GAAG,GAAGF,GAAG;IAC/BhB,QAAQ,CAAC,aAAa,GAAG8D,KAAK,EAAEb,CAAC,EAAEe,GAAG,EAAElB,IAAI,CAAC;IAC7C,OAAOG,CAAC;EACV;EAEA,SAASgB,SAASA,CAACC,KAAc;IAC/B,IAAI,EAAEA,KAAK,YAAYC,KAAK,CAAC,EAAE,MAAM,IAAIP,KAAK,CAAC,wBAAwB,CAAC;EAC1E;EACA;EACA;EACA,MAAMQ,YAAY,GAAG7D,QAAQ,CAAC,CAAC8D,CAAQ,EAAEC,EAAW,KAAyB;IAC3E,MAAM;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAGJ,CAAC;IACrB,MAAMK,GAAG,GAAGL,CAAC,CAACK,GAAG,EAAE;IACnB,IAAIJ,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGI,GAAG,GAAGtD,GAAG,GAAIE,EAAE,CAACqD,GAAG,CAACF,CAAC,CAAY,CAAC,CAAC;IACxD,MAAMjD,CAAC,GAAGwB,IAAI,CAACuB,CAAC,GAAGD,EAAE,CAAC;IACtB,MAAM7C,CAAC,GAAGuB,IAAI,CAACwB,CAAC,GAAGF,EAAE,CAAC;IACtB,MAAMM,EAAE,GAAGtD,EAAE,CAACS,GAAG,CAAC0C,CAAC,EAAEH,EAAE,CAAC;IACxB,IAAII,GAAG,EAAE,OAAO;MAAElD,CAAC,EAAER,GAAG;MAAES,CAAC,EAAEP;IAAG,CAAE;IAClC,IAAI0D,EAAE,KAAK1D,GAAG,EAAE,MAAM,IAAI0C,KAAK,CAAC,kBAAkB,CAAC;IACnD,OAAO;MAAEpC,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC,CAAC;EACF,MAAMoD,eAAe,GAAGtE,QAAQ,CAAE8D,CAAQ,IAAI;IAC5C,MAAM;MAAErC,CAAC;MAAEG;IAAC,CAAE,GAAGZ,KAAK;IACtB,IAAI8C,CAAC,CAACK,GAAG,EAAE,EAAE,MAAM,IAAId,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;IACjD;IACA;IACA,MAAM;MAAEW,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEK;IAAC,CAAE,GAAGT,CAAC;IACxB,MAAMU,EAAE,GAAG/B,IAAI,CAACuB,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IACxB,MAAMS,EAAE,GAAGhC,IAAI,CAACwB,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IACxB,MAAMS,EAAE,GAAGjC,IAAI,CAACyB,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IACxB,MAAMS,EAAE,GAAGlC,IAAI,CAACiC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;IAC1B,MAAME,GAAG,GAAGnC,IAAI,CAAC+B,EAAE,GAAG/C,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAMH,IAAI,GAAGmB,IAAI,CAACiC,EAAE,GAAGjC,IAAI,CAACmC,GAAG,GAAGH,EAAE,CAAC,CAAC,CAAC,CAAC;IACxC,MAAM/C,KAAK,GAAGe,IAAI,CAACkC,EAAE,GAAGlC,IAAI,CAACb,CAAC,GAAGa,IAAI,CAAC+B,EAAE,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,IAAInD,IAAI,KAAKI,KAAK,EAAE,MAAM,IAAI2B,KAAK,CAAC,uCAAuC,CAAC;IAC5E;IACA,MAAMwB,EAAE,GAAGpC,IAAI,CAACuB,CAAC,GAAGC,CAAC,CAAC;IACtB,MAAMa,EAAE,GAAGrC,IAAI,CAACyB,CAAC,GAAGK,CAAC,CAAC;IACtB,IAAIM,EAAE,KAAKC,EAAE,EAAE,MAAM,IAAIzB,KAAK,CAAC,uCAAuC,CAAC;IACvE,OAAO,IAAI;EACb,CAAC,CAAC;EAEF;EACA;EACA,MAAMO,KAAK;IAeTmB,YAAYf,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEK,CAAS;MACpD,IAAI,CAACP,CAAC,GAAGV,MAAM,CAAC,GAAG,EAAEU,CAAC,CAAC;MACvB,IAAI,CAACC,CAAC,GAAGX,MAAM,CAAC,GAAG,EAAEW,CAAC,CAAC;MACvB,IAAI,CAACC,CAAC,GAAGZ,MAAM,CAAC,GAAG,EAAEY,CAAC,EAAE,IAAI,CAAC;MAC7B,IAAI,CAACK,CAAC,GAAGjB,MAAM,CAAC,GAAG,EAAEiB,CAAC,CAAC;MACvBS,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACrB;IAEA,OAAOjE,KAAKA,CAAA;MACV,OAAOA,KAAK;IACd;IAEA,OAAOkE,UAAUA,CAACpB,CAAsB;MACtC,IAAIA,CAAC,YAAYF,KAAK,EAAE,MAAM,IAAIP,KAAK,CAAC,4BAA4B,CAAC;MACrE,MAAM;QAAEpC,CAAC;QAAEC;MAAC,CAAE,GAAG4C,CAAC,IAAI,EAAE;MACxBR,MAAM,CAAC,GAAG,EAAErC,CAAC,CAAC;MACdqC,MAAM,CAAC,GAAG,EAAEpC,CAAC,CAAC;MACd,OAAO,IAAI0C,KAAK,CAAC3C,CAAC,EAAEC,CAAC,EAAEP,GAAG,EAAE8B,IAAI,CAACxB,CAAC,GAAGC,CAAC,CAAC,CAAC;IAC1C;IAEA;IACA,OAAOiE,SAASA,CAACC,KAAiB,EAAEC,MAAM,GAAG,KAAK;MAChD,MAAMC,GAAG,GAAGvE,EAAE,CAACyB,KAAK;MACpB,MAAM;QAAEf,CAAC;QAAEG;MAAC,CAAE,GAAGZ,KAAK;MACtBoE,KAAK,GAAGvF,SAAS,CAACL,MAAM,CAAC4F,KAAK,EAAEE,GAAG,EAAE,OAAO,CAAC,CAAC;MAC9ChG,KAAK,CAAC+F,MAAM,EAAE,QAAQ,CAAC;MACvB,MAAME,MAAM,GAAG1F,SAAS,CAACuF,KAAK,CAAC,CAAC,CAAC;MACjC,MAAMI,QAAQ,GAAGJ,KAAK,CAACE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MACjCC,MAAM,CAACD,GAAG,GAAG,CAAC,CAAC,GAAGE,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;MACpC,MAAMtE,CAAC,GAAGvB,eAAe,CAAC4F,MAAM,CAAC;MAEjC;MACA;MACA;MACA;MACA,MAAME,GAAG,GAAGJ,MAAM,GAAG9C,IAAI,GAAGxB,EAAE,CAAC2E,KAAK;MACpCjG,QAAQ,CAAC,SAAS,EAAEyB,CAAC,EAAET,GAAG,EAAEgF,GAAG,CAAC;MAEhC;MACA;MACA,MAAMpE,EAAE,GAAGoB,IAAI,CAACvB,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;MACxB,MAAM0B,CAAC,GAAGH,IAAI,CAACpB,EAAE,GAAGV,GAAG,CAAC,CAAC,CAAC;MAC1B,MAAMkC,CAAC,GAAGJ,IAAI,CAACb,CAAC,GAAGP,EAAE,GAAGI,CAAC,CAAC,CAAC,CAAC;MAC5B,IAAI;QAAEqB,OAAO;QAAEC,KAAK,EAAE9B;MAAC,CAAE,GAAGqB,OAAO,CAACM,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;MAC3C,IAAI,CAACC,OAAO,EAAE,MAAM,IAAIO,KAAK,CAAC,iCAAiC,CAAC;MAChE,MAAMsC,MAAM,GAAG,CAAC1E,CAAC,GAAGN,GAAG,MAAMA,GAAG,CAAC,CAAC;MAClC,MAAMiF,aAAa,GAAG,CAACJ,QAAQ,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACH,MAAM,IAAIpE,CAAC,KAAKR,GAAG,IAAImF,aAAa;QACvC;QACA,MAAM,IAAIvC,KAAK,CAAC,0BAA0B,CAAC;MAC7C,IAAIuC,aAAa,KAAKD,MAAM,EAAE1E,CAAC,GAAGwB,IAAI,CAAC,CAACxB,CAAC,CAAC,CAAC,CAAC;MAC5C,OAAO2C,KAAK,CAACsB,UAAU,CAAC;QAAEjE,CAAC;QAAEC;MAAC,CAAE,CAAC;IACnC;IACA,OAAO2E,OAAOA,CAACT,KAAiB,EAAEC,MAAM,GAAG,KAAK;MAC9C,OAAOzB,KAAK,CAACuB,SAAS,CAACrF,WAAW,CAAC,OAAO,EAAEsF,KAAK,CAAC,EAAEC,MAAM,CAAC;IAC7D;IAEA,IAAIpE,CAACA,CAAA;MACH,OAAO,IAAI,CAAC6E,QAAQ,EAAE,CAAC7E,CAAC;IAC1B;IACA,IAAIC,CAACA,CAAA;MACH,OAAO,IAAI,CAAC4E,QAAQ,EAAE,CAAC5E,CAAC;IAC1B;IAEA6E,UAAUA,CAACC,UAAA,GAAqB,CAAC,EAAEC,MAAM,GAAG,IAAI;MAC9CC,IAAI,CAACC,WAAW,CAAC,IAAI,EAAEH,UAAU,CAAC;MAClC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACG,QAAQ,CAACxF,GAAG,CAAC,CAAC,CAAC;MACjC,OAAO,IAAI;IACb;IAEA;IACAyF,cAAcA,CAAA;MACZ/B,eAAe,CAAC,IAAI,CAAC;IACvB;IAEA;IACAgC,MAAMA,CAAC3C,KAAY;MACjBD,SAAS,CAACC,KAAK,CAAC;MAChB,MAAM;QAAEK,CAAC,EAAEuC,EAAE;QAAEtC,CAAC,EAAEuC,EAAE;QAAEtC,CAAC,EAAEuC;MAAE,CAAE,GAAG,IAAI;MACpC,MAAM;QAAEzC,CAAC,EAAEQ,EAAE;QAAEP,CAAC,EAAEQ,EAAE;QAAEP,CAAC,EAAEQ;MAAE,CAAE,GAAGf,KAAK;MACrC,MAAM+C,IAAI,GAAGjE,IAAI,CAAC8D,EAAE,GAAG7B,EAAE,CAAC;MAC1B,MAAMiC,IAAI,GAAGlE,IAAI,CAAC+B,EAAE,GAAGiC,EAAE,CAAC;MAC1B,MAAMG,IAAI,GAAGnE,IAAI,CAAC+D,EAAE,GAAG9B,EAAE,CAAC;MAC1B,MAAMmC,IAAI,GAAGpE,IAAI,CAACgC,EAAE,GAAGgC,EAAE,CAAC;MAC1B,OAAOC,IAAI,KAAKC,IAAI,IAAIC,IAAI,KAAKC,IAAI;IACvC;IAEA1C,GAAGA,CAAA;MACD,OAAO,IAAI,CAACmC,MAAM,CAAC1C,KAAK,CAACkD,IAAI,CAAC;IAChC;IAEAC,MAAMA,CAAA;MACJ;MACA,OAAO,IAAInD,KAAK,CAACnB,IAAI,CAAC,CAAC,IAAI,CAACuB,CAAC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAEzB,IAAI,CAAC,CAAC,IAAI,CAAC8B,CAAC,CAAC,CAAC;IAChE;IAEA;IACA;IACA;IACAyC,MAAMA,CAAA;MACJ,MAAM;QAAEvF;MAAC,CAAE,GAAGT,KAAK;MACnB,MAAM;QAAEgD,CAAC,EAAEuC,EAAE;QAAEtC,CAAC,EAAEuC,EAAE;QAAEtC,CAAC,EAAEuC;MAAE,CAAE,GAAG,IAAI;MACpC,MAAMQ,CAAC,GAAGxE,IAAI,CAAC8D,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;MACzB,MAAMW,CAAC,GAAGzE,IAAI,CAAC+D,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;MACzB,MAAMW,CAAC,GAAG1E,IAAI,CAAC7B,GAAG,GAAG6B,IAAI,CAACgE,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC;MACrC,MAAMW,CAAC,GAAG3E,IAAI,CAAChB,CAAC,GAAGwF,CAAC,CAAC,CAAC,CAAC;MACvB,MAAMI,IAAI,GAAGd,EAAE,GAAGC,EAAE;MACpB,MAAMc,CAAC,GAAG7E,IAAI,CAACA,IAAI,CAAC4E,IAAI,GAAGA,IAAI,CAAC,GAAGJ,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;MAC3C,MAAMK,CAAC,GAAGH,CAAC,GAAGF,CAAC,CAAC,CAAC;MACjB,MAAMM,CAAC,GAAGD,CAAC,GAAGJ,CAAC,CAAC,CAAC;MACjB,MAAMM,CAAC,GAAGL,CAAC,GAAGF,CAAC,CAAC,CAAC;MACjB,MAAMQ,EAAE,GAAGjF,IAAI,CAAC6E,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMG,EAAE,GAAGlF,IAAI,CAAC8E,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMG,EAAE,GAAGnF,IAAI,CAAC6E,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMI,EAAE,GAAGpF,IAAI,CAAC+E,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;MACxB,OAAO,IAAI3D,KAAK,CAAC8D,EAAE,EAAEC,EAAE,EAAEE,EAAE,EAAED,EAAE,CAAC;IAClC;IAEA;IACA;IACA;IACArG,GAAGA,CAACoC,KAAY;MACdD,SAAS,CAACC,KAAK,CAAC;MAChB,MAAM;QAAElC,CAAC;QAAEG;MAAC,CAAE,GAAGZ,KAAK;MACtB,MAAM;QAAEgD,CAAC,EAAEuC,EAAE;QAAEtC,CAAC,EAAEuC,EAAE;QAAEtC,CAAC,EAAEuC,EAAE;QAAElC,CAAC,EAAEuD;MAAE,CAAE,GAAG,IAAI;MAC3C,MAAM;QAAE9D,CAAC,EAAEQ,EAAE;QAAEP,CAAC,EAAEQ,EAAE;QAAEP,CAAC,EAAEQ,EAAE;QAAEH,CAAC,EAAEwD;MAAE,CAAE,GAAGpE,KAAK;MAC5C,MAAMsD,CAAC,GAAGxE,IAAI,CAAC8D,EAAE,GAAG/B,EAAE,CAAC,CAAC,CAAC;MACzB,MAAM0C,CAAC,GAAGzE,IAAI,CAAC+D,EAAE,GAAG/B,EAAE,CAAC,CAAC,CAAC;MACzB,MAAM0C,CAAC,GAAG1E,IAAI,CAACqF,EAAE,GAAGlG,CAAC,GAAGmG,EAAE,CAAC,CAAC,CAAC;MAC7B,MAAMX,CAAC,GAAG3E,IAAI,CAACgE,EAAE,GAAG/B,EAAE,CAAC,CAAC,CAAC;MACzB,MAAM4C,CAAC,GAAG7E,IAAI,CAAC,CAAC8D,EAAE,GAAGC,EAAE,KAAKhC,EAAE,GAAGC,EAAE,CAAC,GAAGwC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;MAC/C,MAAMM,CAAC,GAAGJ,CAAC,GAAGD,CAAC,CAAC,CAAC;MACjB,MAAMI,CAAC,GAAGH,CAAC,GAAGD,CAAC,CAAC,CAAC;MACjB,MAAMM,CAAC,GAAGhF,IAAI,CAACyE,CAAC,GAAGzF,CAAC,GAAGwF,CAAC,CAAC,CAAC,CAAC;MAC3B,MAAMS,EAAE,GAAGjF,IAAI,CAAC6E,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMG,EAAE,GAAGlF,IAAI,CAAC8E,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMG,EAAE,GAAGnF,IAAI,CAAC6E,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMI,EAAE,GAAGpF,IAAI,CAAC+E,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;MACxB,OAAO,IAAI3D,KAAK,CAAC8D,EAAE,EAAEC,EAAE,EAAEE,EAAE,EAAED,EAAE,CAAC;IAClC;IAEAI,QAAQA,CAACrE,KAAY;MACnB,OAAO,IAAI,CAACpC,GAAG,CAACoC,KAAK,CAACoD,MAAM,EAAE,CAAC;IACjC;IAEA;IACAX,QAAQA,CAAC6B,MAAc;MACrB;MACA,IAAI,CAAC9F,EAAE,CAAC+F,WAAW,CAACD,MAAM,CAAC,EAAE,MAAM,IAAI5E,KAAK,CAAC,4CAA4C,CAAC;MAC1F,MAAM;QAAES,CAAC;QAAEqE;MAAC,CAAE,GAAGjC,IAAI,CAACkC,MAAM,CAAC,IAAI,EAAEH,MAAM,EAAGnE,CAAC,IAAKzD,UAAU,CAACuD,KAAK,EAAEE,CAAC,CAAC,CAAC;MACvE,OAAOzD,UAAU,CAACuD,KAAK,EAAE,CAACE,CAAC,EAAEqE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;IAEA;IACA;IACA;IACA;IACA;IACAE,cAAcA,CAACJ,MAAc,EAAEK,GAAG,GAAG1E,KAAK,CAACkD,IAAI;MAC7C;MACA,IAAI,CAAC3E,EAAE,CAACW,OAAO,CAACmF,MAAM,CAAC,EAAE,MAAM,IAAI5E,KAAK,CAAC,4CAA4C,CAAC;MACtF,IAAI4E,MAAM,KAAKxH,GAAG,EAAE,OAAOmD,KAAK,CAACkD,IAAI;MACrC,IAAI,IAAI,CAAC3C,GAAG,EAAE,IAAI8D,MAAM,KAAKtH,GAAG,EAAE,OAAO,IAAI;MAC7C,OAAOuF,IAAI,CAACqC,MAAM,CAAC,IAAI,EAAEN,MAAM,EAAGnE,CAAC,IAAKzD,UAAU,CAACuD,KAAK,EAAEE,CAAC,CAAC,EAAEwE,GAAG,CAAC;IACpE;IAEA;IACA;IACA;IACA;IACAE,YAAYA,CAAA;MACV,OAAO,IAAI,CAACH,cAAc,CAAChG,QAAQ,CAAC,CAAC8B,GAAG,EAAE;IAC5C;IAEA;IACA;IACAsE,aAAaA,CAAA;MACX,OAAOvC,IAAI,CAACqC,MAAM,CAAC,IAAI,EAAEvH,KAAK,CAAC0B,CAAC,CAAC,CAACyB,GAAG,EAAE;IACzC;IAEA;IACA;IACA2B,QAAQA,CAAC4C,SAAkB;MACzB,OAAO7E,YAAY,CAAC,IAAI,EAAE6E,SAAS,CAAC;IACtC;IAEAC,aAAaA,CAAA;MACX,IAAItG,QAAQ,KAAK1B,GAAG,EAAE,OAAO,IAAI;MACjC,OAAO,IAAI,CAAC0H,cAAc,CAAChG,QAAQ,CAAC;IACtC;IAEAuG,OAAOA,CAAA;MACL,MAAM;QAAE3H,CAAC;QAAEC;MAAC,CAAE,GAAG,IAAI,CAAC4E,QAAQ,EAAE;MAChC;MACA,MAAMV,KAAK,GAAGrE,EAAE,CAAC6H,OAAO,CAAC1H,CAAC,CAAC;MAC3B;MACA;MACAkE,KAAK,CAACA,KAAK,CAACyD,MAAM,GAAG,CAAC,CAAC,IAAI5H,CAAC,GAAGN,GAAG,GAAG,IAAI,GAAG,CAAC;MAC7C,OAAOyE,KAAK;IACd;IACA0D,KAAKA,CAAA;MACH,OAAOpJ,UAAU,CAAC,IAAI,CAACkJ,OAAO,EAAE,CAAC;IACnC;IAEAG,QAAQA,CAAA;MACN,OAAO,UAAU,IAAI,CAAC5E,GAAG,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC2E,KAAK,EAAE,GAAG;IACxD;IAEA;IACA,IAAIE,EAAEA,CAAA;MACJ,OAAO,IAAI,CAAChF,CAAC;IACf;IACA,IAAIiF,EAAEA,CAAA;MACJ,OAAO,IAAI,CAAChF,CAAC;IACf;IACA,IAAIiF,EAAEA,CAAA;MACJ,OAAO,IAAI,CAAChF,CAAC;IACf;IACA,IAAIiF,EAAEA,CAAA;MACJ,OAAO,IAAI,CAAC5E,CAAC;IACf;IACA,OAAOlE,UAAUA,CAAC+I,MAAe;MAC/B,OAAO/I,UAAU,CAACuD,KAAK,EAAEwF,MAAM,CAAC;IAClC;IACA,OAAOC,GAAGA,CAACD,MAAe,EAAEE,OAAiB;MAC3C,OAAOhJ,SAAS,CAACsD,KAAK,EAAEzB,EAAE,EAAEiH,MAAM,EAAEE,OAAO,CAAC;IAC9C;IACAC,cAAcA,CAACvD,UAAkB;MAC/B,IAAI,CAACD,UAAU,CAACC,UAAU,CAAC;IAC7B;IACAwD,UAAUA,CAAA;MACR,OAAO,IAAI,CAACZ,OAAO,EAAE;IACvB;;EAtPA;EACgBhF,KAAA,CAAA6F,IAAI,GAAG,IAAI7F,KAAK,CAAC5C,KAAK,CAACmC,EAAE,EAAEnC,KAAK,CAACoC,EAAE,EAAEzC,GAAG,EAAE8B,IAAI,CAACzB,KAAK,CAACmC,EAAE,GAAGnC,KAAK,CAACoC,EAAE,CAAC,CAAC;EACpF;EACgBQ,KAAA,CAAAkD,IAAI,GAAG,IAAIlD,KAAK,CAACnD,GAAG,EAAEE,GAAG,EAAEA,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;EACtD;EACgBmD,KAAA,CAAA7C,EAAE,GAAGA,EAAE;EACvB;EACgB6C,KAAA,CAAAzB,EAAE,GAAGA,EAAE;EAiPzB,MAAM+D,IAAI,GAAG,IAAI3F,IAAI,CAACqD,KAAK,EAAEzB,EAAE,CAACuH,IAAI,CAAC;EACrC9F,KAAK,CAAC6F,IAAI,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,OAAOnC,KAAK;AACd;AAEA;;;;;AAKA,OAAM,MAAgB+F,iBAAiB;EAUrC5E,YAAY6E,EAAgB;IAC1B,IAAI,CAACA,EAAE,GAAGA,EAAE;EACd;EAMA;EACA,OAAOzE,SAASA,CAAC0E,MAAkB;IACjC5J,cAAc,EAAE;EAClB;EAEA,OAAO4F,OAAOA,CAACiE,IAAS;IACtB7J,cAAc,EAAE;EAClB;EAEA,IAAIgB,CAACA,CAAA;IACH,OAAO,IAAI,CAAC6E,QAAQ,EAAE,CAAC7E,CAAC;EAC1B;EACA,IAAIC,CAACA,CAAA;IACH,OAAO,IAAI,CAAC4E,QAAQ,EAAE,CAAC5E,CAAC;EAC1B;EAEA;EACAyH,aAAaA,CAAA;IACX;IACA,OAAO,IAAW;EACpB;EAEAtC,cAAcA,CAAA;IACZ,IAAI,CAACuD,EAAE,CAACvD,cAAc,EAAE;EAC1B;EAEAP,QAAQA,CAAC4C,SAAkB;IACzB,OAAO,IAAI,CAACkB,EAAE,CAAC9D,QAAQ,CAAC4C,SAAS,CAAC;EACpC;EAEAI,KAAKA,CAAA;IACH,OAAOpJ,UAAU,CAAC,IAAI,CAACkJ,OAAO,EAAE,CAAC;EACnC;EAEAG,QAAQA,CAAA;IACN,OAAO,IAAI,CAACD,KAAK,EAAE;EACrB;EAEAL,aAAaA,CAAA;IACX,OAAO,IAAI;EACb;EAEAD,YAAYA,CAAA;IACV,OAAO,KAAK;EACd;EAEAjH,GAAGA,CAACoC,KAAQ;IACV,IAAI,CAACoG,UAAU,CAACpG,KAAK,CAAC;IACtB,OAAO,IAAI,CAACqG,IAAI,CAAC,IAAI,CAACJ,EAAE,CAACrI,GAAG,CAACoC,KAAK,CAACiG,EAAE,CAAC,CAAC;EACzC;EAEA5B,QAAQA,CAACrE,KAAQ;IACf,IAAI,CAACoG,UAAU,CAACpG,KAAK,CAAC;IACtB,OAAO,IAAI,CAACqG,IAAI,CAAC,IAAI,CAACJ,EAAE,CAAC5B,QAAQ,CAACrE,KAAK,CAACiG,EAAE,CAAC,CAAC;EAC9C;EAEAxD,QAAQA,CAAC6B,MAAc;IACrB,OAAO,IAAI,CAAC+B,IAAI,CAAC,IAAI,CAACJ,EAAE,CAACxD,QAAQ,CAAC6B,MAAM,CAAC,CAAC;EAC5C;EAEAI,cAAcA,CAACJ,MAAc;IAC3B,OAAO,IAAI,CAAC+B,IAAI,CAAC,IAAI,CAACJ,EAAE,CAACvB,cAAc,CAACJ,MAAM,CAAC,CAAC;EAClD;EAEAjB,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACgD,IAAI,CAAC,IAAI,CAACJ,EAAE,CAAC5C,MAAM,EAAE,CAAC;EACpC;EAEAD,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACiD,IAAI,CAAC,IAAI,CAACJ,EAAE,CAAC7C,MAAM,EAAE,CAAC;EACpC;EAEAhB,UAAUA,CAACC,UAAmB,EAAEC,MAAgB;IAC9C,OAAO,IAAI,CAAC+D,IAAI,CAAC,IAAI,CAACJ,EAAE,CAAC7D,UAAU,CAACC,UAAU,EAAEC,MAAM,CAAC,CAAC;EAC1D;EAOA;EACAuD,UAAUA,CAAA;IACR,OAAO,IAAI,CAACZ,OAAO,EAAE;EACvB;;AAGF;;;AAGA,OAAM,SAAUqB,KAAKA,CAACrG,KAAuB,EAAEsG,KAAY,EAAEC,SAAA,GAAuB,EAAE;EACpF,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE,MAAM,IAAI7G,KAAK,CAAC,mCAAmC,CAAC;EACrFjE,eAAe,CACb+K,SAAS,EACT,EAAE,EACF;IACEC,iBAAiB,EAAE,UAAU;IAC7BlK,WAAW,EAAE,UAAU;IACvBmK,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE;GACb,CACF;EAED,MAAM;IAAED;EAAO,CAAE,GAAGH,SAAS;EAC7B,MAAM;IAAEV,IAAI;IAAE1I,EAAE;IAAEoB;EAAE,CAAE,GAAGyB,KAAK;EAE9B,MAAM1D,WAAW,GAAGiK,SAAS,CAACjK,WAAW,IAAIC,cAAc;EAC3D,MAAMiK,iBAAiB,GAAGD,SAAS,CAACC,iBAAiB,KAAMhF,KAAiB,IAAKA,KAAK,CAAC;EACvF,MAAMiF,MAAM,GACVF,SAAS,CAACE,MAAM,KACf,CAACG,IAAgB,EAAEC,GAAe,EAAEC,MAAe,KAAI;IACtDpL,KAAK,CAACoL,MAAM,EAAE,QAAQ,CAAC;IACvB,IAAID,GAAG,CAAC5B,MAAM,IAAI6B,MAAM,EAAE,MAAM,IAAIrH,KAAK,CAAC,qCAAqC,CAAC;IAChF,OAAOmH,IAAI;EACb,CAAC,CAAC,CAAC,CAAC;EAEN;EACA,SAASG,OAAOA,CAACC,IAAgB;IAC/B,OAAOzI,EAAE,CAACQ,MAAM,CAAChD,eAAe,CAACiL,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3C;EAEA;EACA,SAASC,gBAAgBA,CAACC,GAAQ;IAChC,MAAMxF,GAAG,GAAGyF,OAAO,CAACC,SAAS;IAC7BF,GAAG,GAAGhL,WAAW,CAAC,aAAa,EAAEgL,GAAG,EAAExF,GAAG,CAAC;IAC1C;IACA;IACA,MAAM2F,MAAM,GAAGnL,WAAW,CAAC,oBAAoB,EAAEoK,KAAK,CAACY,GAAG,CAAC,EAAE,CAAC,GAAGxF,GAAG,CAAC;IACrE,MAAM4F,IAAI,GAAGd,iBAAiB,CAACa,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE7F,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,MAAM8F,MAAM,GAAGH,MAAM,CAACE,KAAK,CAAC7F,GAAG,EAAE,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAM2C,MAAM,GAAG0C,OAAO,CAACO,IAAI,CAAC,CAAC,CAAC;IAC9B,OAAO;MAAEA,IAAI;MAAEE,MAAM;MAAEnD;IAAM,CAAE;EACjC;EAEA;EACA,SAASoD,oBAAoBA,CAACL,SAAc;IAC1C,MAAM;MAAEE,IAAI;MAAEE,MAAM;MAAEnD;IAAM,CAAE,GAAG4C,gBAAgB,CAACG,SAAS,CAAC;IAC5D,MAAMM,KAAK,GAAG7B,IAAI,CAACrD,QAAQ,CAAC6B,MAAM,CAAC,CAAC,CAAC;IACrC,MAAMsD,UAAU,GAAGD,KAAK,CAAC1C,OAAO,EAAE;IAClC,OAAO;MAAEsC,IAAI;MAAEE,MAAM;MAAEnD,MAAM;MAAEqD,KAAK;MAAEC;IAAU,CAAE;EACpD;EAEA;EACA,SAASC,YAAYA,CAACR,SAAc;IAClC,OAAOK,oBAAoB,CAACL,SAAS,CAAC,CAACO,UAAU;EACnD;EAEA;EACA,SAASE,kBAAkBA,CAACC,OAAA,GAAeC,UAAU,CAACC,EAAE,EAAE,EAAE,GAAGC,IAAkB;IAC/E,MAAMC,GAAG,GAAGlM,WAAW,CAAC,GAAGiM,IAAI,CAAC;IAChC,OAAOlB,OAAO,CAACT,KAAK,CAACG,MAAM,CAACyB,GAAG,EAAEhM,WAAW,CAAC,SAAS,EAAE4L,OAAO,CAAC,EAAE,CAAC,CAACpB,OAAO,CAAC,CAAC,CAAC;EAChF;EAEA;EACA,SAASyB,IAAIA,CAACD,GAAQ,EAAEd,SAAc,EAAEgB,OAAA,GAA6B,EAAE;IACrEF,GAAG,GAAGhM,WAAW,CAAC,SAAS,EAAEgM,GAAG,CAAC;IACjC,IAAIxB,OAAO,EAAEwB,GAAG,GAAGxB,OAAO,CAACwB,GAAG,CAAC,CAAC,CAAC;IACjC,MAAM;MAAEV,MAAM;MAAEnD,MAAM;MAAEsD;IAAU,CAAE,GAAGF,oBAAoB,CAACL,SAAS,CAAC;IACtE,MAAMiB,CAAC,GAAGR,kBAAkB,CAACO,OAAO,CAACN,OAAO,EAAEN,MAAM,EAAEU,GAAG,CAAC,CAAC,CAAC;IAC5D,MAAMI,CAAC,GAAGzC,IAAI,CAACrD,QAAQ,CAAC6F,CAAC,CAAC,CAACrD,OAAO,EAAE,CAAC,CAAC;IACtC,MAAMuD,CAAC,GAAGV,kBAAkB,CAACO,OAAO,CAACN,OAAO,EAAEQ,CAAC,EAAEX,UAAU,EAAEO,GAAG,CAAC,CAAC,CAAC;IACnE,MAAMM,CAAC,GAAGjK,EAAE,CAACQ,MAAM,CAACsJ,CAAC,GAAGE,CAAC,GAAGlE,MAAM,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC9F,EAAE,CAACW,OAAO,CAACsJ,CAAC,CAAC,EAAE,MAAM,IAAI/I,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;IAC/D,MAAMgJ,EAAE,GAAGzM,WAAW,CAACsM,CAAC,EAAE/J,EAAE,CAACyG,OAAO,CAACwD,CAAC,CAAC,CAAC;IACxC,OAAO5M,MAAM,CAAC6M,EAAE,EAAEtB,OAAO,CAACuB,SAAS,EAAE,QAAQ,CAAC;EAChD;EAEA;EACA,MAAMC,UAAU,GAAwC;IAAElH,MAAM,EAAE;EAAI,CAAE;EAExE;;;;EAIA,SAASmH,MAAMA,CAACC,GAAQ,EAAEX,GAAQ,EAAEY,SAAc,EAAEV,OAAO,GAAGO,UAAU;IACtE,MAAM;MAAEb,OAAO;MAAErG;IAAM,CAAE,GAAG2G,OAAO;IACnC,MAAM1G,GAAG,GAAGyF,OAAO,CAACuB,SAAS;IAC7BG,GAAG,GAAG3M,WAAW,CAAC,WAAW,EAAE2M,GAAG,EAAEnH,GAAG,CAAC;IACxCwG,GAAG,GAAGhM,WAAW,CAAC,SAAS,EAAEgM,GAAG,CAAC;IACjCY,SAAS,GAAG5M,WAAW,CAAC,WAAW,EAAE4M,SAAS,EAAE3B,OAAO,CAAC2B,SAAS,CAAC;IAClE,IAAIrH,MAAM,KAAKsH,SAAS,EAAErN,KAAK,CAAC+F,MAAM,EAAE,QAAQ,CAAC;IACjD,IAAIiF,OAAO,EAAEwB,GAAG,GAAGxB,OAAO,CAACwB,GAAG,CAAC,CAAC,CAAC;IAEjC,MAAMc,GAAG,GAAGtH,GAAG,GAAG,CAAC;IACnB,MAAM2G,CAAC,GAAGQ,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAED,GAAG,CAAC;IAC9B,MAAMR,CAAC,GAAGzM,eAAe,CAAC8M,GAAG,CAACI,QAAQ,CAACD,GAAG,EAAEtH,GAAG,CAAC,CAAC;IACjD,IAAI2B,CAAC,EAAEiF,CAAC,EAAEY,EAAE;IACZ,IAAI;MACF;MACA;MACA;MACA7F,CAAC,GAAGrD,KAAK,CAACuB,SAAS,CAACuH,SAAS,EAAErH,MAAM,CAAC;MACtC6G,CAAC,GAAGtI,KAAK,CAACuB,SAAS,CAAC8G,CAAC,EAAE5G,MAAM,CAAC;MAC9ByH,EAAE,GAAGrD,IAAI,CAACpB,cAAc,CAAC+D,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd,OAAO,KAAK;IACd;IACA,IAAI,CAAC1H,MAAM,IAAI4B,CAAC,CAACuB,YAAY,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC;IAE/C,MAAM2D,CAAC,GAAGV,kBAAkB,CAACC,OAAO,EAAEQ,CAAC,CAACtD,OAAO,EAAE,EAAE3B,CAAC,CAAC2B,OAAO,EAAE,EAAEkD,GAAG,CAAC;IACpE,MAAMkB,GAAG,GAAGd,CAAC,CAAC3K,GAAG,CAAC0F,CAAC,CAACoB,cAAc,CAAC8D,CAAC,CAAC,CAAC;IACtC;IACA;IACA,OAAOa,GAAG,CAAChF,QAAQ,CAAC8E,EAAE,CAAC,CAACnE,aAAa,EAAE,CAACxE,GAAG,EAAE;EAC/C;EAEA,MAAM8I,KAAK,GAAGlM,EAAE,CAACyB,KAAK,CAAC,CAAC;EACxB,MAAMuI,OAAO,GAAG;IACdC,SAAS,EAAEiC,KAAK;IAChBP,SAAS,EAAEO,KAAK;IAChBX,SAAS,EAAE,CAAC,GAAGW,KAAK;IACpBC,IAAI,EAAED;GACP;EACD,SAASE,eAAeA,CAACD,IAAI,GAAGhN,WAAW,CAAC6K,OAAO,CAACmC,IAAI,CAAC;IACvD,OAAO1N,MAAM,CAAC0N,IAAI,EAAEnC,OAAO,CAACmC,IAAI,EAAE,MAAM,CAAC;EAC3C;EACA,SAASE,MAAMA,CAACF,IAAiB;IAC/B,MAAMlC,SAAS,GAAGqC,KAAK,CAACF,eAAe,CAACD,IAAI,CAAC;IAC7C,OAAO;MAAElC,SAAS;MAAE0B,SAAS,EAAElB,YAAY,CAACR,SAAS;IAAC,CAAE;EAC1D;EACA,SAASsC,gBAAgBA,CAACxC,GAAe;IACvC,OAAO/K,OAAO,CAAC+K,GAAG,CAAC,IAAIA,GAAG,CAACjC,MAAM,KAAK1G,EAAE,CAACK,KAAK;EAChD;EACA,SAAS+K,gBAAgBA,CAACzC,GAAe,EAAEzF,MAAgB;IACzD,IAAI;MACF,OAAO,CAAC,CAACzB,KAAK,CAACuB,SAAS,CAAC2F,GAAG,EAAEzF,MAAM,CAAC;IACvC,CAAC,CAAC,OAAO0H,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;EAEA,MAAMM,KAAK,GAAG;IACZhC,oBAAoB;IACpB8B,eAAe;IACfG,gBAAgB;IAChBC,gBAAgB;IAChB;;;;;;;;;IASAC,YAAYA,CAACd,SAAqB;MAChC,MAAM;QAAExL;MAAC,CAAE,GAAG0C,KAAK,CAACuB,SAAS,CAACuH,SAAS,CAAC;MACxC,MAAMe,IAAI,GAAG1C,OAAO,CAAC2B,SAAS;MAC9B,MAAMgB,OAAO,GAAGD,IAAI,KAAK,EAAE;MAC3B,IAAI,CAACC,OAAO,IAAID,IAAI,KAAK,EAAE,EAAE,MAAM,IAAIpK,KAAK,CAAC,gCAAgC,CAAC;MAC9E,MAAMT,CAAC,GAAG8K,OAAO,GAAG3M,EAAE,CAACkC,GAAG,CAACtC,GAAG,GAAGO,CAAC,EAAEP,GAAG,GAAGO,CAAC,CAAC,GAAGH,EAAE,CAACkC,GAAG,CAAC/B,CAAC,GAAGP,GAAG,EAAEO,CAAC,GAAGP,GAAG,CAAC;MACvE,OAAOI,EAAE,CAAC6H,OAAO,CAAChG,CAAC,CAAC;IACtB,CAAC;IAED+K,kBAAkBA,CAAC3C,SAAqB;MACtC,MAAMyC,IAAI,GAAG1C,OAAO,CAACC,SAAS;MAC9BxL,MAAM,CAACwL,SAAS,EAAEyC,IAAI,CAAC;MACvB,MAAMxC,MAAM,GAAGf,KAAK,CAACc,SAAS,CAAC6B,QAAQ,CAAC,CAAC,EAAEY,IAAI,CAAC,CAAC;MACjD,OAAOrD,iBAAiB,CAACa,MAAM,CAAC,CAAC4B,QAAQ,CAAC,CAAC,EAAEY,IAAI,CAAC;IACpD,CAAC;IAED;IACAG,gBAAgB,EAAET,eAAe;IACjC;IACApH,UAAUA,CAACC,UAAU,GAAG,CAAC,EAAEsF,KAAA,GAAsB1H,KAAK,CAAC6F,IAAI;MACzD,OAAO6B,KAAK,CAACvF,UAAU,CAACC,UAAU,EAAE,KAAK,CAAC;IAC5C;GACD;EAED,OAAOhB,MAAM,CAACC,MAAM,CAAC;IACnBmI,MAAM;IACN5B,YAAY;IACZO,IAAI;IACJS,MAAM;IACNa,KAAK;IACLzJ,KAAK;IACLmH;GACD,CAAC;AACJ;AAmCA,SAAS8C,yBAAyBA,CAACC,CAAsB;EACvD,MAAM9M,KAAK,GAAgB;IACzBS,CAAC,EAAEqM,CAAC,CAACrM,CAAC;IACNG,CAAC,EAAEkM,CAAC,CAAClM,CAAC;IACNkC,CAAC,EAAEgK,CAAC,CAAC/M,EAAE,CAAC2E,KAAK;IACbhD,CAAC,EAAEoL,CAAC,CAACpL,CAAC;IACNN,CAAC,EAAE0L,CAAC,CAAC1L,CAAC;IACNe,EAAE,EAAE2K,CAAC,CAAC3K,EAAE;IACRC,EAAE,EAAE0K,CAAC,CAAC1K;GACP;EACD,MAAMrC,EAAE,GAAG+M,CAAC,CAAC/M,EAAE;EACf,MAAMoB,EAAE,GAAG3B,KAAK,CAACQ,KAAK,CAAC0B,CAAC,EAAEoL,CAAC,CAACC,UAAU,EAAE,IAAI,CAAC;EAC7C,MAAMC,SAAS,GAAqB;IAAEjN,EAAE;IAAEoB,EAAE;IAAEG,OAAO,EAAEwL,CAAC,CAACxL;EAAO,CAAE;EAClE,MAAM6H,SAAS,GAAc;IAC3BjK,WAAW,EAAE4N,CAAC,CAAC5N,WAAW;IAC1BkK,iBAAiB,EAAE0D,CAAC,CAAC1D,iBAAiB;IACtCC,MAAM,EAAEyD,CAAC,CAACzD,MAAM;IAChBC,OAAO,EAAEwD,CAAC,CAACxD,OAAO;IAClBC,UAAU,EAAEuD,CAAC,CAACvD;GACf;EACD,OAAO;IAAEvJ,KAAK;IAAEgN,SAAS;IAAEpD,IAAI,EAAEkD,CAAC,CAAClD,IAAI;IAAET;EAAS,CAAE;AACtD;AACA,SAAS8D,2BAA2BA,CAACH,CAAsB,EAAE7D,KAAY;EACvE,MAAMrG,KAAK,GAAGqG,KAAK,CAACrG,KAAK;EACzB,MAAMsK,MAAM,GAAGlJ,MAAM,CAACmJ,MAAM,CAAC,EAAE,EAAElE,KAAK,EAAE;IACtCmE,aAAa,EAAExK,KAAK;IACpB5C,KAAK,EAAE8M,CAAC;IACRC,UAAU,EAAEnK,KAAK,CAACzB,EAAE,CAACuH,IAAI;IACzB2E,WAAW,EAAEzK,KAAK,CAACzB,EAAE,CAACK;GACvB,CAAC;EACF,OAAO0L,MAAM;AACf;AACA;AACA,OAAM,SAAUI,cAAcA,CAACR,CAAsB;EACnD,MAAM;IAAE9M,KAAK;IAAEgN,SAAS;IAAEpD,IAAI;IAAET;EAAS,CAAE,GAAG0D,yBAAyB,CAACC,CAAC,CAAC;EAC1E,MAAMlK,KAAK,GAAG9B,OAAO,CAACd,KAAK,EAAEgN,SAAS,CAAC;EACvC,MAAMO,KAAK,GAAGtE,KAAK,CAACrG,KAAK,EAAEgH,IAAI,EAAET,SAAS,CAAC;EAC3C,OAAO8D,2BAA2B,CAACH,CAAC,EAAES,KAAK,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}