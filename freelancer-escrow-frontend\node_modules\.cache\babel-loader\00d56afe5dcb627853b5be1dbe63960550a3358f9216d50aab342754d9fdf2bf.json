{"ast": null, "code": "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n/** Checks if something is Uint8Array. Be careful: nodej<PERSON> <PERSON>uffer will return true. */\nexport function isBytes(a) {\n  return a instanceof Uint8Array || ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array';\n}\n/** Asserts something is positive integer. */\nexport function anumber(n) {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nexport function abytes(b, ...lengths) {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length)) throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nexport function ahash(h) {\n  if (typeof h !== 'function' || typeof h.create !== 'function') throw new Error('Hash should be wrapped by utils.createHasher');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nexport function aexists(instance, checkFinished = true) {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nexport function aoutput(out, instance) {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n/** Cast u8 / u16 / u32 to u8. */\nexport function u8(arr) {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nexport function u32(arr) {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nexport function clean(...arrays) {\n  for (let i = 0; i < arrays.length; i++) {\n    arrays[i].fill(0);\n  }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nexport function createView(arr) {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word, shift) {\n  return word << 32 - shift | word >>> shift;\n}\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word, shift) {\n  return word << shift | word >>> 32 - shift >>> 0;\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE = /* @__PURE__ */(() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nexport function byteSwap(word) {\n  return word << 24 & 0xff000000 | word << 8 & 0xff0000 | word >>> 8 & 0xff00 | word >>> 24 & 0xff;\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const swap8IfBE = isLE ? n => n : n => byteSwap(n);\n/** @deprecated */\nexport const byteSwapIfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr) {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n  return arr;\n}\nexport const swap32IfBE = isLE ? u => u : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */(() =>\n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */Array.from({\n  length: 256\n}, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n  abytes(bytes);\n  // @ts-ignore\n  if (hasHexBuiltin) return bytes.toHex();\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = {\n  _0: 48,\n  _9: 57,\n  A: 65,\n  F: 70,\n  a: 97,\n  f: 102\n};\nfunction asciiToBase16(ch) {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // @ts-ignore\n  if (hasHexBuiltin) return Uint8Array.fromHex(hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async () => {};\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(iters, tick, cb) {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes) {\n  return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nexport function kdfInputToBytes(data) {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n/** Copies several Uint8Arrays into one. */\nexport function concatBytes(...arrays) {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\nexport function checkOpts(defaults, opts) {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]') throw new Error('options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged;\n}\n/** For runtime check if class implements interface */\nexport class Hash {}\n/** Wraps hash function, creating an interface on top of it */\nexport function createHasher(hashCons) {\n  const hashC = msg => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\nexport function createOptHasher(hashCons) {\n  const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({});\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = opts => hashCons(opts);\n  return hashC;\n}\nexport function createXOFer(hashCons) {\n  const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({});\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = opts => hashCons(opts);\n  return hashC;\n}\nexport const wrapConstructor = createHasher;\nexport const wrapConstructorWithOpts = createOptHasher;\nexport const wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32) {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return Uint8Array.from(crypto.randomBytes(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}", "map": {"version": 3, "names": ["crypto", "isBytes", "a", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "name", "anumber", "n", "Number", "isSafeInteger", "Error", "abytes", "b", "lengths", "length", "includes", "ahash", "h", "create", "outputLen", "blockLen", "aexists", "instance", "checkFinished", "destroyed", "finished", "aoutput", "out", "min", "u8", "arr", "buffer", "byteOffset", "byteLength", "u32", "Uint32Array", "Math", "floor", "clean", "arrays", "i", "fill", "createView", "DataView", "rotr", "word", "shift", "rotl", "isLE", "byteSwap", "swap8IfBE", "byteSwapIfBE", "byteSwap32", "swap32IfBE", "u", "hasHexBuiltin", "from", "toHex", "fromHex", "hexes", "Array", "_", "toString", "padStart", "bytesToHex", "bytes", "hex", "asciis", "_0", "_9", "A", "F", "f", "asciiToBase16", "ch", "hexToBytes", "hl", "al", "array", "ai", "hi", "n1", "charCodeAt", "n2", "undefined", "char", "nextTick", "asyncLoop", "iters", "tick", "cb", "ts", "Date", "now", "diff", "utf8ToBytes", "str", "TextEncoder", "encode", "bytesToUtf8", "TextDecoder", "decode", "toBytes", "data", "kdfInputToBytes", "concatBytes", "sum", "res", "pad", "set", "checkOpts", "defaults", "opts", "call", "merged", "Object", "assign", "Hash", "createHasher", "hashCons", "hashC", "msg", "update", "digest", "tmp", "createOptHasher", "createXOFer", "wrapConstructor", "wrapConstructorWithOpts", "wrapXOFConstructorWithOpts", "randomBytes", "bytesLength", "getRandomValues"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\utils.ts"], "sourcesContent": ["/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n/** Checks if something is Uint8Array. Be careful: nodejs <PERSON>uffer will return true. */\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n/** Asserts something is positive integer. */\nexport function anumber(n: number): void {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n\n/** Asserts something is Uint8Array. */\nexport function abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\n/** Asserts something is hash */\nexport function ahash(h: IHash): void {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.createHasher');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n\n/** Asserts a hash instance has not been destroyed / finished */\nexport function aexists(instance: any, checkFinished = true): void {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n\n/** Asserts output is properly-sized byte array */\nexport function aoutput(out: any, instance: any): void {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\n/** Generic type encompassing 8/16/32-byte arrays - but not 64-byte. */\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n/** Cast u8 / u16 / u32 to u8. */\nexport function u8(arr: TypedArray): Uint8Array {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** Cast u8 / u16 / u32 to u32. */\nexport function u32(arr: TypedArray): Uint32Array {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nexport function clean(...arrays: TypedArray[]): void {\n  for (let i = 0; i < arrays.length; i++) {\n    arrays[i].fill(0);\n  }\n}\n\n/** Create DataView of an array for easy byte-level manipulation. */\nexport function createView(arr: TypedArray): DataView {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word: number, shift: number): number {\n  return (word << (32 - shift)) | (word >>> shift);\n}\n\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word: number, shift: number): number {\n  return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE: boolean = /* @__PURE__ */ (() =>\n  new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n\n/** The byte swap operation for uint32 */\nexport function byteSwap(word: number): number {\n  return (\n    ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff)\n  );\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const swap8IfBE: (n: number) => number = isLE\n  ? (n: number) => n\n  : (n: number) => byteSwap(n);\n\n/** @deprecated */\nexport const byteSwapIfBE: typeof swap8IfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr: Uint32Array): Uint32Array {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n  return arr;\n}\n\nexport const swap32IfBE: (u: Uint32Array) => Uint32Array = isLE\n  ? (u: Uint32Array) => u\n  : byteSwap32;\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  // @ts-ignore\n  typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // @ts-ignore\n  if (hasHexBuiltin) return bytes.toHex();\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // @ts-ignore\n  if (hasHexBuiltin) return Uint8Array.fromHex(hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async (): Promise<void> => {};\n\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(\n  iters: number,\n  tick: number,\n  cb: (i: number) => void\n): Promise<void> {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols, but ts doesn't see them: https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes: Uint8Array): string {\n  return new TextDecoder().decode(bytes);\n}\n\n/** Accepted input of hash functions. Strings are converted to byte arrays. */\nexport type Input = string | Uint8Array;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** KDFs can accept string or Uint8Array for user convenience. */\nexport type KDFInput = string | Uint8Array;\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nexport function kdfInputToBytes(data: KDFInput): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** Copies several Uint8Arrays into one. */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n    throw new Error('options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\n/** Hash interface. */\nexport type IHash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\n\n/** For runtime check if class implements interface */\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  abstract clone(): T;\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\n/** Hash function */\nexport type CHash = ReturnType<typeof createHasher>;\n/** Hash function with output */\nexport type CHashO = ReturnType<typeof createOptHasher>;\n/** XOF with output */\nexport type CHashXO = ReturnType<typeof createXOFer>;\n\n/** Wraps hash function, creating an interface on top of it */\nexport function createHasher<T extends Hash<T>>(\n  hashCons: () => Hash<T>\n): {\n  (msg: Input): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(): Hash<T>;\n} {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function createOptHasher<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): Hash<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function createXOFer<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): HashXOF<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\nexport const wrapConstructor: typeof createHasher = createHasher;\nexport const wrapConstructorWithOpts: typeof createOptHasher = createOptHasher;\nexport const wrapXOFConstructorWithOpts: typeof createXOFer = createXOFer;\n\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return Uint8Array.from(crypto.randomBytes(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n"], "mappings": "AAAA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,sBAAsB;AAE7C;AACA,OAAM,SAAUC,OAAOA,CAACC,CAAU;EAChC,OAAOA,CAAC,YAAYC,UAAU,IAAKC,WAAW,CAACC,MAAM,CAACH,CAAC,CAAC,IAAIA,CAAC,CAACI,WAAW,CAACC,IAAI,KAAK,YAAa;AAClG;AAEA;AACA,OAAM,SAAUC,OAAOA,CAACC,CAAS;EAC/B,IAAI,CAACC,MAAM,CAACC,aAAa,CAACF,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,GAAGH,CAAC,CAAC;AAC/F;AAEA;AACA,OAAM,SAAUI,MAAMA,CAACC,CAAyB,EAAE,GAAGC,OAAiB;EACpE,IAAI,CAACd,OAAO,CAACa,CAAC,CAAC,EAAE,MAAM,IAAIF,KAAK,CAAC,qBAAqB,CAAC;EACvD,IAAIG,OAAO,CAACC,MAAM,GAAG,CAAC,IAAI,CAACD,OAAO,CAACE,QAAQ,CAACH,CAAC,CAACE,MAAM,CAAC,EACnD,MAAM,IAAIJ,KAAK,CAAC,gCAAgC,GAAGG,OAAO,GAAG,eAAe,GAAGD,CAAC,CAACE,MAAM,CAAC;AAC5F;AAEA;AACA,OAAM,SAAUE,KAAKA,CAACC,CAAQ;EAC5B,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,UAAU,EAC3D,MAAM,IAAIR,KAAK,CAAC,8CAA8C,CAAC;EACjEJ,OAAO,CAACW,CAAC,CAACE,SAAS,CAAC;EACpBb,OAAO,CAACW,CAAC,CAACG,QAAQ,CAAC;AACrB;AAEA;AACA,OAAM,SAAUC,OAAOA,CAACC,QAAa,EAAEC,aAAa,GAAG,IAAI;EACzD,IAAID,QAAQ,CAACE,SAAS,EAAE,MAAM,IAAId,KAAK,CAAC,kCAAkC,CAAC;EAC3E,IAAIa,aAAa,IAAID,QAAQ,CAACG,QAAQ,EAAE,MAAM,IAAIf,KAAK,CAAC,uCAAuC,CAAC;AAClG;AAEA;AACA,OAAM,SAAUgB,OAAOA,CAACC,GAAQ,EAAEL,QAAa;EAC7CX,MAAM,CAACgB,GAAG,CAAC;EACX,MAAMC,GAAG,GAAGN,QAAQ,CAACH,SAAS;EAC9B,IAAIQ,GAAG,CAACb,MAAM,GAAGc,GAAG,EAAE;IACpB,MAAM,IAAIlB,KAAK,CAAC,wDAAwD,GAAGkB,GAAG,CAAC;EACjF;AACF;AAOA;AACA,OAAM,SAAUC,EAAEA,CAACC,GAAe;EAChC,OAAO,IAAI7B,UAAU,CAAC6B,GAAG,CAACC,MAAM,EAAED,GAAG,CAACE,UAAU,EAAEF,GAAG,CAACG,UAAU,CAAC;AACnE;AAEA;AACA,OAAM,SAAUC,GAAGA,CAACJ,GAAe;EACjC,OAAO,IAAIK,WAAW,CAACL,GAAG,CAACC,MAAM,EAAED,GAAG,CAACE,UAAU,EAAEI,IAAI,CAACC,KAAK,CAACP,GAAG,CAACG,UAAU,GAAG,CAAC,CAAC,CAAC;AACpF;AAEA;AACA,OAAM,SAAUK,KAAKA,CAAC,GAAGC,MAAoB;EAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACzB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACtCD,MAAM,CAACC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EACnB;AACF;AAEA;AACA,OAAM,SAAUC,UAAUA,CAACZ,GAAe;EACxC,OAAO,IAAIa,QAAQ,CAACb,GAAG,CAACC,MAAM,EAAED,GAAG,CAACE,UAAU,EAAEF,GAAG,CAACG,UAAU,CAAC;AACjE;AAEA;AACA,OAAM,SAAUW,IAAIA,CAACC,IAAY,EAAEC,KAAa;EAC9C,OAAQD,IAAI,IAAK,EAAE,GAAGC,KAAM,GAAKD,IAAI,KAAKC,KAAM;AAClD;AAEA;AACA,OAAM,SAAUC,IAAIA,CAACF,IAAY,EAAEC,KAAa;EAC9C,OAAQD,IAAI,IAAIC,KAAK,GAAMD,IAAI,KAAM,EAAE,GAAGC,KAAM,KAAM,CAAE;AAC1D;AAEA;AACA,OAAO,MAAME,IAAI,GAAY,eAAgB,CAAC,MAC5C,IAAI/C,UAAU,CAAC,IAAIkC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAC,CAAE;AAErE;AACA,OAAM,SAAUkB,QAAQA,CAACJ,IAAY;EACnC,OACIA,IAAI,IAAI,EAAE,GAAI,UAAU,GACxBA,IAAI,IAAI,CAAC,GAAI,QAAS,GACtBA,IAAI,KAAK,CAAC,GAAI,MAAO,GACrBA,IAAI,KAAK,EAAE,GAAI,IAAK;AAE1B;AACA;AACA,OAAO,MAAMK,SAAS,GAA0BF,IAAI,GAC/CzC,CAAS,IAAKA,CAAC,GACfA,CAAS,IAAK0C,QAAQ,CAAC1C,CAAC,CAAC;AAE9B;AACA,OAAO,MAAM4C,YAAY,GAAqBD,SAAS;AACvD;AACA,OAAM,SAAUE,UAAUA,CAACtB,GAAgB;EACzC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,GAAG,CAAChB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACnCV,GAAG,CAACU,CAAC,CAAC,GAAGS,QAAQ,CAACnB,GAAG,CAACU,CAAC,CAAC,CAAC;EAC3B;EACA,OAAOV,GAAG;AACZ;AAEA,OAAO,MAAMuB,UAAU,GAAoCL,IAAI,GAC1DM,CAAc,IAAKA,CAAC,GACrBF,UAAU;AAEd;AACA,MAAMG,aAAa,GAAY,eAAgB,CAAC;AAC9C;AACA,OAAOtD,UAAU,CAACuD,IAAI,CAAC,EAAE,CAAC,CAACC,KAAK,KAAK,UAAU,IAAI,OAAOxD,UAAU,CAACyD,OAAO,KAAK,UAAU,EAAC,CAAE;AAEhG;AACA,MAAMC,KAAK,GAAG,eAAgBC,KAAK,CAACJ,IAAI,CAAC;EAAE1C,MAAM,EAAE;AAAG,CAAE,EAAE,CAAC+C,CAAC,EAAErB,CAAC,KAC7DA,CAAC,CAACsB,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAChC;AAED;;;;AAIA,OAAM,SAAUC,UAAUA,CAACC,KAAiB;EAC1CtD,MAAM,CAACsD,KAAK,CAAC;EACb;EACA,IAAIV,aAAa,EAAE,OAAOU,KAAK,CAACR,KAAK,EAAE;EACvC;EACA,IAAIS,GAAG,GAAG,EAAE;EACZ,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,KAAK,CAACnD,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACrC0B,GAAG,IAAIP,KAAK,CAACM,KAAK,CAACzB,CAAC,CAAC,CAAC;EACxB;EACA,OAAO0B,GAAG;AACZ;AAEA;AACA,MAAMC,MAAM,GAAG;EAAEC,EAAE,EAAE,EAAE;EAAEC,EAAE,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEC,CAAC,EAAE,EAAE;EAAEvE,CAAC,EAAE,EAAE;EAAEwE,CAAC,EAAE;AAAG,CAAW;AACvE,SAASC,aAAaA,CAACC,EAAU;EAC/B,IAAIA,EAAE,IAAIP,MAAM,CAACC,EAAE,IAAIM,EAAE,IAAIP,MAAM,CAACE,EAAE,EAAE,OAAOK,EAAE,GAAGP,MAAM,CAACC,EAAE,CAAC,CAAC;EAC/D,IAAIM,EAAE,IAAIP,MAAM,CAACG,CAAC,IAAII,EAAE,IAAIP,MAAM,CAACI,CAAC,EAAE,OAAOG,EAAE,IAAIP,MAAM,CAACG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACnE,IAAII,EAAE,IAAIP,MAAM,CAACnE,CAAC,IAAI0E,EAAE,IAAIP,MAAM,CAACK,CAAC,EAAE,OAAOE,EAAE,IAAIP,MAAM,CAACnE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACnE;AACF;AAEA;;;;AAIA,OAAM,SAAU2E,UAAUA,CAACT,GAAW;EACpC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIxD,KAAK,CAAC,2BAA2B,GAAG,OAAOwD,GAAG,CAAC;EACtF;EACA,IAAIX,aAAa,EAAE,OAAOtD,UAAU,CAACyD,OAAO,CAACQ,GAAG,CAAC;EACjD,MAAMU,EAAE,GAAGV,GAAG,CAACpD,MAAM;EACrB,MAAM+D,EAAE,GAAGD,EAAE,GAAG,CAAC;EACjB,IAAIA,EAAE,GAAG,CAAC,EAAE,MAAM,IAAIlE,KAAK,CAAC,kDAAkD,GAAGkE,EAAE,CAAC;EACpF,MAAME,KAAK,GAAG,IAAI7E,UAAU,CAAC4E,EAAE,CAAC;EAChC,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAGF,EAAE,EAAEE,EAAE,EAAE,EAAEC,EAAE,IAAI,CAAC,EAAE;IAC/C,MAAMC,EAAE,GAAGR,aAAa,CAACP,GAAG,CAACgB,UAAU,CAACF,EAAE,CAAC,CAAC;IAC5C,MAAMG,EAAE,GAAGV,aAAa,CAACP,GAAG,CAACgB,UAAU,CAACF,EAAE,GAAG,CAAC,CAAC,CAAC;IAChD,IAAIC,EAAE,KAAKG,SAAS,IAAID,EAAE,KAAKC,SAAS,EAAE;MACxC,MAAMC,IAAI,GAAGnB,GAAG,CAACc,EAAE,CAAC,GAAGd,GAAG,CAACc,EAAE,GAAG,CAAC,CAAC;MAClC,MAAM,IAAItE,KAAK,CAAC,8CAA8C,GAAG2E,IAAI,GAAG,aAAa,GAAGL,EAAE,CAAC;IAC7F;IACAF,KAAK,CAACC,EAAE,CAAC,GAAGE,EAAE,GAAG,EAAE,GAAGE,EAAE,CAAC,CAAC;EAC5B;EACA,OAAOL,KAAK;AACd;AAEA;;;;;AAKA,OAAO,MAAMQ,QAAQ,GAAG,MAAAA,CAAA,KAA0B,CAAE,CAAC;AAErD;AACA,OAAO,eAAeC,SAASA,CAC7BC,KAAa,EACbC,IAAY,EACZC,EAAuB;EAEvB,IAAIC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;EACnB,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,KAAK,EAAEhD,CAAC,EAAE,EAAE;IAC9BkD,EAAE,CAAClD,CAAC,CAAC;IACL;IACA,MAAMsD,IAAI,GAAGF,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;IAC5B,IAAIG,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGL,IAAI,EAAE;IAC9B,MAAMH,QAAQ,EAAE;IAChBK,EAAE,IAAIG,IAAI;EACZ;AACF;AAMA;;;;AAIA,OAAM,SAAUC,WAAWA,CAACC,GAAW;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAItF,KAAK,CAAC,iBAAiB,CAAC;EAC/D,OAAO,IAAIT,UAAU,CAAC,IAAIgG,WAAW,EAAE,CAACC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD;AAEA;;;;AAIA,OAAM,SAAUG,WAAWA,CAAClC,KAAiB;EAC3C,OAAO,IAAImC,WAAW,EAAE,CAACC,MAAM,CAACpC,KAAK,CAAC;AACxC;AAIA;;;;;AAKA,OAAM,SAAUqC,OAAOA,CAACC,IAAW;EACjC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAGR,WAAW,CAACQ,IAAI,CAAC;EACtD5F,MAAM,CAAC4F,IAAI,CAAC;EACZ,OAAOA,IAAI;AACb;AAIA;;;;AAIA,OAAM,SAAUC,eAAeA,CAACD,IAAc;EAC5C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAGR,WAAW,CAACQ,IAAI,CAAC;EACtD5F,MAAM,CAAC4F,IAAI,CAAC;EACZ,OAAOA,IAAI;AACb;AAEA;AACA,OAAM,SAAUE,WAAWA,CAAC,GAAGlE,MAAoB;EACjD,IAAImE,GAAG,GAAG,CAAC;EACX,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACzB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACtC,MAAMxC,CAAC,GAAGuC,MAAM,CAACC,CAAC,CAAC;IACnB7B,MAAM,CAACX,CAAC,CAAC;IACT0G,GAAG,IAAI1G,CAAC,CAACc,MAAM;EACjB;EACA,MAAM6F,GAAG,GAAG,IAAI1G,UAAU,CAACyG,GAAG,CAAC;EAC/B,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEoE,GAAG,GAAG,CAAC,EAAEpE,CAAC,GAAGD,MAAM,CAACzB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IAC/C,MAAMxC,CAAC,GAAGuC,MAAM,CAACC,CAAC,CAAC;IACnBmE,GAAG,CAACE,GAAG,CAAC7G,CAAC,EAAE4G,GAAG,CAAC;IACfA,GAAG,IAAI5G,CAAC,CAACc,MAAM;EACjB;EACA,OAAO6F,GAAG;AACZ;AAGA,OAAM,SAAUG,SAASA,CACvBC,QAAY,EACZC,IAAS;EAET,IAAIA,IAAI,KAAK5B,SAAS,IAAI,EAAE,CAACtB,QAAQ,CAACmD,IAAI,CAACD,IAAI,CAAC,KAAK,iBAAiB,EACpE,MAAM,IAAItG,KAAK,CAAC,uCAAuC,CAAC;EAC1D,MAAMwG,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACL,QAAQ,EAAEC,IAAI,CAAC;EAC5C,OAAOE,MAAiB;AAC1B;AAUA;AACA,OAAM,MAAgBG,IAAI;AA2C1B;AACA,OAAM,SAAUC,YAAYA,CAC1BC,QAAuB;EAOvB,MAAMC,KAAK,GAAIC,GAAU,IAAiBF,QAAQ,EAAE,CAACG,MAAM,CAACpB,OAAO,CAACmB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAClF,MAAMC,GAAG,GAAGL,QAAQ,EAAE;EACtBC,KAAK,CAACrG,SAAS,GAAGyG,GAAG,CAACzG,SAAS;EAC/BqG,KAAK,CAACpG,QAAQ,GAAGwG,GAAG,CAACxG,QAAQ;EAC7BoG,KAAK,CAACtG,MAAM,GAAG,MAAMqG,QAAQ,EAAE;EAC/B,OAAOC,KAAK;AACd;AAEA,OAAM,SAAUK,eAAeA,CAC7BN,QAA+B;EAO/B,MAAMC,KAAK,GAAGA,CAACC,GAAU,EAAET,IAAQ,KAAiBO,QAAQ,CAACP,IAAI,CAAC,CAACU,MAAM,CAACpB,OAAO,CAACmB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAChG,MAAMC,GAAG,GAAGL,QAAQ,CAAC,EAAO,CAAC;EAC7BC,KAAK,CAACrG,SAAS,GAAGyG,GAAG,CAACzG,SAAS;EAC/BqG,KAAK,CAACpG,QAAQ,GAAGwG,GAAG,CAACxG,QAAQ;EAC7BoG,KAAK,CAACtG,MAAM,GAAI8F,IAAQ,IAAKO,QAAQ,CAACP,IAAI,CAAC;EAC3C,OAAOQ,KAAK;AACd;AAEA,OAAM,SAAUM,WAAWA,CACzBP,QAAkC;EAOlC,MAAMC,KAAK,GAAGA,CAACC,GAAU,EAAET,IAAQ,KAAiBO,QAAQ,CAACP,IAAI,CAAC,CAACU,MAAM,CAACpB,OAAO,CAACmB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAChG,MAAMC,GAAG,GAAGL,QAAQ,CAAC,EAAO,CAAC;EAC7BC,KAAK,CAACrG,SAAS,GAAGyG,GAAG,CAACzG,SAAS;EAC/BqG,KAAK,CAACpG,QAAQ,GAAGwG,GAAG,CAACxG,QAAQ;EAC7BoG,KAAK,CAACtG,MAAM,GAAI8F,IAAQ,IAAKO,QAAQ,CAACP,IAAI,CAAC;EAC3C,OAAOQ,KAAK;AACd;AACA,OAAO,MAAMO,eAAe,GAAwBT,YAAY;AAChE,OAAO,MAAMU,uBAAuB,GAA2BH,eAAe;AAC9E,OAAO,MAAMI,0BAA0B,GAAuBH,WAAW;AAEzE;AACA,OAAM,SAAUI,WAAWA,CAACC,WAAW,GAAG,EAAE;EAC1C,IAAIrI,MAAM,IAAI,OAAOA,MAAM,CAACsI,eAAe,KAAK,UAAU,EAAE;IAC1D,OAAOtI,MAAM,CAACsI,eAAe,CAAC,IAAInI,UAAU,CAACkI,WAAW,CAAC,CAAC;EAC5D;EACA;EACA,IAAIrI,MAAM,IAAI,OAAOA,MAAM,CAACoI,WAAW,KAAK,UAAU,EAAE;IACtD,OAAOjI,UAAU,CAACuD,IAAI,CAAC1D,MAAM,CAACoI,WAAW,CAACC,WAAW,CAAC,CAAC;EACzD;EACA,MAAM,IAAIzH,KAAK,CAAC,wCAAwC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}