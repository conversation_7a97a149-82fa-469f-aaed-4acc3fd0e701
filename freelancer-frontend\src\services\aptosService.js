import { Aptos, AptosConfig, Network } from '@aptos-labs/ts-sdk';

// Initialize Aptos client
const config = new AptosConfig({ network: Network.DEVNET });
const aptos = new Aptos(config);

// Contract address - replace with your deployed contract address
const CONTRACT_ADDRESS = '0x96dea488c901652bb2865884c46bdf4dd70852b9d4151cecfb2ce59ea6e01a22';

// Status constants matching the smart contract
export const ESCROW_STATUS = {
  CREATED: 0,
  FUNDED: 1,
  IN_PROGRESS: 2,
  SUBMITTED: 3,
  COMPLETED: 4,
  DISPUTED: 5,
  CANCELLED: 6,
  REFUNDED: 7
};

export const STATUS_LABELS = {
  [ESCROW_STATUS.CREATED]: 'Created',
  [ESCROW_STATUS.FUNDED]: 'Funded',
  [ESCROW_STATUS.IN_PROGRESS]: 'In Progress',
  [ESCROW_STATUS.SUBMITTED]: 'Submitted',
  [ESCROW_STATUS.COMPLETED]: 'Completed',
  [ESCROW_STATUS.DISPUTED]: 'Disputed',
  [ESCROW_STATUS.CANCELLED]: 'Cancelled',
  [ESCROW_STATUS.REFUNDED]: 'Refunded'
};

// Helper function to convert APT to Octas (1 APT = 100,000,000 Octas)
export const aptToOctas = (apt) => {
  return Math.floor(parseFloat(apt) * 100000000);
};

// Helper function to convert Octas to APT
export const octasToApt = (octas) => {
  return (parseInt(octas) / 100000000).toFixed(8);
};

// Create a new escrow contract
export const createEscrow = async (signAndSubmitTransaction, {
  freelancerAddress,
  title,
  description,
  category,
  amount, // in APT
  deadline // Unix timestamp
}) => {
  const amountInOctas = aptToOctas(amount);
  
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::create_escrow`,
      functionArguments: [
        freelancerAddress,
        title,
        description,
        category,
        amountInOctas.toString(),
        deadline.toString()
      ]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error creating escrow:', error);
    throw error;
  }
};

// Fund an escrow contract
export const fundEscrow = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::fund_escrow`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error funding escrow:', error);
    throw error;
  }
};

// Start work on an escrow
export const startWork = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::start_work`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error starting work:', error);
    throw error;
  }
};

// Submit work for review
export const submitWork = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::submit_work`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error submitting work:', error);
    throw error;
  }
};

// Approve and complete work
export const approveWork = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::approve_work`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error approving work:', error);
    throw error;
  }
};

// Get user's escrows
export const getUserEscrows = async (userAddress) => {
  try {
    const resource = await aptos.getAccountResource({
      accountAddress: CONTRACT_ADDRESS,
      resourceType: `${CONTRACT_ADDRESS}::freelancer_escrow::Platform`
    });
    
    // This is a simplified version - in a real implementation,
    // you'd need to query the user_escrows table
    return [];
  } catch (error) {
    console.error('Error fetching user escrows:', error);
    return [];
  }
};

// Get escrow details by ID
export const getEscrowDetails = async (escrowId) => {
  try {
    // This would require a view function in the smart contract
    // For now, return mock data structure
    return {
      id: escrowId,
      client: '',
      freelancer: '',
      title: '',
      description: '',
      category: '',
      amount: 0,
      platform_fee: 0,
      deadline: 0,
      status: ESCROW_STATUS.CREATED,
      created_at: 0,
      funded_at: 0,
      started_at: 0,
      submitted_at: 0,
      completed_at: 0
    };
  } catch (error) {
    console.error('Error fetching escrow details:', error);
    throw error;
  }
};

// Get account balance
export const getAccountBalance = async (accountAddress) => {
  try {
    const resources = await aptos.getAccountResources({
      accountAddress
    });
    
    const coinResource = resources.find(
      (resource) => resource.type === '0x1::coin::CoinStore<0x1::aptos_coin::AptosCoin>'
    );
    
    if (coinResource) {
      return octasToApt(coinResource.data.coin.value);
    }
    return '0';
  } catch (error) {
    console.error('Error fetching account balance:', error);
    return '0';
  }
};

export default {
  createEscrow,
  fundEscrow,
  startWork,
  submitWork,
  approveWork,
  getUserEscrows,
  getEscrowDetails,
  getAccountBalance,
  aptToOctas,
  octasToApt,
  ESCROW_STATUS,
  STATUS_LABELS
};
