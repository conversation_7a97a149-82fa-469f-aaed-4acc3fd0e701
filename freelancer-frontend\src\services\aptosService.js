import { Aptos, AptosConfig, Network } from '@aptos-labs/ts-sdk';
import {
    API_ENDPOINTS,
    CONTRACT_ADDRESS,
    ESCROW_STATUS,
    STATUS_LABELS
} from '../constants';
import { aptToOctas, octasToApt } from '../utils';

// Initialize Aptos client
const config = new AptosConfig({
  network: Network.DEVNET,
  fullnode: API_ENDPOINTS.APTOS_DEVNET,
  faucet: API_ENDPOINTS.FAUCET_DEVNET
});
const aptos = new Aptos(config);

// Export constants for use in components
export { ESCROW_STATUS, STATUS_LABELS };

// Enhanced error handling
class AptosServiceError extends Error {
  constructor(message, code, originalError) {
    super(message);
    this.name = 'AptosServiceError';
    this.code = code;
    this.originalError = originalError;
  }
}

// Retry mechanism for failed transactions
const retryTransaction = async (fn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
};

// Create a new escrow contract
export const createEscrow = async (signAndSubmitTransaction, {
  freelancerAddress,
  title,
  description,
  category,
  amount, // in APT
  deadline // Unix timestamp
}) => {
  try {
    if (!signAndSubmitTransaction) {
      throw new AptosServiceError('Wallet not connected', 'WALLET_NOT_CONNECTED');
    }

    const amountInOctas = aptToOctas(amount);

    const transaction = {
      data: {
        function: `${CONTRACT_ADDRESS}::freelancer_escrow::create_escrow`,
        functionArguments: [
          freelancerAddress,
          title,
          description,
          category,
          amountInOctas.toString(),
          deadline.toString()
        ]
      }
    };

    const response = await retryTransaction(async () => {
      const txResponse = await signAndSubmitTransaction(transaction);
      await aptos.waitForTransaction({
        transactionHash: txResponse.hash,
        options: { timeoutSecs: 30 }
      });
      return txResponse;
    });

    return {
      success: true,
      transactionHash: response.hash,
      message: 'Escrow created successfully'
    };
  } catch (error) {
    console.error('Error creating escrow:', error);
    throw new AptosServiceError(
      getErrorMessage(error) || 'Failed to create escrow',
      'CREATE_ESCROW_FAILED',
      error
    );
  }
};

// Fund an escrow contract
export const fundEscrow = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::fund_escrow`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error funding escrow:', error);
    throw error;
  }
};

// Start work on an escrow
export const startWork = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::start_work`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error starting work:', error);
    throw error;
  }
};

// Submit work for review
export const submitWork = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::submit_work`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error submitting work:', error);
    throw error;
  }
};

// Approve and complete work
export const approveWork = async (signAndSubmitTransaction, escrowId) => {
  const transaction = {
    data: {
      function: `${CONTRACT_ADDRESS}::freelancer_escrow::approve_work`,
      functionArguments: [escrowId.toString()]
    }
  };

  try {
    const response = await signAndSubmitTransaction(transaction);
    await aptos.waitForTransaction({ transactionHash: response.hash });
    return response;
  } catch (error) {
    console.error('Error approving work:', error);
    throw error;
  }
};

// Get user's escrows
export const getUserEscrows = async (userAddress) => {
  try {
    const resource = await aptos.getAccountResource({
      accountAddress: CONTRACT_ADDRESS,
      resourceType: `${CONTRACT_ADDRESS}::freelancer_escrow::Platform`
    });
    
    // This is a simplified version - in a real implementation,
    // you'd need to query the user_escrows table
    return [];
  } catch (error) {
    console.error('Error fetching user escrows:', error);
    return [];
  }
};

// Get escrow details by ID
export const getEscrowDetails = async (escrowId) => {
  try {
    // This would require a view function in the smart contract
    // For now, return mock data structure
    return {
      id: escrowId,
      client: '',
      freelancer: '',
      title: '',
      description: '',
      category: '',
      amount: 0,
      platform_fee: 0,
      deadline: 0,
      status: ESCROW_STATUS.CREATED,
      created_at: 0,
      funded_at: 0,
      started_at: 0,
      submitted_at: 0,
      completed_at: 0
    };
  } catch (error) {
    console.error('Error fetching escrow details:', error);
    throw error;
  }
};

// Get account balance
export const getAccountBalance = async (accountAddress) => {
  try {
    const resources = await aptos.getAccountResources({
      accountAddress
    });
    
    const coinResource = resources.find(
      (resource) => resource.type === '0x1::coin::CoinStore<0x1::aptos_coin::AptosCoin>'
    );
    
    if (coinResource) {
      return octasToApt(coinResource.data.coin.value);
    }
    return '0';
  } catch (error) {
    console.error('Error fetching account balance:', error);
    return '0';
  }
};

export default {
  createEscrow,
  fundEscrow,
  startWork,
  submitWork,
  approveWork,
  getUserEscrows,
  getEscrowDetails,
  getAccountBalance,
  aptToOctas,
  octasToApt,
  ESCROW_STATUS,
  STATUS_LABELS
};
