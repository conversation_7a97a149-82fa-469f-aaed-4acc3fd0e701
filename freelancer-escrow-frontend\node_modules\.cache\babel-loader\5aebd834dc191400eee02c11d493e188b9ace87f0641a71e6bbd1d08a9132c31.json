{"ast": null, "code": "function number(n) {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n  if (typeof b !== 'boolean') throw new Error(`Expected boolean, not ${b}`);\n}\n// copied from utils\nfunction isBytes(a) {\n  return a instanceof Uint8Array || a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array';\n}\nfunction bytes(b, ...lengths) {\n  if (!isBytes(b)) throw new Error('Expected Uint8Array');\n  if (lengths.length > 0 && !lengths.includes(b.length)) throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n  if (typeof hash !== 'function' || typeof hash.create !== 'function') throw new Error('Hash should be wrapped by utils.wrapConstructor');\n  number(hash.outputLen);\n  number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n  bytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n  }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = {\n  number,\n  bool,\n  bytes,\n  hash,\n  exists,\n  output\n};\nexport default assert;", "map": {"version": 3, "names": ["number", "n", "Number", "isSafeInteger", "Error", "bool", "b", "isBytes", "a", "Uint8Array", "constructor", "name", "bytes", "lengths", "length", "includes", "hash", "create", "outputLen", "blockLen", "exists", "instance", "checkFinished", "destroyed", "finished", "output", "out", "min", "assert"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\aptos\\node_modules\\@noble\\hashes\\src\\_assert.ts"], "sourcesContent": ["function number(n: number) {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error(`Wrong positive integer: ${n}`);\n}\n\nfunction bool(b: boolean) {\n  if (typeof b !== 'boolean') throw new Error(`Expected boolean, not ${b}`);\n}\n\n// copied from utils\nfunction isBytes(a: unknown): a is Uint8Array {\n  return (\n    a instanceof Uint8Array ||\n    (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array')\n  );\n}\n\nfunction bytes(b: Uint8Array | undefined, ...lengths: number[]) {\n  if (!isBytes(b)) throw new Error('Expected Uint8Array');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\n\ntype Hash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\nfunction hash(hash: Hash) {\n  if (typeof hash !== 'function' || typeof hash.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.wrapConstructor');\n  number(hash.outputLen);\n  number(hash.blockLen);\n}\n\nfunction exists(instance: any, checkFinished = true) {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\nfunction output(out: any, instance: any) {\n  bytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n  }\n}\n\nexport { number, bool, bytes, hash, exists, output };\n\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,CAAS;EACvB,IAAI,CAACC,MAAM,CAACC,aAAa,CAACF,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,2BAA2BH,CAAC,EAAE,CAAC;AACxF;AAEA,SAASI,IAAIA,CAACC,CAAU;EACtB,IAAI,OAAOA,CAAC,KAAK,SAAS,EAAE,MAAM,IAAIF,KAAK,CAAC,yBAAyBE,CAAC,EAAE,CAAC;AAC3E;AAEA;AACA,SAASC,OAAOA,CAACC,CAAU;EACzB,OACEA,CAAC,YAAYC,UAAU,IACtBD,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACE,WAAW,CAACC,IAAI,KAAK,YAAa;AAE/E;AAEA,SAASC,KAAKA,CAACN,CAAyB,EAAE,GAAGO,OAAiB;EAC5D,IAAI,CAACN,OAAO,CAACD,CAAC,CAAC,EAAE,MAAM,IAAIF,KAAK,CAAC,qBAAqB,CAAC;EACvD,IAAIS,OAAO,CAACC,MAAM,GAAG,CAAC,IAAI,CAACD,OAAO,CAACE,QAAQ,CAACT,CAAC,CAACQ,MAAM,CAAC,EACnD,MAAM,IAAIV,KAAK,CAAC,iCAAiCS,OAAO,mBAAmBP,CAAC,CAACQ,MAAM,EAAE,CAAC;AAC1F;AAQA,SAASE,IAAIA,CAACA,IAAU;EACtB,IAAI,OAAOA,IAAI,KAAK,UAAU,IAAI,OAAOA,IAAI,CAACC,MAAM,KAAK,UAAU,EACjE,MAAM,IAAIb,KAAK,CAAC,iDAAiD,CAAC;EACpEJ,MAAM,CAACgB,IAAI,CAACE,SAAS,CAAC;EACtBlB,MAAM,CAACgB,IAAI,CAACG,QAAQ,CAAC;AACvB;AAEA,SAASC,MAAMA,CAACC,QAAa,EAAEC,aAAa,GAAG,IAAI;EACjD,IAAID,QAAQ,CAACE,SAAS,EAAE,MAAM,IAAInB,KAAK,CAAC,kCAAkC,CAAC;EAC3E,IAAIkB,aAAa,IAAID,QAAQ,CAACG,QAAQ,EAAE,MAAM,IAAIpB,KAAK,CAAC,uCAAuC,CAAC;AAClG;AACA,SAASqB,MAAMA,CAACC,GAAQ,EAAEL,QAAa;EACrCT,KAAK,CAACc,GAAG,CAAC;EACV,MAAMC,GAAG,GAAGN,QAAQ,CAACH,SAAS;EAC9B,IAAIQ,GAAG,CAACZ,MAAM,GAAGa,GAAG,EAAE;IACpB,MAAM,IAAIvB,KAAK,CAAC,yDAAyDuB,GAAG,EAAE,CAAC;EACjF;AACF;AAEA,SAAS3B,MAAM,EAAEK,IAAI,EAAEO,KAAK,EAAEI,IAAI,EAAEI,MAAM,EAAEK,MAAM;AAElD,MAAMG,MAAM,GAAG;EAAE5B,MAAM;EAAEK,IAAI;EAAEO,KAAK;EAAEI,IAAI;EAAEI,MAAM;EAAEK;AAAM,CAAE;AAC5D,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}