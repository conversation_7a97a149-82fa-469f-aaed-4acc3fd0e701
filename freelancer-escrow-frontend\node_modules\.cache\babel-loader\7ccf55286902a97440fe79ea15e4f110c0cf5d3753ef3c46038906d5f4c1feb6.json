{"ast": null, "code": "import { c as r } from \"./chunk-56CNRT2K.mjs\";\nvar n = class {\n  constructor(e) {\n    this.buffer = new ArrayBuffer(e.length), new Uint8Array(this.buffer).set(e, 0), this.offset = 0;\n  }\n  read(e) {\n    if (this.offset + e > this.buffer.byteLength) throw new Error(\"Reached to the end of buffer\");\n    let i = this.buffer.slice(this.offset, this.offset + e);\n    return this.offset += e, i;\n  }\n  deserializeStr() {\n    let e = this.deserializeBytes();\n    return new TextDecoder().decode(e);\n  }\n  deserializeOptionStr() {\n    return this.deserializeBool() ? this.deserializeStr() : void 0;\n  }\n  deserializeOption(e) {\n    return this.deserializeBool() ? this.deserialize(e) : void 0;\n  }\n  deserializeBytes() {\n    let e = this.deserializeUleb128AsU32();\n    return new Uint8Array(this.read(e));\n  }\n  deserializeFixedBytes(e) {\n    return new Uint8Array(this.read(e));\n  }\n  deserializeBool() {\n    let e = new Uint8Array(this.read(1))[0];\n    if (e !== 1 && e !== 0) throw new Error(\"Invalid boolean value\");\n    return e === 1;\n  }\n  deserializeU8() {\n    return new DataView(this.read(1)).getUint8(0);\n  }\n  deserializeU16() {\n    return new DataView(this.read(2)).getUint16(0, !0);\n  }\n  deserializeU32() {\n    return new DataView(this.read(4)).getUint32(0, !0);\n  }\n  deserializeU64() {\n    let e = this.deserializeU32(),\n      i = this.deserializeU32();\n    return BigInt(BigInt(i) << BigInt(32) | BigInt(e));\n  }\n  deserializeU128() {\n    let e = this.deserializeU64(),\n      i = this.deserializeU64();\n    return BigInt(i << BigInt(64) | e);\n  }\n  deserializeU256() {\n    let e = this.deserializeU128(),\n      i = this.deserializeU128();\n    return BigInt(i << BigInt(128) | e);\n  }\n  deserializeUleb128AsU32() {\n    let e = BigInt(0),\n      i = 0;\n    for (; e < r;) {\n      let t = this.deserializeU8();\n      if (e |= BigInt(t & 127) << BigInt(i), !(t & 128)) break;\n      i += 7;\n    }\n    if (e > r) throw new Error(\"Overflow while parsing uleb128-encoded uint32 value\");\n    return Number(e);\n  }\n  deserialize(e) {\n    return e.deserialize(this);\n  }\n  deserializeVector(e) {\n    let i = this.deserializeUleb128AsU32(),\n      t = new Array();\n    for (let s = 0; s < i; s += 1) t.push(this.deserialize(e));\n    return t;\n  }\n};\nexport { n as a };", "map": {"version": 3, "names": ["n", "constructor", "e", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Uint8Array", "set", "offset", "read", "byteLength", "Error", "i", "slice", "deserializeStr", "deserializeBytes", "TextDecoder", "decode", "deserializeOptionStr", "deserializeBool", "deserializeOption", "deserialize", "deserializeUleb128AsU32", "deserializeFixedBytes", "deserializeU8", "DataView", "getUint8", "deserializeU16", "getUint16", "deserializeU32", "getUint32", "deserializeU64", "BigInt", "deserializeU128", "deserializeU256", "r", "t", "Number", "deserializeVector", "Array", "s", "push", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\deserializer.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable no-bitwise */\nimport { MAX_U32_NUMBER } from \"./consts\";\nimport { Uint8, Uint16, Uint32, Uint64, Uint128, Uint256 } from \"../types\";\n\n/**\n * This interface exists to define Deserializable<T> inputs for functions that\n * deserialize a byte buffer into a type T.\n * It is not intended to be implemented or extended, because Typescript has no support\n * for static methods in interfaces.\n */\nexport interface Deserializable<T> {\n  deserialize(deserializer: Deserializer): T;\n}\n\nexport class Deserializer {\n  private buffer: ArrayBuffer;\n\n  private offset: number;\n\n  constructor(data: Uint8Array) {\n    // copies data to prevent outside mutation of buffer.\n    this.buffer = new ArrayBuffer(data.length);\n    new Uint8Array(this.buffer).set(data, 0);\n    this.offset = 0;\n  }\n\n  private read(length: number): ArrayBuffer {\n    if (this.offset + length > this.buffer.byteLength) {\n      throw new Error(\"Reached to the end of buffer\");\n    }\n\n    const bytes = this.buffer.slice(this.offset, this.offset + length);\n    this.offset += length;\n    return bytes;\n  }\n\n  /**\n   * Deserializes a string. UTF8 string is supported. Reads the string's bytes length \"l\" first,\n   * and then reads \"l\" bytes of content. Decodes the byte array into a string.\n   *\n   * BCS layout for \"string\": string_length | string_content\n   * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.\n   *\n   * @example\n   * ```ts\n   * const deserializer = new Deserializer(new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   * assert(deserializer.deserializeStr() === \"1234abcd\");\n   * ```\n   */\n  deserializeStr(): string {\n    const value = this.deserializeBytes();\n    const textDecoder = new TextDecoder();\n    return textDecoder.decode(value);\n  }\n\n  /**\n   * Deserializes a an optional string.\n   *\n   * BCS layout for Optional<String>: 0 if none, else 1 | string_length | string_content\n   * @example\n   * ```ts\n   * const deserializer = new Deserializer(new Uint8Array([0x00]));\n   * assert(deserializer.deserializeOptionStr() === undefined);\n   * const deserializer = new Deserializer(new Uint8Array([1, 8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   * assert(deserializer.deserializeOptionStr() === \"1234abcd\");\n   * ```\n   */\n  deserializeOptionStr(): string | undefined {\n    const exists = this.deserializeBool();\n    return exists ? this.deserializeStr() : undefined;\n  }\n\n  /**\n   * Deserializes a an optional deserializable class.\n   *\n   * BCS layout for Optional<T>: 0 if none, else 1 | bcs representation of class\n   *\n   * @example\n   * const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));\n   * const value = deserializer.deserializeOption(MyClass); // where MyClass has a `deserialize` function\n   * // value is now an instance of MyClass\n   *\n   * const deserializer = new Deserializer(new Uint8Array([0]));\n   * const value = deserializer.deserializeOption(MyClass); // where MyClass has a `deserialize` function\n   * // value is undefined\n   *\n   * @param cls The BCS-deserializable class to deserialize the buffered bytes into.\n   *\n   * @returns the deserialized value of class type T\n   */\n  deserializeOption<T>(cls: Deserializable<T>): T | undefined {\n    const exists = this.deserializeBool();\n    return exists ? this.deserialize(cls) : undefined;\n  }\n\n  /**\n   * Deserializes an array of bytes.\n   *\n   * BCS layout for \"bytes\": bytes_length | bytes\n   * where bytes_length is a u32 integer encoded as a uleb128 integer, equal to the length of the bytes array.\n   */\n  deserializeBytes(): Uint8Array {\n    const len = this.deserializeUleb128AsU32();\n    return new Uint8Array(this.read(len));\n  }\n\n  /**\n   * Deserializes an array of bytes. The number of bytes to read is already known.\n   *\n   */\n  deserializeFixedBytes(len: number): Uint8Array {\n    return new Uint8Array(this.read(len));\n  }\n\n  /**\n   * Deserializes a boolean value.\n   *\n   * BCS layout for \"boolean\": One byte. \"0x01\" for true and \"0x00\" for false.\n   */\n  deserializeBool(): boolean {\n    const bool = new Uint8Array(this.read(1))[0];\n    if (bool !== 1 && bool !== 0) {\n      throw new Error(\"Invalid boolean value\");\n    }\n    return bool === 1;\n  }\n\n  /**\n   * Deserializes a uint8 number.\n   *\n   * BCS layout for \"uint8\": One byte. Binary format in little-endian representation.\n   */\n  deserializeU8(): Uint8 {\n    return new DataView(this.read(1)).getUint8(0);\n  }\n\n  /**\n   * Deserializes a uint16 number.\n   *\n   * BCS layout for \"uint16\": Two bytes. Binary format in little-endian representation.\n   * @example\n   * ```ts\n   * const deserializer = new Deserializer(new Uint8Array([0x34, 0x12]));\n   * assert(deserializer.deserializeU16() === 4660);\n   * ```\n   */\n  deserializeU16(): Uint16 {\n    return new DataView(this.read(2)).getUint16(0, true);\n  }\n\n  /**\n   * Deserializes a uint32 number.\n   *\n   * BCS layout for \"uint32\": Four bytes. Binary format in little-endian representation.\n   * @example\n   * ```ts\n   * const deserializer = new Deserializer(new Uint8Array([0x78, 0x56, 0x34, 0x12]));\n   * assert(deserializer.deserializeU32() === 305419896);\n   * ```\n   */\n  deserializeU32(): Uint32 {\n    return new DataView(this.read(4)).getUint32(0, true);\n  }\n\n  /**\n   * Deserializes a uint64 number.\n   *\n   * BCS layout for \"uint64\": Eight bytes. Binary format in little-endian representation.\n   * @example\n   * ```ts\n   * const deserializer = new Deserializer(new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));\n   * assert(deserializer.deserializeU64() === 1311768467750121216);\n   * ```\n   */\n  deserializeU64(): Uint64 {\n    const low = this.deserializeU32();\n    const high = this.deserializeU32();\n\n    // combine the two 32-bit values and return (little endian)\n    return BigInt((BigInt(high) << BigInt(32)) | BigInt(low));\n  }\n\n  /**\n   * Deserializes a uint128 number.\n   *\n   * BCS layout for \"uint128\": Sixteen bytes. Binary format in little-endian representation.\n   */\n  deserializeU128(): Uint128 {\n    const low = this.deserializeU64();\n    const high = this.deserializeU64();\n\n    // combine the two 64-bit values and return (little endian)\n    return BigInt((high << BigInt(64)) | low);\n  }\n\n  /**\n   * Deserializes a uint256 number.\n   *\n   * BCS layout for \"uint256\": Thirty-two bytes. Binary format in little-endian representation.\n   */\n  deserializeU256(): Uint256 {\n    const low = this.deserializeU128();\n    const high = this.deserializeU128();\n\n    // combine the two 128-bit values and return (little endian)\n    return BigInt((high << BigInt(128)) | low);\n  }\n\n  /**\n   * Deserializes a uleb128 encoded uint32 number.\n   *\n   * BCS use uleb128 encoding in two cases: (1) lengths of variable-length sequences and (2) tags of enum values\n   */\n  deserializeUleb128AsU32(): Uint32 {\n    let value: bigint = BigInt(0);\n    let shift = 0;\n\n    while (value < MAX_U32_NUMBER) {\n      const byte = this.deserializeU8();\n      value |= BigInt(byte & 0x7f) << BigInt(shift);\n\n      if ((byte & 0x80) === 0) {\n        break;\n      }\n      shift += 7;\n    }\n\n    if (value > MAX_U32_NUMBER) {\n      throw new Error(\"Overflow while parsing uleb128-encoded uint32 value\");\n    }\n\n    return Number(value);\n  }\n\n  /**\n   * Helper function that primarily exists to support alternative syntax for deserialization.\n   * That is, if we have a `const deserializer: new Deserializer(...)`, instead of having to use\n   * `MyClass.deserialize(deserializer)`, we can call `deserializer.deserialize(MyClass)`.\n   *\n   * @example const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));\n   * const value = deserializer.deserialize(MyClass); // where MyClass has a `deserialize` function\n   * // value is now an instance of MyClass\n   * // equivalent to `const value = MyClass.deserialize(deserializer)`\n   * @param cls The BCS-deserializable class to deserialize the buffered bytes into.\n   *\n   * @returns the deserialized value of class type T\n   */\n  deserialize<T>(cls: Deserializable<T>): T {\n    // NOTE: `deserialize` in `cls.deserialize(this)` here is a static method defined in `cls`,\n    // It is separate from the `deserialize` instance method defined here in Deserializer.\n    return cls.deserialize(this);\n  }\n\n  /**\n   * Deserializes an array of BCS Deserializable values given an existing Deserializer\n   * instance with a loaded byte buffer.\n   *\n   * @param cls The BCS-deserializable class to deserialize the buffered bytes into.\n   * @example\n   * // serialize a vector of addresses\n   * const addresses = new Array<AccountAddress>(\n   *   AccountAddress.from(\"0x1\"),\n   *   AccountAddress.from(\"0x2\"),\n   *   AccountAddress.from(\"0xa\"),\n   *   AccountAddress.from(\"0xb\"),\n   * );\n   * const serializer = new Serializer();\n   * serializer.serializeVector(addresses);\n   * const serializedBytes = serializer.toUint8Array();\n   *\n   * // deserialize the bytes into an array of addresses\n   * const deserializer = new Deserializer(serializedBytes);\n   * const deserializedAddresses = deserializer.deserializeVector(AccountAddress);\n   * // deserializedAddresses is now an array of AccountAddress instances\n   * @returns an array of deserialized values of type T\n   */\n  deserializeVector<T>(cls: Deserializable<T>): Array<T> {\n    const length = this.deserializeUleb128AsU32();\n    const vector = new Array<T>();\n    for (let i = 0; i < length; i += 1) {\n      vector.push(this.deserialize(cls));\n    }\n    return vector;\n  }\n}\n"], "mappings": ";AAiBO,IAAMA,CAAA,GAAN,MAAmB;EAKxBC,YAAYC,CAAA,EAAkB;IAE5B,KAAKC,MAAA,GAAS,IAAIC,WAAA,CAAYF,CAAA,CAAKG,MAAM,GACzC,IAAIC,UAAA,CAAW,KAAKH,MAAM,EAAEI,GAAA,CAAIL,CAAA,EAAM,CAAC,GACvC,KAAKM,MAAA,GAAS,CAChB;EAAA;EAEQC,KAAKP,CAAA,EAA6B;IACxC,IAAI,KAAKM,MAAA,GAASN,CAAA,GAAS,KAAKC,MAAA,CAAOO,UAAA,EACrC,MAAM,IAAIC,KAAA,CAAM,8BAA8B;IAGhD,IAAMC,CAAA,GAAQ,KAAKT,MAAA,CAAOU,KAAA,CAAM,KAAKL,MAAA,EAAQ,KAAKA,MAAA,GAASN,CAAM;IACjE,YAAKM,MAAA,IAAUN,CAAA,EACRU,CACT;EAAA;EAeAE,eAAA,EAAyB;IACvB,IAAMZ,CAAA,GAAQ,KAAKa,gBAAA,CAAiB;IAEpC,OADoB,IAAIC,WAAA,CAAY,EACjBC,MAAA,CAAOf,CAAK,CACjC;EAAA;EAcAgB,qBAAA,EAA2C;IAEzC,OADe,KAAKC,eAAA,CAAgB,IACpB,KAAKL,cAAA,CAAe,IAAI,MAC1C;EAAA;EAoBAM,kBAAqBlB,CAAA,EAAuC;IAE1D,OADe,KAAKiB,eAAA,CAAgB,IACpB,KAAKE,WAAA,CAAYnB,CAAG,IAAI,MAC1C;EAAA;EAQAa,iBAAA,EAA+B;IAC7B,IAAMb,CAAA,GAAM,KAAKoB,uBAAA,CAAwB;IACzC,OAAO,IAAIhB,UAAA,CAAW,KAAKG,IAAA,CAAKP,CAAG,CAAC,CACtC;EAAA;EAMAqB,sBAAsBrB,CAAA,EAAyB;IAC7C,OAAO,IAAII,UAAA,CAAW,KAAKG,IAAA,CAAKP,CAAG,CAAC,CACtC;EAAA;EAOAiB,gBAAA,EAA2B;IACzB,IAAMjB,CAAA,GAAO,IAAII,UAAA,CAAW,KAAKG,IAAA,CAAK,CAAC,CAAC,EAAE,CAAC;IAC3C,IAAIP,CAAA,KAAS,KAAKA,CAAA,KAAS,GACzB,MAAM,IAAIS,KAAA,CAAM,uBAAuB;IAEzC,OAAOT,CAAA,KAAS,CAClB;EAAA;EAOAsB,cAAA,EAAuB;IACrB,OAAO,IAAIC,QAAA,CAAS,KAAKhB,IAAA,CAAK,CAAC,CAAC,EAAEiB,QAAA,CAAS,CAAC,CAC9C;EAAA;EAYAC,eAAA,EAAyB;IACvB,OAAO,IAAIF,QAAA,CAAS,KAAKhB,IAAA,CAAK,CAAC,CAAC,EAAEmB,SAAA,CAAU,GAAG,EAAI,CACrD;EAAA;EAYAC,eAAA,EAAyB;IACvB,OAAO,IAAIJ,QAAA,CAAS,KAAKhB,IAAA,CAAK,CAAC,CAAC,EAAEqB,SAAA,CAAU,GAAG,EAAI,CACrD;EAAA;EAYAC,eAAA,EAAyB;IACvB,IAAM7B,CAAA,GAAM,KAAK2B,cAAA,CAAe;MAC1BjB,CAAA,GAAO,KAAKiB,cAAA,CAAe;IAGjC,OAAOG,MAAA,CAAQA,MAAA,CAAOpB,CAAI,KAAKoB,MAAA,CAAO,EAAE,IAAKA,MAAA,CAAO9B,CAAG,CAAC,CAC1D;EAAA;EAOA+B,gBAAA,EAA2B;IACzB,IAAM/B,CAAA,GAAM,KAAK6B,cAAA,CAAe;MAC1BnB,CAAA,GAAO,KAAKmB,cAAA,CAAe;IAGjC,OAAOC,MAAA,CAAQpB,CAAA,IAAQoB,MAAA,CAAO,EAAE,IAAK9B,CAAG,CAC1C;EAAA;EAOAgC,gBAAA,EAA2B;IACzB,IAAMhC,CAAA,GAAM,KAAK+B,eAAA,CAAgB;MAC3BrB,CAAA,GAAO,KAAKqB,eAAA,CAAgB;IAGlC,OAAOD,MAAA,CAAQpB,CAAA,IAAQoB,MAAA,CAAO,GAAG,IAAK9B,CAAG,CAC3C;EAAA;EAOAoB,wBAAA,EAAkC;IAChC,IAAIpB,CAAA,GAAgB8B,MAAA,CAAO,CAAC;MACxBpB,CAAA,GAAQ;IAEZ,OAAOV,CAAA,GAAQiC,CAAA,GAAgB;MAC7B,IAAMC,CAAA,GAAO,KAAKZ,aAAA,CAAc;MAGhC,IAFAtB,CAAA,IAAS8B,MAAA,CAAOI,CAAA,GAAO,GAAI,KAAKJ,MAAA,CAAOpB,CAAK,GAEvC,EAAAwB,CAAA,GAAO,MACV;MAEFxB,CAAA,IAAS,CACX;IAAA;IAEA,IAAIV,CAAA,GAAQiC,CAAA,EACV,MAAM,IAAIxB,KAAA,CAAM,qDAAqD;IAGvE,OAAO0B,MAAA,CAAOnC,CAAK,CACrB;EAAA;EAeAmB,YAAenB,CAAA,EAA2B;IAGxC,OAAOA,CAAA,CAAImB,WAAA,CAAY,IAAI,CAC7B;EAAA;EAyBAiB,kBAAqBpC,CAAA,EAAkC;IACrD,IAAMU,CAAA,GAAS,KAAKU,uBAAA,CAAwB;MACtCc,CAAA,GAAS,IAAIG,KAAA;IACnB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI5B,CAAA,EAAQ4B,CAAA,IAAK,GAC/BJ,CAAA,CAAOK,IAAA,CAAK,KAAKpB,WAAA,CAAYnB,CAAG,CAAC;IAEnC,OAAOkC,CACT;EAAA;AACF;AAAA,SAAApC,CAAA,IAAA0C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}