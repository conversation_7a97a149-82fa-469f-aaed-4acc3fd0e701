{"ast": null, "code": "/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */BigInt(32);\nfunction fromBig(n, le = false) {\n  if (le) return {\n    h: Number(n & U32_MASK64),\n    l: Number(n >> _32n & U32_MASK64)\n  };\n  return {\n    h: Number(n >> _32n & U32_MASK64) | 0,\n    l: Number(n & U32_MASK64) | 0\n  };\n}\nfunction split(lst, le = false) {\n  const len = lst.length;\n  let Ah = new Uint32Array(len);\n  let Al = new Uint32Array(len);\n  for (let i = 0; i < len; i++) {\n    const {\n      h,\n      l\n    } = fromBig(lst[i], le);\n    [Ah[i], <PERSON>[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\nconst toBig = (h, l) => BigInt(h >>> 0) << _32n | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => h << 32 - s | l >>> s;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => h >>> s | l << 32 - s;\nconst rotrSL = (h, l, s) => h << 32 - s | l >>> s;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => h << 64 - s | l >>> s - 32;\nconst rotrBL = (h, l, s) => h >>> s - 32 | l << 64 - s;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => h << s | l >>> 32 - s;\nconst rotlSL = (h, l, s) => l << s | h >>> 32 - s;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => l << s - 32 | h >>> 64 - s;\nconst rotlBL = (h, l, s) => h << s - 32 | l >>> 64 - s;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return {\n    h: Ah + Bh + (l / 2 ** 32 | 0) | 0,\n    l: l | 0\n  };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => Ah + Bh + Ch + (low / 2 ** 32 | 0) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => Ah + Bh + Ch + Dh + (low / 2 ** 32 | 0) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => Ah + Bh + Ch + Dh + Eh + (low / 2 ** 32 | 0) | 0;\n// prettier-ignore\nexport { add, add3H, add3L, add4H, add4L, add5H, add5L, fromBig, rotlBH, rotlBL, rotlSH, rotlSL, rotr32H, rotr32L, rotrBH, rotrBL, rotrSH, rotrSL, shrSH, shrSL, split, toBig };\n// prettier-ignore\nconst u64 = {\n  fromBig,\n  split,\n  toBig,\n  shrSH,\n  shrSL,\n  rotrSH,\n  rotrSL,\n  rotrBH,\n  rotrBL,\n  rotr32H,\n  rotr32L,\n  rotlSH,\n  rotlSL,\n  rotlBH,\n  rotlBL,\n  add,\n  add3L,\n  add3H,\n  add4L,\n  add4H,\n  add5H,\n  add5L\n};\nexport default u64;", "map": {"version": 3, "names": ["U32_MASK64", "BigInt", "_32n", "fromBig", "n", "le", "h", "Number", "l", "split", "lst", "len", "length", "Ah", "Uint32Array", "Al", "i", "toBig", "shrSH", "_l", "s", "shrSL", "rotrSH", "rotrSL", "rotrBH", "rotrBL", "rotr32H", "_h", "rotr32L", "rotlSH", "rotlSL", "rotlBH", "rotlBL", "add", "Bh", "Bl", "add3L", "Cl", "add3H", "low", "Ch", "add4L", "Dl", "add4H", "Dh", "add5L", "El", "add5H", "Eh", "u64"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\_u64.ts"], "sourcesContent": ["/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  const len = lst.length;\n  let Ah = new Uint32Array(len);\n  let Al = new Uint32Array(len);\n  for (let i = 0; i < len; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  add, add3H, add3L, add4H, add4L, add5H, add5L, fromBig, rotlBH, rotlBL, rotlSH, rotlSL, rotr32H, rotr32L, rotrBH, rotrBL, rotrSH, rotrSL, shrSH, shrSL, split, toBig\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n"], "mappings": "AAAA;;;;;AAKA,MAAMA,UAAU,GAAG,eAAgBC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACtD,MAAMC,IAAI,GAAG,eAAgBD,MAAM,CAAC,EAAE,CAAC;AAEvC,SAASE,OAAOA,CACdC,CAAS,EACTC,EAAE,GAAG,KAAK;EAKV,IAAIA,EAAE,EAAE,OAAO;IAAEC,CAAC,EAAEC,MAAM,CAACH,CAAC,GAAGJ,UAAU,CAAC;IAAEQ,CAAC,EAAED,MAAM,CAAEH,CAAC,IAAIF,IAAI,GAAIF,UAAU;EAAC,CAAE;EACjF,OAAO;IAAEM,CAAC,EAAEC,MAAM,CAAEH,CAAC,IAAIF,IAAI,GAAIF,UAAU,CAAC,GAAG,CAAC;IAAEQ,CAAC,EAAED,MAAM,CAACH,CAAC,GAAGJ,UAAU,CAAC,GAAG;EAAC,CAAE;AACnF;AAEA,SAASS,KAAKA,CAACC,GAAa,EAAEL,EAAE,GAAG,KAAK;EACtC,MAAMM,GAAG,GAAGD,GAAG,CAACE,MAAM;EACtB,IAAIC,EAAE,GAAG,IAAIC,WAAW,CAACH,GAAG,CAAC;EAC7B,IAAII,EAAE,GAAG,IAAID,WAAW,CAACH,GAAG,CAAC;EAC7B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,EAAEK,CAAC,EAAE,EAAE;IAC5B,MAAM;MAAEV,CAAC;MAAEE;IAAC,CAAE,GAAGL,OAAO,CAACO,GAAG,CAACM,CAAC,CAAC,EAAEX,EAAE,CAAC;IACpC,CAACQ,EAAE,CAACG,CAAC,CAAC,EAAED,EAAE,CAACC,CAAC,CAAC,CAAC,GAAG,CAACV,CAAC,EAAEE,CAAC,CAAC;EACzB;EACA,OAAO,CAACK,EAAE,EAAEE,EAAE,CAAC;AACjB;AAEA,MAAME,KAAK,GAAGA,CAACX,CAAS,EAAEE,CAAS,KAAcP,MAAM,CAACK,CAAC,KAAK,CAAC,CAAC,IAAIJ,IAAI,GAAID,MAAM,CAACO,CAAC,KAAK,CAAC,CAAC;AAC3F;AACA,MAAMU,KAAK,GAAGA,CAACZ,CAAS,EAAEa,EAAU,EAAEC,CAAS,KAAad,CAAC,KAAKc,CAAC;AACnE,MAAMC,KAAK,GAAGA,CAACf,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,IAAK,EAAE,GAAGc,CAAE,GAAKZ,CAAC,KAAKY,CAAE;AACtF;AACA,MAAME,MAAM,GAAGA,CAAChB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,KAAKc,CAAC,GAAKZ,CAAC,IAAK,EAAE,GAAGY,CAAG;AACvF,MAAMG,MAAM,GAAGA,CAACjB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,IAAK,EAAE,GAAGc,CAAE,GAAKZ,CAAC,KAAKY,CAAE;AACvF;AACA,MAAMI,MAAM,GAAGA,CAAClB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,IAAK,EAAE,GAAGc,CAAE,GAAKZ,CAAC,KAAMY,CAAC,GAAG,EAAI;AAC9F,MAAMK,MAAM,GAAGA,CAACnB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,KAAMc,CAAC,GAAG,EAAG,GAAKZ,CAAC,IAAK,EAAE,GAAGY,CAAG;AAC9F;AACA,MAAMM,OAAO,GAAGA,CAACC,EAAU,EAAEnB,CAAS,KAAaA,CAAC;AACpD,MAAMoB,OAAO,GAAGA,CAACtB,CAAS,EAAEa,EAAU,KAAab,CAAC;AACpD;AACA,MAAMuB,MAAM,GAAGA,CAACvB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,IAAIc,CAAC,GAAKZ,CAAC,KAAM,EAAE,GAAGY,CAAG;AACvF,MAAMU,MAAM,GAAGA,CAACxB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcZ,CAAC,IAAIY,CAAC,GAAKd,CAAC,KAAM,EAAE,GAAGc,CAAG;AACvF;AACA,MAAMW,MAAM,GAAGA,CAACzB,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcZ,CAAC,IAAKY,CAAC,GAAG,EAAG,GAAKd,CAAC,KAAM,EAAE,GAAGc,CAAG;AAC9F,MAAMY,MAAM,GAAGA,CAAC1B,CAAS,EAAEE,CAAS,EAAEY,CAAS,KAAcd,CAAC,IAAKc,CAAC,GAAG,EAAG,GAAKZ,CAAC,KAAM,EAAE,GAAGY,CAAG;AAE9F;AACA;AACA,SAASa,GAAGA,CACVpB,EAAU,EACVE,EAAU,EACVmB,EAAU,EACVC,EAAU;EAKV,MAAM3B,CAAC,GAAG,CAACO,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC;EACjC,OAAO;IAAE7B,CAAC,EAAGO,EAAE,GAAGqB,EAAE,IAAK1B,CAAC,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;IAAEA,CAAC,EAAEA,CAAC,GAAG;EAAC,CAAE;AAC7D;AACA;AACA,MAAM4B,KAAK,GAAGA,CAACrB,EAAU,EAAEoB,EAAU,EAAEE,EAAU,KAAa,CAACtB,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC;AAClG,MAAMC,KAAK,GAAGA,CAACC,GAAW,EAAE1B,EAAU,EAAEqB,EAAU,EAAEM,EAAU,KAC3D3B,EAAE,GAAGqB,EAAE,GAAGM,EAAE,IAAKD,GAAG,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;AAC5C,MAAME,KAAK,GAAGA,CAAC1B,EAAU,EAAEoB,EAAU,EAAEE,EAAU,EAAEK,EAAU,KAC3D,CAAC3B,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC,IAAIK,EAAE,KAAK,CAAC,CAAC;AACnD,MAAMC,KAAK,GAAGA,CAACJ,GAAW,EAAE1B,EAAU,EAAEqB,EAAU,EAAEM,EAAU,EAAEI,EAAU,KACvE/B,EAAE,GAAGqB,EAAE,GAAGM,EAAE,GAAGI,EAAE,IAAKL,GAAG,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;AACjD,MAAMM,KAAK,GAAGA,CAAC9B,EAAU,EAAEoB,EAAU,EAAEE,EAAU,EAAEK,EAAU,EAAEI,EAAU,KACvE,CAAC/B,EAAE,KAAK,CAAC,KAAKoB,EAAE,KAAK,CAAC,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC,IAAIK,EAAE,KAAK,CAAC,CAAC,IAAII,EAAE,KAAK,CAAC,CAAC;AAChE,MAAMC,KAAK,GAAGA,CAACR,GAAW,EAAE1B,EAAU,EAAEqB,EAAU,EAAEM,EAAU,EAAEI,EAAU,EAAEI,EAAU,KACnFnC,EAAE,GAAGqB,EAAE,GAAGM,EAAE,GAAGI,EAAE,GAAGI,EAAE,IAAKT,GAAG,GAAG,CAAC,IAAI,EAAE,GAAI,CAAC,CAAC,GAAI,CAAC;AAEtD;AACA,SACEN,GAAG,EAAEK,KAAK,EAAEF,KAAK,EAAEO,KAAK,EAAEF,KAAK,EAAEM,KAAK,EAAEF,KAAK,EAAE1C,OAAO,EAAE4B,MAAM,EAAEC,MAAM,EAAEH,MAAM,EAAEC,MAAM,EAAEJ,OAAO,EAAEE,OAAO,EAAEJ,MAAM,EAAEC,MAAM,EAAEH,MAAM,EAAEC,MAAM,EAAEL,KAAK,EAAEG,KAAK,EAAEZ,KAAK,EAAEQ,KAAK;AAEtK;AACA,MAAMgC,GAAG,GAAkpC;EACzpC9C,OAAO;EAAEM,KAAK;EAAEQ,KAAK;EACrBC,KAAK;EAAEG,KAAK;EACZC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAC9BC,OAAO;EAAEE,OAAO;EAChBC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAC9BC,GAAG;EAAEG,KAAK;EAAEE,KAAK;EAAEG,KAAK;EAAEE,KAAK;EAAEI,KAAK;EAAEF;CACzC;AACD,eAAeI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}