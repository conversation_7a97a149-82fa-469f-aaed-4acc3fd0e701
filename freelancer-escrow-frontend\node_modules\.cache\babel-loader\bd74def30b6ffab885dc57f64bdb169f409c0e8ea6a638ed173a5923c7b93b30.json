{"ast": null, "code": "var n = new Map();\nfunction m(r, e, t) {\n  return async (...s) => {\n    if (n.has(e)) {\n      let {\n        value: i,\n        timestamp: u\n      } = n.get(e);\n      if (t === void 0 || Date.now() - u <= t) return i;\n    }\n    let a = await r(...s);\n    return n.set(e, {\n      value: a,\n      timestamp: Date.now()\n    }), a;\n  };\n}\nfunction o(r, e, t) {\n  return (...s) => {\n    if (n.has(e)) {\n      let {\n        value: i,\n        timestamp: u\n      } = n.get(e);\n      if (t === void 0 || Date.now() - u <= t) return i;\n    }\n    let a = r(...s);\n    return n.set(e, {\n      value: a,\n      timestamp: Date.now()\n    }), a;\n  };\n}\nexport { m as a, o as b };", "map": {"version": 3, "names": ["n", "Map", "m", "r", "e", "t", "s", "has", "value", "i", "timestamp", "u", "get", "Date", "now", "a", "set", "o", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\utils\\memoize.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * The global cache Map shared across all functions.  Must keep care to ensure that the\n * cache keys are unique across all functions.\n */\nconst cache = new Map<string, { value: any; timestamp: number }>();\n\n/**\n * A memoize high order function to cache async function response\n *\n * @param func An async function to cache the result of\n * @param key The provided cache key\n * @param ttlMs time-to-live in milliseconds for cached data\n * @returns the cached or latest result\n */\nexport function memoizeAsync<T>(\n  func: (...args: any[]) => Promise<T>,\n  key: string,\n  ttlMs?: number,\n): (...args: any[]) => Promise<T> {\n  return async (...args: any[]) => {\n    // Check if the cached result exists and is within TTL\n    if (cache.has(key)) {\n      const { value, timestamp } = cache.get(key)!;\n      if (ttlMs === undefined || Date.now() - timestamp <= ttlMs) {\n        return value;\n      }\n    }\n\n    // If not cached or TTL expired, compute the result\n    const result = await func(...args);\n\n    // Cache the result with a timestamp\n    cache.set(key, { value: result, timestamp: Date.now() });\n\n    return result;\n  };\n}\n\n/**\n * A memoize high order function to cache function response\n *\n * @param func A function to cache the result of\n * @param key The provided cache key\n * @param ttlMs time-to-live in milliseconds for cached data\n * @returns the cached or latest result\n */\nexport function memoize<T>(func: (...args: any[]) => T, key: string, ttlMs?: number): (...args: any[]) => T {\n  return (...args: any[]) => {\n    // Check if the cached result exists and is within TTL\n    if (cache.has(key)) {\n      const { value, timestamp } = cache.get(key)!;\n      if (ttlMs === undefined || Date.now() - timestamp <= ttlMs) {\n        return value;\n      }\n    }\n\n    // If not cached or TTL expired, compute the result\n    const result = func(...args);\n\n    // Cache the result with a timestamp\n    cache.set(key, { value: result, timestamp: Date.now() });\n\n    return result;\n  };\n}\n"], "mappings": "AAOA,IAAMA,CAAA,GAAQ,IAAIC,GAAA;AAUX,SAASC,EACdC,CAAA,EACAC,CAAA,EACAC,CAAA,EACgC;EAChC,OAAO,UAAUC,CAAA,KAAgB;IAE/B,IAAIN,CAAA,CAAMO,GAAA,CAAIH,CAAG,GAAG;MAClB,IAAM;QAAEI,KAAA,EAAAC,CAAA;QAAOC,SAAA,EAAAC;MAAU,IAAIX,CAAA,CAAMY,GAAA,CAAIR,CAAG;MAC1C,IAAIC,CAAA,KAAU,UAAaQ,IAAA,CAAKC,GAAA,CAAI,IAAIH,CAAA,IAAaN,CAAA,EACnD,OAAOI,CAEX;IAAA;IAGA,IAAMM,CAAA,GAAS,MAAMZ,CAAA,CAAK,GAAGG,CAAI;IAGjC,OAAAN,CAAA,CAAMgB,GAAA,CAAIZ,CAAA,EAAK;MAAEI,KAAA,EAAOO,CAAA;MAAQL,SAAA,EAAWG,IAAA,CAAKC,GAAA,CAAI;IAAE,CAAC,GAEhDC,CACT;EAAA,CACF;AAAA;AAUO,SAASE,EAAWd,CAAA,EAA6BC,CAAA,EAAaC,CAAA,EAAuC;EAC1G,OAAO,IAAIC,CAAA,KAAgB;IAEzB,IAAIN,CAAA,CAAMO,GAAA,CAAIH,CAAG,GAAG;MAClB,IAAM;QAAEI,KAAA,EAAAC,CAAA;QAAOC,SAAA,EAAAC;MAAU,IAAIX,CAAA,CAAMY,GAAA,CAAIR,CAAG;MAC1C,IAAIC,CAAA,KAAU,UAAaQ,IAAA,CAAKC,GAAA,CAAI,IAAIH,CAAA,IAAaN,CAAA,EACnD,OAAOI,CAEX;IAAA;IAGA,IAAMM,CAAA,GAASZ,CAAA,CAAK,GAAGG,CAAI;IAG3B,OAAAN,CAAA,CAAMgB,GAAA,CAAIZ,CAAA,EAAK;MAAEI,KAAA,EAAOO,CAAA;MAAQL,SAAA,EAAWG,IAAA,CAAKC,GAAA,CAAI;IAAE,CAAC,GAEhDC,CACT;EAAA,CACF;AAAA;AAAA,SAAAb,CAAA,IAAAa,CAAA,EAAAE,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}