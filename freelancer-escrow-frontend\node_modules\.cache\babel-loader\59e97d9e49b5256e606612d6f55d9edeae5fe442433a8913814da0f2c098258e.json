{"ast": null, "code": "import { d as p } from \"./chunk-VTKPSYKA.mjs\";\nimport { d as s } from \"./chunk-AYKZA676.mjs\";\nimport { a as u, b as y } from \"./chunk-QVWBJJRF.mjs\";\nimport { b as a } from \"./chunk-6ZQWPHLV.mjs\";\nimport { b as i } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b as g } from \"./chunk-BF46IXHH.mjs\";\nvar h = class c {\n  constructor(e) {\n    this.signingScheme = 2;\n    let {\n      privateKey: r,\n      address: t\n    } = e;\n    this.privateKey = r, this.publicKey = new u(r.publicKey()), this.accountAddress = t ? g.from(t) : this.publicKey.authKey().derivedAddress();\n  }\n  static generate(e = {}) {\n    let {\n        scheme: r = 0\n      } = e,\n      t;\n    switch (r) {\n      case 0:\n        t = i.generate();\n        break;\n      case 2:\n        t = a.generate();\n        break;\n      default:\n        throw new Error(`Unsupported signature scheme ${r}`);\n    }\n    return new c({\n      privateKey: t\n    });\n  }\n  static fromDerivationPath(e) {\n    let {\n        scheme: r = 0,\n        path: t,\n        mnemonic: o\n      } = e,\n      n;\n    switch (r) {\n      case 0:\n        n = i.fromDerivationPath(t, o);\n        break;\n      case 2:\n        n = a.fromDerivationPath(t, o);\n        break;\n      default:\n        throw new Error(`Unsupported signature scheme ${r}`);\n    }\n    return new c({\n      privateKey: n\n    });\n  }\n  verifySignature(e) {\n    return this.publicKey.verifySignature(e);\n  }\n  signWithAuthenticator(e) {\n    return new s(this.publicKey, this.sign(e));\n  }\n  signTransactionWithAuthenticator(e) {\n    return new s(this.publicKey, this.signTransaction(e));\n  }\n  sign(e) {\n    return new y(this.privateKey.sign(e));\n  }\n  signTransaction(e) {\n    return this.sign(p(e));\n  }\n};\nexport { h as a };", "map": {"version": 3, "names": ["h", "c", "constructor", "e", "signingScheme", "privateKey", "r", "address", "t", "public<PERSON>ey", "u", "accountAddress", "g", "from", "auth<PERSON><PERSON>", "derivedAddress", "generate", "scheme", "i", "a", "Error", "fromDerivationPath", "path", "mnemonic", "o", "n", "verifySignature", "signWithAuthenticator", "s", "sign", "signTransactionWithAuthenticator", "signTransaction", "y", "p"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\account\\SingleKeyAccount.ts"], "sourcesContent": ["import { AccountAuthenticator<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"../transactions/authenticator/account\";\nimport { type HexInput, SigningScheme, SigningSchemeInput } from \"../types\";\nimport { AccountAddress, AccountAddressInput } from \"../core/accountAddress\";\nimport { AnyPublicKey, AnySignature, Ed25519PrivateKey, PrivateKey, Secp256k1PrivateKey } from \"../core/crypto\";\nimport type { Account } from \"./Account\";\nimport { generateSigningMessageForTransaction } from \"../transactions/transactionBuilder/signingMessage\";\nimport { AnyRawTransaction } from \"../transactions/types\";\n\nexport interface SingleKeySignerConstructorArgs {\n  privateKey: PrivateKey;\n  address?: AccountAddressInput;\n}\n\nexport interface SingleKeySignerGenerateArgs {\n  scheme?: SigningSchemeInput;\n}\n\nexport type SingleKeySignerFromDerivationPathArgs = SingleKeySignerGenerateArgs & {\n  path: string;\n  mnemonic: string;\n};\n\nexport interface VerifySingleKeySignatureArgs {\n  message: HexInput;\n  signature: AnySignature;\n}\n\n/**\n * Signer implementation for the SingleKey authentication scheme.\n * This extends a SingleKeyAccount by adding signing capabilities through a valid private key.\n * Currently, the only supported signature schemes are Ed25519 and Secp256k1.\n *\n * Note: Generating a signer instance does not create the account on-chain.\n */\nexport class SingleKeyAccount implements Account {\n  /**\n   * Private key associated with the account\n   */\n  readonly privateKey: PrivateKey;\n\n  readonly publicKey: AnyPublicKey;\n\n  readonly accountAddress: AccountAddress;\n\n  readonly signingScheme = SigningScheme.SingleKey;\n\n  // region Constructors\n\n  constructor(args: SingleKeySignerConstructorArgs) {\n    const { privateKey, address } = args;\n    this.privateKey = privateKey;\n    this.publicKey = new AnyPublicKey(privateKey.publicKey());\n    this.accountAddress = address ? AccountAddress.from(address) : this.publicKey.authKey().derivedAddress();\n  }\n\n  /**\n   * Derives an account from a randomly generated private key.\n   * Default generation is using an Ed25519 key\n   * @returns Account with the given signature scheme\n   */\n  static generate(args: SingleKeySignerGenerateArgs = {}) {\n    const { scheme = SigningSchemeInput.Ed25519 } = args;\n    let privateKey: PrivateKey;\n    switch (scheme) {\n      case SigningSchemeInput.Ed25519:\n        privateKey = Ed25519PrivateKey.generate();\n        break;\n      case SigningSchemeInput.Secp256k1Ecdsa:\n        privateKey = Secp256k1PrivateKey.generate();\n        break;\n      default:\n        throw new Error(`Unsupported signature scheme ${scheme}`);\n    }\n    return new SingleKeyAccount({ privateKey });\n  }\n\n  /**\n   * Derives an account with bip44 path and mnemonics,\n   * Default to using an Ed25519 signature scheme.\n   *\n   * @param args.scheme The signature scheme to derive the private key with\n   * @param args.path the BIP44 derive hardened path (e.g. m/44'/637'/0'/0'/0') for Ed25519,\n   * or non-hardened path (e.g. m/44'/637'/0'/0/0) for secp256k1\n   * Detailed description: {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}\n   * @param args.mnemonic the mnemonic seed phrase of the account\n   */\n  static fromDerivationPath(args: SingleKeySignerFromDerivationPathArgs) {\n    const { scheme = SigningSchemeInput.Ed25519, path, mnemonic } = args;\n    let privateKey: PrivateKey;\n    switch (scheme) {\n      case SigningSchemeInput.Ed25519:\n        privateKey = Ed25519PrivateKey.fromDerivationPath(path, mnemonic);\n        break;\n      case SigningSchemeInput.Secp256k1Ecdsa:\n        privateKey = Secp256k1PrivateKey.fromDerivationPath(path, mnemonic);\n        break;\n      default:\n        throw new Error(`Unsupported signature scheme ${scheme}`);\n    }\n    return new SingleKeyAccount({ privateKey });\n  }\n\n  // endregion\n\n  // region Account\n\n  /**\n   * Verify the given message and signature with the public key.\n   *\n   * @param args.message raw message data in HexInput format\n   * @param args.signature signed message Signature\n   * @returns\n   */\n  verifySignature(args: VerifySingleKeySignatureArgs): boolean {\n    return this.publicKey.verifySignature(args);\n  }\n\n  /**\n   * Sign a message using the account's private key.\n   * @param message the signing message, as binary input\n   * @return the AccountAuthenticator containing the signature, together with the account's public key\n   */\n  signWithAuthenticator(message: HexInput): AccountAuthenticatorSingleKey {\n    return new AccountAuthenticatorSingleKey(this.publicKey, this.sign(message));\n  }\n\n  /**\n   * Sign a transaction using the account's private key.\n   * @param transaction the raw transaction\n   * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key\n   */\n  signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorSingleKey {\n    return new AccountAuthenticatorSingleKey(this.publicKey, this.signTransaction(transaction));\n  }\n\n  /**\n   * Sign the given message using the account's private key.\n   * @param message in HexInput format\n   * @returns Signature\n   */\n  sign(message: HexInput): AnySignature {\n    return new AnySignature(this.privateKey.sign(message));\n  }\n\n  /**\n   * Sign the given transaction using the account's private key.\n   * @param transaction the transaction to be signed\n   * @returns Signature\n   */\n  signTransaction(transaction: AnyRawTransaction): AnySignature {\n    return this.sign(generateSigningMessageForTransaction(transaction));\n  }\n\n  // endregion\n}\n"], "mappings": ";;;;;;AAkCO,IAAMA,CAAA,GAAN,MAAMC,CAAoC;EAc/CC,YAAYC,CAAA,EAAsC;IAJlD,KAASC,aAAA,GAAgB;IAKvB,IAAM;MAAEC,UAAA,EAAAC,CAAA;MAAYC,OAAA,EAAAC;IAAQ,IAAIL,CAAA;IAChC,KAAKE,UAAA,GAAaC,CAAA,EAClB,KAAKG,SAAA,GAAY,IAAIC,CAAA,CAAaJ,CAAA,CAAWG,SAAA,CAAU,CAAC,GACxD,KAAKE,cAAA,GAAiBH,CAAA,GAAUI,CAAA,CAAeC,IAAA,CAAKL,CAAO,IAAI,KAAKC,SAAA,CAAUK,OAAA,CAAQ,EAAEC,cAAA,CAAe,CACzG;EAAA;EAOA,OAAOC,SAASb,CAAA,GAAoC,CAAC,GAAG;IACtD,IAAM;QAAEc,MAAA,EAAAX,CAAA;MAAoC,IAAIH,CAAA;MAC5CK,CAAA;IACJ,QAAQF,CAAA;MACN;QACEE,CAAA,GAAaU,CAAA,CAAkBF,QAAA,CAAS;QACxC;MACF;QACER,CAAA,GAAaW,CAAA,CAAoBH,QAAA,CAAS;QAC1C;MACF;QACE,MAAM,IAAII,KAAA,CAAM,gCAAgCd,CAAM,EAAE,CAC5D;IAAA;IACA,OAAO,IAAIL,CAAA,CAAiB;MAAEI,UAAA,EAAAG;IAAW,CAAC,CAC5C;EAAA;EAYA,OAAOa,mBAAmBlB,CAAA,EAA6C;IACrE,IAAM;QAAEc,MAAA,EAAAX,CAAA;QAAqCgB,IAAA,EAAAd,CAAA;QAAMe,QAAA,EAAAC;MAAS,IAAIrB,CAAA;MAC5DsB,CAAA;IACJ,QAAQnB,CAAA;MACN;QACEmB,CAAA,GAAaP,CAAA,CAAkBG,kBAAA,CAAmBb,CAAA,EAAMgB,CAAQ;QAChE;MACF;QACEC,CAAA,GAAaN,CAAA,CAAoBE,kBAAA,CAAmBb,CAAA,EAAMgB,CAAQ;QAClE;MACF;QACE,MAAM,IAAIJ,KAAA,CAAM,gCAAgCd,CAAM,EAAE,CAC5D;IAAA;IACA,OAAO,IAAIL,CAAA,CAAiB;MAAEI,UAAA,EAAAoB;IAAW,CAAC,CAC5C;EAAA;EAaAC,gBAAgBvB,CAAA,EAA6C;IAC3D,OAAO,KAAKM,SAAA,CAAUiB,eAAA,CAAgBvB,CAAI,CAC5C;EAAA;EAOAwB,sBAAsBxB,CAAA,EAAkD;IACtE,OAAO,IAAIyB,CAAA,CAA8B,KAAKnB,SAAA,EAAW,KAAKoB,IAAA,CAAK1B,CAAO,CAAC,CAC7E;EAAA;EAOA2B,iCAAiC3B,CAAA,EAA+D;IAC9F,OAAO,IAAIyB,CAAA,CAA8B,KAAKnB,SAAA,EAAW,KAAKsB,eAAA,CAAgB5B,CAAW,CAAC,CAC5F;EAAA;EAOA0B,KAAK1B,CAAA,EAAiC;IACpC,OAAO,IAAI6B,CAAA,CAAa,KAAK3B,UAAA,CAAWwB,IAAA,CAAK1B,CAAO,CAAC,CACvD;EAAA;EAOA4B,gBAAgB5B,CAAA,EAA8C;IAC5D,OAAO,KAAK0B,IAAA,CAAKI,CAAA,CAAqC9B,CAAW,CAAC,CACpE;EAAA;AAGF;AAAA,SAAAH,CAAA,IAAAmB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}