{"ast": null, "code": "import { b as p } from \"./chunk-362JBD4O.mjs\";\nasync function r(e) {\n  let {\n      aptosConfig: t,\n      overrides: s,\n      params: n,\n      contentType: o,\n      acceptType: i,\n      path: a,\n      originMethod: R,\n      type: d\n    } = e,\n    u = t.getRequestUrl(d);\n  return p({\n    url: u,\n    method: \"GET\",\n    originMethod: R,\n    path: a,\n    contentType: o,\n    acceptType: i,\n    params: n,\n    overrides: {\n      ...t.clientConfig,\n      ...s\n    }\n  }, t, e.type);\n}\nasync function m(e) {\n  let {\n    aptosConfig: t\n  } = e;\n  return r({\n    ...e,\n    type: \"Fullnode\",\n    overrides: {\n      ...t.clientConfig,\n      ...t.fullnodeConfig,\n      ...e.overrides,\n      HEADERS: {\n        ...t.clientConfig?.HEADERS,\n        ...t.fullnodeConfig?.HEADERS\n      }\n    }\n  });\n}\nasync function y(e) {\n  return r({\n    ...e,\n    type: \"Pepper\"\n  });\n}\nasync function A(e) {\n  let t = [],\n    s,\n    n = e.params;\n  do {\n    let o = await r({\n      type: \"Fullnode\",\n      aptosConfig: e.aptosConfig,\n      originMethod: e.originMethod,\n      path: e.path,\n      params: n,\n      overrides: e.overrides\n    });\n    s = o.headers[\"x-aptos-cursor\"], delete o.headers, t.push(...o.data), n.start = s;\n  } while (s != null);\n  return t;\n}\nexport { r as a, m as b, y as c, A as d };", "map": {"version": 3, "names": ["r", "e", "aptosConfig", "t", "overrides", "s", "params", "n", "contentType", "o", "acceptType", "i", "path", "a", "originMethod", "R", "type", "d", "u", "getRequestUrl", "p", "url", "method", "clientConfig", "m", "fullnodeConfig", "HEADERS", "y", "A", "headers", "push", "data", "start", "b", "c"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\client\\get.ts"], "sourcesContent": ["import { AptosConfig } from \"../api/aptosConfig\";\nimport { aptosRequest } from \"./core\";\nimport { AptosResponse } from \"./types\";\nimport { AnyNumber, ClientConfig, MimeType } from \"../types\";\nimport { AptosApiType } from \"../utils/const\";\n\nexport type GetRequestOptions = {\n  /**\n   * The config for the API client\n   */\n  aptosConfig: AptosConfig;\n  /**\n   * The type of API endpoint to call e.g. fullnode, indexer, etc\n   */\n  type: AptosApiType;\n  /**\n   * The name of the API method\n   */\n  originMethod: string;\n  /**\n   * The URL path to the API method\n   */\n  path: string;\n  /**\n   * The content type of the request body\n   */\n  contentType?: MimeType;\n  /**\n   * The accepted content type of the response of the API\n   */\n  acceptType?: MimeType;\n  /**\n   * The query parameters for the request\n   */\n  params?: Record<string, string | AnyNumber | boolean | undefined>;\n  /**\n   * Specific client overrides for this request to override aptosConfig\n   */\n  overrides?: ClientConfig;\n};\n\nexport type GetAptosRequestOptions = Omit<GetRequestOptions, \"type\">;\n\n/**\n * Main function to do a Get request\n *\n * @param options GetRequestOptions\n * @returns\n */\nexport async function get<Req extends {}, Res extends {}>(\n  options: GetRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig, overrides, params, contentType, acceptType, path, originMethod, type } = options;\n  const url = aptosConfig.getRequestUrl(type);\n\n  return aptosRequest<Req, Res>(\n    {\n      url,\n      method: \"GET\",\n      originMethod,\n      path,\n      contentType,\n      acceptType,\n      params,\n      overrides: {\n        ...aptosConfig.clientConfig,\n        ...overrides,\n      },\n    },\n    aptosConfig,\n    options.type,\n  );\n}\n\nexport async function getAptosFullNode<Req extends {}, Res extends {}>(\n  options: GetAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n\n  return get<Req, Res>({\n    ...options,\n    type: AptosApiType.FULLNODE,\n    overrides: {\n      ...aptosConfig.clientConfig,\n      ...aptosConfig.fullnodeConfig,\n      ...options.overrides,\n      HEADERS: { ...aptosConfig.clientConfig?.HEADERS, ...aptosConfig.fullnodeConfig?.HEADERS },\n    },\n  });\n}\n\n/**\n * Makes a get request to the pepper service\n *\n * @param options GetAptosRequestOptions\n * @returns AptosResponse\n */\nexport async function getAptosPepperService<Req extends {}, Res extends {}>(\n  options: GetAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  return get<Req, Res>({ ...options, type: AptosApiType.PEPPER });\n}\n\n/// This function is a helper for paginating using a function wrapping an API\nexport async function paginateWithCursor<Req extends Record<string, any>, Res extends Array<{}>>(\n  options: GetAptosRequestOptions,\n): Promise<Res> {\n  const out: any[] = [];\n  let cursor: string | undefined;\n  const requestParams = options.params as { start?: string; limit?: number };\n  do {\n    // eslint-disable-next-line no-await-in-loop\n    const response = await get<Req, Res>({\n      type: AptosApiType.FULLNODE,\n      aptosConfig: options.aptosConfig,\n      originMethod: options.originMethod,\n      path: options.path,\n      params: requestParams,\n      overrides: options.overrides,\n    });\n    /**\n     * the cursor is a \"state key\" from the API perspective. Client\n     * should not need to \"care\" what it represents but just use it\n     * to query the next chunk of data.\n     */\n    cursor = response.headers[\"x-aptos-cursor\"];\n    // Now that we have the cursor (if any), we remove the headers before\n    // adding these to the output of this function.\n    delete response.headers;\n    out.push(...response.data);\n    requestParams.start = cursor;\n  } while (cursor !== null && cursor !== undefined);\n  return out as Res;\n}\n"], "mappings": ";AAiDA,eAAsBA,EACpBC,CAAA,EACkC;EAClC,IAAM;MAAEC,WAAA,EAAAC,CAAA;MAAaC,SAAA,EAAAC,CAAA;MAAWC,MAAA,EAAAC,CAAA;MAAQC,WAAA,EAAAC,CAAA;MAAaC,UAAA,EAAAC,CAAA;MAAYC,IAAA,EAAAC,CAAA;MAAMC,YAAA,EAAAC,CAAA;MAAcC,IAAA,EAAAC;IAAK,IAAIhB,CAAA;IACxFiB,CAAA,GAAMf,CAAA,CAAYgB,aAAA,CAAcF,CAAI;EAE1C,OAAOG,CAAA,CACL;IACEC,GAAA,EAAAH,CAAA;IACAI,MAAA,EAAQ;IACRR,YAAA,EAAAC,CAAA;IACAH,IAAA,EAAAC,CAAA;IACAL,WAAA,EAAAC,CAAA;IACAC,UAAA,EAAAC,CAAA;IACAL,MAAA,EAAAC,CAAA;IACAH,SAAA,EAAW;MACT,GAAGD,CAAA,CAAYoB,YAAA;MACf,GAAGlB;IACL;EACF,GACAF,CAAA,EACAF,CAAA,CAAQe,IACV,CACF;AAAA;AAEA,eAAsBQ,EACpBvB,CAAA,EACkC;EAClC,IAAM;IAAEC,WAAA,EAAAC;EAAY,IAAIF,CAAA;EAExB,OAAOD,CAAA,CAAc;IACnB,GAAGC,CAAA;IACHe,IAAA;IACAZ,SAAA,EAAW;MACT,GAAGD,CAAA,CAAYoB,YAAA;MACf,GAAGpB,CAAA,CAAYsB,cAAA;MACf,GAAGxB,CAAA,CAAQG,SAAA;MACXsB,OAAA,EAAS;QAAE,GAAGvB,CAAA,CAAYoB,YAAA,EAAcG,OAAA;QAAS,GAAGvB,CAAA,CAAYsB,cAAA,EAAgBC;MAAQ;IAC1F;EACF,CAAC,CACH;AAAA;AAQA,eAAsBC,EACpB1B,CAAA,EACkC;EAClC,OAAOD,CAAA,CAAc;IAAE,GAAGC,CAAA;IAASe,IAAA;EAA0B,CAAC,CAChE;AAAA;AAGA,eAAsBY,EACpB3B,CAAA,EACc;EACd,IAAME,CAAA,GAAa,EAAC;IAChBE,CAAA;IACEE,CAAA,GAAgBN,CAAA,CAAQK,MAAA;EAC9B,GAAG;IAED,IAAMG,CAAA,GAAW,MAAMT,CAAA,CAAc;MACnCgB,IAAA;MACAd,WAAA,EAAaD,CAAA,CAAQC,WAAA;MACrBY,YAAA,EAAcb,CAAA,CAAQa,YAAA;MACtBF,IAAA,EAAMX,CAAA,CAAQW,IAAA;MACdN,MAAA,EAAQC,CAAA;MACRH,SAAA,EAAWH,CAAA,CAAQG;IACrB,CAAC;IAMDC,CAAA,GAASI,CAAA,CAASoB,OAAA,CAAQ,gBAAgB,GAG1C,OAAOpB,CAAA,CAASoB,OAAA,EAChB1B,CAAA,CAAI2B,IAAA,CAAK,GAAGrB,CAAA,CAASsB,IAAI,GACzBxB,CAAA,CAAcyB,KAAA,GAAQ3B,CACxB;EAAA,SAASA,CAAA,IAAW;EACpB,OAAOF,CACT;AAAA;AAAA,SAAAH,CAAA,IAAAa,CAAA,EAAAW,CAAA,IAAAS,CAAA,EAAAN,CAAA,IAAAO,CAAA,EAAAN,CAAA,IAAAX,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}