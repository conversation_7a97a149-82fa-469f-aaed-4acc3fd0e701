{"ast": null, "code": "import { a } from \"./chunk-LG7RJQ57.mjs\";\nimport { bytesToHex as s, hexToBytes as o } from \"@noble/hashes/utils\";\nvar l = (e => (e.TOO_SHORT = \"too_short\", e.INVALID_LENGTH = \"invalid_length\", e.INVALID_HEX_CHARS = \"invalid_hex_chars\", e))(l || {}),\n  i = class n {\n    constructor(t) {\n      this.data = t;\n    }\n    toUint8Array() {\n      return this.data;\n    }\n    toStringWithoutPrefix() {\n      return s(this.data);\n    }\n    toString() {\n      return `0x${this.toStringWithoutPrefix()}`;\n    }\n    static fromHexString(t) {\n      let r = t;\n      if (r.startsWith(\"0x\") && (r = r.slice(2)), r.length === 0) throw new a(\"Hex string is too short, must be at least 1 char long, excluding the optional leading 0x.\", \"too_short\");\n      if (r.length % 2 !== 0) throw new a(\"Hex string must be an even number of hex characters.\", \"invalid_length\");\n      try {\n        return new n(o(r));\n      } catch (e) {\n        throw new a(`Hex string contains invalid hex characters: ${e?.message}`, \"invalid_hex_chars\");\n      }\n    }\n    static fromHexInput(t) {\n      return t instanceof Uint8Array ? new n(t) : n.fromHexString(t);\n    }\n    static isValid(t) {\n      try {\n        return n.fromHexString(t), {\n          valid: !0\n        };\n      } catch (r) {\n        return {\n          valid: !1,\n          invalidReason: r?.invalidReason,\n          invalidReasonMessage: r?.message\n        };\n      }\n    }\n    equals(t) {\n      return this.data.length !== t.data.length ? !1 : this.data.every((r, e) => r === t.data[e]);\n    }\n  };\nexport { l as a, i as b };", "map": {"version": 3, "names": ["bytesToHex", "s", "hexToBytes", "o", "l", "e", "TOO_SHORT", "INVALID_LENGTH", "INVALID_HEX_CHARS", "i", "n", "constructor", "t", "data", "toUint8Array", "toStringWithoutPrefix", "toString", "fromHexString", "r", "startsWith", "slice", "length", "a", "message", "fromHexInput", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON>", "valid", "invalidReason", "invalidReasonMessage", "equals", "every", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\hex.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bytesToHex, hexToBytes } from \"@noble/hashes/utils\";\nimport { ParsingError, ParsingResult } from \"./common\";\nimport { HexInput } from \"../types\";\n\n/**\n * This enum is used to explain why parsing might have failed.\n */\nexport enum HexInvalidReason {\n  TOO_SHORT = \"too_short\",\n  INVALID_LENGTH = \"invalid_length\",\n  INVALID_HEX_CHARS = \"invalid_hex_chars\",\n}\n\n/**\n * NOTE: Do not use this class when working with account addresses, use AccountAddress.\n *\n * NOTE: When accepting hex data as input to a function, prefer to accept HexInput and\n * then use the static helper methods of this class to convert it into the desired\n * format. This enables the greatest flexibility for the developer.\n *\n * Hex is a helper class for working with hex data. Hex data, when represented as a\n * string, generally looks like this, for example: 0xaabbcc, 45cd32, etc.\n *\n * You might use this class like this:\n *\n * ```ts\n * getTransactionByHash(txnHash: HexInput): Promise<Transaction> {\n *   const txnHashString = Hex.fromHexInput(txnHash).toString();\n *   return await getTransactionByHashInner(txnHashString);\n * }\n * ```\n *\n * This call to `Hex.fromHexInput().toString()` converts the HexInput to a hex string\n * with a leading 0x prefix, regardless of what the input format was.\n *\n * These are some other ways to chain the functions together:\n * - `Hex.fromHexString({ hexInput: \"0x1f\" }).toUint8Array()`\n * - `new Hex([1, 3]).toStringWithoutPrefix()`\n */\nexport class Hex {\n  private readonly data: Uint8Array;\n\n  /**\n   * Create a new Hex instance from a Uint8Array.\n   *\n   * @param data Uint8Array\n   */\n  constructor(data: Uint8Array) {\n    this.data = data;\n  }\n\n  // ===\n  // Methods for representing an instance of Hex as other types.\n  // ===\n\n  /**\n   * Get the inner hex data. The inner data is already a Uint8Array so no conversion\n   * is taking place here, it just returns the inner data.\n   *\n   * @returns Hex data as Uint8Array\n   */\n  toUint8Array(): Uint8Array {\n    return this.data;\n  }\n\n  /**\n   * Get the hex data as a string without the 0x prefix.\n   *\n   * @returns Hex string without 0x prefix\n   */\n  toStringWithoutPrefix(): string {\n    return bytesToHex(this.data);\n  }\n\n  /**\n   * Get the hex data as a string with the 0x prefix.\n   *\n   * @returns Hex string with 0x prefix\n   */\n  toString(): string {\n    return `0x${this.toStringWithoutPrefix()}`;\n  }\n\n  // ===\n  // Methods for creating an instance of Hex from other types.\n  // ===\n\n  /**\n   * Static method to convert a hex string to Hex\n   *\n   * @param str A hex string, with or without the 0x prefix\n   *\n   * @returns Hex\n   */\n  static fromHexString(str: string): Hex {\n    let input = str;\n\n    if (input.startsWith(\"0x\")) {\n      input = input.slice(2);\n    }\n\n    if (input.length === 0) {\n      throw new ParsingError(\n        \"Hex string is too short, must be at least 1 char long, excluding the optional leading 0x.\",\n        HexInvalidReason.TOO_SHORT,\n      );\n    }\n\n    if (input.length % 2 !== 0) {\n      throw new ParsingError(\"Hex string must be an even number of hex characters.\", HexInvalidReason.INVALID_LENGTH);\n    }\n\n    try {\n      return new Hex(hexToBytes(input));\n    } catch (error: any) {\n      throw new ParsingError(\n        `Hex string contains invalid hex characters: ${error?.message}`,\n        HexInvalidReason.INVALID_HEX_CHARS,\n      );\n    }\n  }\n\n  /**\n   * Static method to convert an instance of HexInput to Hex\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   *\n   * @returns Hex\n   */\n  static fromHexInput(hexInput: HexInput): Hex {\n    if (hexInput instanceof Uint8Array) return new Hex(hexInput);\n    return Hex.fromHexString(hexInput);\n  }\n\n  // ===\n  // Methods for checking validity.\n  // ===\n\n  /**\n   * Check if the string is valid hex.\n   *\n   * @param str A hex string representing byte data.\n   *\n   * @returns valid = true if the string is valid, false if not. If the string is not\n   * valid, invalidReason and invalidReasonMessage will be set explaining why it is\n   * invalid.\n   */\n  static isValid(str: string): ParsingResult<HexInvalidReason> {\n    try {\n      Hex.fromHexString(str);\n      return { valid: true };\n    } catch (error: any) {\n      return {\n        valid: false,\n        invalidReason: error?.invalidReason,\n        invalidReasonMessage: error?.message,\n      };\n    }\n  }\n\n  /**\n   * Return whether Hex instances are equal. Hex instances are considered equal if\n   * their underlying byte data is identical.\n   *\n   * @param other The Hex instance to compare to.\n   * @returns true if the Hex instances are equal, false if not.\n   */\n  equals(other: Hex): boolean {\n    if (this.data.length !== other.data.length) return false;\n    return this.data.every((value, index) => value === other.data[index]);\n  }\n}\n"], "mappings": ";AAGA,SAASA,UAAA,IAAAC,CAAA,EAAYC,UAAA,IAAAC,CAAA,QAAkB;AAOhC,IAAKC,CAAA,IAAAC,CAAA,KACVA,CAAA,CAAAC,SAAA,GAAY,aACZD,CAAA,CAAAE,cAAA,GAAiB,kBACjBF,CAAA,CAAAG,iBAAA,GAAoB,qBAHVH,CAAA,GAAAD,CAAA;EAgCCK,CAAA,GAAN,MAAMC,CAAI;IAQfC,YAAYC,CAAA,EAAkB;MAC5B,KAAKC,IAAA,GAAOD,CACd;IAAA;IAYAE,aAAA,EAA2B;MACzB,OAAO,KAAKD,IACd;IAAA;IAOAE,sBAAA,EAAgC;MAC9B,OAAOd,CAAA,CAAW,KAAKY,IAAI,CAC7B;IAAA;IAOAG,SAAA,EAAmB;MACjB,OAAO,KAAK,KAAKD,qBAAA,CAAsB,CAAC,EAC1C;IAAA;IAaA,OAAOE,cAAcL,CAAA,EAAkB;MACrC,IAAIM,CAAA,GAAQN,CAAA;MAMZ,IAJIM,CAAA,CAAMC,UAAA,CAAW,IAAI,MACvBD,CAAA,GAAQA,CAAA,CAAME,KAAA,CAAM,CAAC,IAGnBF,CAAA,CAAMG,MAAA,KAAW,GACnB,MAAM,IAAIC,CAAA,CACR,6FACA,WACF;MAGF,IAAIJ,CAAA,CAAMG,MAAA,GAAS,MAAM,GACvB,MAAM,IAAIC,CAAA,CAAa,wDAAwD,gBAA+B;MAGhH,IAAI;QACF,OAAO,IAAIZ,CAAA,CAAIP,CAAA,CAAWe,CAAK,CAAC,CAClC;MAAA,SAASb,CAAA,EAAY;QACnB,MAAM,IAAIiB,CAAA,CACR,+CAA+CjB,CAAA,EAAOkB,OAAO,IAC7D,mBACF,CACF;MAAA;IACF;IASA,OAAOC,aAAaZ,CAAA,EAAyB;MAC3C,OAAIA,CAAA,YAAoBa,UAAA,GAAmB,IAAIf,CAAA,CAAIE,CAAQ,IACpDF,CAAA,CAAIO,aAAA,CAAcL,CAAQ,CACnC;IAAA;IAeA,OAAOc,QAAQd,CAAA,EAA8C;MAC3D,IAAI;QACF,OAAAF,CAAA,CAAIO,aAAA,CAAcL,CAAG,GACd;UAAEe,KAAA,EAAO;QAAK,CACvB;MAAA,SAAST,CAAA,EAAY;QACnB,OAAO;UACLS,KAAA,EAAO;UACPC,aAAA,EAAeV,CAAA,EAAOU,aAAA;UACtBC,oBAAA,EAAsBX,CAAA,EAAOK;QAC/B,CACF;MAAA;IACF;IASAO,OAAOlB,CAAA,EAAqB;MAC1B,OAAI,KAAKC,IAAA,CAAKQ,MAAA,KAAWT,CAAA,CAAMC,IAAA,CAAKQ,MAAA,GAAe,KAC5C,KAAKR,IAAA,CAAKkB,KAAA,CAAM,CAACb,CAAA,EAAOb,CAAA,KAAUa,CAAA,KAAUN,CAAA,CAAMC,IAAA,CAAKR,CAAK,CAAC,CACtE;IAAA;EACF;AAAA,SAAAD,CAAA,IAAAkB,CAAA,EAAAb,CAAA,IAAAuB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}