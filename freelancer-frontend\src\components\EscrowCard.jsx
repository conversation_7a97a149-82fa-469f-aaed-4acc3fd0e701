import React from 'react';
import { Calendar, DollarSign, User, Clock, CheckCircle, AlertTriangle, Play, Send, ThumbsUp } from 'lucide-react';
import aptosService from '../services/aptosService';

const EscrowCard = ({ escrow, userRole, onFund, onStart, onSubmit, onApprove, onViewDetails }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case aptosService.ESCROW_STATUS.CREATED:
        return 'bg-gray-100 text-gray-800';
      case aptosService.ESCROW_STATUS.FUNDED:
        return 'bg-blue-100 text-blue-800';
      case aptosService.ESCROW_STATUS.IN_PROGRESS:
        return 'bg-yellow-100 text-yellow-800';
      case aptosService.ESCROW_STATUS.SUBMITTED:
        return 'bg-purple-100 text-purple-800';
      case aptosService.ESCROW_STATUS.COMPLETED:
        return 'bg-green-100 text-green-800';
      case aptosService.ESCROW_STATUS.DISPUTED:
        return 'bg-red-100 text-red-800';
      case aptosService.ESCROW_STATUS.CANCELLED:
        return 'bg-gray-100 text-gray-800';
      case aptosService.ESCROW_STATUS.REFUNDED:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case aptosService.ESCROW_STATUS.COMPLETED:
        return <CheckCircle className="w-4 h-4" />;
      case aptosService.ESCROW_STATUS.DISPUTED:
        return <AlertTriangle className="w-4 h-4" />;
      case aptosService.ESCROW_STATUS.IN_PROGRESS:
        return <Clock className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getDaysUntilDeadline = () => {
    const now = new Date();
    const deadline = new Date(escrow.deadline);
    const diffTime = deadline - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const canPerformAction = () => {
    if (userRole === 'client') {
      return escrow.status === aptosService.ESCROW_STATUS.CREATED || 
             escrow.status === aptosService.ESCROW_STATUS.SUBMITTED;
    } else if (userRole === 'freelancer') {
      return escrow.status === aptosService.ESCROW_STATUS.FUNDED || 
             escrow.status === aptosService.ESCROW_STATUS.IN_PROGRESS;
    }
    return false;
  };

  const getActionButton = () => {
    if (userRole === 'client') {
      if (escrow.status === aptosService.ESCROW_STATUS.CREATED) {
        return (
          <button
            onClick={onFund}
            className="btn-primary flex items-center space-x-2"
          >
            <DollarSign className="w-4 h-4" />
            <span>Fund Project</span>
          </button>
        );
      } else if (escrow.status === aptosService.ESCROW_STATUS.SUBMITTED) {
        return (
          <button
            onClick={onApprove}
            className="btn-primary flex items-center space-x-2"
          >
            <ThumbsUp className="w-4 h-4" />
            <span>Approve & Pay</span>
          </button>
        );
      }
    } else if (userRole === 'freelancer') {
      if (escrow.status === aptosService.ESCROW_STATUS.FUNDED) {
        return (
          <button
            onClick={onStart}
            className="btn-primary flex items-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>Start Work</span>
          </button>
        );
      } else if (escrow.status === aptosService.ESCROW_STATUS.IN_PROGRESS) {
        return (
          <button
            onClick={onSubmit}
            className="btn-primary flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>Submit Work</span>
          </button>
        );
      }
    }
    return null;
  };

  const daysUntilDeadline = getDaysUntilDeadline();
  const isOverdue = daysUntilDeadline < 0;

  return (
    <div className="card p-6 hover:shadow-lg transition-shadow">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-secondary-900 mb-1">
            {escrow.title}
          </h3>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(escrow.status)}`}>
              <span className="flex items-center space-x-1">
                {getStatusIcon(escrow.status)}
                <span>{aptosService.STATUS_LABELS[escrow.status]}</span>
              </span>
            </span>
            <span className="text-xs text-secondary-500">#{escrow.id}</span>
          </div>
        </div>
        <div className="text-right">
          <p className="text-lg font-bold text-secondary-900">{escrow.amount} APT</p>
          <p className="text-xs text-secondary-500">+ {escrow.platform_fee} APT fee</p>
        </div>
      </div>

      {/* Description */}
      <p className="text-secondary-600 text-sm mb-4 line-clamp-2">
        {escrow.description}
      </p>

      {/* Details */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center text-sm text-secondary-600">
          <User className="w-4 h-4 mr-2" />
          <span className="font-medium">Category:</span>
          <span className="ml-1">{escrow.category}</span>
        </div>
        
        <div className="flex items-center text-sm text-secondary-600">
          <Calendar className="w-4 h-4 mr-2" />
          <span className="font-medium">Deadline:</span>
          <span className="ml-1">{formatDate(escrow.deadline)}</span>
          {isOverdue ? (
            <span className="ml-2 text-red-600 font-medium">(Overdue)</span>
          ) : daysUntilDeadline <= 3 ? (
            <span className="ml-2 text-yellow-600 font-medium">({daysUntilDeadline} days left)</span>
          ) : (
            <span className="ml-2 text-secondary-500">({daysUntilDeadline} days left)</span>
          )}
        </div>

        {userRole === 'client' && (
          <div className="flex items-center text-sm text-secondary-600">
            <User className="w-4 h-4 mr-2" />
            <span className="font-medium">Freelancer:</span>
            <span className="ml-1 font-mono text-xs">
              {escrow.freelancer.slice(0, 6)}...{escrow.freelancer.slice(-4)}
            </span>
          </div>
        )}

        {userRole === 'freelancer' && (
          <div className="flex items-center text-sm text-secondary-600">
            <User className="w-4 h-4 mr-2" />
            <span className="font-medium">Client:</span>
            <span className="ml-1 font-mono text-xs">
              {escrow.client.slice(0, 6)}...{escrow.client.slice(-4)}
            </span>
          </div>
        )}
      </div>

      {/* Timeline */}
      <div className="border-t border-secondary-200 pt-4 mb-4">
        <div className="flex justify-between text-xs text-secondary-500">
          <span>Created: {formatDate(escrow.created_at)}</span>
          {escrow.completed_at > 0 && (
            <span>Completed: {formatDate(escrow.completed_at)}</span>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <button
          onClick={onViewDetails}
          className="text-primary-600 hover:text-primary-700 text-sm font-medium"
        >
          View Details
        </button>
        
        {canPerformAction() && getActionButton()}
      </div>
    </div>
  );
};

export default EscrowCard;
