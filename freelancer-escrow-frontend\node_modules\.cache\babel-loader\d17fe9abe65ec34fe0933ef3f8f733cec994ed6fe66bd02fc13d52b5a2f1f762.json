{"ast": null, "code": "(function (nacl) {\n  'use strict';\n\n  // Ported in 2014 by <PERSON> and <PERSON>.\n  // Public domain.\n  //\n  // Implementation derived from TweetNaCl version 20140427.\n  // See for details: http://tweetnacl.cr.yp.to/\n  var gf = function (init) {\n    var i,\n      r = new Float64Array(16);\n    if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n    return r;\n  };\n\n  //  Pluggable, initialized in high-level API below.\n  var randombytes = function /* x, n */ () {\n    throw new Error('no PRNG');\n  };\n  var _0 = new Uint8Array(16);\n  var _9 = new Uint8Array(32);\n  _9[0] = 9;\n  var gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n  function ts64(x, i, h, l) {\n    x[i] = h >> 24 & 0xff;\n    x[i + 1] = h >> 16 & 0xff;\n    x[i + 2] = h >> 8 & 0xff;\n    x[i + 3] = h & 0xff;\n    x[i + 4] = l >> 24 & 0xff;\n    x[i + 5] = l >> 16 & 0xff;\n    x[i + 6] = l >> 8 & 0xff;\n    x[i + 7] = l & 0xff;\n  }\n  function vn(x, xi, y, yi, n) {\n    var i,\n      d = 0;\n    for (i = 0; i < n; i++) d |= x[xi + i] ^ y[yi + i];\n    return (1 & d - 1 >>> 8) - 1;\n  }\n  function crypto_verify_16(x, xi, y, yi) {\n    return vn(x, xi, y, yi, 16);\n  }\n  function crypto_verify_32(x, xi, y, yi) {\n    return vn(x, xi, y, yi, 32);\n  }\n  function core_salsa20(o, p, k, c) {\n    var j0 = c[0] & 0xff | (c[1] & 0xff) << 8 | (c[2] & 0xff) << 16 | (c[3] & 0xff) << 24,\n      j1 = k[0] & 0xff | (k[1] & 0xff) << 8 | (k[2] & 0xff) << 16 | (k[3] & 0xff) << 24,\n      j2 = k[4] & 0xff | (k[5] & 0xff) << 8 | (k[6] & 0xff) << 16 | (k[7] & 0xff) << 24,\n      j3 = k[8] & 0xff | (k[9] & 0xff) << 8 | (k[10] & 0xff) << 16 | (k[11] & 0xff) << 24,\n      j4 = k[12] & 0xff | (k[13] & 0xff) << 8 | (k[14] & 0xff) << 16 | (k[15] & 0xff) << 24,\n      j5 = c[4] & 0xff | (c[5] & 0xff) << 8 | (c[6] & 0xff) << 16 | (c[7] & 0xff) << 24,\n      j6 = p[0] & 0xff | (p[1] & 0xff) << 8 | (p[2] & 0xff) << 16 | (p[3] & 0xff) << 24,\n      j7 = p[4] & 0xff | (p[5] & 0xff) << 8 | (p[6] & 0xff) << 16 | (p[7] & 0xff) << 24,\n      j8 = p[8] & 0xff | (p[9] & 0xff) << 8 | (p[10] & 0xff) << 16 | (p[11] & 0xff) << 24,\n      j9 = p[12] & 0xff | (p[13] & 0xff) << 8 | (p[14] & 0xff) << 16 | (p[15] & 0xff) << 24,\n      j10 = c[8] & 0xff | (c[9] & 0xff) << 8 | (c[10] & 0xff) << 16 | (c[11] & 0xff) << 24,\n      j11 = k[16] & 0xff | (k[17] & 0xff) << 8 | (k[18] & 0xff) << 16 | (k[19] & 0xff) << 24,\n      j12 = k[20] & 0xff | (k[21] & 0xff) << 8 | (k[22] & 0xff) << 16 | (k[23] & 0xff) << 24,\n      j13 = k[24] & 0xff | (k[25] & 0xff) << 8 | (k[26] & 0xff) << 16 | (k[27] & 0xff) << 24,\n      j14 = k[28] & 0xff | (k[29] & 0xff) << 8 | (k[30] & 0xff) << 16 | (k[31] & 0xff) << 24,\n      j15 = c[12] & 0xff | (c[13] & 0xff) << 8 | (c[14] & 0xff) << 16 | (c[15] & 0xff) << 24;\n    var x0 = j0,\n      x1 = j1,\n      x2 = j2,\n      x3 = j3,\n      x4 = j4,\n      x5 = j5,\n      x6 = j6,\n      x7 = j7,\n      x8 = j8,\n      x9 = j9,\n      x10 = j10,\n      x11 = j11,\n      x12 = j12,\n      x13 = j13,\n      x14 = j14,\n      x15 = j15,\n      u;\n    for (var i = 0; i < 20; i += 2) {\n      u = x0 + x12 | 0;\n      x4 ^= u << 7 | u >>> 32 - 7;\n      u = x4 + x0 | 0;\n      x8 ^= u << 9 | u >>> 32 - 9;\n      u = x8 + x4 | 0;\n      x12 ^= u << 13 | u >>> 32 - 13;\n      u = x12 + x8 | 0;\n      x0 ^= u << 18 | u >>> 32 - 18;\n      u = x5 + x1 | 0;\n      x9 ^= u << 7 | u >>> 32 - 7;\n      u = x9 + x5 | 0;\n      x13 ^= u << 9 | u >>> 32 - 9;\n      u = x13 + x9 | 0;\n      x1 ^= u << 13 | u >>> 32 - 13;\n      u = x1 + x13 | 0;\n      x5 ^= u << 18 | u >>> 32 - 18;\n      u = x10 + x6 | 0;\n      x14 ^= u << 7 | u >>> 32 - 7;\n      u = x14 + x10 | 0;\n      x2 ^= u << 9 | u >>> 32 - 9;\n      u = x2 + x14 | 0;\n      x6 ^= u << 13 | u >>> 32 - 13;\n      u = x6 + x2 | 0;\n      x10 ^= u << 18 | u >>> 32 - 18;\n      u = x15 + x11 | 0;\n      x3 ^= u << 7 | u >>> 32 - 7;\n      u = x3 + x15 | 0;\n      x7 ^= u << 9 | u >>> 32 - 9;\n      u = x7 + x3 | 0;\n      x11 ^= u << 13 | u >>> 32 - 13;\n      u = x11 + x7 | 0;\n      x15 ^= u << 18 | u >>> 32 - 18;\n      u = x0 + x3 | 0;\n      x1 ^= u << 7 | u >>> 32 - 7;\n      u = x1 + x0 | 0;\n      x2 ^= u << 9 | u >>> 32 - 9;\n      u = x2 + x1 | 0;\n      x3 ^= u << 13 | u >>> 32 - 13;\n      u = x3 + x2 | 0;\n      x0 ^= u << 18 | u >>> 32 - 18;\n      u = x5 + x4 | 0;\n      x6 ^= u << 7 | u >>> 32 - 7;\n      u = x6 + x5 | 0;\n      x7 ^= u << 9 | u >>> 32 - 9;\n      u = x7 + x6 | 0;\n      x4 ^= u << 13 | u >>> 32 - 13;\n      u = x4 + x7 | 0;\n      x5 ^= u << 18 | u >>> 32 - 18;\n      u = x10 + x9 | 0;\n      x11 ^= u << 7 | u >>> 32 - 7;\n      u = x11 + x10 | 0;\n      x8 ^= u << 9 | u >>> 32 - 9;\n      u = x8 + x11 | 0;\n      x9 ^= u << 13 | u >>> 32 - 13;\n      u = x9 + x8 | 0;\n      x10 ^= u << 18 | u >>> 32 - 18;\n      u = x15 + x14 | 0;\n      x12 ^= u << 7 | u >>> 32 - 7;\n      u = x12 + x15 | 0;\n      x13 ^= u << 9 | u >>> 32 - 9;\n      u = x13 + x12 | 0;\n      x14 ^= u << 13 | u >>> 32 - 13;\n      u = x14 + x13 | 0;\n      x15 ^= u << 18 | u >>> 32 - 18;\n    }\n    x0 = x0 + j0 | 0;\n    x1 = x1 + j1 | 0;\n    x2 = x2 + j2 | 0;\n    x3 = x3 + j3 | 0;\n    x4 = x4 + j4 | 0;\n    x5 = x5 + j5 | 0;\n    x6 = x6 + j6 | 0;\n    x7 = x7 + j7 | 0;\n    x8 = x8 + j8 | 0;\n    x9 = x9 + j9 | 0;\n    x10 = x10 + j10 | 0;\n    x11 = x11 + j11 | 0;\n    x12 = x12 + j12 | 0;\n    x13 = x13 + j13 | 0;\n    x14 = x14 + j14 | 0;\n    x15 = x15 + j15 | 0;\n    o[0] = x0 >>> 0 & 0xff;\n    o[1] = x0 >>> 8 & 0xff;\n    o[2] = x0 >>> 16 & 0xff;\n    o[3] = x0 >>> 24 & 0xff;\n    o[4] = x1 >>> 0 & 0xff;\n    o[5] = x1 >>> 8 & 0xff;\n    o[6] = x1 >>> 16 & 0xff;\n    o[7] = x1 >>> 24 & 0xff;\n    o[8] = x2 >>> 0 & 0xff;\n    o[9] = x2 >>> 8 & 0xff;\n    o[10] = x2 >>> 16 & 0xff;\n    o[11] = x2 >>> 24 & 0xff;\n    o[12] = x3 >>> 0 & 0xff;\n    o[13] = x3 >>> 8 & 0xff;\n    o[14] = x3 >>> 16 & 0xff;\n    o[15] = x3 >>> 24 & 0xff;\n    o[16] = x4 >>> 0 & 0xff;\n    o[17] = x4 >>> 8 & 0xff;\n    o[18] = x4 >>> 16 & 0xff;\n    o[19] = x4 >>> 24 & 0xff;\n    o[20] = x5 >>> 0 & 0xff;\n    o[21] = x5 >>> 8 & 0xff;\n    o[22] = x5 >>> 16 & 0xff;\n    o[23] = x5 >>> 24 & 0xff;\n    o[24] = x6 >>> 0 & 0xff;\n    o[25] = x6 >>> 8 & 0xff;\n    o[26] = x6 >>> 16 & 0xff;\n    o[27] = x6 >>> 24 & 0xff;\n    o[28] = x7 >>> 0 & 0xff;\n    o[29] = x7 >>> 8 & 0xff;\n    o[30] = x7 >>> 16 & 0xff;\n    o[31] = x7 >>> 24 & 0xff;\n    o[32] = x8 >>> 0 & 0xff;\n    o[33] = x8 >>> 8 & 0xff;\n    o[34] = x8 >>> 16 & 0xff;\n    o[35] = x8 >>> 24 & 0xff;\n    o[36] = x9 >>> 0 & 0xff;\n    o[37] = x9 >>> 8 & 0xff;\n    o[38] = x9 >>> 16 & 0xff;\n    o[39] = x9 >>> 24 & 0xff;\n    o[40] = x10 >>> 0 & 0xff;\n    o[41] = x10 >>> 8 & 0xff;\n    o[42] = x10 >>> 16 & 0xff;\n    o[43] = x10 >>> 24 & 0xff;\n    o[44] = x11 >>> 0 & 0xff;\n    o[45] = x11 >>> 8 & 0xff;\n    o[46] = x11 >>> 16 & 0xff;\n    o[47] = x11 >>> 24 & 0xff;\n    o[48] = x12 >>> 0 & 0xff;\n    o[49] = x12 >>> 8 & 0xff;\n    o[50] = x12 >>> 16 & 0xff;\n    o[51] = x12 >>> 24 & 0xff;\n    o[52] = x13 >>> 0 & 0xff;\n    o[53] = x13 >>> 8 & 0xff;\n    o[54] = x13 >>> 16 & 0xff;\n    o[55] = x13 >>> 24 & 0xff;\n    o[56] = x14 >>> 0 & 0xff;\n    o[57] = x14 >>> 8 & 0xff;\n    o[58] = x14 >>> 16 & 0xff;\n    o[59] = x14 >>> 24 & 0xff;\n    o[60] = x15 >>> 0 & 0xff;\n    o[61] = x15 >>> 8 & 0xff;\n    o[62] = x15 >>> 16 & 0xff;\n    o[63] = x15 >>> 24 & 0xff;\n  }\n  function core_hsalsa20(o, p, k, c) {\n    var j0 = c[0] & 0xff | (c[1] & 0xff) << 8 | (c[2] & 0xff) << 16 | (c[3] & 0xff) << 24,\n      j1 = k[0] & 0xff | (k[1] & 0xff) << 8 | (k[2] & 0xff) << 16 | (k[3] & 0xff) << 24,\n      j2 = k[4] & 0xff | (k[5] & 0xff) << 8 | (k[6] & 0xff) << 16 | (k[7] & 0xff) << 24,\n      j3 = k[8] & 0xff | (k[9] & 0xff) << 8 | (k[10] & 0xff) << 16 | (k[11] & 0xff) << 24,\n      j4 = k[12] & 0xff | (k[13] & 0xff) << 8 | (k[14] & 0xff) << 16 | (k[15] & 0xff) << 24,\n      j5 = c[4] & 0xff | (c[5] & 0xff) << 8 | (c[6] & 0xff) << 16 | (c[7] & 0xff) << 24,\n      j6 = p[0] & 0xff | (p[1] & 0xff) << 8 | (p[2] & 0xff) << 16 | (p[3] & 0xff) << 24,\n      j7 = p[4] & 0xff | (p[5] & 0xff) << 8 | (p[6] & 0xff) << 16 | (p[7] & 0xff) << 24,\n      j8 = p[8] & 0xff | (p[9] & 0xff) << 8 | (p[10] & 0xff) << 16 | (p[11] & 0xff) << 24,\n      j9 = p[12] & 0xff | (p[13] & 0xff) << 8 | (p[14] & 0xff) << 16 | (p[15] & 0xff) << 24,\n      j10 = c[8] & 0xff | (c[9] & 0xff) << 8 | (c[10] & 0xff) << 16 | (c[11] & 0xff) << 24,\n      j11 = k[16] & 0xff | (k[17] & 0xff) << 8 | (k[18] & 0xff) << 16 | (k[19] & 0xff) << 24,\n      j12 = k[20] & 0xff | (k[21] & 0xff) << 8 | (k[22] & 0xff) << 16 | (k[23] & 0xff) << 24,\n      j13 = k[24] & 0xff | (k[25] & 0xff) << 8 | (k[26] & 0xff) << 16 | (k[27] & 0xff) << 24,\n      j14 = k[28] & 0xff | (k[29] & 0xff) << 8 | (k[30] & 0xff) << 16 | (k[31] & 0xff) << 24,\n      j15 = c[12] & 0xff | (c[13] & 0xff) << 8 | (c[14] & 0xff) << 16 | (c[15] & 0xff) << 24;\n    var x0 = j0,\n      x1 = j1,\n      x2 = j2,\n      x3 = j3,\n      x4 = j4,\n      x5 = j5,\n      x6 = j6,\n      x7 = j7,\n      x8 = j8,\n      x9 = j9,\n      x10 = j10,\n      x11 = j11,\n      x12 = j12,\n      x13 = j13,\n      x14 = j14,\n      x15 = j15,\n      u;\n    for (var i = 0; i < 20; i += 2) {\n      u = x0 + x12 | 0;\n      x4 ^= u << 7 | u >>> 32 - 7;\n      u = x4 + x0 | 0;\n      x8 ^= u << 9 | u >>> 32 - 9;\n      u = x8 + x4 | 0;\n      x12 ^= u << 13 | u >>> 32 - 13;\n      u = x12 + x8 | 0;\n      x0 ^= u << 18 | u >>> 32 - 18;\n      u = x5 + x1 | 0;\n      x9 ^= u << 7 | u >>> 32 - 7;\n      u = x9 + x5 | 0;\n      x13 ^= u << 9 | u >>> 32 - 9;\n      u = x13 + x9 | 0;\n      x1 ^= u << 13 | u >>> 32 - 13;\n      u = x1 + x13 | 0;\n      x5 ^= u << 18 | u >>> 32 - 18;\n      u = x10 + x6 | 0;\n      x14 ^= u << 7 | u >>> 32 - 7;\n      u = x14 + x10 | 0;\n      x2 ^= u << 9 | u >>> 32 - 9;\n      u = x2 + x14 | 0;\n      x6 ^= u << 13 | u >>> 32 - 13;\n      u = x6 + x2 | 0;\n      x10 ^= u << 18 | u >>> 32 - 18;\n      u = x15 + x11 | 0;\n      x3 ^= u << 7 | u >>> 32 - 7;\n      u = x3 + x15 | 0;\n      x7 ^= u << 9 | u >>> 32 - 9;\n      u = x7 + x3 | 0;\n      x11 ^= u << 13 | u >>> 32 - 13;\n      u = x11 + x7 | 0;\n      x15 ^= u << 18 | u >>> 32 - 18;\n      u = x0 + x3 | 0;\n      x1 ^= u << 7 | u >>> 32 - 7;\n      u = x1 + x0 | 0;\n      x2 ^= u << 9 | u >>> 32 - 9;\n      u = x2 + x1 | 0;\n      x3 ^= u << 13 | u >>> 32 - 13;\n      u = x3 + x2 | 0;\n      x0 ^= u << 18 | u >>> 32 - 18;\n      u = x5 + x4 | 0;\n      x6 ^= u << 7 | u >>> 32 - 7;\n      u = x6 + x5 | 0;\n      x7 ^= u << 9 | u >>> 32 - 9;\n      u = x7 + x6 | 0;\n      x4 ^= u << 13 | u >>> 32 - 13;\n      u = x4 + x7 | 0;\n      x5 ^= u << 18 | u >>> 32 - 18;\n      u = x10 + x9 | 0;\n      x11 ^= u << 7 | u >>> 32 - 7;\n      u = x11 + x10 | 0;\n      x8 ^= u << 9 | u >>> 32 - 9;\n      u = x8 + x11 | 0;\n      x9 ^= u << 13 | u >>> 32 - 13;\n      u = x9 + x8 | 0;\n      x10 ^= u << 18 | u >>> 32 - 18;\n      u = x15 + x14 | 0;\n      x12 ^= u << 7 | u >>> 32 - 7;\n      u = x12 + x15 | 0;\n      x13 ^= u << 9 | u >>> 32 - 9;\n      u = x13 + x12 | 0;\n      x14 ^= u << 13 | u >>> 32 - 13;\n      u = x14 + x13 | 0;\n      x15 ^= u << 18 | u >>> 32 - 18;\n    }\n    o[0] = x0 >>> 0 & 0xff;\n    o[1] = x0 >>> 8 & 0xff;\n    o[2] = x0 >>> 16 & 0xff;\n    o[3] = x0 >>> 24 & 0xff;\n    o[4] = x5 >>> 0 & 0xff;\n    o[5] = x5 >>> 8 & 0xff;\n    o[6] = x5 >>> 16 & 0xff;\n    o[7] = x5 >>> 24 & 0xff;\n    o[8] = x10 >>> 0 & 0xff;\n    o[9] = x10 >>> 8 & 0xff;\n    o[10] = x10 >>> 16 & 0xff;\n    o[11] = x10 >>> 24 & 0xff;\n    o[12] = x15 >>> 0 & 0xff;\n    o[13] = x15 >>> 8 & 0xff;\n    o[14] = x15 >>> 16 & 0xff;\n    o[15] = x15 >>> 24 & 0xff;\n    o[16] = x6 >>> 0 & 0xff;\n    o[17] = x6 >>> 8 & 0xff;\n    o[18] = x6 >>> 16 & 0xff;\n    o[19] = x6 >>> 24 & 0xff;\n    o[20] = x7 >>> 0 & 0xff;\n    o[21] = x7 >>> 8 & 0xff;\n    o[22] = x7 >>> 16 & 0xff;\n    o[23] = x7 >>> 24 & 0xff;\n    o[24] = x8 >>> 0 & 0xff;\n    o[25] = x8 >>> 8 & 0xff;\n    o[26] = x8 >>> 16 & 0xff;\n    o[27] = x8 >>> 24 & 0xff;\n    o[28] = x9 >>> 0 & 0xff;\n    o[29] = x9 >>> 8 & 0xff;\n    o[30] = x9 >>> 16 & 0xff;\n    o[31] = x9 >>> 24 & 0xff;\n  }\n  function crypto_core_salsa20(out, inp, k, c) {\n    core_salsa20(out, inp, k, c);\n  }\n  function crypto_core_hsalsa20(out, inp, k, c) {\n    core_hsalsa20(out, inp, k, c);\n  }\n  var sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n  // \"expand 32-byte k\"\n\n  function crypto_stream_salsa20_xor(c, cpos, m, mpos, b, n, k) {\n    var z = new Uint8Array(16),\n      x = new Uint8Array(64);\n    var u, i;\n    for (i = 0; i < 16; i++) z[i] = 0;\n    for (i = 0; i < 8; i++) z[i] = n[i];\n    while (b >= 64) {\n      crypto_core_salsa20(x, z, k, sigma);\n      for (i = 0; i < 64; i++) c[cpos + i] = m[mpos + i] ^ x[i];\n      u = 1;\n      for (i = 8; i < 16; i++) {\n        u = u + (z[i] & 0xff) | 0;\n        z[i] = u & 0xff;\n        u >>>= 8;\n      }\n      b -= 64;\n      cpos += 64;\n      mpos += 64;\n    }\n    if (b > 0) {\n      crypto_core_salsa20(x, z, k, sigma);\n      for (i = 0; i < b; i++) c[cpos + i] = m[mpos + i] ^ x[i];\n    }\n    return 0;\n  }\n  function crypto_stream_salsa20(c, cpos, b, n, k) {\n    var z = new Uint8Array(16),\n      x = new Uint8Array(64);\n    var u, i;\n    for (i = 0; i < 16; i++) z[i] = 0;\n    for (i = 0; i < 8; i++) z[i] = n[i];\n    while (b >= 64) {\n      crypto_core_salsa20(x, z, k, sigma);\n      for (i = 0; i < 64; i++) c[cpos + i] = x[i];\n      u = 1;\n      for (i = 8; i < 16; i++) {\n        u = u + (z[i] & 0xff) | 0;\n        z[i] = u & 0xff;\n        u >>>= 8;\n      }\n      b -= 64;\n      cpos += 64;\n    }\n    if (b > 0) {\n      crypto_core_salsa20(x, z, k, sigma);\n      for (i = 0; i < b; i++) c[cpos + i] = x[i];\n    }\n    return 0;\n  }\n  function crypto_stream(c, cpos, d, n, k) {\n    var s = new Uint8Array(32);\n    crypto_core_hsalsa20(s, n, k, sigma);\n    var sn = new Uint8Array(8);\n    for (var i = 0; i < 8; i++) sn[i] = n[i + 16];\n    return crypto_stream_salsa20(c, cpos, d, sn, s);\n  }\n  function crypto_stream_xor(c, cpos, m, mpos, d, n, k) {\n    var s = new Uint8Array(32);\n    crypto_core_hsalsa20(s, n, k, sigma);\n    var sn = new Uint8Array(8);\n    for (var i = 0; i < 8; i++) sn[i] = n[i + 16];\n    return crypto_stream_salsa20_xor(c, cpos, m, mpos, d, sn, s);\n  }\n\n  /*\n  * Port of Andrew Moon's Poly1305-donna-16. Public domain.\n  * https://github.com/floodyberry/poly1305-donna\n  */\n\n  var poly1305 = function (key) {\n    this.buffer = new Uint8Array(16);\n    this.r = new Uint16Array(10);\n    this.h = new Uint16Array(10);\n    this.pad = new Uint16Array(8);\n    this.leftover = 0;\n    this.fin = 0;\n    var t0, t1, t2, t3, t4, t5, t6, t7;\n    t0 = key[0] & 0xff | (key[1] & 0xff) << 8;\n    this.r[0] = t0 & 0x1fff;\n    t1 = key[2] & 0xff | (key[3] & 0xff) << 8;\n    this.r[1] = (t0 >>> 13 | t1 << 3) & 0x1fff;\n    t2 = key[4] & 0xff | (key[5] & 0xff) << 8;\n    this.r[2] = (t1 >>> 10 | t2 << 6) & 0x1f03;\n    t3 = key[6] & 0xff | (key[7] & 0xff) << 8;\n    this.r[3] = (t2 >>> 7 | t3 << 9) & 0x1fff;\n    t4 = key[8] & 0xff | (key[9] & 0xff) << 8;\n    this.r[4] = (t3 >>> 4 | t4 << 12) & 0x00ff;\n    this.r[5] = t4 >>> 1 & 0x1ffe;\n    t5 = key[10] & 0xff | (key[11] & 0xff) << 8;\n    this.r[6] = (t4 >>> 14 | t5 << 2) & 0x1fff;\n    t6 = key[12] & 0xff | (key[13] & 0xff) << 8;\n    this.r[7] = (t5 >>> 11 | t6 << 5) & 0x1f81;\n    t7 = key[14] & 0xff | (key[15] & 0xff) << 8;\n    this.r[8] = (t6 >>> 8 | t7 << 8) & 0x1fff;\n    this.r[9] = t7 >>> 5 & 0x007f;\n    this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n    this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n    this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n    this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n    this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n    this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n    this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n    this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n  };\n  poly1305.prototype.blocks = function (m, mpos, bytes) {\n    var hibit = this.fin ? 0 : 1 << 11;\n    var t0, t1, t2, t3, t4, t5, t6, t7, c;\n    var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n    var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n    var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n    while (bytes >= 16) {\n      t0 = m[mpos + 0] & 0xff | (m[mpos + 1] & 0xff) << 8;\n      h0 += t0 & 0x1fff;\n      t1 = m[mpos + 2] & 0xff | (m[mpos + 3] & 0xff) << 8;\n      h1 += (t0 >>> 13 | t1 << 3) & 0x1fff;\n      t2 = m[mpos + 4] & 0xff | (m[mpos + 5] & 0xff) << 8;\n      h2 += (t1 >>> 10 | t2 << 6) & 0x1fff;\n      t3 = m[mpos + 6] & 0xff | (m[mpos + 7] & 0xff) << 8;\n      h3 += (t2 >>> 7 | t3 << 9) & 0x1fff;\n      t4 = m[mpos + 8] & 0xff | (m[mpos + 9] & 0xff) << 8;\n      h4 += (t3 >>> 4 | t4 << 12) & 0x1fff;\n      h5 += t4 >>> 1 & 0x1fff;\n      t5 = m[mpos + 10] & 0xff | (m[mpos + 11] & 0xff) << 8;\n      h6 += (t4 >>> 14 | t5 << 2) & 0x1fff;\n      t6 = m[mpos + 12] & 0xff | (m[mpos + 13] & 0xff) << 8;\n      h7 += (t5 >>> 11 | t6 << 5) & 0x1fff;\n      t7 = m[mpos + 14] & 0xff | (m[mpos + 15] & 0xff) << 8;\n      h8 += (t6 >>> 8 | t7 << 8) & 0x1fff;\n      h9 += t7 >>> 5 | hibit;\n      c = 0;\n      d0 = c;\n      d0 += h0 * r0;\n      d0 += h1 * (5 * r9);\n      d0 += h2 * (5 * r8);\n      d0 += h3 * (5 * r7);\n      d0 += h4 * (5 * r6);\n      c = d0 >>> 13;\n      d0 &= 0x1fff;\n      d0 += h5 * (5 * r5);\n      d0 += h6 * (5 * r4);\n      d0 += h7 * (5 * r3);\n      d0 += h8 * (5 * r2);\n      d0 += h9 * (5 * r1);\n      c += d0 >>> 13;\n      d0 &= 0x1fff;\n      d1 = c;\n      d1 += h0 * r1;\n      d1 += h1 * r0;\n      d1 += h2 * (5 * r9);\n      d1 += h3 * (5 * r8);\n      d1 += h4 * (5 * r7);\n      c = d1 >>> 13;\n      d1 &= 0x1fff;\n      d1 += h5 * (5 * r6);\n      d1 += h6 * (5 * r5);\n      d1 += h7 * (5 * r4);\n      d1 += h8 * (5 * r3);\n      d1 += h9 * (5 * r2);\n      c += d1 >>> 13;\n      d1 &= 0x1fff;\n      d2 = c;\n      d2 += h0 * r2;\n      d2 += h1 * r1;\n      d2 += h2 * r0;\n      d2 += h3 * (5 * r9);\n      d2 += h4 * (5 * r8);\n      c = d2 >>> 13;\n      d2 &= 0x1fff;\n      d2 += h5 * (5 * r7);\n      d2 += h6 * (5 * r6);\n      d2 += h7 * (5 * r5);\n      d2 += h8 * (5 * r4);\n      d2 += h9 * (5 * r3);\n      c += d2 >>> 13;\n      d2 &= 0x1fff;\n      d3 = c;\n      d3 += h0 * r3;\n      d3 += h1 * r2;\n      d3 += h2 * r1;\n      d3 += h3 * r0;\n      d3 += h4 * (5 * r9);\n      c = d3 >>> 13;\n      d3 &= 0x1fff;\n      d3 += h5 * (5 * r8);\n      d3 += h6 * (5 * r7);\n      d3 += h7 * (5 * r6);\n      d3 += h8 * (5 * r5);\n      d3 += h9 * (5 * r4);\n      c += d3 >>> 13;\n      d3 &= 0x1fff;\n      d4 = c;\n      d4 += h0 * r4;\n      d4 += h1 * r3;\n      d4 += h2 * r2;\n      d4 += h3 * r1;\n      d4 += h4 * r0;\n      c = d4 >>> 13;\n      d4 &= 0x1fff;\n      d4 += h5 * (5 * r9);\n      d4 += h6 * (5 * r8);\n      d4 += h7 * (5 * r7);\n      d4 += h8 * (5 * r6);\n      d4 += h9 * (5 * r5);\n      c += d4 >>> 13;\n      d4 &= 0x1fff;\n      d5 = c;\n      d5 += h0 * r5;\n      d5 += h1 * r4;\n      d5 += h2 * r3;\n      d5 += h3 * r2;\n      d5 += h4 * r1;\n      c = d5 >>> 13;\n      d5 &= 0x1fff;\n      d5 += h5 * r0;\n      d5 += h6 * (5 * r9);\n      d5 += h7 * (5 * r8);\n      d5 += h8 * (5 * r7);\n      d5 += h9 * (5 * r6);\n      c += d5 >>> 13;\n      d5 &= 0x1fff;\n      d6 = c;\n      d6 += h0 * r6;\n      d6 += h1 * r5;\n      d6 += h2 * r4;\n      d6 += h3 * r3;\n      d6 += h4 * r2;\n      c = d6 >>> 13;\n      d6 &= 0x1fff;\n      d6 += h5 * r1;\n      d6 += h6 * r0;\n      d6 += h7 * (5 * r9);\n      d6 += h8 * (5 * r8);\n      d6 += h9 * (5 * r7);\n      c += d6 >>> 13;\n      d6 &= 0x1fff;\n      d7 = c;\n      d7 += h0 * r7;\n      d7 += h1 * r6;\n      d7 += h2 * r5;\n      d7 += h3 * r4;\n      d7 += h4 * r3;\n      c = d7 >>> 13;\n      d7 &= 0x1fff;\n      d7 += h5 * r2;\n      d7 += h6 * r1;\n      d7 += h7 * r0;\n      d7 += h8 * (5 * r9);\n      d7 += h9 * (5 * r8);\n      c += d7 >>> 13;\n      d7 &= 0x1fff;\n      d8 = c;\n      d8 += h0 * r8;\n      d8 += h1 * r7;\n      d8 += h2 * r6;\n      d8 += h3 * r5;\n      d8 += h4 * r4;\n      c = d8 >>> 13;\n      d8 &= 0x1fff;\n      d8 += h5 * r3;\n      d8 += h6 * r2;\n      d8 += h7 * r1;\n      d8 += h8 * r0;\n      d8 += h9 * (5 * r9);\n      c += d8 >>> 13;\n      d8 &= 0x1fff;\n      d9 = c;\n      d9 += h0 * r9;\n      d9 += h1 * r8;\n      d9 += h2 * r7;\n      d9 += h3 * r6;\n      d9 += h4 * r5;\n      c = d9 >>> 13;\n      d9 &= 0x1fff;\n      d9 += h5 * r4;\n      d9 += h6 * r3;\n      d9 += h7 * r2;\n      d9 += h8 * r1;\n      d9 += h9 * r0;\n      c += d9 >>> 13;\n      d9 &= 0x1fff;\n      c = (c << 2) + c | 0;\n      c = c + d0 | 0;\n      d0 = c & 0x1fff;\n      c = c >>> 13;\n      d1 += c;\n      h0 = d0;\n      h1 = d1;\n      h2 = d2;\n      h3 = d3;\n      h4 = d4;\n      h5 = d5;\n      h6 = d6;\n      h7 = d7;\n      h8 = d8;\n      h9 = d9;\n      mpos += 16;\n      bytes -= 16;\n    }\n    this.h[0] = h0;\n    this.h[1] = h1;\n    this.h[2] = h2;\n    this.h[3] = h3;\n    this.h[4] = h4;\n    this.h[5] = h5;\n    this.h[6] = h6;\n    this.h[7] = h7;\n    this.h[8] = h8;\n    this.h[9] = h9;\n  };\n  poly1305.prototype.finish = function (mac, macpos) {\n    var g = new Uint16Array(10);\n    var c, mask, f, i;\n    if (this.leftover) {\n      i = this.leftover;\n      this.buffer[i++] = 1;\n      for (; i < 16; i++) this.buffer[i] = 0;\n      this.fin = 1;\n      this.blocks(this.buffer, 0, 16);\n    }\n    c = this.h[1] >>> 13;\n    this.h[1] &= 0x1fff;\n    for (i = 2; i < 10; i++) {\n      this.h[i] += c;\n      c = this.h[i] >>> 13;\n      this.h[i] &= 0x1fff;\n    }\n    this.h[0] += c * 5;\n    c = this.h[0] >>> 13;\n    this.h[0] &= 0x1fff;\n    this.h[1] += c;\n    c = this.h[1] >>> 13;\n    this.h[1] &= 0x1fff;\n    this.h[2] += c;\n    g[0] = this.h[0] + 5;\n    c = g[0] >>> 13;\n    g[0] &= 0x1fff;\n    for (i = 1; i < 10; i++) {\n      g[i] = this.h[i] + c;\n      c = g[i] >>> 13;\n      g[i] &= 0x1fff;\n    }\n    g[9] -= 1 << 13;\n    mask = (c ^ 1) - 1;\n    for (i = 0; i < 10; i++) g[i] &= mask;\n    mask = ~mask;\n    for (i = 0; i < 10; i++) this.h[i] = this.h[i] & mask | g[i];\n    this.h[0] = (this.h[0] | this.h[1] << 13) & 0xffff;\n    this.h[1] = (this.h[1] >>> 3 | this.h[2] << 10) & 0xffff;\n    this.h[2] = (this.h[2] >>> 6 | this.h[3] << 7) & 0xffff;\n    this.h[3] = (this.h[3] >>> 9 | this.h[4] << 4) & 0xffff;\n    this.h[4] = (this.h[4] >>> 12 | this.h[5] << 1 | this.h[6] << 14) & 0xffff;\n    this.h[5] = (this.h[6] >>> 2 | this.h[7] << 11) & 0xffff;\n    this.h[6] = (this.h[7] >>> 5 | this.h[8] << 8) & 0xffff;\n    this.h[7] = (this.h[8] >>> 8 | this.h[9] << 5) & 0xffff;\n    f = this.h[0] + this.pad[0];\n    this.h[0] = f & 0xffff;\n    for (i = 1; i < 8; i++) {\n      f = (this.h[i] + this.pad[i] | 0) + (f >>> 16) | 0;\n      this.h[i] = f & 0xffff;\n    }\n    mac[macpos + 0] = this.h[0] >>> 0 & 0xff;\n    mac[macpos + 1] = this.h[0] >>> 8 & 0xff;\n    mac[macpos + 2] = this.h[1] >>> 0 & 0xff;\n    mac[macpos + 3] = this.h[1] >>> 8 & 0xff;\n    mac[macpos + 4] = this.h[2] >>> 0 & 0xff;\n    mac[macpos + 5] = this.h[2] >>> 8 & 0xff;\n    mac[macpos + 6] = this.h[3] >>> 0 & 0xff;\n    mac[macpos + 7] = this.h[3] >>> 8 & 0xff;\n    mac[macpos + 8] = this.h[4] >>> 0 & 0xff;\n    mac[macpos + 9] = this.h[4] >>> 8 & 0xff;\n    mac[macpos + 10] = this.h[5] >>> 0 & 0xff;\n    mac[macpos + 11] = this.h[5] >>> 8 & 0xff;\n    mac[macpos + 12] = this.h[6] >>> 0 & 0xff;\n    mac[macpos + 13] = this.h[6] >>> 8 & 0xff;\n    mac[macpos + 14] = this.h[7] >>> 0 & 0xff;\n    mac[macpos + 15] = this.h[7] >>> 8 & 0xff;\n  };\n  poly1305.prototype.update = function (m, mpos, bytes) {\n    var i, want;\n    if (this.leftover) {\n      want = 16 - this.leftover;\n      if (want > bytes) want = bytes;\n      for (i = 0; i < want; i++) this.buffer[this.leftover + i] = m[mpos + i];\n      bytes -= want;\n      mpos += want;\n      this.leftover += want;\n      if (this.leftover < 16) return;\n      this.blocks(this.buffer, 0, 16);\n      this.leftover = 0;\n    }\n    if (bytes >= 16) {\n      want = bytes - bytes % 16;\n      this.blocks(m, mpos, want);\n      mpos += want;\n      bytes -= want;\n    }\n    if (bytes) {\n      for (i = 0; i < bytes; i++) this.buffer[this.leftover + i] = m[mpos + i];\n      this.leftover += bytes;\n    }\n  };\n  function crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n    var s = new poly1305(k);\n    s.update(m, mpos, n);\n    s.finish(out, outpos);\n    return 0;\n  }\n  function crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n    var x = new Uint8Array(16);\n    crypto_onetimeauth(x, 0, m, mpos, n, k);\n    return crypto_verify_16(h, hpos, x, 0);\n  }\n  function crypto_secretbox(c, m, d, n, k) {\n    var i;\n    if (d < 32) return -1;\n    crypto_stream_xor(c, 0, m, 0, d, n, k);\n    crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n    for (i = 0; i < 16; i++) c[i] = 0;\n    return 0;\n  }\n  function crypto_secretbox_open(m, c, d, n, k) {\n    var i;\n    var x = new Uint8Array(32);\n    if (d < 32) return -1;\n    crypto_stream(x, 0, 32, n, k);\n    if (crypto_onetimeauth_verify(c, 16, c, 32, d - 32, x) !== 0) return -1;\n    crypto_stream_xor(m, 0, c, 0, d, n, k);\n    for (i = 0; i < 32; i++) m[i] = 0;\n    return 0;\n  }\n  function set25519(r, a) {\n    var i;\n    for (i = 0; i < 16; i++) r[i] = a[i] | 0;\n  }\n  function car25519(o) {\n    var i,\n      v,\n      c = 1;\n    for (i = 0; i < 16; i++) {\n      v = o[i] + c + 65535;\n      c = Math.floor(v / 65536);\n      o[i] = v - c * 65536;\n    }\n    o[0] += c - 1 + 37 * (c - 1);\n  }\n  function sel25519(p, q, b) {\n    var t,\n      c = ~(b - 1);\n    for (var i = 0; i < 16; i++) {\n      t = c & (p[i] ^ q[i]);\n      p[i] ^= t;\n      q[i] ^= t;\n    }\n  }\n  function pack25519(o, n) {\n    var i, j, b;\n    var m = gf(),\n      t = gf();\n    for (i = 0; i < 16; i++) t[i] = n[i];\n    car25519(t);\n    car25519(t);\n    car25519(t);\n    for (j = 0; j < 2; j++) {\n      m[0] = t[0] - 0xffed;\n      for (i = 1; i < 15; i++) {\n        m[i] = t[i] - 0xffff - (m[i - 1] >> 16 & 1);\n        m[i - 1] &= 0xffff;\n      }\n      m[15] = t[15] - 0x7fff - (m[14] >> 16 & 1);\n      b = m[15] >> 16 & 1;\n      m[14] &= 0xffff;\n      sel25519(t, m, 1 - b);\n    }\n    for (i = 0; i < 16; i++) {\n      o[2 * i] = t[i] & 0xff;\n      o[2 * i + 1] = t[i] >> 8;\n    }\n  }\n  function neq25519(a, b) {\n    var c = new Uint8Array(32),\n      d = new Uint8Array(32);\n    pack25519(c, a);\n    pack25519(d, b);\n    return crypto_verify_32(c, 0, d, 0);\n  }\n  function par25519(a) {\n    var d = new Uint8Array(32);\n    pack25519(d, a);\n    return d[0] & 1;\n  }\n  function unpack25519(o, n) {\n    var i;\n    for (i = 0; i < 16; i++) o[i] = n[2 * i] + (n[2 * i + 1] << 8);\n    o[15] &= 0x7fff;\n  }\n  function A(o, a, b) {\n    for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n  }\n  function Z(o, a, b) {\n    for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n  }\n  function M(o, a, b) {\n    var v,\n      c,\n      t0 = 0,\n      t1 = 0,\n      t2 = 0,\n      t3 = 0,\n      t4 = 0,\n      t5 = 0,\n      t6 = 0,\n      t7 = 0,\n      t8 = 0,\n      t9 = 0,\n      t10 = 0,\n      t11 = 0,\n      t12 = 0,\n      t13 = 0,\n      t14 = 0,\n      t15 = 0,\n      t16 = 0,\n      t17 = 0,\n      t18 = 0,\n      t19 = 0,\n      t20 = 0,\n      t21 = 0,\n      t22 = 0,\n      t23 = 0,\n      t24 = 0,\n      t25 = 0,\n      t26 = 0,\n      t27 = 0,\n      t28 = 0,\n      t29 = 0,\n      t30 = 0,\n      b0 = b[0],\n      b1 = b[1],\n      b2 = b[2],\n      b3 = b[3],\n      b4 = b[4],\n      b5 = b[5],\n      b6 = b[6],\n      b7 = b[7],\n      b8 = b[8],\n      b9 = b[9],\n      b10 = b[10],\n      b11 = b[11],\n      b12 = b[12],\n      b13 = b[13],\n      b14 = b[14],\n      b15 = b[15];\n    v = a[0];\n    t0 += v * b0;\n    t1 += v * b1;\n    t2 += v * b2;\n    t3 += v * b3;\n    t4 += v * b4;\n    t5 += v * b5;\n    t6 += v * b6;\n    t7 += v * b7;\n    t8 += v * b8;\n    t9 += v * b9;\n    t10 += v * b10;\n    t11 += v * b11;\n    t12 += v * b12;\n    t13 += v * b13;\n    t14 += v * b14;\n    t15 += v * b15;\n    v = a[1];\n    t1 += v * b0;\n    t2 += v * b1;\n    t3 += v * b2;\n    t4 += v * b3;\n    t5 += v * b4;\n    t6 += v * b5;\n    t7 += v * b6;\n    t8 += v * b7;\n    t9 += v * b8;\n    t10 += v * b9;\n    t11 += v * b10;\n    t12 += v * b11;\n    t13 += v * b12;\n    t14 += v * b13;\n    t15 += v * b14;\n    t16 += v * b15;\n    v = a[2];\n    t2 += v * b0;\n    t3 += v * b1;\n    t4 += v * b2;\n    t5 += v * b3;\n    t6 += v * b4;\n    t7 += v * b5;\n    t8 += v * b6;\n    t9 += v * b7;\n    t10 += v * b8;\n    t11 += v * b9;\n    t12 += v * b10;\n    t13 += v * b11;\n    t14 += v * b12;\n    t15 += v * b13;\n    t16 += v * b14;\n    t17 += v * b15;\n    v = a[3];\n    t3 += v * b0;\n    t4 += v * b1;\n    t5 += v * b2;\n    t6 += v * b3;\n    t7 += v * b4;\n    t8 += v * b5;\n    t9 += v * b6;\n    t10 += v * b7;\n    t11 += v * b8;\n    t12 += v * b9;\n    t13 += v * b10;\n    t14 += v * b11;\n    t15 += v * b12;\n    t16 += v * b13;\n    t17 += v * b14;\n    t18 += v * b15;\n    v = a[4];\n    t4 += v * b0;\n    t5 += v * b1;\n    t6 += v * b2;\n    t7 += v * b3;\n    t8 += v * b4;\n    t9 += v * b5;\n    t10 += v * b6;\n    t11 += v * b7;\n    t12 += v * b8;\n    t13 += v * b9;\n    t14 += v * b10;\n    t15 += v * b11;\n    t16 += v * b12;\n    t17 += v * b13;\n    t18 += v * b14;\n    t19 += v * b15;\n    v = a[5];\n    t5 += v * b0;\n    t6 += v * b1;\n    t7 += v * b2;\n    t8 += v * b3;\n    t9 += v * b4;\n    t10 += v * b5;\n    t11 += v * b6;\n    t12 += v * b7;\n    t13 += v * b8;\n    t14 += v * b9;\n    t15 += v * b10;\n    t16 += v * b11;\n    t17 += v * b12;\n    t18 += v * b13;\n    t19 += v * b14;\n    t20 += v * b15;\n    v = a[6];\n    t6 += v * b0;\n    t7 += v * b1;\n    t8 += v * b2;\n    t9 += v * b3;\n    t10 += v * b4;\n    t11 += v * b5;\n    t12 += v * b6;\n    t13 += v * b7;\n    t14 += v * b8;\n    t15 += v * b9;\n    t16 += v * b10;\n    t17 += v * b11;\n    t18 += v * b12;\n    t19 += v * b13;\n    t20 += v * b14;\n    t21 += v * b15;\n    v = a[7];\n    t7 += v * b0;\n    t8 += v * b1;\n    t9 += v * b2;\n    t10 += v * b3;\n    t11 += v * b4;\n    t12 += v * b5;\n    t13 += v * b6;\n    t14 += v * b7;\n    t15 += v * b8;\n    t16 += v * b9;\n    t17 += v * b10;\n    t18 += v * b11;\n    t19 += v * b12;\n    t20 += v * b13;\n    t21 += v * b14;\n    t22 += v * b15;\n    v = a[8];\n    t8 += v * b0;\n    t9 += v * b1;\n    t10 += v * b2;\n    t11 += v * b3;\n    t12 += v * b4;\n    t13 += v * b5;\n    t14 += v * b6;\n    t15 += v * b7;\n    t16 += v * b8;\n    t17 += v * b9;\n    t18 += v * b10;\n    t19 += v * b11;\n    t20 += v * b12;\n    t21 += v * b13;\n    t22 += v * b14;\n    t23 += v * b15;\n    v = a[9];\n    t9 += v * b0;\n    t10 += v * b1;\n    t11 += v * b2;\n    t12 += v * b3;\n    t13 += v * b4;\n    t14 += v * b5;\n    t15 += v * b6;\n    t16 += v * b7;\n    t17 += v * b8;\n    t18 += v * b9;\n    t19 += v * b10;\n    t20 += v * b11;\n    t21 += v * b12;\n    t22 += v * b13;\n    t23 += v * b14;\n    t24 += v * b15;\n    v = a[10];\n    t10 += v * b0;\n    t11 += v * b1;\n    t12 += v * b2;\n    t13 += v * b3;\n    t14 += v * b4;\n    t15 += v * b5;\n    t16 += v * b6;\n    t17 += v * b7;\n    t18 += v * b8;\n    t19 += v * b9;\n    t20 += v * b10;\n    t21 += v * b11;\n    t22 += v * b12;\n    t23 += v * b13;\n    t24 += v * b14;\n    t25 += v * b15;\n    v = a[11];\n    t11 += v * b0;\n    t12 += v * b1;\n    t13 += v * b2;\n    t14 += v * b3;\n    t15 += v * b4;\n    t16 += v * b5;\n    t17 += v * b6;\n    t18 += v * b7;\n    t19 += v * b8;\n    t20 += v * b9;\n    t21 += v * b10;\n    t22 += v * b11;\n    t23 += v * b12;\n    t24 += v * b13;\n    t25 += v * b14;\n    t26 += v * b15;\n    v = a[12];\n    t12 += v * b0;\n    t13 += v * b1;\n    t14 += v * b2;\n    t15 += v * b3;\n    t16 += v * b4;\n    t17 += v * b5;\n    t18 += v * b6;\n    t19 += v * b7;\n    t20 += v * b8;\n    t21 += v * b9;\n    t22 += v * b10;\n    t23 += v * b11;\n    t24 += v * b12;\n    t25 += v * b13;\n    t26 += v * b14;\n    t27 += v * b15;\n    v = a[13];\n    t13 += v * b0;\n    t14 += v * b1;\n    t15 += v * b2;\n    t16 += v * b3;\n    t17 += v * b4;\n    t18 += v * b5;\n    t19 += v * b6;\n    t20 += v * b7;\n    t21 += v * b8;\n    t22 += v * b9;\n    t23 += v * b10;\n    t24 += v * b11;\n    t25 += v * b12;\n    t26 += v * b13;\n    t27 += v * b14;\n    t28 += v * b15;\n    v = a[14];\n    t14 += v * b0;\n    t15 += v * b1;\n    t16 += v * b2;\n    t17 += v * b3;\n    t18 += v * b4;\n    t19 += v * b5;\n    t20 += v * b6;\n    t21 += v * b7;\n    t22 += v * b8;\n    t23 += v * b9;\n    t24 += v * b10;\n    t25 += v * b11;\n    t26 += v * b12;\n    t27 += v * b13;\n    t28 += v * b14;\n    t29 += v * b15;\n    v = a[15];\n    t15 += v * b0;\n    t16 += v * b1;\n    t17 += v * b2;\n    t18 += v * b3;\n    t19 += v * b4;\n    t20 += v * b5;\n    t21 += v * b6;\n    t22 += v * b7;\n    t23 += v * b8;\n    t24 += v * b9;\n    t25 += v * b10;\n    t26 += v * b11;\n    t27 += v * b12;\n    t28 += v * b13;\n    t29 += v * b14;\n    t30 += v * b15;\n    t0 += 38 * t16;\n    t1 += 38 * t17;\n    t2 += 38 * t18;\n    t3 += 38 * t19;\n    t4 += 38 * t20;\n    t5 += 38 * t21;\n    t6 += 38 * t22;\n    t7 += 38 * t23;\n    t8 += 38 * t24;\n    t9 += 38 * t25;\n    t10 += 38 * t26;\n    t11 += 38 * t27;\n    t12 += 38 * t28;\n    t13 += 38 * t29;\n    t14 += 38 * t30;\n    // t15 left as is\n\n    // first car\n    c = 1;\n    v = t0 + c + 65535;\n    c = Math.floor(v / 65536);\n    t0 = v - c * 65536;\n    v = t1 + c + 65535;\n    c = Math.floor(v / 65536);\n    t1 = v - c * 65536;\n    v = t2 + c + 65535;\n    c = Math.floor(v / 65536);\n    t2 = v - c * 65536;\n    v = t3 + c + 65535;\n    c = Math.floor(v / 65536);\n    t3 = v - c * 65536;\n    v = t4 + c + 65535;\n    c = Math.floor(v / 65536);\n    t4 = v - c * 65536;\n    v = t5 + c + 65535;\n    c = Math.floor(v / 65536);\n    t5 = v - c * 65536;\n    v = t6 + c + 65535;\n    c = Math.floor(v / 65536);\n    t6 = v - c * 65536;\n    v = t7 + c + 65535;\n    c = Math.floor(v / 65536);\n    t7 = v - c * 65536;\n    v = t8 + c + 65535;\n    c = Math.floor(v / 65536);\n    t8 = v - c * 65536;\n    v = t9 + c + 65535;\n    c = Math.floor(v / 65536);\n    t9 = v - c * 65536;\n    v = t10 + c + 65535;\n    c = Math.floor(v / 65536);\n    t10 = v - c * 65536;\n    v = t11 + c + 65535;\n    c = Math.floor(v / 65536);\n    t11 = v - c * 65536;\n    v = t12 + c + 65535;\n    c = Math.floor(v / 65536);\n    t12 = v - c * 65536;\n    v = t13 + c + 65535;\n    c = Math.floor(v / 65536);\n    t13 = v - c * 65536;\n    v = t14 + c + 65535;\n    c = Math.floor(v / 65536);\n    t14 = v - c * 65536;\n    v = t15 + c + 65535;\n    c = Math.floor(v / 65536);\n    t15 = v - c * 65536;\n    t0 += c - 1 + 37 * (c - 1);\n\n    // second car\n    c = 1;\n    v = t0 + c + 65535;\n    c = Math.floor(v / 65536);\n    t0 = v - c * 65536;\n    v = t1 + c + 65535;\n    c = Math.floor(v / 65536);\n    t1 = v - c * 65536;\n    v = t2 + c + 65535;\n    c = Math.floor(v / 65536);\n    t2 = v - c * 65536;\n    v = t3 + c + 65535;\n    c = Math.floor(v / 65536);\n    t3 = v - c * 65536;\n    v = t4 + c + 65535;\n    c = Math.floor(v / 65536);\n    t4 = v - c * 65536;\n    v = t5 + c + 65535;\n    c = Math.floor(v / 65536);\n    t5 = v - c * 65536;\n    v = t6 + c + 65535;\n    c = Math.floor(v / 65536);\n    t6 = v - c * 65536;\n    v = t7 + c + 65535;\n    c = Math.floor(v / 65536);\n    t7 = v - c * 65536;\n    v = t8 + c + 65535;\n    c = Math.floor(v / 65536);\n    t8 = v - c * 65536;\n    v = t9 + c + 65535;\n    c = Math.floor(v / 65536);\n    t9 = v - c * 65536;\n    v = t10 + c + 65535;\n    c = Math.floor(v / 65536);\n    t10 = v - c * 65536;\n    v = t11 + c + 65535;\n    c = Math.floor(v / 65536);\n    t11 = v - c * 65536;\n    v = t12 + c + 65535;\n    c = Math.floor(v / 65536);\n    t12 = v - c * 65536;\n    v = t13 + c + 65535;\n    c = Math.floor(v / 65536);\n    t13 = v - c * 65536;\n    v = t14 + c + 65535;\n    c = Math.floor(v / 65536);\n    t14 = v - c * 65536;\n    v = t15 + c + 65535;\n    c = Math.floor(v / 65536);\n    t15 = v - c * 65536;\n    t0 += c - 1 + 37 * (c - 1);\n    o[0] = t0;\n    o[1] = t1;\n    o[2] = t2;\n    o[3] = t3;\n    o[4] = t4;\n    o[5] = t5;\n    o[6] = t6;\n    o[7] = t7;\n    o[8] = t8;\n    o[9] = t9;\n    o[10] = t10;\n    o[11] = t11;\n    o[12] = t12;\n    o[13] = t13;\n    o[14] = t14;\n    o[15] = t15;\n  }\n  function S(o, a) {\n    M(o, a, a);\n  }\n  function inv25519(o, i) {\n    var c = gf();\n    var a;\n    for (a = 0; a < 16; a++) c[a] = i[a];\n    for (a = 253; a >= 0; a--) {\n      S(c, c);\n      if (a !== 2 && a !== 4) M(c, c, i);\n    }\n    for (a = 0; a < 16; a++) o[a] = c[a];\n  }\n  function pow2523(o, i) {\n    var c = gf();\n    var a;\n    for (a = 0; a < 16; a++) c[a] = i[a];\n    for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if (a !== 1) M(c, c, i);\n    }\n    for (a = 0; a < 16; a++) o[a] = c[a];\n  }\n  function crypto_scalarmult(q, n, p) {\n    var z = new Uint8Array(32);\n    var x = new Float64Array(80),\n      r,\n      i;\n    var a = gf(),\n      b = gf(),\n      c = gf(),\n      d = gf(),\n      e = gf(),\n      f = gf();\n    for (i = 0; i < 31; i++) z[i] = n[i];\n    z[31] = n[31] & 127 | 64;\n    z[0] &= 248;\n    unpack25519(x, p);\n    for (i = 0; i < 16; i++) {\n      b[i] = x[i];\n      d[i] = a[i] = c[i] = 0;\n    }\n    a[0] = d[0] = 1;\n    for (i = 254; i >= 0; --i) {\n      r = z[i >>> 3] >>> (i & 7) & 1;\n      sel25519(a, b, r);\n      sel25519(c, d, r);\n      A(e, a, c);\n      Z(a, a, c);\n      A(c, b, d);\n      Z(b, b, d);\n      S(d, e);\n      S(f, a);\n      M(a, c, a);\n      M(c, b, e);\n      A(e, a, c);\n      Z(a, a, c);\n      S(b, a);\n      Z(c, d, f);\n      M(a, c, _121665);\n      A(a, a, d);\n      M(c, c, a);\n      M(a, d, f);\n      M(d, b, x);\n      S(b, e);\n      sel25519(a, b, r);\n      sel25519(c, d, r);\n    }\n    for (i = 0; i < 16; i++) {\n      x[i + 16] = a[i];\n      x[i + 32] = c[i];\n      x[i + 48] = b[i];\n      x[i + 64] = d[i];\n    }\n    var x32 = x.subarray(32);\n    var x16 = x.subarray(16);\n    inv25519(x32, x32);\n    M(x16, x16, x32);\n    pack25519(q, x16);\n    return 0;\n  }\n  function crypto_scalarmult_base(q, n) {\n    return crypto_scalarmult(q, n, _9);\n  }\n  function crypto_box_keypair(y, x) {\n    randombytes(x, 32);\n    return crypto_scalarmult_base(y, x);\n  }\n  function crypto_box_beforenm(k, y, x) {\n    var s = new Uint8Array(32);\n    crypto_scalarmult(s, x, y);\n    return crypto_core_hsalsa20(k, _0, s, sigma);\n  }\n  var crypto_box_afternm = crypto_secretbox;\n  var crypto_box_open_afternm = crypto_secretbox_open;\n  function crypto_box(c, m, d, n, y, x) {\n    var k = new Uint8Array(32);\n    crypto_box_beforenm(k, y, x);\n    return crypto_box_afternm(c, m, d, n, k);\n  }\n  function crypto_box_open(m, c, d, n, y, x) {\n    var k = new Uint8Array(32);\n    crypto_box_beforenm(k, y, x);\n    return crypto_box_open_afternm(m, c, d, n, k);\n  }\n  var K = [0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd, 0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc, 0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019, 0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118, 0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe, 0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2, 0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1, 0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694, 0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3, 0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65, 0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483, 0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5, 0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210, 0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4, 0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725, 0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70, 0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926, 0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df, 0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8, 0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b, 0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001, 0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30, 0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910, 0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8, 0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53, 0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8, 0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb, 0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3, 0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60, 0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec, 0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9, 0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b, 0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207, 0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178, 0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6, 0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b, 0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493, 0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c, 0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a, 0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817];\n  function crypto_hashblocks_hl(hh, hl, m, n) {\n    var wh = new Int32Array(16),\n      wl = new Int32Array(16),\n      bh0,\n      bh1,\n      bh2,\n      bh3,\n      bh4,\n      bh5,\n      bh6,\n      bh7,\n      bl0,\n      bl1,\n      bl2,\n      bl3,\n      bl4,\n      bl5,\n      bl6,\n      bl7,\n      th,\n      tl,\n      i,\n      j,\n      h,\n      l,\n      a,\n      b,\n      c,\n      d;\n    var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n    var pos = 0;\n    while (n >= 128) {\n      for (i = 0; i < 16; i++) {\n        j = 8 * i + pos;\n        wh[i] = m[j + 0] << 24 | m[j + 1] << 16 | m[j + 2] << 8 | m[j + 3];\n        wl[i] = m[j + 4] << 24 | m[j + 5] << 16 | m[j + 6] << 8 | m[j + 7];\n      }\n      for (i = 0; i < 80; i++) {\n        bh0 = ah0;\n        bh1 = ah1;\n        bh2 = ah2;\n        bh3 = ah3;\n        bh4 = ah4;\n        bh5 = ah5;\n        bh6 = ah6;\n        bh7 = ah7;\n        bl0 = al0;\n        bl1 = al1;\n        bl2 = al2;\n        bl3 = al3;\n        bl4 = al4;\n        bl5 = al5;\n        bl6 = al6;\n        bl7 = al7;\n\n        // add\n        h = ah7;\n        l = al7;\n        a = l & 0xffff;\n        b = l >>> 16;\n        c = h & 0xffff;\n        d = h >>> 16;\n\n        // Sigma1\n        h = (ah4 >>> 14 | al4 << 32 - 14) ^ (ah4 >>> 18 | al4 << 32 - 18) ^ (al4 >>> 41 - 32 | ah4 << 32 - (41 - 32));\n        l = (al4 >>> 14 | ah4 << 32 - 14) ^ (al4 >>> 18 | ah4 << 32 - 18) ^ (ah4 >>> 41 - 32 | al4 << 32 - (41 - 32));\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n\n        // Ch\n        h = ah4 & ah5 ^ ~ah4 & ah6;\n        l = al4 & al5 ^ ~al4 & al6;\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n\n        // K\n        h = K[i * 2];\n        l = K[i * 2 + 1];\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n\n        // w\n        h = wh[i % 16];\n        l = wl[i % 16];\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n        b += a >>> 16;\n        c += b >>> 16;\n        d += c >>> 16;\n        th = c & 0xffff | d << 16;\n        tl = a & 0xffff | b << 16;\n\n        // add\n        h = th;\n        l = tl;\n        a = l & 0xffff;\n        b = l >>> 16;\n        c = h & 0xffff;\n        d = h >>> 16;\n\n        // Sigma0\n        h = (ah0 >>> 28 | al0 << 32 - 28) ^ (al0 >>> 34 - 32 | ah0 << 32 - (34 - 32)) ^ (al0 >>> 39 - 32 | ah0 << 32 - (39 - 32));\n        l = (al0 >>> 28 | ah0 << 32 - 28) ^ (ah0 >>> 34 - 32 | al0 << 32 - (34 - 32)) ^ (ah0 >>> 39 - 32 | al0 << 32 - (39 - 32));\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n\n        // Maj\n        h = ah0 & ah1 ^ ah0 & ah2 ^ ah1 & ah2;\n        l = al0 & al1 ^ al0 & al2 ^ al1 & al2;\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n        b += a >>> 16;\n        c += b >>> 16;\n        d += c >>> 16;\n        bh7 = c & 0xffff | d << 16;\n        bl7 = a & 0xffff | b << 16;\n\n        // add\n        h = bh3;\n        l = bl3;\n        a = l & 0xffff;\n        b = l >>> 16;\n        c = h & 0xffff;\n        d = h >>> 16;\n        h = th;\n        l = tl;\n        a += l & 0xffff;\n        b += l >>> 16;\n        c += h & 0xffff;\n        d += h >>> 16;\n        b += a >>> 16;\n        c += b >>> 16;\n        d += c >>> 16;\n        bh3 = c & 0xffff | d << 16;\n        bl3 = a & 0xffff | b << 16;\n        ah1 = bh0;\n        ah2 = bh1;\n        ah3 = bh2;\n        ah4 = bh3;\n        ah5 = bh4;\n        ah6 = bh5;\n        ah7 = bh6;\n        ah0 = bh7;\n        al1 = bl0;\n        al2 = bl1;\n        al3 = bl2;\n        al4 = bl3;\n        al5 = bl4;\n        al6 = bl5;\n        al7 = bl6;\n        al0 = bl7;\n        if (i % 16 === 15) {\n          for (j = 0; j < 16; j++) {\n            // add\n            h = wh[j];\n            l = wl[j];\n            a = l & 0xffff;\n            b = l >>> 16;\n            c = h & 0xffff;\n            d = h >>> 16;\n            h = wh[(j + 9) % 16];\n            l = wl[(j + 9) % 16];\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n\n            // sigma0\n            th = wh[(j + 1) % 16];\n            tl = wl[(j + 1) % 16];\n            h = (th >>> 1 | tl << 32 - 1) ^ (th >>> 8 | tl << 32 - 8) ^ th >>> 7;\n            l = (tl >>> 1 | th << 32 - 1) ^ (tl >>> 8 | th << 32 - 8) ^ (tl >>> 7 | th << 32 - 7);\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n\n            // sigma1\n            th = wh[(j + 14) % 16];\n            tl = wl[(j + 14) % 16];\n            h = (th >>> 19 | tl << 32 - 19) ^ (tl >>> 61 - 32 | th << 32 - (61 - 32)) ^ th >>> 6;\n            l = (tl >>> 19 | th << 32 - 19) ^ (th >>> 61 - 32 | tl << 32 - (61 - 32)) ^ (tl >>> 6 | th << 32 - 6);\n            a += l & 0xffff;\n            b += l >>> 16;\n            c += h & 0xffff;\n            d += h >>> 16;\n            b += a >>> 16;\n            c += b >>> 16;\n            d += c >>> 16;\n            wh[j] = c & 0xffff | d << 16;\n            wl[j] = a & 0xffff | b << 16;\n          }\n        }\n      }\n\n      // add\n      h = ah0;\n      l = al0;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[0];\n      l = hl[0];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[0] = ah0 = c & 0xffff | d << 16;\n      hl[0] = al0 = a & 0xffff | b << 16;\n      h = ah1;\n      l = al1;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[1];\n      l = hl[1];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[1] = ah1 = c & 0xffff | d << 16;\n      hl[1] = al1 = a & 0xffff | b << 16;\n      h = ah2;\n      l = al2;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[2];\n      l = hl[2];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[2] = ah2 = c & 0xffff | d << 16;\n      hl[2] = al2 = a & 0xffff | b << 16;\n      h = ah3;\n      l = al3;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[3];\n      l = hl[3];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[3] = ah3 = c & 0xffff | d << 16;\n      hl[3] = al3 = a & 0xffff | b << 16;\n      h = ah4;\n      l = al4;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[4];\n      l = hl[4];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[4] = ah4 = c & 0xffff | d << 16;\n      hl[4] = al4 = a & 0xffff | b << 16;\n      h = ah5;\n      l = al5;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[5];\n      l = hl[5];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[5] = ah5 = c & 0xffff | d << 16;\n      hl[5] = al5 = a & 0xffff | b << 16;\n      h = ah6;\n      l = al6;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[6];\n      l = hl[6];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[6] = ah6 = c & 0xffff | d << 16;\n      hl[6] = al6 = a & 0xffff | b << 16;\n      h = ah7;\n      l = al7;\n      a = l & 0xffff;\n      b = l >>> 16;\n      c = h & 0xffff;\n      d = h >>> 16;\n      h = hh[7];\n      l = hl[7];\n      a += l & 0xffff;\n      b += l >>> 16;\n      c += h & 0xffff;\n      d += h >>> 16;\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n      hh[7] = ah7 = c & 0xffff | d << 16;\n      hl[7] = al7 = a & 0xffff | b << 16;\n      pos += 128;\n      n -= 128;\n    }\n    return n;\n  }\n  function crypto_hash(out, m, n) {\n    var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i,\n      b = n;\n    hh[0] = 0x6a09e667;\n    hh[1] = 0xbb67ae85;\n    hh[2] = 0x3c6ef372;\n    hh[3] = 0xa54ff53a;\n    hh[4] = 0x510e527f;\n    hh[5] = 0x9b05688c;\n    hh[6] = 0x1f83d9ab;\n    hh[7] = 0x5be0cd19;\n    hl[0] = 0xf3bcc908;\n    hl[1] = 0x84caa73b;\n    hl[2] = 0xfe94f82b;\n    hl[3] = 0x5f1d36f1;\n    hl[4] = 0xade682d1;\n    hl[5] = 0x2b3e6c1f;\n    hl[6] = 0xfb41bd6b;\n    hl[7] = 0x137e2179;\n    crypto_hashblocks_hl(hh, hl, m, n);\n    n %= 128;\n    for (i = 0; i < n; i++) x[i] = m[b - n + i];\n    x[n] = 128;\n    n = 256 - 128 * (n < 112 ? 1 : 0);\n    x[n - 9] = 0;\n    ts64(x, n - 8, b / 0x20000000 | 0, b << 3);\n    crypto_hashblocks_hl(hh, hl, x, n);\n    for (i = 0; i < 8; i++) ts64(out, 8 * i, hh[i], hl[i]);\n    return 0;\n  }\n  function add(p, q) {\n    var a = gf(),\n      b = gf(),\n      c = gf(),\n      d = gf(),\n      e = gf(),\n      f = gf(),\n      g = gf(),\n      h = gf(),\n      t = gf();\n    Z(a, p[1], p[0]);\n    Z(t, q[1], q[0]);\n    M(a, a, t);\n    A(b, p[0], p[1]);\n    A(t, q[0], q[1]);\n    M(b, b, t);\n    M(c, p[3], q[3]);\n    M(c, c, D2);\n    M(d, p[2], q[2]);\n    A(d, d, d);\n    Z(e, b, a);\n    Z(f, d, c);\n    A(g, d, c);\n    A(h, b, a);\n    M(p[0], e, f);\n    M(p[1], h, g);\n    M(p[2], g, f);\n    M(p[3], e, h);\n  }\n  function cswap(p, q, b) {\n    var i;\n    for (i = 0; i < 4; i++) {\n      sel25519(p[i], q[i], b);\n    }\n  }\n  function pack(r, p) {\n    var tx = gf(),\n      ty = gf(),\n      zi = gf();\n    inv25519(zi, p[2]);\n    M(tx, p[0], zi);\n    M(ty, p[1], zi);\n    pack25519(r, ty);\n    r[31] ^= par25519(tx) << 7;\n  }\n  function scalarmult(p, q, s) {\n    var b, i;\n    set25519(p[0], gf0);\n    set25519(p[1], gf1);\n    set25519(p[2], gf1);\n    set25519(p[3], gf0);\n    for (i = 255; i >= 0; --i) {\n      b = s[i / 8 | 0] >> (i & 7) & 1;\n      cswap(p, q, b);\n      add(q, p);\n      add(p, p);\n      cswap(p, q, b);\n    }\n  }\n  function scalarbase(p, s) {\n    var q = [gf(), gf(), gf(), gf()];\n    set25519(q[0], X);\n    set25519(q[1], Y);\n    set25519(q[2], gf1);\n    M(q[3], X, Y);\n    scalarmult(p, q, s);\n  }\n  function crypto_sign_keypair(pk, sk, seeded) {\n    var d = new Uint8Array(64);\n    var p = [gf(), gf(), gf(), gf()];\n    var i;\n    if (!seeded) randombytes(sk, 32);\n    crypto_hash(d, sk, 32);\n    d[0] &= 248;\n    d[31] &= 127;\n    d[31] |= 64;\n    scalarbase(p, d);\n    pack(pk, p);\n    for (i = 0; i < 32; i++) sk[i + 32] = pk[i];\n    return 0;\n  }\n  var L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n  function modL(r, x) {\n    var carry, i, j, k;\n    for (i = 63; i >= 32; --i) {\n      carry = 0;\n      for (j = i - 32, k = i - 12; j < k; ++j) {\n        x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n        carry = Math.floor((x[j] + 128) / 256);\n        x[j] -= carry * 256;\n      }\n      x[j] += carry;\n      x[i] = 0;\n    }\n    carry = 0;\n    for (j = 0; j < 32; j++) {\n      x[j] += carry - (x[31] >> 4) * L[j];\n      carry = x[j] >> 8;\n      x[j] &= 255;\n    }\n    for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n    for (i = 0; i < 32; i++) {\n      x[i + 1] += x[i] >> 8;\n      r[i] = x[i] & 255;\n    }\n  }\n  function reduce(r) {\n    var x = new Float64Array(64),\n      i;\n    for (i = 0; i < 64; i++) x[i] = r[i];\n    for (i = 0; i < 64; i++) r[i] = 0;\n    modL(r, x);\n  }\n\n  // Note: difference from C - smlen returned, not passed as argument.\n  function crypto_sign(sm, m, n, sk) {\n    var d = new Uint8Array(64),\n      h = new Uint8Array(64),\n      r = new Uint8Array(64);\n    var i,\n      j,\n      x = new Float64Array(64);\n    var p = [gf(), gf(), gf(), gf()];\n    crypto_hash(d, sk, 32);\n    d[0] &= 248;\n    d[31] &= 127;\n    d[31] |= 64;\n    var smlen = n + 64;\n    for (i = 0; i < n; i++) sm[64 + i] = m[i];\n    for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n    crypto_hash(r, sm.subarray(32), n + 32);\n    reduce(r);\n    scalarbase(p, r);\n    pack(sm, p);\n    for (i = 32; i < 64; i++) sm[i] = sk[i];\n    crypto_hash(h, sm, n + 64);\n    reduce(h);\n    for (i = 0; i < 64; i++) x[i] = 0;\n    for (i = 0; i < 32; i++) x[i] = r[i];\n    for (i = 0; i < 32; i++) {\n      for (j = 0; j < 32; j++) {\n        x[i + j] += h[i] * d[j];\n      }\n    }\n    modL(sm.subarray(32), x);\n    return smlen;\n  }\n  function unpackneg(r, p) {\n    var t = gf(),\n      chk = gf(),\n      num = gf(),\n      den = gf(),\n      den2 = gf(),\n      den4 = gf(),\n      den6 = gf();\n    set25519(r[2], gf1);\n    unpack25519(r[1], p);\n    S(num, r[1]);\n    M(den, num, D);\n    Z(num, num, r[2]);\n    A(den, r[2], den);\n    S(den2, den);\n    S(den4, den2);\n    M(den6, den4, den2);\n    M(t, den6, num);\n    M(t, t, den);\n    pow2523(t, t);\n    M(t, t, num);\n    M(t, t, den);\n    M(t, t, den);\n    M(r[0], t, den);\n    S(chk, r[0]);\n    M(chk, chk, den);\n    if (neq25519(chk, num)) M(r[0], r[0], I);\n    S(chk, r[0]);\n    M(chk, chk, den);\n    if (neq25519(chk, num)) return -1;\n    if (par25519(r[0]) === p[31] >> 7) Z(r[0], gf0, r[0]);\n    M(r[3], r[0], r[1]);\n    return 0;\n  }\n  function crypto_sign_open(m, sm, n, pk) {\n    var i;\n    var t = new Uint8Array(32),\n      h = new Uint8Array(64);\n    var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n    if (n < 64) return -1;\n    if (unpackneg(q, pk)) return -1;\n    for (i = 0; i < n; i++) m[i] = sm[i];\n    for (i = 0; i < 32; i++) m[i + 32] = pk[i];\n    crypto_hash(h, m, n);\n    reduce(h);\n    scalarmult(p, q, h);\n    scalarbase(q, sm.subarray(32));\n    add(p, q);\n    pack(t, p);\n    n -= 64;\n    if (crypto_verify_32(sm, 0, t, 0)) {\n      for (i = 0; i < n; i++) m[i] = 0;\n      return -1;\n    }\n    for (i = 0; i < n; i++) m[i] = sm[i + 64];\n    return n;\n  }\n  var crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n  nacl.lowlevel = {\n    crypto_core_hsalsa20: crypto_core_hsalsa20,\n    crypto_stream_xor: crypto_stream_xor,\n    crypto_stream: crypto_stream,\n    crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n    crypto_stream_salsa20: crypto_stream_salsa20,\n    crypto_onetimeauth: crypto_onetimeauth,\n    crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n    crypto_verify_16: crypto_verify_16,\n    crypto_verify_32: crypto_verify_32,\n    crypto_secretbox: crypto_secretbox,\n    crypto_secretbox_open: crypto_secretbox_open,\n    crypto_scalarmult: crypto_scalarmult,\n    crypto_scalarmult_base: crypto_scalarmult_base,\n    crypto_box_beforenm: crypto_box_beforenm,\n    crypto_box_afternm: crypto_box_afternm,\n    crypto_box: crypto_box,\n    crypto_box_open: crypto_box_open,\n    crypto_box_keypair: crypto_box_keypair,\n    crypto_hash: crypto_hash,\n    crypto_sign: crypto_sign,\n    crypto_sign_keypair: crypto_sign_keypair,\n    crypto_sign_open: crypto_sign_open,\n    crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n    crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n    crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n    crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n    crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n    crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n    crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n    crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n    crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n    crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n    crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n    crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n    crypto_sign_BYTES: crypto_sign_BYTES,\n    crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n    crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n    crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n    crypto_hash_BYTES: crypto_hash_BYTES,\n    gf: gf,\n    D: D,\n    L: L,\n    pack25519: pack25519,\n    unpack25519: unpack25519,\n    M: M,\n    A: A,\n    S: S,\n    Z: Z,\n    pow2523: pow2523,\n    add: add,\n    set25519: set25519,\n    modL: modL,\n    scalarmult: scalarmult,\n    scalarbase: scalarbase\n  };\n\n  /* High-level API */\n\n  function checkLengths(k, n) {\n    if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n    if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n  }\n  function checkBoxLengths(pk, sk) {\n    if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n    if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n  }\n  function checkArrayTypes() {\n    for (var i = 0; i < arguments.length; i++) {\n      if (!(arguments[i] instanceof Uint8Array)) throw new TypeError('unexpected type, use Uint8Array');\n    }\n  }\n  function cleanup(arr) {\n    for (var i = 0; i < arr.length; i++) arr[i] = 0;\n  }\n  nacl.randomBytes = function (n) {\n    var b = new Uint8Array(n);\n    randombytes(b, n);\n    return b;\n  };\n  nacl.secretbox = function (msg, nonce, key) {\n    checkArrayTypes(msg, nonce, key);\n    checkLengths(key, nonce);\n    var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n    var c = new Uint8Array(m.length);\n    for (var i = 0; i < msg.length; i++) m[i + crypto_secretbox_ZEROBYTES] = msg[i];\n    crypto_secretbox(c, m, m.length, nonce, key);\n    return c.subarray(crypto_secretbox_BOXZEROBYTES);\n  };\n  nacl.secretbox.open = function (box, nonce, key) {\n    checkArrayTypes(box, nonce, key);\n    checkLengths(key, nonce);\n    var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n    var m = new Uint8Array(c.length);\n    for (var i = 0; i < box.length; i++) c[i + crypto_secretbox_BOXZEROBYTES] = box[i];\n    if (c.length < 32) return null;\n    if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n    return m.subarray(crypto_secretbox_ZEROBYTES);\n  };\n  nacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\n  nacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\n  nacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n  nacl.scalarMult = function (n, p) {\n    checkArrayTypes(n, p);\n    if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n    if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n    var q = new Uint8Array(crypto_scalarmult_BYTES);\n    crypto_scalarmult(q, n, p);\n    return q;\n  };\n  nacl.scalarMult.base = function (n) {\n    checkArrayTypes(n);\n    if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n    var q = new Uint8Array(crypto_scalarmult_BYTES);\n    crypto_scalarmult_base(q, n);\n    return q;\n  };\n  nacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\n  nacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n  nacl.box = function (msg, nonce, publicKey, secretKey) {\n    var k = nacl.box.before(publicKey, secretKey);\n    return nacl.secretbox(msg, nonce, k);\n  };\n  nacl.box.before = function (publicKey, secretKey) {\n    checkArrayTypes(publicKey, secretKey);\n    checkBoxLengths(publicKey, secretKey);\n    var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n    crypto_box_beforenm(k, publicKey, secretKey);\n    return k;\n  };\n  nacl.box.after = nacl.secretbox;\n  nacl.box.open = function (msg, nonce, publicKey, secretKey) {\n    var k = nacl.box.before(publicKey, secretKey);\n    return nacl.secretbox.open(msg, nonce, k);\n  };\n  nacl.box.open.after = nacl.secretbox.open;\n  nacl.box.keyPair = function () {\n    var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n    var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n    crypto_box_keypair(pk, sk);\n    return {\n      publicKey: pk,\n      secretKey: sk\n    };\n  };\n  nacl.box.keyPair.fromSecretKey = function (secretKey) {\n    checkArrayTypes(secretKey);\n    if (secretKey.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n    var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n    crypto_scalarmult_base(pk, secretKey);\n    return {\n      publicKey: pk,\n      secretKey: new Uint8Array(secretKey)\n    };\n  };\n  nacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\n  nacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\n  nacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\n  nacl.box.nonceLength = crypto_box_NONCEBYTES;\n  nacl.box.overheadLength = nacl.secretbox.overheadLength;\n  nacl.sign = function (msg, secretKey) {\n    checkArrayTypes(msg, secretKey);\n    if (secretKey.length !== crypto_sign_SECRETKEYBYTES) throw new Error('bad secret key size');\n    var signedMsg = new Uint8Array(crypto_sign_BYTES + msg.length);\n    crypto_sign(signedMsg, msg, msg.length, secretKey);\n    return signedMsg;\n  };\n  nacl.sign.open = function (signedMsg, publicKey) {\n    checkArrayTypes(signedMsg, publicKey);\n    if (publicKey.length !== crypto_sign_PUBLICKEYBYTES) throw new Error('bad public key size');\n    var tmp = new Uint8Array(signedMsg.length);\n    var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n    if (mlen < 0) return null;\n    var m = new Uint8Array(mlen);\n    for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n    return m;\n  };\n  nacl.sign.detached = function (msg, secretKey) {\n    var signedMsg = nacl.sign(msg, secretKey);\n    var sig = new Uint8Array(crypto_sign_BYTES);\n    for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n    return sig;\n  };\n  nacl.sign.detached.verify = function (msg, sig, publicKey) {\n    checkArrayTypes(msg, sig, publicKey);\n    if (sig.length !== crypto_sign_BYTES) throw new Error('bad signature size');\n    if (publicKey.length !== crypto_sign_PUBLICKEYBYTES) throw new Error('bad public key size');\n    var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n    var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n    var i;\n    for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n    for (i = 0; i < msg.length; i++) sm[i + crypto_sign_BYTES] = msg[i];\n    return crypto_sign_open(m, sm, sm.length, publicKey) >= 0;\n  };\n  nacl.sign.keyPair = function () {\n    var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n    var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n    crypto_sign_keypair(pk, sk);\n    return {\n      publicKey: pk,\n      secretKey: sk\n    };\n  };\n  nacl.sign.keyPair.fromSecretKey = function (secretKey) {\n    checkArrayTypes(secretKey);\n    if (secretKey.length !== crypto_sign_SECRETKEYBYTES) throw new Error('bad secret key size');\n    var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n    for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32 + i];\n    return {\n      publicKey: pk,\n      secretKey: new Uint8Array(secretKey)\n    };\n  };\n  nacl.sign.keyPair.fromSeed = function (seed) {\n    checkArrayTypes(seed);\n    if (seed.length !== crypto_sign_SEEDBYTES) throw new Error('bad seed size');\n    var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n    var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n    for (var i = 0; i < 32; i++) sk[i] = seed[i];\n    crypto_sign_keypair(pk, sk, true);\n    return {\n      publicKey: pk,\n      secretKey: sk\n    };\n  };\n  nacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\n  nacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\n  nacl.sign.seedLength = crypto_sign_SEEDBYTES;\n  nacl.sign.signatureLength = crypto_sign_BYTES;\n  nacl.hash = function (msg) {\n    checkArrayTypes(msg);\n    var h = new Uint8Array(crypto_hash_BYTES);\n    crypto_hash(h, msg, msg.length);\n    return h;\n  };\n  nacl.hash.hashLength = crypto_hash_BYTES;\n  nacl.verify = function (x, y) {\n    checkArrayTypes(x, y);\n    // Zero length arguments are considered not equal.\n    if (x.length === 0 || y.length === 0) return false;\n    if (x.length !== y.length) return false;\n    return vn(x, 0, y, 0, x.length) === 0 ? true : false;\n  };\n  nacl.setPRNG = function (fn) {\n    randombytes = fn;\n  };\n  (function () {\n    // Initialize PRNG if environment provides CSPRNG.\n    // If not, methods calling randombytes will throw.\n    var crypto = typeof self !== 'undefined' ? self.crypto || self.msCrypto : null;\n    if (crypto && crypto.getRandomValues) {\n      // Browsers.\n      var QUOTA = 65536;\n      nacl.setPRNG(function (x, n) {\n        var i,\n          v = new Uint8Array(n);\n        for (i = 0; i < n; i += QUOTA) {\n          crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n        }\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    } else if (typeof require !== 'undefined') {\n      // Node.js.\n      crypto = require('crypto');\n      if (crypto && crypto.randomBytes) {\n        nacl.setPRNG(function (x, n) {\n          var i,\n            v = crypto.randomBytes(n);\n          for (i = 0; i < n; i++) x[i] = v[i];\n          cleanup(v);\n        });\n      }\n    }\n  })();\n})(typeof module !== 'undefined' && module.exports ? module.exports : self.nacl = self.nacl || {});", "map": {"version": 3, "names": ["nacl", "gf", "init", "i", "r", "Float64Array", "length", "randombytes", "Error", "_0", "Uint8Array", "_9", "gf0", "gf1", "_121665", "D", "D2", "X", "Y", "I", "ts64", "x", "h", "l", "vn", "xi", "y", "yi", "n", "d", "crypto_verify_16", "crypto_verify_32", "core_salsa20", "o", "p", "k", "c", "j0", "j1", "j2", "j3", "j4", "j5", "j6", "j7", "j8", "j9", "j10", "j11", "j12", "j13", "j14", "j15", "x0", "x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "u", "core_hsalsa20", "crypto_core_salsa20", "out", "inp", "crypto_core_hsalsa20", "sigma", "crypto_stream_salsa20_xor", "cpos", "m", "mpos", "b", "z", "crypto_stream_salsa20", "crypto_stream", "s", "sn", "crypto_stream_xor", "poly1305", "key", "buffer", "Uint16Array", "pad", "leftover", "fin", "t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7", "prototype", "blocks", "bytes", "hibit", "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9", "h0", "h1", "h2", "h3", "h4", "h5", "h6", "h7", "h8", "h9", "r0", "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "finish", "mac", "macpos", "g", "mask", "f", "update", "want", "crypto_onetimeauth", "outpos", "crypto_onetimeauth_verify", "hpos", "crypto_secretbox", "crypto_secretbox_open", "set25519", "a", "car25519", "v", "Math", "floor", "sel25519", "q", "t", "pack25519", "j", "neq25519", "par25519", "unpack25519", "A", "Z", "M", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "t26", "t27", "t28", "t29", "t30", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "b11", "b12", "b13", "b14", "b15", "S", "inv25519", "pow2523", "crypto_scalarmult", "e", "x32", "subarray", "x16", "crypto_scalarmult_base", "crypto_box_keypair", "crypto_box_beforenm", "crypto_box_afternm", "crypto_box_open_afternm", "crypto_box", "crypto_box_open", "K", "crypto_hashblocks_hl", "hh", "hl", "wh", "Int32Array", "wl", "bh0", "bh1", "bh2", "bh3", "bh4", "bh5", "bh6", "bh7", "bl0", "bl1", "bl2", "bl3", "bl4", "bl5", "bl6", "bl7", "th", "tl", "ah0", "ah1", "ah2", "ah3", "ah4", "ah5", "ah6", "ah7", "al0", "al1", "al2", "al3", "al4", "al5", "al6", "al7", "pos", "crypto_hash", "add", "cswap", "pack", "tx", "ty", "zi", "scalarmult", "scalarbase", "crypto_sign_keypair", "pk", "sk", "seeded", "L", "modL", "carry", "reduce", "crypto_sign", "sm", "smlen", "unpackneg", "chk", "num", "den", "den2", "den4", "den6", "crypto_sign_open", "crypto_secretbox_KEYBYTES", "crypto_secretbox_NONCEBYTES", "crypto_secretbox_ZEROBYTES", "crypto_secretbox_BOXZEROBYTES", "crypto_scalarmult_BYTES", "crypto_scalarmult_SCALARBYTES", "crypto_box_PUBLICKEYBYTES", "crypto_box_SECRETKEYBYTES", "crypto_box_BEFORENMBYTES", "crypto_box_NONCEBYTES", "crypto_box_ZEROBYTES", "crypto_box_BOXZEROBYTES", "crypto_sign_BYTES", "crypto_sign_PUBLICKEYBYTES", "crypto_sign_SECRETKEYBYTES", "crypto_sign_SEEDBYTES", "crypto_hash_BYTES", "lowlevel", "checkLengths", "checkBoxLengths", "checkArrayTypes", "arguments", "TypeError", "cleanup", "arr", "randomBytes", "secretbox", "msg", "nonce", "open", "box", "<PERSON><PERSON><PERSON><PERSON>", "non<PERSON><PERSON><PERSON><PERSON>", "overheadLength", "scalarMult", "base", "scalar<PERSON>ength", "groupElementLength", "public<PERSON>ey", "secret<PERSON>ey", "before", "after", "keyPair", "fromSecretKey", "publicKeyLength", "secretKeyLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "signed<PERSON>g", "tmp", "mlen", "detached", "sig", "verify", "fromSeed", "seed", "seedLength", "<PERSON><PERSON><PERSON><PERSON>", "hash", "hash<PERSON><PERSON><PERSON>", "setPRNG", "fn", "crypto", "self", "msCrypto", "getRandomValues", "QUOTA", "min", "require", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Freelancer-Platform-on-Aptos--DAPPS-main/freelancer-escrow-frontend/node_modules/tweetnacl/nacl-fast.js"], "sourcesContent": ["(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n"], "mappings": "AAAA,CAAC,UAASA,IAAI,EAAE;EAChB,YAAY;;EAEZ;EACA;EACA;EACA;EACA;EAEA,IAAIC,EAAE,GAAG,SAAAA,CAASC,IAAI,EAAE;IACtB,IAAIC,CAAC;MAAEC,CAAC,GAAG,IAAIC,YAAY,CAAC,EAAE,CAAC;IAC/B,IAAIH,IAAI,EAAE,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACI,MAAM,EAAEH,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGD,IAAI,CAACC,CAAC,CAAC;IAC1D,OAAOC,CAAC;EACV,CAAC;;EAED;EACA,IAAIG,WAAW,GAAG,SAASA,UAAA,IAAY;IAAE,MAAM,IAAIC,KAAK,CAAC,SAAS,CAAC;EAAE,CAAC;EAEtE,IAAIC,EAAE,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;EAC3B,IAAIC,EAAE,GAAG,IAAID,UAAU,CAAC,EAAE,CAAC;EAAEC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EAEtC,IAAIC,GAAG,GAAGX,EAAE,CAAC,CAAC;IACVY,GAAG,GAAGZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACba,OAAO,GAAGb,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACzBc,CAAC,GAAGd,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACxIe,EAAE,GAAGf,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACzIgB,CAAC,GAAGhB,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACxIiB,CAAC,GAAGjB,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACxIkB,CAAC,GAAGlB,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;EAE5I,SAASmB,IAAIA,CAACC,CAAC,EAAElB,CAAC,EAAEmB,CAAC,EAAEC,CAAC,EAAE;IACxBF,CAAC,CAAClB,CAAC,CAAC,GAAMmB,CAAC,IAAI,EAAE,GAAI,IAAI;IACzBD,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAImB,CAAC,IAAI,EAAE,GAAI,IAAI;IACzBD,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAImB,CAAC,IAAK,CAAC,GAAI,IAAI;IACzBD,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAGmB,CAAC,GAAG,IAAI;IACjBD,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAIoB,CAAC,IAAI,EAAE,GAAK,IAAI;IAC1BF,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAIoB,CAAC,IAAI,EAAE,GAAK,IAAI;IAC1BF,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAIoB,CAAC,IAAK,CAAC,GAAK,IAAI;IAC1BF,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,GAAGoB,CAAC,GAAG,IAAI;EACnB;EAEA,SAASC,EAAEA,CAACH,CAAC,EAAEI,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAEC,CAAC,EAAE;IAC3B,IAAIzB,CAAC;MAAC0B,CAAC,GAAG,CAAC;IACX,KAAK1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAE0B,CAAC,IAAIR,CAAC,CAACI,EAAE,GAACtB,CAAC,CAAC,GAACuB,CAAC,CAACC,EAAE,GAACxB,CAAC,CAAC;IAC5C,OAAO,CAAC,CAAC,GAAK0B,CAAC,GAAG,CAAC,KAAM,CAAE,IAAI,CAAC;EAClC;EAEA,SAASC,gBAAgBA,CAACT,CAAC,EAAEI,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAE;IACtC,OAAOH,EAAE,CAACH,CAAC,EAACI,EAAE,EAACC,CAAC,EAACC,EAAE,EAAC,EAAE,CAAC;EACzB;EAEA,SAASI,gBAAgBA,CAACV,CAAC,EAAEI,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAE;IACtC,OAAOH,EAAE,CAACH,CAAC,EAACI,EAAE,EAACC,CAAC,EAACC,EAAE,EAAC,EAAE,CAAC;EACzB;EAEA,SAASK,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAChC,IAAIC,EAAE,GAAID,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFE,EAAE,GAAIH,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFI,EAAE,GAAIJ,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFK,EAAE,GAAIL,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFM,EAAE,GAAIN,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFO,EAAE,GAAIN,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFO,EAAE,GAAIT,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFU,EAAE,GAAIV,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFW,EAAE,GAAIX,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFY,EAAE,GAAIZ,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFa,GAAG,GAAGX,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFY,GAAG,GAAGb,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFc,GAAG,GAAGd,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFe,GAAG,GAAGf,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFgB,GAAG,GAAGhB,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFiB,GAAG,GAAGhB,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;IAEpF,IAAIiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MACtEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MACvEiB,GAAG,GAAGhB,GAAG;MAAEiB,CAAC;IAEhB,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC9BkE,CAAC,GAAGhB,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIY,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGZ,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,EAAE,IAAIQ,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGR,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAII,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAC1BA,CAAC,GAAGJ,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBR,EAAE,IAAIgB,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGX,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,EAAE,IAAIO,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGP,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAIG,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGH,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBR,EAAE,IAAIe,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGf,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIW,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGN,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBQ,GAAG,IAAIE,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGF,GAAG,GAAGJ,GAAG,GAAG,CAAC;MACjBR,EAAE,IAAIc,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGd,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIU,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGV,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAIM,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAE1BA,CAAC,GAAGD,GAAG,GAAGJ,GAAG,GAAG,CAAC;MACjBR,EAAE,IAAIa,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGb,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIS,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGT,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAIK,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAC1BA,CAAC,GAAGL,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBQ,GAAG,IAAIC,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAE1BA,CAAC,GAAGhB,EAAE,GAAGG,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIe,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGf,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIc,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGd,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIa,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGb,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIgB,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGX,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIU,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGV,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIS,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGT,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIY,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGZ,EAAE,GAAGG,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIW,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGN,GAAG,GAAGD,EAAE,GAAG,CAAC;MAChBE,GAAG,IAAIK,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGL,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBF,EAAE,IAAIQ,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGR,EAAE,GAAGG,GAAG,GAAG,CAAC;MAChBF,EAAE,IAAIO,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGP,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,GAAG,IAAIM,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAE1BA,CAAC,GAAGD,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBF,GAAG,IAAII,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGJ,GAAG,GAAGG,GAAG,GAAG,CAAC;MACjBF,GAAG,IAAIG,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGH,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBE,GAAG,IAAIE,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAC1BA,CAAC,GAAGF,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBE,GAAG,IAAIC,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;IAC5B;IACChB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IAClBiB,EAAE,GAAIA,EAAE,GAAIhB,EAAE,GAAG,CAAC;IACnBiB,GAAG,GAAGA,GAAG,GAAGhB,GAAG,GAAG,CAAC;IACnBiB,GAAG,GAAGA,GAAG,GAAGhB,GAAG,GAAG,CAAC;IACnBiB,GAAG,GAAGA,GAAG,GAAGhB,GAAG,GAAG,CAAC;IACnBiB,GAAG,GAAGA,GAAG,GAAGhB,GAAG,GAAG,CAAC;IACnBiB,GAAG,GAAGA,GAAG,GAAGhB,GAAG,GAAG,CAAC;IACnBiB,GAAG,GAAGA,GAAG,GAAGhB,GAAG,GAAG,CAAC;IAEnBnB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBpB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBpB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBpB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBpB,CAAC,CAAE,CAAC,CAAC,GAAGqB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBrB,CAAC,CAAE,CAAC,CAAC,GAAGqB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBrB,CAAC,CAAE,CAAC,CAAC,GAAGqB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBrB,CAAC,CAAE,CAAC,CAAC,GAAGqB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBrB,CAAC,CAAE,CAAC,CAAC,GAAGsB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBtB,CAAC,CAAE,CAAC,CAAC,GAAGsB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBtB,CAAC,CAAC,EAAE,CAAC,GAAGsB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBtB,CAAC,CAAC,EAAE,CAAC,GAAGsB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBtB,CAAC,CAAC,EAAE,CAAC,GAAGuB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBvB,CAAC,CAAC,EAAE,CAAC,GAAGuB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBvB,CAAC,CAAC,EAAE,CAAC,GAAGuB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBvB,CAAC,CAAC,EAAE,CAAC,GAAGuB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBvB,CAAC,CAAC,EAAE,CAAC,GAAGwB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBxB,CAAC,CAAC,EAAE,CAAC,GAAGwB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBxB,CAAC,CAAC,EAAE,CAAC,GAAGwB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBxB,CAAC,CAAC,EAAE,CAAC,GAAGwB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBxB,CAAC,CAAC,EAAE,CAAC,GAAGyB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBzB,CAAC,CAAC,EAAE,CAAC,GAAGyB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBzB,CAAC,CAAC,EAAE,CAAC,GAAGyB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBzB,CAAC,CAAC,EAAE,CAAC,GAAGyB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBzB,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB1B,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB1B,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB1B,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB1B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB3B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB3B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB3B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB3B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB5B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB5B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB5B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB5B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB7B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB7B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB7B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB7B,CAAC,CAAC,EAAE,CAAC,GAAG8B,GAAG,KAAM,CAAC,GAAG,IAAI;IACzB9B,CAAC,CAAC,EAAE,CAAC,GAAG8B,GAAG,KAAM,CAAC,GAAG,IAAI;IACzB9B,CAAC,CAAC,EAAE,CAAC,GAAG8B,GAAG,KAAK,EAAE,GAAG,IAAI;IACzB9B,CAAC,CAAC,EAAE,CAAC,GAAG8B,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzB9B,CAAC,CAAC,EAAE,CAAC,GAAG+B,GAAG,KAAM,CAAC,GAAG,IAAI;IACzB/B,CAAC,CAAC,EAAE,CAAC,GAAG+B,GAAG,KAAM,CAAC,GAAG,IAAI;IACzB/B,CAAC,CAAC,EAAE,CAAC,GAAG+B,GAAG,KAAK,EAAE,GAAG,IAAI;IACzB/B,CAAC,CAAC,EAAE,CAAC,GAAG+B,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzB/B,CAAC,CAAC,EAAE,CAAC,GAAGgC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBhC,CAAC,CAAC,EAAE,CAAC,GAAGgC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBhC,CAAC,CAAC,EAAE,CAAC,GAAGgC,GAAG,KAAK,EAAE,GAAG,IAAI;IACzBhC,CAAC,CAAC,EAAE,CAAC,GAAGgC,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzBhC,CAAC,CAAC,EAAE,CAAC,GAAGiC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBjC,CAAC,CAAC,EAAE,CAAC,GAAGiC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBjC,CAAC,CAAC,EAAE,CAAC,GAAGiC,GAAG,KAAK,EAAE,GAAG,IAAI;IACzBjC,CAAC,CAAC,EAAE,CAAC,GAAGiC,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzBjC,CAAC,CAAC,EAAE,CAAC,GAAGkC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBlC,CAAC,CAAC,EAAE,CAAC,GAAGkC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBlC,CAAC,CAAC,EAAE,CAAC,GAAGkC,GAAG,KAAK,EAAE,GAAG,IAAI;IACzBlC,CAAC,CAAC,EAAE,CAAC,GAAGkC,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzBlC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBnC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBnC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAK,EAAE,GAAG,IAAI;IACzBnC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAK,EAAE,GAAG,IAAI;EAC3B;EAEA,SAASE,aAAaA,CAACrC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAE;IAC9B,IAAIC,EAAE,GAAID,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFE,EAAE,GAAIH,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFI,EAAE,GAAIJ,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFK,EAAE,GAAIL,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFM,EAAE,GAAIN,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFO,EAAE,GAAIN,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFO,EAAE,GAAIT,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFU,EAAE,GAAIV,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFW,EAAE,GAAIX,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFY,EAAE,GAAIZ,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFa,GAAG,GAAGX,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAE,CAAC,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFY,GAAG,GAAGb,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFc,GAAG,GAAGd,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFe,GAAG,GAAGf,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFgB,GAAG,GAAGhB,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;MAChFiB,GAAG,GAAGhB,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE,GAAG,CAACA,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,KAAG,EAAE;IAEpF,IAAIiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MACtEiB,EAAE,GAAGhB,EAAE;MAAEiB,EAAE,GAAGhB,EAAE;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MAAEiB,GAAG,GAAGhB,GAAG;MACvEiB,GAAG,GAAGhB,GAAG;MAAEiB,CAAC;IAEhB,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC9BkE,CAAC,GAAGhB,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIY,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGZ,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,EAAE,IAAIQ,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGR,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAII,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAC1BA,CAAC,GAAGJ,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBR,EAAE,IAAIgB,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGX,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,EAAE,IAAIO,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGP,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAIG,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGH,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBR,EAAE,IAAIe,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGf,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIW,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGN,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBQ,GAAG,IAAIE,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGF,GAAG,GAAGJ,GAAG,GAAG,CAAC;MACjBR,EAAE,IAAIc,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGd,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIU,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGV,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAIM,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAE1BA,CAAC,GAAGD,GAAG,GAAGJ,GAAG,GAAG,CAAC;MACjBR,EAAE,IAAIa,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGb,EAAE,GAAGY,GAAG,GAAG,CAAC;MAChBR,EAAE,IAAIS,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGT,EAAE,GAAGJ,EAAE,GAAG,CAAC;MACfQ,GAAG,IAAIK,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAC1BA,CAAC,GAAGL,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAChBQ,GAAG,IAAIC,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAE1BA,CAAC,GAAGhB,EAAE,GAAGG,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIe,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGf,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIc,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGd,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIa,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGb,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIgB,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGX,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIU,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGV,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,EAAE,IAAIS,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGT,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIY,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGZ,EAAE,GAAGG,EAAE,GAAG,CAAC;MACfF,EAAE,IAAIW,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAEzBA,CAAC,GAAGN,GAAG,GAAGD,EAAE,GAAG,CAAC;MAChBE,GAAG,IAAIK,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGL,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBF,EAAE,IAAIQ,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACvBA,CAAC,GAAGR,EAAE,GAAGG,GAAG,GAAG,CAAC;MAChBF,EAAE,IAAIO,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MACzBA,CAAC,GAAGP,EAAE,GAAGD,EAAE,GAAG,CAAC;MACfE,GAAG,IAAIM,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAE1BA,CAAC,GAAGD,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBF,GAAG,IAAII,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGJ,GAAG,GAAGG,GAAG,GAAG,CAAC;MACjBF,GAAG,IAAIG,CAAC,IAAE,CAAC,GAAGA,CAAC,KAAI,EAAE,GAAC,CAAE;MACxBA,CAAC,GAAGH,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBE,GAAG,IAAIE,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;MAC1BA,CAAC,GAAGF,GAAG,GAAGD,GAAG,GAAG,CAAC;MACjBE,GAAG,IAAIC,CAAC,IAAE,EAAE,GAAGA,CAAC,KAAI,EAAE,GAAC,EAAG;IAC5B;IAEApC,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBpB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBpB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBpB,CAAC,CAAE,CAAC,CAAC,GAAGoB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBpB,CAAC,CAAE,CAAC,CAAC,GAAGyB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBzB,CAAC,CAAE,CAAC,CAAC,GAAGyB,EAAE,KAAM,CAAC,GAAG,IAAI;IACxBzB,CAAC,CAAE,CAAC,CAAC,GAAGyB,EAAE,KAAK,EAAE,GAAG,IAAI;IACxBzB,CAAC,CAAE,CAAC,CAAC,GAAGyB,EAAE,KAAK,EAAE,GAAG,IAAI;IAExBzB,CAAC,CAAE,CAAC,CAAC,GAAG8B,GAAG,KAAM,CAAC,GAAG,IAAI;IACzB9B,CAAC,CAAE,CAAC,CAAC,GAAG8B,GAAG,KAAM,CAAC,GAAG,IAAI;IACzB9B,CAAC,CAAC,EAAE,CAAC,GAAG8B,GAAG,KAAK,EAAE,GAAG,IAAI;IACzB9B,CAAC,CAAC,EAAE,CAAC,GAAG8B,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzB9B,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBnC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAM,CAAC,GAAG,IAAI;IACzBnC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAK,EAAE,GAAG,IAAI;IACzBnC,CAAC,CAAC,EAAE,CAAC,GAAGmC,GAAG,KAAK,EAAE,GAAG,IAAI;IAEzBnC,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB1B,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB1B,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB1B,CAAC,CAAC,EAAE,CAAC,GAAG0B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB1B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB3B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB3B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB3B,CAAC,CAAC,EAAE,CAAC,GAAG2B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB3B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB5B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB5B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB5B,CAAC,CAAC,EAAE,CAAC,GAAG4B,EAAE,KAAK,EAAE,GAAG,IAAI;IAExB5B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB7B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAM,CAAC,GAAG,IAAI;IACxB7B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAK,EAAE,GAAG,IAAI;IACxB7B,CAAC,CAAC,EAAE,CAAC,GAAG6B,EAAE,KAAK,EAAE,GAAG,IAAI;EAC1B;EAEA,SAASS,mBAAmBA,CAACC,GAAG,EAACC,GAAG,EAACtC,CAAC,EAACC,CAAC,EAAE;IACxCJ,YAAY,CAACwC,GAAG,EAACC,GAAG,EAACtC,CAAC,EAACC,CAAC,CAAC;EAC3B;EAEA,SAASsC,oBAAoBA,CAACF,GAAG,EAACC,GAAG,EAACtC,CAAC,EAACC,CAAC,EAAE;IACzCkC,aAAa,CAACE,GAAG,EAACC,GAAG,EAACtC,CAAC,EAACC,CAAC,CAAC;EAC5B;EAEA,IAAIuC,KAAK,GAAG,IAAIjE,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;EACzF;;EAEZ,SAASkE,yBAAyBA,CAACxC,CAAC,EAACyC,IAAI,EAACC,CAAC,EAACC,IAAI,EAACC,CAAC,EAACpD,CAAC,EAACO,CAAC,EAAE;IACtD,IAAI8C,CAAC,GAAG,IAAIvE,UAAU,CAAC,EAAE,CAAC;MAAEW,CAAC,GAAG,IAAIX,UAAU,CAAC,EAAE,CAAC;IAClD,IAAI2D,CAAC,EAAElE,CAAC;IACR,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE8E,CAAC,CAAC9E,CAAC,CAAC,GAAG,CAAC;IACjC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE8E,CAAC,CAAC9E,CAAC,CAAC,GAAGyB,CAAC,CAACzB,CAAC,CAAC;IACnC,OAAO6E,CAAC,IAAI,EAAE,EAAE;MACdT,mBAAmB,CAAClD,CAAC,EAAC4D,CAAC,EAAC9C,CAAC,EAACwC,KAAK,CAAC;MAChC,KAAKxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEiC,CAAC,CAACyC,IAAI,GAAC1E,CAAC,CAAC,GAAG2E,CAAC,CAACC,IAAI,GAAC5E,CAAC,CAAC,GAAGkB,CAAC,CAAClB,CAAC,CAAC;MACrDkE,CAAC,GAAG,CAAC;MACL,KAAKlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACvBkE,CAAC,GAAGA,CAAC,IAAIY,CAAC,CAAC9E,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QACzB8E,CAAC,CAAC9E,CAAC,CAAC,GAAGkE,CAAC,GAAG,IAAI;QACfA,CAAC,MAAM,CAAC;MACV;MACAW,CAAC,IAAI,EAAE;MACPH,IAAI,IAAI,EAAE;MACVE,IAAI,IAAI,EAAE;IACZ;IACA,IAAIC,CAAC,GAAG,CAAC,EAAE;MACTT,mBAAmB,CAAClD,CAAC,EAAC4D,CAAC,EAAC9C,CAAC,EAACwC,KAAK,CAAC;MAChC,KAAKxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6E,CAAC,EAAE7E,CAAC,EAAE,EAAEiC,CAAC,CAACyC,IAAI,GAAC1E,CAAC,CAAC,GAAG2E,CAAC,CAACC,IAAI,GAAC5E,CAAC,CAAC,GAAGkB,CAAC,CAAClB,CAAC,CAAC;IACtD;IACA,OAAO,CAAC;EACV;EAEA,SAAS+E,qBAAqBA,CAAC9C,CAAC,EAACyC,IAAI,EAACG,CAAC,EAACpD,CAAC,EAACO,CAAC,EAAE;IAC3C,IAAI8C,CAAC,GAAG,IAAIvE,UAAU,CAAC,EAAE,CAAC;MAAEW,CAAC,GAAG,IAAIX,UAAU,CAAC,EAAE,CAAC;IAClD,IAAI2D,CAAC,EAAElE,CAAC;IACR,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE8E,CAAC,CAAC9E,CAAC,CAAC,GAAG,CAAC;IACjC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE8E,CAAC,CAAC9E,CAAC,CAAC,GAAGyB,CAAC,CAACzB,CAAC,CAAC;IACnC,OAAO6E,CAAC,IAAI,EAAE,EAAE;MACdT,mBAAmB,CAAClD,CAAC,EAAC4D,CAAC,EAAC9C,CAAC,EAACwC,KAAK,CAAC;MAChC,KAAKxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEiC,CAAC,CAACyC,IAAI,GAAC1E,CAAC,CAAC,GAAGkB,CAAC,CAAClB,CAAC,CAAC;MACzCkE,CAAC,GAAG,CAAC;MACL,KAAKlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACvBkE,CAAC,GAAGA,CAAC,IAAIY,CAAC,CAAC9E,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QACzB8E,CAAC,CAAC9E,CAAC,CAAC,GAAGkE,CAAC,GAAG,IAAI;QACfA,CAAC,MAAM,CAAC;MACV;MACAW,CAAC,IAAI,EAAE;MACPH,IAAI,IAAI,EAAE;IACZ;IACA,IAAIG,CAAC,GAAG,CAAC,EAAE;MACTT,mBAAmB,CAAClD,CAAC,EAAC4D,CAAC,EAAC9C,CAAC,EAACwC,KAAK,CAAC;MAChC,KAAKxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6E,CAAC,EAAE7E,CAAC,EAAE,EAAEiC,CAAC,CAACyC,IAAI,GAAC1E,CAAC,CAAC,GAAGkB,CAAC,CAAClB,CAAC,CAAC;IAC1C;IACA,OAAO,CAAC;EACV;EAEA,SAASgF,aAAaA,CAAC/C,CAAC,EAACyC,IAAI,EAAChD,CAAC,EAACD,CAAC,EAACO,CAAC,EAAE;IACnC,IAAIiD,CAAC,GAAG,IAAI1E,UAAU,CAAC,EAAE,CAAC;IAC1BgE,oBAAoB,CAACU,CAAC,EAACxD,CAAC,EAACO,CAAC,EAACwC,KAAK,CAAC;IACjC,IAAIU,EAAE,GAAG,IAAI3E,UAAU,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAEkF,EAAE,CAAClF,CAAC,CAAC,GAAGyB,CAAC,CAACzB,CAAC,GAAC,EAAE,CAAC;IAC3C,OAAO+E,qBAAqB,CAAC9C,CAAC,EAACyC,IAAI,EAAChD,CAAC,EAACwD,EAAE,EAACD,CAAC,CAAC;EAC7C;EAEA,SAASE,iBAAiBA,CAAClD,CAAC,EAACyC,IAAI,EAACC,CAAC,EAACC,IAAI,EAAClD,CAAC,EAACD,CAAC,EAACO,CAAC,EAAE;IAC9C,IAAIiD,CAAC,GAAG,IAAI1E,UAAU,CAAC,EAAE,CAAC;IAC1BgE,oBAAoB,CAACU,CAAC,EAACxD,CAAC,EAACO,CAAC,EAACwC,KAAK,CAAC;IACjC,IAAIU,EAAE,GAAG,IAAI3E,UAAU,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAEkF,EAAE,CAAClF,CAAC,CAAC,GAAGyB,CAAC,CAACzB,CAAC,GAAC,EAAE,CAAC;IAC3C,OAAOyE,yBAAyB,CAACxC,CAAC,EAACyC,IAAI,EAACC,CAAC,EAACC,IAAI,EAAClD,CAAC,EAACwD,EAAE,EAACD,CAAC,CAAC;EACxD;;EAEA;AACA;AACA;AACA;;EAEA,IAAIG,QAAQ,GAAG,SAAAA,CAASC,GAAG,EAAE;IAC3B,IAAI,CAACC,MAAM,GAAG,IAAI/E,UAAU,CAAC,EAAE,CAAC;IAChC,IAAI,CAACN,CAAC,GAAG,IAAIsF,WAAW,CAAC,EAAE,CAAC;IAC5B,IAAI,CAACpE,CAAC,GAAG,IAAIoE,WAAW,CAAC,EAAE,CAAC;IAC5B,IAAI,CAACC,GAAG,GAAG,IAAID,WAAW,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACE,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,GAAG,GAAG,CAAC;IAEZ,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElCP,EAAE,GAAGN,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAK0F,EAAE,GAAyB,MAAM;IAC5FC,EAAE,GAAGP,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE0F,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;IAC5FC,EAAE,GAAGR,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE2F,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;IAC5FC,EAAE,GAAGT,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE4F,EAAE,KAAM,CAAC,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;IAC5FC,EAAE,GAAGV,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE6F,EAAE,KAAM,CAAC,GAAKC,EAAE,IAAI,EAAG,IAAI,MAAM;IAC5F,IAAI,CAAC9F,CAAC,CAAC,CAAC,CAAC,GAAK8F,EAAE,KAAM,CAAC,GAAK,MAAM;IAClCC,EAAE,GAAGX,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE8F,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;IAC5FC,EAAE,GAAGZ,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE+F,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;IAC5FC,EAAE,GAAGb,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IAAE,IAAI,CAACpF,CAAC,CAAC,CAAC,CAAC,GAAG,CAAEgG,EAAE,KAAM,CAAC,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;IAC5F,IAAI,CAACjG,CAAC,CAAC,CAAC,CAAC,GAAKiG,EAAE,KAAM,CAAC,GAAK,MAAM;IAElC,IAAI,CAACV,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IACpD,IAAI,CAACG,GAAG,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;EACtD,CAAC;EAEDD,QAAQ,CAACe,SAAS,CAACC,MAAM,GAAG,UAASzB,CAAC,EAAEC,IAAI,EAAEyB,KAAK,EAAE;IACnD,IAAIC,KAAK,GAAG,IAAI,CAACZ,GAAG,GAAG,CAAC,GAAI,CAAC,IAAI,EAAG;IACpC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEjE,CAAC;IACrC,IAAIsE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAE1C,IAAIC,EAAE,GAAG,IAAI,CAAC9F,CAAC,CAAC,CAAC,CAAC;MACd+F,EAAE,GAAG,IAAI,CAAC/F,CAAC,CAAC,CAAC,CAAC;MACdgG,EAAE,GAAG,IAAI,CAAChG,CAAC,CAAC,CAAC,CAAC;MACdiG,EAAE,GAAG,IAAI,CAACjG,CAAC,CAAC,CAAC,CAAC;MACdkG,EAAE,GAAG,IAAI,CAAClG,CAAC,CAAC,CAAC,CAAC;MACdmG,EAAE,GAAG,IAAI,CAACnG,CAAC,CAAC,CAAC,CAAC;MACdoG,EAAE,GAAG,IAAI,CAACpG,CAAC,CAAC,CAAC,CAAC;MACdqG,EAAE,GAAG,IAAI,CAACrG,CAAC,CAAC,CAAC,CAAC;MACdsG,EAAE,GAAG,IAAI,CAACtG,CAAC,CAAC,CAAC,CAAC;MACduG,EAAE,GAAG,IAAI,CAACvG,CAAC,CAAC,CAAC,CAAC;IAElB,IAAIwG,EAAE,GAAG,IAAI,CAAC1H,CAAC,CAAC,CAAC,CAAC;MACd2H,EAAE,GAAG,IAAI,CAAC3H,CAAC,CAAC,CAAC,CAAC;MACd4H,EAAE,GAAG,IAAI,CAAC5H,CAAC,CAAC,CAAC,CAAC;MACd6H,EAAE,GAAG,IAAI,CAAC7H,CAAC,CAAC,CAAC,CAAC;MACd8H,EAAE,GAAG,IAAI,CAAC9H,CAAC,CAAC,CAAC,CAAC;MACd+H,EAAE,GAAG,IAAI,CAAC/H,CAAC,CAAC,CAAC,CAAC;MACdgI,EAAE,GAAG,IAAI,CAAChI,CAAC,CAAC,CAAC,CAAC;MACdiI,EAAE,GAAG,IAAI,CAACjI,CAAC,CAAC,CAAC,CAAC;MACdkI,EAAE,GAAG,IAAI,CAAClI,CAAC,CAAC,CAAC,CAAC;MACdmI,EAAE,GAAG,IAAI,CAACnI,CAAC,CAAC,CAAC,CAAC;IAElB,OAAOoG,KAAK,IAAI,EAAE,EAAE;MAClBV,EAAE,GAAGhB,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;MAAEqC,EAAE,IAAMtB,EAAE,GAAyB,MAAM;MAC5FC,EAAE,GAAGjB,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;MAAEsC,EAAE,IAAI,CAAEvB,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;MAC5FC,EAAE,GAAGlB,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;MAAEuC,EAAE,IAAI,CAAEvB,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;MAC5FC,EAAE,GAAGnB,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;MAAEwC,EAAE,IAAI,CAAEvB,EAAE,KAAM,CAAC,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;MAC5FC,EAAE,GAAGpB,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAE,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;MAAEyC,EAAE,IAAI,CAAEvB,EAAE,KAAM,CAAC,GAAKC,EAAE,IAAI,EAAG,IAAI,MAAM;MAC5FuB,EAAE,IAAMvB,EAAE,KAAM,CAAC,GAAK,MAAM;MAC5BC,EAAE,GAAGrB,CAAC,CAACC,IAAI,GAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;MAAE2C,EAAE,IAAI,CAAExB,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;MAC5FC,EAAE,GAAGtB,CAAC,CAACC,IAAI,GAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;MAAE4C,EAAE,IAAI,CAAExB,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;MAC5FC,EAAE,GAAGvB,CAAC,CAACC,IAAI,GAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,CAACC,IAAI,GAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;MAAE6C,EAAE,IAAI,CAAExB,EAAE,KAAM,CAAC,GAAKC,EAAE,IAAK,CAAE,IAAI,MAAM;MAC5FwB,EAAE,IAAMxB,EAAE,KAAK,CAAC,GAAKI,KAAK;MAE1BrE,CAAC,GAAG,CAAC;MAELsE,EAAE,GAAGtE,CAAC;MACNsE,EAAE,IAAIU,EAAE,GAAGU,EAAE;MACbpB,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGkB,EAAE,CAAC;MACnB7B,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGgB,EAAE,CAAC;MACnB5B,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGc,EAAE,CAAC;MACnB3B,EAAE,IAAIc,EAAE,IAAI,CAAC,GAAGY,EAAE,CAAC;MACnBhG,CAAC,GAAIsE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIe,EAAE,IAAI,CAAC,GAAGU,EAAE,CAAC;MACnBzB,EAAE,IAAIgB,EAAE,IAAI,CAAC,GAAGQ,EAAE,CAAC;MACnBxB,EAAE,IAAIiB,EAAE,IAAI,CAAC,GAAGM,EAAE,CAAC;MACnBvB,EAAE,IAAIkB,EAAE,IAAI,CAAC,GAAGI,EAAE,CAAC;MACnBtB,EAAE,IAAImB,EAAE,IAAI,CAAC,GAAGE,EAAE,CAAC;MACnB3F,CAAC,IAAKsE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAGvE,CAAC;MACNuE,EAAE,IAAIS,EAAE,GAAGW,EAAE;MACbpB,EAAE,IAAIU,EAAE,GAAGS,EAAE;MACbnB,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGiB,EAAE,CAAC;MACnB5B,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGe,EAAE,CAAC;MACnB3B,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGa,EAAE,CAAC;MACnBjG,CAAC,GAAIuE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIc,EAAE,IAAI,CAAC,GAAGW,EAAE,CAAC;MACnBzB,EAAE,IAAIe,EAAE,IAAI,CAAC,GAAGS,EAAE,CAAC;MACnBxB,EAAE,IAAIgB,EAAE,IAAI,CAAC,GAAGO,EAAE,CAAC;MACnBvB,EAAE,IAAIiB,EAAE,IAAI,CAAC,GAAGK,EAAE,CAAC;MACnBtB,EAAE,IAAIkB,EAAE,IAAI,CAAC,GAAGG,EAAE,CAAC;MACnB5F,CAAC,IAAKuE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAGxE,CAAC;MACNwE,EAAE,IAAIQ,EAAE,GAAGY,EAAE;MACbpB,EAAE,IAAIS,EAAE,GAAGU,EAAE;MACbnB,EAAE,IAAIU,EAAE,GAAGQ,EAAE;MACblB,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGgB,EAAE,CAAC;MACnB3B,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGc,EAAE,CAAC;MACnBlG,CAAC,GAAIwE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGY,EAAE,CAAC;MACnBzB,EAAE,IAAIc,EAAE,IAAI,CAAC,GAAGU,EAAE,CAAC;MACnBxB,EAAE,IAAIe,EAAE,IAAI,CAAC,GAAGQ,EAAE,CAAC;MACnBvB,EAAE,IAAIgB,EAAE,IAAI,CAAC,GAAGM,EAAE,CAAC;MACnBtB,EAAE,IAAIiB,EAAE,IAAI,CAAC,GAAGI,EAAE,CAAC;MACnB7F,CAAC,IAAKwE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAGzE,CAAC;MACNyE,EAAE,IAAIO,EAAE,GAAGa,EAAE;MACbpB,EAAE,IAAIQ,EAAE,GAAGW,EAAE;MACbnB,EAAE,IAAIS,EAAE,GAAGS,EAAE;MACblB,EAAE,IAAIU,EAAE,GAAGO,EAAE;MACbjB,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGe,EAAE,CAAC;MACnBnG,CAAC,GAAIyE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGa,EAAE,CAAC;MACnBzB,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGW,EAAE,CAAC;MACnBxB,EAAE,IAAIc,EAAE,IAAI,CAAC,GAAGS,EAAE,CAAC;MACnBvB,EAAE,IAAIe,EAAE,IAAI,CAAC,GAAGO,EAAE,CAAC;MACnBtB,EAAE,IAAIgB,EAAE,IAAI,CAAC,GAAGK,EAAE,CAAC;MACnB9F,CAAC,IAAKyE,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAG1E,CAAC;MACN0E,EAAE,IAAIM,EAAE,GAAGc,EAAE;MACbpB,EAAE,IAAIO,EAAE,GAAGY,EAAE;MACbnB,EAAE,IAAIQ,EAAE,GAAGU,EAAE;MACblB,EAAE,IAAIS,EAAE,GAAGQ,EAAE;MACbjB,EAAE,IAAIU,EAAE,GAAGM,EAAE;MACb1F,CAAC,GAAI0E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGc,EAAE,CAAC;MACnBzB,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGY,EAAE,CAAC;MACnBxB,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGU,EAAE,CAAC;MACnBvB,EAAE,IAAIc,EAAE,IAAI,CAAC,GAAGQ,EAAE,CAAC;MACnBtB,EAAE,IAAIe,EAAE,IAAI,CAAC,GAAGM,EAAE,CAAC;MACnB/F,CAAC,IAAK0E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAG3E,CAAC;MACN2E,EAAE,IAAIK,EAAE,GAAGe,EAAE;MACbpB,EAAE,IAAIM,EAAE,GAAGa,EAAE;MACbnB,EAAE,IAAIO,EAAE,GAAGW,EAAE;MACblB,EAAE,IAAIQ,EAAE,GAAGS,EAAE;MACbjB,EAAE,IAAIS,EAAE,GAAGO,EAAE;MACb3F,CAAC,GAAI2E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIU,EAAE,GAAGK,EAAE;MACbf,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGa,EAAE,CAAC;MACnBxB,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGW,EAAE,CAAC;MACnBvB,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGS,EAAE,CAAC;MACnBtB,EAAE,IAAIc,EAAE,IAAI,CAAC,GAAGO,EAAE,CAAC;MACnBhG,CAAC,IAAK2E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAG5E,CAAC;MACN4E,EAAE,IAAII,EAAE,GAAGgB,EAAE;MACbpB,EAAE,IAAIK,EAAE,GAAGc,EAAE;MACbnB,EAAE,IAAIM,EAAE,GAAGY,EAAE;MACblB,EAAE,IAAIO,EAAE,GAAGU,EAAE;MACbjB,EAAE,IAAIQ,EAAE,GAAGQ,EAAE;MACb5F,CAAC,GAAI4E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIS,EAAE,GAAGM,EAAE;MACbf,EAAE,IAAIU,EAAE,GAAGI,EAAE;MACbd,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGY,EAAE,CAAC;MACnBvB,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGU,EAAE,CAAC;MACnBtB,EAAE,IAAIa,EAAE,IAAI,CAAC,GAAGQ,EAAE,CAAC;MACnBjG,CAAC,IAAK4E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAG7E,CAAC;MACN6E,EAAE,IAAIG,EAAE,GAAGiB,EAAE;MACbpB,EAAE,IAAII,EAAE,GAAGe,EAAE;MACbnB,EAAE,IAAIK,EAAE,GAAGa,EAAE;MACblB,EAAE,IAAIM,EAAE,GAAGW,EAAE;MACbjB,EAAE,IAAIO,EAAE,GAAGS,EAAE;MACb7F,CAAC,GAAI6E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIQ,EAAE,GAAGO,EAAE;MACbf,EAAE,IAAIS,EAAE,GAAGK,EAAE;MACbd,EAAE,IAAIU,EAAE,GAAGG,EAAE;MACbb,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGW,EAAE,CAAC;MACnBtB,EAAE,IAAIY,EAAE,IAAI,CAAC,GAAGS,EAAE,CAAC;MACnBlG,CAAC,IAAK6E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAG9E,CAAC;MACN8E,EAAE,IAAIE,EAAE,GAAGkB,EAAE;MACbpB,EAAE,IAAIG,EAAE,GAAGgB,EAAE;MACbnB,EAAE,IAAII,EAAE,GAAGc,EAAE;MACblB,EAAE,IAAIK,EAAE,GAAGY,EAAE;MACbjB,EAAE,IAAIM,EAAE,GAAGU,EAAE;MACb9F,CAAC,GAAI8E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIO,EAAE,GAAGQ,EAAE;MACbf,EAAE,IAAIQ,EAAE,GAAGM,EAAE;MACbd,EAAE,IAAIS,EAAE,GAAGI,EAAE;MACbb,EAAE,IAAIU,EAAE,GAAGE,EAAE;MACbZ,EAAE,IAAIW,EAAE,IAAI,CAAC,GAAGU,EAAE,CAAC;MACnBnG,CAAC,IAAK8E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9BC,EAAE,GAAG/E,CAAC;MACN+E,EAAE,IAAIC,EAAE,GAAGmB,EAAE;MACbpB,EAAE,IAAIE,EAAE,GAAGiB,EAAE;MACbnB,EAAE,IAAIG,EAAE,GAAGe,EAAE;MACblB,EAAE,IAAII,EAAE,GAAGa,EAAE;MACbjB,EAAE,IAAIK,EAAE,GAAGW,EAAE;MACb/F,CAAC,GAAI+E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAC7BA,EAAE,IAAIM,EAAE,GAAGS,EAAE;MACbf,EAAE,IAAIO,EAAE,GAAGO,EAAE;MACbd,EAAE,IAAIQ,EAAE,GAAGK,EAAE;MACbb,EAAE,IAAIS,EAAE,GAAGG,EAAE;MACbZ,EAAE,IAAIU,EAAE,GAAGC,EAAE;MACb1F,CAAC,IAAK+E,EAAE,KAAK,EAAG;MAAEA,EAAE,IAAI,MAAM;MAE9B/E,CAAC,GAAK,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAK,CAAC;MACxBA,CAAC,GAAIA,CAAC,GAAGsE,EAAE,GAAI,CAAC;MAChBA,EAAE,GAAGtE,CAAC,GAAG,MAAM;MACfA,CAAC,GAAIA,CAAC,KAAK,EAAG;MACduE,EAAE,IAAIvE,CAAC;MAEPgF,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MACPW,EAAE,GAAGV,EAAE;MAEPpC,IAAI,IAAI,EAAE;MACVyB,KAAK,IAAI,EAAE;IACb;IACA,IAAI,CAAClF,CAAC,CAAC,CAAC,CAAC,GAAG8F,EAAE;IACd,IAAI,CAAC9F,CAAC,CAAC,CAAC,CAAC,GAAG+F,EAAE;IACd,IAAI,CAAC/F,CAAC,CAAC,CAAC,CAAC,GAAGgG,EAAE;IACd,IAAI,CAAChG,CAAC,CAAC,CAAC,CAAC,GAAGiG,EAAE;IACd,IAAI,CAACjG,CAAC,CAAC,CAAC,CAAC,GAAGkG,EAAE;IACd,IAAI,CAAClG,CAAC,CAAC,CAAC,CAAC,GAAGmG,EAAE;IACd,IAAI,CAACnG,CAAC,CAAC,CAAC,CAAC,GAAGoG,EAAE;IACd,IAAI,CAACpG,CAAC,CAAC,CAAC,CAAC,GAAGqG,EAAE;IACd,IAAI,CAACrG,CAAC,CAAC,CAAC,CAAC,GAAGsG,EAAE;IACd,IAAI,CAACtG,CAAC,CAAC,CAAC,CAAC,GAAGuG,EAAE;EAChB,CAAC;EAEDtC,QAAQ,CAACe,SAAS,CAACkC,MAAM,GAAG,UAASC,GAAG,EAAEC,MAAM,EAAE;IAChD,IAAIC,CAAC,GAAG,IAAIjD,WAAW,CAAC,EAAE,CAAC;IAC3B,IAAItD,CAAC,EAAEwG,IAAI,EAAEC,CAAC,EAAE1I,CAAC;IAEjB,IAAI,IAAI,CAACyF,QAAQ,EAAE;MACjBzF,CAAC,GAAG,IAAI,CAACyF,QAAQ;MACjB,IAAI,CAACH,MAAM,CAACtF,CAAC,EAAE,CAAC,GAAG,CAAC;MACpB,OAAOA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE,IAAI,CAACsF,MAAM,CAACtF,CAAC,CAAC,GAAG,CAAC;MACtC,IAAI,CAAC0F,GAAG,GAAG,CAAC;MACZ,IAAI,CAACU,MAAM,CAAC,IAAI,CAACd,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IACjC;IAEArD,CAAC,GAAG,IAAI,CAACd,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;IACpB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM;IACnB,KAAKnB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvB,IAAI,CAACmB,CAAC,CAACnB,CAAC,CAAC,IAAIiC,CAAC;MACdA,CAAC,GAAG,IAAI,CAACd,CAAC,CAACnB,CAAC,CAAC,KAAK,EAAE;MACpB,IAAI,CAACmB,CAAC,CAACnB,CAAC,CAAC,IAAI,MAAM;IACrB;IACA,IAAI,CAACmB,CAAC,CAAC,CAAC,CAAC,IAAKc,CAAC,GAAG,CAAE;IACpBA,CAAC,GAAG,IAAI,CAACd,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;IACpB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM;IACnB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAIc,CAAC;IACdA,CAAC,GAAG,IAAI,CAACd,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;IACpB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM;IACnB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAIc,CAAC;IAEduG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACrH,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACpBc,CAAC,GAAGuG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;IACfA,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM;IACd,KAAKxI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvBwI,CAAC,CAACxI,CAAC,CAAC,GAAG,IAAI,CAACmB,CAAC,CAACnB,CAAC,CAAC,GAAGiC,CAAC;MACpBA,CAAC,GAAGuG,CAAC,CAACxI,CAAC,CAAC,KAAK,EAAE;MACfwI,CAAC,CAACxI,CAAC,CAAC,IAAI,MAAM;IAChB;IACAwI,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,IAAI,EAAG;IAEjBC,IAAI,GAAG,CAACxG,CAAC,GAAG,CAAC,IAAI,CAAC;IAClB,KAAKjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEwI,CAAC,CAACxI,CAAC,CAAC,IAAIyI,IAAI;IACrCA,IAAI,GAAG,CAACA,IAAI;IACZ,KAAKzI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE,IAAI,CAACmB,CAAC,CAACnB,CAAC,CAAC,GAAI,IAAI,CAACmB,CAAC,CAACnB,CAAC,CAAC,GAAGyI,IAAI,GAAID,CAAC,CAACxI,CAAC,CAAC;IAE9D,IAAI,CAACmB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAY,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,IAAwB,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,IAAwB,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAK,CAAE,IAAwB,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAK,CAAE,IAAwB,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAK,CAAE,GAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,IAAI,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG,IAAwB,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAK,CAAE,IAAwB,MAAM;IACjF,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAE,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAK,CAAE,IAAwB,MAAM;IAEjFuH,CAAC,GAAG,IAAI,CAACvH,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACqE,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACrE,CAAC,CAAC,CAAC,CAAC,GAAGuH,CAAC,GAAG,MAAM;IACtB,KAAK1I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtB0I,CAAC,GAAI,CAAE,IAAI,CAACvH,CAAC,CAACnB,CAAC,CAAC,GAAG,IAAI,CAACwF,GAAG,CAACxF,CAAC,CAAC,GAAI,CAAC,KAAK0I,CAAC,KAAK,EAAE,CAAC,GAAI,CAAC;MACtD,IAAI,CAACvH,CAAC,CAACnB,CAAC,CAAC,GAAG0I,CAAC,GAAG,MAAM;IACxB;IAEAJ,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAE,CAAC,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAC,EAAE,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAC,EAAE,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAC,EAAE,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAC,EAAE,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAC,EAAE,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACzCmH,GAAG,CAACC,MAAM,GAAC,EAAE,CAAC,GAAI,IAAI,CAACpH,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;EAC3C,CAAC;EAEDiE,QAAQ,CAACe,SAAS,CAACwC,MAAM,GAAG,UAAShE,CAAC,EAAEC,IAAI,EAAEyB,KAAK,EAAE;IACnD,IAAIrG,CAAC,EAAE4I,IAAI;IAEX,IAAI,IAAI,CAACnD,QAAQ,EAAE;MACjBmD,IAAI,GAAI,EAAE,GAAG,IAAI,CAACnD,QAAS;MAC3B,IAAImD,IAAI,GAAGvC,KAAK,EACduC,IAAI,GAAGvC,KAAK;MACd,KAAKrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,IAAI,EAAE5I,CAAC,EAAE,EACvB,IAAI,CAACsF,MAAM,CAAC,IAAI,CAACG,QAAQ,GAAGzF,CAAC,CAAC,GAAG2E,CAAC,CAACC,IAAI,GAAC5E,CAAC,CAAC;MAC5CqG,KAAK,IAAIuC,IAAI;MACbhE,IAAI,IAAIgE,IAAI;MACZ,IAAI,CAACnD,QAAQ,IAAImD,IAAI;MACrB,IAAI,IAAI,CAACnD,QAAQ,GAAG,EAAE,EACpB;MACF,IAAI,CAACW,MAAM,CAAC,IAAI,CAACd,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;MAC/B,IAAI,CAACG,QAAQ,GAAG,CAAC;IACnB;IAEA,IAAIY,KAAK,IAAI,EAAE,EAAE;MACfuC,IAAI,GAAGvC,KAAK,GAAIA,KAAK,GAAG,EAAG;MAC3B,IAAI,CAACD,MAAM,CAACzB,CAAC,EAAEC,IAAI,EAAEgE,IAAI,CAAC;MAC1BhE,IAAI,IAAIgE,IAAI;MACZvC,KAAK,IAAIuC,IAAI;IACf;IAEA,IAAIvC,KAAK,EAAE;MACT,KAAKrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,KAAK,EAAErG,CAAC,EAAE,EACxB,IAAI,CAACsF,MAAM,CAAC,IAAI,CAACG,QAAQ,GAAGzF,CAAC,CAAC,GAAG2E,CAAC,CAACC,IAAI,GAAC5E,CAAC,CAAC;MAC5C,IAAI,CAACyF,QAAQ,IAAIY,KAAK;IACxB;EACF,CAAC;EAED,SAASwC,kBAAkBA,CAACxE,GAAG,EAAEyE,MAAM,EAAEnE,CAAC,EAAEC,IAAI,EAAEnD,CAAC,EAAEO,CAAC,EAAE;IACtD,IAAIiD,CAAC,GAAG,IAAIG,QAAQ,CAACpD,CAAC,CAAC;IACvBiD,CAAC,CAAC0D,MAAM,CAAChE,CAAC,EAAEC,IAAI,EAAEnD,CAAC,CAAC;IACpBwD,CAAC,CAACoD,MAAM,CAAChE,GAAG,EAAEyE,MAAM,CAAC;IACrB,OAAO,CAAC;EACV;EAEA,SAASC,yBAAyBA,CAAC5H,CAAC,EAAE6H,IAAI,EAAErE,CAAC,EAAEC,IAAI,EAAEnD,CAAC,EAAEO,CAAC,EAAE;IACzD,IAAId,CAAC,GAAG,IAAIX,UAAU,CAAC,EAAE,CAAC;IAC1BsI,kBAAkB,CAAC3H,CAAC,EAAC,CAAC,EAACyD,CAAC,EAACC,IAAI,EAACnD,CAAC,EAACO,CAAC,CAAC;IAClC,OAAOL,gBAAgB,CAACR,CAAC,EAAC6H,IAAI,EAAC9H,CAAC,EAAC,CAAC,CAAC;EACrC;EAEA,SAAS+H,gBAAgBA,CAAChH,CAAC,EAAC0C,CAAC,EAACjD,CAAC,EAACD,CAAC,EAACO,CAAC,EAAE;IACnC,IAAIhC,CAAC;IACL,IAAI0B,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;IACrByD,iBAAiB,CAAClD,CAAC,EAAC,CAAC,EAAC0C,CAAC,EAAC,CAAC,EAACjD,CAAC,EAACD,CAAC,EAACO,CAAC,CAAC;IAChC6G,kBAAkB,CAAC5G,CAAC,EAAE,EAAE,EAAEA,CAAC,EAAE,EAAE,EAAEP,CAAC,GAAG,EAAE,EAAEO,CAAC,CAAC;IAC3C,KAAKjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEiC,CAAC,CAACjC,CAAC,CAAC,GAAG,CAAC;IACjC,OAAO,CAAC;EACV;EAEA,SAASkJ,qBAAqBA,CAACvE,CAAC,EAAC1C,CAAC,EAACP,CAAC,EAACD,CAAC,EAACO,CAAC,EAAE;IACxC,IAAIhC,CAAC;IACL,IAAIkB,CAAC,GAAG,IAAIX,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAImB,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;IACrBsD,aAAa,CAAC9D,CAAC,EAAC,CAAC,EAAC,EAAE,EAACO,CAAC,EAACO,CAAC,CAAC;IACzB,IAAI+G,yBAAyB,CAAC9G,CAAC,EAAE,EAAE,EAACA,CAAC,EAAE,EAAE,EAACP,CAAC,GAAG,EAAE,EAACR,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IACpEiE,iBAAiB,CAACR,CAAC,EAAC,CAAC,EAAC1C,CAAC,EAAC,CAAC,EAACP,CAAC,EAACD,CAAC,EAACO,CAAC,CAAC;IAChC,KAAKhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,CAAC,GAAG,CAAC;IACjC,OAAO,CAAC;EACV;EAEA,SAASmJ,QAAQA,CAAClJ,CAAC,EAAEmJ,CAAC,EAAE;IACtB,IAAIpJ,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGoJ,CAAC,CAACpJ,CAAC,CAAC,GAAC,CAAC;EACxC;EAEA,SAASqJ,QAAQA,CAACvH,CAAC,EAAE;IACnB,IAAI9B,CAAC;MAAEsJ,CAAC;MAAErH,CAAC,GAAG,CAAC;IACf,KAAKjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvBsJ,CAAC,GAAGxH,CAAC,CAAC9B,CAAC,CAAC,GAAGiC,CAAC,GAAG,KAAK;MACpBA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;MACzBxH,CAAC,CAAC9B,CAAC,CAAC,GAAGsJ,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACtB;IACAH,CAAC,CAAC,CAAC,CAAC,IAAIG,CAAC,GAAC,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAC,CAAC,CAAC;EAC1B;EAEA,SAASwH,QAAQA,CAAC1H,CAAC,EAAE2H,CAAC,EAAE7E,CAAC,EAAE;IACzB,IAAI8E,CAAC;MAAE1H,CAAC,GAAG,EAAE4C,CAAC,GAAC,CAAC,CAAC;IACjB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B2J,CAAC,GAAG1H,CAAC,IAAIF,CAAC,CAAC/B,CAAC,CAAC,GAAG0J,CAAC,CAAC1J,CAAC,CAAC,CAAC;MACrB+B,CAAC,CAAC/B,CAAC,CAAC,IAAI2J,CAAC;MACTD,CAAC,CAAC1J,CAAC,CAAC,IAAI2J,CAAC;IACX;EACF;EAEA,SAASC,SAASA,CAAC9H,CAAC,EAAEL,CAAC,EAAE;IACvB,IAAIzB,CAAC,EAAE6J,CAAC,EAAEhF,CAAC;IACX,IAAIF,CAAC,GAAG7E,EAAE,CAAC,CAAC;MAAE6J,CAAC,GAAG7J,EAAE,CAAC,CAAC;IACtB,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE2J,CAAC,CAAC3J,CAAC,CAAC,GAAGyB,CAAC,CAACzB,CAAC,CAAC;IACpCqJ,QAAQ,CAACM,CAAC,CAAC;IACXN,QAAQ,CAACM,CAAC,CAAC;IACXN,QAAQ,CAACM,CAAC,CAAC;IACX,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtBlF,CAAC,CAAC,CAAC,CAAC,GAAGgF,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM;MACpB,KAAK3J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACvB2E,CAAC,CAAC3E,CAAC,CAAC,GAAG2J,CAAC,CAAC3J,CAAC,CAAC,GAAG,MAAM,IAAK2E,CAAC,CAAC3E,CAAC,GAAC,CAAC,CAAC,IAAE,EAAE,GAAI,CAAC,CAAC;QACzC2E,CAAC,CAAC3E,CAAC,GAAC,CAAC,CAAC,IAAI,MAAM;MAClB;MACA2E,CAAC,CAAC,EAAE,CAAC,GAAGgF,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,IAAKhF,CAAC,CAAC,EAAE,CAAC,IAAE,EAAE,GAAI,CAAC,CAAC;MAC1CE,CAAC,GAAIF,CAAC,CAAC,EAAE,CAAC,IAAE,EAAE,GAAI,CAAC;MACnBA,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM;MACf8E,QAAQ,CAACE,CAAC,EAAEhF,CAAC,EAAE,CAAC,GAACE,CAAC,CAAC;IACrB;IACA,KAAK7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvB8B,CAAC,CAAC,CAAC,GAAC9B,CAAC,CAAC,GAAG2J,CAAC,CAAC3J,CAAC,CAAC,GAAG,IAAI;MACpB8B,CAAC,CAAC,CAAC,GAAC9B,CAAC,GAAC,CAAC,CAAC,GAAG2J,CAAC,CAAC3J,CAAC,CAAC,IAAE,CAAC;IACpB;EACF;EAEA,SAAS8J,QAAQA,CAACV,CAAC,EAAEvE,CAAC,EAAE;IACtB,IAAI5C,CAAC,GAAG,IAAI1B,UAAU,CAAC,EAAE,CAAC;MAAEmB,CAAC,GAAG,IAAInB,UAAU,CAAC,EAAE,CAAC;IAClDqJ,SAAS,CAAC3H,CAAC,EAAEmH,CAAC,CAAC;IACfQ,SAAS,CAAClI,CAAC,EAAEmD,CAAC,CAAC;IACf,OAAOjD,gBAAgB,CAACK,CAAC,EAAE,CAAC,EAAEP,CAAC,EAAE,CAAC,CAAC;EACrC;EAEA,SAASqI,QAAQA,CAACX,CAAC,EAAE;IACnB,IAAI1H,CAAC,GAAG,IAAInB,UAAU,CAAC,EAAE,CAAC;IAC1BqJ,SAAS,CAAClI,CAAC,EAAE0H,CAAC,CAAC;IACf,OAAO1H,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACjB;EAEA,SAASsI,WAAWA,CAAClI,CAAC,EAAEL,CAAC,EAAE;IACzB,IAAIzB,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE8B,CAAC,CAAC9B,CAAC,CAAC,GAAGyB,CAAC,CAAC,CAAC,GAACzB,CAAC,CAAC,IAAIyB,CAAC,CAAC,CAAC,GAACzB,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACxD8B,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM;EACjB;EAEA,SAASmI,CAACA,CAACnI,CAAC,EAAEsH,CAAC,EAAEvE,CAAC,EAAE;IAClB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE8B,CAAC,CAAC9B,CAAC,CAAC,GAAGoJ,CAAC,CAACpJ,CAAC,CAAC,GAAG6E,CAAC,CAAC7E,CAAC,CAAC;EACjD;EAEA,SAASkK,CAACA,CAACpI,CAAC,EAAEsH,CAAC,EAAEvE,CAAC,EAAE;IAClB,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE8B,CAAC,CAAC9B,CAAC,CAAC,GAAGoJ,CAAC,CAACpJ,CAAC,CAAC,GAAG6E,CAAC,CAAC7E,CAAC,CAAC;EACjD;EAEA,SAASmK,CAACA,CAACrI,CAAC,EAAEsH,CAAC,EAAEvE,CAAC,EAAE;IAClB,IAAIyE,CAAC;MAAErH,CAAC;MACL0D,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MACrEkE,EAAE,GAAG,CAAC;MAAGC,EAAE,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MACtEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MACtEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAAEC,GAAG,GAAG,CAAC;MAC7DC,EAAE,GAAG9G,CAAC,CAAC,CAAC,CAAC;MACT+G,EAAE,GAAG/G,CAAC,CAAC,CAAC,CAAC;MACTgH,EAAE,GAAGhH,CAAC,CAAC,CAAC,CAAC;MACTiH,EAAE,GAAGjH,CAAC,CAAC,CAAC,CAAC;MACTkH,EAAE,GAAGlH,CAAC,CAAC,CAAC,CAAC;MACTmH,EAAE,GAAGnH,CAAC,CAAC,CAAC,CAAC;MACToH,EAAE,GAAGpH,CAAC,CAAC,CAAC,CAAC;MACTqH,EAAE,GAAGrH,CAAC,CAAC,CAAC,CAAC;MACTsH,EAAE,GAAGtH,CAAC,CAAC,CAAC,CAAC;MACTuH,EAAE,GAAGvH,CAAC,CAAC,CAAC,CAAC;MACTwH,GAAG,GAAGxH,CAAC,CAAC,EAAE,CAAC;MACXyH,GAAG,GAAGzH,CAAC,CAAC,EAAE,CAAC;MACX0H,GAAG,GAAG1H,CAAC,CAAC,EAAE,CAAC;MACX2H,GAAG,GAAG3H,CAAC,CAAC,EAAE,CAAC;MACX4H,GAAG,GAAG5H,CAAC,CAAC,EAAE,CAAC;MACX6H,GAAG,GAAG7H,CAAC,CAAC,EAAE,CAAC;IAEbyE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRzD,EAAE,IAAI2D,CAAC,GAAGqC,EAAE;IACZ/F,EAAE,IAAI0D,CAAC,GAAGsC,EAAE;IACZ/F,EAAE,IAAIyD,CAAC,GAAGuC,EAAE;IACZ/F,EAAE,IAAIwD,CAAC,GAAGwC,EAAE;IACZ/F,EAAE,IAAIuD,CAAC,GAAGyC,EAAE;IACZ/F,EAAE,IAAIsD,CAAC,GAAG0C,EAAE;IACZ/F,EAAE,IAAIqD,CAAC,GAAG2C,EAAE;IACZ/F,EAAE,IAAIoD,CAAC,GAAG4C,EAAE;IACZ9B,EAAE,IAAId,CAAC,GAAG6C,EAAE;IACZ9B,EAAE,IAAIf,CAAC,GAAG8C,EAAE;IACZ9B,GAAG,IAAIhB,CAAC,GAAG+C,GAAG;IACd9B,GAAG,IAAIjB,CAAC,GAAGgD,GAAG;IACd9B,GAAG,IAAIlB,CAAC,GAAGiD,GAAG;IACd9B,GAAG,IAAInB,CAAC,GAAGkD,GAAG;IACd9B,GAAG,IAAIpB,CAAC,GAAGmD,GAAG;IACd9B,GAAG,IAAIrB,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRxD,EAAE,IAAI0D,CAAC,GAAGqC,EAAE;IACZ9F,EAAE,IAAIyD,CAAC,GAAGsC,EAAE;IACZ9F,EAAE,IAAIwD,CAAC,GAAGuC,EAAE;IACZ9F,EAAE,IAAIuD,CAAC,GAAGwC,EAAE;IACZ9F,EAAE,IAAIsD,CAAC,GAAGyC,EAAE;IACZ9F,EAAE,IAAIqD,CAAC,GAAG0C,EAAE;IACZ9F,EAAE,IAAIoD,CAAC,GAAG2C,EAAE;IACZ7B,EAAE,IAAId,CAAC,GAAG4C,EAAE;IACZ7B,EAAE,IAAIf,CAAC,GAAG6C,EAAE;IACZ7B,GAAG,IAAIhB,CAAC,GAAG8C,EAAE;IACb7B,GAAG,IAAIjB,CAAC,GAAG+C,GAAG;IACd7B,GAAG,IAAIlB,CAAC,GAAGgD,GAAG;IACd7B,GAAG,IAAInB,CAAC,GAAGiD,GAAG;IACd7B,GAAG,IAAIpB,CAAC,GAAGkD,GAAG;IACd7B,GAAG,IAAIrB,CAAC,GAAGmD,GAAG;IACd7B,GAAG,IAAItB,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRvD,EAAE,IAAIyD,CAAC,GAAGqC,EAAE;IACZ7F,EAAE,IAAIwD,CAAC,GAAGsC,EAAE;IACZ7F,EAAE,IAAIuD,CAAC,GAAGuC,EAAE;IACZ7F,EAAE,IAAIsD,CAAC,GAAGwC,EAAE;IACZ7F,EAAE,IAAIqD,CAAC,GAAGyC,EAAE;IACZ7F,EAAE,IAAIoD,CAAC,GAAG0C,EAAE;IACZ5B,EAAE,IAAId,CAAC,GAAG2C,EAAE;IACZ5B,EAAE,IAAIf,CAAC,GAAG4C,EAAE;IACZ5B,GAAG,IAAIhB,CAAC,GAAG6C,EAAE;IACb5B,GAAG,IAAIjB,CAAC,GAAG8C,EAAE;IACb5B,GAAG,IAAIlB,CAAC,GAAG+C,GAAG;IACd5B,GAAG,IAAInB,CAAC,GAAGgD,GAAG;IACd5B,GAAG,IAAIpB,CAAC,GAAGiD,GAAG;IACd5B,GAAG,IAAIrB,CAAC,GAAGkD,GAAG;IACd5B,GAAG,IAAItB,CAAC,GAAGmD,GAAG;IACd5B,GAAG,IAAIvB,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRtD,EAAE,IAAIwD,CAAC,GAAGqC,EAAE;IACZ5F,EAAE,IAAIuD,CAAC,GAAGsC,EAAE;IACZ5F,EAAE,IAAIsD,CAAC,GAAGuC,EAAE;IACZ5F,EAAE,IAAIqD,CAAC,GAAGwC,EAAE;IACZ5F,EAAE,IAAIoD,CAAC,GAAGyC,EAAE;IACZ3B,EAAE,IAAId,CAAC,GAAG0C,EAAE;IACZ3B,EAAE,IAAIf,CAAC,GAAG2C,EAAE;IACZ3B,GAAG,IAAIhB,CAAC,GAAG4C,EAAE;IACb3B,GAAG,IAAIjB,CAAC,GAAG6C,EAAE;IACb3B,GAAG,IAAIlB,CAAC,GAAG8C,EAAE;IACb3B,GAAG,IAAInB,CAAC,GAAG+C,GAAG;IACd3B,GAAG,IAAIpB,CAAC,GAAGgD,GAAG;IACd3B,GAAG,IAAIrB,CAAC,GAAGiD,GAAG;IACd3B,GAAG,IAAItB,CAAC,GAAGkD,GAAG;IACd3B,GAAG,IAAIvB,CAAC,GAAGmD,GAAG;IACd3B,GAAG,IAAIxB,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRrD,EAAE,IAAIuD,CAAC,GAAGqC,EAAE;IACZ3F,EAAE,IAAIsD,CAAC,GAAGsC,EAAE;IACZ3F,EAAE,IAAIqD,CAAC,GAAGuC,EAAE;IACZ3F,EAAE,IAAIoD,CAAC,GAAGwC,EAAE;IACZ1B,EAAE,IAAId,CAAC,GAAGyC,EAAE;IACZ1B,EAAE,IAAIf,CAAC,GAAG0C,EAAE;IACZ1B,GAAG,IAAIhB,CAAC,GAAG2C,EAAE;IACb1B,GAAG,IAAIjB,CAAC,GAAG4C,EAAE;IACb1B,GAAG,IAAIlB,CAAC,GAAG6C,EAAE;IACb1B,GAAG,IAAInB,CAAC,GAAG8C,EAAE;IACb1B,GAAG,IAAIpB,CAAC,GAAG+C,GAAG;IACd1B,GAAG,IAAIrB,CAAC,GAAGgD,GAAG;IACd1B,GAAG,IAAItB,CAAC,GAAGiD,GAAG;IACd1B,GAAG,IAAIvB,CAAC,GAAGkD,GAAG;IACd1B,GAAG,IAAIxB,CAAC,GAAGmD,GAAG;IACd1B,GAAG,IAAIzB,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRpD,EAAE,IAAIsD,CAAC,GAAGqC,EAAE;IACZ1F,EAAE,IAAIqD,CAAC,GAAGsC,EAAE;IACZ1F,EAAE,IAAIoD,CAAC,GAAGuC,EAAE;IACZzB,EAAE,IAAId,CAAC,GAAGwC,EAAE;IACZzB,EAAE,IAAIf,CAAC,GAAGyC,EAAE;IACZzB,GAAG,IAAIhB,CAAC,GAAG0C,EAAE;IACbzB,GAAG,IAAIjB,CAAC,GAAG2C,EAAE;IACbzB,GAAG,IAAIlB,CAAC,GAAG4C,EAAE;IACbzB,GAAG,IAAInB,CAAC,GAAG6C,EAAE;IACbzB,GAAG,IAAIpB,CAAC,GAAG8C,EAAE;IACbzB,GAAG,IAAIrB,CAAC,GAAG+C,GAAG;IACdzB,GAAG,IAAItB,CAAC,GAAGgD,GAAG;IACdzB,GAAG,IAAIvB,CAAC,GAAGiD,GAAG;IACdzB,GAAG,IAAIxB,CAAC,GAAGkD,GAAG;IACdzB,GAAG,IAAIzB,CAAC,GAAGmD,GAAG;IACdzB,GAAG,IAAI1B,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRnD,EAAE,IAAIqD,CAAC,GAAGqC,EAAE;IACZzF,EAAE,IAAIoD,CAAC,GAAGsC,EAAE;IACZxB,EAAE,IAAId,CAAC,GAAGuC,EAAE;IACZxB,EAAE,IAAIf,CAAC,GAAGwC,EAAE;IACZxB,GAAG,IAAIhB,CAAC,GAAGyC,EAAE;IACbxB,GAAG,IAAIjB,CAAC,GAAG0C,EAAE;IACbxB,GAAG,IAAIlB,CAAC,GAAG2C,EAAE;IACbxB,GAAG,IAAInB,CAAC,GAAG4C,EAAE;IACbxB,GAAG,IAAIpB,CAAC,GAAG6C,EAAE;IACbxB,GAAG,IAAIrB,CAAC,GAAG8C,EAAE;IACbxB,GAAG,IAAItB,CAAC,GAAG+C,GAAG;IACdxB,GAAG,IAAIvB,CAAC,GAAGgD,GAAG;IACdxB,GAAG,IAAIxB,CAAC,GAAGiD,GAAG;IACdxB,GAAG,IAAIzB,CAAC,GAAGkD,GAAG;IACdxB,GAAG,IAAI1B,CAAC,GAAGmD,GAAG;IACdxB,GAAG,IAAI3B,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRlD,EAAE,IAAIoD,CAAC,GAAGqC,EAAE;IACZvB,EAAE,IAAId,CAAC,GAAGsC,EAAE;IACZvB,EAAE,IAAIf,CAAC,GAAGuC,EAAE;IACZvB,GAAG,IAAIhB,CAAC,GAAGwC,EAAE;IACbvB,GAAG,IAAIjB,CAAC,GAAGyC,EAAE;IACbvB,GAAG,IAAIlB,CAAC,GAAG0C,EAAE;IACbvB,GAAG,IAAInB,CAAC,GAAG2C,EAAE;IACbvB,GAAG,IAAIpB,CAAC,GAAG4C,EAAE;IACbvB,GAAG,IAAIrB,CAAC,GAAG6C,EAAE;IACbvB,GAAG,IAAItB,CAAC,GAAG8C,EAAE;IACbvB,GAAG,IAAIvB,CAAC,GAAG+C,GAAG;IACdvB,GAAG,IAAIxB,CAAC,GAAGgD,GAAG;IACdvB,GAAG,IAAIzB,CAAC,GAAGiD,GAAG;IACdvB,GAAG,IAAI1B,CAAC,GAAGkD,GAAG;IACdvB,GAAG,IAAI3B,CAAC,GAAGmD,GAAG;IACdvB,GAAG,IAAI5B,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRgB,EAAE,IAAId,CAAC,GAAGqC,EAAE;IACZtB,EAAE,IAAIf,CAAC,GAAGsC,EAAE;IACZtB,GAAG,IAAIhB,CAAC,GAAGuC,EAAE;IACbtB,GAAG,IAAIjB,CAAC,GAAGwC,EAAE;IACbtB,GAAG,IAAIlB,CAAC,GAAGyC,EAAE;IACbtB,GAAG,IAAInB,CAAC,GAAG0C,EAAE;IACbtB,GAAG,IAAIpB,CAAC,GAAG2C,EAAE;IACbtB,GAAG,IAAIrB,CAAC,GAAG4C,EAAE;IACbtB,GAAG,IAAItB,CAAC,GAAG6C,EAAE;IACbtB,GAAG,IAAIvB,CAAC,GAAG8C,EAAE;IACbtB,GAAG,IAAIxB,CAAC,GAAG+C,GAAG;IACdtB,GAAG,IAAIzB,CAAC,GAAGgD,GAAG;IACdtB,GAAG,IAAI1B,CAAC,GAAGiD,GAAG;IACdtB,GAAG,IAAI3B,CAAC,GAAGkD,GAAG;IACdtB,GAAG,IAAI5B,CAAC,GAAGmD,GAAG;IACdtB,GAAG,IAAI7B,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACRiB,EAAE,IAAIf,CAAC,GAAGqC,EAAE;IACZrB,GAAG,IAAIhB,CAAC,GAAGsC,EAAE;IACbrB,GAAG,IAAIjB,CAAC,GAAGuC,EAAE;IACbrB,GAAG,IAAIlB,CAAC,GAAGwC,EAAE;IACbrB,GAAG,IAAInB,CAAC,GAAGyC,EAAE;IACbrB,GAAG,IAAIpB,CAAC,GAAG0C,EAAE;IACbrB,GAAG,IAAIrB,CAAC,GAAG2C,EAAE;IACbrB,GAAG,IAAItB,CAAC,GAAG4C,EAAE;IACbrB,GAAG,IAAIvB,CAAC,GAAG6C,EAAE;IACbrB,GAAG,IAAIxB,CAAC,GAAG8C,EAAE;IACbrB,GAAG,IAAIzB,CAAC,GAAG+C,GAAG;IACdrB,GAAG,IAAI1B,CAAC,GAAGgD,GAAG;IACdrB,GAAG,IAAI3B,CAAC,GAAGiD,GAAG;IACdrB,GAAG,IAAI5B,CAAC,GAAGkD,GAAG;IACdrB,GAAG,IAAI7B,CAAC,GAAGmD,GAAG;IACdrB,GAAG,IAAI9B,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,EAAE,CAAC;IACTkB,GAAG,IAAIhB,CAAC,GAAGqC,EAAE;IACbpB,GAAG,IAAIjB,CAAC,GAAGsC,EAAE;IACbpB,GAAG,IAAIlB,CAAC,GAAGuC,EAAE;IACbpB,GAAG,IAAInB,CAAC,GAAGwC,EAAE;IACbpB,GAAG,IAAIpB,CAAC,GAAGyC,EAAE;IACbpB,GAAG,IAAIrB,CAAC,GAAG0C,EAAE;IACbpB,GAAG,IAAItB,CAAC,GAAG2C,EAAE;IACbpB,GAAG,IAAIvB,CAAC,GAAG4C,EAAE;IACbpB,GAAG,IAAIxB,CAAC,GAAG6C,EAAE;IACbpB,GAAG,IAAIzB,CAAC,GAAG8C,EAAE;IACbpB,GAAG,IAAI1B,CAAC,GAAG+C,GAAG;IACdpB,GAAG,IAAI3B,CAAC,GAAGgD,GAAG;IACdpB,GAAG,IAAI5B,CAAC,GAAGiD,GAAG;IACdpB,GAAG,IAAI7B,CAAC,GAAGkD,GAAG;IACdpB,GAAG,IAAI9B,CAAC,GAAGmD,GAAG;IACdpB,GAAG,IAAI/B,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,EAAE,CAAC;IACTmB,GAAG,IAAIjB,CAAC,GAAGqC,EAAE;IACbnB,GAAG,IAAIlB,CAAC,GAAGsC,EAAE;IACbnB,GAAG,IAAInB,CAAC,GAAGuC,EAAE;IACbnB,GAAG,IAAIpB,CAAC,GAAGwC,EAAE;IACbnB,GAAG,IAAIrB,CAAC,GAAGyC,EAAE;IACbnB,GAAG,IAAItB,CAAC,GAAG0C,EAAE;IACbnB,GAAG,IAAIvB,CAAC,GAAG2C,EAAE;IACbnB,GAAG,IAAIxB,CAAC,GAAG4C,EAAE;IACbnB,GAAG,IAAIzB,CAAC,GAAG6C,EAAE;IACbnB,GAAG,IAAI1B,CAAC,GAAG8C,EAAE;IACbnB,GAAG,IAAI3B,CAAC,GAAG+C,GAAG;IACdnB,GAAG,IAAI5B,CAAC,GAAGgD,GAAG;IACdnB,GAAG,IAAI7B,CAAC,GAAGiD,GAAG;IACdnB,GAAG,IAAI9B,CAAC,GAAGkD,GAAG;IACdnB,GAAG,IAAI/B,CAAC,GAAGmD,GAAG;IACdnB,GAAG,IAAIhC,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,EAAE,CAAC;IACToB,GAAG,IAAIlB,CAAC,GAAGqC,EAAE;IACblB,GAAG,IAAInB,CAAC,GAAGsC,EAAE;IACblB,GAAG,IAAIpB,CAAC,GAAGuC,EAAE;IACblB,GAAG,IAAIrB,CAAC,GAAGwC,EAAE;IACblB,GAAG,IAAItB,CAAC,GAAGyC,EAAE;IACblB,GAAG,IAAIvB,CAAC,GAAG0C,EAAE;IACblB,GAAG,IAAIxB,CAAC,GAAG2C,EAAE;IACblB,GAAG,IAAIzB,CAAC,GAAG4C,EAAE;IACblB,GAAG,IAAI1B,CAAC,GAAG6C,EAAE;IACblB,GAAG,IAAI3B,CAAC,GAAG8C,EAAE;IACblB,GAAG,IAAI5B,CAAC,GAAG+C,GAAG;IACdlB,GAAG,IAAI7B,CAAC,GAAGgD,GAAG;IACdlB,GAAG,IAAI9B,CAAC,GAAGiD,GAAG;IACdlB,GAAG,IAAI/B,CAAC,GAAGkD,GAAG;IACdlB,GAAG,IAAIhC,CAAC,GAAGmD,GAAG;IACdlB,GAAG,IAAIjC,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,EAAE,CAAC;IACTqB,GAAG,IAAInB,CAAC,GAAGqC,EAAE;IACbjB,GAAG,IAAIpB,CAAC,GAAGsC,EAAE;IACbjB,GAAG,IAAIrB,CAAC,GAAGuC,EAAE;IACbjB,GAAG,IAAItB,CAAC,GAAGwC,EAAE;IACbjB,GAAG,IAAIvB,CAAC,GAAGyC,EAAE;IACbjB,GAAG,IAAIxB,CAAC,GAAG0C,EAAE;IACbjB,GAAG,IAAIzB,CAAC,GAAG2C,EAAE;IACbjB,GAAG,IAAI1B,CAAC,GAAG4C,EAAE;IACbjB,GAAG,IAAI3B,CAAC,GAAG6C,EAAE;IACbjB,GAAG,IAAI5B,CAAC,GAAG8C,EAAE;IACbjB,GAAG,IAAI7B,CAAC,GAAG+C,GAAG;IACdjB,GAAG,IAAI9B,CAAC,GAAGgD,GAAG;IACdjB,GAAG,IAAI/B,CAAC,GAAGiD,GAAG;IACdjB,GAAG,IAAIhC,CAAC,GAAGkD,GAAG;IACdjB,GAAG,IAAIjC,CAAC,GAAGmD,GAAG;IACdjB,GAAG,IAAIlC,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,EAAE,CAAC;IACTsB,GAAG,IAAIpB,CAAC,GAAGqC,EAAE;IACbhB,GAAG,IAAIrB,CAAC,GAAGsC,EAAE;IACbhB,GAAG,IAAItB,CAAC,GAAGuC,EAAE;IACbhB,GAAG,IAAIvB,CAAC,GAAGwC,EAAE;IACbhB,GAAG,IAAIxB,CAAC,GAAGyC,EAAE;IACbhB,GAAG,IAAIzB,CAAC,GAAG0C,EAAE;IACbhB,GAAG,IAAI1B,CAAC,GAAG2C,EAAE;IACbhB,GAAG,IAAI3B,CAAC,GAAG4C,EAAE;IACbhB,GAAG,IAAI5B,CAAC,GAAG6C,EAAE;IACbhB,GAAG,IAAI7B,CAAC,GAAG8C,EAAE;IACbhB,GAAG,IAAI9B,CAAC,GAAG+C,GAAG;IACdhB,GAAG,IAAI/B,CAAC,GAAGgD,GAAG;IACdhB,GAAG,IAAIhC,CAAC,GAAGiD,GAAG;IACdhB,GAAG,IAAIjC,CAAC,GAAGkD,GAAG;IACdhB,GAAG,IAAIlC,CAAC,GAAGmD,GAAG;IACdhB,GAAG,IAAInC,CAAC,GAAGoD,GAAG;IACdpD,CAAC,GAAGF,CAAC,CAAC,EAAE,CAAC;IACTuB,GAAG,IAAIrB,CAAC,GAAGqC,EAAE;IACbf,GAAG,IAAItB,CAAC,GAAGsC,EAAE;IACbf,GAAG,IAAIvB,CAAC,GAAGuC,EAAE;IACbf,GAAG,IAAIxB,CAAC,GAAGwC,EAAE;IACbf,GAAG,IAAIzB,CAAC,GAAGyC,EAAE;IACbf,GAAG,IAAI1B,CAAC,GAAG0C,EAAE;IACbf,GAAG,IAAI3B,CAAC,GAAG2C,EAAE;IACbf,GAAG,IAAI5B,CAAC,GAAG4C,EAAE;IACbf,GAAG,IAAI7B,CAAC,GAAG6C,EAAE;IACbf,GAAG,IAAI9B,CAAC,GAAG8C,EAAE;IACbf,GAAG,IAAI/B,CAAC,GAAG+C,GAAG;IACdf,GAAG,IAAIhC,CAAC,GAAGgD,GAAG;IACdf,GAAG,IAAIjC,CAAC,GAAGiD,GAAG;IACdf,GAAG,IAAIlC,CAAC,GAAGkD,GAAG;IACdf,GAAG,IAAInC,CAAC,GAAGmD,GAAG;IACdf,GAAG,IAAIpC,CAAC,GAAGoD,GAAG;IAEd/G,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACfhF,EAAE,IAAK,EAAE,GAAGiF,GAAG;IACff,EAAE,IAAK,EAAE,GAAGgB,GAAG;IACff,EAAE,IAAK,EAAE,GAAGgB,GAAG;IACff,GAAG,IAAI,EAAE,GAAGgB,GAAG;IACff,GAAG,IAAI,EAAE,GAAGgB,GAAG;IACff,GAAG,IAAI,EAAE,GAAGgB,GAAG;IACff,GAAG,IAAI,EAAE,GAAGgB,GAAG;IACff,GAAG,IAAI,EAAE,GAAGgB,GAAG;IACf;;IAEA;IACAzJ,CAAC,GAAG,CAAC;IACLqH,CAAC,GAAI3D,EAAE,GAAG1D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAG3D,EAAE,GAAG2D,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAI1D,EAAE,GAAG3D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAG1D,EAAE,GAAG0D,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIzD,EAAE,GAAG5D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGzD,EAAE,GAAGyD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIxD,EAAE,GAAG7D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGxD,EAAE,GAAGwD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIvD,EAAE,GAAG9D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGvD,EAAE,GAAGuD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAItD,EAAE,GAAG/D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGtD,EAAE,GAAGsD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIrD,EAAE,GAAGhE,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGrD,EAAE,GAAGqD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIpD,EAAE,GAAGjE,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGpD,EAAE,GAAGoD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIc,EAAE,GAAGnI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGc,EAAE,GAAGd,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIe,EAAE,GAAGpI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGe,EAAE,GAAGf,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGgB,GAAG,GAAGrI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEgB,GAAG,GAAGhB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGiB,GAAG,GAAGtI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEiB,GAAG,GAAGjB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGkB,GAAG,GAAGvI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEkB,GAAG,GAAGlB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGmB,GAAG,GAAGxI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEmB,GAAG,GAAGnB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGoB,GAAG,GAAGzI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEoB,GAAG,GAAGpB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGqB,GAAG,GAAG1I,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEqB,GAAG,GAAGrB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnE0D,EAAE,IAAI1D,CAAC,GAAC,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAC,CAAC,CAAC;;IAEtB;IACAA,CAAC,GAAG,CAAC;IACLqH,CAAC,GAAI3D,EAAE,GAAG1D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAG3D,EAAE,GAAG2D,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAI1D,EAAE,GAAG3D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAG1D,EAAE,GAAG0D,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIzD,EAAE,GAAG5D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGzD,EAAE,GAAGyD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIxD,EAAE,GAAG7D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGxD,EAAE,GAAGwD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIvD,EAAE,GAAG9D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGvD,EAAE,GAAGuD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAItD,EAAE,GAAG/D,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGtD,EAAE,GAAGsD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIrD,EAAE,GAAGhE,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGrD,EAAE,GAAGqD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIpD,EAAE,GAAGjE,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGpD,EAAE,GAAGoD,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIc,EAAE,GAAGnI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGc,EAAE,GAAGd,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAIe,EAAE,GAAGpI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAGe,EAAE,GAAGf,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGgB,GAAG,GAAGrI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEgB,GAAG,GAAGhB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGiB,GAAG,GAAGtI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEiB,GAAG,GAAGjB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGkB,GAAG,GAAGvI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEkB,GAAG,GAAGlB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGmB,GAAG,GAAGxI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEmB,GAAG,GAAGnB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGoB,GAAG,GAAGzI,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEoB,GAAG,GAAGpB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnEqH,CAAC,GAAGqB,GAAG,GAAG1I,CAAC,GAAG,KAAK;IAAEA,CAAC,GAAGsH,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,KAAK,CAAC;IAAEqB,GAAG,GAAGrB,CAAC,GAAGrH,CAAC,GAAG,KAAK;IACnE0D,EAAE,IAAI1D,CAAC,GAAC,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAC,CAAC,CAAC;IAEtBH,CAAC,CAAE,CAAC,CAAC,GAAG6D,EAAE;IACV7D,CAAC,CAAE,CAAC,CAAC,GAAG8D,EAAE;IACV9D,CAAC,CAAE,CAAC,CAAC,GAAG+D,EAAE;IACV/D,CAAC,CAAE,CAAC,CAAC,GAAGgE,EAAE;IACVhE,CAAC,CAAE,CAAC,CAAC,GAAGiE,EAAE;IACVjE,CAAC,CAAE,CAAC,CAAC,GAAGkE,EAAE;IACVlE,CAAC,CAAE,CAAC,CAAC,GAAGmE,EAAE;IACVnE,CAAC,CAAE,CAAC,CAAC,GAAGoE,EAAE;IACVpE,CAAC,CAAE,CAAC,CAAC,GAAGsI,EAAE;IACVtI,CAAC,CAAE,CAAC,CAAC,GAAGuI,EAAE;IACVvI,CAAC,CAAC,EAAE,CAAC,GAAGwI,GAAG;IACXxI,CAAC,CAAC,EAAE,CAAC,GAAGyI,GAAG;IACXzI,CAAC,CAAC,EAAE,CAAC,GAAG0I,GAAG;IACX1I,CAAC,CAAC,EAAE,CAAC,GAAG2I,GAAG;IACX3I,CAAC,CAAC,EAAE,CAAC,GAAG4I,GAAG;IACX5I,CAAC,CAAC,EAAE,CAAC,GAAG6I,GAAG;EACb;EAEA,SAASgC,CAACA,CAAC7K,CAAC,EAAEsH,CAAC,EAAE;IACfe,CAAC,CAACrI,CAAC,EAAEsH,CAAC,EAAEA,CAAC,CAAC;EACZ;EAEA,SAASwD,QAAQA,CAAC9K,CAAC,EAAE9B,CAAC,EAAE;IACtB,IAAIiC,CAAC,GAAGnC,EAAE,CAAC,CAAC;IACZ,IAAIsJ,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEnH,CAAC,CAACmH,CAAC,CAAC,GAAGpJ,CAAC,CAACoJ,CAAC,CAAC;IACpC,KAAKA,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzBuD,CAAC,CAAC1K,CAAC,EAAEA,CAAC,CAAC;MACP,IAAGmH,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAEe,CAAC,CAAClI,CAAC,EAAEA,CAAC,EAAEjC,CAAC,CAAC;IACnC;IACA,KAAKoJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEtH,CAAC,CAACsH,CAAC,CAAC,GAAGnH,CAAC,CAACmH,CAAC,CAAC;EACtC;EAEA,SAASyD,OAAOA,CAAC/K,CAAC,EAAE9B,CAAC,EAAE;IACrB,IAAIiC,CAAC,GAAGnC,EAAE,CAAC,CAAC;IACZ,IAAIsJ,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEnH,CAAC,CAACmH,CAAC,CAAC,GAAGpJ,CAAC,CAACoJ,CAAC,CAAC;IACpC,KAAKA,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvBuD,CAAC,CAAC1K,CAAC,EAAEA,CAAC,CAAC;MACP,IAAGmH,CAAC,KAAK,CAAC,EAAEe,CAAC,CAAClI,CAAC,EAAEA,CAAC,EAAEjC,CAAC,CAAC;IAC1B;IACA,KAAKoJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEtH,CAAC,CAACsH,CAAC,CAAC,GAAGnH,CAAC,CAACmH,CAAC,CAAC;EACtC;EAEA,SAAS0D,iBAAiBA,CAACpD,CAAC,EAAEjI,CAAC,EAAEM,CAAC,EAAE;IAClC,IAAI+C,CAAC,GAAG,IAAIvE,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAIW,CAAC,GAAG,IAAIhB,YAAY,CAAC,EAAE,CAAC;MAAED,CAAC;MAAED,CAAC;IAClC,IAAIoJ,CAAC,GAAGtJ,EAAE,CAAC,CAAC;MAAE+E,CAAC,GAAG/E,EAAE,CAAC,CAAC;MAAEmC,CAAC,GAAGnC,EAAE,CAAC,CAAC;MAC5B4B,CAAC,GAAG5B,EAAE,CAAC,CAAC;MAAEiN,CAAC,GAAGjN,EAAE,CAAC,CAAC;MAAE4I,CAAC,GAAG5I,EAAE,CAAC,CAAC;IAChC,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE8E,CAAC,CAAC9E,CAAC,CAAC,GAAGyB,CAAC,CAACzB,CAAC,CAAC;IACpC8E,CAAC,CAAC,EAAE,CAAC,GAAErD,CAAC,CAAC,EAAE,CAAC,GAAC,GAAG,GAAE,EAAE;IACpBqD,CAAC,CAAC,CAAC,CAAC,IAAE,GAAG;IACTkF,WAAW,CAAC9I,CAAC,EAACa,CAAC,CAAC;IAChB,KAAK/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvB6E,CAAC,CAAC7E,CAAC,CAAC,GAACkB,CAAC,CAAClB,CAAC,CAAC;MACT0B,CAAC,CAAC1B,CAAC,CAAC,GAACoJ,CAAC,CAACpJ,CAAC,CAAC,GAACiC,CAAC,CAACjC,CAAC,CAAC,GAAC,CAAC;IAClB;IACAoJ,CAAC,CAAC,CAAC,CAAC,GAAC1H,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC;IACX,KAAK1B,CAAC,GAAC,GAAG,EAAEA,CAAC,IAAE,CAAC,EAAE,EAAEA,CAAC,EAAE;MACrBC,CAAC,GAAE6E,CAAC,CAAC9E,CAAC,KAAG,CAAC,CAAC,MAAIA,CAAC,GAAC,CAAC,CAAC,GAAE,CAAC;MACtByJ,QAAQ,CAACL,CAAC,EAACvE,CAAC,EAAC5E,CAAC,CAAC;MACfwJ,QAAQ,CAACxH,CAAC,EAACP,CAAC,EAACzB,CAAC,CAAC;MACfgK,CAAC,CAAC8C,CAAC,EAAC3D,CAAC,EAACnH,CAAC,CAAC;MACRiI,CAAC,CAACd,CAAC,EAACA,CAAC,EAACnH,CAAC,CAAC;MACRgI,CAAC,CAAChI,CAAC,EAAC4C,CAAC,EAACnD,CAAC,CAAC;MACRwI,CAAC,CAACrF,CAAC,EAACA,CAAC,EAACnD,CAAC,CAAC;MACRiL,CAAC,CAACjL,CAAC,EAACqL,CAAC,CAAC;MACNJ,CAAC,CAACjE,CAAC,EAACU,CAAC,CAAC;MACNe,CAAC,CAACf,CAAC,EAACnH,CAAC,EAACmH,CAAC,CAAC;MACRe,CAAC,CAAClI,CAAC,EAAC4C,CAAC,EAACkI,CAAC,CAAC;MACR9C,CAAC,CAAC8C,CAAC,EAAC3D,CAAC,EAACnH,CAAC,CAAC;MACRiI,CAAC,CAACd,CAAC,EAACA,CAAC,EAACnH,CAAC,CAAC;MACR0K,CAAC,CAAC9H,CAAC,EAACuE,CAAC,CAAC;MACNc,CAAC,CAACjI,CAAC,EAACP,CAAC,EAACgH,CAAC,CAAC;MACRyB,CAAC,CAACf,CAAC,EAACnH,CAAC,EAACtB,OAAO,CAAC;MACdsJ,CAAC,CAACb,CAAC,EAACA,CAAC,EAAC1H,CAAC,CAAC;MACRyI,CAAC,CAAClI,CAAC,EAACA,CAAC,EAACmH,CAAC,CAAC;MACRe,CAAC,CAACf,CAAC,EAAC1H,CAAC,EAACgH,CAAC,CAAC;MACRyB,CAAC,CAACzI,CAAC,EAACmD,CAAC,EAAC3D,CAAC,CAAC;MACRyL,CAAC,CAAC9H,CAAC,EAACkI,CAAC,CAAC;MACNtD,QAAQ,CAACL,CAAC,EAACvE,CAAC,EAAC5E,CAAC,CAAC;MACfwJ,QAAQ,CAACxH,CAAC,EAACP,CAAC,EAACzB,CAAC,CAAC;IACjB;IACA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvBkB,CAAC,CAAClB,CAAC,GAAC,EAAE,CAAC,GAACoJ,CAAC,CAACpJ,CAAC,CAAC;MACZkB,CAAC,CAAClB,CAAC,GAAC,EAAE,CAAC,GAACiC,CAAC,CAACjC,CAAC,CAAC;MACZkB,CAAC,CAAClB,CAAC,GAAC,EAAE,CAAC,GAAC6E,CAAC,CAAC7E,CAAC,CAAC;MACZkB,CAAC,CAAClB,CAAC,GAAC,EAAE,CAAC,GAAC0B,CAAC,CAAC1B,CAAC,CAAC;IACd;IACA,IAAIgN,GAAG,GAAG9L,CAAC,CAAC+L,QAAQ,CAAC,EAAE,CAAC;IACxB,IAAIC,GAAG,GAAGhM,CAAC,CAAC+L,QAAQ,CAAC,EAAE,CAAC;IACxBL,QAAQ,CAACI,GAAG,EAACA,GAAG,CAAC;IACjB7C,CAAC,CAAC+C,GAAG,EAACA,GAAG,EAACF,GAAG,CAAC;IACdpD,SAAS,CAACF,CAAC,EAACwD,GAAG,CAAC;IAChB,OAAO,CAAC;EACV;EAEA,SAASC,sBAAsBA,CAACzD,CAAC,EAAEjI,CAAC,EAAE;IACpC,OAAOqL,iBAAiB,CAACpD,CAAC,EAAEjI,CAAC,EAAEjB,EAAE,CAAC;EACpC;EAEA,SAAS4M,kBAAkBA,CAAC7L,CAAC,EAAEL,CAAC,EAAE;IAChCd,WAAW,CAACc,CAAC,EAAE,EAAE,CAAC;IAClB,OAAOiM,sBAAsB,CAAC5L,CAAC,EAAEL,CAAC,CAAC;EACrC;EAEA,SAASmM,mBAAmBA,CAACrL,CAAC,EAAET,CAAC,EAAEL,CAAC,EAAE;IACpC,IAAI+D,CAAC,GAAG,IAAI1E,UAAU,CAAC,EAAE,CAAC;IAC1BuM,iBAAiB,CAAC7H,CAAC,EAAE/D,CAAC,EAAEK,CAAC,CAAC;IAC1B,OAAOgD,oBAAoB,CAACvC,CAAC,EAAE1B,EAAE,EAAE2E,CAAC,EAAET,KAAK,CAAC;EAC9C;EAEA,IAAI8I,kBAAkB,GAAGrE,gBAAgB;EACzC,IAAIsE,uBAAuB,GAAGrE,qBAAqB;EAEnD,SAASsE,UAAUA,CAACvL,CAAC,EAAE0C,CAAC,EAAEjD,CAAC,EAAED,CAAC,EAAEF,CAAC,EAAEL,CAAC,EAAE;IACpC,IAAIc,CAAC,GAAG,IAAIzB,UAAU,CAAC,EAAE,CAAC;IAC1B8M,mBAAmB,CAACrL,CAAC,EAAET,CAAC,EAAEL,CAAC,CAAC;IAC5B,OAAOoM,kBAAkB,CAACrL,CAAC,EAAE0C,CAAC,EAAEjD,CAAC,EAAED,CAAC,EAAEO,CAAC,CAAC;EAC1C;EAEA,SAASyL,eAAeA,CAAC9I,CAAC,EAAE1C,CAAC,EAAEP,CAAC,EAAED,CAAC,EAAEF,CAAC,EAAEL,CAAC,EAAE;IACzC,IAAIc,CAAC,GAAG,IAAIzB,UAAU,CAAC,EAAE,CAAC;IAC1B8M,mBAAmB,CAACrL,CAAC,EAAET,CAAC,EAAEL,CAAC,CAAC;IAC5B,OAAOqM,uBAAuB,CAAC5I,CAAC,EAAE1C,CAAC,EAAEP,CAAC,EAAED,CAAC,EAAEO,CAAC,CAAC;EAC/C;EAEA,IAAI0L,CAAC,GAAG,CACN,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/C;EAED,SAASC,oBAAoBA,CAACC,EAAE,EAAEC,EAAE,EAAElJ,CAAC,EAAElD,CAAC,EAAE;IAC1C,IAAIqM,EAAE,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;MAAEC,EAAE,GAAG,IAAID,UAAU,CAAC,EAAE,CAAC;MAChDE,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MACtCC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MAAEC,GAAG;MACtCC,EAAE;MAAEC,EAAE;MAAElP,CAAC;MAAE6J,CAAC;MAAE1I,CAAC;MAAEC,CAAC;MAAEgI,CAAC;MAAEvE,CAAC;MAAE5C,CAAC;MAAEP,CAAC;IAElC,IAAIyN,GAAG,GAAGvB,EAAE,CAAC,CAAC,CAAC;MACXwB,GAAG,GAAGxB,EAAE,CAAC,CAAC,CAAC;MACXyB,GAAG,GAAGzB,EAAE,CAAC,CAAC,CAAC;MACX0B,GAAG,GAAG1B,EAAE,CAAC,CAAC,CAAC;MACX2B,GAAG,GAAG3B,EAAE,CAAC,CAAC,CAAC;MACX4B,GAAG,GAAG5B,EAAE,CAAC,CAAC,CAAC;MACX6B,GAAG,GAAG7B,EAAE,CAAC,CAAC,CAAC;MACX8B,GAAG,GAAG9B,EAAE,CAAC,CAAC,CAAC;MAEX+B,GAAG,GAAG9B,EAAE,CAAC,CAAC,CAAC;MACX+B,GAAG,GAAG/B,EAAE,CAAC,CAAC,CAAC;MACXgC,GAAG,GAAGhC,EAAE,CAAC,CAAC,CAAC;MACXiC,GAAG,GAAGjC,EAAE,CAAC,CAAC,CAAC;MACXkC,GAAG,GAAGlC,EAAE,CAAC,CAAC,CAAC;MACXmC,GAAG,GAAGnC,EAAE,CAAC,CAAC,CAAC;MACXoC,GAAG,GAAGpC,EAAE,CAAC,CAAC,CAAC;MACXqC,GAAG,GAAGrC,EAAE,CAAC,CAAC,CAAC;IAEf,IAAIsC,GAAG,GAAG,CAAC;IACX,OAAO1O,CAAC,IAAI,GAAG,EAAE;MACf,KAAKzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACvB6J,CAAC,GAAG,CAAC,GAAG7J,CAAC,GAAGmQ,GAAG;QACfrC,EAAE,CAAC9N,CAAC,CAAC,GAAI2E,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC,IAAI,EAAE,GAAKlF,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC,IAAI,EAAG,GAAIlF,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC,IAAI,CAAE,GAAGlF,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC;QAChEmE,EAAE,CAAChO,CAAC,CAAC,GAAI2E,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC,IAAI,EAAE,GAAKlF,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC,IAAI,EAAG,GAAIlF,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC,IAAI,CAAE,GAAGlF,CAAC,CAACkF,CAAC,GAAC,CAAC,CAAC;MAClE;MACA,KAAK7J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACvBiO,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QAETjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;QACTjB,GAAG,GAAGkB,GAAG;;QAET;QACA/O,CAAC,GAAGuO,GAAG;QACPtO,CAAC,GAAG8O,GAAG;QAEP9G,CAAC,GAAGhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;QAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;QAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;;QAE5B;QACAA,CAAC,GAAG,CAAEoO,GAAG,KAAK,EAAE,GAAKQ,GAAG,IAAK,EAAE,GAAC,EAAI,KAAMR,GAAG,KAAK,EAAE,GAAKQ,GAAG,IAAK,EAAE,GAAC,EAAI,CAAC,IAAKA,GAAG,KAAM,EAAE,GAAC,EAAG,GAAKR,GAAG,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC;QACvHnO,CAAC,GAAG,CAAE2O,GAAG,KAAK,EAAE,GAAKR,GAAG,IAAK,EAAE,GAAC,EAAI,KAAMQ,GAAG,KAAK,EAAE,GAAKR,GAAG,IAAK,EAAE,GAAC,EAAI,CAAC,IAAKA,GAAG,KAAM,EAAE,GAAC,EAAG,GAAKQ,GAAG,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC;QAEvH3G,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;;QAE9B;QACAA,CAAC,GAAIoO,GAAG,GAAGC,GAAG,GAAK,CAACD,GAAG,GAAGE,GAAI;QAC9BrO,CAAC,GAAI2O,GAAG,GAAGC,GAAG,GAAK,CAACD,GAAG,GAAGE,GAAI;QAE9B7G,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;;QAE9B;QACAA,CAAC,GAAGuM,CAAC,CAAC1N,CAAC,GAAC,CAAC,CAAC;QACVoB,CAAC,GAAGsM,CAAC,CAAC1N,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;QAEZoJ,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;;QAE9B;QACAA,CAAC,GAAG2M,EAAE,CAAC9N,CAAC,GAAC,EAAE,CAAC;QACZoB,CAAC,GAAG4M,EAAE,CAAChO,CAAC,GAAC,EAAE,CAAC;QAEZoJ,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;QAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;QACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;QACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;QAEbgN,EAAE,GAAGhN,CAAC,GAAG,MAAM,GAAGP,CAAC,IAAI,EAAE;QACzBwN,EAAE,GAAG9F,CAAC,GAAG,MAAM,GAAGvE,CAAC,IAAI,EAAE;;QAEzB;QACA1D,CAAC,GAAG8N,EAAE;QACN7N,CAAC,GAAG8N,EAAE;QAEN9F,CAAC,GAAGhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;QAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;QAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;;QAE5B;QACAA,CAAC,GAAG,CAAEgO,GAAG,KAAK,EAAE,GAAKQ,GAAG,IAAK,EAAE,GAAC,EAAI,KAAMA,GAAG,KAAM,EAAE,GAAC,EAAG,GAAKR,GAAG,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC,IAAKQ,GAAG,KAAM,EAAE,GAAC,EAAG,GAAKR,GAAG,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC;QACjI/N,CAAC,GAAG,CAAEuO,GAAG,KAAK,EAAE,GAAKR,GAAG,IAAK,EAAE,GAAC,EAAI,KAAMA,GAAG,KAAM,EAAE,GAAC,EAAG,GAAKQ,GAAG,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC,IAAKR,GAAG,KAAM,EAAE,GAAC,EAAG,GAAKQ,GAAG,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC;QAEjIvG,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;;QAE9B;QACAA,CAAC,GAAIgO,GAAG,GAAGC,GAAG,GAAKD,GAAG,GAAGE,GAAI,GAAID,GAAG,GAAGC,GAAI;QAC3CjO,CAAC,GAAIuO,GAAG,GAAGC,GAAG,GAAKD,GAAG,GAAGE,GAAI,GAAID,GAAG,GAAGC,GAAI;QAE3CzG,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;QAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;QACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;QACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;QAEbuM,GAAG,GAAIvM,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;QAC9BsN,GAAG,GAAI5F,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;;QAE9B;QACA1D,CAAC,GAAGiN,GAAG;QACPhN,CAAC,GAAGwN,GAAG;QAEPxF,CAAC,GAAGhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;QAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;QAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;QAE5BA,CAAC,GAAG8N,EAAE;QACN7N,CAAC,GAAG8N,EAAE;QAEN9F,CAAC,IAAIhI,CAAC,GAAG,MAAM;QAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;QAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;QAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;QAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;QACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;QACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;QAEbmM,GAAG,GAAInM,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;QAC9BkN,GAAG,GAAIxF,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;QAE9BuK,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACTY,GAAG,GAAGX,GAAG;QAEToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACToB,GAAG,GAAGnB,GAAG;QACTY,GAAG,GAAGX,GAAG;QAET,IAAIhP,CAAC,GAAC,EAAE,KAAK,EAAE,EAAE;UACf,KAAK6J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;YACvB;YACA1I,CAAC,GAAG2M,EAAE,CAACjE,CAAC,CAAC;YACTzI,CAAC,GAAG4M,EAAE,CAACnE,CAAC,CAAC;YAETT,CAAC,GAAGhI,CAAC,GAAG,MAAM;YAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;YAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;YAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;YAE5BA,CAAC,GAAG2M,EAAE,CAAC,CAACjE,CAAC,GAAC,CAAC,IAAE,EAAE,CAAC;YAChBzI,CAAC,GAAG4M,EAAE,CAAC,CAACnE,CAAC,GAAC,CAAC,IAAE,EAAE,CAAC;YAEhBT,CAAC,IAAIhI,CAAC,GAAG,MAAM;YAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;YAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;YAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;;YAE9B;YACA8N,EAAE,GAAGnB,EAAE,CAAC,CAACjE,CAAC,GAAC,CAAC,IAAE,EAAE,CAAC;YACjBqF,EAAE,GAAGlB,EAAE,CAAC,CAACnE,CAAC,GAAC,CAAC,IAAE,EAAE,CAAC;YACjB1I,CAAC,GAAG,CAAE8N,EAAE,KAAK,CAAC,GAAKC,EAAE,IAAK,EAAE,GAAC,CAAG,KAAMD,EAAE,KAAK,CAAC,GAAKC,EAAE,IAAK,EAAE,GAAC,CAAG,CAAC,GAAID,EAAE,KAAK,CAAE;YAC9E7N,CAAC,GAAG,CAAE8N,EAAE,KAAK,CAAC,GAAKD,EAAE,IAAK,EAAE,GAAC,CAAG,KAAMC,EAAE,KAAK,CAAC,GAAKD,EAAE,IAAK,EAAE,GAAC,CAAG,CAAC,IAAKC,EAAE,KAAK,CAAC,GAAKD,EAAE,IAAK,EAAE,GAAC,CAAG,CAAC;YAEjG7F,CAAC,IAAIhI,CAAC,GAAG,MAAM;YAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;YAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;YAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;;YAE9B;YACA8N,EAAE,GAAGnB,EAAE,CAAC,CAACjE,CAAC,GAAC,EAAE,IAAE,EAAE,CAAC;YAClBqF,EAAE,GAAGlB,EAAE,CAAC,CAACnE,CAAC,GAAC,EAAE,IAAE,EAAE,CAAC;YAClB1I,CAAC,GAAG,CAAE8N,EAAE,KAAK,EAAE,GAAKC,EAAE,IAAK,EAAE,GAAC,EAAI,KAAMA,EAAE,KAAM,EAAE,GAAC,EAAG,GAAKD,EAAE,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC,GAAIA,EAAE,KAAK,CAAE;YAC5F7N,CAAC,GAAG,CAAE8N,EAAE,KAAK,EAAE,GAAKD,EAAE,IAAK,EAAE,GAAC,EAAI,KAAMA,EAAE,KAAM,EAAE,GAAC,EAAG,GAAKC,EAAE,IAAK,EAAE,IAAE,EAAE,GAAC,EAAE,CAAG,CAAC,IAAKA,EAAE,KAAK,CAAC,GAAKD,EAAE,IAAK,EAAE,GAAC,CAAG,CAAC;YAE/G7F,CAAC,IAAIhI,CAAC,GAAG,MAAM;YAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;YAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;YAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;YAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;YACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;YACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;YAEb6L,EAAE,CAACjE,CAAC,CAAC,GAAI5H,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;YAChCsM,EAAE,CAACnE,CAAC,CAAC,GAAIT,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;UAClC;QACF;MACF;;MAEA;MACA1D,CAAC,GAAGgO,GAAG;MACP/N,CAAC,GAAGuO,GAAG;MAEPvG,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAGuB,GAAG,GAAIlN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAG8B,GAAG,GAAIvG,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGiO,GAAG;MACPhO,CAAC,GAAGwO,GAAG;MAEPxG,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAGwB,GAAG,GAAInN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAG+B,GAAG,GAAIxG,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGkO,GAAG;MACPjO,CAAC,GAAGyO,GAAG;MAEPzG,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAGyB,GAAG,GAAIpN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAGgC,GAAG,GAAIzG,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGmO,GAAG;MACPlO,CAAC,GAAG0O,GAAG;MAEP1G,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAG0B,GAAG,GAAIrN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAGiC,GAAG,GAAI1G,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGoO,GAAG;MACPnO,CAAC,GAAG2O,GAAG;MAEP3G,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAG2B,GAAG,GAAItN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAGkC,GAAG,GAAI3G,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGqO,GAAG;MACPpO,CAAC,GAAG4O,GAAG;MAEP5G,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAG4B,GAAG,GAAIvN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAGmC,GAAG,GAAI5G,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGsO,GAAG;MACPrO,CAAC,GAAG6O,GAAG;MAEP7G,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAG6B,GAAG,GAAIxN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAGoC,GAAG,GAAI7G,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtC1D,CAAC,GAAGuO,GAAG;MACPtO,CAAC,GAAG8O,GAAG;MAEP9G,CAAC,GAAGhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,GAAGzD,CAAC,KAAK,EAAE;MAC5Ba,CAAC,GAAGd,CAAC,GAAG,MAAM;MAAEO,CAAC,GAAGP,CAAC,KAAK,EAAE;MAE5BA,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MACTxM,CAAC,GAAGyM,EAAE,CAAC,CAAC,CAAC;MAETzE,CAAC,IAAIhI,CAAC,GAAG,MAAM;MAAEyD,CAAC,IAAIzD,CAAC,KAAK,EAAE;MAC9Ba,CAAC,IAAId,CAAC,GAAG,MAAM;MAAEO,CAAC,IAAIP,CAAC,KAAK,EAAE;MAE9B0D,CAAC,IAAIuE,CAAC,KAAK,EAAE;MACbnH,CAAC,IAAI4C,CAAC,KAAK,EAAE;MACbnD,CAAC,IAAIO,CAAC,KAAK,EAAE;MAEb2L,EAAE,CAAC,CAAC,CAAC,GAAG8B,GAAG,GAAIzN,CAAC,GAAG,MAAM,GAAKP,CAAC,IAAI,EAAG;MACtCmM,EAAE,CAAC,CAAC,CAAC,GAAGqC,GAAG,GAAI9G,CAAC,GAAG,MAAM,GAAKvE,CAAC,IAAI,EAAG;MAEtCsL,GAAG,IAAI,GAAG;MACV1O,CAAC,IAAI,GAAG;IACV;IAEA,OAAOA,CAAC;EACV;EAEA,SAAS2O,WAAWA,CAAC/L,GAAG,EAAEM,CAAC,EAAElD,CAAC,EAAE;IAC9B,IAAImM,EAAE,GAAG,IAAIG,UAAU,CAAC,CAAC,CAAC;MACtBF,EAAE,GAAG,IAAIE,UAAU,CAAC,CAAC,CAAC;MACtB7M,CAAC,GAAG,IAAIX,UAAU,CAAC,GAAG,CAAC;MACvBP,CAAC;MAAE6E,CAAC,GAAGpD,CAAC;IAEZmM,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAElBC,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAClBA,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;IAElBF,oBAAoB,CAACC,EAAE,EAAEC,EAAE,EAAElJ,CAAC,EAAElD,CAAC,CAAC;IAClCA,CAAC,IAAI,GAAG;IAER,KAAKzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAEkB,CAAC,CAAClB,CAAC,CAAC,GAAG2E,CAAC,CAACE,CAAC,GAACpD,CAAC,GAACzB,CAAC,CAAC;IACvCkB,CAAC,CAACO,CAAC,CAAC,GAAG,GAAG;IAEVA,CAAC,GAAG,GAAG,GAAC,GAAG,IAAEA,CAAC,GAAC,GAAG,GAAC,CAAC,GAAC,CAAC,CAAC;IACvBP,CAAC,CAACO,CAAC,GAAC,CAAC,CAAC,GAAG,CAAC;IACVR,IAAI,CAACC,CAAC,EAAEO,CAAC,GAAC,CAAC,EAAIoD,CAAC,GAAG,UAAU,GAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,CAAC;IAC3C8I,oBAAoB,CAACC,EAAE,EAAEC,EAAE,EAAE3M,CAAC,EAAEO,CAAC,CAAC;IAElC,KAAKzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAEiB,IAAI,CAACoD,GAAG,EAAE,CAAC,GAACrE,CAAC,EAAE4N,EAAE,CAAC5N,CAAC,CAAC,EAAE6N,EAAE,CAAC7N,CAAC,CAAC,CAAC;IAEpD,OAAO,CAAC;EACV;EAEA,SAASqQ,GAAGA,CAACtO,CAAC,EAAE2H,CAAC,EAAE;IACjB,IAAIN,CAAC,GAAGtJ,EAAE,CAAC,CAAC;MAAE+E,CAAC,GAAG/E,EAAE,CAAC,CAAC;MAAEmC,CAAC,GAAGnC,EAAE,CAAC,CAAC;MAC5B4B,CAAC,GAAG5B,EAAE,CAAC,CAAC;MAAEiN,CAAC,GAAGjN,EAAE,CAAC,CAAC;MAAE4I,CAAC,GAAG5I,EAAE,CAAC,CAAC;MAC5B0I,CAAC,GAAG1I,EAAE,CAAC,CAAC;MAAEqB,CAAC,GAAGrB,EAAE,CAAC,CAAC;MAAE6J,CAAC,GAAG7J,EAAE,CAAC,CAAC;IAEhCoK,CAAC,CAACd,CAAC,EAAErH,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBmI,CAAC,CAACP,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBS,CAAC,CAACf,CAAC,EAAEA,CAAC,EAAEO,CAAC,CAAC;IACVM,CAAC,CAACpF,CAAC,EAAE9C,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBkI,CAAC,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBS,CAAC,CAACtF,CAAC,EAAEA,CAAC,EAAE8E,CAAC,CAAC;IACVQ,CAAC,CAAClI,CAAC,EAAEF,CAAC,CAAC,CAAC,CAAC,EAAE2H,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBS,CAAC,CAAClI,CAAC,EAAEA,CAAC,EAAEpB,EAAE,CAAC;IACXsJ,CAAC,CAACzI,CAAC,EAAEK,CAAC,CAAC,CAAC,CAAC,EAAE2H,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBO,CAAC,CAACvI,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;IACVwI,CAAC,CAAC6C,CAAC,EAAElI,CAAC,EAAEuE,CAAC,CAAC;IACVc,CAAC,CAACxB,CAAC,EAAEhH,CAAC,EAAEO,CAAC,CAAC;IACVgI,CAAC,CAACzB,CAAC,EAAE9G,CAAC,EAAEO,CAAC,CAAC;IACVgI,CAAC,CAAC9I,CAAC,EAAE0D,CAAC,EAAEuE,CAAC,CAAC;IAEVe,CAAC,CAACpI,CAAC,CAAC,CAAC,CAAC,EAAEgL,CAAC,EAAErE,CAAC,CAAC;IACbyB,CAAC,CAACpI,CAAC,CAAC,CAAC,CAAC,EAAEZ,CAAC,EAAEqH,CAAC,CAAC;IACb2B,CAAC,CAACpI,CAAC,CAAC,CAAC,CAAC,EAAEyG,CAAC,EAAEE,CAAC,CAAC;IACbyB,CAAC,CAACpI,CAAC,CAAC,CAAC,CAAC,EAAEgL,CAAC,EAAE5L,CAAC,CAAC;EACf;EAEA,SAASmP,KAAKA,CAACvO,CAAC,EAAE2H,CAAC,EAAE7E,CAAC,EAAE;IACtB,IAAI7E,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtByJ,QAAQ,CAAC1H,CAAC,CAAC/B,CAAC,CAAC,EAAE0J,CAAC,CAAC1J,CAAC,CAAC,EAAE6E,CAAC,CAAC;IACzB;EACF;EAEA,SAAS0L,IAAIA,CAACtQ,CAAC,EAAE8B,CAAC,EAAE;IAClB,IAAIyO,EAAE,GAAG1Q,EAAE,CAAC,CAAC;MAAE2Q,EAAE,GAAG3Q,EAAE,CAAC,CAAC;MAAE4Q,EAAE,GAAG5Q,EAAE,CAAC,CAAC;IACnC8M,QAAQ,CAAC8D,EAAE,EAAE3O,CAAC,CAAC,CAAC,CAAC,CAAC;IAClBoI,CAAC,CAACqG,EAAE,EAAEzO,CAAC,CAAC,CAAC,CAAC,EAAE2O,EAAE,CAAC;IACfvG,CAAC,CAACsG,EAAE,EAAE1O,CAAC,CAAC,CAAC,CAAC,EAAE2O,EAAE,CAAC;IACf9G,SAAS,CAAC3J,CAAC,EAAEwQ,EAAE,CAAC;IAChBxQ,CAAC,CAAC,EAAE,CAAC,IAAI8J,QAAQ,CAACyG,EAAE,CAAC,IAAI,CAAC;EAC5B;EAEA,SAASG,UAAUA,CAAC5O,CAAC,EAAE2H,CAAC,EAAEzE,CAAC,EAAE;IAC3B,IAAIJ,CAAC,EAAE7E,CAAC;IACRmJ,QAAQ,CAACpH,CAAC,CAAC,CAAC,CAAC,EAAEtB,GAAG,CAAC;IACnB0I,QAAQ,CAACpH,CAAC,CAAC,CAAC,CAAC,EAAErB,GAAG,CAAC;IACnByI,QAAQ,CAACpH,CAAC,CAAC,CAAC,CAAC,EAAErB,GAAG,CAAC;IACnByI,QAAQ,CAACpH,CAAC,CAAC,CAAC,CAAC,EAAEtB,GAAG,CAAC;IACnB,KAAKT,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzB6E,CAAC,GAAII,CAAC,CAAEjF,CAAC,GAAC,CAAC,GAAE,CAAC,CAAC,KAAKA,CAAC,GAAC,CAAC,CAAC,GAAI,CAAC;MAC7BsQ,KAAK,CAACvO,CAAC,EAAE2H,CAAC,EAAE7E,CAAC,CAAC;MACdwL,GAAG,CAAC3G,CAAC,EAAE3H,CAAC,CAAC;MACTsO,GAAG,CAACtO,CAAC,EAAEA,CAAC,CAAC;MACTuO,KAAK,CAACvO,CAAC,EAAE2H,CAAC,EAAE7E,CAAC,CAAC;IAChB;EACF;EAEA,SAAS+L,UAAUA,CAAC7O,CAAC,EAAEkD,CAAC,EAAE;IACxB,IAAIyE,CAAC,GAAG,CAAC5J,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC;IAChCqJ,QAAQ,CAACO,CAAC,CAAC,CAAC,CAAC,EAAE5I,CAAC,CAAC;IACjBqI,QAAQ,CAACO,CAAC,CAAC,CAAC,CAAC,EAAE3I,CAAC,CAAC;IACjBoI,QAAQ,CAACO,CAAC,CAAC,CAAC,CAAC,EAAEhJ,GAAG,CAAC;IACnByJ,CAAC,CAACT,CAAC,CAAC,CAAC,CAAC,EAAE5I,CAAC,EAAEC,CAAC,CAAC;IACb4P,UAAU,CAAC5O,CAAC,EAAE2H,CAAC,EAAEzE,CAAC,CAAC;EACrB;EAEA,SAAS4L,mBAAmBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAE;IAC3C,IAAItP,CAAC,GAAG,IAAInB,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAIwB,CAAC,GAAG,CAACjC,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC;IAChC,IAAIE,CAAC;IAEL,IAAI,CAACgR,MAAM,EAAE5Q,WAAW,CAAC2Q,EAAE,EAAE,EAAE,CAAC;IAChCX,WAAW,CAAC1O,CAAC,EAAEqP,EAAE,EAAE,EAAE,CAAC;IACtBrP,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG;IACXA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG;IACZA,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE;IAEXkP,UAAU,CAAC7O,CAAC,EAAEL,CAAC,CAAC;IAChB6O,IAAI,CAACO,EAAE,EAAE/O,CAAC,CAAC;IAEX,KAAK/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE+Q,EAAE,CAAC/Q,CAAC,GAAC,EAAE,CAAC,GAAG8Q,EAAE,CAAC9Q,CAAC,CAAC;IACzC,OAAO,CAAC;EACV;EAEA,IAAIiR,CAAC,GAAG,IAAI/Q,YAAY,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAE7K,SAASgR,IAAIA,CAACjR,CAAC,EAAEiB,CAAC,EAAE;IAClB,IAAIiQ,KAAK,EAAEnR,CAAC,EAAE6J,CAAC,EAAE7H,CAAC;IAClB,KAAKhC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE,EAAEA,CAAC,EAAE;MACzBmR,KAAK,GAAG,CAAC;MACT,KAAKtH,CAAC,GAAG7J,CAAC,GAAG,EAAE,EAAEgC,CAAC,GAAGhC,CAAC,GAAG,EAAE,EAAE6J,CAAC,GAAG7H,CAAC,EAAE,EAAE6H,CAAC,EAAE;QACvC3I,CAAC,CAAC2I,CAAC,CAAC,IAAIsH,KAAK,GAAG,EAAE,GAAGjQ,CAAC,CAAClB,CAAC,CAAC,GAAGiR,CAAC,CAACpH,CAAC,IAAI7J,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3CmR,KAAK,GAAG5H,IAAI,CAACC,KAAK,CAAC,CAACtI,CAAC,CAAC2I,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;QACtC3I,CAAC,CAAC2I,CAAC,CAAC,IAAIsH,KAAK,GAAG,GAAG;MACrB;MACAjQ,CAAC,CAAC2I,CAAC,CAAC,IAAIsH,KAAK;MACbjQ,CAAC,CAAClB,CAAC,CAAC,GAAG,CAAC;IACV;IACAmR,KAAK,GAAG,CAAC;IACT,KAAKtH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvB3I,CAAC,CAAC2I,CAAC,CAAC,IAAIsH,KAAK,GAAG,CAACjQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI+P,CAAC,CAACpH,CAAC,CAAC;MACnCsH,KAAK,GAAGjQ,CAAC,CAAC2I,CAAC,CAAC,IAAI,CAAC;MACjB3I,CAAC,CAAC2I,CAAC,CAAC,IAAI,GAAG;IACb;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE3I,CAAC,CAAC2I,CAAC,CAAC,IAAIsH,KAAK,GAAGF,CAAC,CAACpH,CAAC,CAAC;IAC7C,KAAK7J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvBkB,CAAC,CAAClB,CAAC,GAAC,CAAC,CAAC,IAAIkB,CAAC,CAAClB,CAAC,CAAC,IAAI,CAAC;MACnBC,CAAC,CAACD,CAAC,CAAC,GAAGkB,CAAC,CAAClB,CAAC,CAAC,GAAG,GAAG;IACnB;EACF;EAEA,SAASoR,MAAMA,CAACnR,CAAC,EAAE;IACjB,IAAIiB,CAAC,GAAG,IAAIhB,YAAY,CAAC,EAAE,CAAC;MAAEF,CAAC;IAC/B,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEkB,CAAC,CAAClB,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC;IACpC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAG,CAAC;IACjCkR,IAAI,CAACjR,CAAC,EAAEiB,CAAC,CAAC;EACZ;;EAEA;EACA,SAASmQ,WAAWA,CAACC,EAAE,EAAE3M,CAAC,EAAElD,CAAC,EAAEsP,EAAE,EAAE;IACjC,IAAIrP,CAAC,GAAG,IAAInB,UAAU,CAAC,EAAE,CAAC;MAAEY,CAAC,GAAG,IAAIZ,UAAU,CAAC,EAAE,CAAC;MAAEN,CAAC,GAAG,IAAIM,UAAU,CAAC,EAAE,CAAC;IAC1E,IAAIP,CAAC;MAAE6J,CAAC;MAAE3I,CAAC,GAAG,IAAIhB,YAAY,CAAC,EAAE,CAAC;IAClC,IAAI6B,CAAC,GAAG,CAACjC,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC;IAEhCsQ,WAAW,CAAC1O,CAAC,EAAEqP,EAAE,EAAE,EAAE,CAAC;IACtBrP,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG;IACXA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG;IACZA,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE;IAEX,IAAI6P,KAAK,GAAG9P,CAAC,GAAG,EAAE;IAClB,KAAKzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAEsR,EAAE,CAAC,EAAE,GAAGtR,CAAC,CAAC,GAAG2E,CAAC,CAAC3E,CAAC,CAAC;IACzC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEsR,EAAE,CAAC,EAAE,GAAGtR,CAAC,CAAC,GAAG0B,CAAC,CAAC,EAAE,GAAG1B,CAAC,CAAC;IAE/CoQ,WAAW,CAACnQ,CAAC,EAAEqR,EAAE,CAACrE,QAAQ,CAAC,EAAE,CAAC,EAAExL,CAAC,GAAC,EAAE,CAAC;IACrC2P,MAAM,CAACnR,CAAC,CAAC;IACT2Q,UAAU,CAAC7O,CAAC,EAAE9B,CAAC,CAAC;IAChBsQ,IAAI,CAACe,EAAE,EAAEvP,CAAC,CAAC;IAEX,KAAK/B,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEsR,EAAE,CAACtR,CAAC,CAAC,GAAG+Q,EAAE,CAAC/Q,CAAC,CAAC;IACvCoQ,WAAW,CAACjP,CAAC,EAAEmQ,EAAE,EAAE7P,CAAC,GAAG,EAAE,CAAC;IAC1B2P,MAAM,CAACjQ,CAAC,CAAC;IAET,KAAKnB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEkB,CAAC,CAAClB,CAAC,CAAC,GAAG,CAAC;IACjC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEkB,CAAC,CAAClB,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC;IACpC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACvB,KAAK6J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACvB3I,CAAC,CAAClB,CAAC,GAAC6J,CAAC,CAAC,IAAI1I,CAAC,CAACnB,CAAC,CAAC,GAAG0B,CAAC,CAACmI,CAAC,CAAC;MACvB;IACF;IAEAqH,IAAI,CAACI,EAAE,CAACrE,QAAQ,CAAC,EAAE,CAAC,EAAE/L,CAAC,CAAC;IACxB,OAAOqQ,KAAK;EACd;EAEA,SAASC,SAASA,CAACvR,CAAC,EAAE8B,CAAC,EAAE;IACvB,IAAI4H,CAAC,GAAG7J,EAAE,CAAC,CAAC;MAAE2R,GAAG,GAAG3R,EAAE,CAAC,CAAC;MAAE4R,GAAG,GAAG5R,EAAE,CAAC,CAAC;MAChC6R,GAAG,GAAG7R,EAAE,CAAC,CAAC;MAAE8R,IAAI,GAAG9R,EAAE,CAAC,CAAC;MAAE+R,IAAI,GAAG/R,EAAE,CAAC,CAAC;MACpCgS,IAAI,GAAGhS,EAAE,CAAC,CAAC;IAEfqJ,QAAQ,CAAClJ,CAAC,CAAC,CAAC,CAAC,EAAES,GAAG,CAAC;IACnBsJ,WAAW,CAAC/J,CAAC,CAAC,CAAC,CAAC,EAAE8B,CAAC,CAAC;IACpB4K,CAAC,CAAC+E,GAAG,EAAEzR,CAAC,CAAC,CAAC,CAAC,CAAC;IACZkK,CAAC,CAACwH,GAAG,EAAED,GAAG,EAAE9Q,CAAC,CAAC;IACdsJ,CAAC,CAACwH,GAAG,EAAEA,GAAG,EAAEzR,CAAC,CAAC,CAAC,CAAC,CAAC;IACjBgK,CAAC,CAAC0H,GAAG,EAAE1R,CAAC,CAAC,CAAC,CAAC,EAAE0R,GAAG,CAAC;IAEjBhF,CAAC,CAACiF,IAAI,EAAED,GAAG,CAAC;IACZhF,CAAC,CAACkF,IAAI,EAAED,IAAI,CAAC;IACbzH,CAAC,CAAC2H,IAAI,EAAED,IAAI,EAAED,IAAI,CAAC;IACnBzH,CAAC,CAACR,CAAC,EAAEmI,IAAI,EAAEJ,GAAG,CAAC;IACfvH,CAAC,CAACR,CAAC,EAAEA,CAAC,EAAEgI,GAAG,CAAC;IAEZ9E,OAAO,CAAClD,CAAC,EAAEA,CAAC,CAAC;IACbQ,CAAC,CAACR,CAAC,EAAEA,CAAC,EAAE+H,GAAG,CAAC;IACZvH,CAAC,CAACR,CAAC,EAAEA,CAAC,EAAEgI,GAAG,CAAC;IACZxH,CAAC,CAACR,CAAC,EAAEA,CAAC,EAAEgI,GAAG,CAAC;IACZxH,CAAC,CAAClK,CAAC,CAAC,CAAC,CAAC,EAAE0J,CAAC,EAAEgI,GAAG,CAAC;IAEfhF,CAAC,CAAC8E,GAAG,EAAExR,CAAC,CAAC,CAAC,CAAC,CAAC;IACZkK,CAAC,CAACsH,GAAG,EAAEA,GAAG,EAAEE,GAAG,CAAC;IAChB,IAAI7H,QAAQ,CAAC2H,GAAG,EAAEC,GAAG,CAAC,EAAEvH,CAAC,CAAClK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEe,CAAC,CAAC;IAExC2L,CAAC,CAAC8E,GAAG,EAAExR,CAAC,CAAC,CAAC,CAAC,CAAC;IACZkK,CAAC,CAACsH,GAAG,EAAEA,GAAG,EAAEE,GAAG,CAAC;IAChB,IAAI7H,QAAQ,CAAC2H,GAAG,EAAEC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;IAEjC,IAAI3H,QAAQ,CAAC9J,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM8B,CAAC,CAAC,EAAE,CAAC,IAAE,CAAE,EAAEmI,CAAC,CAACjK,CAAC,CAAC,CAAC,CAAC,EAAEQ,GAAG,EAAER,CAAC,CAAC,CAAC,CAAC,CAAC;IAErDkK,CAAC,CAAClK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,OAAO,CAAC;EACV;EAEA,SAAS8R,gBAAgBA,CAACpN,CAAC,EAAE2M,EAAE,EAAE7P,CAAC,EAAEqP,EAAE,EAAE;IACtC,IAAI9Q,CAAC;IACL,IAAI2J,CAAC,GAAG,IAAIpJ,UAAU,CAAC,EAAE,CAAC;MAAEY,CAAC,GAAG,IAAIZ,UAAU,CAAC,EAAE,CAAC;IAClD,IAAIwB,CAAC,GAAG,CAACjC,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC;MAC5B4J,CAAC,GAAG,CAAC5J,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC;IAEhC,IAAI2B,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;IAErB,IAAI+P,SAAS,CAAC9H,CAAC,EAAEoH,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IAE/B,KAAK9Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,CAAC,GAAGsR,EAAE,CAACtR,CAAC,CAAC;IACpC,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,GAAC,EAAE,CAAC,GAAG8Q,EAAE,CAAC9Q,CAAC,CAAC;IACxCoQ,WAAW,CAACjP,CAAC,EAAEwD,CAAC,EAAElD,CAAC,CAAC;IACpB2P,MAAM,CAACjQ,CAAC,CAAC;IACTwP,UAAU,CAAC5O,CAAC,EAAE2H,CAAC,EAAEvI,CAAC,CAAC;IAEnByP,UAAU,CAAClH,CAAC,EAAE4H,EAAE,CAACrE,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC9BoD,GAAG,CAACtO,CAAC,EAAE2H,CAAC,CAAC;IACT6G,IAAI,CAAC5G,CAAC,EAAE5H,CAAC,CAAC;IAEVN,CAAC,IAAI,EAAE;IACP,IAAIG,gBAAgB,CAAC0P,EAAE,EAAE,CAAC,EAAE3H,CAAC,EAAE,CAAC,CAAC,EAAE;MACjC,KAAK3J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,CAAC,GAAG,CAAC;MAChC,OAAO,CAAC,CAAC;IACX;IAEA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,CAAC,GAAGsR,EAAE,CAACtR,CAAC,GAAG,EAAE,CAAC;IACzC,OAAOyB,CAAC;EACV;EAEA,IAAIuQ,yBAAyB,GAAG,EAAE;IAC9BC,2BAA2B,GAAG,EAAE;IAChCC,0BAA0B,GAAG,EAAE;IAC/BC,6BAA6B,GAAG,EAAE;IAClCC,uBAAuB,GAAG,EAAE;IAC5BC,6BAA6B,GAAG,EAAE;IAClCC,yBAAyB,GAAG,EAAE;IAC9BC,yBAAyB,GAAG,EAAE;IAC9BC,wBAAwB,GAAG,EAAE;IAC7BC,qBAAqB,GAAGR,2BAA2B;IACnDS,oBAAoB,GAAGR,0BAA0B;IACjDS,uBAAuB,GAAGR,6BAA6B;IACvDS,iBAAiB,GAAG,EAAE;IACtBC,0BAA0B,GAAG,EAAE;IAC/BC,0BAA0B,GAAG,EAAE;IAC/BC,qBAAqB,GAAG,EAAE;IAC1BC,iBAAiB,GAAG,EAAE;EAE1BnT,IAAI,CAACoT,QAAQ,GAAG;IACd1O,oBAAoB,EAAEA,oBAAoB;IAC1CY,iBAAiB,EAAEA,iBAAiB;IACpCH,aAAa,EAAEA,aAAa;IAC5BP,yBAAyB,EAAEA,yBAAyB;IACpDM,qBAAqB,EAAEA,qBAAqB;IAC5C8D,kBAAkB,EAAEA,kBAAkB;IACtCE,yBAAyB,EAAEA,yBAAyB;IACpDpH,gBAAgB,EAAEA,gBAAgB;IAClCC,gBAAgB,EAAEA,gBAAgB;IAClCqH,gBAAgB,EAAEA,gBAAgB;IAClCC,qBAAqB,EAAEA,qBAAqB;IAC5C4D,iBAAiB,EAAEA,iBAAiB;IACpCK,sBAAsB,EAAEA,sBAAsB;IAC9CE,mBAAmB,EAAEA,mBAAmB;IACxCC,kBAAkB,EAAEA,kBAAkB;IACtCE,UAAU,EAAEA,UAAU;IACtBC,eAAe,EAAEA,eAAe;IAChCL,kBAAkB,EAAEA,kBAAkB;IACtCgD,WAAW,EAAEA,WAAW;IACxBiB,WAAW,EAAEA,WAAW;IACxBR,mBAAmB,EAAEA,mBAAmB;IACxCkB,gBAAgB,EAAEA,gBAAgB;IAElCC,yBAAyB,EAAEA,yBAAyB;IACpDC,2BAA2B,EAAEA,2BAA2B;IACxDC,0BAA0B,EAAEA,0BAA0B;IACtDC,6BAA6B,EAAEA,6BAA6B;IAC5DC,uBAAuB,EAAEA,uBAAuB;IAChDC,6BAA6B,EAAEA,6BAA6B;IAC5DC,yBAAyB,EAAEA,yBAAyB;IACpDC,yBAAyB,EAAEA,yBAAyB;IACpDC,wBAAwB,EAAEA,wBAAwB;IAClDC,qBAAqB,EAAEA,qBAAqB;IAC5CC,oBAAoB,EAAEA,oBAAoB;IAC1CC,uBAAuB,EAAEA,uBAAuB;IAChDC,iBAAiB,EAAEA,iBAAiB;IACpCC,0BAA0B,EAAEA,0BAA0B;IACtDC,0BAA0B,EAAEA,0BAA0B;IACtDC,qBAAqB,EAAEA,qBAAqB;IAC5CC,iBAAiB,EAAEA,iBAAiB;IAEpClT,EAAE,EAAEA,EAAE;IACNc,CAAC,EAAEA,CAAC;IACJqQ,CAAC,EAAEA,CAAC;IACJrH,SAAS,EAAEA,SAAS;IACpBI,WAAW,EAAEA,WAAW;IACxBG,CAAC,EAAEA,CAAC;IACJF,CAAC,EAAEA,CAAC;IACJ0C,CAAC,EAAEA,CAAC;IACJzC,CAAC,EAAEA,CAAC;IACJ2C,OAAO,EAAEA,OAAO;IAChBwD,GAAG,EAAEA,GAAG;IACRlH,QAAQ,EAAEA,QAAQ;IAClB+H,IAAI,EAAEA,IAAI;IACVP,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA;EACd,CAAC;;EAED;;EAEA,SAASsC,YAAYA,CAAClR,CAAC,EAAEP,CAAC,EAAE;IAC1B,IAAIO,CAAC,CAAC7B,MAAM,KAAK6R,yBAAyB,EAAE,MAAM,IAAI3R,KAAK,CAAC,cAAc,CAAC;IAC3E,IAAIoB,CAAC,CAACtB,MAAM,KAAK8R,2BAA2B,EAAE,MAAM,IAAI5R,KAAK,CAAC,gBAAgB,CAAC;EACjF;EAEA,SAAS8S,eAAeA,CAACrC,EAAE,EAAEC,EAAE,EAAE;IAC/B,IAAID,EAAE,CAAC3Q,MAAM,KAAKmS,yBAAyB,EAAE,MAAM,IAAIjS,KAAK,CAAC,qBAAqB,CAAC;IACnF,IAAI0Q,EAAE,CAAC5Q,MAAM,KAAKoS,yBAAyB,EAAE,MAAM,IAAIlS,KAAK,CAAC,qBAAqB,CAAC;EACrF;EAEA,SAAS+S,eAAeA,CAAA,EAAG;IACzB,KAAK,IAAIpT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqT,SAAS,CAAClT,MAAM,EAAEH,CAAC,EAAE,EAAE;MACzC,IAAI,EAAEqT,SAAS,CAACrT,CAAC,CAAC,YAAYO,UAAU,CAAC,EACvC,MAAM,IAAI+S,SAAS,CAAC,iCAAiC,CAAC;IAC1D;EACF;EAEA,SAASC,OAAOA,CAACC,GAAG,EAAE;IACpB,KAAK,IAAIxT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwT,GAAG,CAACrT,MAAM,EAAEH,CAAC,EAAE,EAAEwT,GAAG,CAACxT,CAAC,CAAC,GAAG,CAAC;EACjD;EAEAH,IAAI,CAAC4T,WAAW,GAAG,UAAShS,CAAC,EAAE;IAC7B,IAAIoD,CAAC,GAAG,IAAItE,UAAU,CAACkB,CAAC,CAAC;IACzBrB,WAAW,CAACyE,CAAC,EAAEpD,CAAC,CAAC;IACjB,OAAOoD,CAAC;EACV,CAAC;EAEDhF,IAAI,CAAC6T,SAAS,GAAG,UAASC,GAAG,EAAEC,KAAK,EAAEvO,GAAG,EAAE;IACzC+N,eAAe,CAACO,GAAG,EAAEC,KAAK,EAAEvO,GAAG,CAAC;IAChC6N,YAAY,CAAC7N,GAAG,EAAEuO,KAAK,CAAC;IACxB,IAAIjP,CAAC,GAAG,IAAIpE,UAAU,CAAC2R,0BAA0B,GAAGyB,GAAG,CAACxT,MAAM,CAAC;IAC/D,IAAI8B,CAAC,GAAG,IAAI1B,UAAU,CAACoE,CAAC,CAACxE,MAAM,CAAC;IAChC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2T,GAAG,CAACxT,MAAM,EAAEH,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,GAACkS,0BAA0B,CAAC,GAAGyB,GAAG,CAAC3T,CAAC,CAAC;IAC7EiJ,gBAAgB,CAAChH,CAAC,EAAE0C,CAAC,EAAEA,CAAC,CAACxE,MAAM,EAAEyT,KAAK,EAAEvO,GAAG,CAAC;IAC5C,OAAOpD,CAAC,CAACgL,QAAQ,CAACkF,6BAA6B,CAAC;EAClD,CAAC;EAEDtS,IAAI,CAAC6T,SAAS,CAACG,IAAI,GAAG,UAASC,GAAG,EAAEF,KAAK,EAAEvO,GAAG,EAAE;IAC9C+N,eAAe,CAACU,GAAG,EAAEF,KAAK,EAAEvO,GAAG,CAAC;IAChC6N,YAAY,CAAC7N,GAAG,EAAEuO,KAAK,CAAC;IACxB,IAAI3R,CAAC,GAAG,IAAI1B,UAAU,CAAC4R,6BAA6B,GAAG2B,GAAG,CAAC3T,MAAM,CAAC;IAClE,IAAIwE,CAAC,GAAG,IAAIpE,UAAU,CAAC0B,CAAC,CAAC9B,MAAM,CAAC;IAChC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8T,GAAG,CAAC3T,MAAM,EAAEH,CAAC,EAAE,EAAEiC,CAAC,CAACjC,CAAC,GAACmS,6BAA6B,CAAC,GAAG2B,GAAG,CAAC9T,CAAC,CAAC;IAChF,IAAIiC,CAAC,CAAC9B,MAAM,GAAG,EAAE,EAAE,OAAO,IAAI;IAC9B,IAAI+I,qBAAqB,CAACvE,CAAC,EAAE1C,CAAC,EAAEA,CAAC,CAAC9B,MAAM,EAAEyT,KAAK,EAAEvO,GAAG,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;IACxE,OAAOV,CAAC,CAACsI,QAAQ,CAACiF,0BAA0B,CAAC;EAC/C,CAAC;EAEDrS,IAAI,CAAC6T,SAAS,CAACK,SAAS,GAAG/B,yBAAyB;EACpDnS,IAAI,CAAC6T,SAAS,CAACM,WAAW,GAAG/B,2BAA2B;EACxDpS,IAAI,CAAC6T,SAAS,CAACO,cAAc,GAAG9B,6BAA6B;EAE7DtS,IAAI,CAACqU,UAAU,GAAG,UAASzS,CAAC,EAAEM,CAAC,EAAE;IAC/BqR,eAAe,CAAC3R,CAAC,EAAEM,CAAC,CAAC;IACrB,IAAIN,CAAC,CAACtB,MAAM,KAAKkS,6BAA6B,EAAE,MAAM,IAAIhS,KAAK,CAAC,YAAY,CAAC;IAC7E,IAAI0B,CAAC,CAAC5B,MAAM,KAAKiS,uBAAuB,EAAE,MAAM,IAAI/R,KAAK,CAAC,YAAY,CAAC;IACvE,IAAIqJ,CAAC,GAAG,IAAInJ,UAAU,CAAC6R,uBAAuB,CAAC;IAC/CtF,iBAAiB,CAACpD,CAAC,EAAEjI,CAAC,EAAEM,CAAC,CAAC;IAC1B,OAAO2H,CAAC;EACV,CAAC;EAED7J,IAAI,CAACqU,UAAU,CAACC,IAAI,GAAG,UAAS1S,CAAC,EAAE;IACjC2R,eAAe,CAAC3R,CAAC,CAAC;IAClB,IAAIA,CAAC,CAACtB,MAAM,KAAKkS,6BAA6B,EAAE,MAAM,IAAIhS,KAAK,CAAC,YAAY,CAAC;IAC7E,IAAIqJ,CAAC,GAAG,IAAInJ,UAAU,CAAC6R,uBAAuB,CAAC;IAC/CjF,sBAAsB,CAACzD,CAAC,EAAEjI,CAAC,CAAC;IAC5B,OAAOiI,CAAC;EACV,CAAC;EAED7J,IAAI,CAACqU,UAAU,CAACE,YAAY,GAAG/B,6BAA6B;EAC5DxS,IAAI,CAACqU,UAAU,CAACG,kBAAkB,GAAGjC,uBAAuB;EAE5DvS,IAAI,CAACiU,GAAG,GAAG,UAASH,GAAG,EAAEC,KAAK,EAAEU,SAAS,EAAEC,SAAS,EAAE;IACpD,IAAIvS,CAAC,GAAGnC,IAAI,CAACiU,GAAG,CAACU,MAAM,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC7C,OAAO1U,IAAI,CAAC6T,SAAS,CAACC,GAAG,EAAEC,KAAK,EAAE5R,CAAC,CAAC;EACtC,CAAC;EAEDnC,IAAI,CAACiU,GAAG,CAACU,MAAM,GAAG,UAASF,SAAS,EAAEC,SAAS,EAAE;IAC/CnB,eAAe,CAACkB,SAAS,EAAEC,SAAS,CAAC;IACrCpB,eAAe,CAACmB,SAAS,EAAEC,SAAS,CAAC;IACrC,IAAIvS,CAAC,GAAG,IAAIzB,UAAU,CAACiS,wBAAwB,CAAC;IAChDnF,mBAAmB,CAACrL,CAAC,EAAEsS,SAAS,EAAEC,SAAS,CAAC;IAC5C,OAAOvS,CAAC;EACV,CAAC;EAEDnC,IAAI,CAACiU,GAAG,CAACW,KAAK,GAAG5U,IAAI,CAAC6T,SAAS;EAE/B7T,IAAI,CAACiU,GAAG,CAACD,IAAI,GAAG,UAASF,GAAG,EAAEC,KAAK,EAAEU,SAAS,EAAEC,SAAS,EAAE;IACzD,IAAIvS,CAAC,GAAGnC,IAAI,CAACiU,GAAG,CAACU,MAAM,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC7C,OAAO1U,IAAI,CAAC6T,SAAS,CAACG,IAAI,CAACF,GAAG,EAAEC,KAAK,EAAE5R,CAAC,CAAC;EAC3C,CAAC;EAEDnC,IAAI,CAACiU,GAAG,CAACD,IAAI,CAACY,KAAK,GAAG5U,IAAI,CAAC6T,SAAS,CAACG,IAAI;EAEzChU,IAAI,CAACiU,GAAG,CAACY,OAAO,GAAG,YAAW;IAC5B,IAAI5D,EAAE,GAAG,IAAIvQ,UAAU,CAAC+R,yBAAyB,CAAC;IAClD,IAAIvB,EAAE,GAAG,IAAIxQ,UAAU,CAACgS,yBAAyB,CAAC;IAClDnF,kBAAkB,CAAC0D,EAAE,EAAEC,EAAE,CAAC;IAC1B,OAAO;MAACuD,SAAS,EAAExD,EAAE;MAAEyD,SAAS,EAAExD;IAAE,CAAC;EACvC,CAAC;EAEDlR,IAAI,CAACiU,GAAG,CAACY,OAAO,CAACC,aAAa,GAAG,UAASJ,SAAS,EAAE;IACnDnB,eAAe,CAACmB,SAAS,CAAC;IAC1B,IAAIA,SAAS,CAACpU,MAAM,KAAKoS,yBAAyB,EAChD,MAAM,IAAIlS,KAAK,CAAC,qBAAqB,CAAC;IACxC,IAAIyQ,EAAE,GAAG,IAAIvQ,UAAU,CAAC+R,yBAAyB,CAAC;IAClDnF,sBAAsB,CAAC2D,EAAE,EAAEyD,SAAS,CAAC;IACrC,OAAO;MAACD,SAAS,EAAExD,EAAE;MAAEyD,SAAS,EAAE,IAAIhU,UAAU,CAACgU,SAAS;IAAC,CAAC;EAC9D,CAAC;EAED1U,IAAI,CAACiU,GAAG,CAACc,eAAe,GAAGtC,yBAAyB;EACpDzS,IAAI,CAACiU,GAAG,CAACe,eAAe,GAAGtC,yBAAyB;EACpD1S,IAAI,CAACiU,GAAG,CAACgB,eAAe,GAAGtC,wBAAwB;EACnD3S,IAAI,CAACiU,GAAG,CAACE,WAAW,GAAGvB,qBAAqB;EAC5C5S,IAAI,CAACiU,GAAG,CAACG,cAAc,GAAGpU,IAAI,CAAC6T,SAAS,CAACO,cAAc;EAEvDpU,IAAI,CAACkV,IAAI,GAAG,UAASpB,GAAG,EAAEY,SAAS,EAAE;IACnCnB,eAAe,CAACO,GAAG,EAAEY,SAAS,CAAC;IAC/B,IAAIA,SAAS,CAACpU,MAAM,KAAK2S,0BAA0B,EACjD,MAAM,IAAIzS,KAAK,CAAC,qBAAqB,CAAC;IACxC,IAAI2U,SAAS,GAAG,IAAIzU,UAAU,CAACqS,iBAAiB,GAACe,GAAG,CAACxT,MAAM,CAAC;IAC5DkR,WAAW,CAAC2D,SAAS,EAAErB,GAAG,EAAEA,GAAG,CAACxT,MAAM,EAAEoU,SAAS,CAAC;IAClD,OAAOS,SAAS;EAClB,CAAC;EAEDnV,IAAI,CAACkV,IAAI,CAAClB,IAAI,GAAG,UAASmB,SAAS,EAAEV,SAAS,EAAE;IAC9ClB,eAAe,CAAC4B,SAAS,EAAEV,SAAS,CAAC;IACrC,IAAIA,SAAS,CAACnU,MAAM,KAAK0S,0BAA0B,EACjD,MAAM,IAAIxS,KAAK,CAAC,qBAAqB,CAAC;IACxC,IAAI4U,GAAG,GAAG,IAAI1U,UAAU,CAACyU,SAAS,CAAC7U,MAAM,CAAC;IAC1C,IAAI+U,IAAI,GAAGnD,gBAAgB,CAACkD,GAAG,EAAED,SAAS,EAAEA,SAAS,CAAC7U,MAAM,EAAEmU,SAAS,CAAC;IACxE,IAAIY,IAAI,GAAG,CAAC,EAAE,OAAO,IAAI;IACzB,IAAIvQ,CAAC,GAAG,IAAIpE,UAAU,CAAC2U,IAAI,CAAC;IAC5B,KAAK,IAAIlV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2E,CAAC,CAACxE,MAAM,EAAEH,CAAC,EAAE,EAAE2E,CAAC,CAAC3E,CAAC,CAAC,GAAGiV,GAAG,CAACjV,CAAC,CAAC;IAChD,OAAO2E,CAAC;EACV,CAAC;EAED9E,IAAI,CAACkV,IAAI,CAACI,QAAQ,GAAG,UAASxB,GAAG,EAAEY,SAAS,EAAE;IAC5C,IAAIS,SAAS,GAAGnV,IAAI,CAACkV,IAAI,CAACpB,GAAG,EAAEY,SAAS,CAAC;IACzC,IAAIa,GAAG,GAAG,IAAI7U,UAAU,CAACqS,iBAAiB,CAAC;IAC3C,KAAK,IAAI5S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoV,GAAG,CAACjV,MAAM,EAAEH,CAAC,EAAE,EAAEoV,GAAG,CAACpV,CAAC,CAAC,GAAGgV,SAAS,CAAChV,CAAC,CAAC;IAC1D,OAAOoV,GAAG;EACZ,CAAC;EAEDvV,IAAI,CAACkV,IAAI,CAACI,QAAQ,CAACE,MAAM,GAAG,UAAS1B,GAAG,EAAEyB,GAAG,EAAEd,SAAS,EAAE;IACxDlB,eAAe,CAACO,GAAG,EAAEyB,GAAG,EAAEd,SAAS,CAAC;IACpC,IAAIc,GAAG,CAACjV,MAAM,KAAKyS,iBAAiB,EAClC,MAAM,IAAIvS,KAAK,CAAC,oBAAoB,CAAC;IACvC,IAAIiU,SAAS,CAACnU,MAAM,KAAK0S,0BAA0B,EACjD,MAAM,IAAIxS,KAAK,CAAC,qBAAqB,CAAC;IACxC,IAAIiR,EAAE,GAAG,IAAI/Q,UAAU,CAACqS,iBAAiB,GAAGe,GAAG,CAACxT,MAAM,CAAC;IACvD,IAAIwE,CAAC,GAAG,IAAIpE,UAAU,CAACqS,iBAAiB,GAAGe,GAAG,CAACxT,MAAM,CAAC;IACtD,IAAIH,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4S,iBAAiB,EAAE5S,CAAC,EAAE,EAAEsR,EAAE,CAACtR,CAAC,CAAC,GAAGoV,GAAG,CAACpV,CAAC,CAAC;IACtD,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2T,GAAG,CAACxT,MAAM,EAAEH,CAAC,EAAE,EAAEsR,EAAE,CAACtR,CAAC,GAAC4S,iBAAiB,CAAC,GAAGe,GAAG,CAAC3T,CAAC,CAAC;IACjE,OAAQ+R,gBAAgB,CAACpN,CAAC,EAAE2M,EAAE,EAAEA,EAAE,CAACnR,MAAM,EAAEmU,SAAS,CAAC,IAAI,CAAC;EAC5D,CAAC;EAEDzU,IAAI,CAACkV,IAAI,CAACL,OAAO,GAAG,YAAW;IAC7B,IAAI5D,EAAE,GAAG,IAAIvQ,UAAU,CAACsS,0BAA0B,CAAC;IACnD,IAAI9B,EAAE,GAAG,IAAIxQ,UAAU,CAACuS,0BAA0B,CAAC;IACnDjC,mBAAmB,CAACC,EAAE,EAAEC,EAAE,CAAC;IAC3B,OAAO;MAACuD,SAAS,EAAExD,EAAE;MAAEyD,SAAS,EAAExD;IAAE,CAAC;EACvC,CAAC;EAEDlR,IAAI,CAACkV,IAAI,CAACL,OAAO,CAACC,aAAa,GAAG,UAASJ,SAAS,EAAE;IACpDnB,eAAe,CAACmB,SAAS,CAAC;IAC1B,IAAIA,SAAS,CAACpU,MAAM,KAAK2S,0BAA0B,EACjD,MAAM,IAAIzS,KAAK,CAAC,qBAAqB,CAAC;IACxC,IAAIyQ,EAAE,GAAG,IAAIvQ,UAAU,CAACsS,0BAA0B,CAAC;IACnD,KAAK,IAAI7S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Q,EAAE,CAAC3Q,MAAM,EAAEH,CAAC,EAAE,EAAE8Q,EAAE,CAAC9Q,CAAC,CAAC,GAAGuU,SAAS,CAAC,EAAE,GAACvU,CAAC,CAAC;IAC3D,OAAO;MAACsU,SAAS,EAAExD,EAAE;MAAEyD,SAAS,EAAE,IAAIhU,UAAU,CAACgU,SAAS;IAAC,CAAC;EAC9D,CAAC;EAED1U,IAAI,CAACkV,IAAI,CAACL,OAAO,CAACY,QAAQ,GAAG,UAASC,IAAI,EAAE;IAC1CnC,eAAe,CAACmC,IAAI,CAAC;IACrB,IAAIA,IAAI,CAACpV,MAAM,KAAK4S,qBAAqB,EACvC,MAAM,IAAI1S,KAAK,CAAC,eAAe,CAAC;IAClC,IAAIyQ,EAAE,GAAG,IAAIvQ,UAAU,CAACsS,0BAA0B,CAAC;IACnD,IAAI9B,EAAE,GAAG,IAAIxQ,UAAU,CAACuS,0BAA0B,CAAC;IACnD,KAAK,IAAI9S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE+Q,EAAE,CAAC/Q,CAAC,CAAC,GAAGuV,IAAI,CAACvV,CAAC,CAAC;IAC5C6Q,mBAAmB,CAACC,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAC;IACjC,OAAO;MAACuD,SAAS,EAAExD,EAAE;MAAEyD,SAAS,EAAExD;IAAE,CAAC;EACvC,CAAC;EAEDlR,IAAI,CAACkV,IAAI,CAACH,eAAe,GAAG/B,0BAA0B;EACtDhT,IAAI,CAACkV,IAAI,CAACF,eAAe,GAAG/B,0BAA0B;EACtDjT,IAAI,CAACkV,IAAI,CAACS,UAAU,GAAGzC,qBAAqB;EAC5ClT,IAAI,CAACkV,IAAI,CAACU,eAAe,GAAG7C,iBAAiB;EAE7C/S,IAAI,CAAC6V,IAAI,GAAG,UAAS/B,GAAG,EAAE;IACxBP,eAAe,CAACO,GAAG,CAAC;IACpB,IAAIxS,CAAC,GAAG,IAAIZ,UAAU,CAACyS,iBAAiB,CAAC;IACzC5C,WAAW,CAACjP,CAAC,EAAEwS,GAAG,EAAEA,GAAG,CAACxT,MAAM,CAAC;IAC/B,OAAOgB,CAAC;EACV,CAAC;EAEDtB,IAAI,CAAC6V,IAAI,CAACC,UAAU,GAAG3C,iBAAiB;EAExCnT,IAAI,CAACwV,MAAM,GAAG,UAASnU,CAAC,EAAEK,CAAC,EAAE;IAC3B6R,eAAe,CAAClS,CAAC,EAAEK,CAAC,CAAC;IACrB;IACA,IAAIL,CAAC,CAACf,MAAM,KAAK,CAAC,IAAIoB,CAAC,CAACpB,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAClD,IAAIe,CAAC,CAACf,MAAM,KAAKoB,CAAC,CAACpB,MAAM,EAAE,OAAO,KAAK;IACvC,OAAQkB,EAAE,CAACH,CAAC,EAAE,CAAC,EAAEK,CAAC,EAAE,CAAC,EAAEL,CAAC,CAACf,MAAM,CAAC,KAAK,CAAC,GAAI,IAAI,GAAG,KAAK;EACxD,CAAC;EAEDN,IAAI,CAAC+V,OAAO,GAAG,UAASC,EAAE,EAAE;IAC1BzV,WAAW,GAAGyV,EAAE;EAClB,CAAC;EAED,CAAC,YAAW;IACV;IACA;IACA,IAAIC,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAIA,IAAI,CAACD,MAAM,IAAIC,IAAI,CAACC,QAAQ,GAAI,IAAI;IAChF,IAAIF,MAAM,IAAIA,MAAM,CAACG,eAAe,EAAE;MACpC;MACA,IAAIC,KAAK,GAAG,KAAK;MACjBrW,IAAI,CAAC+V,OAAO,CAAC,UAAS1U,CAAC,EAAEO,CAAC,EAAE;QAC1B,IAAIzB,CAAC;UAAEsJ,CAAC,GAAG,IAAI/I,UAAU,CAACkB,CAAC,CAAC;QAC5B,KAAKzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,IAAIkW,KAAK,EAAE;UAC7BJ,MAAM,CAACG,eAAe,CAAC3M,CAAC,CAAC2D,QAAQ,CAACjN,CAAC,EAAEA,CAAC,GAAGuJ,IAAI,CAAC4M,GAAG,CAAC1U,CAAC,GAAGzB,CAAC,EAAEkW,KAAK,CAAC,CAAC,CAAC;QACnE;QACA,KAAKlW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAEkB,CAAC,CAAClB,CAAC,CAAC,GAAGsJ,CAAC,CAACtJ,CAAC,CAAC;QACnCuT,OAAO,CAACjK,CAAC,CAAC;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAO8M,OAAO,KAAK,WAAW,EAAE;MACzC;MACAN,MAAM,GAAGM,OAAO,CAAC,QAAQ,CAAC;MAC1B,IAAIN,MAAM,IAAIA,MAAM,CAACrC,WAAW,EAAE;QAChC5T,IAAI,CAAC+V,OAAO,CAAC,UAAS1U,CAAC,EAAEO,CAAC,EAAE;UAC1B,IAAIzB,CAAC;YAAEsJ,CAAC,GAAGwM,MAAM,CAACrC,WAAW,CAAChS,CAAC,CAAC;UAChC,KAAKzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,CAAC,EAAEzB,CAAC,EAAE,EAAEkB,CAAC,CAAClB,CAAC,CAAC,GAAGsJ,CAAC,CAACtJ,CAAC,CAAC;UACnCuT,OAAO,CAACjK,CAAC,CAAC;QACZ,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAAC;AAEJ,CAAC,EAAE,OAAO+M,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,GAAGD,MAAM,CAACC,OAAO,GAAIP,IAAI,CAAClW,IAAI,GAAGkW,IAAI,CAAClW,IAAI,IAAI,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}