{"ast": null, "code": "/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { <PERSON>, HashMD, Maj, SHA224_IV, SHA256_IV, SHA384_IV, SHA512_IV } from \"./_md.js\";\nimport * as u64 from \"./_u64.js\";\nimport { clean, createHasher, rotr } from \"./utils.js\";\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */Uint32Array.from([0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2]);\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */new Uint32Array(64);\nexport class SHA256 extends HashMD {\n  constructor(outputLen = 32) {\n    super(64, outputLen, 8, false);\n    // We cannot use array here since array allows indexing by variable\n    // which means optimizer/compiler cannot use registers.\n    this.A = SHA256_IV[0] | 0;\n    this.B = SHA256_IV[1] | 0;\n    this.C = SHA256_IV[2] | 0;\n    this.D = SHA256_IV[3] | 0;\n    this.E = SHA256_IV[4] | 0;\n    this.F = SHA256_IV[5] | 0;\n    this.G = SHA256_IV[6] | 0;\n    this.H = SHA256_IV[7] | 0;\n  }\n  get() {\n    const {\n      A,\n      B,\n      C,\n      D,\n      E,\n      F,\n      G,\n      H\n    } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  set(A, B, C, D, E, F, G, H) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  process(view, offset) {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ W15 >>> 3;\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ W2 >>> 10;\n      SHA256_W[i] = s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16] | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let {\n      A,\n      B,\n      C,\n      D,\n      E,\n      F,\n      G,\n      H\n    } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i] | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = sigma0 + Maj(A, B, C) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = D + T1 | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = T1 + T2 | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = A + this.A | 0;\n    B = B + this.B | 0;\n    C = C + this.C | 0;\n    D = D + this.D | 0;\n    E = E + this.E | 0;\n    F = F + this.F | 0;\n    G = G + this.G | 0;\n    H = H + this.H | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  roundClean() {\n    clean(SHA256_W);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\nexport class SHA224 extends SHA256 {\n  constructor() {\n    super(28);\n    this.A = SHA224_IV[0] | 0;\n    this.B = SHA224_IV[1] | 0;\n    this.C = SHA224_IV[2] | 0;\n    this.D = SHA224_IV[3] | 0;\n    this.E = SHA224_IV[4] | 0;\n    this.F = SHA224_IV[5] | 0;\n    this.G = SHA224_IV[6] | 0;\n    this.H = SHA224_IV[7] | 0;\n  }\n}\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */(() => u64.split(['0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc', '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118', '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2', '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694', '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65', '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5', '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4', '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70', '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df', '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b', '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30', '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8', '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8', '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3', '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec', '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b', '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178', '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b', '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c', '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */(() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */(() => K512[1])();\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */new Uint32Array(80);\nexport class SHA512 extends HashMD {\n  constructor(outputLen = 64) {\n    super(128, outputLen, 16, false);\n    // We cannot use array here since array allows indexing by variable\n    // which means optimizer/compiler cannot use registers.\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = SHA512_IV[0] | 0;\n    this.Al = SHA512_IV[1] | 0;\n    this.Bh = SHA512_IV[2] | 0;\n    this.Bl = SHA512_IV[3] | 0;\n    this.Ch = SHA512_IV[4] | 0;\n    this.Cl = SHA512_IV[5] | 0;\n    this.Dh = SHA512_IV[6] | 0;\n    this.Dl = SHA512_IV[7] | 0;\n    this.Eh = SHA512_IV[8] | 0;\n    this.El = SHA512_IV[9] | 0;\n    this.Fh = SHA512_IV[10] | 0;\n    this.Fl = SHA512_IV[11] | 0;\n    this.Gh = SHA512_IV[12] | 0;\n    this.Gl = SHA512_IV[13] | 0;\n    this.Hh = SHA512_IV[14] | 0;\n    this.Hl = SHA512_IV[15] | 0;\n  }\n  // prettier-ignore\n  get() {\n    const {\n      Ah,\n      Al,\n      Bh,\n      Bl,\n      Ch,\n      Cl,\n      Dh,\n      Dl,\n      Eh,\n      El,\n      Fh,\n      Fl,\n      Gh,\n      Gl,\n      Hh,\n      Hl\n    } = this;\n    return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n  }\n  // prettier-ignore\n  set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n    this.Ah = Ah | 0;\n    this.Al = Al | 0;\n    this.Bh = Bh | 0;\n    this.Bl = Bl | 0;\n    this.Ch = Ch | 0;\n    this.Cl = Cl | 0;\n    this.Dh = Dh | 0;\n    this.Dl = Dl | 0;\n    this.Eh = Eh | 0;\n    this.El = El | 0;\n    this.Fh = Fh | 0;\n    this.Fl = Fl | 0;\n    this.Gh = Gh | 0;\n    this.Gl = Gl | 0;\n    this.Hh = Hh | 0;\n    this.Hl = Hl | 0;\n  }\n  process(view, offset) {\n    // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) {\n      SHA512_W_H[i] = view.getUint32(offset);\n      SHA512_W_L[i] = view.getUint32(offset += 4);\n    }\n    for (let i = 16; i < 80; i++) {\n      // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n      const W15h = SHA512_W_H[i - 15] | 0;\n      const W15l = SHA512_W_L[i - 15] | 0;\n      const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n      const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n      // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n      const W2h = SHA512_W_H[i - 2] | 0;\n      const W2l = SHA512_W_L[i - 2] | 0;\n      const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n      const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n      // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n      const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n      const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n      SHA512_W_H[i] = SUMh | 0;\n      SHA512_W_L[i] = SUMl | 0;\n    }\n    let {\n      Ah,\n      Al,\n      Bh,\n      Bl,\n      Ch,\n      Cl,\n      Dh,\n      Dl,\n      Eh,\n      El,\n      Fh,\n      Fl,\n      Gh,\n      Gl,\n      Hh,\n      Hl\n    } = this;\n    // Compression function main loop, 80 rounds\n    for (let i = 0; i < 80; i++) {\n      // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n      const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n      const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n      //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const CHIh = Eh & Fh ^ ~Eh & Gh;\n      const CHIl = El & Fl ^ ~El & Gl;\n      // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n      // prettier-ignore\n      const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n      const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n      const T1l = T1ll | 0;\n      // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n      const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n      const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n      const MAJh = Ah & Bh ^ Ah & Ch ^ Bh & Ch;\n      const MAJl = Al & Bl ^ Al & Cl ^ Bl & Cl;\n      Hh = Gh | 0;\n      Hl = Gl | 0;\n      Gh = Fh | 0;\n      Gl = Fl | 0;\n      Fh = Eh | 0;\n      Fl = El | 0;\n      ({\n        h: Eh,\n        l: El\n      } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n      Dh = Ch | 0;\n      Dl = Cl | 0;\n      Ch = Bh | 0;\n      Cl = Bl | 0;\n      Bh = Ah | 0;\n      Bl = Al | 0;\n      const All = u64.add3L(T1l, sigma0l, MAJl);\n      Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n      Al = All | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    ({\n      h: Ah,\n      l: Al\n    } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n    ({\n      h: Bh,\n      l: Bl\n    } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n    ({\n      h: Ch,\n      l: Cl\n    } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n    ({\n      h: Dh,\n      l: Dl\n    } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n    ({\n      h: Eh,\n      l: El\n    } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n    ({\n      h: Fh,\n      l: Fl\n    } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n    ({\n      h: Gh,\n      l: Gl\n    } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n    ({\n      h: Hh,\n      l: Hl\n    } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n    this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n  }\n  roundClean() {\n    clean(SHA512_W_H, SHA512_W_L);\n  }\n  destroy() {\n    clean(this.buffer);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\nexport class SHA384 extends SHA512 {\n  constructor() {\n    super(48);\n    this.Ah = SHA384_IV[0] | 0;\n    this.Al = SHA384_IV[1] | 0;\n    this.Bh = SHA384_IV[2] | 0;\n    this.Bl = SHA384_IV[3] | 0;\n    this.Ch = SHA384_IV[4] | 0;\n    this.Cl = SHA384_IV[5] | 0;\n    this.Dh = SHA384_IV[6] | 0;\n    this.Dl = SHA384_IV[7] | 0;\n    this.Eh = SHA384_IV[8] | 0;\n    this.El = SHA384_IV[9] | 0;\n    this.Fh = SHA384_IV[10] | 0;\n    this.Fl = SHA384_IV[11] | 0;\n    this.Gh = SHA384_IV[12] | 0;\n    this.Gl = SHA384_IV[13] | 0;\n    this.Hh = SHA384_IV[14] | 0;\n    this.Hl = SHA384_IV[15] | 0;\n  }\n}\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */Uint32Array.from([0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf, 0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1]);\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */Uint32Array.from([0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd, 0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2]);\nexport class SHA512_224 extends SHA512 {\n  constructor() {\n    super(28);\n    this.Ah = T224_IV[0] | 0;\n    this.Al = T224_IV[1] | 0;\n    this.Bh = T224_IV[2] | 0;\n    this.Bl = T224_IV[3] | 0;\n    this.Ch = T224_IV[4] | 0;\n    this.Cl = T224_IV[5] | 0;\n    this.Dh = T224_IV[6] | 0;\n    this.Dl = T224_IV[7] | 0;\n    this.Eh = T224_IV[8] | 0;\n    this.El = T224_IV[9] | 0;\n    this.Fh = T224_IV[10] | 0;\n    this.Fl = T224_IV[11] | 0;\n    this.Gh = T224_IV[12] | 0;\n    this.Gl = T224_IV[13] | 0;\n    this.Hh = T224_IV[14] | 0;\n    this.Hl = T224_IV[15] | 0;\n  }\n}\nexport class SHA512_256 extends SHA512 {\n  constructor() {\n    super(32);\n    this.Ah = T256_IV[0] | 0;\n    this.Al = T256_IV[1] | 0;\n    this.Bh = T256_IV[2] | 0;\n    this.Bl = T256_IV[3] | 0;\n    this.Ch = T256_IV[4] | 0;\n    this.Cl = T256_IV[5] | 0;\n    this.Dh = T256_IV[6] | 0;\n    this.Dl = T256_IV[7] | 0;\n    this.Eh = T256_IV[8] | 0;\n    this.El = T256_IV[9] | 0;\n    this.Fh = T256_IV[10] | 0;\n    this.Fl = T256_IV[11] | 0;\n    this.Gh = T256_IV[12] | 0;\n    this.Gl = T256_IV[13] | 0;\n    this.Hh = T256_IV[14] | 0;\n    this.Hl = T256_IV[15] | 0;\n  }\n}\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nexport const sha256 = /* @__PURE__ */createHasher(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nexport const sha224 = /* @__PURE__ */createHasher(() => new SHA224());\n/** SHA2-512 hash function from RFC 4634. */\nexport const sha512 = /* @__PURE__ */createHasher(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nexport const sha384 = /* @__PURE__ */createHasher(() => new SHA384());\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexport const sha512_256 = /* @__PURE__ */createHasher(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexport const sha512_224 = /* @__PURE__ */createHasher(() => new SHA512_224());", "map": {"version": 3, "names": ["<PERSON>", "HashMD", "Maj", "SHA224_IV", "SHA256_IV", "SHA384_IV", "SHA512_IV", "u64", "clean", "createHasher", "rotr", "SHA256_K", "Uint32Array", "from", "SHA256_W", "SHA256", "constructor", "outputLen", "A", "B", "C", "D", "E", "F", "G", "H", "get", "set", "process", "view", "offset", "i", "getUint32", "W15", "W2", "s0", "s1", "sigma1", "T1", "sigma0", "T2", "roundClean", "destroy", "buffer", "SHA224", "K512", "split", "map", "n", "BigInt", "SHA512_Kh", "SHA512_Kl", "SHA512_W_H", "SHA512_W_L", "SHA512", "Ah", "Al", "Bh", "Bl", "Ch", "Cl", "Dh", "Dl", "Eh", "El", "Fh", "Fl", "Gh", "Gl", "Hh", "Hl", "W15h", "W15l", "s0h", "rotrSH", "shrSH", "s0l", "rotrSL", "shrSL", "W2h", "W2l", "s1h", "rotrBH", "s1l", "rotrBL", "SUMl", "add4L", "SUMh", "add4H", "sigma1h", "sigma1l", "CHIh", "CHIl", "T1ll", "add5L", "T1h", "add5H", "T1l", "sigma0h", "sigma0l", "MAJh", "MAJl", "h", "l", "add", "All", "add3L", "add3H", "SHA384", "T224_IV", "T256_IV", "SHA512_224", "SHA512_256", "sha256", "sha224", "sha512", "sha384", "sha512_256", "sha512_224"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@noble\\hashes\\src\\sha2.ts"], "sourcesContent": ["/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { <PERSON>, HashMD, Maj, SHA224_IV, SHA256_IV, SHA384_IV, SHA512_IV } from './_md.ts';\nimport * as u64 from './_u64.ts';\nimport { type CHash, clean, createHasher, rotr } from './utils.ts';\n\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nexport class SHA256 extends HashMD<SHA256> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  protected A: number = SHA256_IV[0] | 0;\n  protected B: number = SHA256_IV[1] | 0;\n  protected C: number = SHA256_IV[2] | 0;\n  protected D: number = SHA256_IV[3] | 0;\n  protected E: number = SHA256_IV[4] | 0;\n  protected F: number = SHA256_IV[5] | 0;\n  protected G: number = SHA256_IV[6] | 0;\n  protected H: number = SHA256_IV[7] | 0;\n\n  constructor(outputLen: number = 32) {\n    super(64, outputLen, 8, false);\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { A, B, C, D, E, F, G, H } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  protected set(\n    A: number, B: number, C: number, D: number, E: number, F: number, G: number, H: number\n  ): void {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n      SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D, E, F, G, H } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = (sigma0 + Maj(A, B, C)) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = (D + T1) | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = (T1 + T2) | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    F = (F + this.F) | 0;\n    G = (G + this.G) | 0;\n    H = (H + this.H) | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  protected roundClean(): void {\n    clean(SHA256_W);\n  }\n  destroy(): void {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\n\nexport class SHA224 extends SHA256 {\n  protected A: number = SHA224_IV[0] | 0;\n  protected B: number = SHA224_IV[1] | 0;\n  protected C: number = SHA224_IV[2] | 0;\n  protected D: number = SHA224_IV[3] | 0;\n  protected E: number = SHA224_IV[4] | 0;\n  protected F: number = SHA224_IV[5] | 0;\n  protected G: number = SHA224_IV[6] | 0;\n  protected H: number = SHA224_IV[7] | 0;\n  constructor() {\n    super(28);\n  }\n}\n\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => u64.split([\n  '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n  '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n  '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n  '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n  '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n  '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n  '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n  '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n  '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n  '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n  '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n  '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n  '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n  '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n  '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n  '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n  '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n  '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n  '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n  '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\n\nexport class SHA512 extends HashMD<SHA512> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  // h -- high 32 bits, l -- low 32 bits\n  protected Ah: number = SHA512_IV[0] | 0;\n  protected Al: number = SHA512_IV[1] | 0;\n  protected Bh: number = SHA512_IV[2] | 0;\n  protected Bl: number = SHA512_IV[3] | 0;\n  protected Ch: number = SHA512_IV[4] | 0;\n  protected Cl: number = SHA512_IV[5] | 0;\n  protected Dh: number = SHA512_IV[6] | 0;\n  protected Dl: number = SHA512_IV[7] | 0;\n  protected Eh: number = SHA512_IV[8] | 0;\n  protected El: number = SHA512_IV[9] | 0;\n  protected Fh: number = SHA512_IV[10] | 0;\n  protected Fl: number = SHA512_IV[11] | 0;\n  protected Gh: number = SHA512_IV[12] | 0;\n  protected Gl: number = SHA512_IV[13] | 0;\n  protected Hh: number = SHA512_IV[14] | 0;\n  protected Hl: number = SHA512_IV[15] | 0;\n\n  constructor(outputLen: number = 64) {\n    super(128, outputLen, 16, false);\n  }\n  // prettier-ignore\n  protected get(): [\n    number, number, number, number, number, number, number, number,\n    number, number, number, number, number, number, number, number\n  ] {\n    const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n    return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n  }\n  // prettier-ignore\n  protected set(\n    Ah: number, Al: number, Bh: number, Bl: number, Ch: number, Cl: number, Dh: number, Dl: number,\n    Eh: number, El: number, Fh: number, Fl: number, Gh: number, Gl: number, Hh: number, Hl: number\n  ): void {\n    this.Ah = Ah | 0;\n    this.Al = Al | 0;\n    this.Bh = Bh | 0;\n    this.Bl = Bl | 0;\n    this.Ch = Ch | 0;\n    this.Cl = Cl | 0;\n    this.Dh = Dh | 0;\n    this.Dl = Dl | 0;\n    this.Eh = Eh | 0;\n    this.El = El | 0;\n    this.Fh = Fh | 0;\n    this.Fl = Fl | 0;\n    this.Gh = Gh | 0;\n    this.Gl = Gl | 0;\n    this.Hh = Hh | 0;\n    this.Hl = Hl | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) {\n      SHA512_W_H[i] = view.getUint32(offset);\n      SHA512_W_L[i] = view.getUint32((offset += 4));\n    }\n    for (let i = 16; i < 80; i++) {\n      // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n      const W15h = SHA512_W_H[i - 15] | 0;\n      const W15l = SHA512_W_L[i - 15] | 0;\n      const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n      const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n      // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n      const W2h = SHA512_W_H[i - 2] | 0;\n      const W2l = SHA512_W_L[i - 2] | 0;\n      const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n      const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n      // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n      const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n      const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n      SHA512_W_H[i] = SUMh | 0;\n      SHA512_W_L[i] = SUMl | 0;\n    }\n    let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n    // Compression function main loop, 80 rounds\n    for (let i = 0; i < 80; i++) {\n      // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n      const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n      const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n      //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n      const CHIl = (El & Fl) ^ (~El & Gl);\n      // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n      // prettier-ignore\n      const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n      const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n      const T1l = T1ll | 0;\n      // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n      const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n      const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n      const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n      const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n      Hh = Gh | 0;\n      Hl = Gl | 0;\n      Gh = Fh | 0;\n      Gl = Fl | 0;\n      Fh = Eh | 0;\n      Fl = El | 0;\n      ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n      Dh = Ch | 0;\n      Dl = Cl | 0;\n      Ch = Bh | 0;\n      Cl = Bl | 0;\n      Bh = Ah | 0;\n      Bl = Al | 0;\n      const All = u64.add3L(T1l, sigma0l, MAJl);\n      Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n      Al = All | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n    ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n    ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n    ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n    ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n    ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n    ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n    ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n    this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n  }\n  protected roundClean(): void {\n    clean(SHA512_W_H, SHA512_W_L);\n  }\n  destroy(): void {\n    clean(this.buffer);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\n\nexport class SHA384 extends SHA512 {\n  protected Ah: number = SHA384_IV[0] | 0;\n  protected Al: number = SHA384_IV[1] | 0;\n  protected Bh: number = SHA384_IV[2] | 0;\n  protected Bl: number = SHA384_IV[3] | 0;\n  protected Ch: number = SHA384_IV[4] | 0;\n  protected Cl: number = SHA384_IV[5] | 0;\n  protected Dh: number = SHA384_IV[6] | 0;\n  protected Dl: number = SHA384_IV[7] | 0;\n  protected Eh: number = SHA384_IV[8] | 0;\n  protected El: number = SHA384_IV[9] | 0;\n  protected Fh: number = SHA384_IV[10] | 0;\n  protected Fl: number = SHA384_IV[11] | 0;\n  protected Gh: number = SHA384_IV[12] | 0;\n  protected Gl: number = SHA384_IV[13] | 0;\n  protected Hh: number = SHA384_IV[14] | 0;\n  protected Hl: number = SHA384_IV[15] | 0;\n\n  constructor() {\n    super(48);\n  }\n}\n\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n  0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n  0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n  0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n  0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\n\nexport class SHA512_224 extends SHA512 {\n  protected Ah: number = T224_IV[0] | 0;\n  protected Al: number = T224_IV[1] | 0;\n  protected Bh: number = T224_IV[2] | 0;\n  protected Bl: number = T224_IV[3] | 0;\n  protected Ch: number = T224_IV[4] | 0;\n  protected Cl: number = T224_IV[5] | 0;\n  protected Dh: number = T224_IV[6] | 0;\n  protected Dl: number = T224_IV[7] | 0;\n  protected Eh: number = T224_IV[8] | 0;\n  protected El: number = T224_IV[9] | 0;\n  protected Fh: number = T224_IV[10] | 0;\n  protected Fl: number = T224_IV[11] | 0;\n  protected Gh: number = T224_IV[12] | 0;\n  protected Gl: number = T224_IV[13] | 0;\n  protected Hh: number = T224_IV[14] | 0;\n  protected Hl: number = T224_IV[15] | 0;\n\n  constructor() {\n    super(28);\n  }\n}\n\nexport class SHA512_256 extends SHA512 {\n  protected Ah: number = T256_IV[0] | 0;\n  protected Al: number = T256_IV[1] | 0;\n  protected Bh: number = T256_IV[2] | 0;\n  protected Bl: number = T256_IV[3] | 0;\n  protected Ch: number = T256_IV[4] | 0;\n  protected Cl: number = T256_IV[5] | 0;\n  protected Dh: number = T256_IV[6] | 0;\n  protected Dl: number = T256_IV[7] | 0;\n  protected Eh: number = T256_IV[8] | 0;\n  protected El: number = T256_IV[9] | 0;\n  protected Fh: number = T256_IV[10] | 0;\n  protected Fl: number = T256_IV[11] | 0;\n  protected Gh: number = T256_IV[12] | 0;\n  protected Gl: number = T256_IV[13] | 0;\n  protected Hh: number = T256_IV[14] | 0;\n  protected Hl: number = T256_IV[15] | 0;\n\n  constructor() {\n    super(32);\n  }\n}\n\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nexport const sha256: CHash = /* @__PURE__ */ createHasher(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nexport const sha224: CHash = /* @__PURE__ */ createHasher(() => new SHA224());\n\n/** SHA2-512 hash function from RFC 4634. */\nexport const sha512: CHash = /* @__PURE__ */ createHasher(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nexport const sha384: CHash = /* @__PURE__ */ createHasher(() => new SHA384());\n\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexport const sha512_256: CHash = /* @__PURE__ */ createHasher(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexport const sha512_224: CHash = /* @__PURE__ */ createHasher(() => new SHA512_224());\n"], "mappings": "AAAA;;;;;;;AAOA,SAASA,GAAG,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,QAAQ,UAAU;AACvF,OAAO,KAAKC,GAAG,MAAM,WAAW;AAChC,SAAqBC,KAAK,EAAEC,YAAY,EAAEC,IAAI,QAAQ,YAAY;AAElE;;;;AAIA;AACA,MAAMC,QAAQ,GAAG,eAAgBC,WAAW,CAACC,IAAI,CAAC,CAChD,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/F,CAAC;AAEF;AACA,MAAMC,QAAQ,GAAG,eAAgB,IAAIF,WAAW,CAAC,EAAE,CAAC;AACpD,OAAM,MAAOG,MAAO,SAAQd,MAAc;EAYxCe,YAAYC,SAAA,GAAoB,EAAE;IAChC,KAAK,CAAC,EAAE,EAAEA,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC;IAZhC;IACA;IACU,KAAAC,CAAC,GAAWd,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAe,CAAC,GAAWf,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAgB,CAAC,GAAWhB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAiB,CAAC,GAAWjB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAkB,CAAC,GAAWlB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAmB,CAAC,GAAWnB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAoB,CAAC,GAAWpB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAqB,CAAC,GAAWrB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAItC;EACUsB,GAAGA,CAAA;IACX,MAAM;MAAER,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IACvC,OAAO,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACjC;EACA;EACUE,GAAGA,CACXT,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS;IAEtF,IAAI,CAACP,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;EAChB;EACUG,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAEhB,QAAQ,CAACiB,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,EAAE,KAAK,CAAC;IACrF,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAME,GAAG,GAAGnB,QAAQ,CAACiB,CAAC,GAAG,EAAE,CAAC;MAC5B,MAAMG,EAAE,GAAGpB,QAAQ,CAACiB,CAAC,GAAG,CAAC,CAAC;MAC1B,MAAMI,EAAE,GAAGzB,IAAI,CAACuB,GAAG,EAAE,CAAC,CAAC,GAAGvB,IAAI,CAACuB,GAAG,EAAE,EAAE,CAAC,GAAIA,GAAG,KAAK,CAAE;MACrD,MAAMG,EAAE,GAAG1B,IAAI,CAACwB,EAAE,EAAE,EAAE,CAAC,GAAGxB,IAAI,CAACwB,EAAE,EAAE,EAAE,CAAC,GAAIA,EAAE,KAAK,EAAG;MACpDpB,QAAQ,CAACiB,CAAC,CAAC,GAAIK,EAAE,GAAGtB,QAAQ,CAACiB,CAAC,GAAG,CAAC,CAAC,GAAGI,EAAE,GAAGrB,QAAQ,CAACiB,CAAC,GAAG,EAAE,CAAC,GAAI,CAAC;IAClE;IACA;IACA,IAAI;MAAEb,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IACrC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMM,MAAM,GAAG3B,IAAI,CAACY,CAAC,EAAE,CAAC,CAAC,GAAGZ,IAAI,CAACY,CAAC,EAAE,EAAE,CAAC,GAAGZ,IAAI,CAACY,CAAC,EAAE,EAAE,CAAC;MACrD,MAAMgB,EAAE,GAAIb,CAAC,GAAGY,MAAM,GAAGrC,GAAG,CAACsB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGb,QAAQ,CAACoB,CAAC,CAAC,GAAGjB,QAAQ,CAACiB,CAAC,CAAC,GAAI,CAAC;MACtE,MAAMQ,MAAM,GAAG7B,IAAI,CAACQ,CAAC,EAAE,CAAC,CAAC,GAAGR,IAAI,CAACQ,CAAC,EAAE,EAAE,CAAC,GAAGR,IAAI,CAACQ,CAAC,EAAE,EAAE,CAAC;MACrD,MAAMsB,EAAE,GAAID,MAAM,GAAGrC,GAAG,CAACgB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAI,CAAC;MACtCK,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAID,CAAC,GAAGiB,EAAE,GAAI,CAAC;MAChBjB,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAIoB,EAAE,GAAGE,EAAE,GAAI,CAAC;IACnB;IACA;IACAtB,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpB,IAAI,CAACE,GAAG,CAACT,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAClC;EACUgB,UAAUA,CAAA;IAClBjC,KAAK,CAACM,QAAQ,CAAC;EACjB;EACA4B,OAAOA,CAAA;IACL,IAAI,CAACf,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChCnB,KAAK,CAAC,IAAI,CAACmC,MAAM,CAAC;EACpB;;AAGF,OAAM,MAAOC,MAAO,SAAQ7B,MAAM;EAShCC,YAAA;IACE,KAAK,CAAC,EAAE,CAAC;IATD,KAAAE,CAAC,GAAWf,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAgB,CAAC,GAAWhB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAiB,CAAC,GAAWjB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAkB,CAAC,GAAWlB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAmB,CAAC,GAAWnB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAoB,CAAC,GAAWpB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAqB,CAAC,GAAWrB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5B,KAAAsB,CAAC,GAAWtB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAGtC;;AAGF;AAEA;AACA;AACA;AACA,MAAM0C,IAAI,GAAG,eAAgB,CAAC,MAAMtC,GAAG,CAACuC,KAAK,CAAC,CAC5C,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,CACvF,CAACC,GAAG,CAACC,CAAC,IAAIC,MAAM,CAACD,CAAC,CAAC,CAAC,CAAC,EAAC,CAAE;AACzB,MAAME,SAAS,GAAG,eAAgB,CAAC,MAAML,IAAI,CAAC,CAAC,CAAC,EAAC,CAAE;AACnD,MAAMM,SAAS,GAAG,eAAgB,CAAC,MAAMN,IAAI,CAAC,CAAC,CAAC,EAAC,CAAE;AAEnD;AACA,MAAMO,UAAU,GAAG,eAAgB,IAAIxC,WAAW,CAAC,EAAE,CAAC;AACtD,MAAMyC,UAAU,GAAG,eAAgB,IAAIzC,WAAW,CAAC,EAAE,CAAC;AAEtD,OAAM,MAAO0C,MAAO,SAAQrD,MAAc;EAqBxCe,YAAYC,SAAA,GAAoB,EAAE;IAChC,KAAK,CAAC,GAAG,EAAEA,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC;IArBlC;IACA;IACA;IACU,KAAAsC,EAAE,GAAWjD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAkD,EAAE,GAAWlD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAmD,EAAE,GAAWnD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAoD,EAAE,GAAWpD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAqD,EAAE,GAAWrD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAsD,EAAE,GAAWtD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAuD,EAAE,GAAWvD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAwD,EAAE,GAAWxD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAyD,EAAE,GAAWzD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAA0D,EAAE,GAAW1D,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAA2D,EAAE,GAAW3D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA4D,EAAE,GAAW5D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA6D,EAAE,GAAW7D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA8D,EAAE,GAAW9D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA+D,EAAE,GAAW/D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAAgE,EAAE,GAAWhE,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;EAIxC;EACA;EACUoB,GAAGA,CAAA;IAIX,MAAM;MAAE6B,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAE,GAAG,IAAI;IAC/E,OAAO,CAACf,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACzE;EACA;EACU3C,GAAGA,CACX4B,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAC9FC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;IAE9F,IAAI,CAACf,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;EAClB;EACU1C,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAE;MACxCsB,UAAU,CAACrB,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,CAAC;MACtCuB,UAAU,CAACtB,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAAEF,MAAM,IAAI,CAAE,CAAC;IAC/C;IACA,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B;MACA,MAAMwC,IAAI,GAAGnB,UAAU,CAACrB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;MACnC,MAAMyC,IAAI,GAAGnB,UAAU,CAACtB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;MACnC,MAAM0C,GAAG,GAAGlE,GAAG,CAACmE,MAAM,CAACH,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGjE,GAAG,CAACmE,MAAM,CAACH,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGjE,GAAG,CAACoE,KAAK,CAACJ,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC;MAC5F,MAAMI,GAAG,GAAGrE,GAAG,CAACsE,MAAM,CAACN,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGjE,GAAG,CAACsE,MAAM,CAACN,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGjE,GAAG,CAACuE,KAAK,CAACP,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC;MAC5F;MACA,MAAMO,GAAG,GAAG3B,UAAU,CAACrB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACjC,MAAMiD,GAAG,GAAG3B,UAAU,CAACtB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACjC,MAAMkD,GAAG,GAAG1E,GAAG,CAACmE,MAAM,CAACK,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAGzE,GAAG,CAAC2E,MAAM,CAACH,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAGzE,GAAG,CAACoE,KAAK,CAACI,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC;MACxF,MAAMG,GAAG,GAAG5E,GAAG,CAACsE,MAAM,CAACE,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAGzE,GAAG,CAAC6E,MAAM,CAACL,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAGzE,GAAG,CAACuE,KAAK,CAACC,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC;MACxF;MACA,MAAMK,IAAI,GAAG9E,GAAG,CAAC+E,KAAK,CAACV,GAAG,EAAEO,GAAG,EAAE9B,UAAU,CAACtB,CAAC,GAAG,CAAC,CAAC,EAAEsB,UAAU,CAACtB,CAAC,GAAG,EAAE,CAAC,CAAC;MACvE,MAAMwD,IAAI,GAAGhF,GAAG,CAACiF,KAAK,CAACH,IAAI,EAAEZ,GAAG,EAAEQ,GAAG,EAAE7B,UAAU,CAACrB,CAAC,GAAG,CAAC,CAAC,EAAEqB,UAAU,CAACrB,CAAC,GAAG,EAAE,CAAC,CAAC;MAC7EqB,UAAU,CAACrB,CAAC,CAAC,GAAGwD,IAAI,GAAG,CAAC;MACxBlC,UAAU,CAACtB,CAAC,CAAC,GAAGsD,IAAI,GAAG,CAAC;IAC1B;IACA,IAAI;MAAE9B,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAE,GAAG,IAAI;IAC7E;IACA,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B;MACA,MAAM0D,OAAO,GAAGlF,GAAG,CAACmE,MAAM,CAACX,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGzD,GAAG,CAACmE,MAAM,CAACX,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGzD,GAAG,CAAC2E,MAAM,CAACnB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF,MAAM0B,OAAO,GAAGnF,GAAG,CAACsE,MAAM,CAACd,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGzD,GAAG,CAACsE,MAAM,CAACd,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGzD,GAAG,CAAC6E,MAAM,CAACrB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF;MACA,MAAM2B,IAAI,GAAI5B,EAAE,GAAGE,EAAE,GAAK,CAACF,EAAE,GAAGI,EAAG;MACnC,MAAMyB,IAAI,GAAI5B,EAAE,GAAGE,EAAE,GAAK,CAACF,EAAE,GAAGI,EAAG;MACnC;MACA;MACA,MAAMyB,IAAI,GAAGtF,GAAG,CAACuF,KAAK,CAACxB,EAAE,EAAEoB,OAAO,EAAEE,IAAI,EAAEzC,SAAS,CAACpB,CAAC,CAAC,EAAEsB,UAAU,CAACtB,CAAC,CAAC,CAAC;MACtE,MAAMgE,GAAG,GAAGxF,GAAG,CAACyF,KAAK,CAACH,IAAI,EAAExB,EAAE,EAAEoB,OAAO,EAAEE,IAAI,EAAEzC,SAAS,CAACnB,CAAC,CAAC,EAAEqB,UAAU,CAACrB,CAAC,CAAC,CAAC;MAC3E,MAAMkE,GAAG,GAAGJ,IAAI,GAAG,CAAC;MACpB;MACA,MAAMK,OAAO,GAAG3F,GAAG,CAACmE,MAAM,CAACnB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGjD,GAAG,CAAC2E,MAAM,CAAC3B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGjD,GAAG,CAAC2E,MAAM,CAAC3B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF,MAAM2C,OAAO,GAAG5F,GAAG,CAACsE,MAAM,CAACtB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGjD,GAAG,CAAC6E,MAAM,CAAC7B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGjD,GAAG,CAAC6E,MAAM,CAAC7B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF,MAAM4C,IAAI,GAAI7C,EAAE,GAAGE,EAAE,GAAKF,EAAE,GAAGI,EAAG,GAAIF,EAAE,GAAGE,EAAG;MAC9C,MAAM0C,IAAI,GAAI7C,EAAE,GAAGE,EAAE,GAAKF,EAAE,GAAGI,EAAG,GAAIF,EAAE,GAAGE,EAAG;MAC9CS,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACX,CAAC;QAAEsC,CAAC,EAAEvC,EAAE;QAAEwC,CAAC,EAAEvC;MAAE,CAAE,GAAGzD,GAAG,CAACiG,GAAG,CAAC3C,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEiC,GAAG,GAAG,CAAC,EAAEE,GAAG,GAAG,CAAC,CAAC;MAC7DpC,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACX,MAAMiD,GAAG,GAAGlG,GAAG,CAACmG,KAAK,CAACT,GAAG,EAAEE,OAAO,EAAEE,IAAI,CAAC;MACzC9C,EAAE,GAAGhD,GAAG,CAACoG,KAAK,CAACF,GAAG,EAAEV,GAAG,EAAEG,OAAO,EAAEE,IAAI,CAAC;MACvC5C,EAAE,GAAGiD,GAAG,GAAG,CAAC;IACd;IACA;IACA,CAAC;MAAEH,CAAC,EAAE/C,EAAE;MAAEgD,CAAC,EAAE/C;IAAE,CAAE,GAAGjD,GAAG,CAACiG,GAAG,CAAC,IAAI,CAACjD,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAE8C,CAAC,EAAE7C,EAAE;MAAE8C,CAAC,EAAE7C;IAAE,CAAE,GAAGnD,GAAG,CAACiG,GAAG,CAAC,IAAI,CAAC/C,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAE4C,CAAC,EAAE3C,EAAE;MAAE4C,CAAC,EAAE3C;IAAE,CAAE,GAAGrD,GAAG,CAACiG,GAAG,CAAC,IAAI,CAAC7C,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAE0C,CAAC,EAAEzC,EAAE;MAAE0C,CAAC,EAAEzC;IAAE,CAAE,GAAGvD,GAAG,CAACiG,GAAG,CAAC,IAAI,CAAC3C,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEwC,CAAC,EAAEvC,EAAE;MAAEwC,CAAC,EAAEvC;IAAE,CAAE,GAAGzD,GAAG,CAACiG,GAAG,CAAC,IAAI,CAACzC,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEsC,CAAC,EAAErC,EAAE;MAAEsC,CAAC,EAAErC;IAAE,CAAE,GAAG3D,GAAG,CAACiG,GAAG,CAAC,IAAI,CAACvC,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEoC,CAAC,EAAEnC,EAAE;MAAEoC,CAAC,EAAEnC;IAAE,CAAE,GAAG7D,GAAG,CAACiG,GAAG,CAAC,IAAI,CAACrC,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEkC,CAAC,EAAEjC,EAAE;MAAEkC,CAAC,EAAEjC;IAAE,CAAE,GAAG/D,GAAG,CAACiG,GAAG,CAAC,IAAI,CAACnC,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,CAAC3C,GAAG,CAAC4B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC1E;EACU7B,UAAUA,CAAA;IAClBjC,KAAK,CAAC4C,UAAU,EAAEC,UAAU,CAAC;EAC/B;EACAX,OAAOA,CAAA;IACLlC,KAAK,CAAC,IAAI,CAACmC,MAAM,CAAC;IAClB,IAAI,CAAChB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1D;;AAGF,OAAM,MAAOiF,MAAO,SAAQtD,MAAM;EAkBhCtC,YAAA;IACE,KAAK,CAAC,EAAE,CAAC;IAlBD,KAAAuC,EAAE,GAAWlD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAmD,EAAE,GAAWnD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAoD,EAAE,GAAWpD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAqD,EAAE,GAAWrD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAsD,EAAE,GAAWtD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAuD,EAAE,GAAWvD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAwD,EAAE,GAAWxD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAAyD,EAAE,GAAWzD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAA0D,EAAE,GAAW1D,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAA2D,EAAE,GAAW3D,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7B,KAAA4D,EAAE,GAAW5D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA6D,EAAE,GAAW7D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA8D,EAAE,GAAW9D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAA+D,EAAE,GAAW/D,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAAgE,EAAE,GAAWhE,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IAC9B,KAAAiE,EAAE,GAAWjE,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;EAIxC;;AAGF;;;;;;AAOA;AACA,MAAMwG,OAAO,GAAG,eAAgBjG,WAAW,CAACC,IAAI,CAAC,CAC/C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/F,CAAC;AAEF;AACA,MAAMiG,OAAO,GAAG,eAAgBlG,WAAW,CAACC,IAAI,CAAC,CAC/C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/F,CAAC;AAEF,OAAM,MAAOkG,UAAW,SAAQzD,MAAM;EAkBpCtC,YAAA;IACE,KAAK,CAAC,EAAE,CAAC;IAlBD,KAAAuC,EAAE,GAAWsD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAArD,EAAE,GAAWqD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAApD,EAAE,GAAWoD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAnD,EAAE,GAAWmD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAlD,EAAE,GAAWkD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAjD,EAAE,GAAWiD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAhD,EAAE,GAAWgD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA/C,EAAE,GAAW+C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA9C,EAAE,GAAW8C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA7C,EAAE,GAAW6C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA5C,EAAE,GAAW4C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAA3C,EAAE,GAAW2C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAA1C,EAAE,GAAW0C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAAzC,EAAE,GAAWyC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAAxC,EAAE,GAAWwC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAAvC,EAAE,GAAWuC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;EAItC;;AAGF,OAAM,MAAOG,UAAW,SAAQ1D,MAAM;EAkBpCtC,YAAA;IACE,KAAK,CAAC,EAAE,CAAC;IAlBD,KAAAuC,EAAE,GAAWuD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAtD,EAAE,GAAWsD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAArD,EAAE,GAAWqD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAApD,EAAE,GAAWoD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAnD,EAAE,GAAWmD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAlD,EAAE,GAAWkD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAjD,EAAE,GAAWiD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAAhD,EAAE,GAAWgD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA/C,EAAE,GAAW+C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA9C,EAAE,GAAW8C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,KAAA7C,EAAE,GAAW6C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAA5C,EAAE,GAAW4C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAA3C,EAAE,GAAW2C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAA1C,EAAE,GAAW0C,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAAzC,EAAE,GAAWyC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;IAC5B,KAAAxC,EAAE,GAAWwC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC;EAItC;;AAGF;;;;;;;AAOA,OAAO,MAAMG,MAAM,GAAU,eAAgBxG,YAAY,CAAC,MAAM,IAAIM,MAAM,EAAE,CAAC;AAC7E;AACA,OAAO,MAAMmG,MAAM,GAAU,eAAgBzG,YAAY,CAAC,MAAM,IAAImC,MAAM,EAAE,CAAC;AAE7E;AACA,OAAO,MAAMuE,MAAM,GAAU,eAAgB1G,YAAY,CAAC,MAAM,IAAI6C,MAAM,EAAE,CAAC;AAC7E;AACA,OAAO,MAAM8D,MAAM,GAAU,eAAgB3G,YAAY,CAAC,MAAM,IAAImG,MAAM,EAAE,CAAC;AAE7E;;;;AAIA,OAAO,MAAMS,UAAU,GAAU,eAAgB5G,YAAY,CAAC,MAAM,IAAIuG,UAAU,EAAE,CAAC;AACrF;;;;AAIA,OAAO,MAAMM,UAAU,GAAU,eAAgB7G,YAAY,CAAC,MAAM,IAAIsG,UAAU,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}