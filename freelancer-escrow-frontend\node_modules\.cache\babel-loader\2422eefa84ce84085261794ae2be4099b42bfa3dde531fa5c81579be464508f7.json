{"ast": null, "code": "import { a as e } from \"./chunk-A63SMUOU.mjs\";\nimport { b as r } from \"./chunk-BCUSI3N6.mjs\";\nvar t = class extends e {\n    toString() {\n      let n = this.toUint8Array();\n      return r.fromHexInput(n).toString();\n    }\n  },\n  a = class extends t {};\nexport { t as a, a as b };", "map": {"version": 3, "names": ["t", "e", "toString", "n", "toUint8Array", "r", "fromHexInput", "a", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\publicKey.ts"], "sourcesContent": ["import { Serializable } from \"../../bcs\";\nimport { HexInput } from \"../../types\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Hex } from \"../hex\";\nimport { Signature } from \"./signature\";\n\n/**\n * Arguments for verifying a signature\n */\nexport interface VerifySignatureArgs {\n  message: HexInput;\n  signature: Signature;\n}\n\n/**\n * An abstract representation of a public key.\n *\n * Provides a common interface for verifying any signature.\n */\nexport abstract class PublicKey extends Serializable {\n  /**\n   * Verifies that the private key associated with this public key signed the message with the given signature.\n   * @param args.message The message that was signed\n   * @param args.signature The signature to verify\n   */\n  abstract verifySignature(args: VerifySignatureArgs): boolean;\n\n  /**\n   * Get the raw public key bytes\n   */\n  abstract toUint8Array(): Uint8Array;\n\n  /**\n   * Get the public key as a hex string with a 0x prefix e.g. 0x123456...\n   */\n  toString(): string {\n    const bytes = this.toUint8Array();\n    return Hex.fromHexInput(bytes).toString();\n  }\n}\n\n/**\n * An abstract representation of an account public key.\n *\n * Provides a common interface for deriving an authentication key.\n */\nexport abstract class AccountPublicKey extends PublicKey {\n  /**\n   * Get the authentication key associated with this public key\n   */\n  abstract authKey(): AuthenticationKey;\n}\n"], "mappings": ";;AAmBO,IAAeA,CAAA,GAAf,cAAiCC,CAAa;IAgBnDC,SAAA,EAAmB;MACjB,IAAMC,CAAA,GAAQ,KAAKC,YAAA,CAAa;MAChC,OAAOC,CAAA,CAAIC,YAAA,CAAaH,CAAK,EAAED,QAAA,CAAS,CAC1C;IAAA;EACF;EAOsBK,CAAA,GAAf,cAAwCP,CAAU,GAKzD;AAAA,SAAAA,CAAA,IAAAO,CAAA,EAAAA,CAAA,IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}