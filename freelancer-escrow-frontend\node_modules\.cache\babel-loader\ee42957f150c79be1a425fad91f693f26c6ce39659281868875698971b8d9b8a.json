{"ast": null, "code": "import { a as h, c as l } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b as E } from \"./chunk-76OH2Z4Q.mjs\";\nimport { a as p } from \"./chunk-MLDQ2TY2.mjs\";\nimport { a as g } from \"./chunk-77NXCSLY.mjs\";\nvar o = class o extends E {\n  constructor(r) {\n    super();\n    let {\n      publicKeys: e,\n      threshold: t\n    } = r;\n    if (e.length > o.MAX_KEYS || e.length < o.MIN_KEYS) throw new Error(`Must have between ${o.MIN_KEYS} and ${o.MAX_KEYS} public keys, inclusive`);\n    if (t < o.MIN_THRESHOLD || t > e.length) throw new Error(`Threshold must be between ${o.MIN_THRESHOLD} and ${e.length}, inclusive`);\n    this.publicKeys = e, this.threshold = t;\n  }\n  verifySignature(r) {\n    let {\n      message: e,\n      signature: t\n    } = r;\n    if (!(t instanceof y)) return !1;\n    let s = [];\n    for (let i = 0; i < 4; i += 1) for (let n = 0; n < 8; n += 1) if ((t.bitmap[i] & 1 << 7 - n) !== 0) {\n      let u = i * 8 + n;\n      s.push(u);\n    }\n    if (s.length !== t.signatures.length) throw new Error(\"Bitmap and signatures length mismatch\");\n    if (s.length < this.threshold) throw new Error(\"Not enough signatures\");\n    for (let i = 0; i < s.length; i += 1) if (!this.publicKeys[s[i]].verifySignature({\n      message: e,\n      signature: t.signatures[i]\n    })) return !1;\n    return !0;\n  }\n  authKey() {\n    return g.fromSchemeAndBytes({\n      scheme: 1,\n      input: this.toUint8Array()\n    });\n  }\n  toUint8Array() {\n    let r = new Uint8Array(this.publicKeys.length * h.LENGTH + 1);\n    return this.publicKeys.forEach((e, t) => {\n      r.set(e.toUint8Array(), t * h.LENGTH);\n    }), r[this.publicKeys.length * h.LENGTH] = this.threshold, r;\n  }\n  serialize(r) {\n    r.serializeBytes(this.toUint8Array());\n  }\n  static deserialize(r) {\n    let e = r.deserializeBytes(),\n      t = e[e.length - 1],\n      s = [];\n    for (let i = 0; i < e.length - 1; i += h.LENGTH) {\n      let n = i;\n      s.push(new h(e.subarray(n, n + h.LENGTH)));\n    }\n    return new o({\n      publicKeys: s,\n      threshold: t\n    });\n  }\n};\no.MAX_KEYS = 32, o.MIN_KEYS = 2, o.MIN_THRESHOLD = 1;\nvar d = o,\n  a = class a extends p {\n    constructor(r) {\n      super();\n      let {\n        signatures: e,\n        bitmap: t\n      } = r;\n      if (e.length > a.MAX_SIGNATURES_SUPPORTED) throw new Error(`The number of signatures cannot be greater than ${a.MAX_SIGNATURES_SUPPORTED}`);\n      if (this.signatures = e, !(t instanceof Uint8Array)) this.bitmap = a.createBitmap({\n        bits: t\n      });else {\n        if (t.length !== a.BITMAP_LEN) throw new Error(`\"bitmap\" length should be ${a.BITMAP_LEN}`);\n        this.bitmap = t;\n      }\n    }\n    toUint8Array() {\n      let r = new Uint8Array(this.signatures.length * l.LENGTH + a.BITMAP_LEN);\n      return this.signatures.forEach((e, t) => {\n        r.set(e.toUint8Array(), t * l.LENGTH);\n      }), r.set(this.bitmap, this.signatures.length * l.LENGTH), r;\n    }\n    serialize(r) {\n      r.serializeBytes(this.toUint8Array());\n    }\n    static deserialize(r) {\n      let e = r.deserializeBytes(),\n        t = e.subarray(e.length - 4),\n        s = [];\n      for (let i = 0; i < e.length - t.length; i += l.LENGTH) {\n        let n = i;\n        s.push(new l(e.subarray(n, n + l.LENGTH)));\n      }\n      return new a({\n        signatures: s,\n        bitmap: t\n      });\n    }\n    static createBitmap(r) {\n      let {\n          bits: e\n        } = r,\n        t = 128,\n        s = new Uint8Array([0, 0, 0, 0]),\n        i = new Set();\n      return e.forEach((n, c) => {\n        if (n >= a.MAX_SIGNATURES_SUPPORTED) throw new Error(`Cannot have a signature larger than ${a.MAX_SIGNATURES_SUPPORTED - 1}.`);\n        if (i.has(n)) throw new Error(\"Duplicate bits detected.\");\n        if (c > 0 && n <= e[c - 1]) throw new Error(\"The bits need to be sorted in ascending order.\");\n        i.add(n);\n        let u = Math.floor(n / 8),\n          b = s[u];\n        b |= t >> n % 8, s[u] = b;\n      }), s;\n    }\n  };\na.MAX_SIGNATURES_SUPPORTED = 32, a.BITMAP_LEN = 4;\nvar y = a;\nexport { d as a, y as b };", "map": {"version": 3, "names": ["o", "E", "constructor", "r", "publicKeys", "e", "threshold", "t", "length", "MAX_KEYS", "MIN_KEYS", "Error", "MIN_THRESHOLD", "verifySignature", "message", "signature", "y", "s", "i", "n", "bitmap", "u", "push", "signatures", "auth<PERSON><PERSON>", "g", "fromSchemeAndBytes", "scheme", "input", "toUint8Array", "Uint8Array", "h", "LENGTH", "for<PERSON>ach", "set", "serialize", "serializeBytes", "deserialize", "deserializeBytes", "subarray", "d", "a", "p", "MAX_SIGNATURES_SUPPORTED", "createBitmap", "bits", "BITMAP_LEN", "l", "Set", "c", "has", "add", "Math", "floor", "b"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\core\\crypto\\multiEd25519.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Deserializer, Serializer } from \"../../bcs\";\nimport { SigningScheme as AuthenticationKeyScheme } from \"../../types\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Ed25519Pub<PERSON><PERSON><PERSON>, Ed25519Signature } from \"./ed25519\";\nimport { AccountPublicKey, VerifySignatureArgs } from \"./publicKey\";\nimport { Signature } from \"./signature\";\n\n/**\n * Represents the public key of a K-of-N Ed25519 multi-sig transaction.\n */\nexport class MultiEd25519PublicKey extends AccountPublicKey {\n  /**\n   * Maximum number of public keys supported\n   */\n  static readonly MAX_KEYS = 32;\n\n  /**\n   * Minimum number of public keys needed\n   */\n  static readonly MIN_KEYS = 2;\n\n  /**\n   * Minimum threshold for the number of valid signatures required\n   */\n  static readonly MIN_THRESHOLD = 1;\n\n  /**\n   * List of Ed25519 public keys for this LegacyMultiEd25519PublicKey\n   */\n  public readonly publicKeys: Ed25519<PERSON>ub<PERSON><PERSON>ey[];\n\n  /**\n   * The minimum number of valid signatures required, for the number of public keys specified\n   */\n  public readonly threshold: number;\n\n  /**\n   * Public key for a K-of-N multi-sig transaction. A K-of-N multi-sig transaction means that for such a\n   * transaction to be executed, at least K out of the N authorized signers have signed the transaction\n   * and passed the check conducted by the chain.\n   *\n   * @see {@link\n   * https://aptos.dev/integration/creating-a-signed-transaction/ | Creating a Signed Transaction}\n   *\n   * @param args.publicKeys A list of public keys\n   * @param args.threshold At least \"threshold\" signatures must be valid\n   */\n  constructor(args: { publicKeys: Ed25519PublicKey[]; threshold: number }) {\n    super();\n    const { publicKeys, threshold } = args;\n\n    // Validate number of public keys\n    if (publicKeys.length > MultiEd25519PublicKey.MAX_KEYS || publicKeys.length < MultiEd25519PublicKey.MIN_KEYS) {\n      throw new Error(\n        `Must have between ${MultiEd25519PublicKey.MIN_KEYS} and ` +\n          `${MultiEd25519PublicKey.MAX_KEYS} public keys, inclusive`,\n      );\n    }\n\n    // Validate threshold: must be between 1 and the number of public keys, inclusive\n    if (threshold < MultiEd25519PublicKey.MIN_THRESHOLD || threshold > publicKeys.length) {\n      throw new Error(\n        `Threshold must be between ${MultiEd25519PublicKey.MIN_THRESHOLD} and ${publicKeys.length}, inclusive`,\n      );\n    }\n\n    this.publicKeys = publicKeys;\n    this.threshold = threshold;\n  }\n\n  // region AccountPublicKey\n\n  verifySignature(args: VerifySignatureArgs): boolean {\n    const { message, signature } = args;\n    if (!(signature instanceof MultiEd25519Signature)) {\n      return false;\n    }\n\n    const indices: number[] = [];\n    for (let i = 0; i < 4; i += 1) {\n      for (let j = 0; j < 8; j += 1) {\n        // eslint-disable-next-line no-bitwise\n        const bitIsSet = (signature.bitmap[i] & (1 << (7 - j))) !== 0;\n        if (bitIsSet) {\n          const index = i * 8 + j;\n          indices.push(index);\n        }\n      }\n    }\n\n    if (indices.length !== signature.signatures.length) {\n      throw new Error(\"Bitmap and signatures length mismatch\");\n    }\n\n    if (indices.length < this.threshold) {\n      throw new Error(\"Not enough signatures\");\n    }\n\n    for (let i = 0; i < indices.length; i += 1) {\n      const publicKey = this.publicKeys[indices[i]];\n      if (!publicKey.verifySignature({ message, signature: signature.signatures[i] })) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  authKey(): AuthenticationKey {\n    return AuthenticationKey.fromSchemeAndBytes({\n      scheme: AuthenticationKeyScheme.MultiEd25519,\n      input: this.toUint8Array(),\n    });\n  }\n\n  /**\n   * Converts a PublicKeys into Uint8Array (bytes) with: bytes = p1_bytes | ... | pn_bytes | threshold\n   */\n  toUint8Array(): Uint8Array {\n    const bytes = new Uint8Array(this.publicKeys.length * Ed25519PublicKey.LENGTH + 1);\n    this.publicKeys.forEach((k: Ed25519PublicKey, i: number) => {\n      bytes.set(k.toUint8Array(), i * Ed25519PublicKey.LENGTH);\n    });\n\n    bytes[this.publicKeys.length * Ed25519PublicKey.LENGTH] = this.threshold;\n\n    return bytes;\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): MultiEd25519PublicKey {\n    const bytes = deserializer.deserializeBytes();\n    const threshold = bytes[bytes.length - 1];\n\n    const keys: Ed25519PublicKey[] = [];\n\n    for (let i = 0; i < bytes.length - 1; i += Ed25519PublicKey.LENGTH) {\n      const begin = i;\n      keys.push(new Ed25519PublicKey(bytes.subarray(begin, begin + Ed25519PublicKey.LENGTH)));\n    }\n    return new MultiEd25519PublicKey({ publicKeys: keys, threshold });\n  }\n\n  // endregion\n}\n\n/**\n * Represents the signature of a K-of-N Ed25519 multi-sig transaction.\n */\nexport class MultiEd25519Signature extends Signature {\n  /**\n   * Maximum number of Ed25519 signatures supported\n   */\n  static MAX_SIGNATURES_SUPPORTED = 32;\n\n  /**\n   * Number of bytes in the bitmap representing who signed the transaction (32-bits)\n   */\n  static BITMAP_LEN: number = 4;\n\n  /**\n   * The list of underlying Ed25519 signatures\n   */\n  public readonly signatures: Ed25519Signature[];\n\n  /**\n   * 32-bit Bitmap representing who signed the transaction\n   *\n   * This is represented where each public key can be masked to determine whether the message was signed by that key.\n   */\n  public readonly bitmap: Uint8Array;\n\n  /**\n   * Signature for a K-of-N multi-sig transaction.\n   *\n   * @see {@link\n   * https://aptos.dev/integration/creating-a-signed-transaction/#multisignature-transactions | Creating a Signed Transaction}\n   *\n   * @param args.signatures A list of signatures\n   * @param args.bitmap 4 bytes, at most 32 signatures are supported. If Nth bit value is `1`, the Nth\n   * signature should be provided in `signatures`. Bits are read from left to right.\n   * Alternatively, you can specify an array of bitmap positions.\n   * Valid position should range between 0 and 31.\n   * @see MultiEd25519Signature.createBitmap\n   */\n  constructor(args: { signatures: Ed25519Signature[]; bitmap: Uint8Array | number[] }) {\n    super();\n    const { signatures, bitmap } = args;\n\n    if (signatures.length > MultiEd25519Signature.MAX_SIGNATURES_SUPPORTED) {\n      throw new Error(\n        `The number of signatures cannot be greater than ${MultiEd25519Signature.MAX_SIGNATURES_SUPPORTED}`,\n      );\n    }\n    this.signatures = signatures;\n\n    if (!(bitmap instanceof Uint8Array)) {\n      this.bitmap = MultiEd25519Signature.createBitmap({ bits: bitmap });\n    } else if (bitmap.length !== MultiEd25519Signature.BITMAP_LEN) {\n      throw new Error(`\"bitmap\" length should be ${MultiEd25519Signature.BITMAP_LEN}`);\n    } else {\n      this.bitmap = bitmap;\n    }\n  }\n\n  // region AccountSignature\n\n  /**\n   * Converts a MultiSignature into Uint8Array (bytes) with `bytes = s1_bytes | ... | sn_bytes | bitmap`\n   */\n  toUint8Array(): Uint8Array {\n    const bytes = new Uint8Array(this.signatures.length * Ed25519Signature.LENGTH + MultiEd25519Signature.BITMAP_LEN);\n    this.signatures.forEach((k: Ed25519Signature, i: number) => {\n      bytes.set(k.toUint8Array(), i * Ed25519Signature.LENGTH);\n    });\n\n    bytes.set(this.bitmap, this.signatures.length * Ed25519Signature.LENGTH);\n\n    return bytes;\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): MultiEd25519Signature {\n    const bytes = deserializer.deserializeBytes();\n    const bitmap = bytes.subarray(bytes.length - 4);\n\n    const signatures: Ed25519Signature[] = [];\n\n    for (let i = 0; i < bytes.length - bitmap.length; i += Ed25519Signature.LENGTH) {\n      const begin = i;\n      signatures.push(new Ed25519Signature(bytes.subarray(begin, begin + Ed25519Signature.LENGTH)));\n    }\n    return new MultiEd25519Signature({ signatures, bitmap });\n  }\n\n  // endregion\n\n  /**\n   * Helper method to create a bitmap out of the specified bit positions\n   * @param args.bits The bitmap positions that should be set. A position starts at index 0.\n   * Valid position should range between 0 and 31.\n   * @example\n   * Here's an example of valid `bits`\n   * ```\n   * [0, 2, 31]\n   * ```\n   * `[0, 2, 31]` means the 1st, 3rd and 32nd bits should be set in the bitmap.\n   * The result bitmap should be 0b1010000000000000000000000000001\n   *\n   * @returns bitmap that is 32bit long\n   */\n  static createBitmap(args: { bits: number[] }): Uint8Array {\n    const { bits } = args;\n    // Bits are read from left to right. e.g. 0b10000000 represents the first bit is set in one byte.\n    // The decimal value of 0b10000000 is 128.\n    const firstBitInByte = 128;\n    const bitmap = new Uint8Array([0, 0, 0, 0]);\n\n    // Check if duplicates exist in bits\n    const dupCheckSet = new Set();\n\n    bits.forEach((bit: number, index) => {\n      if (bit >= MultiEd25519Signature.MAX_SIGNATURES_SUPPORTED) {\n        throw new Error(`Cannot have a signature larger than ${MultiEd25519Signature.MAX_SIGNATURES_SUPPORTED - 1}.`);\n      }\n\n      if (dupCheckSet.has(bit)) {\n        throw new Error(\"Duplicate bits detected.\");\n      }\n\n      if (index > 0 && bit <= bits[index - 1]) {\n        throw new Error(\"The bits need to be sorted in ascending order.\");\n      }\n\n      dupCheckSet.add(bit);\n\n      const byteOffset = Math.floor(bit / 8);\n\n      let byte = bitmap[byteOffset];\n\n      // eslint-disable-next-line no-bitwise\n      byte |= firstBitInByte >> bit % 8;\n\n      bitmap[byteOffset] = byte;\n    });\n\n    return bitmap;\n  }\n}\n"], "mappings": ";;;;AAaO,IAAMA,CAAA,GAAN,MAAMA,CAAA,SAA8BC,CAAiB;EAqC1DC,YAAYC,CAAA,EAA6D;IACvE,MAAM;IACN,IAAM;MAAEC,UAAA,EAAAC,CAAA;MAAYC,SAAA,EAAAC;IAAU,IAAIJ,CAAA;IAGlC,IAAIE,CAAA,CAAWG,MAAA,GAASR,CAAA,CAAsBS,QAAA,IAAYJ,CAAA,CAAWG,MAAA,GAASR,CAAA,CAAsBU,QAAA,EAClG,MAAM,IAAIC,KAAA,CACR,qBAAqBX,CAAA,CAAsBU,QAAQ,QAC9CV,CAAA,CAAsBS,QAAQ,yBACrC;IAIF,IAAIF,CAAA,GAAYP,CAAA,CAAsBY,aAAA,IAAiBL,CAAA,GAAYF,CAAA,CAAWG,MAAA,EAC5E,MAAM,IAAIG,KAAA,CACR,6BAA6BX,CAAA,CAAsBY,aAAa,QAAQP,CAAA,CAAWG,MAAM,aAC3F;IAGF,KAAKJ,UAAA,GAAaC,CAAA,EAClB,KAAKC,SAAA,GAAYC,CACnB;EAAA;EAIAM,gBAAgBV,CAAA,EAAoC;IAClD,IAAM;MAAEW,OAAA,EAAAT,CAAA;MAASU,SAAA,EAAAR;IAAU,IAAIJ,CAAA;IAC/B,IAAI,EAAEI,CAAA,YAAqBS,CAAA,GACzB,OAAO;IAGT,IAAMC,CAAA,GAAoB,EAAC;IAC3B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK,GAC1B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK,GAG1B,KADkBZ,CAAA,CAAUa,MAAA,CAAOF,CAAC,IAAK,KAAM,IAAIC,CAAA,MAAS,GAC9C;MACZ,IAAME,CAAA,GAAQH,CAAA,GAAI,IAAIC,CAAA;MACtBF,CAAA,CAAQK,IAAA,CAAKD,CAAK,CACpB;IAAA;IAIJ,IAAIJ,CAAA,CAAQT,MAAA,KAAWD,CAAA,CAAUgB,UAAA,CAAWf,MAAA,EAC1C,MAAM,IAAIG,KAAA,CAAM,uCAAuC;IAGzD,IAAIM,CAAA,CAAQT,MAAA,GAAS,KAAKF,SAAA,EACxB,MAAM,IAAIK,KAAA,CAAM,uBAAuB;IAGzC,SAASO,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,CAAQT,MAAA,EAAQU,CAAA,IAAK,GAEvC,IAAI,CADc,KAAKd,UAAA,CAAWa,CAAA,CAAQC,CAAC,CAAC,EAC7BL,eAAA,CAAgB;MAAEC,OAAA,EAAAT,CAAA;MAASU,SAAA,EAAWR,CAAA,CAAUgB,UAAA,CAAWL,CAAC;IAAE,CAAC,GAC5E,OAAO;IAGX,OAAO,EACT;EAAA;EAEAM,QAAA,EAA6B;IAC3B,OAAOC,CAAA,CAAkBC,kBAAA,CAAmB;MAC1CC,MAAA;MACAC,KAAA,EAAO,KAAKC,YAAA,CAAa;IAC3B,CAAC,CACH;EAAA;EAKAA,aAAA,EAA2B;IACzB,IAAM1B,CAAA,GAAQ,IAAI2B,UAAA,CAAW,KAAK1B,UAAA,CAAWI,MAAA,GAASuB,CAAA,CAAiBC,MAAA,GAAS,CAAC;IACjF,YAAK5B,UAAA,CAAW6B,OAAA,CAAQ,CAAC5B,CAAA,EAAqBE,CAAA,KAAc;MAC1DJ,CAAA,CAAM+B,GAAA,CAAI7B,CAAA,CAAEwB,YAAA,CAAa,GAAGtB,CAAA,GAAIwB,CAAA,CAAiBC,MAAM,CACzD;IAAA,CAAC,GAED7B,CAAA,CAAM,KAAKC,UAAA,CAAWI,MAAA,GAASuB,CAAA,CAAiBC,MAAM,IAAI,KAAK1B,SAAA,EAExDH,CACT;EAAA;EAMAgC,UAAUhC,CAAA,EAA8B;IACtCA,CAAA,CAAWiC,cAAA,CAAe,KAAKP,YAAA,CAAa,CAAC,CAC/C;EAAA;EAEA,OAAOQ,YAAYlC,CAAA,EAAmD;IACpE,IAAME,CAAA,GAAQF,CAAA,CAAamC,gBAAA,CAAiB;MACtC/B,CAAA,GAAYF,CAAA,CAAMA,CAAA,CAAMG,MAAA,GAAS,CAAC;MAElCS,CAAA,GAA2B,EAAC;IAElC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIb,CAAA,CAAMG,MAAA,GAAS,GAAGU,CAAA,IAAKa,CAAA,CAAiBC,MAAA,EAAQ;MAClE,IAAMb,CAAA,GAAQD,CAAA;MACdD,CAAA,CAAKK,IAAA,CAAK,IAAIS,CAAA,CAAiB1B,CAAA,CAAMkC,QAAA,CAASpB,CAAA,EAAOA,CAAA,GAAQY,CAAA,CAAiBC,MAAM,CAAC,CAAC,CACxF;IAAA;IACA,OAAO,IAAIhC,CAAA,CAAsB;MAAEI,UAAA,EAAYa,CAAA;MAAMX,SAAA,EAAAC;IAAU,CAAC,CAClE;EAAA;AAGF;AA5IaP,CAAA,CAIKS,QAAA,GAAW,IAJhBT,CAAA,CASKU,QAAA,GAAW,GAThBV,CAAA,CAcKY,aAAA,GAAgB;AAd3B,IAAM4B,CAAA,GAANxC,CAAA;EAiJMyC,CAAA,GAAN,MAAMA,CAAA,SAA8BC,CAAU;IAoCnDxC,YAAYC,CAAA,EAAyE;MACnF,MAAM;MACN,IAAM;QAAEoB,UAAA,EAAAlB,CAAA;QAAYe,MAAA,EAAAb;MAAO,IAAIJ,CAAA;MAE/B,IAAIE,CAAA,CAAWG,MAAA,GAASiC,CAAA,CAAsBE,wBAAA,EAC5C,MAAM,IAAIhC,KAAA,CACR,mDAAmD8B,CAAA,CAAsBE,wBAAwB,EACnG;MAIF,IAFA,KAAKpB,UAAA,GAAalB,CAAA,EAEd,EAAEE,CAAA,YAAkBuB,UAAA,GACtB,KAAKV,MAAA,GAASqB,CAAA,CAAsBG,YAAA,CAAa;QAAEC,IAAA,EAAMtC;MAAO,CAAC,OAC5D;QAAA,IAAIA,CAAA,CAAOC,MAAA,KAAWiC,CAAA,CAAsBK,UAAA,EACjD,MAAM,IAAInC,KAAA,CAAM,6BAA6B8B,CAAA,CAAsBK,UAAU,EAAE;QAE/E,KAAK1B,MAAA,GAASb,CAAA;MAAA;IAElB;IAOAsB,aAAA,EAA2B;MACzB,IAAM1B,CAAA,GAAQ,IAAI2B,UAAA,CAAW,KAAKP,UAAA,CAAWf,MAAA,GAASuC,CAAA,CAAiBf,MAAA,GAASS,CAAA,CAAsBK,UAAU;MAChH,YAAKvB,UAAA,CAAWU,OAAA,CAAQ,CAAC5B,CAAA,EAAqBE,CAAA,KAAc;QAC1DJ,CAAA,CAAM+B,GAAA,CAAI7B,CAAA,CAAEwB,YAAA,CAAa,GAAGtB,CAAA,GAAIwC,CAAA,CAAiBf,MAAM,CACzD;MAAA,CAAC,GAED7B,CAAA,CAAM+B,GAAA,CAAI,KAAKd,MAAA,EAAQ,KAAKG,UAAA,CAAWf,MAAA,GAASuC,CAAA,CAAiBf,MAAM,GAEhE7B,CACT;IAAA;IAMAgC,UAAUhC,CAAA,EAA8B;MACtCA,CAAA,CAAWiC,cAAA,CAAe,KAAKP,YAAA,CAAa,CAAC,CAC/C;IAAA;IAEA,OAAOQ,YAAYlC,CAAA,EAAmD;MACpE,IAAME,CAAA,GAAQF,CAAA,CAAamC,gBAAA,CAAiB;QACtC/B,CAAA,GAASF,CAAA,CAAMkC,QAAA,CAASlC,CAAA,CAAMG,MAAA,GAAS,CAAC;QAExCS,CAAA,GAAiC,EAAC;MAExC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIb,CAAA,CAAMG,MAAA,GAASD,CAAA,CAAOC,MAAA,EAAQU,CAAA,IAAK6B,CAAA,CAAiBf,MAAA,EAAQ;QAC9E,IAAMb,CAAA,GAAQD,CAAA;QACdD,CAAA,CAAWK,IAAA,CAAK,IAAIyB,CAAA,CAAiB1C,CAAA,CAAMkC,QAAA,CAASpB,CAAA,EAAOA,CAAA,GAAQ4B,CAAA,CAAiBf,MAAM,CAAC,CAAC,CAC9F;MAAA;MACA,OAAO,IAAIS,CAAA,CAAsB;QAAElB,UAAA,EAAAN,CAAA;QAAYG,MAAA,EAAAb;MAAO,CAAC,CACzD;IAAA;IAkBA,OAAOqC,aAAazC,CAAA,EAAsC;MACxD,IAAM;UAAE0C,IAAA,EAAAxC;QAAK,IAAIF,CAAA;QAGXI,CAAA,GAAiB;QACjBU,CAAA,GAAS,IAAIa,UAAA,CAAW,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAGpCZ,CAAA,GAAc,IAAI8B,GAAA;MAExB,OAAA3C,CAAA,CAAK4B,OAAA,CAAQ,CAACd,CAAA,EAAa8B,CAAA,KAAU;QACnC,IAAI9B,CAAA,IAAOsB,CAAA,CAAsBE,wBAAA,EAC/B,MAAM,IAAIhC,KAAA,CAAM,uCAAuC8B,CAAA,CAAsBE,wBAAA,GAA2B,CAAC,GAAG;QAG9G,IAAIzB,CAAA,CAAYgC,GAAA,CAAI/B,CAAG,GACrB,MAAM,IAAIR,KAAA,CAAM,0BAA0B;QAG5C,IAAIsC,CAAA,GAAQ,KAAK9B,CAAA,IAAOd,CAAA,CAAK4C,CAAA,GAAQ,CAAC,GACpC,MAAM,IAAItC,KAAA,CAAM,gDAAgD;QAGlEO,CAAA,CAAYiC,GAAA,CAAIhC,CAAG;QAEnB,IAAME,CAAA,GAAa+B,IAAA,CAAKC,KAAA,CAAMlC,CAAA,GAAM,CAAC;UAEjCmC,CAAA,GAAOrC,CAAA,CAAOI,CAAU;QAG5BiC,CAAA,IAAQ/C,CAAA,IAAkBY,CAAA,GAAM,GAEhCF,CAAA,CAAOI,CAAU,IAAIiC,CACvB;MAAA,CAAC,GAEMrC,CACT;IAAA;EACF;AAlJawB,CAAA,CAIJE,wBAAA,GAA2B,IAJvBF,CAAA,CASJK,UAAA,GAAqB;AATvB,IAAM9B,CAAA,GAANyB,CAAA;AAAA,SAAAD,CAAA,IAAAC,CAAA,EAAAzB,CAAA,IAAAsC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}