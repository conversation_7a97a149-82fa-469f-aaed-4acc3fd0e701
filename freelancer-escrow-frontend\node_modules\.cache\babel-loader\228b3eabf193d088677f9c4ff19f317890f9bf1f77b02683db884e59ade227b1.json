{"ast": null, "code": "/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities\n/**\n * @__NO_SIDE_EFFECTS__\n */\nexport function assertNumber(n) {\n  if (!Number.isSafeInteger(n)) throw new Error(`Wrong integer: ${n}`);\n}\nfunction isBytes(a) {\n  return a instanceof Uint8Array || a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array';\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n  const id = a => a;\n  // Wrap call in closure so JIT can inline calls\n  const wrap = (a, b) => c => a(b(c));\n  // Construct chain of args[-1].encode(args[-2].encode([...]))\n  const encode = args.map(x => x.encode).reduceRight(wrap, id);\n  // Construct chain of args[0].decode(args[1].decode(...))\n  const decode = args.map(x => x.decode).reduce(wrap, id);\n  return {\n    encode,\n    decode\n  };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(alphabet) {\n  return {\n    encode: digits => {\n      if (!Array.isArray(digits) || digits.length && typeof digits[0] !== 'number') throw new Error('alphabet.encode input should be an array of numbers');\n      return digits.map(i => {\n        assertNumber(i);\n        if (i < 0 || i >= alphabet.length) throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n        return alphabet[i];\n      });\n    },\n    decode: input => {\n      if (!Array.isArray(input) || input.length && typeof input[0] !== 'string') throw new Error('alphabet.decode input should be array of strings');\n      return input.map(letter => {\n        if (typeof letter !== 'string') throw new Error(`alphabet.decode: not string element=${letter}`);\n        const index = alphabet.indexOf(letter);\n        if (index === -1) throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n        return index;\n      });\n    }\n  };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n  if (typeof separator !== 'string') throw new Error('join separator should be string');\n  return {\n    encode: from => {\n      if (!Array.isArray(from) || from.length && typeof from[0] !== 'string') throw new Error('join.encode input should be array of strings');\n      for (let i of from) if (typeof i !== 'string') throw new Error(`join.encode: non-string input=${i}`);\n      return from.join(separator);\n    },\n    decode: to => {\n      if (typeof to !== 'string') throw new Error('join.decode input should be string');\n      return to.split(separator);\n    }\n  };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n  assertNumber(bits);\n  if (typeof chr !== 'string') throw new Error('padding chr should be string');\n  return {\n    encode(data) {\n      if (!Array.isArray(data) || data.length && typeof data[0] !== 'string') throw new Error('padding.encode input should be array of strings');\n      for (let i of data) if (typeof i !== 'string') throw new Error(`padding.encode: non-string input=${i}`);\n      while (data.length * bits % 8) data.push(chr);\n      return data;\n    },\n    decode(input) {\n      if (!Array.isArray(input) || input.length && typeof input[0] !== 'string') throw new Error('padding.encode input should be array of strings');\n      for (let i of input) if (typeof i !== 'string') throw new Error(`padding.decode: non-string input=${i}`);\n      let end = input.length;\n      if (end * bits % 8) throw new Error('Invalid padding: string should have whole number of bytes');\n      for (; end > 0 && input[end - 1] === chr; end--) {\n        if (!((end - 1) * bits % 8)) throw new Error('Invalid padding: string has too much padding');\n      }\n      return input.slice(0, end);\n    }\n  };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize(fn) {\n  if (typeof fn !== 'function') throw new Error('normalize fn should be function');\n  return {\n    encode: from => from,\n    decode: to => fn(to)\n  };\n}\n/**\n * Slow: O(n^2) time complexity\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix(data, from, to) {\n  // base 1 is impossible\n  if (from < 2) throw new Error(`convertRadix: wrong from=${from}, base cannot be less than 2`);\n  if (to < 2) throw new Error(`convertRadix: wrong to=${to}, base cannot be less than 2`);\n  if (!Array.isArray(data)) throw new Error('convertRadix: data should be array');\n  if (!data.length) return [];\n  let pos = 0;\n  const res = [];\n  const digits = Array.from(data);\n  digits.forEach(d => {\n    assertNumber(d);\n    if (d < 0 || d >= from) throw new Error(`Wrong integer: ${d}`);\n  });\n  while (true) {\n    let carry = 0;\n    let done = true;\n    for (let i = pos; i < digits.length; i++) {\n      const digit = digits[i];\n      const digitBase = from * carry + digit;\n      if (!Number.isSafeInteger(digitBase) || from * carry / from !== carry || digitBase - digit !== from * carry) {\n        throw new Error('convertRadix: carry overflow');\n      }\n      carry = digitBase % to;\n      const rounded = Math.floor(digitBase / to);\n      digits[i] = rounded;\n      if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase) throw new Error('convertRadix: carry overflow');\n      if (!done) continue;else if (!rounded) pos = i;else done = false;\n    }\n    res.push(carry);\n    if (done) break;\n  }\n  for (let i = 0; i < data.length - 1 && data[i] === 0; i++) res.push(0);\n  return res.reverse();\n}\nconst gcd = /* @__NO_SIDE_EFFECTS__ */(a, b) => !b ? a : gcd(b, a % b);\nconst radix2carry = /*@__NO_SIDE_EFFECTS__ */(from, to) => from + (to - gcd(from, to));\n/**\n * Implemented with numbers, because BigInt is 5x slower\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix2(data, from, to, padding) {\n  if (!Array.isArray(data)) throw new Error('convertRadix2: data should be array');\n  if (from <= 0 || from > 32) throw new Error(`convertRadix2: wrong from=${from}`);\n  if (to <= 0 || to > 32) throw new Error(`convertRadix2: wrong to=${to}`);\n  if (radix2carry(from, to) > 32) {\n    throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n  }\n  let carry = 0;\n  let pos = 0; // bitwise position in current element\n  const mask = 2 ** to - 1;\n  const res = [];\n  for (const n of data) {\n    assertNumber(n);\n    if (n >= 2 ** from) throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n    carry = carry << from | n;\n    if (pos + from > 32) throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n    pos += from;\n    for (; pos >= to; pos -= to) res.push((carry >> pos - to & mask) >>> 0);\n    carry &= 2 ** pos - 1; // clean carry, otherwise it will cause overflow\n  }\n  carry = carry << to - pos & mask;\n  if (!padding && pos >= from) throw new Error('Excess padding');\n  if (!padding && carry) throw new Error(`Non-zero padding: ${carry}`);\n  if (padding && pos > 0) res.push(carry >>> 0);\n  return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n  assertNumber(num);\n  return {\n    encode: bytes => {\n      if (!isBytes(bytes)) throw new Error('radix.encode input should be Uint8Array');\n      return convertRadix(Array.from(bytes), 2 ** 8, num);\n    },\n    decode: digits => {\n      if (!Array.isArray(digits) || digits.length && typeof digits[0] !== 'number') throw new Error('radix.decode input should be array of numbers');\n      return Uint8Array.from(convertRadix(digits, num, 2 ** 8));\n    }\n  };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n  assertNumber(bits);\n  if (bits <= 0 || bits > 32) throw new Error('radix2: bits should be in (0..32]');\n  if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32) throw new Error('radix2: carry overflow');\n  return {\n    encode: bytes => {\n      if (!isBytes(bytes)) throw new Error('radix2.encode input should be Uint8Array');\n      return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n    },\n    decode: digits => {\n      if (!Array.isArray(digits) || digits.length && typeof digits[0] !== 'number') throw new Error('radix2.decode input should be array of numbers');\n      return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n    }\n  };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction unsafeWrapper(fn) {\n  if (typeof fn !== 'function') throw new Error('unsafeWrapper fn should be function');\n  return function (...args) {\n    try {\n      return fn.apply(null, args);\n    } catch (e) {}\n  };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction checksum(len, fn) {\n  assertNumber(len);\n  if (typeof fn !== 'function') throw new Error('checksum fn should be function');\n  return {\n    encode(data) {\n      if (!isBytes(data)) throw new Error('checksum.encode: input should be Uint8Array');\n      const checksum = fn(data).slice(0, len);\n      const res = new Uint8Array(data.length + len);\n      res.set(data);\n      res.set(checksum, data.length);\n      return res;\n    },\n    decode(data) {\n      if (!isBytes(data)) throw new Error('checksum.decode: input should be Uint8Array');\n      const payload = data.slice(0, -len);\n      const newChecksum = fn(payload).slice(0, len);\n      const oldChecksum = data.slice(-len);\n      for (let i = 0; i < len; i++) if (newChecksum[i] !== oldChecksum[i]) throw new Error('Invalid checksum');\n      return payload;\n    }\n  };\n}\n// prettier-ignore\nexport const utils = {\n  alphabet,\n  chain,\n  checksum,\n  convertRadix,\n  convertRadix2,\n  radix,\n  radix2,\n  join,\n  padding\n};\n// RFC 4648 aka RFC 3548\n// ---------------------\nexport const base16 = /* @__PURE__ */chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\nexport const base32 = /* @__PURE__ */chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), padding(5), join(''));\nexport const base32nopad = /* @__PURE__ */chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), join(''));\nexport const base32hex = /* @__PURE__ */chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), padding(5), join(''));\nexport const base32hexnopad = /* @__PURE__ */chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), join(''));\nexport const base32crockford = /* @__PURE__ */chain(radix2(5), alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'), join(''), normalize(s => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1')));\nexport const base64 = /* @__PURE__ */chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\nexport const base64nopad = /* @__PURE__ */chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), join(''));\nexport const base64url = /* @__PURE__ */chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\nexport const base64urlnopad = /* @__PURE__ */chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), join(''));\n// base58 code\n// -----------\nconst genBase58 = abc => chain(radix(58), alphabet(abc), join(''));\nexport const base58 = /* @__PURE__ */genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\nexport const base58flickr = /* @__PURE__ */genBase58('123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ');\nexport const base58xrp = /* @__PURE__ */genBase58('rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz');\n// xmr ver is done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n// Block encoding significantly reduces quadratic complexity of base58.\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\nexport const base58xmr = {\n  encode(data) {\n    let res = '';\n    for (let i = 0; i < data.length; i += 8) {\n      const block = data.subarray(i, i + 8);\n      res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length], '1');\n    }\n    return res;\n  },\n  decode(str) {\n    let res = [];\n    for (let i = 0; i < str.length; i += 11) {\n      const slice = str.slice(i, i + 11);\n      const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n      const block = base58.decode(slice);\n      for (let j = 0; j < block.length - blockLen; j++) {\n        if (block[j] !== 0) throw new Error('base58xmr: wrong padding');\n      }\n      res = res.concat(Array.from(block.slice(block.length - blockLen)));\n    }\n    return Uint8Array.from(res);\n  }\n};\nexport const createBase58check = sha256 => chain(checksum(4, data => sha256(sha256(data))), base58);\n// legacy export, bad name\nexport const base58check = createBase58check;\nconst BECH_ALPHABET = /* @__PURE__ */chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bech32Polymod(pre) {\n  const b = pre >> 25;\n  let chk = (pre & 0x1ffffff) << 5;\n  for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n    if ((b >> i & 1) === 1) chk ^= POLYMOD_GENERATORS[i];\n  }\n  return chk;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n  const len = prefix.length;\n  let chk = 1;\n  for (let i = 0; i < len; i++) {\n    const c = prefix.charCodeAt(i);\n    if (c < 33 || c > 126) throw new Error(`Invalid prefix (${prefix})`);\n    chk = bech32Polymod(chk) ^ c >> 5;\n  }\n  chk = bech32Polymod(chk);\n  for (let i = 0; i < len; i++) chk = bech32Polymod(chk) ^ prefix.charCodeAt(i) & 0x1f;\n  for (let v of words) chk = bech32Polymod(chk) ^ v;\n  for (let i = 0; i < 6; i++) chk = bech32Polymod(chk);\n  chk ^= encodingConst;\n  return BECH_ALPHABET.encode(convertRadix2([chk % 2 ** 30], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n  const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n  const _words = radix2(5);\n  const fromWords = _words.decode;\n  const toWords = _words.encode;\n  const fromWordsUnsafe = unsafeWrapper(fromWords);\n  function encode(prefix, words, limit = 90) {\n    if (typeof prefix !== 'string') throw new Error(`bech32.encode prefix should be string, not ${typeof prefix}`);\n    if (words instanceof Uint8Array) words = Array.from(words);\n    if (!Array.isArray(words) || words.length && typeof words[0] !== 'number') throw new Error(`bech32.encode words should be array of numbers, not ${typeof words}`);\n    if (prefix.length === 0) throw new TypeError(`Invalid prefix length ${prefix.length}`);\n    const actualLength = prefix.length + 7 + words.length;\n    if (limit !== false && actualLength > limit) throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n    const lowered = prefix.toLowerCase();\n    const sum = bechChecksum(lowered, words, ENCODING_CONST);\n    return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n  }\n  function decode(str, limit = 90) {\n    if (typeof str !== 'string') throw new Error(`bech32.decode input should be string, not ${typeof str}`);\n    if (str.length < 8 || limit !== false && str.length > limit) throw new TypeError(`Wrong string length: ${str.length} (${str}). Expected (8..${limit})`);\n    // don't allow mixed case\n    const lowered = str.toLowerCase();\n    if (str !== lowered && str !== str.toUpperCase()) throw new Error(`String must be lowercase or uppercase`);\n    const sepIndex = lowered.lastIndexOf('1');\n    if (sepIndex === 0 || sepIndex === -1) throw new Error(`Letter \"1\" must be present between prefix and data only`);\n    const prefix = lowered.slice(0, sepIndex);\n    const data = lowered.slice(sepIndex + 1);\n    if (data.length < 6) throw new Error('Data must be at least 6 characters long');\n    const words = BECH_ALPHABET.decode(data).slice(0, -6);\n    const sum = bechChecksum(prefix, words, ENCODING_CONST);\n    if (!data.endsWith(sum)) throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n    return {\n      prefix,\n      words\n    };\n  }\n  const decodeUnsafe = unsafeWrapper(decode);\n  function decodeToBytes(str) {\n    const {\n      prefix,\n      words\n    } = decode(str, false);\n    return {\n      prefix,\n      words,\n      bytes: fromWords(words)\n    };\n  }\n  function encodeFromBytes(prefix, bytes) {\n    return encode(prefix, toWords(bytes));\n  }\n  return {\n    encode,\n    decode,\n    encodeFromBytes,\n    decodeToBytes,\n    decodeUnsafe,\n    fromWords,\n    fromWordsUnsafe,\n    toWords\n  };\n}\nexport const bech32 = /* @__PURE__ */genBech32('bech32');\nexport const bech32m = /* @__PURE__ */genBech32('bech32m');\nexport const utf8 = {\n  encode: data => new TextDecoder().decode(data),\n  decode: str => new TextEncoder().encode(str)\n};\nexport const hex = /* @__PURE__ */chain(radix2(4), alphabet('0123456789abcdef'), join(''), normalize(s => {\n  if (typeof s !== 'string' || s.length % 2) throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n  return s.toLowerCase();\n}));\n// prettier-ignore\nconst CODERS = {\n  utf8,\n  hex,\n  base16,\n  base32,\n  base64,\n  base64url,\n  base58,\n  base58xmr\n};\nconst coderTypeError = 'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\nexport const bytesToString = (type, bytes) => {\n  if (typeof type !== 'string' || !CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (!isBytes(bytes)) throw new TypeError('bytesToString() expects Uint8Array');\n  return CODERS[type].encode(bytes);\n};\nexport const str = bytesToString; // as in python, but for bytes only\nexport const stringToBytes = (type, str) => {\n  if (!CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (typeof str !== 'string') throw new TypeError('stringToBytes() expects string');\n  return CODERS[type].decode(str);\n};\nexport const bytes = stringToBytes;", "map": {"version": 3, "names": ["assertNumber", "n", "Number", "isSafeInteger", "Error", "isBytes", "a", "Uint8Array", "constructor", "name", "chain", "args", "id", "wrap", "b", "c", "encode", "map", "x", "reduceRight", "decode", "reduce", "alphabet", "digits", "Array", "isArray", "length", "i", "input", "letter", "index", "indexOf", "join", "separator", "from", "to", "split", "padding", "bits", "chr", "data", "push", "end", "slice", "normalize", "fn", "convertRadix", "pos", "res", "for<PERSON>ach", "d", "carry", "done", "digit", "digitBase", "rounded", "Math", "floor", "reverse", "gcd", "radix2carry", "convertRadix2", "mask", "radix", "num", "bytes", "radix2", "revPadding", "unsafeWrapper", "apply", "e", "checksum", "len", "set", "payload", "newChe<PERSON><PERSON>", "oldChecksum", "utils", "base16", "base32", "base32nopad", "base32hex", "base32hexnopad", "base32crockford", "s", "toUpperCase", "replace", "base64", "base64nopad", "base64url", "base64urlnopad", "genBase58", "abc", "base58", "base58flickr", "base58xrp", "XMR_BLOCK_LEN", "base58xmr", "block", "subarray", "padStart", "str", "blockLen", "j", "concat", "createBase58check", "sha256", "base58check", "BECH_ALPHABET", "POLYMOD_GENERATORS", "bech32Polymod", "pre", "chk", "bechChecksum", "prefix", "words", "encodingConst", "charCodeAt", "v", "genBech32", "encoding", "ENCODING_CONST", "_words", "fromWords", "to<PERSON><PERSON>s", "fromWordsUnsafe", "limit", "TypeError", "actualLength", "lowered", "toLowerCase", "sum", "sepIndex", "lastIndexOf", "endsWith", "decodeUnsafe", "decodeToBytes", "encodeFromBytes", "bech32", "bech32m", "utf8", "TextDecoder", "TextEncoder", "hex", "CODERS", "coderTypeError", "bytesToString", "type", "hasOwnProperty", "stringToBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\aptos\\node_modules\\@scure\\base\\index.ts"], "sourcesContent": ["/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// Utilities\n/**\n * @__NO_SIDE_EFFECTS__\n */\nexport function assertNumber(n: number) {\n  if (!Number.isSafeInteger(n)) throw new Error(`Wrong integer: ${n}`);\n}\nexport interface Coder<F, T> {\n  encode(from: F): T;\n  decode(to: T): F;\n}\n\nexport interface BytesCoder extends Coder<Uint8Array, string> {\n  encode: (data: Uint8Array) => string;\n  decode: (str: string) => Uint8Array;\n}\n\nfunction isBytes(a: unknown): a is Uint8Array {\n  return (\n    a instanceof Uint8Array ||\n    (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array')\n  );\n}\n\n// TODO: some recusive type inference so it would check correct order of input/output inside rest?\n// like <string, number>, <number, bytes>, <bytes, float>\ntype Chain = [Coder<any, any>, ...Coder<any, any>[]];\n// Extract info from Coder type\ntype Input<F> = F extends Coder<infer T, any> ? T : never;\ntype Output<F> = F extends Coder<any, infer T> ? T : never;\n// Generic function for arrays\ntype First<T> = T extends [infer U, ...any[]] ? U : never;\ntype Last<T> = T extends [...any[], infer U] ? U : never;\ntype Tail<T> = T extends [any, ...infer U] ? U : never;\n\ntype AsChain<C extends Chain, Rest = Tail<C>> = {\n  // C[K] = Coder<Input<C[K]>, Input<Rest[k]>>\n  [K in keyof C]: Coder<Input<C[K]>, Input<K extends keyof Rest ? Rest[K] : any>>;\n};\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain<T extends Chain & AsChain<T>>(...args: T): Coder<Input<First<T>>, Output<Last<T>>> {\n  const id = (a: any) => a;\n  // Wrap call in closure so JIT can inline calls\n  const wrap = (a: any, b: any) => (c: any) => a(b(c));\n  // Construct chain of args[-1].encode(args[-2].encode([...]))\n  const encode = args.map((x) => x.encode).reduceRight(wrap, id);\n  // Construct chain of args[0].decode(args[1].decode(...))\n  const decode = args.map((x) => x.decode).reduce(wrap, id);\n  return { encode, decode };\n}\n\ntype Alphabet = string[] | string;\n\n/**\n * Encodes integer radix representation to array of strings using alphabet and back\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(alphabet: Alphabet): Coder<number[], string[]> {\n  return {\n    encode: (digits: number[]) => {\n      if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n        throw new Error('alphabet.encode input should be an array of numbers');\n      return digits.map((i) => {\n        assertNumber(i);\n        if (i < 0 || i >= alphabet.length)\n          throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n        return alphabet[i]!;\n      });\n    },\n    decode: (input: string[]) => {\n      if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n        throw new Error('alphabet.decode input should be array of strings');\n      return input.map((letter) => {\n        if (typeof letter !== 'string')\n          throw new Error(`alphabet.decode: not string element=${letter}`);\n        const index = alphabet.indexOf(letter);\n        if (index === -1) throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n        return index;\n      });\n    },\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = ''): Coder<string[], string> {\n  if (typeof separator !== 'string') throw new Error('join separator should be string');\n  return {\n    encode: (from) => {\n      if (!Array.isArray(from) || (from.length && typeof from[0] !== 'string'))\n        throw new Error('join.encode input should be array of strings');\n      for (let i of from)\n        if (typeof i !== 'string') throw new Error(`join.encode: non-string input=${i}`);\n      return from.join(separator);\n    },\n    decode: (to) => {\n      if (typeof to !== 'string') throw new Error('join.decode input should be string');\n      return to.split(separator);\n    },\n  };\n}\n\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits: number, chr = '='): Coder<string[], string[]> {\n  assertNumber(bits);\n  if (typeof chr !== 'string') throw new Error('padding chr should be string');\n  return {\n    encode(data: string[]): string[] {\n      if (!Array.isArray(data) || (data.length && typeof data[0] !== 'string'))\n        throw new Error('padding.encode input should be array of strings');\n      for (let i of data)\n        if (typeof i !== 'string') throw new Error(`padding.encode: non-string input=${i}`);\n      while ((data.length * bits) % 8) data.push(chr);\n      return data;\n    },\n    decode(input: string[]): string[] {\n      if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n        throw new Error('padding.encode input should be array of strings');\n      for (let i of input)\n        if (typeof i !== 'string') throw new Error(`padding.decode: non-string input=${i}`);\n      let end = input.length;\n      if ((end * bits) % 8)\n        throw new Error('Invalid padding: string should have whole number of bytes');\n      for (; end > 0 && input[end - 1] === chr; end--) {\n        if (!(((end - 1) * bits) % 8))\n          throw new Error('Invalid padding: string has too much padding');\n      }\n      return input.slice(0, end);\n    },\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize<T>(fn: (val: T) => T): Coder<T, T> {\n  if (typeof fn !== 'function') throw new Error('normalize fn should be function');\n  return { encode: (from: T) => from, decode: (to: T) => fn(to) };\n}\n\n/**\n * Slow: O(n^2) time complexity\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix(data: number[], from: number, to: number): number[] {\n  // base 1 is impossible\n  if (from < 2) throw new Error(`convertRadix: wrong from=${from}, base cannot be less than 2`);\n  if (to < 2) throw new Error(`convertRadix: wrong to=${to}, base cannot be less than 2`);\n  if (!Array.isArray(data)) throw new Error('convertRadix: data should be array');\n  if (!data.length) return [];\n  let pos = 0;\n  const res = [];\n  const digits = Array.from(data);\n  digits.forEach((d) => {\n    assertNumber(d);\n    if (d < 0 || d >= from) throw new Error(`Wrong integer: ${d}`);\n  });\n  while (true) {\n    let carry = 0;\n    let done = true;\n    for (let i = pos; i < digits.length; i++) {\n      const digit = digits[i]!;\n      const digitBase = from * carry + digit;\n      if (\n        !Number.isSafeInteger(digitBase) ||\n        (from * carry) / from !== carry ||\n        digitBase - digit !== from * carry\n      ) {\n        throw new Error('convertRadix: carry overflow');\n      }\n      carry = digitBase % to;\n      const rounded = Math.floor(digitBase / to);\n      digits[i] = rounded;\n      if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n        throw new Error('convertRadix: carry overflow');\n      if (!done) continue;\n      else if (!rounded) pos = i;\n      else done = false;\n    }\n    res.push(carry);\n    if (done) break;\n  }\n  for (let i = 0; i < data.length - 1 && data[i] === 0; i++) res.push(0);\n  return res.reverse();\n}\n\nconst gcd = /* @__NO_SIDE_EFFECTS__ */ (a: number, b: number): number => (!b ? a : gcd(b, a % b));\nconst radix2carry = /*@__NO_SIDE_EFFECTS__ */ (from: number, to: number) =>\n  from + (to - gcd(from, to));\n/**\n * Implemented with numbers, because BigInt is 5x slower\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix2(data: number[], from: number, to: number, padding: boolean): number[] {\n  if (!Array.isArray(data)) throw new Error('convertRadix2: data should be array');\n  if (from <= 0 || from > 32) throw new Error(`convertRadix2: wrong from=${from}`);\n  if (to <= 0 || to > 32) throw new Error(`convertRadix2: wrong to=${to}`);\n  if (radix2carry(from, to) > 32) {\n    throw new Error(\n      `convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`\n    );\n  }\n  let carry = 0;\n  let pos = 0; // bitwise position in current element\n  const mask = 2 ** to - 1;\n  const res: number[] = [];\n  for (const n of data) {\n    assertNumber(n);\n    if (n >= 2 ** from) throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n    carry = (carry << from) | n;\n    if (pos + from > 32) throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n    pos += from;\n    for (; pos >= to; pos -= to) res.push(((carry >> (pos - to)) & mask) >>> 0);\n    carry &= 2 ** pos - 1; // clean carry, otherwise it will cause overflow\n  }\n  carry = (carry << (to - pos)) & mask;\n  if (!padding && pos >= from) throw new Error('Excess padding');\n  if (!padding && carry) throw new Error(`Non-zero padding: ${carry}`);\n  if (padding && pos > 0) res.push(carry >>> 0);\n  return res;\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num: number): Coder<Uint8Array, number[]> {\n  assertNumber(num);\n  return {\n    encode: (bytes: Uint8Array) => {\n      if (!isBytes(bytes)) throw new Error('radix.encode input should be Uint8Array');\n      return convertRadix(Array.from(bytes), 2 ** 8, num);\n    },\n    decode: (digits: number[]) => {\n      if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n        throw new Error('radix.decode input should be array of numbers');\n      return Uint8Array.from(convertRadix(digits, num, 2 ** 8));\n    },\n  };\n}\n\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits: number, revPadding = false): Coder<Uint8Array, number[]> {\n  assertNumber(bits);\n  if (bits <= 0 || bits > 32) throw new Error('radix2: bits should be in (0..32]');\n  if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n    throw new Error('radix2: carry overflow');\n  return {\n    encode: (bytes: Uint8Array) => {\n      if (!isBytes(bytes)) throw new Error('radix2.encode input should be Uint8Array');\n      return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n    },\n    decode: (digits: number[]) => {\n      if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n        throw new Error('radix2.decode input should be array of numbers');\n      return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n    },\n  };\n}\n\ntype ArgumentTypes<F extends Function> = F extends (...args: infer A) => any ? A : never;\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction unsafeWrapper<T extends (...args: any) => any>(fn: T) {\n  if (typeof fn !== 'function') throw new Error('unsafeWrapper fn should be function');\n  return function (...args: ArgumentTypes<T>): ReturnType<T> | void {\n    try {\n      return fn.apply(null, args);\n    } catch (e) {}\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction checksum(\n  len: number,\n  fn: (data: Uint8Array) => Uint8Array\n): Coder<Uint8Array, Uint8Array> {\n  assertNumber(len);\n  if (typeof fn !== 'function') throw new Error('checksum fn should be function');\n  return {\n    encode(data: Uint8Array) {\n      if (!isBytes(data)) throw new Error('checksum.encode: input should be Uint8Array');\n      const checksum = fn(data).slice(0, len);\n      const res = new Uint8Array(data.length + len);\n      res.set(data);\n      res.set(checksum, data.length);\n      return res;\n    },\n    decode(data: Uint8Array) {\n      if (!isBytes(data)) throw new Error('checksum.decode: input should be Uint8Array');\n      const payload = data.slice(0, -len);\n      const newChecksum = fn(payload).slice(0, len);\n      const oldChecksum = data.slice(-len);\n      for (let i = 0; i < len; i++)\n        if (newChecksum[i] !== oldChecksum[i]) throw new Error('Invalid checksum');\n      return payload;\n    },\n  };\n}\n\n// prettier-ignore\nexport const utils = {\n  alphabet, chain, checksum, convertRadix, convertRadix2, radix, radix2, join, padding,\n};\n\n// RFC 4648 aka RFC 3548\n// ---------------------\nexport const base16: BytesCoder = /* @__PURE__ */ chain(\n  radix2(4),\n  alphabet('0123456789ABCDEF'),\n  join('')\n);\nexport const base32: BytesCoder = /* @__PURE__ */ chain(\n  radix2(5),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'),\n  padding(5),\n  join('')\n);\nexport const base32nopad: BytesCoder = /* @__PURE__ */ chain(\n  radix2(5),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'),\n  join('')\n);\nexport const base32hex: BytesCoder = /* @__PURE__ */ chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'),\n  padding(5),\n  join('')\n);\nexport const base32hexnopad: BytesCoder = /* @__PURE__ */ chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'),\n  join('')\n);\nexport const base32crockford: BytesCoder = /* @__PURE__ */ chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'),\n  join(''),\n  normalize((s: string) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1'))\n);\nexport const base64: BytesCoder = /* @__PURE__ */ chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'),\n  padding(6),\n  join('')\n);\nexport const base64nopad: BytesCoder = /* @__PURE__ */ chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'),\n  join('')\n);\nexport const base64url: BytesCoder = /* @__PURE__ */ chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'),\n  padding(6),\n  join('')\n);\nexport const base64urlnopad: BytesCoder = /* @__PURE__ */ chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'),\n  join('')\n);\n\n// base58 code\n// -----------\nconst genBase58 = (abc: string) => chain(radix(58), alphabet(abc), join(''));\n\nexport const base58: BytesCoder = /* @__PURE__ */ genBase58(\n  '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n);\nexport const base58flickr: BytesCoder = /* @__PURE__ */ genBase58(\n  '123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ'\n);\nexport const base58xrp: BytesCoder = /* @__PURE__ */ genBase58(\n  'rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz'\n);\n\n// xmr ver is done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n// Block encoding significantly reduces quadratic complexity of base58.\n\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\nexport const base58xmr: BytesCoder = {\n  encode(data: Uint8Array) {\n    let res = '';\n    for (let i = 0; i < data.length; i += 8) {\n      const block = data.subarray(i, i + 8);\n      res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length]!, '1');\n    }\n    return res;\n  },\n  decode(str: string) {\n    let res: number[] = [];\n    for (let i = 0; i < str.length; i += 11) {\n      const slice = str.slice(i, i + 11);\n      const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n      const block = base58.decode(slice);\n      for (let j = 0; j < block.length - blockLen; j++) {\n        if (block[j] !== 0) throw new Error('base58xmr: wrong padding');\n      }\n      res = res.concat(Array.from(block.slice(block.length - blockLen)));\n    }\n    return Uint8Array.from(res);\n  },\n};\n\nexport const createBase58check = (sha256: (data: Uint8Array) => Uint8Array): BytesCoder =>\n  chain(\n    checksum(4, (data) => sha256(sha256(data))),\n    base58\n  );\n// legacy export, bad name\nexport const base58check = createBase58check;\n\n// Bech32 code\n// -----------\nexport interface Bech32Decoded<Prefix extends string = string> {\n  prefix: Prefix;\n  words: number[];\n}\nexport interface Bech32DecodedWithArray<Prefix extends string = string> {\n  prefix: Prefix;\n  words: number[];\n  bytes: Uint8Array;\n}\n\nconst BECH_ALPHABET: Coder<number[], string> = /* @__PURE__ */ chain(\n  alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'),\n  join('')\n);\n\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bech32Polymod(pre: number): number {\n  const b = pre >> 25;\n  let chk = (pre & 0x1ffffff) << 5;\n  for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n    if (((b >> i) & 1) === 1) chk ^= POLYMOD_GENERATORS[i]!;\n  }\n  return chk;\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bechChecksum(prefix: string, words: number[], encodingConst = 1): string {\n  const len = prefix.length;\n  let chk = 1;\n  for (let i = 0; i < len; i++) {\n    const c = prefix.charCodeAt(i);\n    if (c < 33 || c > 126) throw new Error(`Invalid prefix (${prefix})`);\n    chk = bech32Polymod(chk) ^ (c >> 5);\n  }\n  chk = bech32Polymod(chk);\n  for (let i = 0; i < len; i++) chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n  for (let v of words) chk = bech32Polymod(chk) ^ v;\n  for (let i = 0; i < 6; i++) chk = bech32Polymod(chk);\n  chk ^= encodingConst;\n  return BECH_ALPHABET.encode(convertRadix2([chk % 2 ** 30], 30, 5, false));\n}\n\nexport interface Bech32 {\n  encode<Prefix extends string>(\n    prefix: Prefix,\n    words: number[] | Uint8Array,\n    limit?: number | false\n  ): `${Lowercase<Prefix>}1${string}`;\n  decode<Prefix extends string>(\n    str: `${Prefix}1${string}`,\n    limit?: number | false\n  ): Bech32Decoded<Prefix>;\n  encodeFromBytes(prefix: string, bytes: Uint8Array): string;\n  decodeToBytes(str: string): Bech32DecodedWithArray;\n  decodeUnsafe(str: string, limit?: number | false): void | Bech32Decoded<string>;\n  fromWords(to: number[]): Uint8Array;\n  fromWordsUnsafe(to: number[]): void | Uint8Array;\n  toWords(from: Uint8Array): number[];\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding: 'bech32' | 'bech32m'): Bech32 {\n  const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n  const _words = radix2(5);\n  const fromWords = _words.decode;\n  const toWords = _words.encode;\n  const fromWordsUnsafe = unsafeWrapper(fromWords);\n\n  function encode<Prefix extends string>(\n    prefix: Prefix,\n    words: number[] | Uint8Array,\n    limit: number | false = 90\n  ): `${Lowercase<Prefix>}1${string}` {\n    if (typeof prefix !== 'string')\n      throw new Error(`bech32.encode prefix should be string, not ${typeof prefix}`);\n    if (words instanceof Uint8Array) words = Array.from(words);\n    if (!Array.isArray(words) || (words.length && typeof words[0] !== 'number'))\n      throw new Error(`bech32.encode words should be array of numbers, not ${typeof words}`);\n    if (prefix.length === 0) throw new TypeError(`Invalid prefix length ${prefix.length}`);\n    const actualLength = prefix.length + 7 + words.length;\n    if (limit !== false && actualLength > limit)\n      throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n    const lowered = prefix.toLowerCase();\n    const sum = bechChecksum(lowered, words, ENCODING_CONST);\n    return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}` as `${Lowercase<Prefix>}1${string}`;\n  }\n\n  function decode<Prefix extends string>(\n    str: `${Prefix}1${string}`,\n    limit?: number | false\n  ): Bech32Decoded<Prefix>;\n  function decode(str: string, limit?: number | false): Bech32Decoded;\n  function decode(str: string, limit: number | false = 90): Bech32Decoded {\n    if (typeof str !== 'string')\n      throw new Error(`bech32.decode input should be string, not ${typeof str}`);\n    if (str.length < 8 || (limit !== false && str.length > limit))\n      throw new TypeError(`Wrong string length: ${str.length} (${str}). Expected (8..${limit})`);\n    // don't allow mixed case\n    const lowered = str.toLowerCase();\n    if (str !== lowered && str !== str.toUpperCase())\n      throw new Error(`String must be lowercase or uppercase`);\n    const sepIndex = lowered.lastIndexOf('1');\n    if (sepIndex === 0 || sepIndex === -1)\n      throw new Error(`Letter \"1\" must be present between prefix and data only`);\n    const prefix = lowered.slice(0, sepIndex);\n    const data = lowered.slice(sepIndex + 1);\n    if (data.length < 6) throw new Error('Data must be at least 6 characters long');\n    const words = BECH_ALPHABET.decode(data).slice(0, -6);\n    const sum = bechChecksum(prefix, words, ENCODING_CONST);\n    if (!data.endsWith(sum)) throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n    return { prefix, words };\n  }\n\n  const decodeUnsafe = unsafeWrapper(decode);\n\n  function decodeToBytes(str: string): Bech32DecodedWithArray {\n    const { prefix, words } = decode(str, false);\n    return { prefix, words, bytes: fromWords(words) };\n  }\n\n  function encodeFromBytes(prefix: string, bytes: Uint8Array) {\n    return encode(prefix, toWords(bytes));\n  }\n\n  return {\n    encode,\n    decode,\n    encodeFromBytes,\n    decodeToBytes,\n    decodeUnsafe,\n    fromWords,\n    fromWordsUnsafe,\n    toWords,\n  };\n}\n\nexport const bech32: Bech32 = /* @__PURE__ */ genBech32('bech32');\nexport const bech32m: Bech32 = /* @__PURE__ */ genBech32('bech32m');\n\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\nexport const utf8: BytesCoder = {\n  encode: (data) => new TextDecoder().decode(data),\n  decode: (str) => new TextEncoder().encode(str),\n};\n\nexport const hex: BytesCoder = /* @__PURE__ */ chain(\n  radix2(4),\n  alphabet('0123456789abcdef'),\n  join(''),\n  normalize((s: string) => {\n    if (typeof s !== 'string' || s.length % 2)\n      throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n    return s.toLowerCase();\n  })\n);\n\n// prettier-ignore\nconst CODERS = {\n  utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\ntype CoderType = keyof typeof CODERS;\nconst coderTypeError =\n  'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\n\nexport const bytesToString = (type: CoderType, bytes: Uint8Array): string => {\n  if (typeof type !== 'string' || !CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (!isBytes(bytes)) throw new TypeError('bytesToString() expects Uint8Array');\n  return CODERS[type].encode(bytes);\n};\nexport const str = bytesToString; // as in python, but for bytes only\n\nexport const stringToBytes = (type: CoderType, str: string): Uint8Array => {\n  if (!CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (typeof str !== 'string') throw new TypeError('stringToBytes() expects string');\n  return CODERS[type].decode(str);\n};\nexport const bytes = stringToBytes;\n"], "mappings": "AAAA;AAEA;AACA;;;AAGA,OAAM,SAAUA,YAAYA,CAACC,CAAS;EACpC,IAAI,CAACC,MAAM,CAACC,aAAa,CAACF,CAAC,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,kBAAkBH,CAAC,EAAE,CAAC;AACtE;AAWA,SAASI,OAAOA,CAACC,CAAU;EACzB,OACEA,CAAC,YAAYC,UAAU,IACtBD,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACE,WAAW,CAACC,IAAI,KAAK,YAAa;AAE/E;AAkBA;;;AAGA,SAASC,KAAKA,CAA+B,GAAGC,IAAO;EACrD,MAAMC,EAAE,GAAIN,CAAM,IAAKA,CAAC;EACxB;EACA,MAAMO,IAAI,GAAGA,CAACP,CAAM,EAAEQ,CAAM,KAAMC,CAAM,IAAKT,CAAC,CAACQ,CAAC,CAACC,CAAC,CAAC,CAAC;EACpD;EACA,MAAMC,MAAM,GAAGL,IAAI,CAACM,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACF,MAAM,CAAC,CAACG,WAAW,CAACN,IAAI,EAAED,EAAE,CAAC;EAC9D;EACA,MAAMQ,MAAM,GAAGT,IAAI,CAACM,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACE,MAAM,CAAC,CAACC,MAAM,CAACR,IAAI,EAAED,EAAE,CAAC;EACzD,OAAO;IAAEI,MAAM;IAAEI;EAAM,CAAE;AAC3B;AAIA;;;;AAIA,SAASE,QAAQA,CAACA,QAAkB;EAClC,OAAO;IACLN,MAAM,EAAGO,MAAgB,IAAI;MAC3B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAKA,MAAM,CAACG,MAAM,IAAI,OAAOH,MAAM,CAAC,CAAC,CAAC,KAAK,QAAS,EAC5E,MAAM,IAAInB,KAAK,CAAC,qDAAqD,CAAC;MACxE,OAAOmB,MAAM,CAACN,GAAG,CAAEU,CAAC,IAAI;QACtB3B,YAAY,CAAC2B,CAAC,CAAC;QACf,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIL,QAAQ,CAACI,MAAM,EAC/B,MAAM,IAAItB,KAAK,CAAC,iCAAiCuB,CAAC,eAAeL,QAAQ,CAACI,MAAM,GAAG,CAAC;QACtF,OAAOJ,QAAQ,CAACK,CAAC,CAAE;MACrB,CAAC,CAAC;IACJ,CAAC;IACDP,MAAM,EAAGQ,KAAe,IAAI;MAC1B,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACG,KAAK,CAAC,IAAKA,KAAK,CAACF,MAAM,IAAI,OAAOE,KAAK,CAAC,CAAC,CAAC,KAAK,QAAS,EACzE,MAAM,IAAIxB,KAAK,CAAC,kDAAkD,CAAC;MACrE,OAAOwB,KAAK,CAACX,GAAG,CAAEY,MAAM,IAAI;QAC1B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAC5B,MAAM,IAAIzB,KAAK,CAAC,uCAAuCyB,MAAM,EAAE,CAAC;QAClE,MAAMC,KAAK,GAAGR,QAAQ,CAACS,OAAO,CAACF,MAAM,CAAC;QACtC,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI1B,KAAK,CAAC,oBAAoByB,MAAM,eAAeP,QAAQ,EAAE,CAAC;QACtF,OAAOQ,KAAK;MACd,CAAC,CAAC;IACJ;GACD;AACH;AAEA;;;AAGA,SAASE,IAAIA,CAACC,SAAS,GAAG,EAAE;EAC1B,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE,MAAM,IAAI7B,KAAK,CAAC,iCAAiC,CAAC;EACrF,OAAO;IACLY,MAAM,EAAGkB,IAAI,IAAI;MACf,IAAI,CAACV,KAAK,CAACC,OAAO,CAACS,IAAI,CAAC,IAAKA,IAAI,CAACR,MAAM,IAAI,OAAOQ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAS,EACtE,MAAM,IAAI9B,KAAK,CAAC,8CAA8C,CAAC;MACjE,KAAK,IAAIuB,CAAC,IAAIO,IAAI,EAChB,IAAI,OAAOP,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIvB,KAAK,CAAC,iCAAiCuB,CAAC,EAAE,CAAC;MAClF,OAAOO,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC;IAC7B,CAAC;IACDb,MAAM,EAAGe,EAAE,IAAI;MACb,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,MAAM,IAAI/B,KAAK,CAAC,oCAAoC,CAAC;MACjF,OAAO+B,EAAE,CAACC,KAAK,CAACH,SAAS,CAAC;IAC5B;GACD;AACH;AAEA;;;;AAIA,SAASI,OAAOA,CAACC,IAAY,EAAEC,GAAG,GAAG,GAAG;EACtCvC,YAAY,CAACsC,IAAI,CAAC;EAClB,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAInC,KAAK,CAAC,8BAA8B,CAAC;EAC5E,OAAO;IACLY,MAAMA,CAACwB,IAAc;MACnB,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACe,IAAI,CAAC,IAAKA,IAAI,CAACd,MAAM,IAAI,OAAOc,IAAI,CAAC,CAAC,CAAC,KAAK,QAAS,EACtE,MAAM,IAAIpC,KAAK,CAAC,iDAAiD,CAAC;MACpE,KAAK,IAAIuB,CAAC,IAAIa,IAAI,EAChB,IAAI,OAAOb,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIvB,KAAK,CAAC,oCAAoCuB,CAAC,EAAE,CAAC;MACrF,OAAQa,IAAI,CAACd,MAAM,GAAGY,IAAI,GAAI,CAAC,EAAEE,IAAI,CAACC,IAAI,CAACF,GAAG,CAAC;MAC/C,OAAOC,IAAI;IACb,CAAC;IACDpB,MAAMA,CAACQ,KAAe;MACpB,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACG,KAAK,CAAC,IAAKA,KAAK,CAACF,MAAM,IAAI,OAAOE,KAAK,CAAC,CAAC,CAAC,KAAK,QAAS,EACzE,MAAM,IAAIxB,KAAK,CAAC,iDAAiD,CAAC;MACpE,KAAK,IAAIuB,CAAC,IAAIC,KAAK,EACjB,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIvB,KAAK,CAAC,oCAAoCuB,CAAC,EAAE,CAAC;MACrF,IAAIe,GAAG,GAAGd,KAAK,CAACF,MAAM;MACtB,IAAKgB,GAAG,GAAGJ,IAAI,GAAI,CAAC,EAClB,MAAM,IAAIlC,KAAK,CAAC,2DAA2D,CAAC;MAC9E,OAAOsC,GAAG,GAAG,CAAC,IAAId,KAAK,CAACc,GAAG,GAAG,CAAC,CAAC,KAAKH,GAAG,EAAEG,GAAG,EAAE,EAAE;QAC/C,IAAI,EAAG,CAACA,GAAG,GAAG,CAAC,IAAIJ,IAAI,GAAI,CAAC,CAAC,EAC3B,MAAM,IAAIlC,KAAK,CAAC,8CAA8C,CAAC;MACnE;MACA,OAAOwB,KAAK,CAACe,KAAK,CAAC,CAAC,EAAED,GAAG,CAAC;IAC5B;GACD;AACH;AAEA;;;AAGA,SAASE,SAASA,CAAIC,EAAiB;EACrC,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE,MAAM,IAAIzC,KAAK,CAAC,iCAAiC,CAAC;EAChF,OAAO;IAAEY,MAAM,EAAGkB,IAAO,IAAKA,IAAI;IAAEd,MAAM,EAAGe,EAAK,IAAKU,EAAE,CAACV,EAAE;EAAC,CAAE;AACjE;AAEA;;;;AAIA,SAASW,YAAYA,CAACN,IAAc,EAAEN,IAAY,EAAEC,EAAU;EAC5D;EACA,IAAID,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI9B,KAAK,CAAC,4BAA4B8B,IAAI,8BAA8B,CAAC;EAC7F,IAAIC,EAAE,GAAG,CAAC,EAAE,MAAM,IAAI/B,KAAK,CAAC,0BAA0B+B,EAAE,8BAA8B,CAAC;EACvF,IAAI,CAACX,KAAK,CAACC,OAAO,CAACe,IAAI,CAAC,EAAE,MAAM,IAAIpC,KAAK,CAAC,oCAAoC,CAAC;EAC/E,IAAI,CAACoC,IAAI,CAACd,MAAM,EAAE,OAAO,EAAE;EAC3B,IAAIqB,GAAG,GAAG,CAAC;EACX,MAAMC,GAAG,GAAG,EAAE;EACd,MAAMzB,MAAM,GAAGC,KAAK,CAACU,IAAI,CAACM,IAAI,CAAC;EAC/BjB,MAAM,CAAC0B,OAAO,CAAEC,CAAC,IAAI;IACnBlD,YAAY,CAACkD,CAAC,CAAC;IACf,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIhB,IAAI,EAAE,MAAM,IAAI9B,KAAK,CAAC,kBAAkB8C,CAAC,EAAE,CAAC;EAChE,CAAC,CAAC;EACF,OAAO,IAAI,EAAE;IACX,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,IAAI,GAAG,IAAI;IACf,KAAK,IAAIzB,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,GAAGJ,MAAM,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;MACxC,MAAM0B,KAAK,GAAG9B,MAAM,CAACI,CAAC,CAAE;MACxB,MAAM2B,SAAS,GAAGpB,IAAI,GAAGiB,KAAK,GAAGE,KAAK;MACtC,IACE,CAACnD,MAAM,CAACC,aAAa,CAACmD,SAAS,CAAC,IAC/BpB,IAAI,GAAGiB,KAAK,GAAIjB,IAAI,KAAKiB,KAAK,IAC/BG,SAAS,GAAGD,KAAK,KAAKnB,IAAI,GAAGiB,KAAK,EAClC;QACA,MAAM,IAAI/C,KAAK,CAAC,8BAA8B,CAAC;MACjD;MACA+C,KAAK,GAAGG,SAAS,GAAGnB,EAAE;MACtB,MAAMoB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,GAAGnB,EAAE,CAAC;MAC1CZ,MAAM,CAACI,CAAC,CAAC,GAAG4B,OAAO;MACnB,IAAI,CAACrD,MAAM,CAACC,aAAa,CAACoD,OAAO,CAAC,IAAIA,OAAO,GAAGpB,EAAE,GAAGgB,KAAK,KAAKG,SAAS,EACtE,MAAM,IAAIlD,KAAK,CAAC,8BAA8B,CAAC;MACjD,IAAI,CAACgD,IAAI,EAAE,SAAS,KACf,IAAI,CAACG,OAAO,EAAER,GAAG,GAAGpB,CAAC,CAAC,KACtByB,IAAI,GAAG,KAAK;IACnB;IACAJ,GAAG,CAACP,IAAI,CAACU,KAAK,CAAC;IACf,IAAIC,IAAI,EAAE;EACZ;EACA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,IAAI,CAACd,MAAM,GAAG,CAAC,IAAIc,IAAI,CAACb,CAAC,CAAC,KAAK,CAAC,EAAEA,CAAC,EAAE,EAAEqB,GAAG,CAACP,IAAI,CAAC,CAAC,CAAC;EACtE,OAAOO,GAAG,CAACU,OAAO,EAAE;AACtB;AAEA,MAAMC,GAAG,GAAG,0BAA2BA,CAACrD,CAAS,EAAEQ,CAAS,KAAc,CAACA,CAAC,GAAGR,CAAC,GAAGqD,GAAG,CAAC7C,CAAC,EAAER,CAAC,GAAGQ,CAAC,CAAE;AACjG,MAAM8C,WAAW,GAAG,yBAA0BA,CAAC1B,IAAY,EAAEC,EAAU,KACrED,IAAI,IAAIC,EAAE,GAAGwB,GAAG,CAACzB,IAAI,EAAEC,EAAE,CAAC,CAAC;AAC7B;;;;AAIA,SAAS0B,aAAaA,CAACrB,IAAc,EAAEN,IAAY,EAAEC,EAAU,EAAEE,OAAgB;EAC/E,IAAI,CAACb,KAAK,CAACC,OAAO,CAACe,IAAI,CAAC,EAAE,MAAM,IAAIpC,KAAK,CAAC,qCAAqC,CAAC;EAChF,IAAI8B,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE,MAAM,IAAI9B,KAAK,CAAC,6BAA6B8B,IAAI,EAAE,CAAC;EAChF,IAAIC,EAAE,IAAI,CAAC,IAAIA,EAAE,GAAG,EAAE,EAAE,MAAM,IAAI/B,KAAK,CAAC,2BAA2B+B,EAAE,EAAE,CAAC;EACxE,IAAIyB,WAAW,CAAC1B,IAAI,EAAEC,EAAE,CAAC,GAAG,EAAE,EAAE;IAC9B,MAAM,IAAI/B,KAAK,CACb,sCAAsC8B,IAAI,OAAOC,EAAE,cAAcyB,WAAW,CAAC1B,IAAI,EAAEC,EAAE,CAAC,EAAE,CACzF;EACH;EACA,IAAIgB,KAAK,GAAG,CAAC;EACb,IAAIJ,GAAG,GAAG,CAAC,CAAC,CAAC;EACb,MAAMe,IAAI,GAAG,CAAC,IAAI3B,EAAE,GAAG,CAAC;EACxB,MAAMa,GAAG,GAAa,EAAE;EACxB,KAAK,MAAM/C,CAAC,IAAIuC,IAAI,EAAE;IACpBxC,YAAY,CAACC,CAAC,CAAC;IACf,IAAIA,CAAC,IAAI,CAAC,IAAIiC,IAAI,EAAE,MAAM,IAAI9B,KAAK,CAAC,oCAAoCH,CAAC,SAASiC,IAAI,EAAE,CAAC;IACzFiB,KAAK,GAAIA,KAAK,IAAIjB,IAAI,GAAIjC,CAAC;IAC3B,IAAI8C,GAAG,GAAGb,IAAI,GAAG,EAAE,EAAE,MAAM,IAAI9B,KAAK,CAAC,qCAAqC2C,GAAG,SAASb,IAAI,EAAE,CAAC;IAC7Fa,GAAG,IAAIb,IAAI;IACX,OAAOa,GAAG,IAAIZ,EAAE,EAAEY,GAAG,IAAIZ,EAAE,EAAEa,GAAG,CAACP,IAAI,CAAC,CAAEU,KAAK,IAAKJ,GAAG,GAAGZ,EAAG,GAAI2B,IAAI,MAAM,CAAC,CAAC;IAC3EX,KAAK,IAAI,CAAC,IAAIJ,GAAG,GAAG,CAAC,CAAC,CAAC;EACzB;EACAI,KAAK,GAAIA,KAAK,IAAKhB,EAAE,GAAGY,GAAI,GAAIe,IAAI;EACpC,IAAI,CAACzB,OAAO,IAAIU,GAAG,IAAIb,IAAI,EAAE,MAAM,IAAI9B,KAAK,CAAC,gBAAgB,CAAC;EAC9D,IAAI,CAACiC,OAAO,IAAIc,KAAK,EAAE,MAAM,IAAI/C,KAAK,CAAC,qBAAqB+C,KAAK,EAAE,CAAC;EACpE,IAAId,OAAO,IAAIU,GAAG,GAAG,CAAC,EAAEC,GAAG,CAACP,IAAI,CAACU,KAAK,KAAK,CAAC,CAAC;EAC7C,OAAOH,GAAG;AACZ;AAEA;;;AAGA,SAASe,KAAKA,CAACC,GAAW;EACxBhE,YAAY,CAACgE,GAAG,CAAC;EACjB,OAAO;IACLhD,MAAM,EAAGiD,KAAiB,IAAI;MAC5B,IAAI,CAAC5D,OAAO,CAAC4D,KAAK,CAAC,EAAE,MAAM,IAAI7D,KAAK,CAAC,yCAAyC,CAAC;MAC/E,OAAO0C,YAAY,CAACtB,KAAK,CAACU,IAAI,CAAC+B,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAED,GAAG,CAAC;IACrD,CAAC;IACD5C,MAAM,EAAGG,MAAgB,IAAI;MAC3B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAKA,MAAM,CAACG,MAAM,IAAI,OAAOH,MAAM,CAAC,CAAC,CAAC,KAAK,QAAS,EAC5E,MAAM,IAAInB,KAAK,CAAC,+CAA+C,CAAC;MAClE,OAAOG,UAAU,CAAC2B,IAAI,CAACY,YAAY,CAACvB,MAAM,EAAEyC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D;GACD;AACH;AAEA;;;;;AAKA,SAASE,MAAMA,CAAC5B,IAAY,EAAE6B,UAAU,GAAG,KAAK;EAC9CnE,YAAY,CAACsC,IAAI,CAAC;EAClB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE,MAAM,IAAIlC,KAAK,CAAC,mCAAmC,CAAC;EAChF,IAAIwD,WAAW,CAAC,CAAC,EAAEtB,IAAI,CAAC,GAAG,EAAE,IAAIsB,WAAW,CAACtB,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,EACxD,MAAM,IAAIlC,KAAK,CAAC,wBAAwB,CAAC;EAC3C,OAAO;IACLY,MAAM,EAAGiD,KAAiB,IAAI;MAC5B,IAAI,CAAC5D,OAAO,CAAC4D,KAAK,CAAC,EAAE,MAAM,IAAI7D,KAAK,CAAC,0CAA0C,CAAC;MAChF,OAAOyD,aAAa,CAACrC,KAAK,CAACU,IAAI,CAAC+B,KAAK,CAAC,EAAE,CAAC,EAAE3B,IAAI,EAAE,CAAC6B,UAAU,CAAC;IAC/D,CAAC;IACD/C,MAAM,EAAGG,MAAgB,IAAI;MAC3B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAKA,MAAM,CAACG,MAAM,IAAI,OAAOH,MAAM,CAAC,CAAC,CAAC,KAAK,QAAS,EAC5E,MAAM,IAAInB,KAAK,CAAC,gDAAgD,CAAC;MACnE,OAAOG,UAAU,CAAC2B,IAAI,CAAC2B,aAAa,CAACtC,MAAM,EAAEe,IAAI,EAAE,CAAC,EAAE6B,UAAU,CAAC,CAAC;IACpE;GACD;AACH;AAGA;;;AAGA,SAASC,aAAaA,CAAkCvB,EAAK;EAC3D,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE,MAAM,IAAIzC,KAAK,CAAC,qCAAqC,CAAC;EACpF,OAAO,UAAU,GAAGO,IAAsB;IACxC,IAAI;MACF,OAAOkC,EAAE,CAACwB,KAAK,CAAC,IAAI,EAAE1D,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAO2D,CAAC,EAAE,CAAC;EACf,CAAC;AACH;AAEA;;;AAGA,SAASC,QAAQA,CACfC,GAAW,EACX3B,EAAoC;EAEpC7C,YAAY,CAACwE,GAAG,CAAC;EACjB,IAAI,OAAO3B,EAAE,KAAK,UAAU,EAAE,MAAM,IAAIzC,KAAK,CAAC,gCAAgC,CAAC;EAC/E,OAAO;IACLY,MAAMA,CAACwB,IAAgB;MACrB,IAAI,CAACnC,OAAO,CAACmC,IAAI,CAAC,EAAE,MAAM,IAAIpC,KAAK,CAAC,6CAA6C,CAAC;MAClF,MAAMmE,QAAQ,GAAG1B,EAAE,CAACL,IAAI,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE6B,GAAG,CAAC;MACvC,MAAMxB,GAAG,GAAG,IAAIzC,UAAU,CAACiC,IAAI,CAACd,MAAM,GAAG8C,GAAG,CAAC;MAC7CxB,GAAG,CAACyB,GAAG,CAACjC,IAAI,CAAC;MACbQ,GAAG,CAACyB,GAAG,CAACF,QAAQ,EAAE/B,IAAI,CAACd,MAAM,CAAC;MAC9B,OAAOsB,GAAG;IACZ,CAAC;IACD5B,MAAMA,CAACoB,IAAgB;MACrB,IAAI,CAACnC,OAAO,CAACmC,IAAI,CAAC,EAAE,MAAM,IAAIpC,KAAK,CAAC,6CAA6C,CAAC;MAClF,MAAMsE,OAAO,GAAGlC,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC6B,GAAG,CAAC;MACnC,MAAMG,WAAW,GAAG9B,EAAE,CAAC6B,OAAO,CAAC,CAAC/B,KAAK,CAAC,CAAC,EAAE6B,GAAG,CAAC;MAC7C,MAAMI,WAAW,GAAGpC,IAAI,CAACG,KAAK,CAAC,CAAC6B,GAAG,CAAC;MACpC,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,GAAG,EAAE7C,CAAC,EAAE,EAC1B,IAAIgD,WAAW,CAAChD,CAAC,CAAC,KAAKiD,WAAW,CAACjD,CAAC,CAAC,EAAE,MAAM,IAAIvB,KAAK,CAAC,kBAAkB,CAAC;MAC5E,OAAOsE,OAAO;IAChB;GACD;AACH;AAEA;AACA,OAAO,MAAMG,KAAK,GAAG;EACnBvD,QAAQ;EAAEZ,KAAK;EAAE6D,QAAQ;EAAEzB,YAAY;EAAEe,aAAa;EAAEE,KAAK;EAAEG,MAAM;EAAElC,IAAI;EAAEK;CAC9E;AAED;AACA;AACA,OAAO,MAAMyC,MAAM,GAAe,eAAgBpE,KAAK,CACrDwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kBAAkB,CAAC,EAC5BU,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAM+C,MAAM,GAAe,eAAgBrE,KAAK,CACrDwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kCAAkC,CAAC,EAC5Ce,OAAO,CAAC,CAAC,CAAC,EACVL,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAMgD,WAAW,GAAe,eAAgBtE,KAAK,CAC1DwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kCAAkC,CAAC,EAC5CU,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAMiD,SAAS,GAAe,eAAgBvE,KAAK,CACxDwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kCAAkC,CAAC,EAC5Ce,OAAO,CAAC,CAAC,CAAC,EACVL,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAMkD,cAAc,GAAe,eAAgBxE,KAAK,CAC7DwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kCAAkC,CAAC,EAC5CU,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAMmD,eAAe,GAAe,eAAgBzE,KAAK,CAC9DwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kCAAkC,CAAC,EAC5CU,IAAI,CAAC,EAAE,CAAC,EACRY,SAAS,CAAEwC,CAAS,IAAKA,CAAC,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CACnF;AACD,OAAO,MAAMC,MAAM,GAAe,eAAgB7E,KAAK,CACrDwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kEAAkE,CAAC,EAC5Ee,OAAO,CAAC,CAAC,CAAC,EACVL,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAMwD,WAAW,GAAe,eAAgB9E,KAAK,CAC1DwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kEAAkE,CAAC,EAC5EU,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAMyD,SAAS,GAAe,eAAgB/E,KAAK,CACxDwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kEAAkE,CAAC,EAC5Ee,OAAO,CAAC,CAAC,CAAC,EACVL,IAAI,CAAC,EAAE,CAAC,CACT;AACD,OAAO,MAAM0D,cAAc,GAAe,eAAgBhF,KAAK,CAC7DwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kEAAkE,CAAC,EAC5EU,IAAI,CAAC,EAAE,CAAC,CACT;AAED;AACA;AACA,MAAM2D,SAAS,GAAIC,GAAW,IAAKlF,KAAK,CAACqD,KAAK,CAAC,EAAE,CAAC,EAAEzC,QAAQ,CAACsE,GAAG,CAAC,EAAE5D,IAAI,CAAC,EAAE,CAAC,CAAC;AAE5E,OAAO,MAAM6D,MAAM,GAAe,eAAgBF,SAAS,CACzD,4DAA4D,CAC7D;AACD,OAAO,MAAMG,YAAY,GAAe,eAAgBH,SAAS,CAC/D,4DAA4D,CAC7D;AACD,OAAO,MAAMI,SAAS,GAAe,eAAgBJ,SAAS,CAC5D,4DAA4D,CAC7D;AAED;AACA;AAEA;AACA,MAAMK,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AACnD,OAAO,MAAMC,SAAS,GAAe;EACnCjF,MAAMA,CAACwB,IAAgB;IACrB,IAAIQ,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,IAAI,CAACd,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;MACvC,MAAMuE,KAAK,GAAG1D,IAAI,CAAC2D,QAAQ,CAACxE,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;MACrCqB,GAAG,IAAI6C,MAAM,CAAC7E,MAAM,CAACkF,KAAK,CAAC,CAACE,QAAQ,CAACJ,aAAa,CAACE,KAAK,CAACxE,MAAM,CAAE,EAAE,GAAG,CAAC;IACzE;IACA,OAAOsB,GAAG;EACZ,CAAC;EACD5B,MAAMA,CAACiF,GAAW;IAChB,IAAIrD,GAAG,GAAa,EAAE;IACtB,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0E,GAAG,CAAC3E,MAAM,EAAEC,CAAC,IAAI,EAAE,EAAE;MACvC,MAAMgB,KAAK,GAAG0D,GAAG,CAAC1D,KAAK,CAAChB,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC;MAClC,MAAM2E,QAAQ,GAAGN,aAAa,CAACjE,OAAO,CAACY,KAAK,CAACjB,MAAM,CAAC;MACpD,MAAMwE,KAAK,GAAGL,MAAM,CAACzE,MAAM,CAACuB,KAAK,CAAC;MAClC,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACxE,MAAM,GAAG4E,QAAQ,EAAEC,CAAC,EAAE,EAAE;QAChD,IAAIL,KAAK,CAACK,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM,IAAInG,KAAK,CAAC,0BAA0B,CAAC;MACjE;MACA4C,GAAG,GAAGA,GAAG,CAACwD,MAAM,CAAChF,KAAK,CAACU,IAAI,CAACgE,KAAK,CAACvD,KAAK,CAACuD,KAAK,CAACxE,MAAM,GAAG4E,QAAQ,CAAC,CAAC,CAAC;IACpE;IACA,OAAO/F,UAAU,CAAC2B,IAAI,CAACc,GAAG,CAAC;EAC7B;CACD;AAED,OAAO,MAAMyD,iBAAiB,GAAIC,MAAwC,IACxEhG,KAAK,CACH6D,QAAQ,CAAC,CAAC,EAAG/B,IAAI,IAAKkE,MAAM,CAACA,MAAM,CAAClE,IAAI,CAAC,CAAC,CAAC,EAC3CqD,MAAM,CACP;AACH;AACA,OAAO,MAAMc,WAAW,GAAGF,iBAAiB;AAc5C,MAAMG,aAAa,GAA4B,eAAgBlG,KAAK,CAClEY,QAAQ,CAAC,kCAAkC,CAAC,EAC5CU,IAAI,CAAC,EAAE,CAAC,CACT;AAED,MAAM6E,kBAAkB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;AACvF;;;AAGA,SAASC,aAAaA,CAACC,GAAW;EAChC,MAAMjG,CAAC,GAAGiG,GAAG,IAAI,EAAE;EACnB,IAAIC,GAAG,GAAG,CAACD,GAAG,GAAG,SAAS,KAAK,CAAC;EAChC,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,kBAAkB,CAACnF,MAAM,EAAEC,CAAC,EAAE,EAAE;IAClD,IAAI,CAAEb,CAAC,IAAIa,CAAC,GAAI,CAAC,MAAM,CAAC,EAAEqF,GAAG,IAAIH,kBAAkB,CAAClF,CAAC,CAAE;EACzD;EACA,OAAOqF,GAAG;AACZ;AAEA;;;AAGA,SAASC,YAAYA,CAACC,MAAc,EAAEC,KAAe,EAAEC,aAAa,GAAG,CAAC;EACtE,MAAM5C,GAAG,GAAG0C,MAAM,CAACxF,MAAM;EACzB,IAAIsF,GAAG,GAAG,CAAC;EACX,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,GAAG,EAAE7C,CAAC,EAAE,EAAE;IAC5B,MAAMZ,CAAC,GAAGmG,MAAM,CAACG,UAAU,CAAC1F,CAAC,CAAC;IAC9B,IAAIZ,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,EAAE,MAAM,IAAIX,KAAK,CAAC,mBAAmB8G,MAAM,GAAG,CAAC;IACpEF,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC,GAAIjG,CAAC,IAAI,CAAE;EACrC;EACAiG,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC;EACxB,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,GAAG,EAAE7C,CAAC,EAAE,EAAEqF,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC,GAAIE,MAAM,CAACG,UAAU,CAAC1F,CAAC,CAAC,GAAG,IAAK;EACtF,KAAK,IAAI2F,CAAC,IAAIH,KAAK,EAAEH,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC,GAAGM,CAAC;EACjD,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAEqF,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC;EACpDA,GAAG,IAAII,aAAa;EACpB,OAAOR,aAAa,CAAC5F,MAAM,CAAC6C,aAAa,CAAC,CAACmD,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3E;AAmBA;;;AAGA,SAASO,SAASA,CAACC,QAA8B;EAC/C,MAAMC,cAAc,GAAGD,QAAQ,KAAK,QAAQ,GAAG,CAAC,GAAG,UAAU;EAC7D,MAAME,MAAM,GAAGxD,MAAM,CAAC,CAAC,CAAC;EACxB,MAAMyD,SAAS,GAAGD,MAAM,CAACtG,MAAM;EAC/B,MAAMwG,OAAO,GAAGF,MAAM,CAAC1G,MAAM;EAC7B,MAAM6G,eAAe,GAAGzD,aAAa,CAACuD,SAAS,CAAC;EAEhD,SAAS3G,MAAMA,CACbkG,MAAc,EACdC,KAA4B,EAC5BW,KAAA,GAAwB,EAAE;IAE1B,IAAI,OAAOZ,MAAM,KAAK,QAAQ,EAC5B,MAAM,IAAI9G,KAAK,CAAC,8CAA8C,OAAO8G,MAAM,EAAE,CAAC;IAChF,IAAIC,KAAK,YAAY5G,UAAU,EAAE4G,KAAK,GAAG3F,KAAK,CAACU,IAAI,CAACiF,KAAK,CAAC;IAC1D,IAAI,CAAC3F,KAAK,CAACC,OAAO,CAAC0F,KAAK,CAAC,IAAKA,KAAK,CAACzF,MAAM,IAAI,OAAOyF,KAAK,CAAC,CAAC,CAAC,KAAK,QAAS,EACzE,MAAM,IAAI/G,KAAK,CAAC,uDAAuD,OAAO+G,KAAK,EAAE,CAAC;IACxF,IAAID,MAAM,CAACxF,MAAM,KAAK,CAAC,EAAE,MAAM,IAAIqG,SAAS,CAAC,yBAAyBb,MAAM,CAACxF,MAAM,EAAE,CAAC;IACtF,MAAMsG,YAAY,GAAGd,MAAM,CAACxF,MAAM,GAAG,CAAC,GAAGyF,KAAK,CAACzF,MAAM;IACrD,IAAIoG,KAAK,KAAK,KAAK,IAAIE,YAAY,GAAGF,KAAK,EACzC,MAAM,IAAIC,SAAS,CAAC,UAAUC,YAAY,kBAAkBF,KAAK,EAAE,CAAC;IACtE,MAAMG,OAAO,GAAGf,MAAM,CAACgB,WAAW,EAAE;IACpC,MAAMC,GAAG,GAAGlB,YAAY,CAACgB,OAAO,EAAEd,KAAK,EAAEM,cAAc,CAAC;IACxD,OAAO,GAAGQ,OAAO,IAAIrB,aAAa,CAAC5F,MAAM,CAACmG,KAAK,CAAC,GAAGgB,GAAG,EAAsC;EAC9F;EAOA,SAAS/G,MAAMA,CAACiF,GAAW,EAAEyB,KAAA,GAAwB,EAAE;IACrD,IAAI,OAAOzB,GAAG,KAAK,QAAQ,EACzB,MAAM,IAAIjG,KAAK,CAAC,6CAA6C,OAAOiG,GAAG,EAAE,CAAC;IAC5E,IAAIA,GAAG,CAAC3E,MAAM,GAAG,CAAC,IAAKoG,KAAK,KAAK,KAAK,IAAIzB,GAAG,CAAC3E,MAAM,GAAGoG,KAAM,EAC3D,MAAM,IAAIC,SAAS,CAAC,wBAAwB1B,GAAG,CAAC3E,MAAM,KAAK2E,GAAG,mBAAmByB,KAAK,GAAG,CAAC;IAC5F;IACA,MAAMG,OAAO,GAAG5B,GAAG,CAAC6B,WAAW,EAAE;IACjC,IAAI7B,GAAG,KAAK4B,OAAO,IAAI5B,GAAG,KAAKA,GAAG,CAAChB,WAAW,EAAE,EAC9C,MAAM,IAAIjF,KAAK,CAAC,uCAAuC,CAAC;IAC1D,MAAMgI,QAAQ,GAAGH,OAAO,CAACI,WAAW,CAAC,GAAG,CAAC;IACzC,IAAID,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,CAAC,EACnC,MAAM,IAAIhI,KAAK,CAAC,yDAAyD,CAAC;IAC5E,MAAM8G,MAAM,GAAGe,OAAO,CAACtF,KAAK,CAAC,CAAC,EAAEyF,QAAQ,CAAC;IACzC,MAAM5F,IAAI,GAAGyF,OAAO,CAACtF,KAAK,CAACyF,QAAQ,GAAG,CAAC,CAAC;IACxC,IAAI5F,IAAI,CAACd,MAAM,GAAG,CAAC,EAAE,MAAM,IAAItB,KAAK,CAAC,yCAAyC,CAAC;IAC/E,MAAM+G,KAAK,GAAGP,aAAa,CAACxF,MAAM,CAACoB,IAAI,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrD,MAAMwF,GAAG,GAAGlB,YAAY,CAACC,MAAM,EAAEC,KAAK,EAAEM,cAAc,CAAC;IACvD,IAAI,CAACjF,IAAI,CAAC8F,QAAQ,CAACH,GAAG,CAAC,EAAE,MAAM,IAAI/H,KAAK,CAAC,uBAAuBiG,GAAG,eAAe8B,GAAG,GAAG,CAAC;IACzF,OAAO;MAAEjB,MAAM;MAAEC;IAAK,CAAE;EAC1B;EAEA,MAAMoB,YAAY,GAAGnE,aAAa,CAAChD,MAAM,CAAC;EAE1C,SAASoH,aAAaA,CAACnC,GAAW;IAChC,MAAM;MAAEa,MAAM;MAAEC;IAAK,CAAE,GAAG/F,MAAM,CAACiF,GAAG,EAAE,KAAK,CAAC;IAC5C,OAAO;MAAEa,MAAM;MAAEC,KAAK;MAAElD,KAAK,EAAE0D,SAAS,CAACR,KAAK;IAAC,CAAE;EACnD;EAEA,SAASsB,eAAeA,CAACvB,MAAc,EAAEjD,KAAiB;IACxD,OAAOjD,MAAM,CAACkG,MAAM,EAAEU,OAAO,CAAC3D,KAAK,CAAC,CAAC;EACvC;EAEA,OAAO;IACLjD,MAAM;IACNI,MAAM;IACNqH,eAAe;IACfD,aAAa;IACbD,YAAY;IACZZ,SAAS;IACTE,eAAe;IACfD;GACD;AACH;AAEA,OAAO,MAAMc,MAAM,GAAW,eAAgBnB,SAAS,CAAC,QAAQ,CAAC;AACjE,OAAO,MAAMoB,OAAO,GAAW,eAAgBpB,SAAS,CAAC,SAAS,CAAC;AAKnE,OAAO,MAAMqB,IAAI,GAAe;EAC9B5H,MAAM,EAAGwB,IAAI,IAAK,IAAIqG,WAAW,EAAE,CAACzH,MAAM,CAACoB,IAAI,CAAC;EAChDpB,MAAM,EAAGiF,GAAG,IAAK,IAAIyC,WAAW,EAAE,CAAC9H,MAAM,CAACqF,GAAG;CAC9C;AAED,OAAO,MAAM0C,GAAG,GAAe,eAAgBrI,KAAK,CAClDwD,MAAM,CAAC,CAAC,CAAC,EACT5C,QAAQ,CAAC,kBAAkB,CAAC,EAC5BU,IAAI,CAAC,EAAE,CAAC,EACRY,SAAS,CAAEwC,CAAS,IAAI;EACtB,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAAC1D,MAAM,GAAG,CAAC,EACvC,MAAM,IAAIqG,SAAS,CAAC,oCAAoC,OAAO3C,CAAC,gBAAgBA,CAAC,CAAC1D,MAAM,EAAE,CAAC;EAC7F,OAAO0D,CAAC,CAAC8C,WAAW,EAAE;AACxB,CAAC,CAAC,CACH;AAED;AACA,MAAMc,MAAM,GAAG;EACbJ,IAAI;EAAEG,GAAG;EAAEjE,MAAM;EAAEC,MAAM;EAAEQ,MAAM;EAAEE,SAAS;EAAEI,MAAM;EAAEI;CACvD;AAED,MAAMgD,cAAc,GAClB,yGAAyG;AAE3G,OAAO,MAAMC,aAAa,GAAGA,CAACC,IAAe,EAAElF,KAAiB,KAAY;EAC1E,IAAI,OAAOkF,IAAI,KAAK,QAAQ,IAAI,CAACH,MAAM,CAACI,cAAc,CAACD,IAAI,CAAC,EAAE,MAAM,IAAIpB,SAAS,CAACkB,cAAc,CAAC;EACjG,IAAI,CAAC5I,OAAO,CAAC4D,KAAK,CAAC,EAAE,MAAM,IAAI8D,SAAS,CAAC,oCAAoC,CAAC;EAC9E,OAAOiB,MAAM,CAACG,IAAI,CAAC,CAACnI,MAAM,CAACiD,KAAK,CAAC;AACnC,CAAC;AACD,OAAO,MAAMoC,GAAG,GAAG6C,aAAa,CAAC,CAAC;AAElC,OAAO,MAAMG,aAAa,GAAGA,CAACF,IAAe,EAAE9C,GAAW,KAAgB;EACxE,IAAI,CAAC2C,MAAM,CAACI,cAAc,CAACD,IAAI,CAAC,EAAE,MAAM,IAAIpB,SAAS,CAACkB,cAAc,CAAC;EACrE,IAAI,OAAO5C,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAI0B,SAAS,CAAC,gCAAgC,CAAC;EAClF,OAAOiB,MAAM,CAACG,IAAI,CAAC,CAAC/H,MAAM,CAACiF,GAAG,CAAC;AACjC,CAAC;AACD,OAAO,MAAMpC,KAAK,GAAGoF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}