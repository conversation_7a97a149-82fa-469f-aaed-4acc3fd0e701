{"ast": null, "code": "import { a as s } from \"./chunk-YUNDX5I7.mjs\";\nimport { b as i } from \"./chunk-BF46IXHH.mjs\";\nimport { a as d } from \"./chunk-A63SMUOU.mjs\";\nvar o = class t extends d {\n  constructor(e, r) {\n    super(), this.address = e, this.name = r;\n  }\n  static fromStr(e) {\n    let r = e.split(\"::\");\n    if (r.length !== 2) throw new Error(\"Invalid module id.\");\n    return new t(i.fromString(r[0]), new s(r[1]));\n  }\n  serialize(e) {\n    this.address.serialize(e), this.name.serialize(e);\n  }\n  static deserialize(e) {\n    let r = i.deserialize(e),\n      n = s.deserialize(e);\n    return new t(r, n);\n  }\n};\nexport { o as a };", "map": {"version": 3, "names": ["o", "t", "d", "constructor", "e", "r", "address", "name", "fromStr", "split", "length", "Error", "i", "fromString", "s", "serialize", "deserialize", "n", "a"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\instances\\moduleId.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { AccountAddress } from \"../../core\";\nimport { Identifier } from \"./identifier\";\nimport { MoveModuleId } from \"../../types\";\n\n/**\n * Representation of a ModuleId that can serialized and deserialized\n * ModuleId means the module address (e.g \"0x1\") and the module name (e.g \"coin\")\n */\nexport class ModuleId extends Serializable {\n  public readonly address: AccountAddress;\n\n  public readonly name: Identifier;\n\n  /**\n   * Full name of a module.\n   * @param address The account address. e.g \"0x1\"\n   * @param name The module name under the \"address\". e.g \"coin\"\n   */\n  constructor(address: AccountAddress, name: Identifier) {\n    super();\n    this.address = address;\n    this.name = name;\n  }\n\n  /**\n   * Converts a string literal to a ModuleId\n   * @param moduleId String literal in format \"account_address::module_name\", e.g. \"0x1::coin\"\n   * @returns ModuleId\n   */\n  static fromStr(moduleId: MoveModuleId): ModuleId {\n    const parts = moduleId.split(\"::\");\n    if (parts.length !== 2) {\n      throw new Error(\"Invalid module id.\");\n    }\n    return new ModuleId(AccountAddress.fromString(parts[0]), new Identifier(parts[1]));\n  }\n\n  serialize(serializer: Serializer): void {\n    this.address.serialize(serializer);\n    this.name.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): ModuleId {\n    const address = AccountAddress.deserialize(deserializer);\n    const name = Identifier.deserialize(deserializer);\n    return new ModuleId(address, name);\n  }\n}\n"], "mappings": ";;;AAaO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAiBC,CAAa;EAUzCC,YAAYC,CAAA,EAAyBC,CAAA,EAAkB;IACrD,MAAM,GACN,KAAKC,OAAA,GAAUF,CAAA,EACf,KAAKG,IAAA,GAAOF,CACd;EAAA;EAOA,OAAOG,QAAQJ,CAAA,EAAkC;IAC/C,IAAMC,CAAA,GAAQD,CAAA,CAASK,KAAA,CAAM,IAAI;IACjC,IAAIJ,CAAA,CAAMK,MAAA,KAAW,GACnB,MAAM,IAAIC,KAAA,CAAM,oBAAoB;IAEtC,OAAO,IAAIV,CAAA,CAASW,CAAA,CAAeC,UAAA,CAAWR,CAAA,CAAM,CAAC,CAAC,GAAG,IAAIS,CAAA,CAAWT,CAAA,CAAM,CAAC,CAAC,CAAC,CACnF;EAAA;EAEAU,UAAUX,CAAA,EAA8B;IACtC,KAAKE,OAAA,CAAQS,SAAA,CAAUX,CAAU,GACjC,KAAKG,IAAA,CAAKQ,SAAA,CAAUX,CAAU,CAChC;EAAA;EAEA,OAAOY,YAAYZ,CAAA,EAAsC;IACvD,IAAMC,CAAA,GAAUO,CAAA,CAAeI,WAAA,CAAYZ,CAAY;MACjDa,CAAA,GAAOH,CAAA,CAAWE,WAAA,CAAYZ,CAAY;IAChD,OAAO,IAAIH,CAAA,CAASI,CAAA,EAASY,CAAI,CACnC;EAAA;AACF;AAAA,SAAAjB,CAAA,IAAAkB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}