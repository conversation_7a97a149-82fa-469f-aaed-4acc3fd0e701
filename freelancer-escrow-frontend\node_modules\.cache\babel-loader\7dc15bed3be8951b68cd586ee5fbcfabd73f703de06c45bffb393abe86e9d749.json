{"ast": null, "code": "\"use strict\";\n\nconst F = BigInt('21888242871839275222246405745257275088548364400416034343698204186575808495617');\n\n// Parameters are generated by a reference script https://extgit.iaik.tugraz.at/krypto/hadeshash/-/blob/master/code/generate_parameters_grain.sage\n// Used like so: sage generate_parameters_grain.sage 1 0 254 2 8 56 0x30644e72e131a029b85045b68181585d2833e84879b9709143e1f593f0000001\n\n// Using recommended parameters from whitepaper https://eprint.iacr.org/2019/458.pdf (table 2, table 8)\n// Generated by https://extgit.iaik.tugraz.at/krypto/hadeshash/-/blob/master/code/calc_round_numbers.py\n// And rounded up to nearest integer that divides by t\nconst N_ROUNDS_F = 8;\nconst N_ROUNDS_P = [56, 57, 56, 60, 60, 63, 64, 63, 60, 66, 60, 65, 70, 60, 64, 68];\nconst pow5 = v => {\n  let o = v * v;\n  return v * o * o % F;\n};\nfunction mix(state, M) {\n  const out = [];\n  for (let x = 0; x < state.length; x++) {\n    let o = 0n;\n    for (let y = 0; y < state.length; y++) {\n      o = o + M[x][y] * state[y];\n    }\n    out.push(o % F);\n  }\n  return out;\n}\nfunction poseidon(_inputs, opt) {\n  const inputs = _inputs.map(i => BigInt(i));\n  if (inputs.length <= 0) {\n    throw new Error('poseidon-lite: Not enough inputs');\n  }\n  if (inputs.length > N_ROUNDS_P.length) {\n    throw new Error('poseidon-lite: Too many inputs');\n  }\n  const t = inputs.length + 1;\n  const nRoundsF = N_ROUNDS_F;\n  const nRoundsP = N_ROUNDS_P[t - 2];\n  const {\n    C,\n    M\n  } = opt;\n  if (M.length !== t) {\n    throw new Error(`poseidon-lite: Incorrect M length, expected ${t} got ${M.length}`);\n  }\n  let state = [0n, ...inputs];\n  for (let x = 0; x < nRoundsF + nRoundsP; x++) {\n    for (let y = 0; y < state.length; y++) {\n      state[y] = state[y] + C[x * t + y];\n      if (x < nRoundsF / 2 || x >= nRoundsF / 2 + nRoundsP) state[y] = pow5(state[y]);else if (y === 0) state[y] = pow5(state[y]);\n    }\n    state = mix(state, M);\n  }\n  return state[0];\n}\nmodule.exports = poseidon;", "map": {"version": 3, "names": ["F", "BigInt", "N_ROUNDS_F", "N_ROUNDS_P", "pow5", "v", "o", "mix", "state", "M", "out", "x", "length", "y", "push", "poseidon", "_inputs", "opt", "inputs", "map", "i", "Error", "t", "nRoundsF", "nRoundsP", "C", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Freelancer-Platform-on-Aptos--DAPPS-main/freelancer-escrow-frontend/node_modules/poseidon-lite/poseidon/index.js"], "sourcesContent": ["\"use strict\";\n\nconst F = BigInt('21888242871839275222246405745257275088548364400416034343698204186575808495617');\n\n// Parameters are generated by a reference script https://extgit.iaik.tugraz.at/krypto/hadeshash/-/blob/master/code/generate_parameters_grain.sage\n// Used like so: sage generate_parameters_grain.sage 1 0 254 2 8 56 0x30644e72e131a029b85045b68181585d2833e84879b9709143e1f593f0000001\n\n// Using recommended parameters from whitepaper https://eprint.iacr.org/2019/458.pdf (table 2, table 8)\n// Generated by https://extgit.iaik.tugraz.at/krypto/hadeshash/-/blob/master/code/calc_round_numbers.py\n// And rounded up to nearest integer that divides by t\nconst N_ROUNDS_F = 8;\nconst N_ROUNDS_P = [56, 57, 56, 60, 60, 63, 64, 63, 60, 66, 60, 65, 70, 60, 64, 68];\nconst pow5 = v => {\n  let o = v * v;\n  return v * o * o % F;\n};\nfunction mix(state, M) {\n  const out = [];\n  for (let x = 0; x < state.length; x++) {\n    let o = 0n;\n    for (let y = 0; y < state.length; y++) {\n      o = o + M[x][y] * state[y];\n    }\n    out.push(o % F);\n  }\n  return out;\n}\nfunction poseidon(_inputs, opt) {\n  const inputs = _inputs.map(i => BigInt(i));\n  if (inputs.length <= 0) {\n    throw new Error('poseidon-lite: Not enough inputs');\n  }\n  if (inputs.length > N_ROUNDS_P.length) {\n    throw new Error('poseidon-lite: Too many inputs');\n  }\n  const t = inputs.length + 1;\n  const nRoundsF = N_ROUNDS_F;\n  const nRoundsP = N_ROUNDS_P[t - 2];\n  const {\n    C,\n    M\n  } = opt;\n  if (M.length !== t) {\n    throw new Error(`poseidon-lite: Incorrect M length, expected ${t} got ${M.length}`);\n  }\n  let state = [0n, ...inputs];\n  for (let x = 0; x < nRoundsF + nRoundsP; x++) {\n    for (let y = 0; y < state.length; y++) {\n      state[y] = state[y] + C[x * t + y];\n      if (x < nRoundsF / 2 || x >= nRoundsF / 2 + nRoundsP) state[y] = pow5(state[y]);else if (y === 0) state[y] = pow5(state[y]);\n    }\n    state = mix(state, M);\n  }\n  return state[0];\n}\nmodule.exports = poseidon;"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,CAAC,GAAGC,MAAM,CAAC,+EAA+E,CAAC;;AAEjG;AACA;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnF,MAAMC,IAAI,GAAGC,CAAC,IAAI;EAChB,IAAIC,CAAC,GAAGD,CAAC,GAAGA,CAAC;EACb,OAAOA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGN,CAAC;AACtB,CAAC;AACD,SAASO,GAAGA,CAACC,KAAK,EAAEC,CAAC,EAAE;EACrB,MAAMC,GAAG,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIL,CAAC,GAAG,EAAE;IACV,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrCP,CAAC,GAAGA,CAAC,GAAGG,CAAC,CAACE,CAAC,CAAC,CAACE,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,CAAC;IAC5B;IACAH,GAAG,CAACI,IAAI,CAACR,CAAC,GAAGN,CAAC,CAAC;EACjB;EACA,OAAOU,GAAG;AACZ;AACA,SAASK,QAAQA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC9B,MAAMC,MAAM,GAAGF,OAAO,CAACG,GAAG,CAACC,CAAC,IAAInB,MAAM,CAACmB,CAAC,CAAC,CAAC;EAC1C,IAAIF,MAAM,CAACN,MAAM,IAAI,CAAC,EAAE;IACtB,MAAM,IAAIS,KAAK,CAAC,kCAAkC,CAAC;EACrD;EACA,IAAIH,MAAM,CAACN,MAAM,GAAGT,UAAU,CAACS,MAAM,EAAE;IACrC,MAAM,IAAIS,KAAK,CAAC,gCAAgC,CAAC;EACnD;EACA,MAAMC,CAAC,GAAGJ,MAAM,CAACN,MAAM,GAAG,CAAC;EAC3B,MAAMW,QAAQ,GAAGrB,UAAU;EAC3B,MAAMsB,QAAQ,GAAGrB,UAAU,CAACmB,CAAC,GAAG,CAAC,CAAC;EAClC,MAAM;IACJG,CAAC;IACDhB;EACF,CAAC,GAAGQ,GAAG;EACP,IAAIR,CAAC,CAACG,MAAM,KAAKU,CAAC,EAAE;IAClB,MAAM,IAAID,KAAK,CAAC,+CAA+CC,CAAC,QAAQb,CAAC,CAACG,MAAM,EAAE,CAAC;EACrF;EACA,IAAIJ,KAAK,GAAG,CAAC,EAAE,EAAE,GAAGU,MAAM,CAAC;EAC3B,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,QAAQ,GAAGC,QAAQ,EAAEb,CAAC,EAAE,EAAE;IAC5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrCL,KAAK,CAACK,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,CAAC,GAAGY,CAAC,CAACd,CAAC,GAAGW,CAAC,GAAGT,CAAC,CAAC;MAClC,IAAIF,CAAC,GAAGY,QAAQ,GAAG,CAAC,IAAIZ,CAAC,IAAIY,QAAQ,GAAG,CAAC,GAAGC,QAAQ,EAAEhB,KAAK,CAACK,CAAC,CAAC,GAAGT,IAAI,CAACI,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAIA,CAAC,KAAK,CAAC,EAAEL,KAAK,CAACK,CAAC,CAAC,GAAGT,IAAI,CAACI,KAAK,CAACK,CAAC,CAAC,CAAC;IAC7H;IACAL,KAAK,GAAGD,GAAG,CAACC,KAAK,EAAEC,CAAC,CAAC;EACvB;EACA,OAAOD,KAAK,CAAC,CAAC,CAAC;AACjB;AACAkB,MAAM,CAACC,OAAO,GAAGZ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}