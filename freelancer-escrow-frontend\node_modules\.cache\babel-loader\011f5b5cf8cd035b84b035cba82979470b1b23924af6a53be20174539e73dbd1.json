{"ast": null, "code": "export * from './bytes.js';\nexport * from './identifier.js';\nexport * from './wallet.js';\nexport * from './window.js';", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\base\\src\\index.ts"], "sourcesContent": ["export * from './bytes.js';\nexport * from './identifier.js';\nexport * from './wallet.js';\nexport * from './window.js';\n"], "mappings": "AAAA,cAAc,YAAY;AAC1B,cAAc,iBAAiB;AAC/B,cAAc,aAAa;AAC3B,cAAc,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}