{"ast": null, "code": "import { a as s, c as u, e as t } from \"./chunk-A63SMUOU.mjs\";\nimport { a, b as l, c as n, d as o, e as c, f as z } from \"./chunk-56CNRT2K.mjs\";\nvar d = class i extends s {\n    constructor(e) {\n      super(), u(e), this.value = e;\n    }\n    serialize(e) {\n      e.serializeBool(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(5), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeBool());\n    }\n  },\n  U = class i extends s {\n    constructor(e) {\n      super(), t(e, 0, a), this.value = e;\n    }\n    serialize(e) {\n      e.serializeU8(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(0), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeU8());\n    }\n  },\n  p = class i extends s {\n    constructor(e) {\n      super(), t(e, 0, l), this.value = e;\n    }\n    serialize(e) {\n      e.serializeU16(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(6), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeU16());\n    }\n  },\n  b = class i extends s {\n    constructor(e) {\n      super(), t(e, 0, n), this.value = e;\n    }\n    serialize(e) {\n      e.serializeU32(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(7), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeU32());\n    }\n  },\n  y = class i extends s {\n    constructor(e) {\n      super(), t(e, BigInt(0), o), this.value = BigInt(e);\n    }\n    serialize(e) {\n      e.serializeU64(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(1), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeU64());\n    }\n  },\n  m = class i extends s {\n    constructor(e) {\n      super(), t(e, BigInt(0), c), this.value = BigInt(e);\n    }\n    serialize(e) {\n      e.serializeU128(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(2), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeU128());\n    }\n  },\n  B = class i extends s {\n    constructor(e) {\n      super(), t(e, BigInt(0), z), this.value = BigInt(e);\n    }\n    serialize(e) {\n      e.serializeU256(this.value);\n    }\n    serializeForEntryFunction(e) {\n      let r = this.bcsToBytes();\n      e.serializeBytes(r);\n    }\n    serializeForScriptFunction(e) {\n      e.serializeU32AsUleb128(8), e.serialize(this);\n    }\n    static deserialize(e) {\n      return new i(e.deserializeU256());\n    }\n  };\nexport { d as a, U as b, p as c, b as d, y as e, m as f, B as g };", "map": {"version": 3, "names": ["d", "i", "s", "constructor", "e", "u", "value", "serialize", "serializeBool", "serializeForEntryFunction", "r", "bcsToBytes", "serializeBytes", "serializeForScriptFunction", "serializeU32AsUleb128", "deserialize", "deserializeBool", "U", "t", "a", "serializeU8", "deserializeU8", "p", "l", "serializeU16", "deserializeU16", "b", "n", "serializeU32", "deserializeU32", "y", "BigInt", "o", "serializeU64", "deserializeU64", "m", "c", "serializeU128", "deserializeU128", "B", "z", "serializeU256", "deserializeU256", "f", "g"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\bcs\\serializable\\movePrimitives.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport {\n  MAX_U128_BIG_INT,\n  MAX_U16_NUMBER,\n  MAX_U32_NUMBER,\n  MAX_U64_BIG_INT,\n  MAX_U8_NUMBER,\n  MAX_U256_BIG_INT,\n} from \"../consts\";\nimport { Deserializer } from \"../deserializer\";\nimport { Serializable, Serializer, ensureBoolean, validateNumberInRange } from \"../serializer\";\nimport { TransactionArgument } from \"../../transactions/instances/transactionArgument\";\nimport { AnyNumber, Uint16, Uint32, Uint8, ScriptTransactionArgumentVariants } from \"../../types\";\n\nexport class Bool extends Serializable implements TransactionArgument {\n  public readonly value: boolean;\n\n  constructor(value: boolean) {\n    super();\n    ensureBoolean(value);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBool(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.Bool);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): Bool {\n    return new Bool(deserializer.deserializeBool());\n  }\n}\n\nexport class U8 extends Serializable implements TransactionArgument {\n  public readonly value: Uint8;\n\n  constructor(value: Uint8) {\n    super();\n    validateNumberInRange(value, 0, MAX_U8_NUMBER);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU8(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U8);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U8 {\n    return new U8(deserializer.deserializeU8());\n  }\n}\n\nexport class U16 extends Serializable implements TransactionArgument {\n  public readonly value: Uint16;\n\n  constructor(value: Uint16) {\n    super();\n    validateNumberInRange(value, 0, MAX_U16_NUMBER);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU16(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U16);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U16 {\n    return new U16(deserializer.deserializeU16());\n  }\n}\n\nexport class U32 extends Serializable implements TransactionArgument {\n  public readonly value: Uint32;\n\n  constructor(value: Uint32) {\n    super();\n    validateNumberInRange(value, 0, MAX_U32_NUMBER);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U32);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U32 {\n    return new U32(deserializer.deserializeU32());\n  }\n}\n\nexport class U64 extends Serializable implements TransactionArgument {\n  public readonly value: bigint;\n\n  constructor(value: AnyNumber) {\n    super();\n    validateNumberInRange(value, BigInt(0), MAX_U64_BIG_INT);\n    this.value = BigInt(value);\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU64(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U64);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U64 {\n    return new U64(deserializer.deserializeU64());\n  }\n}\n\nexport class U128 extends Serializable implements TransactionArgument {\n  public readonly value: bigint;\n\n  constructor(value: AnyNumber) {\n    super();\n    validateNumberInRange(value, BigInt(0), MAX_U128_BIG_INT);\n    this.value = BigInt(value);\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU128(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U128);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U128 {\n    return new U128(deserializer.deserializeU128());\n  }\n}\n\nexport class U256 extends Serializable implements TransactionArgument {\n  public readonly value: bigint;\n\n  constructor(value: AnyNumber) {\n    super();\n    validateNumberInRange(value, BigInt(0), MAX_U256_BIG_INT);\n    this.value = BigInt(value);\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU256(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U256);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U256 {\n    return new U256(deserializer.deserializeU256());\n  }\n}\n"], "mappings": ";;AAgBO,IAAMA,CAAA,GAAN,MAAMC,CAAA,SAAaC,CAA4C;IAGpEC,YAAYC,CAAA,EAAgB;MAC1B,MAAM,GACNC,CAAA,CAAcD,CAAK,GACnB,KAAKE,KAAA,GAAQF,CACf;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAWI,aAAA,CAAc,KAAKF,KAAK,CACrC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA4D,GACvEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAkC;MACnD,OAAO,IAAIH,CAAA,CAAKG,CAAA,CAAaY,eAAA,CAAgB,CAAC,CAChD;IAAA;EACF;EAEaC,CAAA,GAAN,MAAMhB,CAAA,SAAWC,CAA4C;IAGlEC,YAAYC,CAAA,EAAc;MACxB,MAAM,GACNc,CAAA,CAAsBd,CAAA,EAAO,GAAGe,CAAa,GAC7C,KAAKb,KAAA,GAAQF,CACf;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAWgB,WAAA,CAAY,KAAKd,KAAK,CACnC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA0D,GACrEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAgC;MACjD,OAAO,IAAIH,CAAA,CAAGG,CAAA,CAAaiB,aAAA,CAAc,CAAC,CAC5C;IAAA;EACF;EAEaC,CAAA,GAAN,MAAMrB,CAAA,SAAYC,CAA4C;IAGnEC,YAAYC,CAAA,EAAe;MACzB,MAAM,GACNc,CAAA,CAAsBd,CAAA,EAAO,GAAGmB,CAAc,GAC9C,KAAKjB,KAAA,GAAQF,CACf;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAWoB,YAAA,CAAa,KAAKlB,KAAK,CACpC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA2D,GACtEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAiC;MAClD,OAAO,IAAIH,CAAA,CAAIG,CAAA,CAAaqB,cAAA,CAAe,CAAC,CAC9C;IAAA;EACF;EAEaC,CAAA,GAAN,MAAMzB,CAAA,SAAYC,CAA4C;IAGnEC,YAAYC,CAAA,EAAe;MACzB,MAAM,GACNc,CAAA,CAAsBd,CAAA,EAAO,GAAGuB,CAAc,GAC9C,KAAKrB,KAAA,GAAQF,CACf;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAWwB,YAAA,CAAa,KAAKtB,KAAK,CACpC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA2D,GACtEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAiC;MAClD,OAAO,IAAIH,CAAA,CAAIG,CAAA,CAAayB,cAAA,CAAe,CAAC,CAC9C;IAAA;EACF;EAEaC,CAAA,GAAN,MAAM7B,CAAA,SAAYC,CAA4C;IAGnEC,YAAYC,CAAA,EAAkB;MAC5B,MAAM,GACNc,CAAA,CAAsBd,CAAA,EAAO2B,MAAA,CAAO,CAAC,GAAGC,CAAe,GACvD,KAAK1B,KAAA,GAAQyB,MAAA,CAAO3B,CAAK,CAC3B;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAW6B,YAAA,CAAa,KAAK3B,KAAK,CACpC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA2D,GACtEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAiC;MAClD,OAAO,IAAIH,CAAA,CAAIG,CAAA,CAAa8B,cAAA,CAAe,CAAC,CAC9C;IAAA;EACF;EAEaC,CAAA,GAAN,MAAMlC,CAAA,SAAaC,CAA4C;IAGpEC,YAAYC,CAAA,EAAkB;MAC5B,MAAM,GACNc,CAAA,CAAsBd,CAAA,EAAO2B,MAAA,CAAO,CAAC,GAAGK,CAAgB,GACxD,KAAK9B,KAAA,GAAQyB,MAAA,CAAO3B,CAAK,CAC3B;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAWiC,aAAA,CAAc,KAAK/B,KAAK,CACrC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA4D,GACvEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAkC;MACnD,OAAO,IAAIH,CAAA,CAAKG,CAAA,CAAakC,eAAA,CAAgB,CAAC,CAChD;IAAA;EACF;EAEaC,CAAA,GAAN,MAAMtC,CAAA,SAAaC,CAA4C;IAGpEC,YAAYC,CAAA,EAAkB;MAC5B,MAAM,GACNc,CAAA,CAAsBd,CAAA,EAAO2B,MAAA,CAAO,CAAC,GAAGS,CAAgB,GACxD,KAAKlC,KAAA,GAAQyB,MAAA,CAAO3B,CAAK,CAC3B;IAAA;IAEAG,UAAUH,CAAA,EAA8B;MACtCA,CAAA,CAAWqC,aAAA,CAAc,KAAKnC,KAAK,CACrC;IAAA;IAEAG,0BAA0BL,CAAA,EAA8B;MACtD,IAAMM,CAAA,GAAW,KAAKC,UAAA,CAAW;MACjCP,CAAA,CAAWQ,cAAA,CAAeF,CAAQ,CACpC;IAAA;IAEAG,2BAA2BT,CAAA,EAA8B;MACvDA,CAAA,CAAWU,qBAAA,EAA4D,GACvEV,CAAA,CAAWG,SAAA,CAAU,IAAI,CAC3B;IAAA;IAEA,OAAOQ,YAAYX,CAAA,EAAkC;MACnD,OAAO,IAAIH,CAAA,CAAKG,CAAA,CAAasC,eAAA,CAAgB,CAAC,CAChD;IAAA;EACF;AAAA,SAAA1C,CAAA,IAAAmB,CAAA,EAAAF,CAAA,IAAAS,CAAA,EAAAJ,CAAA,IAAAc,CAAA,EAAAV,CAAA,IAAA1B,CAAA,EAAA8B,CAAA,IAAA1B,CAAA,EAAA+B,CAAA,IAAAQ,CAAA,EAAAJ,CAAA,IAAAK,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}