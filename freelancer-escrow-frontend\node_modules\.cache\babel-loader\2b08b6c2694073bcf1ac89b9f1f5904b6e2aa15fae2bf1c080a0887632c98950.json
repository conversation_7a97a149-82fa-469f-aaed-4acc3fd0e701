{"ast": null, "code": "export * from './register.js';\nexport * from './util.js';", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\wallet\\src\\index.ts"], "sourcesContent": ["export * from './register.js';\nexport * from './util.js';\n"], "mappings": "AAAA,cAAc,eAAe;AAC7B,cAAc,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}