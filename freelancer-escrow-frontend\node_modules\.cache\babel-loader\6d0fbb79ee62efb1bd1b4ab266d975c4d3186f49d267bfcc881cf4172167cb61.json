{"ast": null, "code": "import { a as i } from \"./chunk-AYKZA676.mjs\";\nimport { a as b, b as g } from \"./chunk-4EPLOSKY.mjs\";\nimport { a as h, c as p } from \"./chunk-A2Z7I2EY.mjs\";\nimport { b as a } from \"./chunk-BF46IXHH.mjs\";\nimport { a as y } from \"./chunk-A63SMUOU.mjs\";\nvar n = class extends y {\n    static deserialize(e) {\n      let r = e.deserializeUleb128AsU32();\n      switch (r) {\n        case 0:\n          return o.load(e);\n        case 1:\n          return d.load(e);\n        case 2:\n          return u.load(e);\n        case 3:\n          return l.load(e);\n        case 4:\n          return A.load(e);\n        default:\n          throw new Error(`Unknown variant index for TransactionAuthenticator: ${r}`);\n      }\n    }\n  },\n  o = class s extends n {\n    constructor(e, r) {\n      super(), this.public_key = e, this.signature = r;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(0), this.public_key.serialize(e), this.signature.serialize(e);\n    }\n    static load(e) {\n      let r = h.deserialize(e),\n        t = p.deserialize(e);\n      return new s(r, t);\n    }\n  },\n  d = class s extends n {\n    constructor(e, r) {\n      super(), this.public_key = e, this.signature = r;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(1), this.public_key.serialize(e), this.signature.serialize(e);\n    }\n    static load(e) {\n      let r = b.deserialize(e),\n        t = g.deserialize(e);\n      return new s(r, t);\n    }\n  },\n  u = class s extends n {\n    constructor(e, r, t) {\n      super(), this.sender = e, this.secondary_signer_addresses = r, this.secondary_signers = t;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(2), this.sender.serialize(e), e.serializeVector(this.secondary_signer_addresses), e.serializeVector(this.secondary_signers);\n    }\n    static load(e) {\n      let r = i.deserialize(e),\n        t = e.deserializeVector(a),\n        c = e.deserializeVector(i);\n      return new s(r, t, c);\n    }\n  },\n  l = class s extends n {\n    constructor(e, r, t, c) {\n      super(), this.sender = e, this.secondary_signer_addresses = r, this.secondary_signers = t, this.fee_payer = c;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(3), this.sender.serialize(e), e.serializeVector(this.secondary_signer_addresses), e.serializeVector(this.secondary_signers), this.fee_payer.address.serialize(e), this.fee_payer.authenticator.serialize(e);\n    }\n    static load(e) {\n      let r = i.deserialize(e),\n        t = e.deserializeVector(a),\n        c = e.deserializeVector(i),\n        _ = a.deserialize(e),\n        z = i.deserialize(e),\n        S = {\n          address: _,\n          authenticator: z\n        };\n      return new s(r, t, c, S);\n    }\n  },\n  A = class s extends n {\n    constructor(e) {\n      super(), this.sender = e;\n    }\n    serialize(e) {\n      e.serializeU32AsUleb128(4), this.sender.serialize(e);\n    }\n    static load(e) {\n      let r = i.deserialize(e);\n      return new s(r);\n    }\n  };\nexport { n as a, o as b, d as c, u as d, l as e, A as f };", "map": {"version": 3, "names": ["n", "y", "deserialize", "e", "r", "deserializeUleb128AsU32", "o", "load", "d", "u", "l", "A", "Error", "s", "constructor", "public_key", "signature", "serialize", "serializeU32AsUleb128", "h", "t", "p", "b", "g", "sender", "secondary_signer_addresses", "secondary_signers", "serializeVector", "i", "deserializeVector", "a", "c", "fee_payer", "address", "authenticator", "_", "z", "S", "f"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@aptos-labs\\ts-sdk\\src\\transactions\\authenticator\\transaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\nimport { AccountAuthenticator } from \"./account\";\nimport { Deserializer, Serializable, Serializer } from \"../../bcs\";\nimport { AccountAddress } from \"../../core\";\nimport { Ed25519Pub<PERSON><PERSON><PERSON>, Ed25519Signature } from \"../../core/crypto/ed25519\";\nimport { MultiEd25519PublicKey, MultiEd25519Signature } from \"../../core/crypto/multiEd25519\";\nimport { TransactionAuthenticatorVariant } from \"../../types\";\n\nexport abstract class TransactionAuthenticator extends Serializable {\n  abstract serialize(serializer: Serializer): void;\n\n  static deserialize(deserializer: Deserializer): TransactionAuthenticator {\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case TransactionAuthenticatorVariant.Ed25519:\n        return TransactionAuthenticatorEd25519.load(deserializer);\n      case TransactionAuthenticatorVariant.MultiEd25519:\n        return TransactionAuthenticatorMultiEd25519.load(deserializer);\n      case TransactionAuthenticatorVariant.MultiAgent:\n        return TransactionAuthenticatorMultiAgent.load(deserializer);\n      case TransactionAuthenticatorVariant.FeePayer:\n        return TransactionAuthenticatorFeePayer.load(deserializer);\n      case TransactionAuthenticatorVariant.SingleSender:\n        return TransactionAuthenticatorSingleSender.load(deserializer);\n      default:\n        throw new Error(`Unknown variant index for TransactionAuthenticator: ${index}`);\n    }\n  }\n}\n\n/**\n * Transaction authenticator Ed25519 for a single signer transaction\n *\n * @param public_key Client's public key.\n * @param signature Ed25519 signature of a raw transaction.\n * @see {@link https://aptos.dev/integration/creating-a-signed-transaction | Creating a Signed Transaction}\n * for details about generating a signature.\n */\nexport class TransactionAuthenticatorEd25519 extends TransactionAuthenticator {\n  public readonly public_key: Ed25519PublicKey;\n\n  public readonly signature: Ed25519Signature;\n\n  constructor(public_key: Ed25519PublicKey, signature: Ed25519Signature) {\n    super();\n    this.public_key = public_key;\n    this.signature = signature;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionAuthenticatorVariant.Ed25519);\n    this.public_key.serialize(serializer);\n    this.signature.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): TransactionAuthenticatorEd25519 {\n    const public_key = Ed25519PublicKey.deserialize(deserializer);\n    const signature = Ed25519Signature.deserialize(deserializer);\n    return new TransactionAuthenticatorEd25519(public_key, signature);\n  }\n}\n\n/**\n * Transaction authenticator Ed25519 for a multi signers transaction\n *\n * @param public_key Client's public key.\n * @param signature Multi Ed25519 signature of a raw transaction.\n *\n */\nexport class TransactionAuthenticatorMultiEd25519 extends TransactionAuthenticator {\n  public readonly public_key: MultiEd25519PublicKey;\n\n  public readonly signature: MultiEd25519Signature;\n\n  constructor(public_key: MultiEd25519PublicKey, signature: MultiEd25519Signature) {\n    super();\n    this.public_key = public_key;\n    this.signature = signature;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionAuthenticatorVariant.MultiEd25519);\n    this.public_key.serialize(serializer);\n    this.signature.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): TransactionAuthenticatorMultiEd25519 {\n    const public_key = MultiEd25519PublicKey.deserialize(deserializer);\n    const signature = MultiEd25519Signature.deserialize(deserializer);\n    return new TransactionAuthenticatorMultiEd25519(public_key, signature);\n  }\n}\n\n/**\n * Transaction authenticator for a multi-agent transaction\n *\n * @param sender Sender account authenticator\n * @param secondary_signer_addresses Secondary signers address\n * @param secondary_signers Secondary signers account authenticators\n *\n */\nexport class TransactionAuthenticatorMultiAgent extends TransactionAuthenticator {\n  public readonly sender: AccountAuthenticator;\n\n  public readonly secondary_signer_addresses: Array<AccountAddress>;\n\n  public readonly secondary_signers: Array<AccountAuthenticator>;\n\n  constructor(\n    sender: AccountAuthenticator,\n    secondary_signer_addresses: Array<AccountAddress>,\n    secondary_signers: Array<AccountAuthenticator>,\n  ) {\n    super();\n    this.sender = sender;\n    this.secondary_signer_addresses = secondary_signer_addresses;\n    this.secondary_signers = secondary_signers;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionAuthenticatorVariant.MultiAgent);\n    this.sender.serialize(serializer);\n    serializer.serializeVector<AccountAddress>(this.secondary_signer_addresses);\n    serializer.serializeVector<AccountAuthenticator>(this.secondary_signers);\n  }\n\n  static load(deserializer: Deserializer): TransactionAuthenticatorMultiAgent {\n    const sender = AccountAuthenticator.deserialize(deserializer);\n    const secondary_signer_addresses = deserializer.deserializeVector(AccountAddress);\n    const secondary_signers = deserializer.deserializeVector(AccountAuthenticator);\n    return new TransactionAuthenticatorMultiAgent(sender, secondary_signer_addresses, secondary_signers);\n  }\n}\n\n/**\n * Transaction authenticator for a fee payer transaction\n *\n * @param sender Sender account authenticator\n * @param secondary_signer_addresses Secondary signers address\n * @param secondary_signers Secondary signers account authenticators\n * @param fee_payer Object of the fee payer account address and the fee payer authentication\n *\n */\nexport class TransactionAuthenticatorFeePayer extends TransactionAuthenticator {\n  public readonly sender: AccountAuthenticator;\n\n  public readonly secondary_signer_addresses: Array<AccountAddress>;\n\n  public readonly secondary_signers: Array<AccountAuthenticator>;\n\n  public readonly fee_payer: {\n    address: AccountAddress;\n    authenticator: AccountAuthenticator;\n  };\n\n  constructor(\n    sender: AccountAuthenticator,\n    secondary_signer_addresses: Array<AccountAddress>,\n    secondary_signers: Array<AccountAuthenticator>,\n    fee_payer: { address: AccountAddress; authenticator: AccountAuthenticator },\n  ) {\n    super();\n    this.sender = sender;\n    this.secondary_signer_addresses = secondary_signer_addresses;\n    this.secondary_signers = secondary_signers;\n    this.fee_payer = fee_payer;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionAuthenticatorVariant.FeePayer);\n    this.sender.serialize(serializer);\n    serializer.serializeVector<AccountAddress>(this.secondary_signer_addresses);\n    serializer.serializeVector<AccountAuthenticator>(this.secondary_signers);\n    this.fee_payer.address.serialize(serializer);\n    this.fee_payer.authenticator.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): TransactionAuthenticatorMultiAgent {\n    const sender = AccountAuthenticator.deserialize(deserializer);\n    const secondary_signer_addresses = deserializer.deserializeVector(AccountAddress);\n    const secondary_signers = deserializer.deserializeVector(AccountAuthenticator);\n    const address = AccountAddress.deserialize(deserializer);\n    const authenticator = AccountAuthenticator.deserialize(deserializer);\n    const fee_payer = { address, authenticator };\n    return new TransactionAuthenticatorFeePayer(sender, secondary_signer_addresses, secondary_signers, fee_payer);\n  }\n}\n\n/**\n * Single Sender authenticator for a single signer transaction\n *\n * @param sender AccountAuthenticator\n */\nexport class TransactionAuthenticatorSingleSender extends TransactionAuthenticator {\n  public readonly sender: AccountAuthenticator;\n\n  constructor(sender: AccountAuthenticator) {\n    super();\n    this.sender = sender;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(TransactionAuthenticatorVariant.SingleSender);\n    this.sender.serialize(serializer);\n  }\n\n  static load(deserializer: Deserializer): TransactionAuthenticatorSingleSender {\n    const sender = AccountAuthenticator.deserialize(deserializer);\n    return new TransactionAuthenticatorSingleSender(sender);\n  }\n}\n"], "mappings": ";;;;;AAYO,IAAeA,CAAA,GAAf,cAAgDC,CAAa;IAGlE,OAAOC,YAAYC,CAAA,EAAsD;MACvE,IAAMC,CAAA,GAAQD,CAAA,CAAaE,uBAAA,CAAwB;MACnD,QAAQD,CAAA;QACN;UACE,OAAOE,CAAA,CAAgCC,IAAA,CAAKJ,CAAY;QAC1D;UACE,OAAOK,CAAA,CAAqCD,IAAA,CAAKJ,CAAY;QAC/D;UACE,OAAOM,CAAA,CAAmCF,IAAA,CAAKJ,CAAY;QAC7D;UACE,OAAOO,CAAA,CAAiCH,IAAA,CAAKJ,CAAY;QAC3D;UACE,OAAOQ,CAAA,CAAqCJ,IAAA,CAAKJ,CAAY;QAC/D;UACE,MAAM,IAAIS,KAAA,CAAM,uDAAuDR,CAAK,EAAE,CAClF;MAAA;IACF;EACF;EAUaE,CAAA,GAAN,MAAMO,CAAA,SAAwCb,CAAyB;IAK5Ec,YAAYX,CAAA,EAA8BC,CAAA,EAA6B;MACrE,MAAM,GACN,KAAKW,UAAA,GAAaZ,CAAA,EAClB,KAAKa,SAAA,GAAYZ,CACnB;IAAA;IAEAa,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAWe,qBAAA,EAA6D,GACxE,KAAKH,UAAA,CAAWE,SAAA,CAAUd,CAAU,GACpC,KAAKa,SAAA,CAAUC,SAAA,CAAUd,CAAU,CACrC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAA6D;MACvE,IAAMC,CAAA,GAAae,CAAA,CAAiBjB,WAAA,CAAYC,CAAY;QACtDiB,CAAA,GAAYC,CAAA,CAAiBnB,WAAA,CAAYC,CAAY;MAC3D,OAAO,IAAIU,CAAA,CAAgCT,CAAA,EAAYgB,CAAS,CAClE;IAAA;EACF;EASaZ,CAAA,GAAN,MAAMK,CAAA,SAA6Cb,CAAyB;IAKjFc,YAAYX,CAAA,EAAmCC,CAAA,EAAkC;MAC/E,MAAM,GACN,KAAKW,UAAA,GAAaZ,CAAA,EAClB,KAAKa,SAAA,GAAYZ,CACnB;IAAA;IAEAa,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAWe,qBAAA,EAAkE,GAC7E,KAAKH,UAAA,CAAWE,SAAA,CAAUd,CAAU,GACpC,KAAKa,SAAA,CAAUC,SAAA,CAAUd,CAAU,CACrC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAAkE;MAC5E,IAAMC,CAAA,GAAakB,CAAA,CAAsBpB,WAAA,CAAYC,CAAY;QAC3DiB,CAAA,GAAYG,CAAA,CAAsBrB,WAAA,CAAYC,CAAY;MAChE,OAAO,IAAIU,CAAA,CAAqCT,CAAA,EAAYgB,CAAS,CACvE;IAAA;EACF;EAUaX,CAAA,GAAN,MAAMI,CAAA,SAA2Cb,CAAyB;IAO/Ec,YACEX,CAAA,EACAC,CAAA,EACAgB,CAAA,EACA;MACA,MAAM,GACN,KAAKI,MAAA,GAASrB,CAAA,EACd,KAAKsB,0BAAA,GAA6BrB,CAAA,EAClC,KAAKsB,iBAAA,GAAoBN,CAC3B;IAAA;IAEAH,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAWe,qBAAA,EAAgE,GAC3E,KAAKM,MAAA,CAAOP,SAAA,CAAUd,CAAU,GAChCA,CAAA,CAAWwB,eAAA,CAAgC,KAAKF,0BAA0B,GAC1EtB,CAAA,CAAWwB,eAAA,CAAsC,KAAKD,iBAAiB,CACzE;IAAA;IAEA,OAAOnB,KAAKJ,CAAA,EAAgE;MAC1E,IAAMC,CAAA,GAASwB,CAAA,CAAqB1B,WAAA,CAAYC,CAAY;QACtDiB,CAAA,GAA6BjB,CAAA,CAAa0B,iBAAA,CAAkBC,CAAc;QAC1EC,CAAA,GAAoB5B,CAAA,CAAa0B,iBAAA,CAAkBD,CAAoB;MAC7E,OAAO,IAAIf,CAAA,CAAmCT,CAAA,EAAQgB,CAAA,EAA4BW,CAAiB,CACrG;IAAA;EACF;EAWarB,CAAA,GAAN,MAAMG,CAAA,SAAyCb,CAAyB;IAY7Ec,YACEX,CAAA,EACAC,CAAA,EACAgB,CAAA,EACAW,CAAA,EACA;MACA,MAAM,GACN,KAAKP,MAAA,GAASrB,CAAA,EACd,KAAKsB,0BAAA,GAA6BrB,CAAA,EAClC,KAAKsB,iBAAA,GAAoBN,CAAA,EACzB,KAAKY,SAAA,GAAYD,CACnB;IAAA;IAEAd,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAWe,qBAAA,EAA8D,GACzE,KAAKM,MAAA,CAAOP,SAAA,CAAUd,CAAU,GAChCA,CAAA,CAAWwB,eAAA,CAAgC,KAAKF,0BAA0B,GAC1EtB,CAAA,CAAWwB,eAAA,CAAsC,KAAKD,iBAAiB,GACvE,KAAKM,SAAA,CAAUC,OAAA,CAAQhB,SAAA,CAAUd,CAAU,GAC3C,KAAK6B,SAAA,CAAUE,aAAA,CAAcjB,SAAA,CAAUd,CAAU,CACnD;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAAgE;MAC1E,IAAMC,CAAA,GAASwB,CAAA,CAAqB1B,WAAA,CAAYC,CAAY;QACtDiB,CAAA,GAA6BjB,CAAA,CAAa0B,iBAAA,CAAkBC,CAAc;QAC1EC,CAAA,GAAoB5B,CAAA,CAAa0B,iBAAA,CAAkBD,CAAoB;QACvEO,CAAA,GAAUL,CAAA,CAAe5B,WAAA,CAAYC,CAAY;QACjDiC,CAAA,GAAgBR,CAAA,CAAqB1B,WAAA,CAAYC,CAAY;QAC7DkC,CAAA,GAAY;UAAEJ,OAAA,EAAAE,CAAA;UAASD,aAAA,EAAAE;QAAc;MAC3C,OAAO,IAAIvB,CAAA,CAAiCT,CAAA,EAAQgB,CAAA,EAA4BW,CAAA,EAAmBM,CAAS,CAC9G;IAAA;EACF;EAOa1B,CAAA,GAAN,MAAME,CAAA,SAA6Cb,CAAyB;IAGjFc,YAAYX,CAAA,EAA8B;MACxC,MAAM,GACN,KAAKqB,MAAA,GAASrB,CAChB;IAAA;IAEAc,UAAUd,CAAA,EAA8B;MACtCA,CAAA,CAAWe,qBAAA,EAAkE,GAC7E,KAAKM,MAAA,CAAOP,SAAA,CAAUd,CAAU,CAClC;IAAA;IAEA,OAAOI,KAAKJ,CAAA,EAAkE;MAC5E,IAAMC,CAAA,GAASwB,CAAA,CAAqB1B,WAAA,CAAYC,CAAY;MAC5D,OAAO,IAAIU,CAAA,CAAqCT,CAAM,CACxD;IAAA;EACF;AAAA,SAAAJ,CAAA,IAAA8B,CAAA,EAAAxB,CAAA,IAAAgB,CAAA,EAAAd,CAAA,IAAAuB,CAAA,EAAAtB,CAAA,IAAAD,CAAA,EAAAE,CAAA,IAAAP,CAAA,EAAAQ,CAAA,IAAA2B,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}