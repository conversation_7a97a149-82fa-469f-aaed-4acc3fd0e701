{"ast": null, "code": "var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar _AppReadyEvent_detail;\nlet wallets = undefined;\nconst registeredWalletsSet = new Set();\nfunction addRegisteredWallet(wallet) {\n  cachedWalletsArray = undefined;\n  registeredWalletsSet.add(wallet);\n}\nfunction removeRegisteredWallet(wallet) {\n  cachedWalletsArray = undefined;\n  registeredWalletsSet.delete(wallet);\n}\nconst listeners = {};\n/**\n * Get an API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * When called for the first time --\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowAppReadyEvent} to notify each Wallet that the app is ready\n * to register it.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to listen for a notification\n * from each Wallet that the Wallet is ready to be registered by the app.\n *\n * This combination of event dispatch and listener guarantees that each Wallet will be registered synchronously as soon\n * as the app is ready whether the app loads before or after each Wallet.\n *\n * @return API for getting, listening for, and registering Wallets.\n *\n * @group App\n */\nexport function getWallets() {\n  if (wallets) return wallets;\n  wallets = Object.freeze({\n    register,\n    get,\n    on\n  });\n  if (typeof window === 'undefined') return wallets;\n  const api = Object.freeze({\n    register\n  });\n  try {\n    window.addEventListener('wallet-standard:register-wallet', ({\n      detail: callback\n    }) => callback(api));\n  } catch (error) {\n    console.error('wallet-standard:register-wallet event listener could not be added\\n', error);\n  }\n  try {\n    window.dispatchEvent(new AppReadyEvent(api));\n  } catch (error) {\n    console.error('wallet-standard:app-ready event could not be dispatched\\n', error);\n  }\n  return wallets;\n}\nfunction register(...wallets) {\n  // Filter out wallets that have already been registered.\n  // This prevents the same wallet from being registered twice, but it also prevents wallets from being\n  // unregistered by reusing a reference to the wallet to obtain the unregister function for it.\n  wallets = wallets.filter(wallet => !registeredWalletsSet.has(wallet));\n  // If there are no new wallets to register, just return a no-op unregister function.\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  if (!wallets.length) return () => {};\n  wallets.forEach(wallet => addRegisteredWallet(wallet));\n  listeners['register']?.forEach(listener => guard(() => listener(...wallets)));\n  // Return a function that unregisters the registered wallets.\n  return function unregister() {\n    wallets.forEach(wallet => removeRegisteredWallet(wallet));\n    listeners['unregister']?.forEach(listener => guard(() => listener(...wallets)));\n  };\n}\nlet cachedWalletsArray;\nfunction get() {\n  if (!cachedWalletsArray) {\n    cachedWalletsArray = [...registeredWalletsSet];\n  }\n  return cachedWalletsArray;\n}\nfunction on(event, listener) {\n  listeners[event]?.push(listener) || (listeners[event] = [listener]);\n  // Return a function that removes the event listener.\n  return function off() {\n    listeners[event] = listeners[event]?.filter(existingListener => listener !== existingListener);\n  };\n}\nfunction guard(callback) {\n  try {\n    callback();\n  } catch (error) {\n    console.error(error);\n  }\n}\nclass AppReadyEvent extends Event {\n  get detail() {\n    return __classPrivateFieldGet(this, _AppReadyEvent_detail, \"f\");\n  }\n  get type() {\n    return 'wallet-standard:app-ready';\n  }\n  constructor(api) {\n    super('wallet-standard:app-ready', {\n      bubbles: false,\n      cancelable: false,\n      composed: false\n    });\n    _AppReadyEvent_detail.set(this, void 0);\n    __classPrivateFieldSet(this, _AppReadyEvent_detail, api, \"f\");\n  }\n  /** @deprecated */\n  preventDefault() {\n    throw new Error('preventDefault cannot be called');\n  }\n  /** @deprecated */\n  stopImmediatePropagation() {\n    throw new Error('stopImmediatePropagation cannot be called');\n  }\n  /** @deprecated */\n  stopPropagation() {\n    throw new Error('stopPropagation cannot be called');\n  }\n}\n_AppReadyEvent_detail = new WeakMap();\n/**\n * @deprecated Use {@link getWallets} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_getWallets() {\n  if (wallets) return wallets;\n  wallets = getWallets();\n  if (typeof window === 'undefined') return wallets;\n  const callbacks = window.navigator.wallets || [];\n  if (!Array.isArray(callbacks)) {\n    console.error('window.navigator.wallets is not an array');\n    return wallets;\n  }\n  const {\n    register\n  } = wallets;\n  const push = (...callbacks) => callbacks.forEach(callback => guard(() => callback({\n    register\n  })));\n  try {\n    Object.defineProperty(window.navigator, 'wallets', {\n      value: Object.freeze({\n        push\n      })\n    });\n  } catch (error) {\n    console.error('window.navigator.wallets could not be set');\n    return wallets;\n  }\n  push(...callbacks);\n  return wallets;\n}", "map": {"version": 3, "names": ["wallets", "undefined", "registeredWalletsSet", "Set", "addRegisteredWallet", "wallet", "cachedWalletsArray", "add", "removeRegisteredWallet", "delete", "listeners", "getWallets", "Object", "freeze", "register", "get", "on", "window", "api", "addEventListener", "detail", "callback", "error", "console", "dispatchEvent", "AppReadyEvent", "filter", "has", "length", "for<PERSON>ach", "listener", "guard", "unregister", "event", "push", "off", "existingListener", "Event", "__classPrivateFieldGet", "_AppReadyEvent_detail", "type", "constructor", "bubbles", "cancelable", "composed", "set", "__classPrivateFieldSet", "preventDefault", "Error", "stopImmediatePropagation", "stopPropagation", "DEPRECATED_getWallets", "callbacks", "navigator", "Array", "isArray", "defineProperty", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\Freelancer-Platform-on-Aptos--DAPPS-main\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\app\\src\\wallets.ts"], "sourcesContent": ["import type {\n    DEPRECATED_WalletsCallback,\n    DEPRECATED_WalletsWindow,\n    Wallet,\n    WalletEventsWindow,\n    WindowAppReadyEvent,\n    WindowAppReadyEventAPI,\n} from '@wallet-standard/base';\n\nlet wallets: Wallets | undefined = undefined;\nconst registeredWalletsSet = new Set<Wallet>();\nfunction addRegisteredWallet(wallet: Wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.add(wallet);\n}\nfunction removeRegisteredWallet(wallet: Wallet) {\n    cachedWalletsArray = undefined;\n    registeredWalletsSet.delete(wallet);\n}\nconst listeners: { [E in WalletsEventNames]?: WalletsEventsListeners[E][] } = {};\n\n/**\n * Get an API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * When called for the first time --\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowAppReadyEvent} to notify each Wallet that the app is ready\n * to register it.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to listen for a notification\n * from each Wallet that the Wallet is ready to be registered by the app.\n *\n * This combination of event dispatch and listener guarantees that each Wallet will be registered synchronously as soon\n * as the app is ready whether the app loads before or after each Wallet.\n *\n * @return API for getting, listening for, and registering Wallets.\n *\n * @group App\n */\nexport function getWallets(): Wallets {\n    if (wallets) return wallets;\n    wallets = Object.freeze({ register, get, on });\n    if (typeof window === 'undefined') return wallets;\n\n    const api = Object.freeze({ register });\n    try {\n        (window as WalletEventsWindow).addEventListener('wallet-standard:register-wallet', ({ detail: callback }) =>\n            callback(api)\n        );\n    } catch (error) {\n        console.error('wallet-standard:register-wallet event listener could not be added\\n', error);\n    }\n    try {\n        (window as WalletEventsWindow).dispatchEvent(new AppReadyEvent(api));\n    } catch (error) {\n        console.error('wallet-standard:app-ready event could not be dispatched\\n', error);\n    }\n\n    return wallets;\n}\n\n/**\n * API for {@link Wallets.get | getting}, {@link Wallets.on | listening for}, and\n * {@link Wallets.register | registering} {@link \"@wallet-standard/base\".Wallet | Wallets}.\n *\n * @group App\n */\nexport interface Wallets {\n    /**\n     * Get all Wallets that have been registered.\n     *\n     * @return Registered Wallets.\n     */\n    get(): readonly Wallet[];\n\n    /**\n     * Add an event listener and subscribe to events for Wallets that are\n     * {@link WalletsEventsListeners.register | registered} and\n     * {@link WalletsEventsListeners.unregister | unregistered}.\n     *\n     * @param event    Event type to listen for. {@link WalletsEventsListeners.register | `register`} and\n     * {@link WalletsEventsListeners.unregister | `unregister`} are the only event types.\n     * @param listener Function that will be called when an event of the type is emitted.\n     *\n     * @return\n     * `off` function which may be called to remove the event listener and unsubscribe from events.\n     *\n     * As with all event listeners, be careful to avoid memory leaks.\n     */\n    on<E extends WalletsEventNames>(event: E, listener: WalletsEventsListeners[E]): () => void;\n\n    /**\n     * Register Wallets. This can be used to programmatically wrap non-standard wallets as Standard Wallets.\n     *\n     * Apps generally do not need to, and should not, call this.\n     *\n     * @param wallets Wallets to register.\n     *\n     * @return\n     * `unregister` function which may be called to programmatically unregister the registered Wallets.\n     *\n     * Apps generally do not need to, and should not, call this.\n     */\n    register(...wallets: Wallet[]): () => void;\n}\n\n/**\n * Types of event listeners of the {@link Wallets} API.\n *\n * @group App\n */\nexport interface WalletsEventsListeners {\n    /**\n     * Emitted when Wallets are registered.\n     *\n     * @param wallets Wallets that were registered.\n     */\n    register(...wallets: Wallet[]): void;\n\n    /**\n     * Emitted when Wallets are unregistered.\n     *\n     * @param wallets Wallets that were unregistered.\n     */\n    unregister(...wallets: Wallet[]): void;\n}\n\n/**\n * Names of {@link WalletsEventsListeners} that can be listened for.\n *\n * @group App\n */\nexport type WalletsEventNames = keyof WalletsEventsListeners;\n\n/**\n * @deprecated Use {@link WalletsEventsListeners} instead.\n *\n * @group Deprecated\n */\nexport type WalletsEvents = WalletsEventsListeners;\n\nfunction register(...wallets: Wallet[]): () => void {\n    // Filter out wallets that have already been registered.\n    // This prevents the same wallet from being registered twice, but it also prevents wallets from being\n    // unregistered by reusing a reference to the wallet to obtain the unregister function for it.\n    wallets = wallets.filter((wallet) => !registeredWalletsSet.has(wallet));\n    // If there are no new wallets to register, just return a no-op unregister function.\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    if (!wallets.length) return () => {};\n\n    wallets.forEach((wallet) => addRegisteredWallet(wallet));\n    listeners['register']?.forEach((listener) => guard(() => listener(...wallets)));\n    // Return a function that unregisters the registered wallets.\n    return function unregister(): void {\n        wallets.forEach((wallet) => removeRegisteredWallet(wallet));\n        listeners['unregister']?.forEach((listener) => guard(() => listener(...wallets)));\n    };\n}\n\nlet cachedWalletsArray: readonly Wallet[] | undefined;\nfunction get(): readonly Wallet[] {\n    if (!cachedWalletsArray) {\n        cachedWalletsArray = [...registeredWalletsSet];\n    }\n    return cachedWalletsArray;\n}\n\nfunction on<E extends WalletsEventNames>(event: E, listener: WalletsEventsListeners[E]): () => void {\n    listeners[event]?.push(listener) || (listeners[event] = [listener]);\n    // Return a function that removes the event listener.\n    return function off(): void {\n        listeners[event] = listeners[event]?.filter((existingListener) => listener !== existingListener);\n    };\n}\n\nfunction guard(callback: () => void) {\n    try {\n        callback();\n    } catch (error) {\n        console.error(error);\n    }\n}\n\nclass AppReadyEvent extends Event implements WindowAppReadyEvent {\n    readonly #detail: WindowAppReadyEventAPI;\n\n    get detail() {\n        return this.#detail;\n    }\n\n    get type() {\n        return 'wallet-standard:app-ready' as const;\n    }\n\n    constructor(api: WindowAppReadyEventAPI) {\n        super('wallet-standard:app-ready', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        this.#detail = api;\n    }\n\n    /** @deprecated */\n    preventDefault(): never {\n        throw new Error('preventDefault cannot be called');\n    }\n\n    /** @deprecated */\n    stopImmediatePropagation(): never {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n\n    /** @deprecated */\n    stopPropagation(): never {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n\n/**\n * @deprecated Use {@link getWallets} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_getWallets(): Wallets {\n    if (wallets) return wallets;\n    wallets = getWallets();\n    if (typeof window === 'undefined') return wallets;\n\n    const callbacks = (window as DEPRECATED_WalletsWindow).navigator.wallets || [];\n    if (!Array.isArray(callbacks)) {\n        console.error('window.navigator.wallets is not an array');\n        return wallets;\n    }\n\n    const { register } = wallets;\n    const push = (...callbacks: DEPRECATED_WalletsCallback[]): void =>\n        callbacks.forEach((callback) => guard(() => callback({ register })));\n    try {\n        Object.defineProperty((window as DEPRECATED_WalletsWindow).navigator, 'wallets', {\n            value: Object.freeze({ push }),\n        });\n    } catch (error) {\n        console.error('window.navigator.wallets could not be set');\n        return wallets;\n    }\n\n    push(...callbacks);\n    return wallets;\n}\n"], "mappings": ";;;;;;;;;;;;AASA,IAAIA,OAAO,GAAwBC,SAAS;AAC5C,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,EAAU;AAC9C,SAASC,mBAAmBA,CAACC,MAAc;EACvCC,kBAAkB,GAAGL,SAAS;EAC9BC,oBAAoB,CAACK,GAAG,CAACF,MAAM,CAAC;AACpC;AACA,SAASG,sBAAsBA,CAACH,MAAc;EAC1CC,kBAAkB,GAAGL,SAAS;EAC9BC,oBAAoB,CAACO,MAAM,CAACJ,MAAM,CAAC;AACvC;AACA,MAAMK,SAAS,GAA+D,EAAE;AAEhF;;;;;;;;;;;;;;;;;;;AAmBA,OAAM,SAAUC,UAAUA,CAAA;EACtB,IAAIX,OAAO,EAAE,OAAOA,OAAO;EAC3BA,OAAO,GAAGY,MAAM,CAACC,MAAM,CAAC;IAAEC,QAAQ;IAAEC,GAAG;IAAEC;EAAE,CAAE,CAAC;EAC9C,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE,OAAOjB,OAAO;EAEjD,MAAMkB,GAAG,GAAGN,MAAM,CAACC,MAAM,CAAC;IAAEC;EAAQ,CAAE,CAAC;EACvC,IAAI;IACCG,MAA6B,CAACE,gBAAgB,CAAC,iCAAiC,EAAE,CAAC;MAAEC,MAAM,EAAEC;IAAQ,CAAE,KACpGA,QAAQ,CAACH,GAAG,CAAC,CAChB;EACL,CAAC,CAAC,OAAOI,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,qEAAqE,EAAEA,KAAK,CAAC;EAC/F;EACA,IAAI;IACCL,MAA6B,CAACO,aAAa,CAAC,IAAIC,aAAa,CAACP,GAAG,CAAC,CAAC;EACxE,CAAC,CAAC,OAAOI,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;EACrF;EAEA,OAAOtB,OAAO;AAClB;AAkFA,SAASc,QAAQA,CAAC,GAAGd,OAAiB;EAClC;EACA;EACA;EACAA,OAAO,GAAGA,OAAO,CAAC0B,MAAM,CAAErB,MAAM,IAAK,CAACH,oBAAoB,CAACyB,GAAG,CAACtB,MAAM,CAAC,CAAC;EACvE;EACA;EACA,IAAI,CAACL,OAAO,CAAC4B,MAAM,EAAE,OAAO,MAAK,CAAE,CAAC;EAEpC5B,OAAO,CAAC6B,OAAO,CAAExB,MAAM,IAAKD,mBAAmB,CAACC,MAAM,CAAC,CAAC;EACxDK,SAAS,CAAC,UAAU,CAAC,EAAEmB,OAAO,CAAEC,QAAQ,IAAKC,KAAK,CAAC,MAAMD,QAAQ,CAAC,GAAG9B,OAAO,CAAC,CAAC,CAAC;EAC/E;EACA,OAAO,SAASgC,UAAUA,CAAA;IACtBhC,OAAO,CAAC6B,OAAO,CAAExB,MAAM,IAAKG,sBAAsB,CAACH,MAAM,CAAC,CAAC;IAC3DK,SAAS,CAAC,YAAY,CAAC,EAAEmB,OAAO,CAAEC,QAAQ,IAAKC,KAAK,CAAC,MAAMD,QAAQ,CAAC,GAAG9B,OAAO,CAAC,CAAC,CAAC;EACrF,CAAC;AACL;AAEA,IAAIM,kBAAiD;AACrD,SAASS,GAAGA,CAAA;EACR,IAAI,CAACT,kBAAkB,EAAE;IACrBA,kBAAkB,GAAG,CAAC,GAAGJ,oBAAoB,CAAC;EAClD;EACA,OAAOI,kBAAkB;AAC7B;AAEA,SAASU,EAAEA,CAA8BiB,KAAQ,EAAEH,QAAmC;EAClFpB,SAAS,CAACuB,KAAK,CAAC,EAAEC,IAAI,CAACJ,QAAQ,CAAC,KAAKpB,SAAS,CAACuB,KAAK,CAAC,GAAG,CAACH,QAAQ,CAAC,CAAC;EACnE;EACA,OAAO,SAASK,GAAGA,CAAA;IACfzB,SAAS,CAACuB,KAAK,CAAC,GAAGvB,SAAS,CAACuB,KAAK,CAAC,EAAEP,MAAM,CAAEU,gBAAgB,IAAKN,QAAQ,KAAKM,gBAAgB,CAAC;EACpG,CAAC;AACL;AAEA,SAASL,KAAKA,CAACV,QAAoB;EAC/B,IAAI;IACAA,QAAQ,EAAE;EACd,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AAEA,MAAMG,aAAc,SAAQY,KAAK;EAG7B,IAAIjB,MAAMA,CAAA;IACN,OAAOkB,sBAAA,KAAI,EAAAC,qBAAA,MAAQ;EACvB;EAEA,IAAIC,IAAIA,CAAA;IACJ,OAAO,2BAAoC;EAC/C;EAEAC,YAAYvB,GAA2B;IACnC,KAAK,CAAC,2BAA2B,EAAE;MAC/BwB,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE;KACb,CAAC;IAfGL,qBAAA,CAAAM,GAAA;IAgBLC,sBAAA,KAAI,EAAAP,qBAAA,EAAWrB,GAAG;EACtB;EAEA;EACA6B,cAAcA,CAAA;IACV,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACtD;EAEA;EACAC,wBAAwBA,CAAA;IACpB,MAAM,IAAID,KAAK,CAAC,2CAA2C,CAAC;EAChE;EAEA;EACAE,eAAeA,CAAA;IACX,MAAM,IAAIF,KAAK,CAAC,kCAAkC,CAAC;EACvD;;;AAGJ;;;;;AAKA,OAAM,SAAUG,qBAAqBA,CAAA;EACjC,IAAInD,OAAO,EAAE,OAAOA,OAAO;EAC3BA,OAAO,GAAGW,UAAU,EAAE;EACtB,IAAI,OAAOM,MAAM,KAAK,WAAW,EAAE,OAAOjB,OAAO;EAEjD,MAAMoD,SAAS,GAAInC,MAAmC,CAACoC,SAAS,CAACrD,OAAO,IAAI,EAAE;EAC9E,IAAI,CAACsD,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE;IAC3B7B,OAAO,CAACD,KAAK,CAAC,0CAA0C,CAAC;IACzD,OAAOtB,OAAO;EAClB;EAEA,MAAM;IAAEc;EAAQ,CAAE,GAAGd,OAAO;EAC5B,MAAMkC,IAAI,GAAGA,CAAC,GAAGkB,SAAuC,KACpDA,SAAS,CAACvB,OAAO,CAAER,QAAQ,IAAKU,KAAK,CAAC,MAAMV,QAAQ,CAAC;IAAEP;EAAQ,CAAE,CAAC,CAAC,CAAC;EACxE,IAAI;IACAF,MAAM,CAAC4C,cAAc,CAAEvC,MAAmC,CAACoC,SAAS,EAAE,SAAS,EAAE;MAC7EI,KAAK,EAAE7C,MAAM,CAACC,MAAM,CAAC;QAAEqB;MAAI,CAAE;KAChC,CAAC;EACN,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,2CAA2C,CAAC;IAC1D,OAAOtB,OAAO;EAClB;EAEAkC,IAAI,CAAC,GAAGkB,SAAS,CAAC;EAClB,OAAOpD,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}